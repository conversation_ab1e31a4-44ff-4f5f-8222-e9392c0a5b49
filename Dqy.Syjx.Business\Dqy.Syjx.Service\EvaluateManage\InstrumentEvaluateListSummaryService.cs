﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.EvaluateManage;
using Dqy.Syjx.Model.Param.EvaluateManage;

namespace Dqy.Syjx.Service.EvaluateManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-12-03 13:41
    /// 描 述：服务类
    /// </summary>
    public class InstrumentEvaluateListSummaryService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<InstrumentEvaluateListSummaryEntity>> GetList(InstrumentEvaluateListSummaryListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<InstrumentEvaluateListSummaryEntity>> GetPageList(InstrumentEvaluateListSummaryListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<InstrumentEvaluateListSummaryEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<InstrumentEvaluateListSummaryEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(InstrumentEvaluateListSummaryEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(InstrumentEvaluateListSummaryEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }
        #endregion

        #region 私有方法
        private Expression<Func<InstrumentEvaluateListSummaryEntity, bool>> ListFilter(InstrumentEvaluateListSummaryListParam param)
        {
            var expression = LinqExtensions.True<InstrumentEvaluateListSummaryEntity>();
            if (param != null)
            {
                if (param.BaseIsDelete > -1)
                {
                    expression = expression.And(t => t.BaseIsDelete.Value == param.BaseIsDelete);
                }
                if (param.EvaluateStandardId > 0)
                {
                    expression = expression.And(t => t.EvaluateStandardId == param.EvaluateStandardId);
                }
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
            }
            return expression;
        }
        #endregion
    }
}
