﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.CameraManage;
using Dqy.Syjx.Model.Param.CameraManage;

namespace Dqy.Syjx.Service.CameraManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2022-05-05 11:11
    /// 描 述：服务类
    /// </summary>
    public class CameraPointService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<CameraPointEntity>> GetList(CameraPointListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<CameraPointEntity>> GetPageList(CameraPointListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ListFilter(param, strSql);
            var list = await this.BaseRepository().FindList<CameraPointEntity>(strSql.ToString(), filter.ToArray(), pagination);
            return list.ToList();
        }

        public async Task<CameraPointEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<CameraPointEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(CameraPointEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {

                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(CameraPointEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                entity.Create();
                await db.Insert(entity);
            }
            else
            {

                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
               strSql = $" update HaiKangCamera set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
               strSql = $"update HaiKangCamera set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<CameraPointEntity, bool>> ListFilter(CameraPointListParam param)
        {
            var expression = LinqExtensions.True<CameraPointEntity>();
            if (param != null)
            {
               // expression = expression.And(t => t.Status == 1);
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (!string.IsNullOrEmpty(param.CameraName))
                {
                    expression = expression.And(t => t.CameraName.Contains(param.CameraName));
                }
            }
            return expression;
        }

        private List<DbParameter> ListFilter(CameraPointListParam param, StringBuilder strSql)
        {
            strSql.Append($@"  SELECT * From (
                                 SELECT  camera.Id ,
                                         camera.CameraIndexCode ,
                                         camera.CameraName ,
                                         camera.RegionIndexCode ,
                                         camera.RegionName ,
                                         camera.ParentIndexCode ,
                                         camera.RegionPath ,
                                         camera.RegionPathName ,
                                         camera.CreateTime ,
		                                 encodeDevice.EncodeName AS EncodeName ,
                                         encodeDevice.IP AS IP ,
		                                 encodeDevice.Port AS Port
                                FROM HaiKangCamera AS camera
            ) as tb1 WHERE Status = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.CameraName))
                {
                    strSql.Append(" AND (CameraName like @CameraName )");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CameraName", $"%{param.CameraName}%"));
                }
            }

            return parameter;
        }
        #endregion
    }
}
