﻿using Dqy.Syjx.Business.BusinessManage;
using Dqy.Syjx.Business.ExperimentTeachManage;
using Dqy.Syjx.Business.OrganizationManage;
using Dqy.Syjx.Business.SystemManage;
using Dqy.Syjx.Entity;
using Dqy.Syjx.Entity.BusinessManage;
using Dqy.Syjx.Entity.ExperimentTeachManage;
using Dqy.Syjx.Entity.OrganizationManage;
using Dqy.Syjx.Entity.SystemManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Enum.BusinessManage;
using Dqy.Syjx.Enum.ExperimentTeachManage;
using Dqy.Syjx.Enum.InstrumentManage;
using Dqy.Syjx.Model;
using Dqy.Syjx.Model.Input.ExperimentTeachManage;
using Dqy.Syjx.Model.Param.ExperimentTeachManage;
using Dqy.Syjx.Model.Param.InstrumentManage;
using Dqy.Syjx.Model.Param.OrganizationManage;
using Dqy.Syjx.Model.Param.SystemManage;
using Dqy.Syjx.Model.Result;
using Dqy.Syjx.Service.HttpService;
using Dqy.Syjx.Service.HttpService.Wxzhjy;
using Dqy.Syjx.Service.HttpService.Wxzhjy.Models;
using Dqy.Syjx.Service.OrganizationManage;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Web.Code;
using Dqy.Syjx.Web.Controllers;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using Newtonsoft.Json;
using NPOI.SS.Formula.Functions;
using Refit;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace Dqy.Syjx.Web.Areas.ExperimentTeachManage.Controllers
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-12-14 11:28
    /// 描 述：实验预约控制器类
    /// </summary>
    [Area("ExperimentTeachManage")]
    public class ExperimentBookingController :  BaseController
    {
        private ExperimentBookingBLL experimentBookingBLL = new ExperimentBookingBLL();
        private SchoolGradeClassBLL schoolGradeClassBll = new SchoolGradeClassBLL();
        private AttachmentBLL attachmentBLL = new AttachmentBLL();
        private SchoolTermBLL schoolTermBLL = new SchoolTermBLL();
        private ConfigSetBLL configSetBLL = new ConfigSetBLL();
        private UserBLL userBll = new UserBLL();
        private UnitBLL unitBll = new UnitBLL();
        private AppManageBLL appManagerBLL = new AppManageBLL();
        private readonly IHttpContextAccessor _httpContextAccessor;

        public ExperimentBookingController(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        #region 视图功能
        [AuthorizeFilter("experimentteach:experimentbooking:view")]
        public ActionResult Listed()
        {
            return View();
        }
        [AuthorizeFilter("experimentteach:experimentbooking:view")]
        public ActionResult Form()
        {
            return View();
        }

        [AuthorizeFilter("experimentteach:experimentbooking:view")]
        public ActionResult EditForm()
        {
            return View();
        }

        /// <summary>
        /// 选择节次
        /// </summary>
        /// <returns></returns>
        public ActionResult SelectSection()
        {
            return View();
        }

        /// <summary>
        /// 选择计划实验
        /// </summary>
        public ActionResult SelectPlan()
        {
            return View();
        }

        /// <summary>
        /// 按计划预约列表
        /// </summary>
        /// <returns></returns>
        [AuthorizeFilter("experimentteach:experimentbooking:view")]
        public ActionResult PlanList()
        {
            return View();
        }

        /// <summary>
        /// 选择目录实验
        /// </summary>
        public ActionResult SelectCatalog()
        {
            return View();
        }

        /// <summary>
        /// 目录列表
        /// </summary>
        /// <returns></returns>
        [AuthorizeFilter("experimentteach:experimentbooking:view")]
        public ActionResult CatalogList()
        {
            return View();
        }

        /// <summary>
        /// 按计划预约列表
        /// </summary>
        /// <returns></returns>
        [AuthorizeFilter("experimentteach:experimentbooking:view")]
        public ActionResult SelectClassList()
        {
            return View();
        }

        /// <summary>
        /// 实验查看详情页面
        /// </summary>
        /// <returns></returns>
        public ActionResult Detail()
        {
            return View();
        }

        /// <summary>
        /// 待安排列表视图
        /// </summary>
        /// <returns></returns>
        [AuthorizeFilter("experimentteach:experimentbookingwait:view")]
        public ActionResult WaitArrangeList()
        {
            return View();
        }

        /// <summary>
        /// 已安排列表视图
        /// </summary>
        /// <returns></returns>
        [AuthorizeFilter("experimentteach:experimentbooking:view")]
        public ActionResult AlreadyArrangeList()
        {
            return View();
        }

        /// <summary>
        /// 待登记列表视图
        /// </summary>
        /// <returns></returns>
        [AuthorizeFilter("experimentteach:experimentbookingrecord:view")]
        public ActionResult WaitRecordList()
        {
            return View();
        }

        /// <summary>
        /// 已登记列表视图
        /// </summary>
        /// <returns></returns>
        [AuthorizeFilter("experimentteach:experimentbooking:view")]
        public async Task<ActionResult> AlreadyRecordList()
        {
            //过了当前学期的已登记数据，无法撤回

            //获取当前学年学期 
            DateTime now = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day);
            var schoolTermList = (await schoolTermBLL.GetList(new Model.Param.OrganizationManage.SchoolTermListParam
            {
                OptType = 2,
                ClassTime = now
            })).Data;

            int schoolYearStart = -1;
            int schoolTerm = -1;
            if (schoolTermList.Count > 0)
            {
                schoolYearStart = schoolTermList.FirstOrDefault().SchoolYearStart;
                schoolTerm = schoolTermList.FirstOrDefault().SchoolTerm;
            }
            ViewBag.SchoolYearStart = schoolYearStart;
            ViewBag.SchoolTerm = schoolTerm;

            return View();
        }

        /// <summary>
        /// 实验安排
        /// </summary>
        /// <returns></returns>
        public ActionResult ArrangeForm()
        {
            return View();
        }

        /// <summary>
        /// 实验登记
        /// </summary>
        /// <returns></returns>
        public ActionResult RecordForm()
        {
            return View();
        }

        /// <summary>
        /// 任务列表
        /// </summary>
        /// <returns></returns>
        [AuthorizeFilter("experimentteach:experimentbooking:view")]
        public ActionResult TaskList()
        {
            return View();
        }

        /// <summary>
        /// 待安排列表视图
        /// </summary>
        /// <returns></returns>
        [AuthorizeFilter("experimentteach:experimentbookingwait:view")]
        public ActionResult ArrangeList()
        {
            return View();
        }
        /// <summary>
        /// 已安排列表视图
        /// </summary>
        /// <returns></returns>
        [AuthorizeFilter("experimentteach:experimentbookingwait:view")]
        public ActionResult ArrangeListed()
        {
            return View();
        }

        /// <summary>
        /// 安排视图
        /// </summary>
        /// <returns></returns>
        [AuthorizeFilter("experimentteach:experimentbookingwait:view")]
        public ActionResult Arrange()
        {
            return View();
        }
        /// <summary>
        /// 安排视图
        /// </summary>
        /// <returns></returns>
        [AuthorizeFilter("experimentteach:experimentbookingwait:view")]
        public ActionResult ArrangeBatch()
        {
            return View();
        }

        /// <summary>
        /// 安排视图
        /// </summary>
        /// <returns></returns>
        public ActionResult ArrangeDetailListForm()
        {
            return View();
        }

        /// <summary>
        /// 待登记列表视图
        /// </summary>
        /// <returns></returns>
        [AuthorizeFilter("experimentteach:experimentbookingwait:view")]
        public ActionResult RecordList()
        {
            return View();
        }

        /// <summary>
        /// 已登记列表视图
        /// </summary>
        /// <returns></returns>
        [AuthorizeFilter("experimentteach:experimentbookingwait:view")]
        public ActionResult RecordListed()
        {
            return View();
        } 

        /// <summary>
        /// 登记视图
        /// </summary>
        /// <returns></returns>
        [AuthorizeFilter("experimentteach:experimentbookingwait:view")]
        public ActionResult Record()
        {
            return View();
        }

        /// <summary>
        /// 简易登记视图
        /// </summary>
        /// <returns></returns>
        [AuthorizeFilter("experimentteach:experimentbookingwait:view")]
        public ActionResult RecordEasy()
        {
            return View();
        }

        public ActionResult EditRecord()
        {
            return View();
        }
        #endregion

        #region 简易模式视图

        [AuthorizeFilter("experimentteach:experimentbookingeasy:view")]
        public ActionResult EasyRecordSelectClass()
        {
            return View();
        }
        /// <summary>
        /// 简易登记
        /// </summary>
        /// <returns></returns>
        [AuthorizeFilter("experimentteach:experimentbookingeasy:view")]
        public ActionResult EasyRecordForm()
        {
            return View();
        }
        /// <summary>
        /// 简易登记编辑页面
        /// </summary>
        /// <returns></returns>
        [AuthorizeFilter("experimentteach:experimentbookingeasy:view")]
        public ActionResult EasyRecordEditForm()
        {
            return View();
        }
        /// <summary>
        /// 改版 简易模式已登记实验
        /// </summary>
        /// <returns></returns>
        [AuthorizeFilter("experimentteach:experimentbooking:view")]
        public ActionResult EasyRecordedList()
        {
            return View();
        }
        #endregion

        #region 获取数据

        [HttpGet]
        public async Task<ActionResult> GetFormJson(long id)
        {
            TData<ExperimentBookingEntity> obj = await experimentBookingBLL.GetEntity(id);
            return Json(obj);
        }

        [HttpGet]
        public async Task<ActionResult> GetListJson(long pid)
        {
            TData<List<ExperimentBookingEntity>> obj = await experimentBookingBLL.GetList(new ExperimentBookingListParam() { Pid = pid });
            return Json(obj);
        }

        /// <summary>
        /// 已预约实验列表
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        [HttpGet]
        [AuthorizeFilter("experimentteach:experimentbooking:search")]
        public async Task<ActionResult> GetExperimentBookingByUserList(ExperimentBookingListParam param, Pagination pagination)
        {
            OperatorInfo operatorinfo = await Operator.Instance.Current();
            param.BookUserId = operatorinfo.UserId;
            param.RecordMode = ExperimentRecordModeEnum.BookIng.ParseToInt();
            param.IsMain = 1;
            TData<List<ExperimentBookingEntity>> obj = await experimentBookingBLL.GetExperimentBookingList(param, pagination);
            return Json(obj);
        }

        /// <summary>
        /// 待安排实验列表
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        [HttpGet]
        [AuthorizeFilter("experimentteach:experimentbooking:search")]
        public async Task<ActionResult> GetWaitArrangeList(ExperimentBookingListParam param, Pagination pagination)
        {
            OperatorInfo operatorinfo = await Operator.Instance.Current();
            param.SafeguardUserId = operatorinfo.UserId;
            param.RecordMode = ExperimentRecordModeEnum.BookIng.ParseToInt();
            param.Statuz = ExperimentBookStatuzEnum.WaitArrange.ParseToInt();
            param.IsMain = 1;
            TData<List<ExperimentBookingEntity>> obj = await experimentBookingBLL.GetExperimentBookingList(param, pagination);
            return Json(obj);
        }

        /// <summary>
        /// 已安排实验列表
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        [HttpGet]
        [AuthorizeFilter("experimentteach:experimentbooking:search")]
        public async Task<ActionResult> GetAlreadyArrangeList(ExperimentBookingListParam param, Pagination pagination)
        {
            OperatorInfo operatorinfo = await Operator.Instance.Current();
            param.SafeguardUserId = operatorinfo.UserId;
            param.RecordMode = ExperimentRecordModeEnum.BookIng.ParseToInt();
            param.ThanStatuz = ExperimentBookStatuzEnum.WaitArrange.ParseToInt();
            param.IsMain = 1;
            TData<List<ExperimentBookingEntity>> obj = await experimentBookingBLL.GetExperimentBookingList(param, pagination);
            return Json(obj);
        }

        /// <summary>
        /// 待登记实验列表
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        [HttpGet]
        [AuthorizeFilter("experimentteach:experimentbooking:search")]
        public async Task<ActionResult> GetWaitRecordList(ExperimentBookingListParam param, Pagination pagination)
        {
            OperatorInfo operatorinfo = await Operator.Instance.Current();
            param.BookUserId = operatorinfo.UserId;
            param.RecordMode = ExperimentRecordModeEnum.BookIng.ParseToInt();
            param.Statuz = ExperimentBookStatuzEnum.WaitRecord.ParseToInt();
            param.IsMain = 1;
            TData<List<ExperimentBookingEntity>> obj = await experimentBookingBLL.GetExperimentBookingList(param, pagination);
            return Json(obj);
        }

        /// <summary>
        /// 已登记实验列表
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        [HttpGet]
        [AuthorizeFilter("experimentteach:experimentbooking:search")]
        public async Task<ActionResult> GetAlreadyRecordList(ExperimentBookingListParam param, Pagination pagination)
        {
            OperatorInfo operatorinfo = await Operator.Instance.Current();
            param.BookUserId = operatorinfo.UserId;
            param.RecordMode = ExperimentRecordModeEnum.BookIng.ParseToInt();
            param.Statuz = ExperimentBookStatuzEnum.AlreadyRecord.ParseToInt();
            param.IsMain = 1;
            TData<List<ExperimentBookingEntity>> obj = await experimentBookingBLL.GetExperimentBookingList(param, pagination);
            return Json(obj);
        }

        /// <summary>
        /// 简易模式-已登记实验列表
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        [HttpGet]
        [AuthorizeFilter("experimentteach:experimentbooking:search")]
        public async Task<ActionResult> GetEasyAlreadyRecordList(ExperimentBookingListParam param, Pagination pagination)
        {
            OperatorInfo operatorinfo = await Operator.Instance.Current();
            param.BookUserId = operatorinfo.UserId;
            param.RecordMode = ExperimentRecordModeEnum.Easy.ParseToInt();
            param.ThanStatuz = ExperimentBookStatuzEnum.WaitArrange.ParseToInt();
            param.IsMain = 0;
            param.ActivityType = ActivityTypeEnum.InClass.ParseToInt();
            TData<List<ExperimentBookingEntity>> obj = await experimentBookingBLL.GetRecordList(param, pagination);
            return Json(obj);
        }

        /// <summary>
        /// 实验开出记录查询
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        [HttpGet]
        [AuthorizeFilter("querystatisticsmanage:experimentteach:search")]
        public async Task<ActionResult> GetExperimentBookingList(ExperimentBookingListParam param, Pagination pagination)
        {
            param.Statuz = ExperimentBookStatuzEnum.AlreadyRecord.ParseToInt();
            param.IsMain = 0;
            TData<List<ExperimentBookingEntity>> obj = await experimentBookingBLL.GetExperimentBookingList(param, pagination);
            return Json(obj);
        }

        /// <summary>
        /// 实验开出记录统计
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        [HttpGet]
        [AuthorizeFilter("querystatisticsmanage:experimentteach:view")]
        public async Task<ActionResult> GetExperimentRecordStatistics(ExperimentBookingListParam param, Pagination pagination)
        {
            param.Statuz = ExperimentBookStatuzEnum.AlreadyRecord.ParseToInt();
            param.ActivityType = ActivityTypeEnum.InClass.ParseToInt();
            TData<List<ExperimentBookingEntity>> obj = await experimentBookingBLL.GetExperimentRecordStatistics(param, pagination);
            return Json(obj);
        }

        [HttpGet]
        public async Task<ActionResult> GetUserClassListJson(int schoolstage)
        {
            TData<List<SchoolGradeClassEntity>> obj = await experimentBookingBLL.GetGradeClassList(schoolstage);
            return Json(obj);
        }

        [HttpGet]
        public async Task<ActionResult> GetGradeClassByCourseJson(int courseid, string schoolstage="")
        {
            TData<List<SchoolGradeClassEntity>> obj = await experimentBookingBLL.GetGradeClassByCourse(courseid,schoolstage);
            //处理如果班级存在重复的问题。
            if (obj != null && obj.Data != null && obj.Data.Count > 0)
            {
                obj.Data = obj.Data.GroupBy(x => x.Id).Select(m => m.FirstOrDefault()).ToList();
            }
            return Json(obj);
        }

        [HttpGet]
        public async Task<ActionResult> GetUserCourseListJson(int schoolstage)
        {
            TData<List<StaticDictionaryEntity>> obj = await experimentBookingBLL.GetCourseList(schoolstage);
            return Json(obj);
        }
        /// <summary>
        /// 获取预约地点列表。
        /// </summary>
        /// <param name="id">年级班级Id</param>
        /// <param name="courseid">学科</param>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> GetFunRoomListJson(int schoolstage, int courseid)
        {
            TData<List<FunRoomEntity>> obj = await experimentBookingBLL.GetFunroomList(schoolstage, courseid);
            return Json(obj);
        }
        /// <summary>
        /// 获取预约地点列表。
        /// </summary>
        /// <param name="id">年级班级Id</param>
        /// <param name="courseid">学科</param>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> GetArrangerListJson(int schoolstage, int courseid)
        {
            TData<List<UserSchoolStageSubjectEntity>> obj = await experimentBookingBLL.GetArrangerList(schoolstage, courseid);
            return Json(obj);
        }
        /// <summary>
        /// 获取实验预约详细数据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> GetDetailJson(long id)
        {
            TData<ExperimentBookingEntity> obj = await experimentBookingBLL.GetDetailById(id);
            return Json(obj);
        }

        /// <summary>
        /// 实验预约，目录选择调用。
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<ActionResult> GetSelectVersionPageListJson(TextbookVersionDetailListParam param, Pagination pagination)
        {
            TData<List<TextbookVersionDetailEntity>> obj = await experimentBookingBLL.GetSelectVersionDetailPageList(param, pagination);
            return Json(obj);
        }

        /// <summary>
        /// 获取实验安排时自动带出的所需仪器和实验材料值
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> GetArrangerNeedValue(long id)
        {
            var (equipmentNeed, materialNeed) = await experimentBookingBLL.GetArrangerNeedValue(id);
            TData<object> obj = new TData<object>();
            obj.Data = new
            {
                ArrangerEquipmentNeed = equipmentNeed,
                ArrangerMaterialNeed = materialNeed
            };
            obj.Tag = 1;
            return Json(obj);
        }

        /// <summary>
        /// 根据上课时间获取学年学期
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> GetSchoolTermJson(SchoolTermListParam param)
        {
            TData obj = await schoolTermBLL.GetEntityByClassTime(param);
            return Json(obj);
        }

        /// <summary>
        /// 根据班级Id获取班级列表
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> GetGradeFormJson(long id)
        {
            TData<List<ComboBoxInfo>> obj = await experimentBookingBLL.GetGradeList(id);
            return Json(obj);
        }

        /// <summary>
        /// 获取实验，安排的最后一次安排的数据
        /// </summary>
        /// <param name="id"></param>
        /// <param name="sourcepath"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> GetLastArrangeNeed(long id,int sourcepath)
        {
            TData<object> obj = await experimentBookingBLL.GetLastArrangeNeed(id,sourcepath);
            return Json(obj);
        }

        /// <summary>
        /// 获取任务了列表
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        [HttpGet]
        [AuthorizeFilter("experimentteach:experimentbooking:search")]
        public async Task<ActionResult> GetTaskListJson(ExperimentBookingListParam param, Pagination pagination)
        {
            TData<List<ExperimentBookingEntity>> obj = await experimentBookingBLL.GetTaskList(param, pagination);
            return Json(obj);
        }


        /// <summary>
        /// 获取实验计划列表
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        [HttpGet]
        [AuthorizeFilter("experimentteach:experimentbooking:search")]
        public async Task<ActionResult> GetPlanListJson(ExperimentBookingListParam param, Pagination pagination)
        {
            param.ActivityType = ActivityTypeEnum.InClass.ParseToInt();
            var obj = await experimentBookingBLL.GetPlanList(param, pagination);
            return Json(obj);
        }

        /// <summary>
        /// 查询预约页面，该预约班级可添加的实验。
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        [HttpGet]
        [AuthorizeFilter("experimentteach:experimentbooking:search")]
        public async Task<ActionResult> GetAddBookingListJson(ExperimentBookingListParam param, Pagination pagination)
        {
            param.ActivityType = ActivityTypeEnum.InClass.ParseToInt();
            TData<List<ExperimentBookingEntity>> obj = await experimentBookingBLL.GetAddBookingList(param, pagination);
            return Json(obj);
        }
         
        /// <summary>
        /// 获取任务实验详情
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        [HttpGet]
        [AuthorizeFilter("experimentteach:experimentbooking:search")]
        public async Task<ActionResult> GetTaskFormJson(long taskid, long gradeclassid)
        {
            TData<ExperimentBookingEntity> obj = await experimentBookingBLL.GetTaskJson(taskid, gradeclassid);
            return Json(obj);
        }
        /// <summary>
        /// 获取是否开启实验目录选择。
        /// </summary>
        /// <param name="id"></param>
        /// <param name="sourcepath"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> GetIsOpenExperiment()
        {
            TData<int> obj = new TData<int>();
            //获取是【否开启实验目录选择】。
            OperatorInfo operatorInfo = await Operator.Instance.Current();
            var configset = await configSetBLL.GetEntityByCode(new Model.Param.SystemManage.ConfigSetListParam()
            {
                UnitId = operatorInfo.UnitId.Value,
                UnitType = (UnitTypeEnum)operatorInfo.UnitType,
                ConfigUnitType = UnitTypeEnum.County,
                TypeCode = "1003_SFKQSYMLXZ"
            }, operatorInfo);
            if (configset != null && configset.Data != null && configset.Data.ConfigValue == "1")
            {
                obj.Tag = 1;
                obj.Data = 1;
            }
            return Json(obj);
        }

        [HttpGet]
        public async Task<ActionResult> GetClassListJson(ExperimentBookingListParam param)
        {
            TData<List<SchoolGradeClassEntity>> obj = await experimentBookingBLL.GetClassList(param);
            return Json(obj);
        }

        /// <summary>
        /// 获取预约、安排时的仪器列表
        /// </summary>
        /// <param name="id">预约、登记Id</param>
        /// <param name="sourcetype">1：预约  2：安排</param>
        /// <returns></returns>
        [HttpGet]
        [AuthorizeFilter("experimentteach:experimentbooking:search")]
        public async Task<ActionResult> GetBookingInstrumentListJson(long id, int sourcetype = 1)
        {
            var obj = await experimentBookingBLL.GetBookingInstrumentList(id, sourcetype);
            return Json(obj);
        }

        #endregion

        #region 提交数据

        [HttpPost]
        [AuthorizeFilter("experimentteach:experimentbooking:add")]
        public async Task<ActionResult> AddPlanFormJson(ExperimentBookingInputModel model)
        {
            var obj = new TData<string>();
            //验证是否需要班级升级：
            OperatorInfo operatorinfo = await Operator.Instance.Current();
            if (operatorinfo==null)
            {
                obj.Message = "登录超时！";
                obj.Tag = -1;
                return Json(obj);
            }
            /**
             * 1:验证当前单位是否班级是否需要升级
             * 注：预约前，班级必须处于正常状态，就是当前学年学期下，根据入学开始年份，和年级的关系是否正确。
             */
            var isUpgrade = await schoolGradeClassBll.GetIsUpgrade(operatorinfo, 0);
            if (isUpgrade)
            {
                string optmsg = "预约";
                if (model.RecordMode == ExperimentRecordModeEnum.Easy.ParseToInt())
                {
                    optmsg = "登记";
                }
                obj.Tag = 0;
                obj.Message = "学校还未进行年级升级，禁止" + optmsg + "；请联系系统管理员进行年级升级。";
                return Json(obj);
            }
            obj = await experimentBookingBLL.AddPlanForm(model);
            return Json(obj);
        }

        [HttpPost]
        [AuthorizeFilter("experimentteach:experimentbooking:add,experimentteach:experimentbooking:edit")]
        public async Task<ActionResult> SaveFormJson(ExperimentBookingInputModel model)
        {
            TData<string> obj = await experimentBookingBLL.SaveForm(model);
            return Json(obj);
        }

        /// <summary>
        /// 实验预约删除
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("experimentteach:experimentbooking:delete")]
        public async Task<ActionResult> DeleteFormJson(long id)
        {
            TData obj = await experimentBookingBLL.DeleteForm(id);
            return Json(obj);
        }
        /// <summary>
        /// 简易登记保存
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("experimentteach:experimentbooking:add,experimentteach:experimentbooking:edit")]
        public async Task<ActionResult> SaveEasyFormJson(ExperimentBookingInputModel model)
        {
            TData<string> obj = await experimentBookingBLL.SaveEasyForm(model);
            return Json(obj);
        }

        [HttpPost]
        [AuthorizeFilter("experimentteach:experimentbooking:add,experimentteach:experimentbooking:edit")]
        public async Task<ActionResult> VerifyRegisterFormJson(ExperimentBookingInputModel model)
        {
            TData<string> obj = await experimentBookingBLL.VerifyRegister(model);
            return Json(obj);
        }

        /// <summary>
        /// 实验安排、登记
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<ActionResult> ArrangeRecordJson(ExperimentBookArrangeRecordInputModel model)
        {
            TData obj = await experimentBookingBLL.ArrangeRecordForm(model);
            return Json(obj);
        }

        /// <summary>
        /// 实验安排、登记、预约撤回
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<ActionResult> ArrangeRecordWithdrawJson(ArrageRecodeWithdrawInputModel model)
        {
            TData obj = await experimentBookingBLL.ArrangeRecordWithdrawForm(model);
            return Json(obj);
        }


        #endregion

        #region 改版获取实验预约页面信息

        /// <summary>
        /// 已预约列表
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        [HttpGet]
        [AuthorizeFilter("experimentteach:experimentbooking:search")]
        public async Task<ActionResult> GetBookingListedJson(ExperimentBookingListParam param, Pagination pagination)
        {
            OperatorInfo operatorinfo = await Operator.Instance.Current();
            param.BookUserId = operatorinfo.UserId;
            param.RecordMode = ExperimentRecordModeEnum.BookIng.ParseToInt();
            param.ThanStatuz = ExperimentBookStatuzEnum.WaitArrange.ParseToInt();
            param.IsMain = 1;
            if (pagination != null && !string.IsNullOrEmpty(pagination.Sort) && pagination.Sort.ToLower().Contains("classtime"))
            {
                if (pagination.SortType != null && pagination.SortType.ToLower() == "asc")
                {
                    pagination.Sort = " ClassTime ASC , SectionIndex ASC ";
                }
                else
                {
                    pagination.Sort = " ClassTime DESC , SectionIndex DESC ";
                }

            }
            param.ActivityType = ActivityTypeEnum.InClass.ParseToInt();
            TData<List<ExperimentBookingEntity>> obj = await experimentBookingBLL.GetBookingedList(param, pagination);
            return Json(obj);
        }

        /// <summary>
        /// 主预约数据Id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> GetEditFormJson(long id)
        {
            var obj = await experimentBookingBLL.GetEditEntity(id);
            return Json(obj);
        }

        /// <summary>
        /// 获取预约实验室
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> GetGetBookingFunRoomJson(long id)
        {
            OperatorInfo operatorInfo = await Operator.Instance.Current();
            var obj = await experimentBookingBLL.GetBookingFunRoomList(id);
            obj.Data.IsBookingBefore = 1;
            var configset = await configSetBLL.GetEntityByCode(new Model.Param.SystemManage.ConfigSetListParam()
            {
                UnitId = operatorInfo.UnitId.Value,
                UnitType = (UnitTypeEnum)operatorInfo.UnitType,
                ConfigUnitType = UnitTypeEnum.County,
                TypeCode = "1003_SFYXXZSYHYY"
            });
            if (configset != null && configset.Data != null && configset.Data.ConfigValue == "0")
            {
                obj.Data.IsBookingBefore = 0;
            }
            return Json(obj);
        }

        /// <summary>
        /// 获取预约节次数据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> GetSectionJson(long id,long funroomid,int weeknum)
        {
            var obj = await experimentBookingBLL.GetSection(id, funroomid, weeknum);
            return Json(obj);
        }

        /// <summary>
        /// 获取状态大于入室的仪器
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> GetInstrumentListJson(SchoolInstrumentListParam param, Pagination page)
        {
            if (param==null)
            {
                param = new SchoolInstrumentListParam();
            }
            param.ListType = 3;
            //2024-06-14 by lss 根据需求修改状态，只有入库才可以选择。
            param.Statuz = InstrumentInputStatuzEnum.WaitInputStorage.ParseToInt();//要大于待入室；
            var obj = await experimentBookingBLL.GetInstrumentList(param, page);
            if (obj!=null && obj.Data!=null && obj.Data.Count > 0)
            {
                obj.Data.ForEach(m => m.Model = m.Model ?? "");
            }
            return Json(obj);
        }

        /// <summary>
        /// 按目录预约-实验版本下拉数据列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<ActionResult> GetCatalogVersionListJson(TextbookVersionCurrentListParam param)
        {
            var obj = await experimentBookingBLL.GetVersionList(param); 
            return Json(obj);
        }

        #endregion

        #region 改版 预约页面保存提交方法20223-06-23
        /**
         * 1:保存班级上课节次信息。
         * 1.1:修改设置实验员。
         * 2：添加实验保存信息（添加实验每个班级都验证）
         * 3: 删除实验。
         * 4：添加实验仪器。
         * 
         * 99：总提交、确定、保存：保存 （预约说明、预约材料）
         * **/

        /// <summary>
        /// 修改班级预约时间节次信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("experimentteach:experimentbooking:edit")]
        public async Task<ActionResult> SaveBookingSectionJson(ExperimentBookingSectionInputModel model)
        {
            var  obj = await experimentBookingBLL.SaveBookingSection(model);
            return Json(obj);
        }

        /// <summary>
        /// 保存设置实验员
        /// </summary>
        /// <param name="id">预约表Id</param>
        /// <param name="userid">实验员Id</param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("experimentteach:experimentbooking:edit")]
        public async Task<ActionResult> SaveBookingExperimentUserJson(long id,long userid)
        {
            var obj = await experimentBookingBLL.SaveBookingExperimentUser(id,userid);
            return Json(obj);
        }

        /// <summary>
        /// 保存设置实验分组数
        /// </summary>
        /// <param name="id">预约表Id</param>
        /// <param name="groupz">分组数</param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("experimentteach:experimentbooking:edit")]
        public async Task<ActionResult> SaveBookingExperimentGroupzJson(long id, int groupz)
        {
            var obj = await experimentBookingBLL.SaveBookingExperimentGroupz(id, groupz);
            return Json(obj);
        }

        /// <summary>
        /// 保存预约说明
        /// </summary>
        /// <param name="id">预约表Id</param>
        /// <param name="userid">实验员Id</param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("experimentteach:experimentbooking:edit")]
        public async Task<ActionResult> SaveBookingExperimentRemarkJson(ExperimentBookingInputModel modelz)
        {
            var obj = await experimentBookingBLL.SaveBookingExperimentRemark(modelz);
            return Json(obj);
        }


        /// <summary>
        /// 保存预约实验仪器型号
        /// </summary>
        /// <param name="model">model{}</param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("experimentteach:experimentbooking:edit")]
        public async Task<ActionResult> SaveBookingInstrmentModelJson(ExperimentInstrumentInputModel modez)
        {
            var obj = await experimentBookingBLL.SaveBookingInstrmentModel(modez);
            return Json(obj);
        }

        /// <summary>
        /// 保存预约实验仪器单位
        /// </summary>
        /// <param name="model">model{}</param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("experimentteach:experimentbooking:edit")]
        public async Task<ActionResult> SaveBookingInstrmentUnitNamelJson(ExperimentInstrumentInputModel modelz)
        {
            var obj = await experimentBookingBLL.SaveBookingInstrmentUnitName(modelz);
            return Json(obj);
        }

        /// <summary>
        /// 保存预约实验仪器数量
        /// </summary>
        /// <param name="model">model{}</param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("experimentteach:experimentbooking:edit")]
        public async Task<ActionResult> SaveBookingInstrmentNumJson(ExperimentInstrumentInputModel modelz)
        {
            var obj = await experimentBookingBLL.SaveBookingInstrmentNum(modelz);
            return Json(obj);
        }


        /// <summary>
        /// 保存预约实验 实验名称
        /// </summary>
        /// <param name="model">model{}</param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("experimentteach:experimentbooking:edit")]
        public async Task<ActionResult> SaveBookingExperimentNameJson(ExperimentBookingInputModel modelz)
        {
            var obj = await experimentBookingBLL.SaveBookingExperimentName(modelz);
            return Json(obj);
        }

        /// <summary>
        /// 保存预约实验 实验材料
        /// </summary>
        /// <param name="model">model{}</param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("experimentteach:experimentbooking:edit")]
        public async Task<ActionResult> SaveBookingExperimentMaterialNeedJson(ExperimentBookingInputModel modelz)
        {
            var obj = await experimentBookingBLL.SaveBookingExperimentMaterialNeed(modelz);
            return Json(obj);
        }

        /// <summary>
        /// 添加预约实验
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("experimentteach:experimentbooking:edit")]
        public async Task<ActionResult> SaveBookingAddExperimentJson(ExperimentBookingInputModel modelz)
        {
            TData obj = await experimentBookingBLL.SaveBookingAddExperiment(modelz);
            return Json(obj);
        }

        /// <summary>
        /// 删除预约实验。
        /// </summary>
        /// <param name="model">model.Id</param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("experimentteach:experimentbooking:edit")]
        public async Task<ActionResult> SaveBookingDelExperimentJson(ExperimentBookingInputModel modelz)
        {
            TData obj = await experimentBookingBLL.SaveBookingDelExperiment(modelz);
            return Json(obj);
        }


        /// <summary>
        /// 添加预约实验仪器
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("experimentteach:experimentbooking:edit")]
        public async Task<ActionResult> SaveBookingAddApparatusJson(ExperimentInstrumentInputModel modelz)
        {
            TData obj = await experimentBookingBLL.SaveBookingAddApparatus(modelz);
            return Json(obj);
        }


        /// <summary>
        /// 删除添加预约实验仪器
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("experimentteach:experimentbooking:edit")]
        public async Task<ActionResult> SaveBookingDelApparatusJson(ExperimentInstrumentInputModel modelz)
        {
            TData obj = await experimentBookingBLL.SaveBookingDelApparatus(modelz);
            return Json(obj);
        }


        /// <summary>
        /// 确认保存预约，待安排
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("experimentteach:experimentbooking:edit")]
        public async Task<ActionResult> SaveBookingJson(ExperimentBookingInputModel modelz)
        {
            string wxs_token = GetSession("wxs_third_sysendmsg_token");
            TData obj = await experimentBookingBLL.SaveBooking(modelz, wxs_token);
            if (!string.IsNullOrEmpty(wxs_token))
            {
                var userlist = (List<long>)obj.ExtendData;
                if (userlist.Count > 0)
                {
                    SendExperimentUser(userlist, wxs_token);
                }
            }
            return Json(obj);
        }


        private async Task SendExperimentUser(List<long> userlist, string token)
        {
            var requestTokenUri = $"/contact/task/create";
            try
            {
                OperatorInfo operatorinfo = await Operator.Instance.Current();
                if (userlist != null && userlist.Count > 0)
                {
                    TData<List<AppManageEntity>> obj = await appManagerBLL.GetList(new AppManageListParam() { AppCode = "NTJidUZpdzBaMzZzSHlFdw", AppType = 1, IsMain = 2 });
                    if (obj.Total == 0)
                    {
                        throw new BusinessException("请联系运维人员在【第三方应用管理】->【第三方应用列表】中配置相关内容。");
                    }
                    AppManageEntity objEntity = obj.Data.FirstOrDefault();
                    var userEntity = await userBll.GetEntity(operatorinfo.UserId ?? 0);
                    string uuid = "";
                    if (userEntity != null && userEntity.Data != null)
                    {
                        uuid = userEntity.Data.ThirdUserId;
                    }
                    var unitEntity = await unitBll.GetEntity(operatorinfo.UnitId ?? 0);
                    string code = "";
                    if (unitEntity != null && unitEntity.Data != null)
                    {
                        code = unitEntity.Data.Code;
                    }
                    var param = new UserListParam();
                    param.UserIds = string.Join(",", userlist);
                    var resultUser = await userBll.GetList(param);
                    if (resultUser != null && resultUser.Data != null && resultUser.Data.Count > 0)
                    {
                        foreach (var item in resultUser.Data)
                        {
                            var tokenQueryParams = new Dictionary<string, string>
                            {
                                { "code", code },
                                { "uuid", uuid },
                                { "type", "0" },
                                { "content", "你有一个实验待安排。" },
                                { "rcv_uuuid", item.ThirdUserId },
                                { "parameter",  Guid.NewGuid().ToString() },
                                { "uri", objEntity.AuthHost },
                                { "wxuri", objEntity.CallBackUrl },
                            };

                            var content = new FormUrlEncodedContent(tokenQueryParams);

                            using var httpClient = new HttpClient(new HttpClientBusinessHandler("提交推送消息失败。"));

                            httpClient.BaseAddress = new Uri(objEntity.AuthHost);
                            var credentials = $"{objEntity.ClientId}:{objEntity.ClientSecret}";
                            var base64Credentials = Convert.ToBase64String(Encoding.ASCII.GetBytes(credentials));
                            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", base64Credentials);
                            httpClient.DefaultRequestHeaders.Add("token", token);
                            var response = await httpClient.PostAsync(requestTokenUri, content);
                            var tokenResponseJson = await response.Content.ReadAsStringAsync();
                            LogHelper.Error($"---->从【无锡市智慧教育】推送任务，给实验员推送消息，返回值：{tokenResponseJson}", null);
                            var tokenResponse = JsonConvert.DeserializeObject<WxResult>(tokenResponseJson);
                            if (tokenResponse.code == "0")
                            {
                                LogHelper.Error($"---->从【无锡市智慧教育】推送任务，给实验员推送消息，推送成功。", null);
                            }
                        }
                    }
                    else
                    {
                        LogHelper.Error($"---->从【无锡市智慧教育】推送任务，给实验员推送消息，未查询到需要推送数据的实验员，失败请求为:{requestTokenUri}", null);
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"---->从【无锡市智慧教育】推送任务，给实验员推送消息，失败请求为:{requestTokenUri}，{ex.StackTrace}", null);
                throw new BusinessException($"从【无锡市智慧教育】推送任务。" + ex.Message);
            }
        }

        /// <summary>
        /// 获取Session
        /// </summary>
        /// <param name="key">键</param>
        /// <returns>返回对应的值</returns>
        protected string GetSession(string key)
        {
            var value = HttpContext.Session.GetString(key);
            if (string.IsNullOrEmpty(value))
                value = string.Empty;
            return value;
        }

        #endregion

        #region 改版 获取安排信息
        /// <summary>
        /// 待安排实验列表
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        [HttpGet]
        [AuthorizeFilter("experimentteach:experimentbooking:search")]
        public async Task<ActionResult> GetArrangeListJson(ExperimentBookingListParam param, Pagination pagination)
        {
            OperatorInfo operatorinfo = await Operator.Instance.Current();
            param.SafeguardUserId = operatorinfo.UserId;
            param.RecordMode = ExperimentRecordModeEnum.BookIng.ParseToInt();
            param.Statuz = ExperimentBookStatuzEnum.WaitArrange.ParseToInt();
            param.IsMain = 1;
            if (pagination != null && !string.IsNullOrEmpty(pagination.Sort) && pagination.Sort.ToLower().Contains("classtime"))
            {
                if (pagination.SortType != null && pagination.SortType.ToLower() == "asc")
                {
                    pagination.Sort = " ClassTime ASC , SectionIndex ASC ";
                }
                else
                {
                    pagination.Sort = " ClassTime DESC , SectionIndex DESC ";
                }

            }
            param.ActivityType = ActivityTypeEnum.InClass.ParseToInt();
            TData<List<ExperimentBookingEntity>> obj = await experimentBookingBLL.GetArrangeList(param, pagination);
            return Json(obj);
        }

        [HttpGet]
        public async Task<ActionResult> GetArrangeFormJson(long id)
        {
            var obj = await experimentBookingBLL.GetEditArrangeForm(id);
            return Json(obj);
        }


        /// <summary>
        /// 待安排实验列表
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        [HttpGet]
        [AuthorizeFilter("experimentteach:experimentbooking:search")]
        public async Task<ActionResult> GetArrangeListedJson(ExperimentBookingListParam param, Pagination pagination)
        {
            OperatorInfo operatorinfo = await Operator.Instance.Current();
            param.SafeguardUserId = operatorinfo.UserId;
            param.RecordMode = ExperimentRecordModeEnum.BookIng.ParseToInt();
            param.ThanStatuz = ExperimentBookStatuzEnum.WaitArrange.ParseToInt();
            param.IsMain = 1;
            if (pagination!=null && !string.IsNullOrEmpty(pagination.Sort) && pagination.Sort.ToLower().Contains("classtime"))
            {
                if (pagination.SortType != null && pagination.SortType.ToLower() == "asc")
                {
                    pagination.Sort = " ClassTime ASC , SectionIndex ASC ";
                }
                else
                {
                    pagination.Sort = " ClassTime DESC , SectionIndex DESC ";
                }
               
            }
            param.ActivityType = ActivityTypeEnum.InClass.ParseToInt();
            TData<List<ExperimentBookingEntity>> obj = await experimentBookingBLL.GetArrangeList(param, pagination);
            return Json(obj);
        }
        #endregion

        #region 改版 保存安排信息 
        /// <summary>
        /// 保存安排意见
        /// </summary>
        /// <param name="model">model{}</param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("experimentteach:experimentbooking:edit")]
        public async Task<ActionResult> SaveBookingArrangerRemarkJson(ExperimentBookingInputModel model)
        {
            var obj = await experimentBookingBLL.SaveBookingArrangerRemark(model);
            return Json(obj);
        }

        [HttpPost]
        [AuthorizeFilter("experimentteach:experimentbooking:edit")]
        public async Task<ActionResult> SaveBookingArrangerLendJson(ExperimentBookingInputModel model)
        {
            var obj = await experimentBookingBLL.SaveBookingArrangerLend(model);
            return Json(obj);
        }

        /// <summary>
        /// 确认保存 安排
        /// </summary>
        /// <param name="model"></param>
        /// <param name="confirm">是否确定撤销（0：否  1：是）</param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("experimentteach:experimentbooking:edit")]
        public async Task<ActionResult> SaveArrangerJson(ExperimentBookingInputModel model, int confirm = 0)
        {
            TData obj = await experimentBookingBLL.SaveArranger(model, confirm);
            return Json(obj);
        }

        /// <summary>
        /// 撤销安排
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("experimentteach:experimentbooking:edit")]
        public async Task<ActionResult> RevokedArrangerJson(ExperimentBookingInputModel model)
        {
            TData obj = await experimentBookingBLL.RevokedArranger(model);
            return Json(obj);
        }
        #endregion

        #region 改版 批量安排操作 

        /// <summary>
        /// 批量安排，安排前清空之前的填写信息。
        /// </summary>
        /// <param name="id"></param>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> GetBatchArrangeFormJson(long id, string ids)
        {
            var idArr = TextHelper.SplitToArray<long>(ids, ',');
            if (idArr.Count() > 1)
            {
                var param = new ExperimentBookingInputModel();
                param.Ids = ids;
                await experimentBookingBLL.ClearBatchArrangeData(param);
            }
            var obj = await experimentBookingBLL.GetEditArrangeForm(id);
            return Json(obj);
        }

        /// <summary>
        /// 待安排实验列表
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        [HttpGet]
        [AuthorizeFilter("experimentteach:experimentbooking:search")]
        public async Task<ActionResult> GetArrangeClassListJson(ExperimentBookingListParam param, Pagination pagination)
        {
            TData<List<ExperimentBookingEntity>> obj = null;
            var idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
            if (idArr!=null)
            {
                obj = await experimentBookingBLL.GetArrangeClassList(idArr.ToList());
            }
            return Json(obj);
        }

        /// <summary>
        /// 确认保存 安排
        /// </summary>
        /// <param name="model"></param>
        /// <param name="confirm">是否确定撤销（0：否  1：是）</param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("experimentteach:experimentbooking:edit")]
        public async Task<ActionResult> SaveBatchArrangerJson(ExperimentBookingInputModel modelz, int confirm = 0)
        {
            TData<string> obj = new TData<string>();
            if (!string.IsNullOrEmpty(modelz.Ids))
            {
                var idList = TextHelper.SplitToArray<long>(modelz.Ids, ',');
                if (idList != null && idList.Count() > 0)
                {
                    for (int i = 0; i < idList.Count(); i++)
                    {
                        modelz.Id = idList[i];
                        var resultObj = await experimentBookingBLL.SaveArranger(modelz, confirm);
                        if (i == 0)
                        {
                            obj = resultObj;
                        }
                        else
                        {
                            if (obj.Tag == 2)
                            {
                                modelz.Id = idList[i];
                                await experimentBookingBLL.SaveArranger(modelz, 1);
                            }
                        }
                        if (obj.Tag == 2 || obj.Tag == 0)
                        {
                            return Json(obj);
                        }
                    }
                }
            }
            return Json(obj);
        }


        /// <summary>
        /// 批量保存安排意见
        /// </summary>
        /// <param name="model">model{}</param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("experimentteach:experimentbooking:edit")]
        public async Task<ActionResult> SaveBookingBatchArrangerRemarkJson(ExperimentBookingInputModel modelz)
        {
            TData<string> obj = new TData<string>();
            if (!string.IsNullOrEmpty(modelz.Ids))
            {
                var idList = TextHelper.SplitToArray<long>(modelz.Ids, ',');
                if (idList != null && idList.Count() > 0)
                {
                    for (int i = 0; i < idList.Count(); i++)
                    {
                        modelz.Id = idList[i];
                        var resultObj = await experimentBookingBLL.SaveBookingArrangerRemark(modelz);
                        if (i == 0)
                        {
                            obj = resultObj;
                        }
                    }
                }
            }
            return Json(obj);
        }
        /// <summary>
        /// 批量保存是否借出仪器
        /// </summary>
        /// <param name="modelz"></param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("experimentteach:experimentbooking:edit")]
        public async Task<ActionResult> SaveBookingBatchArrangerLendJson(ExperimentBookingInputModel modelz)
        {
            TData<string> obj = new TData<string>();
            if (!string.IsNullOrEmpty(modelz.Ids))
            {
                var idList = TextHelper.SplitToArray<long>(modelz.Ids, ',');
                if (idList != null && idList.Count() > 0)
                {
                    for (int i = 0; i < idList.Count(); i++)
                    {
                        modelz.Id = idList[i];
                        var resultObj = await experimentBookingBLL.SaveBookingArrangerLend(modelz);
                        if (i == 0)
                        {
                            obj = resultObj;
                        }
                    }
                }
            }
            return Json(obj);
        }

        /// <summary>
        /// 安排批量保存 实验材料
        /// </summary>
        /// <param name="model">model{}</param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("experimentteach:experimentbooking:edit")]
        public async Task<ActionResult> SaveBookingBatchExperimentMaterialNeedJson(ExperimentBookingInputModel modelz)
        {
            TData<string> obj = new TData<string>();
            if (!string.IsNullOrEmpty(modelz.Ids))
            {
                var idList = TextHelper.SplitToArray<long>(modelz.Ids, ',');
                if (idList != null && idList.Count() > 0)
                {
                    for (int i = 0; i < idList.Count(); i++)
                    {
                        modelz.Id = idList[i];
                        var resultObj = await experimentBookingBLL.SaveBookingExperimentMaterialNeed(modelz);
                        if (i == 0)
                        {
                            obj = resultObj;
                        }
                    }
                }
            }
            return Json(obj);
        }


        /// <summary>
        /// 添加预约实验仪器
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("experimentteach:experimentbooking:edit")]
        public async Task<ActionResult> SaveBookingBatchAddApparatusJson(ExperimentInstrumentInputModel modelz)
        {
            var obj = await experimentBookingBLL.SaveBookingBatchAddApparatus(modelz); 
            return Json(obj);
        }


        /// <summary>
        /// 删除添加预约实验仪器
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("experimentteach:experimentbooking:edit")]
        public async Task<ActionResult> SaveBookingBatchDelApparatusJson(ExperimentInstrumentInputModel modelz)
        {
            var obj = await experimentBookingBLL.SaveBookingBatchDelApparatus(modelz);
            return Json(obj);

        }

        /// <summary>
        /// 保存预约实验仪器型号
        /// </summary>
        /// <param name="model">model{}</param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("experimentteach:experimentbooking:edit")]
        public async Task<ActionResult> SaveBookingBatchInstrmentModelJson(ExperimentInstrumentInputModel modelz)
        {
            var obj = await experimentBookingBLL.SaveBookingBatchInstrmentModel(modelz);
            return Json(obj);
        }

        /// <summary>
        /// 保存预约实验仪器单位
        /// </summary>
        /// <param name="model">model{}</param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("experimentteach:experimentbooking:edit")]
        public async Task<ActionResult> SaveBookingBatchInstrmentUnitNamelJson(ExperimentInstrumentInputModel modelz)
        {
            var obj = await experimentBookingBLL.SaveBookingBatchInstrmentUnitName(modelz);
            return Json(obj);
        }

        /// <summary>
        /// 保存预约实验仪器数量
        /// </summary>
        /// <param name="model">model{}</param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("experimentteach:experimentbooking:edit")]
        public async Task<ActionResult> SaveBookingBatchInstrmentNumJson(ExperimentInstrumentInputModel modelz)
        {
            var obj = await experimentBookingBLL.SaveBookingBatchInstrmentNum(modelz);
            return Json(obj);
        }

        #endregion

        #region 改版 获取登记信息

        /// <summary>
        /// 待登记实验列表
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        [HttpGet]
        [AuthorizeFilter("experimentteach:experimentbooking:search")]
        public async Task<ActionResult> GetRecordListJson(ExperimentBookingListParam param, Pagination pagination)
        {
            OperatorInfo operatorinfo = await Operator.Instance.Current();
            param.BookUserId = operatorinfo.UserId;
            param.RecordMode = ExperimentRecordModeEnum.BookIng.ParseToInt();
            param.Statuz = ExperimentBookStatuzEnum.WaitRecord.ParseToInt();
            param.IsMain = 0;
            param.ActivityType = ActivityTypeEnum.InClass.ParseToInt();
            TData<List<ExperimentBookingEntity>> obj = await experimentBookingBLL.GetRecordList(param, pagination);
            if (obj != null && obj.Data != null && obj.Data.Count > 0)
            {
                obj.Data.ForEach(m => m.IsAllowRecord = GetIsAllowRecord(m));
            }
            return Json(obj);
        }
        /// <summary>
        /// 获取登记时间，是否小于当前时间（是否已开始上课）
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        private int GetIsAllowRecord(ExperimentBookingEntity entity)
        {
            int isAllowRecord = 0;
            if (entity!=null)
            {
                var currentDate = DateTime.Now;
                if (entity.ClassTime < currentDate.Date)
                {
                    isAllowRecord = 1;
                }
                else if (entity.ClassTime == currentDate.Date)
                {
                    if (entity.SectionBgnTime!=null && entity.SectionBgnTime.IndexOf(":") > -1)
                    {
                        var beginTimeArr = entity.SectionBgnTime.Split(":");
                        if (beginTimeArr.Length ==2)
                        {
                            if (beginTimeArr[0]!=null)
                            {
                                int hour = 0;
                                int.TryParse(beginTimeArr[0].ToString(), out hour);
                                if (currentDate.Hour > hour)
                                {
                                    isAllowRecord = 1;
                                }
                                else if (currentDate.Hour == hour)
                                {
                                    if (beginTimeArr[1] != null)
                                    {
                                        int minute = 0;
                                        int.TryParse(beginTimeArr[1].ToString(), out minute);
                                        if (currentDate.Minute > minute)
                                        {
                                            isAllowRecord = 1;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            return isAllowRecord;
        }
        /// <summary>
        /// 已经登记实验列表
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        [HttpGet]
        [AuthorizeFilter("experimentteach:experimentbooking:search")]
        public async Task<ActionResult> GetRecordListedJson(ExperimentBookingListParam param, Pagination pagination)
        {
            OperatorInfo operatorinfo = await Operator.Instance.Current();
            param.BookUserId = operatorinfo.UserId;
            param.RecordMode = ExperimentRecordModeEnum.BookIng.ParseToInt();
            param.ThanStatuz = ExperimentBookStatuzEnum.WaitRecord.ParseToInt();
            param.IsMain = 0;
            param.ActivityType = ActivityTypeEnum.InClass.ParseToInt();
            TData<List<ExperimentBookingEntity>> obj = await experimentBookingBLL.GetRecordList(param, pagination);
            return Json(obj);
        }

        /// <summary>
        /// 获取登记信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> GetRecordFormJson(long id)
        {
            var obj = await experimentBookingBLL.GetRecordForm(id);
            return Json(obj);
        }

        /// <summary>
        /// 获取当前学期信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> GetSchoolTermInfo()
        {
            OperatorInfo operatorinfo = await Operator.Instance.Current();
            TData<object> obj = new TData<object>();
            if (operatorinfo != null)
            {
                obj.Data = new {
                    operatorinfo.SchoolTermStartYear,
                    operatorinfo.SchoolTerm
                };
                obj.Tag = 1;
                obj.Message = "查询成功";
            }
            return Json(obj);
        }
        #endregion

        #region 查看页面
        /// <summary>
        /// 查看公共页面
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> GetDetailFormJson(long id, long pid,int statuz)
        {
            var obj = await experimentBookingBLL.GetDetailForm(id, pid, statuz);
            return Json(obj);
        }
        #endregion
        #region 保存登记信息

        /// <summary>
        /// 查看公共页面
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> GetRecordDetailFormJson(long id)
        {
            var obj = await experimentBookingBLL.GetRecordDetailForm(id);
            return Json(obj);
        }

        /// <summary>
        /// 保存登记信息-汇总意见
        /// </summary>
        /// <param name="model">model{}</param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("experimentteach:experimentbooking:edit")]
        public async Task<ActionResult> SaveBookingExperimentSummaryJson(ExperimentBookingInputModel model)
        {
            var obj = await experimentBookingBLL.SaveBookingExperimentSummary(model);
            return Json(obj);
        }

        /// <summary>
        /// 保存登记信息-问题描述
        /// </summary>
        /// <param name="model">model{}</param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("experimentteach:experimentbooking:edit")]
        public async Task<ActionResult> SaveBookingProblemDescJson(ExperimentBookingInputModel model)
        {
            var obj = await experimentBookingBLL.SaveBookingProblemDesc(model);
            return Json(obj);
        }

        /// <summary>
        /// 保存登记信息-运行状态
        /// </summary>
        /// <param name="model">model{}</param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("experimentteach:experimentbooking:edit")]
        public async Task<ActionResult> SaveBookingRunStatuzJson(ExperimentBookingInputModel model)
        {
            var obj = await experimentBookingBLL.SaveBookingRunStatuz(model);
            return Json(obj);
        }

        /// <summary>
        /// 保存登记信息-登记添加照片
        /// </summary>
        /// <param name="model">model{}</param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("experimentteach:experimentbooking:edit")]
        public async Task<ActionResult> SaveRecordImageJson(ExperimentBookingInputModel model)
        {
            var obj = await experimentBookingBLL.SaveRecordAddImage(model);
            return Json(obj);
        }

        /// <summary>
        /// 保存登记信息-登记照片
        /// </summary>
        /// <param name="model">model{}</param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("experimentteach:experimentbooking:edit")]
        public async Task<ActionResult> SaveRecordDelImageJson(ExperimentBookingInputModel model)
        {
            var obj = await experimentBookingBLL.SaveRecordDelImage(model);
            return Json(obj);
        }

        /// <summary>
        /// 保存登记信息-运行状态
        /// </summary>
        /// <param name="model">model{}</param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("experimentteach:experimentbooking:edit")]
        public async Task<ActionResult> SaveRecordJson(ExperimentBookingInputModel model)
        {
            var obj = await experimentBookingBLL.SaveRecord(model);
            return Json(obj);
        }
       
        /// <summary>
        /// 保存简易登记
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("experimentteach:experimentbooking:add,experimentteach:experimentbooking:edit")]
        public async Task<ActionResult> SaveEasyRecordJson(ExperimentBookingInputModel model)
        {
            var obj = await experimentBookingBLL.SaveEasyRecord(model);
            return Json(obj);
        }
        #endregion

        #region 已登记修改保存



        [HttpPost]
        [AuthorizeFilter("experimentteach:experimentbooking:edit")]
        public async Task<ActionResult> SaveEditRecordJson(ExperimentBookingInputModel model)
        {
            var obj = await experimentBookingBLL.SaveEditRecord(model);
            return Json(obj);
        }
        #endregion

        #region 已预约待安排撤销

        /// <summary>
        /// 预约撤回
        /// </summary>
        /// <param name="id"></param>
        /// <param name="confirm">是否确定撤销（0：否  1：是）</param>
        /// <returns></returns>
        public async Task<ActionResult> BookingWithdrawJson(long id, int confirm = 0)
        {
            TData obj = await experimentBookingBLL.BookingWithdraw(id, confirm);
            return Json(obj);
        }


        #endregion
    }
    /// <summary>
    /// 无锡 推送消息，调用推送消息实验员
    /// </summary>
    public class WxResult
    {
        public string code { get; set; }

        public string msg { get; set; }

        public string request_id { get; set; }
    }
}