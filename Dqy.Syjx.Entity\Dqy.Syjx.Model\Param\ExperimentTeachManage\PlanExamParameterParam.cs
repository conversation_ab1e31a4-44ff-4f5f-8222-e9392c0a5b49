﻿using System;
using System.Collections.Generic;
using Newtonsoft.Json;
using Dqy.Syjx.Util;
using System.ComponentModel.DataAnnotations.Schema;

namespace Dqy.Syjx.Model.Param.ExperimentTeachManage
{
    /// <summary>
    /// 创 建：陶瑞
    /// 日 期：2024-03-07 17:14
    /// 描 述：设置考核实验实体查询类
    /// </summary>
    public class PlanExamListParameterListParam
    {
        [Json<PERSON>onverter(typeof(StringJsonConverter))]
        public long? Id { get; set; }
        /// <summary>
        /// 是否需要初始化判断 1：需要
        /// </summary>
        public int? IsInit { get; set; }

        [JsonConverter(typeof(StringJsonConverter))]
        public long? UnitId { get; set; }

        [JsonConverter(typeof(StringJsonConverter))]
        public long? SchoolId { get; set; }

        private string _ids;
        public string Ids
        {
            get { return StringFilter.SearchSql(_ids); }
            set { _ids = value; }
        }
        private string _Name;
        public string Name
        {
            get { return StringFilter.SearchSql(_Name); }
            set { _Name = value; }
        }
        /// <summary>
        /// 学年
        /// </summary>
        public int? SchoolYearStart { get; set; }
        /// <summary>
        /// 学期
        /// </summary>
        public int? SchoolTerm { get; set; }
        /// <summary>
        /// 学段
        /// </summary>
        public int? SchoolStage { get; set; }
        /// <summary>
        /// 学段集合
        /// </summary>
        public List<int> SchoolStageList { get; set; }
        /// <summary>
        /// 学校属性集合
        /// </summary>
        public List<int> SchoolPropList { get; set; }
        /// <summary>
        /// 年级
        /// </summary>
        public int? GradeId { get; set; }
        /// <summary>
        /// 年级集合
        /// </summary>
        public List<int> GradeIdList { get; set; }
        /// <summary>
        /// 学科
        /// </summary>
        public int? CourseId { get; set; }
        /// <summary>
        /// 学科集合
        /// </summary>
        public List<int> CourseIdList { get; set; }
        /// <summary>
        /// 高中学科类型
        /// </summary>
        public int? CompulsoryType { get; set; }
        private string _teachername;
        /// <summary>
        /// 任课老师
        /// </summary>
        public string TeacherName
        {
            get { return StringFilter.SearchSql(_teachername); }
            set { _teachername = value; }
        }
        /// <summary>
        /// 是否当前学年 (2:不是 )
        /// </summary>
        public int IsCurrentSchoolYear { get; set; } = -10000;
        /// <summary>
        /// 课程活动类型(1: 课内 2:课外)
        /// </summary>
        public int ActivityType { get; set; } = -10000;
    }


    public class SerachApiPlanExamParam : SerachApiParam
    {
        public SerachApiPlanExamParam()
        {
        }


        [JsonConverter(typeof(StringJsonConverter))]
        public long? UnitId { get; set; }


        [JsonConverter(typeof(StringJsonConverter))]
        public long? SchoolId { get; set; }

        /// <summary>
        /// 学年
        /// </summary>
        public int? SchoolYearStart { get; set; }
        /// <summary>
        /// 学期
        /// </summary>
        public int? SchoolTerm { get; set; }
        /// <summary>
        /// 学段
        /// </summary>
        public int? SchoolStage { get; set; }

        /// <summary>
        /// 是否当前学年 (2:不是 )
        /// </summary>
        public int IsCurrentSchoolYear { get; set; } = -10000;
        /// <summary>
        /// 课程活动类型(1: 课内 2:课外)
        /// </summary>
        public int ActivityType { get; set; } = -10000;
    }

}
