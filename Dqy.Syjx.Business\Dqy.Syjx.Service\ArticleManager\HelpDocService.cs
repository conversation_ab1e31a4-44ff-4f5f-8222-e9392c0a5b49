﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.ArticleManager;
using Dqy.Syjx.Model.Param.ArticleManager;

namespace Dqy.Syjx.Service.ArticleManager
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2023-08-11 09:15
    /// 描 述：服务类
    /// </summary>
    public class HelpDocService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<HelpDocEntity>> GetList(HelpDocListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<HelpDocEntity>> GetPageList(HelpDocListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<HelpDocEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<HelpDocEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(HelpDocEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(HelpDocEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
               strSql = $" update d_HelpDoc set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
               strSql = $"update d_HelpDoc set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<HelpDocEntity, bool>> ListFilter(HelpDocListParam param)
        {
            var expression = LinqExtensions.True<HelpDocEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }

                if (param.SysMenuId > 0)
                {
                    expression = expression.And(t => t.SysMenuId == param.SysMenuId);
                }

                if (!string.IsNullOrEmpty(param.URL))
                {
                    expression = expression.And(t => t.MenuUrl == param.URL);
                }

                if (param.Status > 0)
                {
                    expression = expression.And(t => t.Status == param.Status);
                }
            }
            return expression;
        }
        #endregion
    }
}
