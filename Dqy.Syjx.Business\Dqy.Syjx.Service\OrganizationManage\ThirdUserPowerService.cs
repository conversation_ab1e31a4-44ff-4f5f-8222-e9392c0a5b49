﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.OrganizationManage;
using Dqy.Syjx.Model.Param.OrganizationManage;

namespace Dqy.Syjx.Service.OrganizationManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2022-08-11 11:13
    /// 描 述：第三方登录预授权服务类
    /// </summary>
    public class ThirdUserPowerService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<ThirdUserPowerEntity>> GetList(ThirdUserPowerListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<ThirdUserPowerEntity>> GetPageList(ThirdUserPowerListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<ThirdUserPowerEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<ThirdUserPowerEntity>(id);
        }

        /// <summary>
        /// 根据手机号码获取第三方预授权信息
        /// </summary>
        /// <param name="mobile"></param>
        /// <returns></returns>
        public async Task<ThirdUserPowerEntity> GetThirdUserPower(string mobile)
        {
            var expression = LinqExtensions.True<ThirdUserPowerEntity>();
            expression = expression.And(t => t.BaseIsDelete == 0);
            expression = expression.And(t => t.Mobile == mobile);
            expression = expression.And(a => a.RelationUserId == null || a.RelationUserId == 0);
            var list = await this.BaseRepository().FindList(expression);
            return list.FirstOrDefault();         
        }

        public bool ExistUnitName(ThirdUserPowerEntity entity)
        {
            var expression = LinqExtensions.True<ThirdUserPowerEntity>();
            expression = expression.And(t => t.BaseIsDelete == 0);
            if (entity.Id.IsNullOrZero())
            {
                expression = expression.And(t => t.UnitName == entity.UnitName);
            }
            else
            {
                expression = expression.And(t => t.UnitName == entity.UnitName && t.Id != entity.Id);
            }
            return this.BaseRepository().IQueryable(expression).Count() > 0 ? true : false;
        }

        public bool ExistMobile(ThirdUserPowerEntity entity)
        {
            var expression = LinqExtensions.True<ThirdUserPowerEntity>();
            expression = expression.And(t => t.BaseIsDelete == 0);
            if (entity.Id.IsNullOrZero())
            {
                expression = expression.And(t => t.Mobile == entity.Mobile);
            }
            else
            {
                expression = expression.And(t => t.Mobile == entity.Mobile && t.Id != entity.Id);
            }
            return this.BaseRepository().IQueryable(expression).Count() > 0 ? true : false;
        }

        public bool ExistUserNameAndMobile(ThirdUserPowerEntity entity)
        {
            var expression = LinqExtensions.True<ThirdUserPowerEntity>();
            expression = expression.And(t => t.BaseIsDelete == 0);
            if (entity.Id.IsNullOrZero())
            {
                expression = expression.And(t => t.UserName == entity.UserName && t.Mobile == entity.Mobile);
            }
            else
            {
                expression = expression.And(t => t.UserName == entity.UserName && t.Mobile == entity.Mobile && t.Id != entity.Id);
            }
            return this.BaseRepository().IQueryable(expression).Count() > 0 ? true : false;
        }

        #endregion

        #region 提交数据
        public async Task SaveForm(ThirdUserPowerEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(ThirdUserPowerEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update up_ThirdUserPower set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
                strSql = $"update up_ThirdUserPower set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<ThirdUserPowerEntity, bool>> ListFilter(ThirdUserPowerListParam param)
        {
            var expression = LinqExtensions.True<ThirdUserPowerEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (param.UnitTypeId != null && param.UnitTypeId != -1)
                {
                    expression = expression.And(t=>t.UnitTypeId == param.UnitTypeId);
                }
                if (!string.IsNullOrEmpty(param.Key))
                {
                    expression = expression.And(t => t.UnitName.Contains(param.Key) || t.UserName.Contains(param.Key) || t.Mobile.Contains(param.Key) || t.RoleNames.Contains(param.Key));
                }
                if (!string.IsNullOrEmpty(param.StartTime.ParseToString()))
                {
                    expression = expression.And(t => t.BaseModifyTime >= param.StartTime);
                }
                if (!string.IsNullOrEmpty(param.EndTime.ParseToString()))
                {
                    param.EndTime = param.EndTime.Value.Date.Add(new TimeSpan(23, 59, 59));
                    expression = expression.And(t => t.BaseCreateTime <= param.EndTime);
                }

            }
            return expression;
        }
        #endregion
    }
}
