﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
 }
<style type="text/css">
    .select2-container { width: 100% !important; }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <input id="CountyName" col="CountyName" type="text" placeholder="区县名称" style="display: inline-block;width:150px;">
                    </li>
                    <li>
                        <div id="UnitId" col="UnitId" type="text" style="display: inline-block;width:200px;"></div>
                    </li>
                    
                    <li>
                        <input id="CameraName" col="CameraName" type="text" placeholder="摄像机名称" style="display: inline-block;width:150px;">
                    </li>
                    <li>
                        <input id="CameraCode" col="CameraCode" type="text" placeholder="摄像机编码" style="display: inline-block;width:150px;">
                    </li>
                    <li>
                        <input id="Name" col="Name" type="text" placeholder="摄像机所在功能室和地点" style="display: inline-block;width:200px;">
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                        <a class="btn btn-default btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group-sm hidden-xs" id="toolbar">
            <a onclick="exportForm();" class="btn btn-warning"><i class="fa fa-download"></i> 下载模板</a>
            <a class="btn btn-info " onclick="showSaveForm()"><i class="fa fa-upload"></i> 导入</a>
            <a id="btnExport" class="btn btn-success btn-sm" onclick="exportDataForm()"><i class="fa fa-download"></i>导出</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    $(function () {
        $("#UnitId").ysComboBox({
            url: '@Url.Content("~/OrganizationManage/Unit/GetListJson?PageSize=10000")',
            key: "Id",
            value: "Name",
            defaultName: '学校名称',
        });
        initGrid();
        
        
    });

    function initGrid() {
        var queryUrl = '@Url.Content("~/CameraManage/SchoolCamera/GetPageListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            sortName: 'Id asc',
            sortOrder: 'DESC',
            columns: [
                { checkbox: true, visible: true },
                { field: 'Id', title: 'Id', visible: false },
                { field: 'CountyName', title: '区县名称', sortable: true, width: 200, halign: 'center', valign: 'middle' },
                { field: 'SchoolName', title: '学校名称', sortable: true, width: 200, halign: 'center', valign: 'middle' },
                { field: 'SrcName', title: '摄像机名称', sortable: true, width: 240, halign: 'center', valign: 'middle' },
                { field: 'SrcIndex', title: '摄像机编码', sortable: true, width: 200, halign: 'center', valign: 'middle' },
                { field: 'Address', title: '摄像机所在功能室和地点', sortable: true, width: 160, halign: 'center', valign: 'middle' },
                { field: 'Remark', title: '备注', sortable: true, width: 140, halign: 'center', valign: 'middle' },
                {
                    field: 'Statuz', title: '状态', sortable: true, width: 100, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        if (row.Statuz == "@StatusEnum.Yes.ParseToInt()") {
                            return '<span class="badge badge-primary">' + "@StatusEnum.Yes.GetDescription()" + '</span>';
                        } else {
                            return '<span class="badge badge-warning">' + "@StatusEnum.No.GetDescription()" + '</span>';
                        }
                    }
                },
                {
                    field: 'opt', title: '操作', width: 100, halign: 'center', align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        if (row.Statuz == @StatusEnum.Yes.ParseToInt()) {
                            actions.push('&nbsp;<a class="btn btn-warning btn-xs" href="#" onclick="setStatuz(false,\'' + row.Id + '\');"><i class="fa fa-check"></i>禁用</a>&nbsp;');
                        } else {
                            actions.push('&nbsp;<a class="btn btn-primary btn-xs" href="#" onclick="setStatuz(true,\'' + row.Id + '\');"><i class="fa fa-check"></i>启用</a>&nbsp;');
                        }
                        return actions.join('');
                    }
                }
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function resetGrid() {
        $("#UnitId").ysComboBox('setValue', -1);
        $('#Name').val("");
        $('#CameraName').val("");
        $('#CountyName').val("");
        $('#CameraCode').val("");
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function exportForm() {
        var schoolId = $("#UnitId").ysComboBox('getValue');
        var url = '@Url.Content("~/CameraManage/SchoolCamera/ExportSchoolListJson?schoolId=")' + schoolId;
        ys.exportExcel(url, undefined);
    }
    function showSaveForm() {
        ys.openDialog({
            title: '导入',
            content: '@Url.Content("~/CameraManage/SchoolCamera/Import")',
            width: '768px',
            height: '450px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }
    function setStatuz(isYes,id) {
        var msg = '确认要禁用该摄像头吗？';
        var statuz = @StatusEnum.No.ParseToInt();
        if (isYes) {
            statuz =  @StatusEnum.Yes.ParseToInt();
            '确认要启用该摄像头吗？';
        }
        ys.confirm(msg, function () {
                ys.ajax({
                    url: '@Url.Content("~/CameraManage/SchoolCamera/SetStatuzJson")' + '?id=' + id + '&statuz=' + statuz,
                    type: 'post',
                    success: function (obj) {
                        if (obj.Tag == 1) {
                            ys.msgSuccess(obj.Message);
                            searchGrid();
                        }
                        else {
                            ys.msgError(obj.Message);
                        }
                    }
                });
        });
    }

    function preview(srcName) {
         ys.ajax({
             url: '@Url.Content("~/CameraManage/CameraPoint/GetCameraPoint")' + '?cameraName=' + srcName,
                    type: 'get',
                    success: function (obj) {
                        if (obj.Tag == 1) {
                            var url = '@Url.Content("~/QueryStatisticsManage/PatrolClass/Preview")' + '?index=' + obj.Data.CameraIndexCode;
                            window.open(url);
                        }
                        else {
                            ys.msgError(obj.Message);
                        }
                    }
                });
    }

    function exportDataForm() {
        var url = '@Url.Content("~/CameraManage/SchoolCamera/ExportSchoolcamera")';
        var pagination = $('#gridTable').ysTable('getPagination', { "sort": "Id ASC", "order": "DESC", "offset": 0, "limit": 10 });
        var postData = $("#searchDiv").getWebControls(pagination);
        ys.exportExcel(url, postData);

    }
</script>
