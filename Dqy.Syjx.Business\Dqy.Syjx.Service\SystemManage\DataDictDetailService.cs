using System;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.SystemManage;
using Dqy.Syjx.Model.Param.SystemManage;
using System.Linq.Expressions;

namespace Dqy.Syjx.Service.SystemManage
{
    public class DataDictDetailService : RepositoryFactory
    {
        #region 获取数据
        public async Task<List<DataDictDetailEntity>> GetList(DataDictDetailListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.OrderBy(p => p.DictSort).ToList();
        }

        public async Task<List<DataDictDetailEntity>> GetSortList(DataDictDetailListParam param)
        {
            var expression = ListFilter(param);
            var pagination = new Pagination();
            pagination.Sort = " DictSort ";
            pagination.SortTy
        }

        public async Task<List<DataDictDetailEntity>> GetList(DataDictDetailListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.OrderBy(p => p.DictSort).ToList();
        }

        public async Task<List<DataDictDetailEntity>> GetSortList(DataDictDetailListParam param)
        {
            var expression = ListFilter(param);
            var pagination = new Pagination();
            pagination.Sort = " DictSort ";
            pagination.SortType = "asc";
            pagination.PageIndex = 1;
            pagination.PageSize = int.MaxValue;
            var list = await this.BaseRepository().FindList(expression, pagination);
            return list.OrderBy(p => p.DictSort).ToList();
        }

        public async Task<List<DataDictDetailEntity>> GetPageList(DataDictDetailListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<DataDictDetailEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<DataDictDetailEntity>(id);
        }

        public async Task<int> GetMaxSort(long unitid)
        {
            string sql = "SELECT MAX(DictSort) FROM SysDataDictDetail";
            if (unitid > 0)
            {
                sql += $" Where UnitId = {unitid} ";
            }
            object result = await this.BaseRepository().FindObject(sql);
            int sort = result.ParseToInt();
            sort++;
            return sort;
        }

        public bool ExistDictKeyValue(DataDictDetailEntity entity)
        {
            var expression = LinqExtensions.True<DataDictDetailEntity>();
            expression = expression.And(t => t.BaseIsDelete == 0);
            if (entity.Id.IsNullOrZero())
            {
                expression = expression.And(t => t.DictType == entity.DictType && (t.DictKey == entity.DictKey || t.DictValue == entity.DictValue));
            }
            else
            {
                expression = expression.And(t => t.DictType == entity.DictType && (t.DictKey == entity.DictKey || t.DictValue == entity.DictValue) && t.Id != entity.Id);
            }
            return this.BaseRepository().IQueryable(expression).Count() > 0 ? true : false;
        }
        #endregion

        #region 鎻愪氦鏁版嵁
        public async Task SaveForm(DataDictDetailEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert<DataDictDetailEntity>(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update<DataDictDetailEntity>(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            long[] idArr = TextHelper.SplitToArray<long>(ids, ',');
            await this.BaseRepository().Delete<DataDictDetailEntity>(idArr);
        }
        #endregion

        #region 绉佹湁鏂规硶
        private Expression<Func<DataDictDetailEntity, bool>> ListFilter(DataDictDetailListParam param)
        {
            var expression = LinqExtensions.True<DataDictDetailEntity>();
            if (param != null)
            {
                if (param.DictKey.ParseToInt() > 0)
                {
                    expression = expression.And(t => t.DictKey == param.DictKey);
                }

                if (!string.IsNullOrEmpty(param.DictValue))
                {
                    expression = expression.And(t => t.DictValue.Contains(param.DictValue));
                }

                if (!string.IsNullOrEmpty(param.DictType))
                {
                    expression = expression.And(t => t.DictType.Contains(param.DictType));
                }

                if (!string.IsNullOrEmpty(param.TypeCode))
                {
                    expression = expression.And(t => t.TypeCode == param.TypeCode);
                }
                if (param.UnitId > 0)
                {
                    expression = expression.And(t => t.UnitId == param.UnitId);
                }
                if (param.Statuz != -10000 && param.Statuz != -1)
                {
                    expression = expression.And(t => t.DictStatus == param.Statuz);
                }
            }
            return expression;
        }
        #endregion
    }
}

