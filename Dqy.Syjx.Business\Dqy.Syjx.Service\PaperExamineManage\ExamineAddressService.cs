﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.PaperExamineManage;
using Dqy.Syjx.Model.Param.PaperExamineManage;

namespace Dqy.Syjx.Service.PaperExamineManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2022-08-22 15:14
    /// 描 述：服务类
    /// </summary>
    public class ExamineAddressService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<ExamineAddressEntity>> GetList(ExamineAddressListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<ExamineAddressEntity>> GetPageList(ExamineAddressListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<ExamineAddressEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<ExamineAddressEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(ExamineAddressEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(ExamineAddressEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
               strSql = $" update cp_ExamineAddress set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
               strSql = $"update cp_ExamineAddress set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }

        /// <summary>
        /// 设置是否有效
        /// </summary>
        /// <returns></returns>
        public async Task SetIsCurrentValid(int isCurrentValid,long examineId,long paperId)
        {
            string strSql = $"update cp_ExamineAddress set IsCurrentValid = {isCurrentValid} where ExamineId={examineId} AND PaperId={paperId} AND CONVERT(varchar(10),BaseCreateTime,120) < CONVERT(varchar(10),GETDATE(),120)";
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<ExamineAddressEntity, bool>> ListFilter(ExamineAddressListParam param)
        {
            var expression = LinqExtensions.True<ExamineAddressEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (param.ExamineId > 0)
                {
                    expression = expression.And(t => t.ExamineId == param.ExamineId);
                }
                if (param.PaperId > 0)
                {
                    expression = expression.And(t => t.PaperId == param.PaperId);
                }
                if (!string.IsNullOrEmpty(param.BaseCreateTime.ParseToString()) && param.BaseCreateTime.ParseToString() != "0001/1/1 0:00:00")
                {
                    DateTime bgnTime = param.BaseCreateTime.Date;
                    DateTime endTime = param.BaseCreateTime.Date.Add(new TimeSpan(23, 59, 59));
                    expression = expression.And(t => t.BaseCreateTime >= bgnTime);
                    expression = expression.And(t => t.BaseCreateTime <= endTime);
                }
                if (!string.IsNullOrEmpty(param.ExamineCode))
                {
                    expression = expression.And(t => t.ExamineCode == param.ExamineCode);
                }

                if (param.IsCurrentValid > -1)
                {
                    expression = expression.And(t => t.IsCurrentValid == param.IsCurrentValid);
                }
            }

            return expression;
        }
        #endregion
    }
}
