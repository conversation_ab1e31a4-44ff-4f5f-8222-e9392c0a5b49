﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.ExperimentTeachManage;
using Dqy.Syjx.Model.Param.ExperimentTeachManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Service.OrganizationManage;
using NPOI.OpenXmlFormats.Wordprocessing;

namespace Dqy.Syjx.Service.ExperimentTeachManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2024-10-18 09:44
    /// 描 述：实验发布表服务类
    /// </summary>
    public class ExperimentPublishService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<ExperimentPublishEntity>> GetList(ExperimentPublishListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<ExperimentPublishEntity>> GetPageList(ExperimentPublishListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<ExperimentPublishEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<ExperimentPublishEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(ExperimentPublishEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }
        public async Task UpdateForm(ExperimentPublishEntity entity, List<string> fields)
        {
            if (fields != null)
            {
                await entity.Modify();
                fields.Add("BaseModifyTime");
                fields.Add("BaseModifierId");
            }
            await this.BaseRepository().Update(entity, fields);
        }
        public async Task SaveTransForm(ExperimentPublishEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteByIdForm(long  id)
        { 
             string   strSql = $"update ex_ExperimentPublish set BaseIsDelete = 1 where id = {id}"; 
            await this.BaseRepository().ExecuteBySql(strSql);

            string strChilderSql = $"update ex_ExperimentPublish set BaseIsDelete = 1 where ExperimentPublishId = {id}";
            await this.BaseRepository().ExecuteBySql(strSql);
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update ex_ExperimentPublish set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
                strSql = $"update ex_ExperimentPublish set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }

        /// <summary>
        /// 更新实验数量
        /// </summary>
        /// <param name="schoolId"></param>
        /// <param name="experimentPublishId"></param>
        /// <param name="activityType"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<int> UpdateNumForm(long schoolId, long experimentPublishId, int activityType,long id)
        {
            string strSql = @$" Update ex_ExperimentPublish 
            SET Num = (SELECT COUNT(pd.Id) FROM ex_PublishDetail AS pd WHERE pd.BaseIsDelete = 0 AND pd.SchoolId = {schoolId} AND pd.ExperimentPublishId = {experimentPublishId} AND pd.ActivityType = {activityType} )
            Where Id = {id} ";
            return await this.BaseRepository().ExecuteBySql(strSql);
        }
        /// <summary>
        /// 更新预约人数
        /// </summary>
        /// <param name="id"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public async Task<int> UpdateStudentNumedForm(long id, Repository db)
        {
            string strSql = @$" Update ex_ExperimentPublish 
            SET StudentNumed = (SELECT COUNT(1) FROM (SELECT  MIN(Id) AS Id  FROM ex_ExperimentCriticize WHERE ExperimentPublishId =  {id} AND BaseIsDelete = 0  GROUP BY  ParentStudentId,StudentId) AS tb1 )
            Where Id = {id} ";
            if (db==null)
            {
                return await this.BaseRepository().ExecuteBySql(strSql);
            }
            else
            {
                return await db.ExecuteBySql(strSql);
            }

        }

        #endregion

        #region 私有方法
        private Expression<Func<ExperimentPublishEntity, bool>> ListFilter(ExperimentPublishListParam param)
        {
            var expression = LinqExtensions.True<ExperimentPublishEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (param.SchoolId> 0)
                {
                    expression = expression.And(t => t.SchoolId == param.SchoolId);
                }
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (param.Statuz != -10000 && param.Statuz != -1)
                {
                    expression = expression.And(t => t.Statuz == param.Statuz);
                }
                if (param.Statuzge != -10000)
                {
                    expression = expression.And(t => t.Statuz >= param.Statuzge);
                }
            }
            return expression;
        }

        /// <summary>
        /// 编制计划实验数量以学校为单位
        /// </summary>
        /// <param name="param"></param>
        /// <param name="strSql"></param>
        /// <returns></returns>
        public async Task<List<ExperimentPublishEntity>> GetPageData(ExperimentPublishListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(@$"SELECT * From ( SELECT  
                                    ep.Id ,
                                    ep.BaseCreateTime ,
                                    ep.BaseCreatorId ,
                                    ep.BaseIsDelete ,
                                    ep.BaseModifierId ,
                                    ep.BaseModifyTime ,
                                    ep.BaseVersion ,
                                    ep.ClassTime ,
                                    ep.CourseId ,
                                    ep.FunRoomId ,
                                    ep.FunRoomName ,
                                    ep.Name ,
                                    ep.Num ,
                                    ep.SchoolId ,
                                    ep.SchoolTerm ,
                                    ep.SchoolYearEnd ,
                                    ep.SchoolYearStart ,
                                    ep.SectionId ,
                                    ep.SectionShow ,
                                    ep.Statuz ,
                                    ep.StudentNumed ,
                                    ep.StudentNumLimit ,
                                    sd4.DicName AS CourseName ,
                                    su5.RealName ,
                                    u2.Name AS SchoolName ,ur.UnitId AS CountyId ,u2.Sort ,
                                    sa.AreaName AS CountyName ,UR2.UnitId AS CityId
                                    FROM  ex_ExperimentPublish AS ep
                                    INNER JOIN  up_Unit AS u2 ON ep.SchoolId = u2.Id AND u2.BaseIsDelete = 0 AND u2.Statuz = 1		 									  
                                    INNER JOIN  sys_static_dictionary AS sd4 ON ep.CourseId = sd4.DictionaryId 
                                    INNER JOIN  SysUser AS su5 ON ep.BaseCreatorId = su5.Id
                                    INNER JOIN  up_UnitRelation AS ur ON ep.SchoolId = ur.ExtensionObjId AND ur.ExtensionType = 3 AND ur.BaseIsDelete = 0 
                                    INNER JOIN  up_UnitRelation AS UR2 ON UR.UnitId = UR2.ExtensionObjId AND UR2.ExtensionType = 3 AND UR2.BaseIsDelete = 0
                                    INNER JOIN  up_Unit AS u3 ON ur.UnitId = u3.Id AND u3.BaseIsDelete = 0 
                                    LEFT JOIN  SysArea AS sa ON sa.AreaCode = u3.AreaId AND sa.BaseIsDelete = 0
                          ) as T WHERE  1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = @BaseIsDelete ");
                parameter.Add(DbParameterExtension.CreateDbParameter("@BaseIsDelete", param.BaseIsDelete));
                if (param.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    strSql.Append(" AND SchoolId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                else if (param.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    strSql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.UnitId));
                }
                else if (param.UnitType == UnitTypeEnum.City.ParseToInt())
                {
                    strSql.Append(" AND CityId = @CityId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CityId", param.UnitId));
                    if (param.CountyId > 0)
                    {
                        strSql.Append(" AND CountyId = @CountyId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                    }
                }
                if (param.SchoolIdList != null && param.SchoolIdList.Count > 0)
                {
                    strSql.Append($" AND ({string.Join(" OR ", param.SchoolIdList.Select(m => string.Format(" SchoolId = {0} ", m)))}) ");
                }
                if (param.SchoolId > 0)
                {
                    strSql.Append(" AND SchoolId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.SchoolId));
                }
                if (param.CourseId > 0)
                {
                    strSql.Append(" AND CourseId = @CourseId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                 
                if (param.SchoolTerm > 0)
                {
                    strSql.Append(" AND SchoolTerm = @SchoolTerm ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolTerm", param.SchoolTerm));
                }
                if (param.SchoolYearStart > 0)
                {
                    strSql.Append(" AND SchoolYearStart = @SchoolYearStart ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolYearStart", param.SchoolYearStart)); 
                }
                if (!string.IsNullOrEmpty(param.Name))
                {
                    strSql.Append(" AND Name like @Name ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Name", $"%{param.Name}%"));
                }
                 
                if (param.CreatorId > 0) //编制人
                {
                    strSql.Append(" AND BaseCreatorId = @CreatorId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CreatorId", param.CreatorId));
                }
                 
                if (param.Statuz != -10000 && param.Statuz != -1)
                {
                    strSql.Append(" AND Statuz = @Statuz ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Statuz", param.Statuz));
                }
                if (param.Statuzge != -10000 && param.Statuzge != -1)
                {
                    strSql.Append(" AND Statuz >= @Statuzge ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Statuzge", param.Statuzge));
                }
                if (param.IsBooking == 1)
                {
                    strSql.Append($" AND ClassTime < '{DateTime.Now.AddDays(1).Date}' ");
                }
                if (param.SubjectList!=null && param.SubjectList.Count > 0)
                {
                    strSql.Append($" AND ({string.Join(" OR ", param.SubjectList.Select(n => string.Format(" CourseId = {0} ", n)))}) ");
                }
            }
            var list = await this.BaseRepository().FindList<ExperimentPublishEntity>(strSql.ToString(), parameter.ToArray(),pagination);
            return list.ToList();
        }
        #endregion
    }
}
