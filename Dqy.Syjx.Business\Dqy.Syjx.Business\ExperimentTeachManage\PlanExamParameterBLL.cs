﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Model.Input.ExperimentTeachManage;
using Dqy.Syjx.Entity.ExperimentTeachManage;
using Dqy.Syjx.Model.Param.ExperimentTeachManage;
using Dqy.Syjx.Service.ExperimentTeachManage;
using Dqy.Syjx.Service.SystemManage;
using Dqy.Syjx.Model.Param.SystemManage;
using Dqy.Syjx.Enum;
using Org.BouncyCastle.Crypto.Agreement;
using Dqy.Syjx.Web.Code;
using Dqy.Syjx.Enum.ExperimentTeachManage;
using Dqy.Syjx.Entity.SystemManage;
using Dqy.Syjx.Service.OrganizationManage;
using Dqy.Syjx.Model.Result.ExperimentTeachManage;
using Dqy.Syjx.Model.Param.OrganizationManage;
using Dqy.Syjx.Entity.OrganizationManage;
using Dqy.Syjx.Business.OrganizationManage;
using System.Linq.Dynamic.Core;
using Dqy.Syjx.Util.Tools;
using NetTopologySuite.Index.HPRtree;
using NPOI.SS.Formula.Functions;

namespace Dqy.Syjx.Business.ExperimentTeachManage
{
    /// <summary>
    /// 创 建：陶瑞
    /// 日 期：2024-03-07 17:14
    /// 描 述：设置考核实验业务类
    /// </summary>
    public class PlanExamParameterBLL
    {
        /// <summary>
        /// ApiToken值（仅WebApi端传入）
        /// </summary>
        public string ApiToken = string.Empty;

        private PlanExamParameterService planExamParameterService = new PlanExamParameterService();
        private StaticDictionaryService staticDictionaryService = new StaticDictionaryService();
        private UnitService unitService = new UnitService();
        private SchoolGradeClassService schoolGradeClassService = new SchoolGradeClassService();
        private UserSubjectSetService userSubjectSetService = new UserSubjectSetService();
        #region 获取数据
        public async Task<TData<List<PlanExamParameterEntity>>> GetList(PlanExamListParameterListParam param)
        {
            TData<List<PlanExamParameterEntity>> obj = new TData<List<PlanExamParameterEntity>>();
            obj.Data = await planExamParameterService.GetList(param);
            obj.Total = obj.Data.Count;
            obj.Tag = 1;
            return obj;
        }

        public async Task<TData<List<PlanExamParameterEntity>>> GetPageList(PlanExamListParameterListParam param, Pagination pagination)
        {
            TData<List<PlanExamParameterEntity>> obj = new TData<List<PlanExamParameterEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            param.UnitId = operatorinfo.UnitId ?? 0;
            //初始化数据
            var paramDic = new StaticDictionaryListParam();
            paramDic.TypeCode = DicTypeCodeEnum.Grade.ParseToInt().ToString();
            paramDic.Statuz = 1;
            var listGrade = await staticDictionaryService.GetList(paramDic);

            //初始化数据
            var paramDicCourse = new StaticDictionaryListParam();
            paramDicCourse.TypeCode = DicTypeCodeEnum.Course.ParseToInt().ToString();
            paramDicCourse.PTypeCode = DicTypeCodeEnum.Grade.ParseToInt().ToString();
            paramDicCourse.Statuz = 1;
            paramDicCourse.Nature = 1;
            var listCourseInGrade = await staticDictionaryService.GetAllList(paramDicCourse);
            if (param.IsInit == 1 && param.SchoolYearStart > 0 && param.SchoolTerm > 0 && listGrade != null)
            {
                //获取当前学期。
                var paramInit = new PlanExamListParameterListParam();
                paramInit.SchoolYearStart = param.SchoolYearStart;
                paramInit.SchoolTerm = param.SchoolTerm;
                paramInit.UnitId = operatorinfo.UnitId ?? 0;

                var listTemp = await planExamParameterService.GetList(param);
                if (listTemp != null && listTemp.Count == 0)
                {
                    //年级和学段的关系
                    var paramDicSchoolStage = new StaticDictionaryListParam();
                    paramDicCourse.TypeCode = DicTypeCodeEnum.Grade.ParseToInt().ToString();
                    paramDicCourse.PTypeCode = DicTypeCodeEnum.SchoolStage.ParseToInt().ToString();
                    paramDicCourse.Statuz = 1;
                    var listGradeSchoolStage = await staticDictionaryService.GetChildList(paramDicCourse);


                    PlanExamParameterEntity entity = null;
                    foreach (var item in listGrade)
                    {
                        PlanExamParameterInputModel model = new PlanExamParameterInputModel();
                        model.UnitId = operatorinfo.UnitId ?? 0;
                        model.SchoolYearStart = param.SchoolYearStart ?? 0;
                        model.SchoolTerm = param.SchoolTerm ?? 0;
                        model.GradeId = item.DictionaryId ?? 0;
                        if (listGradeSchoolStage != null)
                        {
                            var listtemp = listGradeSchoolStage.Where(m => m.DictionaryId == item.DictionaryId);
                            if (listtemp != null && listtemp.Count() > 0)
                            {
                                model.SchoolStage = listtemp.FirstOrDefault().Pid ?? 0;
                            }
                        }
                        IEnumerable<int> courseArr = null;
                        if (listCourseInGrade != null)
                        {
                            courseArr = listCourseInGrade.Where(m => m.Pid == item.DictionaryId).Select(m => m.DictionaryId ?? 0);
                        }
                        if (courseArr != null && courseArr.Count() > 0)
                        {
                            foreach (var courseid in courseArr)
                            {
                                model.CourseId = courseid;
                                if (model.GradeId < GradeEnum.GaoYi.ParseToInt())
                                {
                                    model.CompulsoryType = 0;
                                    entity = GetEntityByModel(model);
                                    await planExamParameterService.SaveForm(entity);
                                }
                                else
                                {
                                    model.CompulsoryType = ClassCompulsoryTypeEnum.Select.ParseToInt();
                                    entity = GetEntityByModel(model);
                                    await planExamParameterService.SaveForm(entity);

                                    model.CompulsoryType = ClassCompulsoryTypeEnum.NonSelect.ParseToInt();
                                    entity = GetEntityByModel(model);
                                    await planExamParameterService.SaveForm(entity);
                                }
                            }
                        }
                    }
                }
            }
            obj.Data = await planExamParameterService.GetPageList(param, pagination);
            if (obj.Data != null && obj.Data.Count > 0)
            {
                obj.Data.ForEach(m =>
                {
                    m.GradeName = GetDictionaryName(listGrade, m.GradeId);
                    m.CourseName = GetDictionaryName(listCourseInGrade, m.CourseId);
                    m.BaseModifyTime = ((DateTime)m.BaseModifyTime).Date;
                });
            }
            obj.Total = pagination.TotalCount;
            obj.Tag = 1;
            return obj;
        }

        public async Task<TData<PlanExamParameterEntity>> GetEntity(long id)
        {
            TData<PlanExamParameterEntity> obj = new TData<PlanExamParameterEntity>();
            obj.Data = await planExamParameterService.GetEntity(id);
            if (obj.Data != null)
            {
                obj.Tag = 1;
            }
            return obj;
        }
        #endregion

        #region 提交数据
        public async Task<TData<string>> SaveForm(PlanExamParameterInputModel model)
        {
            var entity = new PlanExamParameterEntity();
            if (model.Id > 0)
            {
                entity = await planExamParameterService.GetEntity(model.Id);
            }
            entity.Id = model.Id;
            entity.UnitId = model.UnitId;
            entity.SchoolStage = model.SchoolStage;
            entity.GradeId = model.GradeId;
            entity.CourseId = model.CourseId;
            entity.SchoolYearStart = model.SchoolYearStart;
            entity.SchoolYearEnd = model.SchoolYearEnd;
            entity.SchoolTerm = model.SchoolTerm;
            entity.Num = model.Num;
            entity.NeedShowNum = model.NeedShowNum;
            entity.NeedGroupNum = model.NeedGroupNum;
            entity.OptionalShowNum = model.OptionalShowNum;
            entity.OptionalGroupNum = model.OptionalGroupNum;
            entity.CompulsoryType = model.CompulsoryType;
            TData<string> obj = new TData<string>();
            await planExamParameterService.SaveForm(entity);
            obj.Data = entity.Id.ParseToString();
            obj.Tag = 1;
            return obj;
        }

        public async Task<TData> DeleteForm(string ids)
        {
            TData obj = new TData();
            if (ids.IsEmpty()) { return obj; }
            PlanExamListParameterListParam param = new PlanExamListParameterListParam { Ids = ids };
            var list = await planExamParameterService.GetList(param);
            ids = "";
            foreach (var m in list)
            {
                //此处需增加校验是否满足删除条件
                ids += ", " + m.Id.Value;
            }
            obj.Tag = 1;
            if (ids.Length > 1)
                await planExamParameterService.DeleteForm(ids);
            else
                obj.Tag = 0;
            return obj;
        }
        #endregion

        #region 私有方法

        private PlanExamParameterEntity GetEntityByModel(PlanExamParameterInputModel model)
        {
            PlanExamParameterEntity entity = new PlanExamParameterEntity();
            entity.UnitId = model.UnitId;
            entity.SchoolStage = model.SchoolStage;
            entity.GradeId = model.GradeId;
            entity.CourseId = model.CourseId;
            entity.SchoolYearStart = model.SchoolYearStart;
            entity.SchoolYearEnd = model.SchoolYearStart + 1;
            entity.SchoolTerm = model.SchoolTerm;
            entity.CompulsoryType = model.CompulsoryType;
            return entity;
        }

        private string GetDictionaryName(List<StaticDictionaryEntity> list, int objid)
        {
            string str = "";
            if (list != null && list.Count > 0)
            {
                var objList = list.Where(m => m.DictionaryId == objid);
                if (objList != null && objList.Count() > 0)
                {
                    str = objList.FirstOrDefault().DicName;
                }
            }
            return str;
        }

        public async Task<TData<List<TextbookVersionDetailEntity>>> GetAddExperimentPageList(PlanExamExperimentListParam param, Pagination pagination)
        {
            TData<List<TextbookVersionDetailEntity>> obj = new TData<List<TextbookVersionDetailEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            param.UnitId = operatorinfo.UnitId ?? 0;
            if (param.PlanParameterSetId > 0)
            {
                var entity = await planExamParameterService.GetEntity(param.PlanParameterSetId);
                if (entity != null)
                {
                    param.SchoolStage = entity.SchoolStage;
                    param.CourseId = entity.CourseId;
                    // param.UnitId = entity.UnitId; 
                    if (entity.GradeId >= GradeEnum.GaoYi.ParseToInt())
                    {
                        //param.CompulsoryType = entity.CompulsoryType;
                    }
                }
            }
            obj.Data = await planExamParameterService.GetAddExperimentPageList(param, pagination);
            obj.Total = pagination.TotalCount;
            obj.Tag = 1;
            return obj;
        }
        #endregion

        #region 编制率、考核编制实验
        public async Task<TData<List<PlanExamParameterEntity>>> GetPlanExamRatePageList(PlanExamListParameterListParam param, Pagination pagination)
        {
            TData<List<PlanExamParameterEntity>> obj = new TData<List<PlanExamParameterEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            var paramPlanDetail = new PlanDetailListParam();
            if (param.SchoolYearStart<0||param.SchoolTerm<0)
            {
                obj.Tag = 0;
                obj.Message = "请选择需要查询的学年、学期。";
                return obj;
            }
            if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                var countyid = await unitService.GetParentUnitId(operatorinfo.UnitId ?? 0);
                paramPlanDetail.SchoolId = operatorinfo.UnitId??0; 
                paramPlanDetail.UnitId = countyid;
                param.SchoolId = operatorinfo.UnitId ?? 0;
                param.SchoolStageList = operatorinfo.SchoolStageList;
                param.UnitId = countyid;
            }
            else if(operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                paramPlanDetail.CountyId = operatorinfo.UnitId ?? 0;
                param.UnitId = operatorinfo.UnitId ?? 0;
                
                var listUserStage = await userSubjectSetService.GetUserSetStageList(operatorinfo.UserId ?? 0);
                if (listUserStage != null)
                {
                    listUserStage = listUserStage.Where(m => m.UserSubjectSetId > 0).ToList();
                    if (listUserStage != null && listUserStage.Count > 0)
                    {
                        param.SchoolStageList = listUserStage.Select(m => m.DictionaryId ?? 0).ToList();
                    }
                }
                //获取当前区县用户，设置的权限学段、学科
                var listUserCourse = await userSubjectSetService.GetUserSetCourseList(operatorinfo.UserId ?? 0);
                if (listUserCourse != null)
                {
                    listUserCourse = listUserCourse.Where(m => m.UserSubjectSetId > 0).ToList();
                    if (listUserCourse != null && listUserCourse.Count > 0)
                    {
                        param.CourseIdList = listUserCourse.Select(m => m.DictionaryId ?? 0).ToList();
                    }
                }
            }
            else
            {
                obj.Tag = 1;
                obj.Data = new List<PlanExamParameterEntity>();
                obj.Total = 0;
                obj.Message = "未查询到相关数据。";
                return obj;
            }

            var list = await planExamParameterService.GetPlanExamRatePageList(param, pagination);
            if (list != null && list.Count() > 0)
            {
                obj.Data = list.ToList();
                var listPlanDetail = await planExamParameterService.GetPlanExamDetailList(param, null);
                if (listPlanDetail != null && listPlanDetail.Count() > 0)
                {
                    obj.Data.ForEach(m => m.PlanNumed = GetNumByList(m.Id ?? 0, m.SchoolId ?? 0, listPlanDetail));
                }
            }
            obj.Total = pagination.TotalCount;
            obj.Tag = 1;
            return obj;
        }

        private int GetNumByList(long id,long schoolid, IEnumerable<PlanExamParameterEntity> list)
        {
            int num = 0;
            if (list != null && list.Count() > 0)
            {
                var listTemp = list.Where(m => m.Id == id && m.SchoolId == schoolid);
                if (listTemp != null && listTemp.Count() > 0)
                {
                    num = listTemp.GroupBy(m => m.ExperimentId).Count();
                }
            }
            return num;
        }

        public async Task<TData<List<PlanDetailEntity>>> GetPlanExamedPageList(PlanExamExperimentListParam param, Pagination pagination)
        {
            TData<List<PlanDetailEntity>> obj = new TData<List<PlanDetailEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
         
            if (param.PlanParameterSetId <= 0)
            {
                obj.Tag = 0;
                obj.Message = "请从考核实验列表点击操作。";
                return obj;
            }
            if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                var countyid = await unitService.GetParentUnitId(operatorinfo.UnitId ?? 0);
            
                param.SchoolId = operatorinfo.UnitId ?? 0; 
                param.UnitId = countyid;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                param.UnitId = operatorinfo.UnitId ?? 0;
            }
            else
            {
                obj.Tag = 1;
                obj.Data = new List<PlanDetailEntity>();
                obj.Total = 0;
                obj.Message = "未查询到相关数据。";
                return obj;
            }

            var list = await planExamParameterService.GetPlanExamedList(param, pagination);
            if (list != null)
            {
                obj.Data = list.ToList();
            }
            else
            {
                obj.Data = new List<PlanDetailEntity>();
            }
            obj.Total = pagination.TotalCount;
            obj.Tag = 1;
            return obj;
        }

        public async Task<TData<List<TextbookVersionBaseEntity>>> GetPlanExamedVersion(long id)
        {
            TData<List<TextbookVersionBaseEntity>> obj = new TData<List<TextbookVersionBaseEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            var param = new PlanExamExperimentListParam();
            if (id <= 0)
            {
                obj.Tag = 0;
                obj.Message = "请从考核实验列表点击操作。";
                return obj;
            }
            param.PlanParameterSetId = id;
            if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                var countyid = await unitService.GetParentUnitId(operatorinfo.UnitId ?? 0);

                param.SchoolId = operatorinfo.UnitId ?? 0;
                param.UnitId = countyid;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                param.UnitId = operatorinfo.UnitId ?? 0;
            }
            else
            {
                obj.Tag = 1;
                obj.Data = new List<TextbookVersionBaseEntity>();
                obj.Total = 0;
                obj.Message = "未查询到相关数据。";
                return obj;
            }
            var pagination = new Pagination();
            pagination.PageSize = int.MaxValue;
            var list = await planExamParameterService.GetPlanExamedList(param, pagination);
            if (list != null)
            {
                obj.Data = list.GroupBy(m=>new { m.TextbookVersionBaseId ,m.VersionBaseName }).Select(m=>new TextbookVersionBaseEntity() { Id=m.FirstOrDefault().TextbookVersionBaseId ,VersionName=m.FirstOrDefault().VersionBaseName}).ToList();
            }
            else
            {
                obj.Data = new List<TextbookVersionBaseEntity>();
            }
            obj.Total = pagination.TotalCount;
            obj.Tag = 1;
            return obj;
        }
        #endregion

        #region 实验达标率、 考核实验达标率
        /// <summary>
        /// 考核实验达标列表（区县）
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<TData<List<PlanExamParameterEntity>>> GetPlanExamExpRateCountyPageList(PlanExamListParameterListParam param, Pagination pagination)
        {
            TData<List<PlanExamParameterEntity>> obj = new TData<List<PlanExamParameterEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            var paramGradeClass = new SchoolGradeClassListParam();
            if (param.SchoolYearStart < 0 || param.SchoolTerm < 0)
            {
                obj.Tag = 0;
                obj.Message = "请选择需要查询的学年、学期。";
                return obj;
            }
            paramGradeClass.UnitType = operatorinfo.UnitType;
            if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                var countyid = await unitService.GetParentUnitId(operatorinfo.UnitId ?? 0);
                param.SchoolId = operatorinfo.UnitId ?? 0;
                param.SchoolStageList = operatorinfo.SchoolStageList;
                param.UnitId = countyid;

                paramGradeClass.UnitId = operatorinfo.UnitId ?? 0;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                param.UnitId = operatorinfo.UnitId ?? 0;

                paramGradeClass.CountyId = operatorinfo.UnitId ?? 0;

                var listUserStage = await userSubjectSetService.GetUserSetStageList(operatorinfo.UserId ?? 0);
                if (listUserStage != null)
                {
                    listUserStage = listUserStage.Where(m => m.UserSubjectSetId > 0).ToList();
                    if (listUserStage != null && listUserStage.Count > 0)
                    {
                        param.SchoolStageList = listUserStage.Select(m => m.DictionaryId ?? 0).ToList();
                    }
                }
                //获取当前区县用户，设置的权限学段、学科
                var listUserCourse= await userSubjectSetService.GetUserSetCourseList(operatorinfo.UserId??0);
                if (listUserCourse!=null)
                {
                    listUserCourse = listUserCourse.Where(m => m.UserSubjectSetId > 0).ToList();
                    if (listUserCourse != null && listUserCourse.Count > 0)
                    {
                        param.CourseIdList = listUserCourse.Select(m => m.DictionaryId ?? 0).ToList();
                    }
                }
            }
            else
            {
                obj.Tag = 1;
                obj.Data = new List<PlanExamParameterEntity>();
                obj.Total = 0;
                obj.Message = "未查询到相关数据。";
                return obj;
            }
            //初始化数据
            var paramDicStage = new StaticDictionaryListParam();
            paramDicStage.TypeCode = DicTypeCodeEnum.SchoolStage.ParseToInt().ToString();
            paramDicStage.Statuz = 1;
            var listSchoolStage = await staticDictionaryService.GetList(paramDicStage);

            //初始化数据
            var paramDic = new StaticDictionaryListParam();
            paramDic.TypeCode = DicTypeCodeEnum.Grade.ParseToInt().ToString();
            paramDic.Statuz = 1;
            var listGrade = await staticDictionaryService.GetList(paramDic);

            //初始化数据
            var paramDicCourse = new StaticDictionaryListParam();
            paramDicCourse.TypeCode = DicTypeCodeEnum.Course.ParseToInt().ToString();
            paramDicCourse.Statuz = 1;
            paramDicCourse.Nature = 1;
            var listCourse = await staticDictionaryService.GetList(paramDicCourse);

            var list = await planExamParameterService.GetPageList(param, pagination);
            if (list != null && list.Count() > 0)
            {
                obj.Data = list.ToList();
                var listRecordDetail = await planExamParameterService.GetPlanExamExpRecordList(param, null);

                //每个考核数，要乘以年级中的班级数。
                IEnumerable<SchoolGradeClassEntity> listGradeClass = null;
                if (param.SchoolYearStart == operatorinfo.SchoolTermStartYear)
                {
                    listGradeClass = await schoolGradeClassService.GetStatistics(paramGradeClass);
                }
                else
                {
                    paramGradeClass.SchoolYear = param.SchoolYearStart ?? operatorinfo.SchoolTermStartYear;
                    listGradeClass = await schoolGradeClassService.GetHistoryStatistics(paramGradeClass);
                }
                obj.Data.ForEach(m =>
                {
                    m.SchoolStageName = GetDictionaryName(listSchoolStage, m.SchoolStage);
                    m.GradeName = GetDictionaryName(listGrade, m.GradeId);
                    m.CourseName = GetDictionaryName(listCourse, m.CourseId);
                    m.NeedShowNum = m.NeedShowNum * GetExameClassNum(listGradeClass, m.GradeId, 0, m.SchoolStage, m.CourseId, m.CompulsoryType ?? 0);
                    m.NeedGroupNum = m.NeedGroupNum * GetExameClassNum(listGradeClass, m.GradeId, 0, m.SchoolStage, m.CourseId, m.CompulsoryType ?? 0);
                    m.OptionalShowNum = m.OptionalShowNum * GetExameClassNum(listGradeClass, m.GradeId, 0, m.SchoolStage, m.CourseId, m.CompulsoryType ?? 0);
                    m.OptionalGroupNum = m.OptionalGroupNum * GetExameClassNum(listGradeClass, m.GradeId, 0, m.SchoolStage, m.CourseId, m.CompulsoryType ?? 0);
                    m.NeedShowNumed = NeedShowNumed(listRecordDetail, m.Id ?? 0, 0, m.GradeId, 0, listGradeClass, m.SchoolStage, m.CourseId, m.CompulsoryType ?? 0);
                    m.NeedGroupNumed = NeedGroupNumed(listRecordDetail, m.Id ?? 0, 0, m.GradeId, 0, listGradeClass, m.SchoolStage, m.CourseId, m.CompulsoryType ?? 0);
                    m.OptionalShowNumed = OptionalShowNumed(listRecordDetail, m.Id ?? 0, 0, m.GradeId, 0, listGradeClass, m.SchoolStage, m.CourseId, m.CompulsoryType ?? 0);
                    m.OptionalGroupNumed = OptionalGroupNumed(listRecordDetail, m.Id ?? 0, 0, m.GradeId, 0, listGradeClass, m.SchoolStage, m.CourseId, m.CompulsoryType ?? 0);
                });
            }
            obj.Total = pagination.TotalCount;
            obj.Tag = 1;
            return obj;
        }

        /// <summary>
        /// 考核实验达标列表(学校年级列表)
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<TData<List<PlanExamParameterEntity>>> GetPlanExamExpRatePageList(PlanExamListParameterListParam param, Pagination pagination)
        {
            TData<List<PlanExamParameterEntity>> obj = new TData<List<PlanExamParameterEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            var paramPlanDetail = new PlanDetailListParam();
            var paramGradeClass = new SchoolGradeClassListParam();
            if (param.SchoolYearStart < 0 || param.SchoolTerm < 0)
            {
                obj.Tag = 0;
                obj.Message = "请选择需要查询的学年、学期。";
                return obj;
            }
            paramGradeClass.UnitType = operatorinfo.UnitType;
            if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                var countyid = await unitService.GetParentUnitId(operatorinfo.UnitId ?? 0);
                paramPlanDetail.SchoolId = operatorinfo.UnitId ?? 0;
                paramPlanDetail.UnitId = countyid;
                param.SchoolId = operatorinfo.UnitId ?? 0;
                param.SchoolStageList = operatorinfo.SchoolStageList;
                param.UnitId = countyid;

                paramGradeClass.UnitId = operatorinfo.UnitId ?? 0;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                paramPlanDetail.CountyId = operatorinfo.UnitId ?? 0;
                param.UnitId = operatorinfo.UnitId ?? 0;

                paramGradeClass.CountyId = operatorinfo.UnitId ?? 0;
            }
            else
            {
                obj.Tag = 1;
                obj.Data = new List<PlanExamParameterEntity>();
                obj.Total = 0;
                obj.Message = "未查询到相关数据。";
                return obj;
            }
            if (param.Id > 0)
            {
                var entity = await planExamParameterService.GetEntity(param.Id ?? 0);
                if (entity != null)
                {
                    param.SchoolPropList = UnitBLL.GetSchoolPropList(entity.SchoolStage);
                    param.SchoolYearStart = entity.SchoolYearStart;
                }
            }
            var list = await planExamParameterService.GetPlanExamRatePageList(param, pagination);
            if (list != null && list.Count() > 0)
            {
                obj.Data = list.ToList();
                var listRecordDetail = await planExamParameterService.GetPlanExamExpRecordList(param, null);

                //每个考核数，要乘以年级中的班级数。
                IEnumerable<SchoolGradeClassEntity> listGradeClass =null;
                //获取当前学期学年
                if (param.SchoolYearStart == operatorinfo.SchoolTermStartYear)
                {
         
                    listGradeClass = await schoolGradeClassService.GetStatistics(paramGradeClass);
                }
                else
                {
                    //查询历史数据
                    paramGradeClass.SchoolYear = param.SchoolYearStart ?? operatorinfo.SchoolTermStartYear;
                    listGradeClass = await schoolGradeClassService.GetHistoryStatistics(paramGradeClass);
                }
                obj.Data.ForEach(m =>
                {
                    m.NeedShowNum = m.NeedShowNum * GetExameClassNum(listGradeClass, m.GradeId, m.SchoolId ?? 0, m.SchoolStage,m.CourseId,m.CompulsoryType??0);
                    m.NeedGroupNum = m.NeedGroupNum * GetExameClassNum(listGradeClass, m.GradeId, m.SchoolId ?? 0, m.SchoolStage, m.CourseId, m.CompulsoryType ?? 0);
                    m.OptionalShowNum = m.OptionalShowNum * GetExameClassNum(listGradeClass, m.GradeId, m.SchoolId ?? 0, m.SchoolStage, m.CourseId, m.CompulsoryType ?? 0);
                    m.OptionalGroupNum = m.OptionalGroupNum * GetExameClassNum(listGradeClass, m.GradeId, m.SchoolId ?? 0, m.SchoolStage, m.CourseId, m.CompulsoryType ?? 0);
                    m.NeedShowNumed = NeedShowNumed(listRecordDetail, m.Id ?? 0, m.SchoolId ?? 0, m.GradeId, 0, listGradeClass, m.SchoolStage, m.CourseId, m.CompulsoryType ?? 0);
                    m.NeedGroupNumed = NeedGroupNumed(listRecordDetail, m.Id ?? 0, m.SchoolId ?? 0, m.GradeId, 0, listGradeClass, m.SchoolStage, m.CourseId, m.CompulsoryType ?? 0);
                    m.OptionalShowNumed = OptionalShowNumed(listRecordDetail, m.Id ?? 0, m.SchoolId ?? 0, m.GradeId, 0, listGradeClass, m.SchoolStage, m.CourseId, m.CompulsoryType ?? 0);
                    m.OptionalGroupNumed = OptionalGroupNumed(listRecordDetail, m.Id ?? 0, m.SchoolId ?? 0, m.GradeId, 0, listGradeClass, m.SchoolStage, m.CourseId, m.CompulsoryType ?? 0);
                });
            }
            else
            {
                obj.Data = new List<PlanExamParameterEntity>();
            }
            obj.Total = pagination.TotalCount;
            obj.Tag = 1;
            return obj;
        }
        /// <summary>
        /// 考核实验达标列表(学校班级明细)
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<TData<List<PlanExamParameterEntity>>> GetPlanExamExpClassRatePageList(PlanExamListParameterListParam param, Pagination pagination)
        {
            TData<List<PlanExamParameterEntity>> obj = new TData<List<PlanExamParameterEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            var paramPlanDetail = new PlanDetailListParam();
            var paramGradeClass = new SchoolGradeClassListParam();
            if (param.SchoolYearStart < 0 || param.SchoolTerm < 0)
            {
                obj.Tag = 0;
                obj.Message = "请选择需要查询的学年、学期。";
                return obj;
            }
            paramGradeClass.UnitType = operatorinfo.UnitType;
            if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                var countyid = await unitService.GetParentUnitId(operatorinfo.UnitId ?? 0);
                paramPlanDetail.SchoolId = operatorinfo.UnitId ?? 0;
                paramPlanDetail.UnitId = countyid;
                param.SchoolId = operatorinfo.UnitId ?? 0;
                param.SchoolStageList = operatorinfo.SchoolStageList;
                param.UnitId = countyid;

                paramGradeClass.UnitId = operatorinfo.UnitId ?? 0;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                paramPlanDetail.CountyId = operatorinfo.UnitId ?? 0;
                param.UnitId = operatorinfo.UnitId ?? 0;

                paramGradeClass.CountyId = operatorinfo.UnitId ?? 0;
                paramGradeClass.UnitId = param.SchoolId;
            }
            else
            {
                obj.Tag = 1;
                obj.Data = new List<PlanExamParameterEntity>();
                obj.Total = 0;
                obj.Message = "未查询到相关数据。";
                return obj;
            }
            var entityParameter = await planExamParameterService.GetEntity(param.Id ?? 0);
            if (entityParameter == null)
            {
                obj.Tag = 1;
                obj.Data = new List<PlanExamParameterEntity>();
                obj.Total = 0;
                obj.Message = "当前考核实验信息不存在，请刷新重新操作。";
                return obj;
            }
            if (entityParameter.GradeId>=GradeEnum.GaoYi.ParseToInt())
            {
                param.GradeId = entityParameter.GradeId;
                param.CourseId = entityParameter.CourseId;
                param.CompulsoryType = entityParameter.CompulsoryType;
            }
            if (entityParameter.SchoolYearStart !=operatorinfo.SchoolTermStartYear)
            {
                param.IsCurrentSchoolYear = 2;
            }
            var list = await planExamParameterService.GetPlanExamClassRatePageList(param, pagination);
            if (list != null && list.Count() > 0)
            {
                obj.Data = list.ToList();
                var listRecordDetail = await planExamParameterService.GetPlanExamExpRecordList(param, null);
                if (listRecordDetail != null && listRecordDetail.Count() > 0)
                {
                    //每个考核数，要乘以年级中的班级数。
                    obj.Data.ForEach(m =>
                    {
                        m.NeedShowNumed = NeedShowNumed(listRecordDetail, m.Id ?? 0, m.SchoolId ?? 0, 0, m.SchoolGradeClassId);
                        m.NeedGroupNumed = NeedGroupNumed(listRecordDetail, m.Id ?? 0, m.SchoolId ?? 0, 0, m.SchoolGradeClassId);
                        m.OptionalShowNumed = OptionalShowNumed(listRecordDetail, m.Id ?? 0, m.SchoolId ?? 0, 0, m.SchoolGradeClassId);
                        m.OptionalGroupNumed = OptionalGroupNumed(listRecordDetail, m.Id ?? 0, m.SchoolId ?? 0, 0, m.SchoolGradeClassId);
                    });
                }
            }
            else
            {
                obj.Data = new List<PlanExamParameterEntity>();
            }
            obj.Total = pagination.TotalCount;
            obj.Tag = 1;
            return obj;
        }

        /// <summary>
        /// 计划考核实验任课老师达标率(学校班级明细)
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<TData<List<PlanExamParameterEntity>>> GetPlanExamExpClassTeacherRatePageList(PlanExamListParameterListParam param, Pagination pagination)
        {
            TData<List<PlanExamParameterEntity>> obj = new TData<List<PlanExamParameterEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            var paramPlanDetail = new PlanDetailListParam();
            var paramGradeClass = new SchoolGradeClassListParam();
            if (param.SchoolYearStart < 0 || param.SchoolTerm < 0)
            {
                obj.Tag = 0;
                obj.Message = "请选择需要查询的学年、学期。";
                return obj;
            }
            paramGradeClass.UnitType = operatorinfo.UnitType;
            if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                var countyid = await unitService.GetParentUnitId(operatorinfo.UnitId ?? 0);
                paramPlanDetail.SchoolId = operatorinfo.UnitId ?? 0;
                paramPlanDetail.UnitId = countyid;
                param.SchoolId = operatorinfo.UnitId ?? 0;
                param.SchoolStageList = operatorinfo.SchoolStageList;
                param.UnitId = countyid;

                paramGradeClass.UnitId = operatorinfo.UnitId ?? 0;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                paramPlanDetail.CountyId = operatorinfo.UnitId ?? 0;
                param.UnitId = operatorinfo.UnitId ?? 0;

                paramGradeClass.CountyId = operatorinfo.UnitId ?? 0;
                paramGradeClass.UnitId = param.SchoolId;
            }
            else
            {
                obj.Tag = 1;
                obj.Data = new List<PlanExamParameterEntity>();
                obj.Total = 0;
                obj.Message = "未查询到相关数据。";
                return obj;
            }
            if (param.SchoolYearStart != operatorinfo.SchoolTermStartYear)
            {
                param.IsCurrentSchoolYear = 2;
            }
            var list = await planExamParameterService.GetPlanExamClassTeacherRatePageList(param, pagination);
            if (list != null && list.Count() > 0)
            {
                obj.Data = list.ToList();
                var listRecordDetail = await planExamParameterService.GetPlanExamExpRecordList(param, null);
                if (listRecordDetail != null && listRecordDetail.Count() > 0)
                {
                    //每个考核数，要乘以年级中的班级数。
                    obj.Data.ForEach(m =>
                    {
                        m.NeedShowNumed = NeedShowNumed(listRecordDetail, m.Id ?? 0, m.SchoolId ?? 0, 0, m.SchoolGradeClassId, null, 0, 0, 0, m.TeacherId);
                        m.NeedGroupNumed = NeedGroupNumed(listRecordDetail, m.Id ?? 0, m.SchoolId ?? 0, 0, m.SchoolGradeClassId, null, 0, 0, 0, m.TeacherId);
                        m.OptionalShowNumed = OptionalShowNumed(listRecordDetail, m.Id ?? 0, m.SchoolId ?? 0, 0, m.SchoolGradeClassId, null, 0, 0, 0, m.TeacherId);
                        m.OptionalGroupNumed = OptionalGroupNumed(listRecordDetail, m.Id ?? 0, m.SchoolId ?? 0, 0, m.SchoolGradeClassId, null, 0, 0, 0, m.TeacherId);
                    });
                }
            }
            else
            {
                obj.Data = new List<PlanExamParameterEntity>();
            }
            obj.Total = pagination.TotalCount;
            obj.Tag = 1;
            return obj;
        }

        #endregion

        #region 看板，计划编制率、实验开出率、实验开出率排行

        /// <summary>
        /// 实验计划编制率（数据看板）
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<TData<object>> GetRatioList(PlanExamListParameterListParam param)
        {
            TData<object> obj = new TData<object>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            if (operatorinfo == null)
            {
                obj.Tag = -1;
                obj.Message = "登录信息失效！";
                return obj;
            }
            if (param.SchoolYearStart < 0 || param.SchoolTerm < 0)
            {
                obj.Tag = 0;
                obj.Message = "请选择需要查询的学年、学期。";
                return obj;
            }
            if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                param.SchoolId = operatorinfo.UnitId ?? 0;
                param.SchoolStageList = operatorinfo.SchoolStageList;
                param.UnitId = await unitService.GetParentUnitId(operatorinfo.UnitId ?? 0);
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                param.UnitId = operatorinfo.UnitId ?? 0;
            }
            else
            {
                obj.Tag = 1;
                obj.Data = new List<PlanExamParameterEntity>();
                obj.Total = 0;
                obj.Message = "未查询到相关数据。";
                return obj;
            }
            var list = await planExamParameterService.GetPlanExamRatePageList(param, null);
            IEnumerable<PlanExamParameterEntity> listPlanDetail = null;
            if (list != null && list.Count() > 0)
            {
                obj.Data = list.ToList();
                var pagePlanDetail = new Pagination();
                pagePlanDetail.PageSize = int.MaxValue;
                listPlanDetail = await planExamParameterService.GetPlanExamDetailList(param, pagePlanDetail);
            }

            var paramSchoolStage = new StaticDictionaryListParam();
            paramSchoolStage.TypeCode = DicTypeCodeEnum.SchoolStage.ParseToInt().ToString();
            var listSchoolSatge = await staticDictionaryService.GetList(paramSchoolStage);

            var paramDicCourse = new StaticDictionaryListParam();
            paramDicCourse.TypeCode = DicTypeCodeEnum.Course.ParseToInt().ToString();
            paramDicCourse.PTypeCode = DicTypeCodeEnum.SchoolStage.ParseToInt().ToString();
            paramDicCourse.Nature = 1;
            paramDicCourse.Statuz = 1;
            var listCourse = await staticDictionaryService.GetAllList(paramDicCourse);
            if (listCourse != null && listCourse.Count > 0 && listSchoolSatge != null && listSchoolSatge.Count > 0)
            {
                List<object> objList = new List<object>();
                List<object> objRatioList = new List<object>();
                foreach (var schoolstage in listSchoolSatge)
                {
                    var listtemp = listCourse.Where(m => m.Pid == schoolstage.DictionaryId).ToList();
                    foreach (var item in listtemp)
                    {
                        int num = 0;
                        int numed = 0;
                        List<object> objCourseTemp = new List<object>();
                        objCourseTemp.Add(schoolstage.DicName);
                        objCourseTemp.Add(item.DicName);
                        if (item.Pid == SchoolStageEnum.XiaoXue.ParseToInt())
                        {
                            objList.Insert(0, objCourseTemp);
                        }
                        else
                        {
                            objList.Add(objCourseTemp);
                        }
                        if (list != null)
                        {
                            var listTemp = list.Where(m => m.SchoolStage == item.Pid && m.CourseId == item.DictionaryId);
                            if (listTemp != null)
                            {
                                num = listTemp.Sum(m => m.Num);
                                if (num > 0)
                                {
                                    if (listPlanDetail != null && listPlanDetail.Count() > 0)
                                    {
                                        numed = listPlanDetail.Where(m => m.SchoolStage == item.Pid && m.CourseId == item.DictionaryId).Count();
                                    }
                                }
                            }
                        }

                        if (item.Pid == SchoolStageEnum.XiaoXue.ParseToInt())
                        {
                            objRatioList.Insert(0, AlgorithmHelper.Percentage(numed, num));
                        }
                        else
                        {
                            objRatioList.Add(AlgorithmHelper.Percentage(numed, num));
                        }
                    }
                }
                obj.Tag = 1;
                obj.Data = new
                {
                    TitleArr = objList,
                    RatioArr = objRatioList
                };
            }
            return obj;
        }

        /// <summary>
        /// 获取区县实验教学达标看板数据
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<TData<object>> GetDataChartsExperimentRatioList(PlanExamListParameterListParam param)
        {
            TData<object> obj = new TData<object>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            if (operatorinfo == null)
            {
                obj.Tag = -1;
                obj.Message = "登录信息失效！";
                return obj;
            }
            var paramGradeClass = new SchoolGradeClassListParam();
            paramGradeClass.UnitType = operatorinfo.UnitType;
            if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                var countyid = await unitService.GetParentUnitId(operatorinfo.UnitId ?? 0);
                param.SchoolId = operatorinfo.UnitId ?? 0;
                param.SchoolStageList = operatorinfo.SchoolStageList;
                param.UnitId = countyid;

                paramGradeClass.UnitId = operatorinfo.UnitId ?? 0;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                param.UnitId = operatorinfo.UnitId ?? 0;

                paramGradeClass.CountyId = operatorinfo.UnitId ?? 0;
            }
            else
            {
                obj.Tag = 1;
                obj.Data = new List<PlanExamParameterEntity>();
                obj.Total = 0;
                obj.Message = "未查询到相关数据。";
                return obj;
            }
             
            var pagination = new Pagination();
            pagination.PageSize = int.MaxValue;
            var list = await planExamParameterService.GetPageList(param, pagination);
            IEnumerable<PlanExamParameterEntity> listRecordDetail = null;
            IEnumerable<SchoolGradeClassEntity> listGradeClass = null;
            if (list != null && list.Count() > 0)
            {
                obj.Data = list.ToList();
                listRecordDetail = await planExamParameterService.GetPlanExamExpRecordList(param, null);

                //每个考核数，要乘以年级中的班级数。
                listGradeClass = await schoolGradeClassService.GetStatistics(paramGradeClass);
            }
            var paramSchoolStage = new StaticDictionaryListParam();
            paramSchoolStage.TypeCode = DicTypeCodeEnum.SchoolStage.ParseToInt().ToString();
            var listSchoolSatge = await staticDictionaryService.GetList(paramSchoolStage);

            var paramDicCourse = new StaticDictionaryListParam();
            paramDicCourse.TypeCode = DicTypeCodeEnum.Course.ParseToInt().ToString();
            paramDicCourse.PTypeCode = DicTypeCodeEnum.SchoolStage.ParseToInt().ToString();
            paramDicCourse.Nature = 1;
            paramDicCourse.Statuz = 1;
            var listCourse = await staticDictionaryService.GetAllList(paramDicCourse);
            if (listCourse != null && listCourse.Count > 0 && listSchoolSatge != null && listSchoolSatge.Count > 0)
            {
                List<object> objList = new List<object>();
                List<object> objRatioList = new List<object>();
                foreach (var schoolstage in listSchoolSatge)
                {
                    var listtemp = listCourse.Where(m => m.Pid == schoolstage.DictionaryId).ToList();
                    foreach (var item in listtemp)
                    {
                        int num = 0;
                        int numed = 0;
                        List<object> objCourseTemp = new List<object>();
                        objCourseTemp.Add(schoolstage.DicName);
                        objCourseTemp.Add(item.DicName);
                        if (item.Pid == SchoolStageEnum.XiaoXue.ParseToInt())
                        {
                            objList.Insert(0, objCourseTemp);
                        }
                        else
                        {
                            objList.Add(objCourseTemp);
                        }
                        if (list != null)
                        {
                            var listTemp = list.Where(m => m.SchoolStage == item.Pid && m.CourseId == item.DictionaryId);
                            if (listTemp != null)
                            {
                                num = listTemp.Sum(m => m.NeedGroupNum* GetExameClassNum(listGradeClass, m.GradeId, 0, m.SchoolStage,m.CourseId,m.CompulsoryType??0)
                                    + m.NeedShowNum* GetExameClassNum(listGradeClass, m.GradeId, 0, m.SchoolStage, m.CourseId, m.CompulsoryType ?? 0)
                                    + m.OptionalGroupNum * GetExameClassNum(listGradeClass, m.GradeId, 0, m.SchoolStage, m.CourseId, m.CompulsoryType ?? 0)
                                    + m.OptionalShowNum * GetExameClassNum(listGradeClass, m.GradeId, 0, m.SchoolStage, m.CourseId, m.CompulsoryType ?? 0));
                                if (num > 0)
                                {
                                    numed = listTemp.Sum(m =>
                                    NeedShowNumed(listRecordDetail, m.Id ?? 0, 0, m.GradeId, 0, listGradeClass, m.SchoolStage, m.CourseId, m.CompulsoryType ?? 0)
                                    + NeedGroupNumed(listRecordDetail, m.Id ?? 0, 0, m.GradeId, 0, listGradeClass, m.SchoolStage, m.CourseId, m.CompulsoryType ?? 0)
                                    + OptionalShowNumed(listRecordDetail, m.Id ?? 0, 0, m.GradeId, 0, listGradeClass, m.SchoolStage, m.CourseId, m.CompulsoryType ?? 0)
                                    + OptionalGroupNumed(listRecordDetail, m.Id ?? 0, 0, m.GradeId, 0, listGradeClass, m.SchoolStage, m.CourseId, m.CompulsoryType ?? 0));
                                }
                            }
                        }
                        if (item.Pid == SchoolStageEnum.XiaoXue.ParseToInt())
                        {
                            objRatioList.Insert(0, AlgorithmHelper.Percentage(numed, num));
                        }
                        else
                        {
                            objRatioList.Add(AlgorithmHelper.Percentage(numed, num));
                        }

                       
                    }
                }
                obj.Tag = 1;
                obj.Data = new
                {
                    TitleArr = objList,
                    RatioArr = objRatioList
                };
            }
            return obj;
        }

        public async Task<TData<object>> GetDataChartsExperimentRanking(PlanExamListParameterListParam param)
        {
            TData<object> obj = new TData<object>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            if (operatorinfo == null)
            {
                obj.Tag = -1;
                obj.Message = "登录信息失效！";
                return obj;
            }
            var paramPlanDetail = new PlanDetailListParam();
            var paramGradeClass = new SchoolGradeClassListParam();
            if (param.SchoolYearStart < 0 || param.SchoolTerm < 0)
            {
                obj.Tag = 0;
                obj.Message = "请选择需要查询的学年、学期。";
                return obj;
            }
            paramGradeClass.UnitType = operatorinfo.UnitType;
            if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                var countyid = await unitService.GetParentUnitId(operatorinfo.UnitId ?? 0);
                paramPlanDetail.SchoolId = operatorinfo.UnitId ?? 0;
                paramPlanDetail.UnitId = countyid;
                param.SchoolId = operatorinfo.UnitId ?? 0;
                param.SchoolStageList = operatorinfo.SchoolStageList;
                param.UnitId = countyid;

                paramGradeClass.UnitId = operatorinfo.UnitId ?? 0;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                paramPlanDetail.CountyId = operatorinfo.UnitId ?? 0;
                param.UnitId = operatorinfo.UnitId ?? 0;

                paramGradeClass.CountyId = operatorinfo.UnitId ?? 0;
            }
            else
            {
                obj.Tag = 1;
                obj.Data = new List<PlanExamParameterEntity>();
                obj.Total = 0;
                obj.Message = "未查询到相关数据。";
                return obj;
            }
            if (param.Id > 0)
            {
                var entity = await planExamParameterService.GetEntity(param.Id ?? 0);
                if (entity != null)
                {
                    param.SchoolPropList = UnitBLL.GetSchoolPropList(entity.SchoolStage);
                }
            }
            var list = await planExamParameterService.GetPlanExamRatePageList(param, null);
            if (list != null && list.Count() > 0)
            {
                var listRecordDetail = await planExamParameterService.GetPlanExamExpList(param);
                var templist = list.ToList();
                //每个考核数，要乘以年级中的班级数。
                var listGradeClass = await schoolGradeClassService.GetStatistics(paramGradeClass);
                var listAll = GetExamRecordList(list, listRecordDetail, listGradeClass);
                var totalList = listAll.GroupBy(m => m.SchoolId).Select(n => new PlanExamParameterEntity
                {
                    SchoolName = n.Min(j => j.SchoolName),
                    NeedShowNum = n.Sum(j => j.NeedShowNum),
                    NeedGroupNum = n.Sum(j => j.NeedGroupNum),
                    OptionalShowNum = n.Sum(j => j.OptionalShowNum),
                    OptionalGroupNum = n.Sum(j => j.OptionalGroupNum),
                    NeedShowNumed = n.Sum(j => j.NeedShowNumed),
                    NeedGroupNumed = n.Sum(j => j.NeedGroupNumed),
                    OptionalShowNumed = n.Sum(j => j.OptionalShowNumed),
                    OptionalGroupNumed = n.Sum(j => j.OptionalGroupNumed),
                });
                obj.Data = totalList.OrderByDescending(n => n.TotalRatio).Select(j => new { SchoolName = j.SchoolName, Ratio = j.TotalRatio }).Take(10);
            }
            obj.Tag = 1;
            return obj;
        }

        #endregion

        #region 学校开出率【按区县教学要求】 
        /// <summary>
        /// 按学校综合开出率（区级）
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<TData<List<PlanExamParameterEntity>>> GetPlanExamSchoolRateList(PlanExamListParameterListParam param, Pagination pagination)
        {
            TData<List<PlanExamParameterEntity>> obj = new TData<List<PlanExamParameterEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            var paramPlanDetail = new PlanDetailListParam();
            var paramGradeClass = new SchoolGradeClassListParam();
            if (param.SchoolYearStart < 0 || param.SchoolTerm < 0)
            {
                obj.Tag = 0;
                obj.Message = "请选择需要查询的学年、学期。";
                return obj;
            }
            paramGradeClass.UnitType = operatorinfo.UnitType;
            if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                var countyid = await unitService.GetParentUnitId(operatorinfo.UnitId ?? 0);
                paramPlanDetail.SchoolId = operatorinfo.UnitId ?? 0;
                paramPlanDetail.UnitId = countyid;
                param.SchoolId = operatorinfo.UnitId ?? 0;
                param.SchoolStageList = operatorinfo.SchoolStageList;
                param.UnitId = countyid;

                paramGradeClass.UnitId = operatorinfo.UnitId ?? 0;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                paramPlanDetail.CountyId = operatorinfo.UnitId ?? 0;
                param.UnitId = operatorinfo.UnitId ?? 0;

                paramGradeClass.CountyId = operatorinfo.UnitId ?? 0;
            }
            else
            {
                obj.Tag = 1;
                obj.Data = new List<PlanExamParameterEntity>();
                obj.Total = 0;
                obj.Message = "未查询到相关数据。";
                return obj;
            }
            if (param.Id > 0)
            {
                var entity = await planExamParameterService.GetEntity(param.Id ?? 0);
                if (entity != null)
                {
                    param.SchoolPropList = UnitBLL.GetSchoolPropList(entity.SchoolStage);
                    param.SchoolYearStart = entity.SchoolYearStart;
                }
            }
            var list = await planExamParameterService.GetPlanExamRatePageList(param,null);
            if (list != null && list.Count() > 0)
            {
                obj.Data = list.ToList();
                var listRecordDetail = await planExamParameterService.GetPlanExamExpList(param);

                //每个考核数，要乘以年级中的班级数。
                IEnumerable<SchoolGradeClassEntity> listGradeClass = null;
                //获取当前学期学年
                if (param.SchoolYearStart == operatorinfo.SchoolTermStartYear)
                {
                    listGradeClass = await schoolGradeClassService.GetStatistics(paramGradeClass);
                }
                else
                {
                    //查询历史数据
                    paramGradeClass.SchoolYear = param.SchoolYearStart ?? operatorinfo.SchoolTermStartYear;
                    listGradeClass = await schoolGradeClassService.GetHistoryStatistics(paramGradeClass);
                } 
                var listAll = GetExamRecordList(list, listRecordDetail, listGradeClass);
                var totalList = listAll.GroupBy(m => new { m.SchoolId, m.SchoolStage }).Select(n => new PlanExamParameterEntity
                {
                    SchoolYearStart = n.Min(j => j.SchoolYearStart),
                    SchoolYearEnd = n.Min(j => j.SchoolYearEnd),
                    SchoolTerm = n.Min(j => j.SchoolTerm),
                    SchoolStageName = n.Min(j => j.SchoolStageName),
                    SchoolName = n.Min(j => j.SchoolName),
                    SchoolId = n.Key.SchoolId,
                    Sort = n.Min(j => j.Sort),
                    NeedShowNum = n.Sum(j => j.NeedShowNum),
                    NeedGroupNum = n.Sum(j => j.NeedGroupNum),
                    OptionalShowNum = n.Sum(j => j.OptionalShowNum),
                    OptionalGroupNum = n.Sum(j => j.OptionalGroupNum),
                    NeedShowNumed = n.Sum(j => j.NeedShowNumed),
                    NeedGroupNumed = n.Sum(j => j.NeedGroupNumed),
                    OptionalShowNumed = n.Sum(j => j.OptionalShowNumed),
                    OptionalGroupNumed = n.Sum(j => j.OptionalGroupNumed),
                });
                obj.Total = totalList.Count();
                int pageNumber = 0;
                if (pagination.PageIndex > 1)
                {
                    pageNumber = (pagination.PageIndex - 1) * pagination.PageSize;
                }

                //排序
                if (pagination.SortType.ToLower().IndexOf("desc") > -1)
                {
                    if (pagination.Sort.ToLower().IndexOf("schoolid") > -1 || pagination.Sort.ToLower().IndexOf("schoolname") > -1)
                    {
                        totalList = totalList.OrderByDescending(m => m.Sort).ThenByDescending(m => m.SchoolId);
                    }
                    else if (pagination.Sort.ToLower().IndexOf("totalratio") > -1)
                    {
                        totalList = totalList.OrderByDescending(m => m.TotalRatio).ThenBy(m => m.SchoolId);
                    }
                }
                else
                {
                    if (pagination.Sort.ToLower().IndexOf("schoolid") > -1 || pagination.Sort.ToLower().IndexOf("schoolname") > -1)
                    {
                        totalList = totalList.OrderBy(m => m.Sort).ThenBy(m => m.SchoolId);
                    }
                    else if (pagination.Sort.ToLower().IndexOf("totalratio") > -1)
                    {
                        totalList = totalList.OrderBy(m => m.TotalRatio).ThenBy(m => m.SchoolId); ;
                    }
                }
                obj.Data = totalList.Skip(pageNumber).Take(pagination.PageSize).ToList();

            }
            else
            {
                obj.Data = new List<PlanExamParameterEntity>();
            }
            obj.Tag = 1;
            return obj;
        }
        /// <summary>
        /// 获取考核实验登记的数量
        /// </summary>
        /// <param name="entity"></param>
        /// <param name="list"></param>
        /// <param name="listclass"></param>
        /// <param name="isneeddo"></param>
        /// <param name="eType"></param>
        /// <returns></returns>
        public int GetPlanExamRecordNum(PlanExamParameterEntity entity, IEnumerable<PlanExamParameterEntity> list, IEnumerable<SchoolGradeClassEntity> listclass,int isneeddo, int eType)
        {
            int recordnum = 0;

            if (list != null && list.Count() > 0)
            {
                var listTemp = list.Where(m => m.GradeId == entity.GradeId && m.CourseId == entity.CourseId && m.SchoolStage == entity.SchoolStage);
                var listClassTemp = listclass.Where(n => UnitBLL.GetSchoolPropList(entity.SchoolStage).Contains(n.SchoolProp));
                if (entity.SchoolStage == SchoolStageEnum.GaoZhong.ParseToInt())
                {
                    if (entity.CompulsoryType == ClassCompulsoryTypeEnum.Select.ParseToInt())
                    {
                        listClassTemp = listClassTemp.Where(m => m.SelectSubject != null && m.SelectSubject.Contains(entity.CourseId.ToString()));
                    }
                    else
                    {
                        listClassTemp = listClassTemp.Where(m => m.SelectSubject == null || !m.SelectSubject.Contains(entity.CourseId.ToString()));
                    }
                  
                }
                listTemp = listTemp.Where(m => listClassTemp.Select(m => m.Id).Contains(m.SchoolGradeClassId));
                if (isneeddo==IsNeedEnum.MustDo.ParseToInt() && eType==ExperimentTypeEnum.Demo.ParseToInt())
                {
                    recordnum = listTemp.Sum(m => m.NeedShowNumed);
                }
                else if (isneeddo == IsNeedEnum.MustDo.ParseToInt() && eType == ExperimentTypeEnum.Group.ParseToInt())
                {
                    recordnum = listTemp.Sum(m => m.NeedGroupNumed);
                }
                else if (isneeddo == IsNeedEnum.SelectToDo.ParseToInt() && eType == ExperimentTypeEnum.Demo.ParseToInt())
                {
                    recordnum = listTemp.Sum(m => m.OptionalShowNumed);
                }
                else if (isneeddo == IsNeedEnum.SelectToDo.ParseToInt() && eType == ExperimentTypeEnum.Group.ParseToInt())
                {
                    recordnum = listTemp.Sum(m => m.OptionalGroupNumed);
                }
            } 
            return recordnum;
        }

        /// <summary>
        /// 按学科综合开出率（区级）
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<TData<List<PlanExamParameterEntity>>> GetPlanExamSchoolCourseRateList(PlanExamListParameterListParam param, Pagination pagination)
        {
            TData<List<PlanExamParameterEntity>> obj = new TData<List<PlanExamParameterEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            var paramPlanDetail = new PlanDetailListParam();
            var paramGradeClass = new SchoolGradeClassListParam();
            if (param.SchoolYearStart < 0 || param.SchoolTerm < 0)
            {
                obj.Tag = 0;
                obj.Message = "请选择需要查询的学年、学期。";
                return obj;
            }
            paramGradeClass.UnitType = operatorinfo.UnitType;
            if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                var countyid = await unitService.GetParentUnitId(operatorinfo.UnitId ?? 0);
                paramPlanDetail.SchoolId = operatorinfo.UnitId ?? 0;
                paramPlanDetail.UnitId = countyid;
                param.SchoolId = operatorinfo.UnitId ?? 0;
                param.SchoolStageList = operatorinfo.SchoolStageList;
                param.UnitId = countyid;

                paramGradeClass.UnitId = operatorinfo.UnitId ?? 0;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                paramPlanDetail.CountyId = operatorinfo.UnitId ?? 0;
                param.UnitId = operatorinfo.UnitId ?? 0;

                paramGradeClass.CountyId = operatorinfo.UnitId ?? 0;
            }
            else
            {
                obj.Tag = 1;
                obj.Data = new List<PlanExamParameterEntity>();
                obj.Total = 0;
                obj.Message = "未查询到相关数据。";
                return obj;
            }
            if (param.Id > 0)
            {
                var entity = await planExamParameterService.GetEntity(param.Id ?? 0);
                if (entity != null)
                {
                    param.SchoolPropList = UnitBLL.GetSchoolPropList(entity.SchoolStage);
                    param.SchoolYearStart = entity.SchoolYearStart;
                }
            }
            var list = await planExamParameterService.GetPlanExamRatePageList(param, null);
            if (list != null && list.Count() > 0)
            {
                obj.Data = list.ToList();
                var listRecordDetail = await planExamParameterService.GetPlanExamExpList(param);

                //每个考核数，要乘以年级中的班级数。
                IEnumerable<SchoolGradeClassEntity> listGradeClass = null;
                //获取当前学期学年
                if (param.SchoolYearStart == operatorinfo.SchoolTermStartYear)
                {

                    listGradeClass = await schoolGradeClassService.GetStatistics(paramGradeClass);
                }
                else
                {
                    //查询历史数据
                    paramGradeClass.SchoolYear = param.SchoolYearStart ?? operatorinfo.SchoolTermStartYear;
                    listGradeClass = await schoolGradeClassService.GetHistoryStatistics(paramGradeClass);
                } 
                var listAll = GetExamRecordList(list, listRecordDetail, listGradeClass);
                var totalList = listAll.GroupBy(m => new { m.SchoolId, m.SchoolStage, m.CourseId }).Select(n => new PlanExamParameterEntity
                {
                    SchoolYearStart = n.Min(j => j.SchoolYearStart),
                    SchoolYearEnd = n.Min(j => j.SchoolYearEnd),
                    SchoolTerm = n.Min(j => j.SchoolTerm),
                    SchoolStageName = n.Min(j => j.SchoolStageName),
                    SchoolName = n.Min(j => j.SchoolName),
                    SchoolId = n.Key.SchoolId,
                    CourseName = n.Min(j => j.CourseName),
                    CourseId = n.Key.CourseId,
                    Sort = n.Min(j => j.Sort),
                    NeedShowNum = n.Sum(j => j.NeedShowNum),
                    NeedGroupNum = n.Sum(j => j.NeedGroupNum),
                    OptionalShowNum = n.Sum(j => j.OptionalShowNum),
                    OptionalGroupNum = n.Sum(j => j.OptionalGroupNum),
                    NeedShowNumed = n.Sum(j => j.NeedShowNumed),
                    NeedGroupNumed = n.Sum(j => j.NeedGroupNumed),
                    OptionalShowNumed = n.Sum(j => j.OptionalShowNumed),
                    OptionalGroupNumed = n.Sum(j => j.OptionalGroupNumed),
                });
                obj.Total = totalList.Count();
                int pageNumber = 0;
                if (pagination.PageIndex > 1)
                {
                    pageNumber = (pagination.PageIndex - 1) * pagination.PageSize;
                }
                //排序
                if (pagination.SortType.ToLower().IndexOf("desc") > -1)
                {
                    if (pagination.Sort.ToLower().IndexOf("schoolid") > -1 || pagination.Sort.ToLower().IndexOf("schoolname") > -1)
                    {
                        totalList = totalList.OrderByDescending(m => m.Sort).ThenByDescending(m => m.SchoolId);
                    }
                    else if (pagination.Sort.ToLower().IndexOf("totalratio") > -1)
                    {
                        totalList = totalList.OrderByDescending(m => m.TotalRatio);
                    }
                }
                else
                {
                    if (pagination.Sort.ToLower().IndexOf("schoolid") > -1 || pagination.Sort.ToLower().IndexOf("schoolname") > -1)
                    {
                        totalList = totalList.OrderBy(m => m.Sort).ThenBy(m => m.SchoolId);
                    }
                    else if (pagination.Sort.ToLower().IndexOf("totalratio") > -1)
                    {
                        totalList = totalList.OrderBy(m => m.TotalRatio);
                    }
                }
                obj.Data = totalList.Skip(pageNumber).Take(pagination.PageSize).ToList();
            }
            else
            {
                obj.Data = new List<PlanExamParameterEntity>();
            }
            obj.Tag = 1;
            return obj;
        }

        #endregion

        #region 私有方法
        private IEnumerable<PlanExamParameterEntity> GetExamRecordList(IEnumerable<PlanExamParameterEntity> list, IEnumerable<PlanExamParameterEntity> listRecordDetail, IEnumerable<SchoolGradeClassEntity> listGradeClass)
        {
            var listRecord = listRecordDetail.GroupBy(m => new { m.Id, m.SchoolId, m.SchoolStage, m.GradeId, m.CourseId, m.SchoolGradeClassId, m.CompulsoryType }).Select(m => new PlanExamParameterEntity()
            {
                Id = m.Key.Id,
                SchoolId = m.Key.SchoolId,
                SchoolStage = m.Key.SchoolStage,
                CourseId = m.Key.CourseId,
                GradeId = m.Key.GradeId,
                SchoolGradeClassId = m.Key.SchoolGradeClassId,
                CompulsoryType = m.Key.CompulsoryType,
                NeedShowNumed = m.Where(j => j.IsNeedDo == 1 && j.ExperimentType == 1).Count(),
                NeedGroupNumed = m.Where(j => j.IsNeedDo == 1 && j.ExperimentType == 2).Count(),
                OptionalShowNumed = m.Where(j => j.IsNeedDo == 2 && j.ExperimentType == 1).Count(),
                OptionalGroupNumed = m.Where(j => j.IsNeedDo == 2 && j.ExperimentType == 2).Count(),
            });//m.SchoolYearStart, m.SchoolTerm, 

            // .Where(m => (m.SchoolStage == SchoolStageEnum.GaoZhong.ParseToInt() && m.CompulsoryType == ClassCompulsoryTypeEnum.Select.ParseToInt())? listGradeClass.Where(t => t.SelectSubject != null && t.SelectSubject.Contains(m.CourseId.ToString())):listGradeClass.Where(o => o.SelectSubject == null || !o.SelectSubject.Contains(m.CourseId.ToString())))
            var listAll = list.GroupJoin(listRecord, examp => new { examp.Id, examp.SchoolId, examp.SchoolStage }, record => new { record.Id, record.SchoolId, record.SchoolStage },
                (examp, record) => new PlanExamParameterEntity()
                {
                    SchoolName = examp.SchoolName,
                    SchoolId = examp.SchoolId,
                    Sort = examp.Sort,
                    SchoolYearStart = examp.SchoolYearStart,
                    SchoolYearEnd = examp.SchoolYearEnd,
                    SchoolTerm = examp.SchoolTerm,
                    SchoolStage = examp.SchoolStage,
                    SchoolStageName = examp.SchoolStageName,
                    CourseId = examp.CourseId,
                    CourseName = examp.CourseName,
                    NeedShowNum = examp.NeedShowNum * GetExameClassNum(listGradeClass, examp.GradeId, examp.SchoolId ?? 0, examp.SchoolStage, examp.CourseId, examp.CompulsoryType ?? 0),
                    NeedGroupNum = examp.NeedGroupNum * GetExameClassNum(listGradeClass, examp.GradeId, examp.SchoolId ?? 0, examp.SchoolStage, examp.CourseId, examp.CompulsoryType ?? 0),
                    OptionalShowNum = examp.OptionalShowNum * GetExameClassNum(listGradeClass, examp.GradeId, examp.SchoolId ?? 0, examp.SchoolStage, examp.CourseId, examp.CompulsoryType ?? 0),
                    OptionalGroupNum = examp.OptionalGroupNum * GetExameClassNum(listGradeClass, examp.GradeId, examp.SchoolId ?? 0, examp.SchoolStage, examp.CourseId, examp.CompulsoryType ?? 0),
                    NeedShowNumed = GetPlanExamRecordNum(examp, record, listGradeClass, 1, 1),
                    NeedGroupNumed = GetPlanExamRecordNum(examp, record, listGradeClass, 1, 2),
                    OptionalShowNumed = GetPlanExamRecordNum(examp, record, listGradeClass, 2, 1),
                    OptionalGroupNumed = GetPlanExamRecordNum(examp, record, listGradeClass, 2, 2),
                }); 
            return listAll;
        }

        private int NeedShowNumed(IEnumerable<PlanExamParameterEntity> list, long id, long schoolid, long gradeid = 0, long schoolgradeclassid = 0, IEnumerable<SchoolGradeClassEntity> listclass = null, int schoolstage = 0, int courseid = 0, int compulsorytype = 0,long teacherid = 0)
        {
            int num = 0;
            if (list != null)
            {
                var listTemp = list.Where(n => n.Id == id && n.IsNeedDo == 1 && n.ExperimentType == 1);
                if (listclass != null && schoolstage > 0)
                {
                    var listClassTemp = listclass.Where(n => UnitBLL.GetSchoolPropList(schoolstage).Contains(n.SchoolProp));
                    if (schoolstage ==SchoolStageEnum.GaoZhong.ParseToInt())
                    {
                        if (compulsorytype == ClassCompulsoryTypeEnum.Select.ParseToInt())
                        {
                            listClassTemp = listClassTemp.Where(m => m.SelectSubject != null && m.SelectSubject.Contains(courseid.ToString()));
                        }
                        else
                        {
                            listClassTemp = listClassTemp.Where(m => m.SelectSubject == null || !m.SelectSubject.Contains(courseid.ToString()));
                        }
                    }
                    listTemp = listTemp.Where(m => listClassTemp.Select(m => m.Id).Contains(m.SchoolGradeClassId));
                }
                if (schoolid > 0)
                {
                    listTemp = listTemp.Where(m => m.SchoolId == schoolid);
                }
                if (gradeid > 0)
                {
                    listTemp = listTemp.Where(m => m.GradeId == gradeid);
                }
                if (schoolgradeclassid > 0)
                {
                    listTemp = listTemp.Where(m => m.SchoolGradeClassId == schoolgradeclassid);
                }
                if (teacherid > 0)
                {
                    listTemp = listTemp.Where(m => m.TeacherId == teacherid);
                }
                num = listTemp.Count();
            }
            return num;
        }
        private int NeedGroupNumed(IEnumerable<PlanExamParameterEntity> list,long id, long schoolid, long gradeid = 0, long schoolgradeclassid = 0, IEnumerable<SchoolGradeClassEntity> listclass = null, int schoolstage = 0, int courseid = 0, int compulsorytype = 0, long teacherid = 0)
        {
            int num = 0;
            if (list != null)
            {
                var listTemp = list.Where(n => n.Id == id && n.IsNeedDo == 1 && n.ExperimentType == 2);
                if (listclass != null && schoolstage > 0)
                {
                    var listClassTemp = listclass.Where(n => UnitBLL.GetSchoolPropList(schoolstage).Contains(n.SchoolProp) && n.GradeId == gradeid);
                    if (schoolstage == SchoolStageEnum.GaoZhong.ParseToInt())
                    {
                        if (compulsorytype == ClassCompulsoryTypeEnum.Select.ParseToInt())
                        {
                            listClassTemp = listClassTemp.Where(m => m.SelectSubject != null && m.SelectSubject.Contains(courseid.ToString()));
                        }
                        else
                        {
                            listClassTemp = listClassTemp.Where(m => m.SelectSubject == null || !m.SelectSubject.Contains(courseid.ToString()));
                        }
                    }
                    listTemp = listTemp.Where(m => listClassTemp.Select(n => n.Id).Contains(m.SchoolGradeClassId));
                }
                if (schoolid > 0)
                {
                    listTemp = listTemp.Where(m => m.SchoolId == schoolid);
                }
                if (gradeid > 0)
                {
                    listTemp = listTemp.Where(m => m.GradeId == gradeid);
                }
                if (schoolgradeclassid > 0)
                {
                    listTemp = listTemp.Where(m => m.SchoolGradeClassId == schoolgradeclassid);
                }
                if (teacherid > 0)
                {
                    listTemp = listTemp.Where(m => m.TeacherId == teacherid);
                }
                num = listTemp.Count();
            }
            return num;
        }
        private int OptionalShowNumed(IEnumerable<PlanExamParameterEntity> list, long id, long schoolid, long gradeid = 0, long schoolgradeclassid = 0, IEnumerable<SchoolGradeClassEntity> listclass = null, int schoolstage = 0, int courseid = 0, int compulsorytype = 0, long teacherid = 0)
        {
            int num = 0;
            if (list != null)
            {
                var listTemp = list.Where(n => n.Id == id && n.IsNeedDo == 2 && n.ExperimentType == 1);
                if (listclass != null && schoolstage > 0)
                {
                    var listClassTemp = listclass.Where(n => UnitBLL.GetSchoolPropList(schoolstage).Contains(n.SchoolProp));
                    if (schoolstage == SchoolStageEnum.GaoZhong.ParseToInt())
                    {
                        if (compulsorytype == ClassCompulsoryTypeEnum.Select.ParseToInt())
                        {
                            listClassTemp = listClassTemp.Where(m => m.SelectSubject != null && m.SelectSubject.Contains(courseid.ToString()));
                        }
                        else
                        {
                            listClassTemp = listClassTemp.Where(m => m.SelectSubject == null || !m.SelectSubject.Contains(courseid.ToString()));
                        }
                    }
                    listTemp = listTemp.Where(m => listClassTemp.Select(m => m.Id).Contains(m.SchoolGradeClassId));
                }
                if (schoolid > 0)
                {
                    listTemp = listTemp.Where(m => m.SchoolId == schoolid);
                }
                if (gradeid > 0)
                {
                    listTemp = listTemp.Where(m => m.GradeId == gradeid);
                }
                if (schoolgradeclassid > 0)
                {
                    listTemp = listTemp.Where(m => m.SchoolGradeClassId == schoolgradeclassid);
                }
                if (teacherid > 0)
                {
                    listTemp = listTemp.Where(m => m.TeacherId == teacherid);
                }
                num = listTemp.Count();
            }
            return num;
        }

        private int OptionalGroupNumed(IEnumerable<PlanExamParameterEntity> list, long id, long schoolid, long gradeid = 0, long schoolgradeclassid=0, IEnumerable<SchoolGradeClassEntity> listclass = null, int schoolstage = 0, int courseid = 0, int compulsorytype = 0, long teacherid = 0)
        {
            int num = 0;
            if (list != null)
            {
                var listTemp = list.Where(n => n.Id == id && n.IsNeedDo == 2 && n.ExperimentType == 2);
                if (listclass != null && schoolstage > 0)
                {
                    var listClassTemp = listclass.Where(n => UnitBLL.GetSchoolPropList(schoolstage).Contains(n.SchoolProp));
                    if (schoolstage == SchoolStageEnum.GaoZhong.ParseToInt())
                    {
                        if (compulsorytype == ClassCompulsoryTypeEnum.Select.ParseToInt())
                        {
                            listClassTemp = listClassTemp.Where(m => m.SelectSubject != null && m.SelectSubject.Contains(courseid.ToString()));
                        }
                        else
                        {
                            listClassTemp = listClassTemp.Where(m => m.SelectSubject == null || !m.SelectSubject.Contains(courseid.ToString()));
                        }
                    }
                    listTemp = listTemp.Where(m => listClassTemp.Select(m => m.Id).Contains(m.SchoolGradeClassId));
                }
                if (schoolid > 0)
                {
                    listTemp = listTemp.Where(m => m.SchoolId == schoolid);
                }
                if (gradeid > 0)
                {
                    listTemp = listTemp.Where(m => m.GradeId == gradeid);
                }
                if (schoolgradeclassid > 0)
                {
                    listTemp = listTemp.Where(m => m.SchoolGradeClassId == schoolgradeclassid);
                }
                if (teacherid > 0)
                {
                    listTemp = listTemp.Where(m => m.TeacherId == teacherid);
                }
                num = listTemp.Count();
            }
            return num;
        }

        private int GetExameClassNum(IEnumerable<SchoolGradeClassEntity> list, int gradeid, long schoolid = 0, int schoolstage = 0, int courseid = 0, int compulsorytype = 0)
        {
            int num = 0;
            if (list != null)
            {
                var listTemp = list.Where(m => m.UnitId > 0);
                if (gradeid > 0)
                {
                    listTemp = listTemp.Where(m => m.GradeId == gradeid);
                }
                if (schoolid > 0)
                {
                    listTemp = listTemp.Where(m => m.UnitId == schoolid);
                }
                if (schoolstage > 0)
                {
                    listTemp = listTemp.Where(m => UnitBLL.GetSchoolPropList(schoolstage).Contains(m.SchoolProp));
                }
                if (gradeid >= GradeEnum.GaoYi.ParseToInt())
                {
                    if (compulsorytype == ClassCompulsoryTypeEnum.Select.ParseToInt())
                    {
                        listTemp = listTemp.Where(m => m.SelectSubject != null && m.SelectSubject.Contains(courseid.ToString()));
                    }
                    else
                    {
                        listTemp = listTemp.Where(m => m.SelectSubject == null || !m.SelectSubject.Contains(courseid.ToString()));
                    }
                }
                num = listTemp.Count();
            }
            return num;
        }

        #endregion
    }
}
