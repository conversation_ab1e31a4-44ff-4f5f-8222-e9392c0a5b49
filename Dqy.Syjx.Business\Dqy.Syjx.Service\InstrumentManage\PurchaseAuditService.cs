﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.InstrumentManage;
using Dqy.Syjx.Model.Param.InstrumentManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Model.Input.InstrumentManage;
using Dqy.Syjx.Enum.InstrumentManage;

namespace Dqy.Syjx.Service.InstrumentManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-10-11 10:38
    /// 描 述：服务类
    /// </summary>
    public class PurchaseAuditService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<PurchaseAuditEntity>> GetList(PurchaseAuditListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<PurchaseAuditEntity>> GetPageList(PurchaseAuditListParam param, Pagination pagination)
        {
            StringBuilder sql = new StringBuilder();
            var expression = ListFilter(param, sql);
            var list = await this.BaseRepository().FindList<PurchaseAuditEntity>(sql.ToString(), expression.ToArray(), pagination);
            return list.ToList();
        }

        public async Task<PurchaseAuditEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<PurchaseAuditEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(PurchaseAuditEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update eq_PurchaseAudit set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
                strSql = $"update eq_PurchaseAudit set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }

        public async Task UpdateIsWithdraw(long purchaseDeclarationId)
        {
            string sql = $" update  eq_PurchaseAudit set IsWithdraw = {IsEnum.No.ParseToInt()} where PurchaseDeclarationId = {purchaseDeclarationId} and IsWithdraw = {IsEnum.Yes.ParseToInt()}";
            await this.BaseRepository().ExecuteBySql(sql);
        }

        public async Task UpdateIsShow(long purchaseDeclarationId)
        {
            string sql = $" update  eq_PurchaseAudit set IsShow = {IsEnum.No.ParseToInt()} where PurchaseDeclarationId = {purchaseDeclarationId} and IsShow = {IsEnum.Yes.ParseToInt()}";
            await this.BaseRepository().ExecuteBySql(sql);
        }

        /// <summary>
        /// 审批方法
        /// </summary>
        /// <param name="auditEntity">审批过程表</param>
        /// <param name="declareEntity">申报表</param>
        /// <returns></returns>
        public async Task Audit(PurchaseAuditEntity auditEntity,PurchaseDeclarationEntity declareEntity)
        {
            var db = await this.BaseRepository().BeginTrans();
            try
            {
                //更新审批过程表撤回状态
                await UpdateIsWithdraw(auditEntity.PurchaseDeclarationId);

                //插入审批过程表数据
                await auditEntity.Create();
                await db.Insert(auditEntity);

                //更新申报主表状态
                PurchaseDeclarationService purchaseDeclarationService = new PurchaseDeclarationService();
                await declareEntity.Modify();
                await db.Update(declareEntity);

                await db.CommitTrans();
            }
            catch (Exception ex)
            {
                await db.RollbackTrans();
                throw new Exception("程序出现异常，异常信息为：" + ex.Message);
            }
        }

        public async Task Withdraw(long id ,PurchaseDeclarationEntity declareEntity)
        {
            var db = await this.BaseRepository().BeginTrans();
            try
            {
                //删除审批数据
                await DeleteForm(id.ToString());

                //查找上级审批
                var auditList = await GetList(new PurchaseAuditListParam { PurchaseDeclarationId = declareEntity.Id, IsShow = IsEnum.Yes.ParseToInt() });
                int statuz = 0;
                if (auditList.Count > 0)
                {
                    var audit = auditList.OrderByDescending(p => p.Id).First();
                    statuz = audit.ApprovalStatuz;

                    //更新上级审批为可撤回
                    audit.IsWithdraw = IsEnum.Yes.ParseToInt();
                    await audit.Modify();
                    await db.Update(audit);
                }
                else
                {
                    statuz = InstrumentAuditStatuzEnum.WaitSchoolAudit.ParseToInt();
                }

                //更新申报表状态
                declareEntity.Statuz = statuz;
                declareEntity.IsGoBack = IsEnum.No.ParseToInt();
                PurchaseDeclarationService purchaseDeclarationService = new PurchaseDeclarationService();
                await declareEntity.Modify();
                await db.Update(declareEntity);

                await db.CommitTrans();
            }
            catch (Exception ex)
            {
                await db.RollbackTrans();
                throw new Exception("程序出现异常，异常信息为：" + ex.Message);
            }
        }
        #endregion

        #region 私有方法

        private Expression<Func<PurchaseAuditEntity, bool>> ListFilter(PurchaseAuditListParam param)
        {
            var expression = LinqExtensions.True<PurchaseAuditEntity>();
            expression = expression.And(t => t.BaseIsDelete == 0);
            if (param != null)
            {
                if (!param.PurchaseDeclarationId.IsNullOrZero())
                {
                    expression = expression.And(t => t.PurchaseDeclarationId == param.PurchaseDeclarationId);
                }
                if (param.IsShow.HasValue)
                {
                    expression = expression.And(t => t.IsShow == param.IsShow);
                }
            }
            return expression;
        }

        private List<DbParameter> ListFilter(PurchaseAuditListParam param, StringBuilder sql)
        {
            sql.Append(@" SELECT * From (
                            SELECT  A.Id ,PD.Code,
                                    A.BaseIsDelete ,A.BaseCreateTime ,A.BaseModifyTime ,A.BaseCreatorId ,A.BaseModifierId ,A.BaseVersion ,
                                    A.PurchaseDeclarationId ,
                                    A.ApprovalStatuz ,
                                    A.ApprovalStatuzDesc ,
                                    A.ApprovalRemark ,
                                    A.ProcessNumber ,
                                    A.IsShow ,
                                    A.IsWithdraw ,
                                    A.UnitId ,
                                    PD.BaseCreateTime AS DeclareDate ,
                                    PD.PurchaseYear ,
                                    PD.Name ,
                                    PD.Model ,
                                    PD.UnitName ,
                                    PD.Num ,
                                    PD.Price ,
                                    (PD.Num * PD.Price) AS AmountSum ,
                                    PD.Stage ,
                                    PD.StageId ,
                                    PD.Course ,
                                    PD.CourseId ,
                                    PD.Reason ,
                                    PD.Statuz ,
                                    PD.SchoolId ,
                                    PD.BaseCreatorId AS DeclareUserId ,
                                    (CASE SD.Depth WHEN 3 THEN SD3.Id WHEN 2 THEN SD2.Id WHEN 1 THEN SD.Id END) AS InstrumentClassId ,
                                    (CASE SD.Depth WHEN 3 THEN SD3.Name WHEN 2 THEN SD2.Name WHEN 1 THEN SD.Name END) AS InstrumentClassName ,
                                     U.RealName AS UserName ,
                                     U2.UserName AS AuditUserName ,SD.IsDangerChemical
                                    FROM  eq_PurchaseAudit AS A
                                    INNER JOIN  eq_PurchaseDeclaration AS PD ON A.PurchaseDeclarationId = PD.Id AND PD.BaseIsDelete = 0
                                    INNER JOIN  SysUser AS U ON PD.BaseCreatorId = U.Id
                                    INNER JOIN  SysUser AS U2 ON A.BaseCreatorId = U2.Id
                                    INNER JOIN  eq_InstrumentStandard AS SD ON PD.InstrumentStandardId = SD.Id AND SD.ClassType = 1 AND IsLast = 1
                                    INNER JOIN  eq_InstrumentStandard AS SD2 ON SD.Pid = SD2.Id
                                    LEFT JOIN  eq_InstrumentStandard AS SD3 ON SD2.Pid = SD3.Id
                                    WHERE A.BaseIsDelete = 0
                            ) as T WHERE 1 = 1  ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.IsShow.HasValue)
                {
                    sql.Append(" AND IsShow = @IsShow ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@IsShow", param.IsShow));
                }
                if (!param.UnitId.IsNullOrZero())
                {
                    sql.Append(" AND UnitId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                if (!param.UserId.IsNullOrZero())
                {
                    sql.Append(" AND BaseCreatorId = @UserId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UserId", param.UserId));
                }
                if (!param.PurchaseDeclarationId.IsNullOrZero())
                {
                    sql.Append(" AND PurchaseDeclarationId = @PurchaseDeclarationId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@PurchaseDeclarationId", param.PurchaseDeclarationId));
                }
                if (param.StartDate.HasValue)
                {
                    sql.Append(" AND BaseCreateTime >= @StartDate");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@StartDate", param.StartDate));
                }
                if (param.EndDate.HasValue)
                {
                    sql.Append(" AND BaseCreateTime <= @EndDate");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@EndDate", param.EndDate.Value.AddDays(1).AddSeconds(-1)));
                }
                if (!param.PurchaseYear.IsNullOrZero() && param.PurchaseYear > 0)
                {
                    sql.Append(" AND PurchaseYear = @PurchaseYear ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@PurchaseYear", param.PurchaseYear));
                }
                if (!param.InstrumentClassId.IsNullOrZero() && param.InstrumentClassId > 0)
                {
                    sql.Append(" AND InstrumentClassId = @InstrumentClassId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@InstrumentClassId", param.InstrumentClassId));
                }
                if (!param.CourseId.IsNullOrZero() && param.CourseId > 0)
                {
                    sql.Append($" AND CourseId = @CourseId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (param.Statuz.HasValue && param.Statuz > -1)
                {
                    sql.Append(" AND Statuz = @Statuz");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Statuz", param.Statuz));
                }
                if (!param.DeclareUserId.IsNullOrZero())
                {
                    sql.Append(" AND DeclareUserId = @DeclareUserId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@DeclareUserId", param.DeclareUserId));
                }
                if (param.IsWithdraw > 0)
                {
                    sql.Append(" AND IsWithdraw = @IsWithdraw");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@IsWithdraw", param.IsWithdraw));
                }
            }
            return parameter;
        }
        #endregion
    }
}
