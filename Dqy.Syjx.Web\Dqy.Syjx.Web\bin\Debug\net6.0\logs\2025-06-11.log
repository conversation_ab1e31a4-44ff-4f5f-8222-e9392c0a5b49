2025-06-11 09:08:02.8776||DEBUG||程序启动 |ContentRootPath:D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web |WebRootPath:D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\wwwroot |IsDevelopment:True |url: |action: 
2025-06-11 09:09:04.3096|3|WARN|Microsoft.WebTools.BrowserLink.Net.BrowserLinkMiddleware|Unable to configure Browser Link script injection on the response.  |url: http://localhost/QueryStatisticsManage/ExperimentAudit/ExperimentAuditDetail|action: ExperimentAuditDetail
2025-06-11 09:09:04.5321||INFO||URL1: |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-11 09:09:04.5998||INFO||URL1: |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-11 09:09:04.7659||INFO||URL1: |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-11 09:09:09.3474||INFO||URL1:/QueryStatisticsManage/UserInfo/CountyExperimenterList |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-11 09:09:21.7085||INFO||URL1:/QueryStatisticsManage/UserInfo/CountyExperimenterList |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-11 09:09:26.0829||INFO||URL1:/QueryStatisticsManage/UserInfo/CountyTrainList |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-11 09:09:30.9926||INFO||URL1:/QueryStatisticsManage/UserInfo/CountyAwardList |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-11 09:09:34.0069||INFO||URL1:/QueryStatisticsManage/UnitInfo/SummaryList |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-11 09:09:34.4652||ERROR||Input array is longer than the number of columns in this table.
   at System.Data.DataTable.NewRecordFromArray(Object[] value)
   at System.Data.DataRowCollection.Add(Object[] values)
   at Dqy.Syjx.Service.QueryStatisticsManage.UnitInfoService.GetSummaryList(UnitInfoListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Service\QueryStatisticsManage\UnitInfoService.cs:line 233
   at Dqy.Syjx.Business.QueryStatisticsManage.UnitInfoBLL.GetSummaryList(UnitInfoListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Business\QueryStatisticsManage\UnitInfoBLL.cs:line 80
   at Dqy.Syjx.Web.Areas.QueryStatisticsManage.Controllers.UnitInfoController.GetSummaryListJson(UnitInfoListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\Areas\QueryStatisticsManage\Controllers\UnitInfoController.cs:line 72
   at lambda_method3112(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
 |url: http://localhost/QueryStatisticsManage/UnitInfo/GetSummaryListJson|action: GetSummaryListJson
2025-06-11 09:09:40.8402||ERROR||Input array is longer than the number of columns in this table.
   at System.Data.DataTable.NewRecordFromArray(Object[] value)
   at System.Data.DataRowCollection.Add(Object[] values)
   at Dqy.Syjx.Service.QueryStatisticsManage.UnitInfoService.GetSummaryList(UnitInfoListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Service\QueryStatisticsManage\UnitInfoService.cs:line 233
   at Dqy.Syjx.Business.QueryStatisticsManage.UnitInfoBLL.GetSummaryList(UnitInfoListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Business\QueryStatisticsManage\UnitInfoBLL.cs:line 80
   at Dqy.Syjx.Web.Areas.QueryStatisticsManage.Controllers.UnitInfoController.GetSummaryListJson(UnitInfoListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\Areas\QueryStatisticsManage\Controllers\UnitInfoController.cs:line 72
   at lambda_method3112(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
 |url: http://localhost/QueryStatisticsManage/UnitInfo/GetSummaryListJson|action: GetSummaryListJson
2025-06-11 09:13:06.3706||ERROR||Input array is longer than the number of columns in this table.
   at System.Data.DataTable.NewRecordFromArray(Object[] value)
   at System.Data.DataRowCollection.Add(Object[] values)
   at Dqy.Syjx.Service.QueryStatisticsManage.UnitInfoService.GetSummaryList(UnitInfoListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Service\QueryStatisticsManage\UnitInfoService.cs:line 233
   at Dqy.Syjx.Business.QueryStatisticsManage.UnitInfoBLL.GetSummaryList(UnitInfoListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Business\QueryStatisticsManage\UnitInfoBLL.cs:line 80
   at Dqy.Syjx.Web.Areas.QueryStatisticsManage.Controllers.UnitInfoController.GetSummaryListJson(UnitInfoListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\Areas\QueryStatisticsManage\Controllers\UnitInfoController.cs:line 72
   at lambda_method3112(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
 |url: http://localhost/QueryStatisticsManage/UnitInfo/GetSummaryListJson|action: GetSummaryListJson
2025-06-11 09:47:58.2549||WARN||耗时的Sql：SELECT  Id,Name,Sort FROM  d_ArticleCategory WHERE BaseIsDelete = 0 AND Pid = 0 AND CateType =  1 ORDER BY Sort ASC LIMIT 3 |url: http://localhost/Home/Welcome|action: Welcome
2025-06-11 09:47:58.2693||WARN||耗时的Sql：INSERT INTO `SysLogOperate` (`Id`, `BaseCreateTime`, `BaseCreatorId`, `BusinessType`, `ExecuteParam`, `ExecuteResult`, `ExecuteTime`, `ExecuteUrl`, `IpAddress`, `IpLocation`, `LogStatus`, `LogType`, `Remark`)
VALUES (852845913427677184, 2025/6/11 9:45:10, 0, , ?schoolId=356379251328421888, , 0, /ExperimentAudit/ExperimentAuditDetail, ::ffff:127.0.0.1, , 2025/6/11 9:45:100, 2025/6/11 9:45:101, 2025/6/11 9:45:102);
 |url: |action: 
2025-06-11 09:48:00.3455||WARN||耗时的Sql：INSERT INTO `SysLogOperate` (`Id`, `BaseCreateTime`, `BaseCreatorId`, `BusinessType`, `ExecuteParam`, `ExecuteResult`, `ExecuteTime`, `ExecuteUrl`, `IpAddress`, `IpLocation`, `LogStatus`, `LogType`, `Remark`)
VALUES (852846199919611904, 2025/6/11 9:46:19, 0, , , , 0, /ExperimentAudit/ExperimentAuditIndex, ::ffff:127.0.0.1, , 2025/6/11 9:46:190, 2025/6/11 9:46:191, 2025/6/11 9:46:192);
 |url: |action: 
2025-06-11 09:48:00.3707||WARN||耗时的Sql：INSERT INTO `SysLogOperate` (`Id`, `BaseCreateTime`, `BaseCreatorId`, `BusinessType`, `ExecuteParam`, `ExecuteResult`, `ExecuteTime`, `ExecuteUrl`, `IpAddress`, `IpLocation`, `LogStatus`, `LogType`, `Remark`)
VALUES (852846199932194816, 2025/6/11 9:46:19, 0, , ?schoolId=356379251328421888, , 0, /ExperimentTeach/PlanExamSchoolRateList, ::ffff:127.0.0.1, , 2025/6/11 9:46:190, 2025/6/11 9:46:191, 2025/6/11 9:46:192);
 |url: |action: 
2025-06-11 09:54:46.4251||WARN||耗时的Sql：INSERT INTO `SysLogOperate` (`Id`, `BaseCreateTime`, `BaseCreatorId`, `BusinessType`, `ExecuteParam`, `ExecuteResult`, `ExecuteTime`, `ExecuteUrl`, `IpAddress`, `IpLocation`, `LogStatus`, `LogType`, `Remark`)
VALUES (852846635279978496, 2025/6/11 9:48:02, 0, , , , 103802, /Home/Welcome, ::ffff:127.0.0.1, , 2025/6/11 9:48:020, 2025/6/11 9:48:021, 2025/6/11 9:48:022);
 |url: http://localhost/Home/Welcome|action: Welcome
2025-06-11 09:54:46.7031||ERROR||Input array is longer than the number of columns in this table.
   at System.Data.DataTable.NewRecordFromArray(Object[] value)
   at System.Data.DataRowCollection.Add(Object[] values)
   at Dqy.Syjx.Service.QueryStatisticsManage.UnitInfoService.GetSummaryList(UnitInfoListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Service\QueryStatisticsManage\UnitInfoService.cs:line 233
   at Dqy.Syjx.Business.QueryStatisticsManage.UnitInfoBLL.GetSummaryList(UnitInfoListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Business\QueryStatisticsManage\UnitInfoBLL.cs:line 80
   at Dqy.Syjx.Web.Areas.QueryStatisticsManage.Controllers.UnitInfoController.GetSummaryListJson(UnitInfoListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\Areas\QueryStatisticsManage\Controllers\UnitInfoController.cs:line 72
   at lambda_method3112(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
 |url: http://localhost/QueryStatisticsManage/UnitInfo/GetSummaryListJson|action: GetSummaryListJson
2025-06-11 09:57:07.3084||ERROR||Input array is longer than the number of columns in this table.
   at System.Data.DataTable.NewRecordFromArray(Object[] value)
   at System.Data.DataRowCollection.Add(Object[] values)
   at Dqy.Syjx.Service.QueryStatisticsManage.UnitInfoService.GetSummaryList(UnitInfoListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Service\QueryStatisticsManage\UnitInfoService.cs:line 233
   at Dqy.Syjx.Business.QueryStatisticsManage.UnitInfoBLL.GetSummaryList(UnitInfoListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Business\QueryStatisticsManage\UnitInfoBLL.cs:line 80
   at Dqy.Syjx.Web.Areas.QueryStatisticsManage.Controllers.UnitInfoController.GetSummaryListJson(UnitInfoListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\Areas\QueryStatisticsManage\Controllers\UnitInfoController.cs:line 72
   at lambda_method3112(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
 |url: http://localhost/QueryStatisticsManage/UnitInfo/GetSummaryListJson|action: GetSummaryListJson
2025-06-11 10:00:20.1918||DEBUG||程序启动 |ContentRootPath:D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web |WebRootPath:D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\wwwroot |IsDevelopment:True |url: |action: 
2025-06-11 10:42:21.4879||INFO||URL1:/OrganizationManage/Unit/UnitChildren |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-11 10:42:23.6328||INFO||URL1:/OrganizationManage/User/CountyMyUnitListIndex |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-11 10:42:53.0302||ERROR||Object reference not set to an instance of an object.
   at Dqy.Syjx.Web.Areas.OrganizationManage.Controllers.UnitController.UnitChildren() in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\Areas\OrganizationManage\Controllers\UnitController.cs:line 56
   at lambda_method946(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
 |url: http://localhost/OrganizationManage/Unit/UnitChildren|action: UnitChildren
2025-06-11 10:42:53.0302|1|ERROR|Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware|An unhandled exception has occurred while executing the request. System.NullReferenceException: Object reference not set to an instance of an object.
   at AspNetCore.Areas_OrganizationManage_Views_User_CountyMyUnitListIndex.ExecuteAsync() in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\Areas\OrganizationManage\Views\User\CountyMyUnitListIndex.cshtml:line 71
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ActionContext actionContext, IView view, ViewDataDictionary viewData, ITempDataDictionary tempData, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewResultExecutor.ExecuteAsync(ActionContext context, ViewResult result)
   at Microsoft.AspNetCore.Mvc.ViewResult.ExecuteResultAsync(ActionContext context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)|url: http://localhost/OrganizationManage/User/CountyMyUnitListIndex|action: CountyMyUnitListIndex
2025-06-11 11:18:48.5491||INFO||URL1:/QueryStatisticsManage/FunRoom/CountyInstitution |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-11 15:02:14.0305||DEBUG||程序启动 |ContentRootPath:D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web |WebRootPath:D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\wwwroot |IsDevelopment:True |url: |action: 
2025-06-11 15:03:09.4368||INFO||URL1:/ExperimentTeachManage/ExperimentBooking/ArrangeList |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-11 17:47:47.5704||DEBUG||程序启动 |ContentRootPath:D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web |WebRootPath:D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\wwwroot |IsDevelopment:True |url: |action: 
