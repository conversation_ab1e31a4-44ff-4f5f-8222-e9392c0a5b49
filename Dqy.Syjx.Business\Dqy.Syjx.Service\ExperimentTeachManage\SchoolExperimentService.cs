﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.ExperimentTeachManage;
using Dqy.Syjx.Model.Param.ExperimentTeachManage;
using Dqy.Syjx.Entity.BusinessManage;

namespace Dqy.Syjx.Service.ExperimentTeachManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2022-11-23 11:39
    /// 描 述：添加实验服务类
    /// </summary>
    public class SchoolExperimentService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<SchoolExperimentEntity>> GetList(SchoolExperimentListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<SchoolExperimentEntity>> GetPageList(SchoolExperimentListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = null;
            if (param.ActivityType == ActivityTypeEnum.OutOfClass.ParseToInt())
            {
                filter = ListOutFilter(param, strSql);
            }
            else
            {
                filter = ListFilter(param, strSql);
            }
            var list = await this.BaseRepository().FindList<SchoolExperimentEntity>(strSql.ToString(), filter.ToArray(), pagination);
            return list.ToList();
        }

        public async Task<SchoolExperimentEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<SchoolExperimentEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(SchoolExperimentEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(SchoolExperimentEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }
        public async Task UpdateTransForm(SchoolExperimentEntity entity, List<string> fields, Repository db = null)
        {
            if (fields != null)
            {
                await entity.Modify();
                fields.Add("BaseModifyTime");
                fields.Add("BaseModifierId");
            }
            if (db != null)
            {
                await db.Update(entity, fields);
            }
            else
            {
                await this.BaseRepository().Update(entity, fields);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update ex_SchoolExperiment set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
                strSql = $"update ex_SchoolExperiment set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<SchoolExperimentEntity, bool>> ListFilter(SchoolExperimentListParam param)
        {
            var expression = LinqExtensions.True<SchoolExperimentEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (param.SchoolId > 0)
                {
                    expression = expression.And(t => t.SchoolId == param.SchoolId);
                }
                if (param.SchoolStage > 0)
                {
                    expression = expression.And(t => t.SchoolStage == param.SchoolStage);
                }
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (param.CourseId > 0)
                {
                    expression = expression.And(t => t.CourseId ==param.CourseId);
                }
                if (param.GradeId > 0)
                {
                    expression = expression.And(t => t.GradeId == param.GradeId);
                }
                if (param.SchoolTerm > 0)
                {
                    expression = expression.And(t => t.SchoolTerm == param.SchoolTerm);
                }
                if (param.Statuz > 0)
                {
                    expression = expression.And(t => t.Statuz == param.Statuz);
                }
                if (param.ActivityType != -10000)
                {
                    expression = expression.And(t => t.ActivityType == param.ActivityType);
                }
            }
            return expression;
        }

        private List<DbParameter> ListFilter(SchoolExperimentListParam param, StringBuilder strSql)
        {
            strSql.Append($@"  SELECT * From (
                SELECT se1.* 
                ,dic2.DicName AS SchoolStageName
                ,dic3.DicName AS CourseName
                ,dic4.DicName AS GradeName
                ,s5.RealName AS CreateUser
                FROM  ex_SchoolExperiment AS se1
                INNER JOIN  sys_static_dictionary AS dic2 ON se1.SchoolStage = dic2.DictionaryId AND dic2.TypeCode = '{DicTypeCodeEnum.SchoolStage.ParseToInt().ToString()}' 
                INNER JOIN  sys_static_dictionary AS dic3 ON se1.CourseId = dic3.DictionaryId AND dic3.TypeCode = '{DicTypeCodeEnum.Course.ParseToInt().ToString()}' 
                INNER JOIN  sys_static_dictionary AS dic4 ON se1.GradeId = dic4.DictionaryId AND dic4.TypeCode = '{DicTypeCodeEnum.Grade.ParseToInt().ToString()}'  
                INNER JOIN  SysUser AS s5 ON se1.BaseCreatorId = s5.Id
            ) as tb1 WHERE   1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = @BaseIsDelete ");
                parameter.Add(DbParameterExtension.CreateDbParameter("@BaseIsDelete", param.BaseIsDelete));

                if (param.SchoolId > 0)
                {
                    strSql.Append(" AND SchoolId = @SchoolId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
                }
                if (param.IsNeedDo > 0)
                {
                    strSql.Append(" AND IsNeedDo = @IsNeedDo ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@IsNeedDo", param.IsNeedDo));
                }
                if (param.ExperimentType > 0)
                {
                    strSql.Append(" AND ExperimentType = @ExperimentType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ExperimentType", param.ExperimentType));
                }
                if (!string.IsNullOrEmpty(param.Name))
                {
                    //  OR EquipmentNeed like @Name OR MaterialNeed like @Name OR Remark like @Name
                    strSql.Append(" AND (ExperimentCode like @Name OR ExperimentName like @Name OR Chapter like @Name) ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Name", $"%{param.Name}%"));
                }

                if (param.GradeId > 0)
                {
                    strSql.Append(" AND GradeId = @GradeId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@GradeId", param.GradeId));
                }
                if (param.SchoolStage > 0)
                {
                    strSql.Append(" AND SchoolStage = @SchoolStage ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStage", param.SchoolStage));
                }
                if (param.CourseId > 0)
                {
                    strSql.Append(" AND CourseId = @CourseId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (param.SchoolTerm > 0)
                {
                    strSql.Append(" AND SchoolTerm = @SchoolTerm ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolTerm", param.SchoolTerm));
                }
                if (param.ActivityType != -10000)
                {
                    strSql.Append($" AND ActivityType = {param.ActivityType} "); //课程类型
                }
            }
            return parameter;
        }


        private List<DbParameter> ListOutFilter(SchoolExperimentListParam param, StringBuilder strSql)
        {
            strSql.Append($@"  SELECT * From (
                SELECT se1.* 
                ,dic3.DicName AS CourseName 
                ,s5.RealName AS CreateUser
                ,sdic.DictValue AS ExperimentTypeName
                FROM  ex_SchoolExperiment AS se1 
                INNER JOIN  sys_static_dictionary AS dic3 ON se1.CourseId = dic3.DictionaryId AND dic3.TypeCode = '{DicTypeCodeEnum.Course.ParseToInt().ToString()}'  
                INNER JOIN  SysUser AS s5 ON se1.BaseCreatorId = s5.Id
                LEFT JOIN SysDataDictDetail AS sdic ON dic3.BaseIsDelete = 0 AND sdic.TypeCode = 'OET101001' AND se1.SchoolId = sdic.UnitId AND se1.ExperimentType = sdic.DictKey
            ) as tb1 WHERE   1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = @BaseIsDelete ");
                parameter.Add(DbParameterExtension.CreateDbParameter("@BaseIsDelete", param.BaseIsDelete));

                if (param.Id > 0)
                {
                    strSql.Append(" AND Id = @Id ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Id", param.Id));
                }
                if (param.SchoolId > 0)
                {
                    strSql.Append(" AND SchoolId = @SchoolId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
                }
                if (param.ExperimentType > 0)
                {
                    strSql.Append(" AND ExperimentType = @ExperimentType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ExperimentType", param.ExperimentType));
                }
                if (!string.IsNullOrEmpty(param.Name))
                {
                    //  OR EquipmentNeed like @Name OR MaterialNeed like @Name OR Remark like @Name
                    strSql.Append(" AND (ExperimentCode like @Name OR ExperimentName like @Name OR Chapter like @Name) ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Name", $"%{param.Name}%"));
                }
                if (param.GradeId > 0)
                {
                    strSql.Append(" AND ApplicableGrade like @ApplicableGrade ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ApplicableGrade", $"%{param.GradeId}%"));
                }
                if (param.CourseId > 0)
                {
                    strSql.Append(" AND CourseId = @CourseId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (param.ActivityType != -10000)
                {
                    strSql.Append($" AND ActivityType = {param.ActivityType} "); //课程类型
                }
                if (param.Statuz != -10000 && param.Statuz != -1)
                {
                    strSql.Append(" AND Statuz = @Statuz ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Statuz", param.Statuz));
                }
                if (param.SubjectList!=null && param.SubjectList.Count > 0)
                {
                    strSql.Append($" AND ({string.Join(" OR ", param.SubjectList.Select(j => string.Format(" CourseId = {0} ", j)))}) ");
                }
            }
            return parameter;
        }

        public async Task<int> GetSchoolTermCourseNum(long schoolid,int courseid)
        {
            int total = 0;
            string strSql = $"  SELECT Count(Id) AS Num FROM ex_SchoolExperiment WHERE CourseId = {courseid} AND SchoolId = {schoolid} AND Statuz = 1 AND BaseIsDelete = 0 AND ActivityType = 2 ";
            var list = await this.BaseRepository().FindList<PlanInfoEntity>(strSql);
            if (list != null && list.Count() > 0)
            {
                total = list.FirstOrDefault().Num;
            }
            return total;
        }
        #endregion
    }
}
