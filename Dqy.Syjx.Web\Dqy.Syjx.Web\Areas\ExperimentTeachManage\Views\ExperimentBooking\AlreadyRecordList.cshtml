﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
    int SchoolYearStart = (int)ViewBag.SchoolYearStart;
    int SchoolTerm = (int)ViewBag.SchoolTerm;
}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .select-list li input {
        width: auto !important;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <span id="schoolYearStart" col="SchoolYearStart" style="display:inline-block;width:80px;"></span>
                    </li>
                    <li>
                        <span id="schoolTerm" col="SchoolTerm" style="display:inline-block;width:80px;"></span>
                    </li>
                    <li>
                        <span id="schoolGradeClassId" col="SchoolGradeClassId" style="display:inline-block;width:110px;"></span>
                    </li>
                    <li>
                        <span id="courseId" col="CourseId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <span id="sourceType" col="SourceType" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <span id="experimentType" col="ExperimentType" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <div id="IsNeedDo" col="IsNeedDo" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <input id="experimentName" col="ExperimentName" placeholder="实验名称" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-default btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group-sm hidden-xs" id="toolbar">
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    $(function () {
        loadSearchCombo();
        loadIsNeedDo();
        initGrid();
        
        
    });
    function loadIsNeedDo() {
        $("#IsNeedDo").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(IsNeedEnum).EnumToDictionaryString())), defaultName: '实验要求' });
    }
    function initGrid() {
        var queryUrl = '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/GetAlreadyRecordList")';
        $('#gridTable').ysTable({
            url: queryUrl,
            columns: [
                {
                    field: '', title: '操作', halign: 'center', align: 'center' ,width: 120 ,
                    formatter: function (value, row, index) {
                        var html = $.Format('<a class="btn btn-info btn-xs" href="#" onclick="look(this)" value="{0}"><i class="fa fa-eye"></i>查看</a> ', row.Id);
                        if (row.SchoolYearStart == @SchoolYearStart && row.SchoolTerm == @SchoolTerm) {
                            //过了当前学期的已登记数据，无法撤回
                            html += $.Format('<a class="btn btn-warning btn-xs" href="#" onclick="withdraw(this)" value="{0}"><i class="fa fa-reply"></i>撤回</a> ', row.Id);
                        }
                        return html;
                    }
                },
                {
                    field: 'SchoolTerm', title: '学期', sortable: true, halign: 'center', align: 'center', width: 100,
                    formatter: function (value, row, index) {
                        return (row.SchoolYearStart + '').substr(2) + '~' + (row.SchoolYearEnd + '').substr(2)
                            + (value == @SchoolTermEnum.LastSemester.ParseToInt()
                            ? "@SchoolTermEnum.LastSemester.GetDescription()"
                            : "@SchoolTermEnum.NextSemester.GetDescription()");
                    }
                },
                /*{ field: 'GradeName', title: '年级', sortable: true, halign: 'center', align: 'center', width: 60 },*/
                {
                    field: 'ClassName', title: '上课班级', sortable: false, halign: 'center', align: 'center', width: 120
                },
                { field: 'CourseName', title: '学科', sortable: true, halign: 'center', align: 'center', width: 50 },
                {
                    field: 'SourceType', title: '实验来源', sortable: true, halign: 'center', align: 'center', width: 50,
                    formatter: function (value, row, index) {
                        switch (value) {
                            case @SourceTypeEnum.ExperimentPlan.ParseToInt():
                                return "@SourceTypeEnum.ExperimentPlan.GetDescription()";
                             case @SourceTypeEnum.ExperimentList.ParseToInt():
                                return "@SourceTypeEnum.ExperimentList.GetDescription()";
                            default:
                                return '实验计划/实验目录';
                        }
                    }
                },
                { field: 'ExperimentName', title: '实验名称', sortable: true, halign: 'center', align: 'left', width: 140 },
                {
                    field: 'ExperimentType', title: '实验类型', sortable: true, halign: 'center', align: 'center', width: 50,
                    formatter: function (value, row, index) {
                        switch (value) {
                            case @ExperimentTypeEnum.Demo.ParseToInt():
                                return "@ExperimentTypeEnum.Demo.GetDescription()";
                             case @ExperimentTypeEnum.Group.ParseToInt():
                                return "@ExperimentTypeEnum.Group.GetDescription()";
                            default:
                                return '演示/分组';
                        }
                    }
                },
                { field: 'Groupz', title: '分组数', sortable: true, halign: 'center', align: 'center', width: 50 },
                {
                    field: 'IsNeedDo', title: '实验要求', sortable: true, halign: 'center', align: 'center', width: 50,
                    formatter: function (value, row, index) {
                        var html = '@IsNeedEnum.MustDo.GetDescription()';
                        if (@IsNeedEnum.SelectToDo.ParseToInt()== value) {
                            html = '@IsNeedEnum.SelectToDo.GetDescription()';
                        }
                        return html;
                    }
                },
                {
                    field: 'ClassTime', title: '上课时间', sortable: true, halign: 'center', align: 'center', width: 80,
                    formatter: function (value, row, index) {
                        return ys.formatDate(value, "yyyy-MM-dd");
                    }
                },
                { field: 'SectionShow', title: '上课节次', sortable: true, halign: 'center', align: 'center', width: 140 },
                { field: 'FunRoomName', title: '上课地点', sortable: true, halign: 'center', align: 'left', width: 140 },
                { field: 'BookUserName', title: '预约人', sortable: true, halign: 'center', align: 'center', width: 80 },
                {
                    field: 'Statuz', title: '实验状态', sortable: true, halign: 'center', align: 'center', width: 100,
                    formatter: function (value, row, index) {
                        switch (value) {
                            case @ExperimentBookStatuzEnum.BookIng.ParseToInt():
                                return "@ExperimentBookStatuzEnum.BookIng.GetDescription()";
                            case @ExperimentBookStatuzEnum.WaitArrange.ParseToInt():
                                return "@ExperimentBookStatuzEnum.WaitArrange.GetDescription()";
                            case @ExperimentBookStatuzEnum.ArrangeNoPass.ParseToInt():
                                return '<span style="color:red;">@ExperimentBookStatuzEnum.ArrangeNoPass.GetDescription()</span>';
                            case @ExperimentBookStatuzEnum.WaitRecord.ParseToInt():
                                return "@ExperimentBookStatuzEnum.WaitRecord.GetDescription()";
                            case @ExperimentBookStatuzEnum.AlreadyRecord.ParseToInt():
                                return "@ExperimentBookStatuzEnum.AlreadyRecord.GetDescription()";
                        }
                    }
                }
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function resetGrid() {
        //清空条件
        $('#schoolYearStart').ysComboBox('setValue', -1);
        $('#schoolTerm').ysComboBox('setValue', -1);
        $('#schoolGradeClassId').ysComboBox('setValue', -1);
        $('#courseId').ysComboBox('setValue', -1);
        $('#sourceType').ysComboBox('setValue', -1);
        $('#experimentType').ysComboBox('setValue', -1);
        $('#experimentName').val('');
        $('#IsNeedDo').ysComboBox('setValue', -1);
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function withdraw(obj) {
        var id = $(obj).attr('value');
        ys.confirm('确认要撤回吗？', function () {
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/ArrangeRecordWithdrawJson")' + '?Id=' + id + '&WithdrawType=2',
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }

    function loadSearchCombo() {
        ComBox.SchoolTermYear($('#schoolYearStart'), undefined, '学年');
        $("#schoolTerm").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(SchoolTermEnum).EnumToDictionaryString())), defaultName: '学期' });
        $('#courseId').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildToListJson")' + '?TypeCode=@DicTypeCodeEnum.Course.ParseToInt()&Ids=1005002,1005003,1005004,1005005',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '学科'
        });
        $("#sourceType").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(SourceTypeEnum).EnumToDictionaryString())), defaultName: '实验来源' });
        $("#experimentType").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(ExperimentTypeEnum).EnumToDictionaryString())), defaultName: '实验类型' });
        $("#statuz").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(ExperimentBookStatuzEnum).EnumToDictionaryString())), defaultName: '实验状态' });
        ys.ajax({
            url: '@Url.Content("~/OrganizationManage/SchoolGradeClass/GetListJson")' + '?IsGraduate=0',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    $('#schoolGradeClassId').ysComboBox({
                        data: obj.Data,
                        key: 'Id',
                        value: 'ClassName',
                        defaultName: '班级'
                    });
                }
            }
        });
    }

    function look(obj) {
        var id = $(obj).attr("value");
        var url = '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/Detail")' + '?id=' + id;
        createMenuItem(url, "实验查看");
    }
</script>
