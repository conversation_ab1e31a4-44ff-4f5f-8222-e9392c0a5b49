﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.ExperimentTeachManage;
using Dqy.Syjx.Model.Param.ExperimentTeachManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Enum.ExperimentTeachManage;

namespace Dqy.Syjx.Service.ExperimentTeachManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-12-08 17:19
    /// 描 述：编制实验计划服务类
    /// </summary>
    public class PlanDetailService : RepositoryFactory
    {
        #region 获取数据
        public async Task<List<PlanDetailEntity>> GetList(PlanDetailListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.Tousing System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.ExperimentTeachManage;
using Dqy.Syjx.Model.Param.ExperimentTeachManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Enum.ExperimentTeachManage;

namespace Dqy.Syjx.Service.ExperimentTeachManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-12-08 17:19
    /// 描 述：编制实验计划服务类
    /// </summary>
    public class PlanDetailService : RepositoryFactory
    {
        #region 获取数据
        public async Task<List<PlanDetailEntity>> GetList(PlanDetailListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<PlanDetailEntity>> GetPageList(PlanDetailListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ListFilter(param, strSql);
            var list = await this.BaseRepository().FindList<PlanDetailEntity>(strSql.ToString(), filter.ToArray(), pagination);
            return list.ToList();
        }
        public async Task<List<PlanDetailEntity>> GetPageBookingList(PlanDetailListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ListBookingFilter(param, strSql);
            var list = await this.BaseRepository().FindList<PlanDetailEntity>(strSql.ToString(), filter.ToArray(), pagination);
            return list.ToList();
        }
        public async Task<List<TextbookVersionDetailEntity>> GetVersionPageList(PlanDetailListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ListVersionFilter(param, strSql);
            var list = await this.BaseRepository().FindList<TextbookVersionDetailEntity>(strSql.ToString(), filter.ToArray(), pagination);
            return list.ToList();
        }

        public async Task<PlanDetailEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<PlanDetailEntity>(id);
        }

        public async Task<List<TextbookVersionBaseEntity>> GetVersionSelectList(long planinfoid)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append($@"  SELECT * From (
                SELECT
                vb3.Id ,
                vb3.VersionName 
                FROM  ex_PlanDetail AS pd1    
                INNER JOIN  ex_PlanInfo AS pi12 ON pd1.PlanInfoId = pi12.Id AND pi12.BaseIsDelete = 0  
                INNER JOIN  ex_TextbookVersionBase AS vb3 ON vb3.BaseIsDelete = 0 AND pd1.TextbookVersionBaseId = vb3.Id AND vb3.Statuz = {StatusEnum.Yes.ParseToInt()}
                WHERE pd1.BaseIsDelete = 0  AND pd1.PlanInfoId = {planinfoid} GROUP BY vb3.Id,vb3.VersionName  
            ) as tb1 ");
            var list = await this.BaseRepository().FindList<TextbookVersionBaseEntity>(strSql.ToString());
            return list.ToList();
        }

        public async Task<List<TextbookVersionBaseEntity>> GetPlanVersionSelectList(PlanDetailListParam param)
        {
            StringBuilder strSql = new StringBuilder();
            string classzWhere = "";
            if (param.GradeId > 0 && param.SchoolStage == SchoolStageEnum.GaoZhong.ParseToInt())
            {
                if (param.SchoolGradeClassIdz != null && param.SchoolGradeClassIdz.Length > 0)
                {
                    string tempWhere = "";
                    var classArr = param.SchoolGradeClassIdz.Split(",");
                    if (classArr != null && classArr.Length > 0)
                    {
                        for (int i = 0; i < classArr.Length; i++)
                        {
                            if (i > 0)
                            {
                                tempWhere += " AND ";
                            }
                            tempWhere += string.Format(" pi12.ClassIdz like '%{0}%'  ", classArr[i]);
                        }
                    }
                    classzWhere = $" AND ({tempWhere}) ";
                }
            }
            strSql.Append($@"  SELECT * From (
                SELECT
                vb3.Id ,
                vb3.VersionName 
                FROM  ex_PlanDetail AS pd1    
                INNER JOIN  ex_PlanInfo AS pi12 ON pd1.PlanInfoId = pi12.Id AND pi12.BaseIsDelete = 0  
                INNER JOIN  ex_TextbookVersionBase AS vb3 ON vb3.BaseIsDelete = 0 AND pd1.TextbookVersionBaseId = vb3.Id AND vb3.Statuz = {StatusEnum.Yes.ParseToInt()}
                WHERE pd1.BaseIsDelete = 0  
                        AND  pi12.SchoolId = {param.SchoolId} 
				        AND   pi12.GradeId = {param.GradeId} 
				        AND   pi12.CourseId = {param.CourseId} 
				        AND   pi12.SchoolYearStart = {param.SchoolYearStart} 
				        AND   pi12.SchoolTerm = {param.SchoolTerm} 
				        {classzWhere}
                GROUP BY vb3.Id,vb3.VersionName  
            ) as tb1 ");
            var list = await this.BaseRepository().FindList<TextbookVersionBaseEntity>(strSql.ToString());
            return list.ToList();
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(PlanDetailEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(PlanDetailEntity entity, Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update ex_PlanDetail set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
                strSql = $"update ex_PlanDetail set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }

        public async Task DeleteByPlanInfoId(long id)
        {
            string strSql = $"update ex_PlanDetail set BaseIsDelete = 1 where PlanInfoId = {id}";
            
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<PlanDetailEntity, bool>> ListFilter(PlanDetailListParam param)
        {
            var expression = LinqExtensions.True<PlanDetailEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (param.PlanInfoId > 0)
                {
                    expression = expression.And(t => t.PlanInfoId == param.PlanInfoId);
                }
                if (!string.IsNullOrEmpty(param.SchoolExperimentIds))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.SchoolExperimentIds, ',');
                    expression = expression.And(t => idArr.Contains(t.SchoolExperimentId.Value));
                }
            }
            return expression;
        }
        private List<DbParameter> ListFilter(PlanDetailListParam param, StringBuilder strSql)
        {
            strSql.Append($@"  SELECT * From (
                SELECT
                pd1.Id ,
                pd1.BaseIsDelete ,
                pd1.BaseCreateTime ,
                pd1.BaseModifyTime ,
                pd1.BaseCreatorId ,
                pd1.BaseModifierId ,
                pd1.BaseVersion ,
                pd1.PlanInfoId ,
                pd1.TextbookVersionCurrentId ,
                pd1.TextbookVersionBaseId ,
                (CASE WHEN pd1.TextbookVersionBaseId = 0 THEN '校本实验' ELSE vb3.VersionName END) AS VersionBaseName ,
                pd1.TextbookVersionDetailId ,
                (CASE WHEN pd1.TextbookVersionDetailId = 0 THEN se21.Chapter ELSE vd4.Chapter END) AS Chapter ,
                (CASE WHEN pd1.TextbookVersionDetailId = 0 THEN 0 ELSE vd4.ChapterSort END) AS ChapterSort ,
                pd1.ExperimentName ,
                pd1.ExperimentType ,
                pd1.IsNeedDo ,
                pd1.WeekNum ,
				pi12.SchoolId ,
				pi12.GradeId ,
				pi12.CourseId ,
				pi12.SchoolYearStart ,
				pi12.SchoolYearEnd ,
				pi12.SchoolTerm ,
				pi12.PlanName ,
				pi12.Num ,
				pi12.ClassIdz ,
                vd4.Remark ,
                ISNULL(vd4.IsEvaluate,0)  AS IsEvaluate ,
                pd1.SchoolExperimentId ,
                ISNULL(st.FirstWeekDate,GETDATE()) AS  FirstWeekDate ,
                ISNULL(st.TermEnd,GETDATE()) AS  TermEnd ,
                CASE WHEN ISNULL(tab01.Id,0) > 0 THEN 1 ELSE 2 END AS IsExam
                FROM  ex_PlanDetail AS pd1    
                INNER JOIN  ex_PlanInfo AS pi12 ON pd1.PlanInfoId = pi12.Id AND pi12.BaseIsDelete = 0  
                LEFT JOIN  ex_TextbookVersionCurrent AS vc2 ON vc2.BaseIsDelete = 0 AND pd1.TextbookVersionCurrentId = vc2.Id  AND vc2.Statuz = {StatusEnum.Yes.ParseToInt()}
                LEFT JOIN  ex_TextbookVersionBase AS vb3 ON vb3.BaseIsDelete = 0 AND pd1.TextbookVersionBaseId = vb3.Id AND vb3.Statuz = {StatusEnum.Yes.ParseToInt()}
                LEFT JOIN  ex_TextbookVersionDetail AS vd4 ON vd4.BaseIsDelete = 0 AND pd1.TextbookVersionDetailId = vd4.Id AND vd4.Statuz = {StatusEnum.Yes.ParseToInt()}
                LEFT JOIN  ex_SchoolExperiment AS se21 ON pi12.SchoolId = se21.SchoolId AND se21.Id = pd1.SchoolExperimentId
                LEFT JOIN  bn_SchoolTerm AS st ON st.BaseIsDelete = 0 AND pi12.SchoolYearStart = st.SchoolYearStart AND pi12.SchoolTerm = st.SchoolTerm
                LEFT JOIN (
                SELECT pp1.* ,u3.Id AS SchoolId , u3.Name AS SchoolName ,u3.Sort
                ,pe2.ExperimentId 
                FROM ex_PlanExamParameter AS pp1
                INNER JOIN up_UnitRelation AS ur2 ON ur2.BaseIsDelete = 0 AND pp1.UnitId = ur2.UnitId AND ur2.ExtensionType = 3
                INNER JOIN up_Unit AS u3 ON u3.BaseIsDelete = 0 AND u3.Id = ur2.ExtensionObjId
                INNER JOIN up_SchoolExtension AS se11 ON se11.BaseIsDelete = 0 AND se11.UnitId = u3.Id
				INNER JOIN sys_dictionary_relation AS dicr12 ON dicr12.BaseIsDelete = 0 
                                                        AND dicr12.DictionaryId = se11.SchoolProp 
                                                        AND dicr12.DictionaryToId = pp1.SchoolStage 
                                                        AND dicr12.RelationType = 1 
                INNER JOIN ex_PlanExamExperiment AS pe2 ON pe2.BaseIsDelete = 0 AND pp1.Id = pe2.PlanParameterSetId
             ) AS tab01 ON tab01.BaseIsDelete = 0  
                    AND  tab01.SchoolId = pi12.SchoolId
                    AND tab01.SchoolYearStart = pi12.SchoolYearStart
                    AND tab01.SchoolTerm = pi12.SchoolTerm
                    AND  tab01.ExperimentId = pd1.ExperimentId 
                    AND tab01.CourseId = pi12.CourseId 
                    AND tab01.GradeId = pi12.GradeId 
                    AND ISNULL(tab01.CompulsoryType,0) = ISNULL(pi12.CompulsoryType,0)
            ) as tb1 WHERE   BaseIsDelete = 0 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.OptType == 1)
                {
                    strSql.Append(" AND TextbookVersionDetailId = @TextbookVersionDetailId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@TextbookVersionDetailId", param.TextbookVersionDetailId));

                    strSql.Append(" AND SchoolId = @SchoolId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.UnitId));
                }
                else if (param.OptType == 2)
                {
                    strSql.Append(" AND SchoolExperimentId = @SchoolExperimentId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolExperimentId", param.SchoolExperimentId));

                    strSql.Append(" AND SchoolId = @SchoolId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.UnitId));
                }
                else
                {
                    if (param.PlanInfoId > 0)
                    {
                        strSql.Append(" AND PlanInfoId = @PlanInfoId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@PlanInfoId", param.PlanInfoId));
                    }
                    if (param.UnitType == UnitTypeEnum.School.ParseToInt())
                    {
                        strSql.Append(" AND SchoolId = @UnitId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                    }
                    else if (param.UnitType == UnitTypeEnum.County.ParseToInt())
                    {
                        //strSql.Append(" AND CountyId = @CountyId ");
                        //parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.UnitId));
                        if (param.SchoolId > 0)
                        {
                            strSql.Append(" AND SchoolId = @UnitId ");
                            parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.SchoolId));
                        }
                    }
                    else if (param.UnitType == UnitTypeEnum.City.ParseToInt() || param.UnitType == UnitTypeEnum.System.ParseToInt())
                    {
                    }
                    else
                    {
                        strSql.Append(" AND 1 <> 1 ");
                    }
                    if (param.IsNeedDo > 0)
                    {
                        strSql.Append(" AND IsNeedDo = @IsNeedDo ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@IsNeedDo", param.IsNeedDo));
                    }
                    if (param.ExperimentType > 0)
                    {
                        strSql.Append(" AND ExperimentType = @ExperimentType ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@ExperimentType", param.ExperimentType));
                    }
                    if (param.TextbookVersionBaseId >= 0)
                    {
                        strSql.Append(" AND TextbookVersionBaseId = @TextbookVersionBaseId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@TextbookVersionBaseId", param.TextbookVersionBaseId));
                    }
                    if (!string.IsNullOrEmpty(param.Name))
                    {
                        strSql.Append(" AND (VersionBaseName like @Name OR ExperimentName like @Name OR Chapter like @Name ) ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@Name", $"%{param.Name}%"));
                    }

                    if (param.GradeId > 0)
                    {
                        strSql.Append(" AND GradeId = @GradeId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@GradeId", param.GradeId));

                        if (param.SchoolStage == SchoolStageEnum.GaoZhong.ParseToInt())
                        {
                            strSql.Append($" AND ClassIdz like '%{ param.SchoolGradeClassId}%' "); 
                        }
                    }
                    if (param.CourseId > 0)
                    {
                        strSql.Append(" AND CourseId = @CourseId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                    }
                    if (param.SchoolTerm > 0)
                    {
                        strSql.Append(" AND SchoolTerm = @SchoolTerm ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolTerm", param.SchoolTerm));
                    }
                    if (param.SchoolYearStart > 0)
                    {
                        strSql.Append(" AND SchoolYearStart = @SchoolYearStart ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolYearStart", param.SchoolYearStart));
                    }
                    if (param.SchoolYearEnd > 0)
                    {
                        strSql.Append(" AND SchoolYearEnd = @SchoolYearEnd ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolYearEnd", param.SchoolYearEnd));
                    }
                    if (param.IsEvaluate ==2 || param.IsEvaluate==1)
                    {
                        strSql.Append(" AND IsEvaluate = @IsEvaluate ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@IsEvaluate", param.IsEvaluate));
                    }
                    if (param.IsExam == 1 || param.IsExam == 2)
                    {
                        strSql.Append(" AND IsExam = @IsExam ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@IsExam", param.IsExam));
                    }
                }

                if (param.ActivityType != -10000)
                {
                    strSql.Append(" AND ActivityType = @ActivityType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ActivityType", param.ActivityType));
                }
            }
            return parameter;
        }

        private List<DbParameter> ListVersionFilter(PlanDetailListParam param, StringBuilder strSql)
        {
            strSql.Append($@"  SELECT * From (
                SELECT 
				vd2.Id ,
				vd2.BaseIsDelete ,
				vd2.BaseCreateTime ,
				vd2.BaseModifyTime ,
				vd2.BaseCreatorId ,
				vd2.BaseModifierId ,
				vd2.BaseVersion ,
				vd2.TextbookVersionBaseId ,
				vd2.Chapter ,
				vd2.ExperimentCode ,
				vd2.ExperimentName ,
				vd2.ExperimentType ,
				vd2.IsNeedDo ,
				vd2.IsEvaluate ,
				vd2.IsBase ,
				vd2.EquipmentNeed ,
				vd2.MaterialNeed ,
				vd2.Remark ,
				vd2.Statuz ,
                vd2.ChapterSort,
				vb1.VersionName ,
                CASE WHEN ISNULL(vb1.CompulsoryType ,0) = {TextbookCompulsoryTypeEnum.Must.ParseToInt()} Then {TextbookCompulsoryTypeEnum.Must.ParseToInt()} Else {TextbookCompulsoryTypeEnum.NonSelectMust.ParseToInt()} end CompulsoryType,
				vc3.SchoolStage ,
				vc3.GradeId ,
				vc3.CourseId ,
				vc3.SchoolTerm ,
                vc3.Id AS TextbookVersionCurrentId ,
				pd4.PlanInfoId  ,
                ISNULL(pi5.ClassIdz,'') as ClassIdz ,
                ISNULL(pd4.WeekNum,0) AS WeekNum
	            ,{SourcePathEnum.Version.ParseToInt()}  AS SourcePath
		        FROM  ex_TextbookVersionDetail AS vd2
		        INNER JOIN  ex_TextbookVersionBase AS vb1 ON vd2.TextbookVersionBaseId = vb1.Id AND vb1.BaseIsDelete = 0 AND vb1.Statuz = {StatusEnum.Yes.ParseToInt()}
				INNER JOIN   ex_TextbookVersionCurrent AS vc3 ON vb1.Id = vc3.TextbookVersionBaseId AND vc3.BaseIsDelete = 0 AND vc3.Statuz = {StatusEnum.Yes.ParseToInt()}
				LEFT JOIN  ex_PlanDetail AS pd4 ON pd4.PlanInfoId = {param.PlanInfoId}  AND pd4.BaseIsDelete = 0 AND pd4.TextbookVersionDetailId = vd2.Id 
                LEFT JOIN  ex_PlanInfo AS pi5 ON pd4.PlanInfoId = pi5.Id  AND pi5.BaseIsDelete = 0 	        
                WHERE vd2.Statuz = {StatusEnum.Yes.ParseToInt()} AND vc3.CountyIdz like '%{param.CountyId}%' 
                UNION    
                SELECT 
				se21.Id ,se21.BaseIsDelete ,se21.BaseCreateTime ,se21.BaseModifyTime ,se21.BaseCreatorId ,se21.BaseModifierId ,se21.BaseVersion 
				,0 AS TextbookVersionBaseId 
				,se21.Chapter ,se21.ExperimentCode ,se21.ExperimentName ,se21.ExperimentType ,se21.IsNeedDo
				, -1 AS IsEvaluate 
				, -1 AS IsBase 
				 ,se21.EquipmentNeed ,se21.MaterialNeed 
				,se21.Remark ,se21.Statuz
				,0 AS ChapterSort
				,'校本实验' AS VersionName
                , -1 CompulsoryType
				,se21.SchoolStage ,se21.GradeId ,se21.CourseId ,se21.SchoolTerm
				,0 AS TextbookVersionCurrentId
                 ,pd22.PlanInfoId 
                ,ISNULL(pd23.ClassIdz,'') as ClassIdz 
                ,ISNULL(pd22.WeekNum,0) AS WeekNum
                , {SourcePathEnum.School.ParseToInt()} AS SourcePath
				FROM  ex_SchoolExperiment  AS se21
				LEFT JOIN  ex_PlanDetail AS pd22 ON pd22.PlanInfoId =  {param.PlanInfoId} AND pd22.BaseIsDelete = 0 AND pd22.SourcePath = {SourcePathEnum.School.ParseToInt()} AND se21.Id = pd22.SchoolExperimentId 
				LEFT JOIN  ex_PlanInfo AS pd23 ON pd22.PlanInfoId = pd23.Id  AND pd23.BaseIsDelete = 0
                WHERE  se21.SchoolId = {param.UnitId} AND se21.Statuz = {StatusEnum.Yes.ParseToInt()} 
            ) as tb1 WHERE   1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = @BaseIsDelete ");
                parameter.Add(DbParameterExtension.CreateDbParameter("@BaseIsDelete", param.BaseIsDelete));
                if (param.TextbookVersionBaseId > 0)
                {
                    if (param.TextbookVersionBaseId == 99)
                    {
                        strSql.Append(" AND SourcePath = @SourcePath ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@SourcePath", SourcePathEnum.School.ParseToInt()));
                    }
                    else
                    {
                        strSql.Append(" AND TextbookVersionCurrentId = @TextbookVersionBaseId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@TextbookVersionBaseId", param.TextbookVersionBaseId ?? 0));
                    }
                } 
                if (param.IsNeedDo > 0)
                {
                    strSql.Append(" AND IsNeedDo = @IsNeedDo ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@IsNeedDo", param.IsNeedDo));
                }
                if (param.IsEvaluate ==IsStatusEnum.Yes.ParseToInt() || param.IsEvaluate == IsStatusEnum.No.ParseToInt())
                {
                    if (param.IsEvaluate == IsStatusEnum.Yes.ParseToInt())
                    {
                        strSql.Append($" AND IsEvaluate = {IsStatusEnum.Yes.ParseToInt()} ");
                    }
                    else
                    {
                        strSql.Append($" AND IsEvaluate <> {IsStatusEnum.Yes.ParseToInt()} ");
                    }
                }
                if (param.ExperimentType > 0)
                {
                    strSql.Append(" AND ExperimentType = @ExperimentType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ExperimentType", param.ExperimentType));
                }
                if (!string.IsNullOrEmpty(param.Name))
                {
                    strSql.Append(" AND (ExperimentName like @Name OR Chapter like @Name ) ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Name", $"%{param.Name}%"));
                }
                if (param.Ids != null && param.Ids.Length > 0)
                {
                    strSql.Append($" AND Id IN ({param.Ids})");
                }
                /*下面是必要条件。*/
                if (param.SchoolStage > 0)
                {
                    strSql.Append(" AND SchoolStage = @SchoolStage ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStage", param.SchoolStage));
                }
                if (param.GradeId > 0)
                {
                    strSql.Append(" AND GradeId = @GradeId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@GradeId", param.GradeId));
                }
                if (param.CourseId > 0)
                {
                    strSql.Append(" AND CourseId = @CourseId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (param.SchoolTerm > 0)
                {
                    strSql.Append(" AND SchoolTerm = @SchoolTerm ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolTerm", param.SchoolTerm));
                }
                if (param.CompulsoryType > 0)
                {
                    //校本实验，选修科目、非选修科目都可以添加
                    strSql.Append(" AND (CompulsoryType = @CompulsoryType  OR CompulsoryType = -1) ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CompulsoryType", param.CompulsoryType));
                }
            }
            return parameter;
        }
        /// <summary>
        /// 实验预约登记，计划选择。
        /// </summary>
        /// <param name="param"></param>
        /// <param name="strSql"></param>
        /// <returns></returns>
        private List<DbParameter> ListBookingFilter(PlanDetailListParam param, StringBuilder strSql)
        {
            strSql.Append($@"  SELECT * From (
                SELECT
                pd1.Id ,
                pd1.BaseIsDelete ,
                pd1.BaseCreateTime ,
                pd1.BaseModifyTime ,
                pd1.BaseCreatorId ,
                pd1.BaseModifierId ,
                pd1.BaseVersion ,
                pd1.PlanInfoId ,
                pd1.TextbookVersionCurrentId ,
                pd1.TextbookVersionBaseId ,
                (CASE WHEN pd1.TextbookVersionBaseId = 0 THEN '校本实验' ELSE vb3.VersionName END) AS VersionBaseName ,
                pd1.TextbookVersionDetailId ,
                (CASE WHEN pd1.TextbookVersionDetailId = 0 THEN '' ELSE vd4.Chapter END) AS Chapter ,
                pd1.ExperimentName ,
                pd1.ExperimentType ,
                pd1.IsNeedDo ,
                pd1.WeekNum ,
				pi12.SchoolId ,
				pi12.GradeId ,
				pi12.CourseId ,
                sd22.DictionaryId AS SchoolStage,
				pi12.SchoolYearStart ,
				pi12.SchoolYearEnd ,
				pi12.SchoolTerm ,
				pi12.PlanName ,
				pi12.Num ,
				pi12.ClassIdz ,
                CASE WHEN eb4.Id > 0 THEN 1 ELSE 0 END  AS IsSelected ,
                vd4.EquipmentNeed ,
                vd4.MaterialNeed
                ,pd1.SourcePath
                ,pd1.SchoolExperimentId
                FROM  ex_PlanDetail AS pd1    
                INNER JOIN  ex_PlanInfo AS pi12 ON pd1.PlanInfoId = pi12.Id AND pi12.BaseIsDelete = 0  
                INNER JOIN  sys_dictionary_relation AS dr21 ON pi12.GradeId = dr21.DictionaryToId
                INNER JOIN  sys_static_dictionary AS sd22 ON dr21.DictionaryId = sd22.DictionaryId AND sd22.TypeCode= '{DicTypeCodeEnum.SchoolStage.ParseToInt()}'
                LEFT JOIN  ex_TextbookVersionCurrent AS vc2 ON vc2.BaseIsDelete = 0 AND pd1.TextbookVersionCurrentId = vc2.Id
                LEFT JOIN  ex_TextbookVersionBase AS vb3 ON vb3.BaseIsDelete = 0 AND pd1.TextbookVersionBaseId = vb3.Id
                LEFT JOIN  ex_TextbookVersionDetail AS vd4 ON vd4.BaseIsDelete = 0 AND pd1.TextbookVersionDetailId = vd4.Id
                LEFT JOIN  ex_ExperimentBooking AS eb4 ON eb4.BaseIsDelete = 0 AND eb4.SchoolId = {param.SchoolId} 
                                                        AND (pd1.Id = eb4.PlanDetailId AND  eb4.SchoolGradeClassId =  {param.SchoolGradeClassId} ) 
            ) as tb1 WHERE   1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = @BaseIsDelete ");
                parameter.Add(DbParameterExtension.CreateDbParameter("@BaseIsDelete", param.BaseIsDelete));
                if (param.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    strSql.Append(" AND SchoolId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                else if (param.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    if (param.SchoolId > 0)
                    {
                        strSql.Append(" AND SchoolId = @UnitId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.SchoolId));
                    }
                }
                else if (param.UnitType == UnitTypeEnum.City.ParseToInt() || param.UnitType == UnitTypeEnum.System.ParseToInt())
                {
                }
                else
                {
                    strSql.Append(" AND 1 <> 1 ");
                }
                if (param.IsNeedDo > 0)
                {
                    strSql.Append(" AND IsNeedDo = @IsNeedDo ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@IsNeedDo", param.IsNeedDo));
                }
                if (param.ExperimentType > 0)
                {
                    strSql.Append(" AND ExperimentType = @ExperimentType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ExperimentType", param.ExperimentType));
                }
                if (param.TextbookVersionBaseId >= 0)
                {
                    strSql.Append(" AND TextbookVersionBaseId = @TextbookVersionBaseId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@TextbookVersionBaseId", param.TextbookVersionBaseId));
                }
                if (!string.IsNullOrEmpty(param.Name))
                {
                    strSql.Append(" AND (VersionBaseName like @Name OR ExperimentName like @Name OR Chapter like @Name ) ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Name", $"%{param.Name}%"));
                }
                if (param.SchoolStage > 0)
                {
                    strSql.Append(" AND SchoolStage = @SchoolStage ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStage", param.SchoolStage)); 
                }
                if (param.GradeId > 0)
                {
                    strSql.Append(" AND GradeId = @GradeId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@GradeId", param.GradeId));

                    if (param.SchoolStage == SchoolStageEnum.GaoZhong.ParseToInt())
                    {
                        if (param.SchoolGradeClassIdz!=null && param.SchoolGradeClassIdz.Length > 0)
                        {
                            string tempWhere = "";
                            var classArr = param.SchoolGradeClassIdz.Split(",");
                            if (classArr !=null && classArr.Length > 0)
                            {
                                for (int i = 0; i < classArr.Length; i++)
                                {
                                    if (i>0)
                                    {
                                        tempWhere += " AND ";
                                    }
                                    tempWhere += string.Format(" ClassIdz like '%{0}%'  ", classArr[i]);
                                }
                            }
                            strSql.Append($" AND ({tempWhere}) ");
                        }
                        
                    }
                }
                if (param.CourseId > 0)
                {
                    strSql.Append(" AND CourseId = @CourseId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (param.SchoolTerm > 0)
                {
                    strSql.Append(" AND SchoolTerm = @SchoolTerm ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolTerm", param.SchoolTerm));
                }
                if (param.SchoolYearStart > 0)
                {
                    strSql.Append(" AND SchoolYearStart = @SchoolYearStart ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolYearStart", param.SchoolYearStart));
                }
                if (param.SchoolYearEnd > 0)
                {
                    strSql.Append(" AND SchoolYearEnd = @SchoolYearEnd ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolYearEnd", param.SchoolYearEnd));
                }
            }
            return parameter;
        }

        #endregion

        #region 查询统计

        /// <summary>
        /// 实验预约登记，计划选择。
        /// </summary>
        /// <param name="param"></param>
        /// <param name="strSql"></param>
        /// <returns></returns>
        public async Task<List<PlanDetailEntity>> GetListBookingFilter(PlanDetailListParam param)
        {
            StringBuilder strSql= new StringBuilder();
            strSql.Append($@"  SELECT * From (
                SELECT
                pd1.Id ,
                pd1.BaseIsDelete ,
                pd1.BaseCreateTime ,
                pd1.BaseModifyTime ,
                pd1.BaseCreatorId ,
                pd1.BaseModifierId ,
                pd1.BaseVersion ,
                pd1.PlanInfoId ,
                pd1.TextbookVersionCurrentId ,
                pd1.TextbookVersionBaseId ,
                (CASE WHEN pd1.TextbookVersionBaseId = 0 THEN '校本实验' ELSE vb3.VersionName END) AS VersionBaseName ,
                pd1.TextbookVersionDetailId ,
                (CASE WHEN pd1.TextbookVersionDetailId = 0 THEN '' ELSE vd4.Chapter END) AS Chapter ,
                pd1.ExperimentName ,
                pd1.ExperimentType ,
                pd1.IsNeedDo ,
                pd1.WeekNum ,
				pi12.SchoolId ,
				pi12.GradeId ,
				pi12.CourseId ,
                sd22.DictionaryId AS SchoolStage,
				pi12.SchoolYearStart ,
				pi12.SchoolYearEnd ,
				pi12.SchoolTerm ,
				pi12.PlanName ,
				pi12.Num ,
				pi12.ClassIdz ,
                CASE WHEN eb4.Id > 0 THEN 1 ELSE 0 END  AS IsSelected ,
                vd4.EquipmentNeed ,
                vd4.MaterialNeed
                ,pd1.SourcePath
                ,pd1.SchoolExperimentId
                FROM  ex_PlanDetail AS pd1    
                INNER JOIN  ex_PlanInfo AS pi12 ON pd1.PlanInfoId = pi12.Id AND pi12.BaseIsDelete = 0  
                INNER JOIN  sys_dictionary_relation AS dr21 ON pi12.GradeId = dr21.DictionaryToId
                INNER JOIN  sys_static_dictionary AS sd22 ON dr21.DictionaryId = sd22.DictionaryId AND sd22.TypeCode= '{DicTypeCodeEnum.SchoolStage.ParseToInt()}'
             ) as tb1 WHERE   1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = @BaseIsDelete ");
                parameter.Add(DbParameterExtension.CreateDbParameter("@BaseIsDelete", param.BaseIsDelete));
                if (param.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    strSql.Append(" AND SchoolId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                else if (param.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    if (param.SchoolId > 0)
                    {
                        strSql.Append(" AND SchoolId = @UnitId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.SchoolId));
                    }
                }
                else if (param.UnitType == UnitTypeEnum.City.ParseToInt() || param.UnitType == UnitTypeEnum.System.ParseToInt())
                {
                }
                else
                {
                    strSql.Append(" AND 1 <> 1 ");
                }
                if (param.IsNeedDo > 0)
                {
                    strSql.Append(" AND IsNeedDo = @IsNeedDo ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@IsNeedDo", param.IsNeedDo));
                }
                if (param.ExperimentType > 0)
                {
                    strSql.Append(" AND ExperimentType = @ExperimentType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ExperimentType", param.ExperimentType));
                }
                if (param.TextbookVersionBaseId >= 0)
                {
                    strSql.Append(" AND TextbookVersionBaseId = @TextbookVersionBaseId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@TextbookVersionBaseId", param.TextbookVersionBaseId));
                }
                if (!string.IsNullOrEmpty(param.Name))
                {
                    strSql.Append(" AND (VersionBaseName like @Name OR ExperimentName like @Name OR Chapter like @Name ) ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Name", $"%{param.Name}%"));
                }
                if (param.SchoolStage > 0)
                {
                    strSql.Append(" AND SchoolStage = @SchoolStage ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStage", param.SchoolStage));
                }
                if (param.GradeId > 0)
                {
                    strSql.Append(" AND GradeId = @GradeId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@GradeId", param.GradeId));

                    if (param.SchoolStage == SchoolStageEnum.GaoZhong.ParseToInt())
                    {
                        if (param.SchoolGradeClassIdz != null && param.SchoolGradeClassIdz.Length > 0)
                        {
                            string tempWhere = "";
                            var classArr = param.SchoolGradeClassIdz.Split(",");
                            if (classArr != null && classArr.Length > 0)
                            {
                                for (int i = 0; i < classArr.Length; i++)
                                {
                                    if (i > 0)
                                    {
                                        tempWhere += " AND ";
                                    }
                                    tempWhere += string.Format(" ClassIdz like '%{0}%'  ", classArr[i]);
                                }
                            }
                            strSql.Append($" AND ({tempWhere}) ");
                        }

                    }
                }
                if (param.CourseId > 0)
                {
                    strSql.Append(" AND CourseId = @CourseId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (param.SchoolTerm > 0)
                {
                    strSql.Append(" AND SchoolTerm = @SchoolTerm ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolTerm", param.SchoolTerm));
                }
                if (param.SchoolYearStart > 0)
                {
                    strSql.Append(" AND SchoolYearStart = @SchoolYearStart ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolYearStart", param.SchoolYearStart));
                }
                if (param.SchoolYearEnd > 0)
                {
                    strSql.Append(" AND SchoolYearEnd = @SchoolYearEnd ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolYearEnd", param.SchoolYearEnd));
                }
            } 
            var list = await this.BaseRepository().FindList<PlanDetailEntity>(strSql.ToString(), parameter.ToArray());
            return list.ToList();
        }


        /// <summary>
        /// 实验预约查询
        /// </summary>
        /// <param name="param">查询参数</param>
        /// <param name="pagination">分页参数</param>
        /// <returns></returns>
        public async Task<List<PlanInfoEntity>> GetList(PlanDetailListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append($@"  SELECT * From (
                SELECT
                pd1.Id ,
                pd1.BaseIsDelete ,
                pd1.BaseCreateTime ,
                pd1.BaseModifyTime ,
                pd1.BaseCreatorId ,
                pd1.BaseModifierId ,
                pd1.BaseVersion ,
                pd1.PlanInfoId ,
                pd1.TextbookVersionCurrentId ,
                pd1.TextbookVersionBaseId ,
                (CASE WHEN pd1.TextbookVersionBaseId = 0 THEN '校本实验' ELSE vb3.VersionName END) AS VersionBaseName , 
                pd1.TextbookVersionDetailId ,
                (CASE WHEN pd1.TextbookVersionDetailId = 0 THEN se21.Chapter ELSE vd4.Chapter END) AS Chapter ,
                (CASE WHEN pd1.TextbookVersionDetailId = 0 THEN 0 ELSE vd4.ChapterSort END) AS ChapterSort ,
                pd1.ExperimentName ,
                pd1.ExperimentType ,
                pd1.IsNeedDo ,
                pd1.WeekNum ,
				pi12.SchoolId ,
				pi12.GradeId ,
				pi12.CourseId ,
				pi12.SchoolYearStart ,
				pi12.SchoolYearEnd ,
				pi12.SchoolTerm ,
				pi12.PlanName ,
				pi12.Num ,
				pi12.ClassIdz ,
                pi12.CompulsoryType,
                vd4.Remark ,
                ISNULL(vd4.IsEvaluate,0)  AS IsEvaluate ,
                pd1.SchoolExperimentId ,
                ISNULL(st.TermEnd,GETDATE()) AS  TermEnd ,
                ISNULL(st.FirstWeekDate,GETDATE()) AS  FirstWeekDate ,
                urcounty.UnitId AS CountyId
                FROM  ex_PlanDetail AS pd1    
                INNER JOIN  ex_PlanInfo AS pi12 ON pd1.PlanInfoId = pi12.Id AND pi12.BaseIsDelete = 0  
                LEFT JOIN  ex_TextbookVersionCurrent AS vc2 ON vc2.BaseIsDelete = 0 AND pd1.TextbookVersionCurrentId = vc2.Id  AND vc2.Statuz = {StatusEnum.Yes.ParseToInt()}
                LEFT JOIN  ex_TextbookVersionBase AS vb3 ON vb3.BaseIsDelete = 0 AND pd1.TextbookVersionBaseId = vb3.Id AND vb3.Statuz = {StatusEnum.Yes.ParseToInt()}
                LEFT JOIN  ex_TextbookVersionDetail AS vd4 ON vd4.BaseIsDelete = 0 AND pd1.TextbookVersionDetailId = vd4.Id AND vd4.Statuz = {StatusEnum.Yes.ParseToInt()}
                LEFT JOIN  ex_SchoolExperiment AS se21 ON pi12.SchoolId = se21.SchoolId AND se21.Id = pd1.SchoolExperimentId
                LEFT JOIN  bn_SchoolTerm AS st ON st.BaseIsDelete = 0 AND pi12.SchoolYearStart = st.SchoolYearStart AND pi12.SchoolTerm = st.SchoolTerm
                LEFT JOIN  up_UnitRelation AS urcounty ON pi12.SchoolId = urcounty.ExtensionObjId AND urcounty.ExtensionType = 3 AND urcounty.BaseIsDelete = 0
            ) as tb1 WHERE   BaseIsDelete = 0 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (!param.SchoolId.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolId = {param.SchoolId}");//根据用户登录的单位ID进行查询
                }
                if (!param.CountyId.IsNullOrZero())
                {
                    strSql.Append($" AND CountyId = {param.CountyId}");
                    if (param.SchoolId > 0)
                    {
                        strSql.Append($" AND SchoolId = {param.SchoolId}");
                    }
                }
                if (!param.SchoolYearStart.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolYearStart = {param.SchoolYearStart}"); //开始学年
                }
                if (!param.SchoolTerm.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolTerm = {param.SchoolTerm}"); //学期
                }
                if (!param.SchoolGradeClassId.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolGradeClassIdz like '%{param.SchoolGradeClassId}%' "); //年级班级Id
                }
                if (!param.CourseId.IsNullOrZero())
                {
                    strSql.Append($" AND CourseId = {param.CourseId}"); //学科
                }
                if (!param.ExperimentType.IsNullOrZero())
                {
                    strSql.Append($" AND (ExperimentType = {param.ExperimentType} or ExperimentType = 3)"); //实验类型
                }
                if (param.WeekNum > 0)
                {
                    strSql.Append(" AND WeekNum = @WeekNum ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@WeekNum", param.WeekNum));
                }
                if (param.WeekNumgt > 0)
                {
                    strSql.Append(" AND WeekNum > @WeekNumgt ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@WeekNumgt", param.WeekNumgt));
                }
            }
            IEnumerable<PlanInfoEntity> list = null;
            if (pagination != null)
            {
                list = await this.BaseRepository().FindList<PlanInfoEntity>(strSql.ToString(), parameter.ToArray(), pagination);
            }
            else
            {
                list = await this.BaseRepository().FindList<PlanInfoEntity>(strSql.ToString(), parameter.ToArray());
            }
            return list.ToList();
        }

        #endregion
    }
}
