﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.OrganizationManage;
using Dqy.Syjx.Model.Param.OrganizationManage;
using Dqy.Syjx.Enum;

namespace Dqy.Syjx.Service.OrganizationManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-09-16 13:40
    /// 描 述：服务类
    /// </summary>
    public class UnitRelationService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<UnitRelationEntity>> GetList(UnitRelationListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<UnitRelationEntity>> GetPageList(UnitRelationListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<UnitRelationEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<UnitRelationEntity>(id);
        }

        public async Task<UnitRelationEntity> GetEntity(long userId, long unitId)
        {
            return await this.BaseRepository().FindEntity<UnitRelationEntity>(f => f.ExtensionType == UnitRelationTypeEnum.User.ParseToInt() && f.ExtensionObjId == userId);
        }

        /// <summary>
        /// 查询单位与下级单位关系是否存在
        /// </summary>
        /// <param name="unitId">上级Id</param>
        /// <param name="extensionObjId">下级Id</param>
        /// <param name="extensionType">关系类型(1：用户关系  2：部门关系  3：单位关系)</param>
        /// <returns></returns>
        public async Task<UnitRelationEntity> GetEntity(long unitId,long extensionObjId,int extensionType)
        {
            return await this.BaseRepository().FindEntity<UnitRelationEntity>(a=>a.UnitId == unitId && a.BaseIsDelete == 0 && a.ExtensionType == extensionType && a.ExtensionObjId == extensionObjId);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(UnitRelationEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }
        public async Task SaveTransForm(UnitRelationEntity entity, Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }
        public async Task DeleteForm(string ids)
        {
            long[] idArr = TextHelper.SplitToArray<long>(ids, ',');
            await this.BaseRepository().Delete<UnitRelationEntity>(idArr);
        }
        #endregion

        #region 私有方法
        private Expression<Func<UnitRelationEntity, bool>> ListFilter(UnitRelationListParam param)
        {
            var expression = LinqExtensions.True<UnitRelationEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                expression = expression.And(t => t.ExtensionType == param.ExtensionType);
                if (param.UnitId > 0)
                {
                    expression = expression.And(t => t.UnitId == param.UnitId);
                } 
                if (param.ExtensionObjId > 0)
                {
                    expression = expression.And(t => t.ExtensionObjId == param.ExtensionObjId);
                }
            }
            return expression;
        }
        #endregion
    }
}
