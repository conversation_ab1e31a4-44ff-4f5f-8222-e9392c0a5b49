﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <a class="btn btn-default btn-sm" onclick="showUseLogForm()"><i class="fa fa-street-view"></i>&nbsp;使用记录统计</a>
                    </li>
                    <li>
                        <a class="btn btn-default btn-sm" onclick="showDetailForm()"><i class="fa fa-street-view"></i>&nbsp;使用明细查看</a>
                    </li>
                    <li>
                        <div id="SchoolYearStart" col="SchoolYearStart" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <div id="SchoolTerm" col="SchoolTerm" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <div id="SchoolId" col="SchoolId" style="display: inline-block;width:200px;"></div>
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                        <a id="btnSearch" class="btn btn-default btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>
<script type="text/javascript">
    var Com_SchoolTermYear = new Date().getFullYear();
    var Com_SchoolTerm = 1;
    $(function () {
        ComBox.SchoolTermYear($("#SchoolYearStart"), undefined, '学年');
        $("#SchoolTerm").ysComboBox({
            data: ys.getJson(@Html.Raw(typeof(SchoolTermEnum).EnumToDictionaryString())),
            defaultName: '学期'
        });
        ys.ajax({
            url: '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/GetSchoolTermInfo")',
            type: 'get',
            async: false,
            success: function (obj) {
                if (obj.Tag == 1 && obj.Data != undefined) {
                    if (obj.Data.SchoolTermStartYear != undefined && parseInt(obj.Data.SchoolTermStartYear) > 0) {
                        Com_SchoolTermYear = obj.Data.SchoolTermStartYear;
                        $("#SchoolYearStart").ysComboBox('setValue', Com_SchoolTermYear);
                    }
                    if (obj.Data.SchoolTerm == 1 || obj.Data.SchoolTerm == 2) {
                        Com_SchoolTerm = obj.Data.SchoolTerm;
                        $("#SchoolTerm").ysComboBox('setValue', Com_SchoolTerm);
                    }
                }
            }
        });
        $('#SchoolId').ysComboBox({ url: '@Url.Content("~/OrganizationManage/Unit/GetChildrenPageList")' + "?PageSize=10000&SchoolStageId=0", key: 'Id', value: 'Name', defaultName: '单位名称' });
        initGrid();
    });
    function initGrid() {

        var columnsArr = [];

        columnsArr.push({
            field: 'index', title: '序号', width: 80, halign: 'center', valign: 'middle', align: 'center', formatter: function (value, row, index) {

                if (row.Id != undefined && Number(row.Id) && row.Id > 0) {
                    return index + 1;
                } else {
                    return "";
                }
            }
        },
            {
                title: '操作', halign: 'center', valign: 'middle', align: 'center', width: 100,
                formatter: function (value, row, index) {
                    var actions = [];
                    actions.push('&nbsp;<a class="btn btn-info btn-xs" href="#" onclick="showSchoolDetail(\'' + row.Id + '\')"><i class="fa fa-eye"></i>查看</a>&nbsp;');
                    if (row.Id != undefined && Number(row.Id) && row.Id > 0) {
                        return actions.join('');
                    } else {
                        return "";
                    }
                }
            },
            {
                field: 'UnitName', title: '单位名称', width: 200, halign: 'center', valign: 'middle', align: 'center',
                formatter: function (value, row, index) {
                    var html = '';
                    if (row.Id != undefined && Number(row.Id) && row.Id > 0) {
                        html = value;
                    } else {
                        html = "<b>总计：</b>";
                    }
                    return html;
                }
            },
            {
                field: 'UseTotal', title: '使用总次数', width: 140, halign: 'center', valign: 'middle', align: 'center',
                formatter: function (value, row, index) {
                    var html = '';
                    if (row.Id != undefined && Number(row.Id) && row.Id > 0) {
                        html = value;
                    } else {
                        html = "<b>" + value + "</b>";
                    }
                    return html;
                }
            });

        //加载分类列
        ys.ajax({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetList2Json")' + '?TypeCode=1006',
            type: 'get',
            async: false,
            success: function (obj) {
                if (obj.Tag == 1 && obj.Data != undefined) {
                    if (obj.Data.length > 0) {
                        for (var i = 0; i < obj.Data.length; i++) {
                            var itemNode = obj.Data[i];
                            if (isNaN(itemNode.Pid) || itemNode.Pid == 0 || itemNode.Pid == undefined) {
                                columnsArr.push({
                                    field: 'Catalog_' + itemNode.DictionaryId, title: itemNode.DicName, width: 140, halign: 'center', valign: 'middle', align: 'center',
                                    formatter: function (value, row, index) {
                                        var html = '';
                                        if (row.Id != undefined && Number(row.Id) && row.Id > 0) {
                                            html = value;
                                        } else {
                                            html = "<b>" + value + "</b>";
                                        }
                                        return html;
                                    }
                                });
                            }
                        }
                    }

                }
            }
        });

        var queryUrl = '@Url.Content("~/QueryStatisticsManage/FunRoomUse/GetUnitTotalJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            sortName: ' Sort ASC ,Id ASC',
            columns: [columnsArr],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
    }
    function resetGrid() {
        $('#SchoolYearStart').ysComboBox('setValue', Com_SchoolTermYear);
        $('#SchoolTerm').ysComboBox('setValue', Com_SchoolTerm);
        $("#SchoolId").ysComboBox('setValue', -1);
        $('#gridTable').ysTable('search');
    }
    function showDetailForm() {
        var url = '@Url.Content("~/QueryStatisticsManage/FunRoomUse/DetailList")';
        createMenuItem(url, "使用明细查看");
    }

    function showUseLogForm() {
        var url = '@Url.Content("~/QueryStatisticsManage/FunRoomUse/CountyRecordList")';
        createMenuItem(url, "使用记录统计");
    }

    function showSchoolDetail(schoolid) {
        var url = '@Url.Content("~/QueryStatisticsManage/FunRoomUse/CountyRecordList")' + '?id=' + schoolid + '&startyear=' + $("#SchoolYearStart").ysComboBox('getValue') + '&term=' + $("#SchoolTerm").ysComboBox('getValue');
        createMenuItem(url, "使用记录统计");
    }
</script>
