﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.ExperimentTeachManage;
using Dqy.Syjx.Model.Param.ExperimentTeachManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Enum.ExperimentTeachManage;

namespace Dqy.Syjx.Service.ExperimentTeachManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-12-10 11:29
    /// 描 述：服务类
    /// </summary>
    public class TextbookVersionCurrentService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<TextbookVersionCurrentEntity>> GetList(TextbookVersionCurrentListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<TextbookVersionCurrentEntity>> GetPageList(TextbookVersionCurrentListParam param, Pagination pagination)
        {
            StringBuilder sql = new StringBuilder();
            var expression = ListFilter(param, sql);
            var list = await this.BaseRepository().FindList<TextbookVersionCurrentEntity>(sql.ToString(), expression.ToArray(), pagination);
            return list.ToList();
        }

        public async Task<TextbookVersionCurrentEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<TextbookVersionCurrentEntity>(id);
        }
        #endregion

        #region 编制计划使用（获取实验版本下拉）

        /// <summary>
        /// 获取实验教材版本
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<List<TextbookVersionCurrentEntity>> GetSelectList(TextbookVersionCurrentListParam param)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ListSelectListFilter(param, strSql);
            var list = await this.BaseRepository().FindList<TextbookVersionCurrentEntity>(strSql.ToString(), filter.ToArray());
            return list.ToList();
        }

        #endregion

        #region 提交数据
        public async Task SaveForm(TextbookVersionCurrentEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(TextbookVersionCurrentEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update ex_TextbookVersionCurrent set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
                strSql = $"update ex_TextbookVersionCurrent set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<TextbookVersionCurrentEntity, bool>> ListFilter(TextbookVersionCurrentListParam param)
        {
            var expression = LinqExtensions.True<TextbookVersionCurrentEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (!param.SchoolStage.IsNullOrZero())
                {
                    expression = expression.And(t => t.SchoolStage == param.SchoolStage);
                }
                if (!param.GradeId.IsNullOrZero())
                {
                    expression = expression.And(t => t.GradeId == param.GradeId);
                }
                if (!param.CourseId.IsNullOrZero())
                {
                    expression = expression.And(t => t.CourseId == param.CourseId);
                }
                if (!param.SchoolTerm.IsNullOrZero())
                {
                    expression = expression.And(t => t.SchoolTerm == param.SchoolTerm);
                }
                if (!param.TextbookVersionBaseId.IsNullOrZero())
                {
                    expression = expression.And(t => t.TextbookVersionBaseId == param.TextbookVersionBaseId);
                }
            }
            return expression;
        }

        private List<DbParameter> ListFilter(TextbookVersionCurrentListParam param, StringBuilder strSql)
        {
            strSql.Append(@$" SELECT * From (
                               SELECT  TC.Id ,
                                       TC.BaseCreateTime ,TC.BaseModifyTime ,TC.BaseCreatorId ,TC.BaseModifierId ,TC.BaseVersion ,
                                       TC.TextbookVersionBaseId ,
                                       TC.SchoolStage ,TC.GradeId ,TC.CourseId ,TC.SchoolTerm ,TC.Statuz ,
                                       D1.DicName AS SchoolStageName ,D2.DicName AS GradeName ,D3.DicName AS CourseName ,TB.VersionName ,
                                       '' AS CountyNames
                               FROM  ex_TextbookVersionCurrent AS TC
                               INNER JOIN  sys_static_dictionary AS D1 ON TC.SchoolStage = D1.DictionaryId 
                                                                    AND D1.BaseIsDelete = 0 AND D1.TypeCode = '{DicTypeCodeEnum.SchoolStage.ParseToInt()}'
                               LEFT JOIN  sys_static_dictionary AS D2 ON TC.GradeId = D2.DictionaryId AND D2.BaseIsDelete = 0 
                                                                    AND D2.TypeCode = '{DicTypeCodeEnum.Grade.ParseToInt()}'
                               INNER JOIN  sys_static_dictionary AS D3 ON TC.CourseId = D3.DictionaryId AND D3.BaseIsDelete = 0 
                                                                    AND D3.TypeCode = '{DicTypeCodeEnum.Course.ParseToInt()}'
                               INNER JOIN  ex_TextbookVersionBase AS TB ON TC.TextbookVersionBaseId = TB.Id AND TB.BaseIsDelete = 0
                               WHERE TC.BaseIsDelete = 0                               
                          ) as T WHERE  1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (!param.SchoolStage.IsNullOrZero())
                {
                    strSql.Append(" AND SchoolStage = @SchoolStage ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStage", param.SchoolStage));
                }
                if (!param.GradeId.IsNullOrZero())
                {
                    strSql.Append(" AND GradeId = @GradeId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@GradeId", param.GradeId));
                }
                if (!param.CourseId.IsNullOrZero())
                {
                    strSql.Append(" AND CourseId = @CourseId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (!param.VersionName.IsEmpty())
                {
                    strSql.Append($" AND VersionName LIKE '%{param.VersionName.Trim()}%'");
                }
            }
            return parameter;
        }

        private List<DbParameter> ListSelectListFilter(TextbookVersionCurrentListParam param, StringBuilder strSql)
        {
            strSql.Append(" SELECT DISTINCT Id , VersionName, GradeId, SchoolTerm From (");
            strSql.Append($@"  SELECT * From (
                		SELECT 
                        vc1.Id ,
                        vc1.BaseIsDelete ,
                        vc1.BaseCreateTime ,
                        vc1.BaseModifyTime ,
                        vc1.BaseCreatorId ,
                        vc1.BaseModifierId ,
                        vc1.BaseVersion ,
                        vb1.VersionName ,
                        CASE WHEN ISNULL(vb1.CompulsoryType,0) = {TextbookCompulsoryTypeEnum.Must.ParseToInt()} Then {TextbookCompulsoryTypeEnum.Must.ParseToInt()} ELSE {TextbookCompulsoryTypeEnum.NonSelectMust.ParseToInt()}  END CompulsoryType ,
                        vc1.SchoolStage ,
                        vc1.GradeId ,
                        vc1.CourseId ,
                        vc1.SchoolTerm  ,
                        vc1.TextbookVersionBaseId ,
                        vc1.CountyIdz
		        FROM  ex_TextbookVersionBase AS vb1
		        INNER JOIN  ex_TextbookVersionCurrent AS vc1 ON vc1.BaseIsDelete = 0 AND vb1.Id = vc1.TextbookVersionBaseId AND vc1.Statuz =  {StatusEnum.Yes.ParseToInt()}  AND vc1.BaseIsDelete = 0
		        WHERE vb1.BaseIsDelete = 0 AND vb1.Statuz = {StatusEnum.Yes.ParseToInt()} AND vc1.Statuz = {StatusEnum.Yes.ParseToInt()}  
            ) as tb1 WHERE   1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = 0 ");
                if (param.GradeId > 0)
                {
                    strSql.Append(" AND GradeId = @GradeId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@GradeId", param.GradeId));
                }
                if (param.GradeIds != null && param.GradeIds.Count > 0)
                {
                    var wheretemp = new List<string>();
                    foreach (var item in param.GradeIds)
                    {
                        wheretemp.Add(string.Format(" GradeId = {0} ", item));
                    }
                    strSql.AppendFormat(" AND ({0}) ", string.Join(" OR ", wheretemp));
                }
                if (param.CourseId > 0)
                {
                    strSql.Append(" AND CourseId = @CourseId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (param.CourseIds != null && param.CourseIds.Count > 0)
                {
                    var wheretemp = new List<string>();
                    foreach (var item in param.CourseIds)
                    {
                        wheretemp.Add(string.Format(" CourseId = {0} ", item));
                    }
                    strSql.AppendFormat(" AND ({0}) ", string.Join(" OR ", wheretemp));
                }
                if (param.SchoolStage > 0)
                {
                    strSql.Append(" AND SchoolStage = @SchoolStage ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStage", param.SchoolStage));
                }
                if (param.SchoolStageList != null && param.SchoolStageList.Count > 0)
                {
                    var whereArr = new List<string>();
                    foreach (var item in param.SchoolStageList)
                    {
                        whereArr.Add($" SchoolStage = {item} ");
                    }
                    strSql.AppendFormat(" AND ({0}) ", string.Join(" OR ", whereArr));
                }
                if (param.CountyId > 0)
                {
                    strSql.Append($" AND CountyIdz like '%{param.CountyId}%' ");
                }
                if (param.SchoolTerm > 0)
                {
                    strSql.Append(" AND SchoolTerm = @SchoolTerm ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolTerm", param.SchoolTerm));
                }
                if (param.CompulsoryType > 0)
                {
                    strSql.Append(" AND CompulsoryType = @CompulsoryType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CompulsoryType", param.CompulsoryType));
                }
            }
            strSql.Append(" ) tb01");
            return parameter;
        }
        #endregion
    }
}
