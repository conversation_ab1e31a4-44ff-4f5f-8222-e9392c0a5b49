﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Model.Input.BusinessManage;
using Dqy.Syjx.Entity.BusinessManage;
using Dqy.Syjx.Model.Param.BusinessManage;
using Dqy.Syjx.Service.BusinessManage;
using Dqy.Syjx.Service.SystemManage;
using Dqy.Syjx.Entity.SystemManage;
using Dqy.Syjx.Model.Param.SystemManage;
using Dqy.Syjx.Web.Code;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Enum.BusinessManage;
using Dqy.Syjx.Service.OrganizationManage;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Model.Result.FunRoomUseManage;
using Dqy.Syjx.Service.PersonManage;
using Dqy.Syjx.Model.Param.PersonManage;
using Dqy.Syjx.Model.Param.OrganizationManage;
using System.Data;
using NetTopologySuite.Index.HPRtree;
using Dqy.Syjx.Service.InstrumentManage;
using Dqy.Syjx.Service.ExperimentTeachManage;
using Dqy.Syjx.Enum.InstrumentManage;
using Dqy.Syjx.Business.EvaluateManage;
using Dqy.Syjx.Business.SystemManage;

namespace Dqy.Syjx.Business.BusinessManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-11-01 09:27
    /// 描 述：基本信息管理业务类
    /// </summary>
    public class FunRoomBLL
    {
        /// <summary>
        /// ApiToken值（仅WebApi端传入）
        /// </summary>
        public string ApiToken = string.Empty;

        private FunRoomService funRoomService = new FunRoomService();
        private AddressService addressService = new();
        private StaticDictionaryService dictionaryService = new StaticDictionaryService();
        private AttachmentService attachmentService = new AttachmentService();
        private UserService userService = new UserService();
        private UserBelongService userBelongService = new UserBelongService();
        private UserClassInfoService userClassInfoService = new UserClassInfoService();
        private CourseSectionService courseSectionService = new CourseSectionService();
        private UserSchoolStageSubjectService userSchoolStageSubjectService = new UserSchoolStageSubjectService();
        private SnCodeService snCodeService = new SnCodeService();
        private CupboardService cupboardService = new CupboardService();
        private SchoolGradeClassService schoolGradeclassService = new SchoolGradeClassService();
        private SchoolInstrumentService schoolInstrumentService = new SchoolInstrumentService();
        private ExperimentBookingService experimentBookingService = new ExperimentBookingService();
        private FunRoomAttendStaticBLL funroomAttendStaticBll = new FunRoomAttendStaticBLL();
        private UnitService unitService = new UnitService();
        #region 获取数据
        public async Task<TData<List<FunRoomEntity>>> GetList(FunRoomListParam param)
        {
            TData<List<FunRoomEntity>> obj = new TData<List<FunRoomEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            if (operatorinfo == null)
            {
                obj.Tag = -1;
                obj.Message = "登录信息失效！";
                return obj;
            }
            param.Nature = FunNatureTypeEnum.ZhuanRoom.ParseToInt();
            param.UnitType = operatorinfo.UnitType;
            if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                param.CountyId = operatorinfo.UnitId.Value;
            }
            else
            {
                param.UnitId = operatorinfo.UnitId.Value;
            }
            Pagination pagin = new Pagination();
            pagin.PageSize = int.MaxValue;
            obj.Data = await funRoomService.GetPageList(param, pagin);
            obj.Total = obj.Data.Count;
            obj.Tag = 1;
            return obj;
        }

        public async Task<TData<List<FunRoomEntity>>> GetUserList(FunRoomListParam param)
        {
            TData<List<FunRoomEntity>> obj = new TData<List<FunRoomEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            if (operatorinfo == null)
            {
                obj.Tag = -1;
                obj.Message = "登录信息失效！";
                return obj;
            }
            param.UnitType = operatorinfo.UnitType;
            if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                param.CountyId = operatorinfo.UnitId.Value;
            }
            else
            {
                param.UnitId = operatorinfo.UnitId.Value;
            }
            obj.Data = await funRoomService.GetUserList(param);
            obj.Total = obj.Data.Count;
            obj.Tag = 1;
            return obj;
        }

        public async Task<TData<List<FunRoomEntity>>> GetPageByUserIdList(FunRoomListParam param, Pagination pagination)
        {
            TData<List<FunRoomEntity>> obj = new TData<List<FunRoomEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            param.UserId = operatorinfo.UserId.Value;
            param.UnitType = operatorinfo.UnitType;
            if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                param.CountyId = operatorinfo.UnitId.Value;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                param.UnitId = operatorinfo.UnitId.Value;
                param.SafeguardUserId = operatorinfo.UserId.Value;
            }
            obj.Data = await funRoomService.GetPageList(param, pagination);
            obj.Total = pagination.TotalCount;
            obj.Tag = 1;
            return obj;
        }

        /// <summary>
        /// 实验（专用）室统计页面-学校端
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<TData<List<FunRoomEntity>>> GetStatisticsList(FunRoomListParam param, Pagination pagination)
        {
            TData<List<FunRoomEntity>> obj = new TData<List<FunRoomEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            param.UserId = operatorinfo.UserId.Value;
            param.UnitType = operatorinfo.UnitType;
            if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                param.CountyId = operatorinfo.UnitId.Value;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                param.UnitId = operatorinfo.UnitId.Value;
            }
            obj.Data = await funRoomService.GetStatisticsList(param, pagination);
            obj.Total = pagination.TotalCount;
            if (param.IsShowTotalRow == 1 && obj.Total > 0)
            {
                //增加总计行
                obj.Data.Add(await funRoomService.GetTotalSum(param,"",""));
            }
            obj.Tag = 1;
            return obj;
        }

        /// <summary>
        /// 实验（专用）室统计页面-区县端
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<TData<List<FunRoomEntity>>> GetCountyStatisticsList(FunRoomListParam param, Pagination pagination)
        {
            TData<List<FunRoomEntity>> obj = new TData<List<FunRoomEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            param.UserId = operatorinfo.UserId.Value;
            param.UnitType = operatorinfo.UnitType;
            param.SetUserId = operatorinfo.UserId.Value;
            if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                param.CountyId = operatorinfo.UnitId.Value;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                param.UnitId = operatorinfo.UnitId.Value;
            }
            obj.Data = await funRoomService.GetCountyStatisticsList(param, pagination, "CountyId ,", " ,CountyId");
            obj.Total = pagination.TotalCount;
            if (param.IsShowTotalRow == 1 && obj.Total > 0)
            {
                //增加总计行
                obj.Data.Add(await funRoomService.GetTotalSum(param, "CountyId ,", " ,CountyId"));
            }
            obj.Tag = 1;
            return obj;
        }

        /// <summary>
        /// 实验（专用）室统计页面-区县端
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<TData<List<FunRoomEntity>>> GetCountyStatisticsListBySchool(FunRoomListParam param, Pagination pagination)
        {
            TData<List<FunRoomEntity>> obj = new TData<List<FunRoomEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            param.UserId = operatorinfo.UserId.Value;
            param.UnitType = operatorinfo.UnitType;
            param.SetUserId = operatorinfo.UserId.Value;
            if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                param.CountyId = operatorinfo.UnitId.Value;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                param.UnitId = operatorinfo.UnitId.Value;
            }
            obj.Data = await funRoomService.GetCountyStatisticsList(param, pagination, "UnitId ,SchoolName,", " ,UnitId ,SchoolName");
            obj.Total = pagination.TotalCount;
            if (param.IsShowTotalRow == 1 && obj.Total > 0)
            {
                //增加总计行
                obj.Data.Add(await funRoomService.GetTotalSum(param, "UnitId ,SchoolName,", " ,UnitId ,SchoolName"));
            }
            obj.Tag = 1;
            return obj;
        }

        /// <summary>
        /// 实验（专用）室统计页面-市级端
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<TData<List<FunRoomEntity>>> GetCityStatisticsList(FunRoomListParam param, Pagination pagination)
        {
            TData<List<FunRoomEntity>> obj = new TData<List<FunRoomEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            param.UserId = operatorinfo.UserId.Value;
            param.UnitType = operatorinfo.UnitType;
            if (operatorinfo.UnitType == UnitTypeEnum.City.ParseToInt())
            {
                param.CityId = operatorinfo.UnitId.Value;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                param.CountyId = operatorinfo.UnitId.Value;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                param.UnitId = operatorinfo.UnitId.Value;
            }
            obj.Data = await funRoomService.GetCountyStatisticsList(param, pagination, "CityId,", ",CityId");
            obj.Total = pagination.TotalCount;
            if (param.IsShowTotalRow == 1 && obj.Total > 0)
            {
                //增加总计行
                obj.Data.Add(await funRoomService.GetTotalSum(param, "CityId,", ",CityId"));
            }
            obj.Tag = 1;
            return obj;
        }

        /// <summary>
        /// 实验（专用）室统计页面-市级端
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<TData<List<FunRoomEntity>>> GetCityStatisticsListByCounty(FunRoomListParam param, Pagination pagination)
        {
            TData<List<FunRoomEntity>> obj = new TData<List<FunRoomEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            param.UserId = operatorinfo.UserId.Value;
            param.UnitType = operatorinfo.UnitType;
            if (operatorinfo.UnitType == UnitTypeEnum.City.ParseToInt())
            {
                param.CityId = operatorinfo.UnitId.Value;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                param.CountyId = operatorinfo.UnitId.Value;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                param.UnitId = operatorinfo.UnitId.Value;
            }
            obj.Data = await funRoomService.GetCountyStatisticsList(param, pagination, "CityId, CountyId, CountyName,", ",CountyId ,CityId ,CountyName");
            obj.Total = pagination.TotalCount;
            if (param.IsShowTotalRow == 1 && obj.Total > 0)
            {
                //增加总计行
                obj.Data.Add(await funRoomService.GetTotalSum(param, "CityId, CountyId, CountyName,", ",CountyId ,CityId ,CountyName"));
            }
            obj.Tag = 1;
            return obj;
        }

        public async Task<TData<List<FunRoomEntity>>> GetPageList(FunRoomListParam param, Pagination pagination)
        {
            TData<List<FunRoomEntity>> obj = new TData<List<FunRoomEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            param.UserId = operatorinfo.UserId.Value;
            param.UnitType = operatorinfo.UnitType;
            param.SetUserId = operatorinfo.UserId.Value;
            if(operatorinfo.UnitType == UnitTypeEnum.City.ParseToInt())
            {
                param.CityId = operatorinfo.UnitId.Value;

            }else if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                param.CountyId = operatorinfo.UnitId.Value;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                param.UnitId = operatorinfo.UnitId.Value;
            }
            obj.Data = await funRoomService.GetPageList(param, pagination);
            obj.Total = pagination.TotalCount;
            if (param.IsShowTotalRow == 1 && obj.Total > 0)
            {
                //增加总计行
                obj.Data.Add(await funRoomService.GetPageListTotalSum(param));
            }
            obj.Tag = 1;
            return obj;
        }

        public async Task<TData<List<FunRoomEntity>>> GetCountyPageList(FunRoomListParam param, Pagination pagination)
        {
            TData<List<FunRoomEntity>> obj = new TData<List<FunRoomEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            param.UserId = operatorinfo.UserId.Value;
            param.UnitType = operatorinfo.UnitType;
            if(operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                param.UnitId = operatorinfo.UnitId.Value;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                param.CountyId = operatorinfo.UnitId.Value;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.City.ParseToInt())
            {
                param.CityId = operatorinfo.UnitId.Value;
            }

            obj.Data = await funRoomService.GetCountyPageList(param, pagination);
            obj.Total = pagination.TotalCount;
            obj.Tag = 1;
            return obj;
        }
        public async Task<TData<List<FunRoomEntity>>> GetListBySafeguardUserId(FunRoomListParam param)
        {
            TData<List<FunRoomEntity>> obj = new TData<List<FunRoomEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            param.UnitType = operatorinfo.UnitType;
            param.UnitId = operatorinfo.UnitId.Value;
            param.SafeguardUserId = operatorinfo.UserId.Value;
            if (param.OptType == 1)
            {
                param.SafeguardUserId = 0;
            }
            param.Statuz = StatusEnum.Yes.ParseToInt();
            obj.Data = await funRoomService.GetPageList(param, new Pagination() { PageIndex = 0, PageSize = int.MaxValue, Sort = "Id", SortType = "ASC" });
            obj.Tag = 1;
            return obj;
        }
        public async Task<TData<FunRoomEntity>> GetEntity(long id)
        {
            TData<FunRoomEntity> obj = new TData<FunRoomEntity>();
            if (id > 0)
            {
                obj.Data = await funRoomService.GetEntity(id);
                if (obj.Data != null)
                {
                    obj.Tag = 1;
                    //加载附件
                    var attachmentList = await attachmentService.GetList(new AttachmentListParam() { FileCategory = FileCategoryEnum.SceneImg.ParseToInt(), ObjectId = obj.Data.Id });
                    if (attachmentList != null && attachmentList.Count > 0)
                    {
                        obj.Data.AttachmentList = attachmentList;
                    }
                }
            }
            return obj;
        }

        public async Task<TData<FunRoomEntity>> GetDetailEntity(long id)
        {
            TData<FunRoomEntity> obj = new TData<FunRoomEntity>();
            if (id > 0)
            {
                OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
                FunRoomListParam param = new FunRoomListParam();
                param.UserId = operatorinfo.UserId.Value;
                param.UnitId = operatorinfo.UnitId.Value;
                param.UnitType = operatorinfo.UnitType;
                param.Id = id;
                Pagination pagination = new Pagination();
                pagination.PageIndex = 1;
                pagination.PageSize = 1;
                pagination.Sort = "Id";
                pagination.SortType = "ASC";
                var templist = await funRoomService.GetPageList(param, pagination);
                if (templist != null && templist.Count > 0)
                {
                    obj.Data = templist[0];
                }

                var entity = await funRoomService.GetEntity(id);
                if (entity != null)
                {
                    if (obj.Data != null)
                    {
                        obj.Data.Address = entity.Address;
                        obj.Data.SeatNum = entity.SeatNum;
                        obj.Data.IsDigitalize = entity.IsDigitalize;
                        obj.Data.BuildTime = entity.BuildTime;
                        obj.Data.ReformTime = entity.ReformTime;
                    }
                    else
                    {
                        obj.Data = entity;
                    }
                    var diclist = await dictionaryService.GetList(new StaticDictionaryListParam() { Ids = entity.SchoolStagez, TypeCode = DicTypeCodeEnum.SchoolStage.ParseToInt().ToString() });
                    if (diclist != null && diclist.Count > 0)
                    {
                        obj.Data.SchoolStageName = string.Join(',', diclist.Select(m => m.DicName).ToList());
                    }

                    long addressid = obj.Data.Address;

                    var addressEntity = await addressService.GetEntity(addressid);
                    if (addressEntity != null)
                    {
                        obj.Data.AddressFull = addressEntity.Name;
                        if (addressEntity.Pid > 0)
                        {
                            var addressPEntity = await addressService.GetEntity(addressEntity.Pid.Value);
                            if (addressPEntity != null)
                            {
                                obj.Data.AddressFull = addressPEntity.Name + "（" + addressEntity.Name + "）";
                            }
                        }
                    }
                }
                if (obj.Data != null)
                {
                    obj.Tag = 1;
                    //加载附件
                    var attachmentList = await attachmentService.GetList(new AttachmentListParam() { FileCategory = FileCategoryEnum.SceneImg.ParseToInt(), ObjectId = obj.Data.Id });
                    if (attachmentList != null && attachmentList.Count > 0)
                    {
                        obj.Data.AttachmentList = attachmentList;
                    }
                }
            }
            return obj;
        }

        /// <summary>
        /// 获取单条数据信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<TData<FunRoomEntity>> GetFunRoomSingle(long id)
        {
            TData<FunRoomEntity> obj = new TData<FunRoomEntity>();
            if (id > 0)
            {
                obj.Data = await funRoomService.GetFunRoomSingle(id);
                if (obj.Data != null)
                {
                    obj.Tag = 1;
                    //加载附件
                    var attachmentList1 = await attachmentService.GetList(new AttachmentListParam() { FileCategory = FileCategoryEnum.ManageSystem.ParseToInt(), ObjectId = id });
                    var attachmentList2 = await attachmentService.GetList(new AttachmentListParam() { FileCategory = FileCategoryEnum.BriefInfo.ParseToInt(), ObjectId = id });
                    if (attachmentList1 != null && attachmentList2 != null)
                    {
                        obj.Data.AttachmentList = attachmentList1.Concat(attachmentList2).ToList();
                    }
                }
            }
            return obj;
        }

        public async Task<TData<List<StaticDictionaryEntity>>> GetSubjectIdzList(int classid)
        {
            TData<List<StaticDictionaryEntity>> obj = new TData<List<StaticDictionaryEntity>>();
            StaticDictionaryListParam param = new StaticDictionaryListParam();
            param.DictionaryId = classid;
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            if (operatorinfo != null)
            {
                param.UnitId = operatorinfo.UnitId.Value;
                param.UserId = operatorinfo.UserId.Value;
            }
            obj.Data = await dictionaryService.GetSubjectIdzList(param);
            if (obj.Data != null)
            {
                obj.Tag = 1;
            }
            return obj;
        }
        public async Task<TData<List<StaticDictionaryEntity>>> GetSchoolStageIdzList(int subjectid)
        {
            TData<List<StaticDictionaryEntity>> obj = new TData<List<StaticDictionaryEntity>>();
            StaticDictionaryListParam param = new StaticDictionaryListParam();
            if (subjectid > 0)
            {
                param.DictionaryId = subjectid;
            }
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            if (operatorinfo != null)
            {
                param.UnitId = operatorinfo.UnitId.Value;
                param.UserId = operatorinfo.UserId.Value;
            }
            obj.Data = await dictionaryService.GetSchoolStageIdzList(param);
            if (obj.Data != null)
            {
                obj.Tag = 1;
            }
            return obj;
        }
      
        public async Task<TData<List<StaticDictionaryEntity>>> GetClassBySubjectList(StaticDictionaryListParam param)
        {
            TData<List<StaticDictionaryEntity>> obj = new TData<List<StaticDictionaryEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            if (operatorinfo != null)
            {
                param.UnitId = operatorinfo.UnitId.Value;
                param.UserId = operatorinfo.UserId.Value;
            }
            string subjectz = "";
            UserSchoolStageSubjectListParam subjectParam = new UserSchoolStageSubjectListParam ();
            subjectParam.UnitId = operatorinfo.UnitId;
            subjectParam.IsCurrentUnit = IsEnum.Yes.ParseToInt();
            subjectParam.UserId = operatorinfo.UserId.Value;
            var subjectList = await userSchoolStageSubjectService.GetList(subjectParam);
            if (subjectList!=null && subjectList.Count > 0)
            {
                subjectz = string.Join(",", subjectList.Select(m=>m.SubjectIdz.ToString()));
            }
            if (subjectz!="")
            {
                param.Ids = subjectz;
                obj.Data = await dictionaryService.GetClassBySubjectList(param);
                if (obj.Data != null)
                {
                    obj.Tag = 1;
                }
            }
            return obj;
        }

        //
        public async Task<TData<List<AttachmentEntity>>> GetReportList(long id)
        {
            TData<List<AttachmentEntity>> obj = new TData<List<AttachmentEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            var entity = await funRoomService.GetEntity(id);
            if (entity != null)
            {
                if (entity.UnitId == operatorinfo.UnitId && entity.SafeguardUserId == operatorinfo.UserId)
                {
                    var attachmentList1 = await attachmentService.GetList(new AttachmentListParam() { FileCategory = FileCategoryEnum.ManageSystem.ParseToInt(), ObjectId = id });
                    var attachmentList2 = await attachmentService.GetList(new AttachmentListParam() { FileCategory = FileCategoryEnum.BriefInfo.ParseToInt(), ObjectId = id });
                    if (attachmentList1 != null && attachmentList2 != null)
                    {
                        obj.Data = attachmentList1.Concat(attachmentList2).ToList();
                    }

                    obj.Tag = 1;
                }
                else
                {
                    obj.Tag = 0;
                    obj.Message = "你无权操作当前实验室。 ";
                }
            }
            else
            {
                obj.Tag = 0;
                obj.Message = "当前实验室不存在，请刷新重新操作。";
            }
            return obj;

        }

        /// <summary>
        /// 根据二维码编号获取专用室信息
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        public async Task<TData<ScanFunRoomUseResult>> GetScanRoomByQrcode(string code)
        {

            LogHelper.Info($" 【GetScanRoomByQrcode】接收到的code参数：{code}");
            TData<ScanFunRoomUseResult> obj = new TData<ScanFunRoomUseResult>();
            try
            {
                OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
                if (operatorinfo == null)
                {
                    obj.Tag = -1;
                    obj.Message = "登录信息失效！";
                    return obj;
                }
                FunRoomEntity entity = await funRoomService.GetEntityByQrcode(code, operatorinfo.UnitId ?? 0);
                if (entity == null)
                {
                    obj.Tag = 0;
                    obj.Message = "未找到数据！";
                    return obj;
                }
                if (entity.RoomAttribute != 1009002)
                {
                    obj.Tag = 0;
                    obj.Message = "仅支持专用室扫码登记！";
                    return obj;
                }
                int schoolStageId = await GetSchoolStageId(entity.SchoolStagez, operatorinfo);
                var (courseSectionId, courseSectionName) = await GetSection(operatorinfo);
                var result = new ScanFunRoomUseResult
                {
                    SchoolStageId = schoolStageId,
                    SchoolStageName = await GetDictionaryName(schoolStageId, DicTypeCodeEnum.SchoolStage.ParseToInt().ParseToString()),
                    CourseId = entity.DictionaryId1005,
                    CourseName = await GetDictionaryName(entity.DictionaryId1005, DicTypeCodeEnum.Course.ParseToInt().ParseToString()),
                    UseDate = DateTime.Now.ToString("yyyy-MM-dd"),
                    CourseSectionId = courseSectionId,
                    CourseSectionName = courseSectionName,
                    FunRoomId = entity.Id ?? 0,
                    FunRoomName = entity.Name
                };
                obj.Tag = 1;
                obj.Message = "查询成功";
                obj.Data = result;
            }
            catch (Exception ex)
            {
                obj.Tag = 0;
                obj.Message = ex.Message;
                throw new Exception("程序出现异常，异常信息为：" + ex.Message);
            }
            return obj;
        }

        public async Task<TData<object>> GetFunRoomNumJson(int SchoolStageId)
        {
            TData<object> result = new TData<object>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            FunRoomListParam param = new FunRoomListParam();
            param.UnitType = operatorinfo.UnitType;
            param.SchoolStage = SchoolStageId;
            if (operatorinfo.UnitType == UnitTypeEnum.City.ParseToInt())
            {
                param.CityId = operatorinfo.UnitId.Value;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                param.CountyId = operatorinfo.UnitId.Value;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                param.UnitId = operatorinfo.UnitId.Value;
            }
            var tb1 = await funRoomService.GetStatisticsNum(param);
            var LaboratoryTotal = 0;
            var PrivateRoomNum = 0;
            if (tb1 != null && tb1.Rows != null && tb1.Rows.Count > 0)
            {
                foreach (DataRow item in tb1.Rows)
                {
                    if (item["Nature"] != null && item["Num"] != null)
                    {
                        int tempNum = 0;
                        int.TryParse(item["Num"].ToString(), out tempNum);
                        if (item["Nature"].ToString() == FunRoomNatureType.Laboratory.ParseToInt().ToString())
                        {
                            LaboratoryTotal = tempNum;
                        }
                        else if (item["Nature"].ToString() == FunRoomNatureType.PrivateRoom.ParseToInt().ToString())
                        {
                            PrivateRoomNum = tempNum;
                        }
                    }
                }
            }
            result.Data = new { LaboratoryTotal = LaboratoryTotal, PrivateRoomNum = PrivateRoomNum };
            result.Total = 1;
            result.Tag = 1;
            return result;
        }


        public async Task<TData<List<FunRoomEntity>>> GetFunRoomClassifyNumJson(int SchoolStageId)
        {
            TData<List<FunRoomEntity>> result = new TData<List<FunRoomEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            FunRoomListParam param = new FunRoomListParam();
            param.UnitType = operatorinfo.UnitType;
            param.SchoolStage = SchoolStageId;
            if (operatorinfo.UnitType == UnitTypeEnum.City.ParseToInt())
            {
                param.CityId = operatorinfo.UnitId.Value;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                param.CountyId = operatorinfo.UnitId.Value;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                param.UnitId = operatorinfo.UnitId.Value;
            }
            param.Nature = FunRoomNatureType.WareHouse.ParseToInt();
            result.Data = await funRoomService.GetStatisticsClassifyNum(param);
            result.Total = result.Data.Count;
            result.Tag = 1;
            return result;
        }


        public async Task<TData<List<FunRoomEntity>>> GetFunRoomClassifyTwoNumJson(int SchoolStageId)
        {
            TData<List<FunRoomEntity>> result = new TData<List<FunRoomEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            FunRoomListParam param = new FunRoomListParam();
            param.UnitType = operatorinfo.UnitType;
            param.SchoolStage = SchoolStageId;
            if (operatorinfo.UnitType == UnitTypeEnum.City.ParseToInt())
            {
                param.CityId = operatorinfo.UnitId.Value;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                param.CountyId = operatorinfo.UnitId.Value;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                param.UnitId = operatorinfo.UnitId.Value;
            }
            param.Nature = FunRoomNatureType.WareHouse.ParseToInt();
            result.Data = await funRoomService.GetStatisticsClassifyTwoNum(param);
            result.Total = result.Data.Count;
            result.Tag = 1;
            return result;
        }

        /// <summary>
        /// 实验室统计信息（看板）
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<TData<IEnumerable<object>>> GetStatisticsJson(FunRoomListParam param)
        {
            TData<IEnumerable<object>> result = new TData<IEnumerable<object>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            param.UnitType = operatorinfo.UnitType;
            if (operatorinfo.UnitType == UnitTypeEnum.City.ParseToInt())
            {
                param.CityId = operatorinfo.UnitId.Value;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                param.CountyId = operatorinfo.UnitId.Value;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                param.UnitId = operatorinfo.UnitId.Value;
            }
            param.Nature = FunRoomNatureType.Laboratory.ParseToInt();
            var pageFunroom = new Pagination();
            pageFunroom.PageSize = int.MaxValue;
            var list = await funRoomService.GetStatistics(param, pageFunroom);

            SchoolGradeClassListParam paramGradeClass = new SchoolGradeClassListParam();
            paramGradeClass.UnitType = operatorinfo.UnitType;
            paramGradeClass.SchoolStage =param.SchoolStage.ToString();
            if (operatorinfo.UnitType == UnitTypeEnum.City.ParseToInt())
            {
                paramGradeClass.CityId = operatorinfo.UnitId.Value;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                paramGradeClass.CountyId = operatorinfo.UnitId.Value;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                paramGradeClass.UnitId = operatorinfo.UnitId.Value;
            }
            var resultClass = await schoolGradeclassService.GetStatistics(paramGradeClass);
            var paramDicCourse = new StaticDictionaryListParam();
            //paramDicCourse.Pids = param.SchoolStage.ToString();
            paramDicCourse.TypeCode = DicTypeCodeEnum.Course.ParseToInt().ToString();
            paramDicCourse.PTypeCode = DicTypeCodeEnum.SchoolStage.ParseToInt().ToString();
            paramDicCourse.Nature = 1;
            paramDicCourse.Statuz = 1;
            var courseList = await dictionaryService.GetChildToList(paramDicCourse);

            var paramDic = new StaticDictionaryListParam();
            paramDic.TypeCode = DicTypeCodeEnum.Grade.ParseToInt().ToString();
            paramDic.PTypeCode = DicTypeCodeEnum.Course.ParseToInt().ToString();
            paramDic.Statuz = 1;
            paramDic.Nature = 1;
            var gradeList = await dictionaryService.GetChildList(paramDic);

            if (courseList != null && courseList.Count > 0)
            {
                List<object> objList=new List<object>();
                foreach (var item in courseList)
                {
                    IEnumerable<int> gradeIdList = null;
                    if (gradeList != null && gradeList.Count()>0)
                    {
                        gradeIdList = gradeList.Where(n => n.Pid == item.DictionaryId).Select(n => n.DictionaryId ?? 0);
                    }
                    int classNum = 0;
                    if (resultClass != null && resultClass.Count() > 0 && gradeIdList != null)
                    {
                        classNum = resultClass.Where(s => gradeIdList.Contains<int>(s.GradeId ?? 0)).Count();
                    }
                    int numFunroom = 0;
                    if (list != null && list.Count() > 0)
                    {
                        numFunroom = list.Where(m => m.DictionaryId1005 == item.DictionaryId).Count();
                    }
                    var itemobj = new
                    {
                        CoourseId = item.DictionaryId,
                        CourseName = item.DicName,
                        Num = numFunroom,
                        RoomEven = classNum == 0 ? 0 : Math.Round((double)numFunroom / classNum, 2),
                        IsVaild = item.Pid == param.SchoolStage ? 1 : 0,
                        Sort = item.Sequence
                    };
                    if (item.DicName=="科学")
                    {
                        objList.Insert(0, itemobj);
                    }
                    else
                    {
                        objList.Add(itemobj);
                    }

                }
                result.Data = objList;
                result.Total = result.Data.Count();
            }
            result.Tag = 1;
            return result;
        }

        /// <summary>
        /// 实验室统计信息（看板）
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<TData<IEnumerable<object>>> GetPrivateStatisticsJson(FunRoomListParam param)
        {
            TData<IEnumerable<object>> result = new TData<IEnumerable<object>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            param.UnitType = operatorinfo.UnitType;
            if (operatorinfo.UnitType == UnitTypeEnum.City.ParseToInt())
            {
                param.CityId = operatorinfo.UnitId.Value;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                param.CountyId = operatorinfo.UnitId.Value;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                param.UnitId = operatorinfo.UnitId.Value;
            }
            param.Nature = FunRoomNatureType.PrivateRoom.ParseToInt();
            var pageFunroom = new Pagination();
            pageFunroom.PageSize = int.MaxValue;
            var list = await funRoomService.GetStatistics(param, pageFunroom);
            //音乐教室,美术教室,舞蹈教室,劳动技术室,通用技术室,科技创新室,计算机教室,录播教室
            var classifyTwos = new List<int>() { 1006021, 1006023, 1006027, 1006014, 1006030, 1006018, 1006032, 1006016, 1006045 };
            SchoolGradeClassListParam paramGradeClass = new SchoolGradeClassListParam();
            paramGradeClass.UnitType = operatorinfo.UnitType;
            paramGradeClass.SchoolStage = param.SchoolStage.ToString();
            if (operatorinfo.UnitType == UnitTypeEnum.City.ParseToInt())
            {
                paramGradeClass.CityId = operatorinfo.UnitId.Value;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                paramGradeClass.CountyId = operatorinfo.UnitId.Value;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                paramGradeClass.UnitId = operatorinfo.UnitId.Value;
            }
            paramGradeClass.SchoolProple = param.SchoolProple;
            var resultClass = await schoolGradeclassService.GetStatistics(paramGradeClass);

            //获取分类和学科的关系，获取的是学科
            var paramDicCourse = new StaticDictionaryListParam();
            //paramDicCourse.Pids = param.SchoolStage.ToString();
            paramDicCourse.TypeCode = DicTypeCodeEnum.FunRoomClass.ParseToInt().ToString();
            paramDicCourse.PTypeCode = DicTypeCodeEnum.Course.ParseToInt().ToString();
           // paramDicCourse.Nature = FunNatureTypeEnum.Room;
            paramDicCourse.Statuz = 1;
            var listFunroomClass = await dictionaryService.GetAllList(paramDicCourse);

            //获取学科和学段的关系
            var paramDic = new StaticDictionaryListParam();
            paramDic.TypeCode = DicTypeCodeEnum.Course.ParseToInt().ToString();
            paramDic.PTypeCode = DicTypeCodeEnum.SchoolStage.ParseToInt().ToString();
            paramDic.Statuz = 1;
            //paramDic.Nature = 1;
            var listCourse = await dictionaryService.GetAllList(paramDic);

            var paramGrade = new StaticDictionaryListParam();
            paramGrade.TypeCode = DicTypeCodeEnum.Grade.ParseToInt().ToString();
            paramGrade.PTypeCode = DicTypeCodeEnum.Course.ParseToInt().ToString();
            paramGrade.Statuz = 1;
            var listGrade = await dictionaryService.GetChildList(paramGrade);
            List<object> objList = new List<object>();
            foreach (var item in classifyTwos)
            {
                string classifyName = "";
                int classNum = 0;
                int numFunroom = 0;
                if (listFunroomClass != null)
                {
                    var objtemp = listFunroomClass.Where(m => m.DictionaryId == item);
                    if (objtemp != null && objtemp.Count() > 0)
                    {
                        var entityTemp = objtemp.FirstOrDefault();
                        classifyName = entityTemp.DicName;

                        var listSchoolStage = listCourse.Where(m => m.DictionaryId == entityTemp.Pid).Select(m => m.Pid);
                        //获取学段下的年级
                        if (resultClass != null && listSchoolStage != null)
                        {
                            classNum = resultClass.Where(m => listSchoolStage.Contains(m.SchoolStage)).Count();
                        }
                    }
                }
                if (list != null && list.Count() > 0)
                {
                    numFunroom = list.Where(m => m.DictionaryId1006B == item).Count();
                }
                var itemobj = new
                {
                    Id = item,
                    Name = classifyName,
                    Num = numFunroom,
                    RoomEven = classNum == 0 ? 0 : Math.Round((double)numFunroom / classNum, 2),
                };
                objList.Add(itemobj);
            }
            result.Data = objList;
            result.Total = result.Data.Count();
            result.Tag = 1;
            return result;
        }


        /// <summary>
        /// 单位实验室分类统计
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<TData<List<Dictionary<string, object>>>> GetUnitTotalList(FunRoomListParam param, Pagination pagination)
        {
            TData<List<Dictionary<string, object>>> obj = new TData<List<Dictionary<string, object>>>();
            obj.Data = new List<Dictionary<string, object>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            var paramUnit = new UnitListParam();
            paramUnit.Pid = operatorinfo.UnitId ?? 0;
            if (param.UnitId > 0)
            {
                paramUnit.Id = param.UnitId;
            }
            var pageUnit = new Pagination();
            pageUnit.PageIndex = pagination.PageIndex;
            pageUnit.PageSize = pagination.PageSize;
            pageUnit.Sort = pagination.Sort;
            pageUnit.SortType= pagination.SortType;
            var listUnit = await unitService.GetChildrenPageList(paramUnit, pageUnit);
            List<StaticDictionaryEntity> listCourse = null;
            IEnumerable<FunRoomEntity> list = null;
            if (listUnit != null && listUnit.Count > 0)
            {
                var paramDic = new StaticDictionaryListParam();
                paramDic.TypeCode = DicTypeCodeEnum.FunRoomClass.ParseToInt().ToString();
                listCourse = await dictionaryService.GetList(paramDic);

                if (listCourse != null && listCourse.Count > 0)
                {
                    listCourse = listCourse.Where(m => m.Pid == null || m.Pid == 0).ToList();
                }

                if (listCourse != null && listCourse.Count > 0)
                {
                    var paramUse = new FunRoomListParam();
                    //paramUse.SchoolYearStart = param.SchoolYearStart;
                    //paramUse.SchoolTerm = param.SchoolTerm;
                    if (param.UnitId > 0)
                    {
                        paramUse.UnitId = param.UnitId;
                    }
                    paramUse.UnitType = operatorinfo.UnitType;
                    if (operatorinfo.UnitType == UnitTypeEnum.City.ParseToInt())
                    {
                        paramUse.CityId = operatorinfo.UnitId.Value;
                    }
                    else if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
                    {
                        paramUse.CountyId = operatorinfo.UnitId.Value;
                    }
                    else if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
                    {
                        paramUse.UnitId = operatorinfo.UnitId.Value;
                    }
                    //paramUse.NatureType = FunRoomNatureType.Laboratory.ParseToInt();
                    var pageFunroom = new Pagination();
                    pageFunroom.PageSize = int.MaxValue;
                    list = await funRoomService.GetStatistics(paramUse, pageFunroom);
                    //保证不为null
                    if (list == null)
                    {
                        list = new List<FunRoomEntity>();
                    }
                }

                foreach (var itemUnit in listUnit)
                {
                    int itemTotal = 0;
                    Dictionary<string, object> dicPproperties = new Dictionary<string, object>();
                    dicPproperties.Add("Id", itemUnit.Id.ToString());
                    dicPproperties.Add("UnitName", itemUnit.Name);
                    dicPproperties.Add("Sort", itemUnit.Sort);

                    if (listCourse != null && listCourse.Count > 0)
                    {
                        var listSchoolUse = list.Where(m => m.UnitId == itemUnit.Id);
                        foreach (var itemCourse in listCourse)
                        {
                            int num = 0;
                            if (listSchoolUse != null && listSchoolUse.Count() > 0)
                            {
                                var listTemp = listSchoolUse.Where(m => m.DictionaryId1006A == itemCourse.DictionaryId);
                                if (listTemp != null && listTemp.Count() > 0)
                                {
                                    num = listTemp.Count();
                                }
                            }
                            dicPproperties.Add("Catalog_" + itemCourse.DictionaryId.ToString(), num);
                            itemTotal += num;
                        }
                    }
                    dicPproperties.Add("UseTotal", itemTotal);
                    obj.Data.Add(dicPproperties);
                }
            }
            obj.Total = pageUnit.TotalCount;
            obj.Tag = 1;

            //总计
            if (obj.Data != null && obj.Data.Count > 0)
            {
                int itemTotal = 0;
                Dictionary<string, object> dicPproperties = new Dictionary<string, object>();
                dicPproperties.Add("Id", 0);
                dicPproperties.Add("UnitName", "总计");
                dicPproperties.Add("Sort", 0);
                foreach (var itemCourse in listCourse)
                {
                    int num = 0;
                    var listTemp = list.Where(m => m.DictionaryId1006A == itemCourse.DictionaryId);
                    if (listTemp != null && listTemp.Count() > 0)
                    {
                        num = listTemp.Count();
                    }
                    dicPproperties.Add("Catalog_" + itemCourse.DictionaryId.ToString(), num);
                    itemTotal += num;
                }
                dicPproperties.Add("UseTotal", itemTotal);
                obj.Data.Add(dicPproperties);
            }
            return obj;
        }
        #endregion

        #region 提交数据
        public async Task<TData<string>> SaveForm(FunRoomInput model)
        {
            TData<string> obj = new TData<string>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            Repository db = funRoomService.BaseRepository();
            var entity = new FunRoomEntity();
            try
            {
                if (model.Id > 0)
                {
                    entity = await funRoomService.GetEntity(model.Id);
                    if (entity == null || entity.UnitId != operatorinfo.UnitId)
                    {
                        obj.Tag = 0;
                        obj.Message = "非法操作，禁止操作其他单位数据信息。";
                        return obj;
                    }
                    if (entity.SafeguardUserId != operatorinfo.UserId)
                    {
                        obj.Tag = 0;
                        obj.Message = "禁止修改,你非本实验室管理员。";
                        return obj;
                    }
                }
                else
                {
                    entity.Statuz = StatusEnum.Yes.ParseToInt();
                    entity.SafeguardUserId = operatorinfo.UserId.Value;
                    entity.UnitId = operatorinfo.UnitId.Value;
                    entity.FillInStatuz = FillInStatuzEnum.NotReported.ParseToInt();
                }
                //验证当前地点是否已添加实验室。
                long addressId = 0;
                if (model.Address != null && model.Address.Length > 0)
                {
                    long.TryParse(model.Address,out addressId);
                    if (addressId > 0)
                    {
                        var list = await funRoomService.GetList(new FunRoomListParam() { OptType = 1, AddressId = addressId, Id = entity.Id ?? 0 });
                        if (list != null && list.Count > 0)
                        {
                            obj.Tag = 0;
                            obj.Message = "保存数据失败,当前地点已存在其他实验室。";
                            return obj;
                        }
                    }
                }
                //验证重名：
                var listSameName = await funRoomService.GetList(new FunRoomListParam() { UnitId = operatorinfo.UnitId ?? 0, DictionaryId1005 = model.DictionaryId1005,  DictionaryId1006B= model.DictionaryId1006B ,Name= model.Name });
                if (listSameName!=null && listSameName.Where(m=>m.Id != entity.Id).Count() > 0)
                {
                    obj.Tag = 0;
                    obj.Message = $"保存数据失败,当前分类学科已存在名称为“{model.Name}”的室，请修改后再提交确认。";
                    return obj;
                }
                //entity.Id = model.Id;
                entity.DictionaryId1005 = model.DictionaryId1005;
                entity.SchoolStagez = model.SchoolStagez;
                entity.DictionaryId1006A = model.DictionaryId1006A;
                entity.DictionaryId1006B = model.DictionaryId1006B;
                entity.Name = model.Name;
                entity.UseArea = model.UseArea;
                entity.RoomAttribute = model.RoomAttribute;
                entity.SeatNum = model.SeatNum ?? 0;
                entity.IsDigitalize = model.IsDigitalize ?? 0;
                entity.SysDepartmentId = model.SysDepartmentId ?? 0;
                entity.SysUserId = model.SysUserId ?? 0;
                entity.BuildTime = model.BuildTime;
                entity.ReformTime = model.ReformTime == null ? "" : model.ReformTime.ToString();
                entity.Address = addressId;
                entity.InvestmentAmount = model.InvestmentAmount;
                try
                {
                    db = await db.BeginTrans();
                    await funRoomService.SaveTransForm(entity, db);
                    //保存附件

                    //获取所有附件。
                    var attachmentList = await attachmentService.GetList(new AttachmentListParam() { FileCategory = FileCategoryEnum.SceneImg.ParseToInt(), OptType = 1, Ids = model.Imagez, ObjectId = entity.Id });
                    if (attachmentList != null && attachmentList.Count > 0)
                    {
                        foreach (var item in attachmentList)
                        {
                            if (!string.IsNullOrEmpty(model.Imagez) && model.Imagez.Contains(item.Id.ToString()))
                            {
                                if (item.ObjectId != entity.Id)
                                {
                                    //更新objectId
                                    item.ObjectId = entity.Id;
                                    await attachmentService.UpdateTransForm(item, db);
                                }
                            }
                            else
                            {
                                item.BaseIsDelete = IsEnum.Yes.ParseToInt();
                                await attachmentService.UpdateTransForm(item, db);
                            }
                        }
                    }
                    await db.CommitTrans();
                    obj.Data = entity.Id.ParseToString();
                    obj.Tag = 1;
                }
                catch (Exception ex)
                {
                    obj.Tag = 0;
                    obj.Message = "执行异常，请联系客服协助处理。";
                    await db.RollbackTrans();
                    throw new Exception("程序出现异常，异常信息为：" + ex.Message);
                }
            }
            catch (Exception ex)
            {
                obj.Tag = 0;
                obj.Message = "执行异常，请联系客服协助处理。";
                throw new Exception("程序出现异常，异常信息为：" + ex.Message);
            }
            //更新实验室达标情况，不需要等待结果。
            funroomAttendStaticBll.UpdateStaticResult(entity.UnitId, entity.DictionaryId1005, null);
            return obj;
        }

        /// <summary>
        /// 删除实验室，
        /// 1：验证实验室是否创建柜子，如果存在柜子，提示请移出柜子再删除地点
        /// 2：验证实验室是否有仪器设备如果存在禁止删除。
        /// 3：验证当前实验室是否已存在备实验使用过，如果使用过禁止删除。
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task<TData> DeleteForm(long id)
        {
            TData obj = new TData();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            var entity = await funRoomService.GetEntity(id);
            if (entity != null)
            {
                if (entity.SafeguardUserId != operatorinfo.UserId)
                {
                    obj.Tag = 0;
                    obj.Message = "删除失败，只能删除自己管理的室";
                    return obj;
                }
                string errorMsg = "";
                var cupboardList = await cupboardService.GetList(new CupboardListParam() { UnitId = operatorinfo.UnitId ?? 0, FunRoomId = entity.Id ?? 0, BaseIsDelete = 0 });
                if (cupboardList != null && cupboardList.Count > 0)
                {
                    errorMsg +=string.Format("删除失败。当前室还有未移出的柜子。<br/>【柜子名称：{0}】<br/>", string.Join(",", cupboardList.Select(m=>m.Name).Take(3)));
                }
                var paramInstrument = new Model.Param.InstrumentManage.SchoolInstrumentListParam();
                paramInstrument.SchoolId = operatorinfo.UnitId;
                paramInstrument.FunRoomId = entity.Id;
                paramInstrument.ListType = 3;
                paramInstrument.Statuz = InstrumentInputStatuzEnum.WaitInputStorage.ParseToInt();
                var listInstrument = await schoolInstrumentService.GetList(paramInstrument);
                if (listInstrument != null && listInstrument.Count > 0)
                {
                    errorMsg += string.Format("删除失败。该室中还存在仪器。<br/>【仪器名称：{0}】<br/>", string.Join(",", listInstrument.Select(m => m.Name).Take(3)));
                }

                var paramBooking = new Model.Param.ExperimentTeachManage.ExperimentBookingListParam();
                paramBooking.SchoolId = operatorinfo.UnitId;
                paramBooking.FunRoomId = entity.Id ?? 0;
                paramBooking.ThanStatuz = 0;//查询必须大于0的
                var bookinglist = await experimentBookingService.GetList(paramBooking);
                if (bookinglist != null && bookinglist.Count > 0)
                {
                    errorMsg += string.Format("删除失败。实验教学中已使用当前实验室的信息。<br/>【实验名称：{0}】<br/>", string.Join(",", bookinglist.Select(m => m.ExperimentName).Take(3)));
                }
                if (errorMsg != "")
                {
                    obj.Tag = 0;
                    obj.Message = errorMsg;
                    return obj;
                }
                else
                {
                    await funRoomService.DeleteForm(entity.Id.ToString());
                    obj.Tag = 1;
                    obj.Message = "删除成功。";
                    funroomAttendStaticBll.UpdateStaticResult(entity.UnitId, entity.DictionaryId1005, "");
                }
            }
            return obj;
        }
        public async Task<TData> UpdateStatuz(long id, int statuz)
        {
            TData obj = new TData();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            var entity = await funRoomService.GetEntity(id);
            if (entity != null && entity.UnitId == operatorinfo.UnitId)
            {
                if (operatorinfo.UserId != entity.SafeguardUserId)
                {
                    obj.Tag = 0;
                    obj.Message = "禁止操作，你非本室的管理员。";
                }
                if (statuz != StatusEnum.Yes.ParseToInt())
                {
                    statuz = StatusEnum.No.ParseToInt();
                }
                if (statuz == entity.Statuz)
                {
                    obj.Message = $"已{((StatusEnum)statuz).GetDescription()}成功。";
                }
                else
                {
                    obj.Message = $"{((StatusEnum)statuz).GetDescription()}成功。";
                    entity.Statuz = statuz;
                    await funRoomService.SaveForm(entity);
                }
                obj.Tag = 1;
            }
            else
            {
                obj.Tag = 0;
                obj.Message = "当前数据已不存在。";
            }
            return obj;
        }
        public async Task<TData<string>> SaveReportForm(long id, long attachmentid, int catelogry)
        {
            TData<string> obj = new TData<string>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            var db = funRoomService.BaseRepository();
            try
            {
                var entity = await funRoomService.GetEntity(id);
                if (entity != null)
                {
                    if (entity.UnitId != operatorinfo.UnitId)
                    {
                        obj.Tag = 0;
                        obj.Message = "非法操作，禁止操作其他单位数据信息。";
                        return obj;
                    }
                    if (entity.SafeguardUserId != operatorinfo.UserId)
                    {
                        obj.Tag = 0;
                        obj.Message = "无权操作，当前实验室你不是维护人员。";
                        return obj;
                    }
                    //获取附件
                    if (catelogry != FileCategoryEnum.BriefInfo.ParseToInt() && catelogry != FileCategoryEnum.ManageSystem.ParseToInt())
                    {
                        obj.Tag = 0;
                        obj.Message = "非法操作，当前实验室禁止上传该文件。";
                        return obj;
                    }
                    var attachment = await attachmentService.GetEntity(attachmentid);
                    if (attachment != null)
                    {
                        if (attachment.FileCategory == catelogry && attachment.BaseCreatorId == operatorinfo.UserId)
                        {
                            await db.BeginTrans();
                            attachment.ObjectId = id;
                            entity.FillInStatuz = FillInStatuzEnum.FilledIn.ParseToInt();
                            await attachmentService.SaveForm(attachment);
                            await funRoomService.SaveForm(entity);
                            obj.Tag = 1;
                            obj.Message = "保存成功";
                            await db.CommitTrans();
                        }
                        else
                        {
                            obj.Tag = 0;
                            obj.Message = "保存失败，非本人上传附件不可保存。";
                        }
                    }
                    else
                    {
                        obj.Tag = 0;
                        obj.Message = "保存失败，附件未上传。";
                    }
                }
                else
                {
                    obj.Tag = 0;
                    obj.Message = "当前数据不存在，请刷新重试。";
                }
                obj.Data = entity.Id.ParseToString();
                obj.Tag = 1;
            }
            catch (Exception ex)
            {
                obj.Tag = 0;
                obj.Message = "保存附件异常，请联系客服协助处理。";
                await db.RollbackTrans();
                throw new Exception("程序出现异常，异常信息为：" + ex.Message);
            }
            return obj;
        }
        /// <summary>
        /// 删除实验室，制度管理上报附件
        /// </summary>
        /// <param name="id">附件Id</param>
        /// <returns></returns>
        public async Task<TData> DeleteReportForm(long id)
        {
            TData obj = new TData();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            Repository db = funRoomService.BaseRepository();
            try
            {
                var attachEntity = await attachmentService.GetEntity(id);
                if (attachEntity == null)
                {
                    obj.Tag = 0;
                    obj.Message = "该附件已不存在。";
                    return obj;
                }
                var funroomEntity = await funRoomService.GetEntity(attachEntity.ObjectId.Value);
                if (funroomEntity == null && funroomEntity.SafeguardUserId == operatorinfo.UserId)
                {
                    obj.Tag = 0;
                    obj.Message = "该实验室非你管理，禁止操作。";
                    return obj;
                }
                //查询是否还存在文件。
                AttachmentListParam atttachmentParam = new AttachmentListParam();
                var filecategorylist = new List<int>();
                filecategorylist.Add(FileCategoryEnum.BriefInfo.ParseToInt());
                filecategorylist.Add(FileCategoryEnum.ManageSystem.ParseToInt());
                atttachmentParam.ListFileCategory = filecategorylist;
                atttachmentParam.ObjectId = funroomEntity.Id;
                var attachmentlist = await attachmentService.GetList(atttachmentParam);

                await db.BeginTrans();
                attachEntity.BaseIsDelete = 1;
                await attachmentService.UpdateTransForm(attachEntity, db);
                if (attachmentlist == null || attachmentlist.Count <= 1)
                {
                    funroomEntity.FillInStatuz = FillInStatuzEnum.NotReported.ParseToInt();
                    await funRoomService.SaveTransForm(funroomEntity, db);
                }

                await db.CommitTrans();
                obj.Tag = 1;
                obj.Message = "删除成功。";
            }
            catch (Exception ex)
            {
                obj.Tag = 0;
                obj.Message = "删除附件异常，请联系客服协助处理。";
                await db.RollbackTrans();
                throw new Exception("程序出现异常，异常信息为：" + ex.Message);
            }
            return obj;
        }

        /// <summary>
        /// 保存更换管理员功能。
        /// </summary>
        /// <param name="id">附件Id</param>
        /// <returns></returns>
        public async Task<TData> SaveSafeguardForm(long id, long safeguarduserid)
        {
            TData obj = new TData();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            Repository db = funRoomService.BaseRepository();
            try
            {
                var funroomEntity = await funRoomService.GetEntity(id);
                if (funroomEntity == null || funroomEntity.UnitId != operatorinfo.UnitId)
                {
                    obj.Tag = 0;
                    obj.Message = "当前实验室已不存在，请刷新重新操作。";
                    return obj;
                }
                //查询用户
                var userEntity =await userService.GetUserSingle(safeguarduserid);
                if (userEntity==null || userEntity.UnitId!= operatorinfo.UnitId)
                {
                    obj.Tag = 0;
                    obj.Message = "新管理员不存在，请刷新重新操作。";
                    return obj;
                }


                funroomEntity.SafeguardUserId = safeguarduserid;
                funroomEntity.BaseModifyTime = DateTime.Now;
                funroomEntity.BaseModifierId = operatorinfo.UserId;
                await db.BeginTrans();
                await funRoomService.SaveTransForm(funroomEntity, db);
                long belongid = 16508640061130100 + RoleEnum.LaboratoryManager.ParseToInt();
                if (!userEntity.RoleIds.Contains(belongid.ToString()))
                {
                    //不存在添加
                    await userBelongService.SaveTransForm(new Entity.OrganizationManage.UserBelongEntity() { UserId = safeguarduserid, BelongId = belongid, BelongType = 2 }, db);
                }
                await db.CommitTrans();
                obj.Tag = 1;
                obj.Message = "更换成功。";
            }
            catch (Exception ex)
            {
                obj.Tag = 0;
                obj.Message = "更换数据异常，请联系客服协助处理。";
                await db.RollbackTrans();
                throw new Exception("程序出现异常，异常信息为：" + ex.Message);
            }
            return obj;
        }

        public async Task<TData> CreateQRCodeForm(string ids)
        {
            TData obj = new TData();
            if (ids.IsEmpty())
            {
                obj.Tag = 0;
                obj.Message = "非法操作，请从页面点击操作！";
                return obj;
            }
            long[] idArr = TextHelper.SplitToArray<long>(ids, ',');
            if (idArr == null && idArr.Count() == 0)
            {
                obj.Tag = 0;
                obj.Message = "非法操作，请从页面点击操作！";
                return obj;
            }
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            FunRoomListParam param = new FunRoomListParam { UnitId = operatorinfo.UnitId.Value, Ids = ids };
            var list = await funRoomService.GetList(param);
            List<FunRoomEntity> updateQRCodeList = new List<FunRoomEntity>();
            if (list != null && list.Count > 0)
            {
                var listSnCode = await snCodeService.GetAndLockSnCodes("R", list.Count, operatorinfo.UnitId.Value);


                foreach (var item in list)
                {
                    if (idArr.Contains(item.Id.Value))
                    {
                        if (string.IsNullOrEmpty(item.QRCode))
                        {
                            if (listSnCode.Count > 0)
                            {
                                item.QRCode = listSnCode[0].Sn6;
                                updateQRCodeList.Add(item);
                                listSnCode.RemoveAt(0);
                            }
                        }
                    }
                }
            }
            if (updateQRCodeList.Count > 0)
            {
                foreach (var item in updateQRCodeList)
                {
                    await funRoomService.SaveForm(item);
                }
                obj.Tag = 1;
                obj.Message = "生成成功。";
            }
            else
            {
                obj.Tag = 0;
                obj.Message = "当前实验（专用）室已生成二维码或不存在。";
            }
            return obj;
        }


        public async Task<TData<FunRoomEntity>> GetQRCodeJson(long id)
        {
            TData<FunRoomEntity> obj = new TData<FunRoomEntity>();
            FunRoomListParam param = new FunRoomListParam() { Id = id};
            Pagination pagination = new Pagination();
            List<FunRoomEntity> list = new List<FunRoomEntity>();
            list = await funRoomService.GetPageList(param, pagination);
            if(list.Count() > 0)
            {
                var entity = list[0];
                obj.Data = entity;
                obj.Tag = 1;
                var attachmentList = await attachmentService.GetList(new AttachmentListParam() { FileCategory = FileCategoryEnum.SceneImg.ParseToInt(), ObjectId = obj.Data.Id });
                if (attachmentList != null && attachmentList.Count > 0)
                {
                    obj.Data.AttachmentList = attachmentList;
                }
            }
            return obj;
        }
        public async Task<TData<FunRoomEntity>> GetByQRCodeJson(string qrCode)
        {
            TData<FunRoomEntity> obj = new TData<FunRoomEntity>();
            FunRoomListParam param = new FunRoomListParam() { QRCode = qrCode };
            Pagination pagination = new Pagination();
            List<FunRoomEntity> list = new List<FunRoomEntity>();
            list = await funRoomService.GetPageList(param, pagination);
            if (list.Count() > 0)
            {
                var entity = list[0];
                obj.Data = entity;
                obj.Tag = 1;
                var attachmentList = await attachmentService.GetList(new AttachmentListParam() { FileCategory = FileCategoryEnum.SceneImg.ParseToInt(), ObjectId = obj.Data.Id });
                if (attachmentList != null && attachmentList.Count > 0)
                {
                    obj.Data.AttachmentList = attachmentList;
                }
            }
            return obj;
        }

        #endregion

        #region 私有方法
        /// <summary>
        /// 扫码登记获取默认学段
        /// 逻辑：当专用室适用学段只有一个时，默认该学段
        ///       当专用室适用学段为多个时，查找当前用户的任课学段，
        ///       当任课学段只有一个时，判断该任课学段是否存在于专用室适用学段中，
        ///       如果存在就默认该学段，不存在就为0
        /// </summary>
        /// <param name="schoolStagez">专用室适用学段</param>
        /// <param name="operatorinfo">登录信息</param>
        /// <returns></returns>
        private async Task<int> GetSchoolStageId(string schoolStagez, OperatorInfo operatorinfo)
        {
            int schoolStageId = 0;
            if (schoolStagez.IndexOf(",") == -1)
            {
                schoolStageId = schoolStagez.ParseToInt();
            }
            else
            {
                var userClasslist = await userClassInfoService.GetList(new UserClassInfoListParam
                {
                    IsCurrentUnit = IsEnum.Yes.ParseToInt(),
                    UnitId = operatorinfo.UnitId,
                    UserId = operatorinfo.UserId
                });
                if (userClasslist.Count > 0)
                {
                    string userstage = userClasslist.FirstOrDefault().SchoolStageIdz;
                    if (userstage.IndexOf(",") == -1 && schoolStagez.Contains(userstage))
                    {
                        schoolStageId = userstage.ParseToInt();
                    }
                }
            }
            return schoolStageId;
        }

        /// <summary>
        /// 根据字典值与字典类型查找字典数据
        /// </summary>
        /// <param name="dictionaryId"></param>
        /// <param name="typeCode"></param>
        /// <returns></returns>
        private async Task<string> GetDictionaryName(int dictionaryId, string typeCode)
        {
            if (dictionaryId > 0)
            {
                var entity = await dictionaryService.GetEntityByDictionaryId(dictionaryId, typeCode);
                return entity != null ? entity.DicName : string.Empty;
            }
            else
                return string.Empty;
        }

        /// <summary>
        /// 获取当前时间的上课节次
        /// </summary>
        /// <param name="operatorinfo"></param>
        /// <returns></returns>
        private async Task<(long, string)> GetSection(OperatorInfo operatorinfo)
        {
            var entity = await courseSectionService.GetSectionByCurrentTime(operatorinfo.UnitId ?? 0);
            if (entity != null)
                return (entity.Id ?? 0, entity.SectionName);
            else
                return (0, string.Empty);
        }
        #endregion
    }
}
