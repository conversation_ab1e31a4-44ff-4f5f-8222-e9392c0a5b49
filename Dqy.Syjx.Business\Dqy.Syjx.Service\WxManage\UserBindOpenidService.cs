﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.WxManage;
using Dqy.Syjx.Model.Param.WxManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Model.Param.ExperimentTeachManage;
using Dqy.Syjx.Entity.ExperimentTeachManage;

namespace Dqy.Syjx.Service.WxManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2022-01-13 17:29
    /// 描 述：服务类
    /// </summary>
    public class UserBindOpenidService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<UserBindOpenidEntity>> GetList(UserBindOpenidListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            retur
        }

        public async Task<List<UserBindOpenidEntity>> GetList(UserBindOpenidListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        /// <summary>
        /// 获取微信绑定用户
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<List<UserBindOpenidEntity>> GetUserBindList(UserBindOpenidListParam param)
        {
            StringBuilder sql = new StringBuilder();
            var expression = ListFilter(param, sql);
            var list = await this.BaseRepository().FindList<UserBindOpenidEntity>(sql.ToString(), expression.ToArray());
            return list.ToList();
        }

        public async Task<List<UserBindOpenidEntity>> GetPageList(UserBindOpenidListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<UserBindOpenidEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<UserBindOpenidEntity>(id);
        }

        /// <summary>
        /// 查询登录信息
        /// </summary>
        /// <param name="token"></param> 
        public async Task<QrcodeLoginEntity> QrcodeLogin(string token)
        {
            string Sql = $"select Id, QrCodeToken, RegDate, Statuz, OpenId, UserId from wx_QrcodeLogin where QrCodeToken = '{token}' order by Id desc";

            var list = await this.BaseRepository().FindList<QrcodeLoginEntity>(Sql);
            if (list.Count() > 0)
            {
                return list.FirstOrDefault();
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 更新登录信息
        /// </summary>
        /// <param name="token"></param>
        /// <param name="userId"></param>
        /// <param name="openId"></param>
        /// <returns>
        /// 0:二维码信息错误，请重新扫码。如果您还未在小程序登录过，请点击左上角主页按钮，先登录一次小程序，之后就可以使用微信扫码登录了
        /// 1:登录成功
        /// 2:已经过期，请在电脑端刷新二维码后，重新扫码
        /// </returns>
        public async Task<int> UpdateQrLogin(string token, long userId, string openId)
        {
            string Sql = $"select Id, QrCodeToken, RegDate, Statuz, OpenId, UserId from wx_QrcodeLogin where QrCodeToken = '{token}' order by Id desc";

            var list = await this.BaseRepository().FindList<QrcodeLoginEntity>(Sql);
            if (list.Count() > 0)
            {
                var entity = list.LastOrDefault();
                if (entity.Statuz == 0)
                {
                    entity.Statuz = 1;
                    entity.UserId = userId;
                    entity.OpenId = openId;
                    await this.BaseRepository().Update(entity);
                    return 1;
                }
                else
                {
                    entity.Statuz = 2;
                    await this.BaseRepository().Update(entity);
                    return 2;
                }
            }
            else
            {
                return 0;
            }
        }

        /// <summary>
        /// 将信息置位失效状态
        /// </summary>
        /// <param name="token"></param>
        public async Task DisableQrcodeLogin(string token)
        {
            string Sql = $"select Id, QrCodeToken, RegDate, Statuz, OpenId, UserId from wx_QrcodeLogin where QrCodeToken = '{token}' order by Id desc";

            var list = await this.BaseRepository().FindList<QrcodeLoginEntity>(Sql);
            if (list.Count() > 0)
            {
                var wql = list.OrderBy(m => m.Id).FirstOrDefault();
                if (wql.Statuz == 0)
                {
                    wql.Statuz = 2;
                    await this.BaseRepository().Update(wql);
                }
            }
        }
        #endregion

        #region 提交数据
        /// <summary>
        /// 保存扫描二维码token信息
        /// </summary>
        /// <param name="token"></param> 
        public async Task SaveQrcodeLogin(QrcodeLoginEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveForm(UserBindOpenidEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(UserBindOpenidEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update wx_UserBindOpenid set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
                strSql = $"update wx_UserBindOpenid set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<UserBindOpenidEntity, bool>> ListFilter(UserBindOpenidListParam param)
        {
            var expression = LinqExtensions.True<UserBindOpenidEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (!param.OpenId.IsEmpty())
                {
                    expression = expression.And(t => t.OpenId == param.OpenId);
                }
                if(param.UserId > 0)
                {
                    expression = expression.And(t => t.UserId == param.UserId);
                }
            }
            return expression;
        }

        private List<DbParameter> ListFilter(UserBindOpenidListParam param, StringBuilder strSql)
        {
            strSql.Append(@$"SELECT w.Id,UserId,UnitId,OpenId,RegTime,IsDefaultLogin,UnionId,WxAccount,u.UserName,u.RealName
                                FROM wx_UserBindOpenid w INNER JOIN  SysUser u ON w.UserId = u.Id
                                WHERE 1 = 1");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.Statuz > -1)
                {
                    strSql.Append(" AND w.Statuz =@Statuz ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Statuz", param.Statuz));
                }
                if (!string.IsNullOrEmpty(param.OpenId))
                {
                    strSql.Append(" AND w.OpenId = @OpenId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@OpenId", param.OpenId));
                }
            }
            return parameter;
        }
        #endregion
    }
}

