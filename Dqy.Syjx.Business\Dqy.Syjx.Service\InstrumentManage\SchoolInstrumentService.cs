﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.InstrumentManage;
using Dqy.Syjx.Model.Param.InstrumentManage;
using Dqy.Syjx.Entity.BusinessManage;
using Dqy.Syjx.Service.BusinessManage;
using Dqy.Syjx.Enum.InstrumentManage;
using System.Data;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Entity.OrganizationManage;
using Dqy.Syjx.Model.Param.EvaluateManage;
using Dqy.Syjx.Service.OrganizationManage;

namespace Dqy.Syjx.Service.InstrumentManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-11-01 15:39
    /// 描 述：服务类
    /// </summary>
    public class SchoolInstrumentService : RepositoryFactory
    {
        #region 获取数据
        public async Task<List<SchoolInstrumentEntity>> GetList()
        {
            var expression = LinqExtensions.True<SchoolInstrumentEntity>();
            var list = await this.BaseRepository().FindList(expression.And(f => f.BaseIsDelete == 0 && f.Statuz == 30 && (f.CourseId == 1005002 || f.CourseId == 1005003 || f.CourseId == 1005004 || f.CourseId == 1005005)));
            //var list = await this.BaseRepository().FindList(expression.And(f => f.BaseIsDelete == 0 && f.Statuz == 30 ));
            return list.ToList();
        }

        public async Task<List<SchoolInstrumentEntity>> GetList(SchoolInstrumentListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<SchoolInstrumentEntity>> GetPageList(SchoolInstrumentListParam param, Pagination pagination)
        {
            StringBuilder sql = new StringBuilder();
            var expression = ListFilter(param, sql);
            IEnumerable<SchoolInstrumentEntity> list = null;
            if (pagination!=null)
            {
                list = await this.BaseRepository().FindList<SchoolInstrumentEntity>(sql.ToString(), expression.ToArray(), pagination);
            }
            else
            {
                list = await this.BaseRepository().FindList<SchoolInstrumentEntity>(sql.ToString(), expression.ToArray());
            }
            return list.ToList();
        }

        /// <summary>
        /// 获取总计数量
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<decimal> GetTotalSum(SchoolInstrumentListParam param)
        {
            StringBuilder sql = new StringBuilder();
            var expression = ListFilter(param, sql, " ISNULL(SUM(StockNum) ,0) AS StockNum ");
            DataTable dt = await this.BaseRepository().FindTable(sql.ToString(), expression.ToArray());
            decimal total = 0;
            if (dt != null && dt.Rows.Count > 0)
            {
                total = dt.Rows[0]["StockNum"].ParseToDecimal();
            }
            return total;
        }

        public async Task<decimal> GetNumSum(SchoolInstrumentListParam param)
        {
            StringBuilder sql = new StringBuilder();
            var expression = ListFilter(param, sql, " ISNULL(SUM(Num) ,0) AS Num ");
            DataTable dt = await this.BaseRepository().FindTable(sql.ToString(), expression.ToArray());
            decimal total = 0;
            if (dt != null && dt.Rows.Count > 0)
            {
                total = dt.Rows[0]["Num"].ParseToDecimal();
            }
            return total;
        }

        public async Task<SchoolInstrumentEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<SchoolInstrumentEntity>(id);
        }

        /// <summary>
        /// 获取仪器达标模板
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<IEnumerable<SchoolInstrumentEntity>> GetInstrumentEvStandardTemplate(long id)
        {
            StringBuilder sql = new StringBuilder();
            sql.Append($@"
                SELECT
                DISTINCT
                Id,Stage,Course,Code,Name,Model,UnitName FROM
                (
                    SELECT  EV.Id,D1.DicName AS Stage ,D2.DicName AS Course ,
		                    ISNULL(ISD2.Code ,ISD.Code) AS Code ,ISD.Name ,ISNULL(ISD2.Model ,'') AS Model ,
		                    ISNULL(ISD2.UnitName ,ISD.UnitName) AS UnitName
                    FROM  eq_InstrumentEvaluateProjectVersion AS EV
                    INNER JOIN  sys_static_dictionary AS D1 ON EV.SchoolStage = D1.DictionaryId AND D1.TypeCode = '{DicTypeCodeEnum.SchoolStage.ParseToInt()}'
                    INNER JOIN  sys_static_dictionary AS D2 ON EV.DictionaryId1005 = D2.DictionaryId AND D2.TypeCode = '{DicTypeCodeEnum.Course.ParseToInt()}'
                    INNER JOIN eq_InstrumentEvaluateList AS EL ON EV.EvaluateStandardId = EL.EvaluateStandardId
                    INNER JOIN eq_InstrumentEvaluateRelation AS ER ON EL.Id = ER.EvaluateListId
                    INNER JOIN  eq_InstrumentStandard AS ISD ON ER.ActualCode = ISD.Code AND ISD.ClassType = 1 AND ISD.BaseIsDelete = 0
                    LEFT JOIN  eq_InstrumentStandard AS ISD2 ON ISD.Id = ISD2.Pid AND ISD2.ClassType = 2 AND ISD2.BaseIsDelete = 0
                    WHERE EL.BaseIsDelete = 0
                ) A WHERE  Id = {id}
            ");
            return await this.BaseRepository().FindList<SchoolInstrumentEntity>(sql.ToString());
        }

        /// <summary>
        /// 获取仪器录入列表信息
        /// </summary>
        /// <param name="statuz"></param>
        /// <param name="userId"></param>
        /// <param name="schoolId"></param>
        /// <returns></returns>
        public async Task<List<SchoolInstrumentEntity>> GetSchoolInstrumentList(int statuz, long userId, long schoolId)
        {
            StringBuilder sql = new StringBuilder();
            sql.Append($@"SELECT SI.Id, SI.BaseIsDelete, SI.BaseCreateTime, SI.BaseModifyTime, SI.BaseCreatorId, SI.BaseModifierId,
	                       SI.BaseVersion, InstrumentStandardId, Name, Code, ModelStandardId, Model, UnitName,
	                       InputNum, Num, StockNum, LendNum, ScrapNum, OutNum, Price, Stage, StageId, Course,
	                       CourseId, PurchaseDate, WarrantyMonth, SupplierName, Brand, Statuz, SchoolId, InputType,
	                       CupboardId, Floor, SourceSchoolInstrumentId, ModelCode, FunRoomId, IsSelfMade,ISNULL(A.Id,0) AS AttachId
                    FROM  eq_SchoolInstrument AS SI
                    LEFT JOIN  bn_Attachment AS A ON SI.Id = A.ObjectId AND A.IsDelete = 0 AND A.BaseIsDelete = 0 AND A.FileCategory = 1200
                    WHERE SI.BaseIsDelete = 0 AND SI.BaseCreatorId = {userId} AND SchoolId = {schoolId} AND Statuz = {statuz}");
            var list = await this.BaseRepository().FindList<SchoolInstrumentEntity>(sql.ToString());
            return list.ToList();
        }


        /// <summary>
        /// 统计库存数量
        /// </summary>
        /// <param name="schoolId"></param>
        /// <param name="stageId"></param>
        /// <param name="courseId"></param>
        /// <param name="code"></param>
        /// <returns></returns>
        //public async Task<decimal> GetSchoolStockNum(long schoolId, int stageId,int courseId,string code)
        //{
        //    StringBuilder sql = new StringBuilder();
        //    sql.Append($@"");
        //}
        #endregion

        #region 提交数据

        ///// <summary>
        ///// 更新数据(更具指定列更新。)
        ///// </summary>
        ///// <param name="entity">更新的实体</param>
        ///// <param name="fields">需要更新的字段集合</param>
        ///// <returns></returns>
        public async Task UpdateForm(SchoolInstrumentEntity entity, List<string> fields)
        {
            if (fields!=null)
            {
                await entity.Modify();
                fields.Add("BaseModifyTime");
                fields.Add("BaseModifierId");
            }
            await this.BaseRepository().Update(entity, fields);
        }

        public async Task SaveForm(SchoolInstrumentEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(SchoolInstrumentEntity entity, Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update eq_SchoolInstrument set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
                strSql = $"update eq_SchoolInstrument set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }

        public async Task BatchEditCourse(string ids, int courseId, string courseName, string stageId, string stageName)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");

            // 使用参数化查询和跨数据库兼容的 IN 语法
            var parameters = new List<DbParameter>
            {
                DbParameterExtension.CreateDbParameter("@CourseId", courseId),
                DbParameterExtension.CreateDbParameter("@CourseName", courseName),
                DbParameterExtension.CreateDbParameter("@StageId", stageId),
                DbParameterExtension.CreateDbParameter("@StageName", stageName)
            };

            string strSql = $"UPDATE eq_SchoolInstrument SET CourseId = @CourseId, Course = @CourseName, StageId = @StageId, Stage = @StageName WHERE Id IN ({ids})";
            await this.BaseRepository().ExecuteBySql(strSql, parameters.ToArray());
        }

        public async Task BatchEditRoom(string ids, long funRoomId, long cupboardId, string floor)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql =
                $" update eq_SchoolInstrument set FunRoomId = {funRoomId} ,CupboardId = {cupboardId} ,Floor = '{floor}' " +
                $" where Id in ({ids}); ";
            strSql +=
                $" update eq_SchoolInstrument set Statuz = {InstrumentInputStatuzEnum.WaitInputStorage.ParseToInt()} " +
                $" where Id in ({ids}) " +
                $" and Statuz = {InstrumentInputStatuzEnum.WaitInputRoom.ParseToInt()};";

            await this.BaseRepository().ExecuteBySql(strSql);
        }

        public async Task BatchInputStorage(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");

            // 使用参数化查询和跨数据库兼容的 IN 语法
            var parameters = new List<DbParameter>
            {
                DbParameterExtension.CreateDbParameter("@Statuz", InstrumentInputStatuzEnum.AlreadyInputStorage.ParseToInt())
            };

            string strSql = $"UPDATE eq_SchoolInstrument SET Statuz = @Statuz WHERE Id IN ({ids})";
            await this.BaseRepository().ExecuteBySql(strSql, parameters.ToArray());
        }

        public async Task BatchRevoke(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");

            // 使用参数化查询和跨数据库兼容的 IN 语法
            var parameters = new List<DbParameter>
            {
                DbParameterExtension.CreateDbParameter("@NewStatuz", InstrumentInputStatuzEnum.WaitInputStorage.ParseToInt()),
                DbParameterExtension.CreateDbParameter("@OldStatuz", InstrumentInputStatuzEnum.AlreadyInputStorage.ParseToInt())
            };

            string sql = $"UPDATE eq_SchoolInstrument SET Statuz = @NewStatuz WHERE Id IN ({ids}) AND Statuz = @OldStatuz";
            await this.BaseRepository().ExecuteBySql(sql, parameters.ToArray());
        }
        #endregion

        #region 私有方法
        private Expression<Func<SchoolInstrumentEntity, bool>> ListFilter(SchoolInstrumentListParam param)
        {
            var expression = LinqExtensions.True<SchoolInstrumentEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (!param.UnitId.IsNullOrZero())
                {
                    expression = expression.And(t => t.SchoolId == param.UnitId);
                }
                if (!param.CourseId.IsNullOrZero() && param.CourseId > -1)
                {
                    expression = expression.And(t => t.SchoolId == param.UnitId);
                }
                if (param.ListType == 3)
                {
                    if (param.Statuz > -1)
                    {
                        expression = expression.And(t => t.Statuz >= param.Statuz);
                    }
                }
                else
                {
                    if (param.Statuz > -1)
                    {
                        expression = expression.And(t => t.Statuz == param.Statuz);
                    }
                }
                if (!param.KeyWord.IsEmpty())
                {
                    expression = expression.And(t => t.Code.Contains(param.KeyWord.Trim()) || t.Name.Contains(param.KeyWord.Trim()));
                }
                if (!param.SchoolId.IsNullOrZero())
                {
                    expression = expression.And(t => t.SchoolId == param.SchoolId);
                }
                if (param.UserId > 0)
                {
                    expression = expression.And(t => t.BaseCreatorId == param.UserId);
                }
                if (param.IsShowAlreadyInputStorage.HasValue)
                {
                    if (param.IsShowAlreadyInputStorage == 0)
                    {
                        expression = expression.And(t => t.Statuz != InstrumentInputStatuzEnum.AlreadyInputStorage.ParseToInt());
                    }
                }
                if (param.FunRoomId.HasValue && param.FunRoomId.Value > 0)
                {
                    expression = expression.And(t => t.FunRoomId == param.FunRoomId.Value);

                    if (param.CupboardId.HasValue && param.CupboardId.Value == -2)
                    {
                        expression = expression.And(t => t.CupboardId == 0);
                    }
                }

                if (param.CupboardId.HasValue && param.CupboardId.Value > 0)
                {
                    expression = expression.And(t => t.CupboardId == param.CupboardId.Value);
                }

            }
            return expression;
        }

        private List<DbParameter> ListFilter(SchoolInstrumentListParam param, StringBuilder sql, string funSql = "*")
        {
            sql.Append(@$" SELECT {funSql} From (
                            SELECT  A.Id ,
                                    A.BaseIsDelete ,A.BaseCreateTime ,A.BaseModifyTime ,A.BaseCreatorId ,A.BaseModifierId ,A.BaseVersion ,
                                    A.InstrumentStandardId ,A.Name ,A.Code ,A.ModelStandardId ,ISNULL(A.Model ,'') AS Model ,A.UnitName ,A.InputNum ,A.Num ,A.Price ,
                                    A.StockNum ,A.LendNum ,A.ScrapNum ,A.OutNum ,
                                    A.Stage ,A.StageId ,A.Course ,A.PurchaseDate ,A.WarrantyMonth ,A.SupplierName ,A.Brand ,
                                    A.Statuz ,A.SchoolId ,A.InputType ,A.CupboardId ,A.Floor ,A.SourceSchoolInstrumentId ,A.IsSelfMade ,
                                    A.CourseId ,ISNULL(B.Name ,'') AS Cupboard , ISNULL(B.Sort,0) AS Sort ,
                                    ISNULL(A.FunRoomId ,0) AS FunRoomId ,ISNULL(C.Name,'') AS FunRoom ,
                                    S.VarietyAttribute ,C.SafeguardUserId ,D.DicName AS VarietyAttributeName ,S.IsDangerChemical ,
                                    U.Name AS SchoolName,A.ModelCode,A.QRCode,SU.UserName,SU.RealName,U2.Id AS CountyId
                                    FROM  eq_SchoolInstrument AS A
                                    INNER JOIN  up_Unit AS U ON A.SchoolId = U.Id
                                    INNER JOIN  up_UnitRelation AS UR ON U.Id = UR.ExtensionObjId AND UR.ExtensionType = 3
                                    INNER JOIN  up_UnitRelation AS UR2 ON UR.UnitId = UR2.ExtensionObjId AND UR2.ExtensionType = 3
                                    INNER JOIN  up_Unit AS U2 ON UR.UnitId = U2.Id
                                    INNER JOIN  eq_InstrumentStandard AS S ON A.InstrumentStandardId = S.Id
                                    INNER JOIN  sys_static_dictionary AS D ON S.VarietyAttribute = D.DictionaryId AND D.BaseIsDelete = 0 AND D.TypeCode = '1060'
                                    INNER JOIN SysUser AS SU ON A.BaseCreatorId = SU.Id
                                    LEFT JOIN  bn_FunRoom AS C ON A.FunRoomId = C.Id
                                    LEFT JOIN  bn_Cupboard AS B ON A.CupboardId = B.Id
                                    WHERE A.BaseIsDelete = 0
                            ) as T WHERE StockNum > 0  ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (!param.UnitId.IsNullOrZero())
                {
                    sql.Append(" AND SchoolId = @UnitId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                if(param.CountyId != null && param.CountyId > 0)
                {
                    sql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
                if (!param.SafeguardUserId.IsNullOrZero())
                {
                    //sql.Append(GetUserSchoolStageSubjectSql(param.SchoolStageIdz, param.SubjectIdz));
                    sql.Append(" AND SafeguardUserId = @SafeguardUserId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SafeguardUserId", param.SafeguardUserId));
                }

                if (param.SafeguardUserIdList != null && param.SafeguardUserIdList.Count > 0)
                {
                    sql.Append($" AND ({string.Join(" OR ", param.SafeguardUserIdList.Select(m => string.Format("SafeguardUserId = {0}", m)))})");
                }
                if (param.CodeList != null && param.CodeList.Count > 0)
                {
                    sql.Append($" AND ({string.Join(" OR ", param.CodeList.Select(m => string.Format("Code = {0}", m)))})");
                }
                if (!param.UserId.IsNullOrZero())
                {
                    sql.Append(" AND BaseCreatorId = @BaseCreatorId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@BaseCreatorId", param.UserId));
                }
                if (!param.CourseId.IsNullOrZero())
                {
                    sql.Append($" AND CourseId = @CourseId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (param.ListType == 3)
                {
                    if (!param.Statuz.IsNullOrZero() && param.Statuz > -1)
                    {
                        sql.Append(" AND Statuz > @Statuz");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@Statuz", param.Statuz));
                    }
                }
                else
                {

                    if (!param.Statuz.IsNullOrZero() && param.Statuz > -1)
                    {
                        sql.Append(" AND Statuz = @Statuz");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@Statuz", param.Statuz));
                    }
                    if (param.Statuz == -2)
                    {
                        sql.Append($" AND Statuz > 0");
                    }

                }
                if (!param.KeyWord.IsEmpty())
                {
                    sql.Append($" AND ( Code LIKE '%{param.KeyWord}%' OR Name LIKE '%{param.KeyWord}%' ");

                    if(param.UnitId == 0)
                    {
                        sql.Append($" OR RealName LIKE '%{param.KeyWord}%' ");
                    }
                    sql.Append(" ) ");
                }
                if (param.IsShowAlreadyInputStorage.HasValue)
                {
                    if (param.IsShowAlreadyInputStorage == 0)
                    {
                        sql.Append($" AND Statuz <> {InstrumentInputStatuzEnum.AlreadyInputStorage.ParseToInt()}");
                    }
                }
                if (!param.StoragePlace.IsEmpty() && !"-1".Equals(param.StoragePlace))
                {
                    if (param.StoragePlace.IndexOf(',') != -1)
                    {
                        sql.Append(" AND CupboardId = @CupboardId");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@CupboardId", param.StoragePlace.Split(',')[1]));
                    }
                    else
                    {
                        sql.Append(" AND FunRoomId = @FunRoomId");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@FunRoomId", param.StoragePlace));
                    }
                }
                if (param.StartDate.HasValue)
                {
                    sql.Append(" AND PurchaseDate >= @StartDate");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@StartDate", param.StartDate));
                }
                if (param.EndDate.HasValue)
                {
                    sql.Append(" AND PurchaseDate <= @EndDate");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@EndDate", param.EndDate.Value.AddDays(1).AddSeconds(-1)));
                }
                if (!param.InstrumentAttribute.IsNullOrZero())
                {
                    if (param.InstrumentAttribute == 1)
                    {
                        //仪器
                        sql.Append($" AND VarietyAttribute <> '{VarietyAttributeEnum.LowValueConsumable.ParseToInt()}' AND VarietyAttribute <> '{VarietyAttributeEnum.Reagent.ParseToInt()}' ");
                    }
                    else if (param.InstrumentAttribute == 2)
                    {
                        //耗材
                        sql.Append($" AND (VarietyAttribute = '{VarietyAttributeEnum.LowValueConsumable.ParseToInt()}' OR VarietyAttribute = '{VarietyAttributeEnum.Reagent.ParseToInt()}')");
                    }
                }
                if (!param.VarietyAttribute.IsNullOrZero())
                {
                    sql.Append(" AND VarietyAttribute = @VarietyAttribute");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@VarietyAttribute", param.VarietyAttribute));
                }
                if (param.IsSelfMade.HasValue && param.IsSelfMade > -1)
                {
                    sql.Append(" AND IsSelfMade = @IsSelfMade");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@IsSelfMade", param.IsSelfMade));
                }
                if (!param.SchoolId.IsNullOrZero())
                {
                    sql.Append(" AND SchoolId = @SchoolId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
                }
                if (!param.QRCode.IsEmpty())
                {
                    sql.Append(" AND QRCode = @QRCode");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@QRCode", param.QRCode));
                }
            }
            return parameter;
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<List<SchoolInstrumentEntity>> GetSchoolInstrumentPageList(SchoolInstrumentListParam param, Pagination pagination)
        {
            int beginNumber = (pagination.PageIndex - 1) * pagination.PageSize;
            int endNumber = pagination.PageIndex * pagination.PageSize;
            if (pagination.Sort.ToUpper().IndexOf("ASC") > -1 || pagination.Sort.ToUpper().IndexOf("DESC") > -1)
            {
                pagination.SortType = "";
            }

            StringBuilder sql = new StringBuilder();
            if (GlobalContext.SystemConfig.DBProvider.Equals("Dm") || GlobalContext.SystemConfig.DBProvider.Equals("MySql"))
            {
                sql.Append(@" SELECT * FROM ( SELECT ");
            }
            else
            {
                sql.AppendFormat(" SELECT * FROM ( SELECT    ROW_NUMBER() OVER ( ORDER BY {0} {1}) AS ROWNUM ,", pagination.Sort, pagination.SortType);
            }



            sql.AppendFormat(@"  *
                                      FROM (SELECT  A.Id ,
            A.BaseIsDelete ,A.BaseCreateTime ,A.BaseModifyTime ,A.BaseCreatorId ,A.BaseModifierId ,A.BaseVersion ,
            A.InstrumentStandardId ,A.Name ,A.Code ,A.ModelStandardId ,ISNULL(A.Model ,'') AS Model ,A.UnitName ,A.InputNum ,A.Num ,A.Price ,
            A.StockNum ,A.LendNum ,A.ScrapNum ,A.OutNum ,
            A.Stage ,A.StageId ,A.Course ,A.PurchaseDate ,A.WarrantyMonth ,A.SupplierName ,A.Brand ,
            A.Statuz ,A.SchoolId ,A.InputType ,A.CupboardId ,A.Floor ,A.SourceSchoolInstrumentId ,A.IsSelfMade ,
            A.CourseId ,ISNULL(B.Name ,'') AS Cupboard , ISNULL(B.Sort,0) AS Sort ,
            ISNULL(A.FunRoomId ,0) AS FunRoomId ,ISNULL(C.Name,'') AS FunRoom ,
            S.VarietyAttribute ,C.SafeguardUserId ,D.DicName AS VarietyAttributeName ,S.IsDangerChemical ,
            U.Name AS SchoolName,A.ModelCode,A.QRCode,SU.UserName,SU.RealName
            FROM  eq_SchoolInstrument AS A
            INNER JOIN  up_Unit AS U ON A.SchoolId = U.Id
            INNER JOIN  eq_InstrumentStandard AS S ON A.InstrumentStandardId = S.Id
            INNER JOIN  sys_static_dictionary AS D ON S.VarietyAttribute = D.DictionaryId AND D.BaseIsDelete = 0 AND D.TypeCode = '1060'
            INNER JOIN SysUser AS SU ON A.BaseCreatorId = SU.Id
            LEFT JOIN  bn_FunRoom AS C ON A.FunRoomId = C.Id
            LEFT JOIN  bn_Cupboard AS B ON A.CupboardId = B.Id
            WHERE A.BaseIsDelete = 0 ) AS T WHERE StockNum > 0 ");

            var parameter = new List<DbParameter>();

            if (param != null)
            {
                if (!param.UnitId.IsNullOrZero())
                {
                    sql.Append(" AND SchoolId = @UnitId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                if (!param.SafeguardUserId.IsNullOrZero())
                {
                    sql.Append(" AND SafeguardUserId = @SafeguardUserId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SafeguardUserId", param.SafeguardUserId));
                }
                if (!param.UserId.IsNullOrZero())
                {
                    sql.Append(" AND BaseCreatorId = @BaseCreatorId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@BaseCreatorId", param.UserId));
                }
                if (!param.CourseId.IsNullOrZero())
                {
                    sql.Append($" AND CourseId = @CourseId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (param.ListType == 3)
                {
                    if (!param.Statuz.IsNullOrZero() && param.Statuz > -1)
                    {
                        sql.Append(" AND Statuz > @Statuz");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@Statuz", param.Statuz));
                    }
                }
                else
                {

                    if (!param.Statuz.IsNullOrZero() && param.Statuz > -1)
                    {
                        sql.Append(" AND Statuz = @Statuz");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@Statuz", param.Statuz));
                    }
                    if (param.Statuz == -2)
                    {
                        sql.Append($" AND Statuz > 0");
                    }

                }
                if (!param.KeyWord.IsEmpty())
                {
                    sql.Append($" AND ( Code LIKE '%{param.KeyWord}%' OR Name LIKE '%{param.KeyWord}%' ");

                    if (param.UnitId == 0)
                    {
                        sql.Append($" OR RealName LIKE '%{param.KeyWord}%' ");
                    }
                    sql.Append(" ) ");
                }
                if (param.IsShowAlreadyInputStorage.HasValue)
                {
                    if (param.IsShowAlreadyInputStorage == 0)
                    {
                        sql.Append($" AND Statuz <> {InstrumentInputStatuzEnum.AlreadyInputStorage.ParseToInt()}");
                    }
                }
                if (!param.StoragePlace.IsEmpty() && !"-1".Equals(param.StoragePlace))
                {
                    if (param.StoragePlace.IndexOf(',') != -1)
                    {
                        sql.Append(" AND CupboardId = @CupboardId");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@CupboardId", param.StoragePlace.Split(',')[1]));
                    }
                    else
                    {
                        sql.Append(" AND FunRoomId = @FunRoomId");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@FunRoomId", param.StoragePlace));
                    }
                }
                if (param.StartDate.HasValue)
                {
                    sql.Append(" AND PurchaseDate >= @StartDate");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@StartDate", param.StartDate));
                }
                if (param.EndDate.HasValue)
                {
                    sql.Append(" AND PurchaseDate <= @EndDate");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@EndDate", param.EndDate.Value.AddDays(1).AddSeconds(-1)));
                }
                if (!param.InstrumentAttribute.IsNullOrZero())
                {
                    if (param.InstrumentAttribute == 1)
                    {
                        //仪器
                        sql.Append($" AND VarietyAttribute <> '{VarietyAttributeEnum.LowValueConsumable.ParseToInt()}' AND VarietyAttribute <> '{VarietyAttributeEnum.Reagent.ParseToInt()}' ");
                    }
                    else if (param.InstrumentAttribute == 2)
                    {
                        //耗材
                        sql.Append($" AND (VarietyAttribute = '{VarietyAttributeEnum.LowValueConsumable.ParseToInt()}' OR VarietyAttribute = '{VarietyAttributeEnum.Reagent.ParseToInt()}')");
                    }
                }
                if (!param.VarietyAttribute.IsNullOrZero())
                {
                    sql.Append(" AND VarietyAttribute = @VarietyAttribute");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@VarietyAttribute", param.VarietyAttribute));
                }
                if (param.IsSelfMade.HasValue && param.IsSelfMade > -1)
                {
                    sql.Append(" AND IsSelfMade = @IsSelfMade");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@IsSelfMade", param.IsSelfMade));
                }
                if (!param.SchoolId.IsNullOrZero())
                {
                    sql.Append(" AND SchoolId = @SchoolId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
                }
                if (!param.QRCode.IsEmpty())
                {
                    sql.Append(" AND QRCode = @QRCode");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@QRCode", param.QRCode));
                }
            }

            sql.Append("  ) N ");
            StringBuilder searchSql = new StringBuilder();
            searchSql.Append(sql.ToString());

            if (GlobalContext.SystemConfig.DBProvider.Equals("Dm") || GlobalContext.SystemConfig.DBProvider.Equals("MySql"))
            {
                searchSql.AppendFormat(" limit {0},{1}", beginNumber, pagination.PageSize);
            }
            else
            {
                searchSql.AppendFormat(" WHERE ROWNUM > {0} AND ROWNUM <= {1}", beginNumber, endNumber);
            }

            var list = await this.BaseRepository().FindList<SchoolInstrumentEntity>(searchSql.ToString(), parameter.ToArray());
            var totalList = await this.BaseRepository().FindList<SchoolInstrumentEntity>(sql.ToString(), parameter.ToArray());
            pagination.TotalCount = totalList.Count();

            return list.ToList();
        }

        /// <summary>
        /// 获取管理授权学段、科目搜索条件
        /// </summary>
        /// <param name="schoolStageIdz"></param>
        /// <param name="subjectIdz"></param>
        /// <returns></returns>
        public string GetUserSchoolStageSubjectSql(string schoolStageIdz, string subjectIdz)
        {
            if (schoolStageIdz.IsEmpty() || subjectIdz.IsEmpty())
            {
                return " AND 1<>1 ";
            }
            string schoolStageSql = "";
            var schoolStageList = TextHelper.SplitToArray<int>(schoolStageIdz, ',');
            for (int i = 0; i < schoolStageList.Length; i++)
            {
                if (i == 0)
                {
                    schoolStageSql += $" AND ( StageId = {schoolStageList[i]} ";
                }
                else
                {
                    schoolStageSql += $" OR StageId = {schoolStageList[i]} ";
                }
            }
            schoolStageSql = schoolStageSql.IsEmpty() ? "" : schoolStageSql + " )";

            string subjectSql = "";
            var subjectIdList = TextHelper.SplitToArray<int>(subjectIdz, ',');
            for (int i = 0; i < subjectIdList.Length; i++)
            {
                if (i == 0)
                {
                    subjectSql += $" AND ( CourseId = {subjectIdList[i]} ";
                }
                else
                {
                    subjectSql += $" OR CourseId = {subjectIdList[i]} ";
                }
            }
            subjectSql = subjectSql.IsEmpty() ? "" : subjectSql + " )";

            return schoolStageSql + subjectSql;
        }

        #endregion
    }
}
