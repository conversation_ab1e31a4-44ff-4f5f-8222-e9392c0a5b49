﻿using System;
using System.Collections.Generic;
using Newtonsoft.Json;
using Dqy.Syjx.Util;

namespace Dqy.Syjx.Model.Param.CameraManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2022-01-10 10:02
    /// 描 述：摄像头管理实体查询类
    /// </summary>
    public class SchoolCameraListParam
    {
        public SchoolCameraListParam()
        {
            BaseIsDelete = 0;
        }
        public int BaseIsDelete { get; set; }
        public long Id { get; set; }
        private string _ids;
        public string Ids
        {
            get { return StringFilter.SearchSql(_ids); }
            set { _ids = value; }
        }

        private string _name;
        public string Name
        {
            get { return StringFilter.SearchSql(_name); }
            set { _name = value; }
        }

        public int Statuz { get; set; }
        public long FunRoomId { get; set; }

        public int UnitType { get; set; }
        public long UnitId { get; set; }
        public long CountyId { get; set; }
        public int Nature { get; set; }

        public long CameraId { get; set; }

        /// <summary>
        /// 区县名称
        /// </summary>

        private string _countyName;
        public string CountyName
        {
            get { return StringFilter.SearchSql(_countyName); }
            set { _countyName = value; }
        }

        /// <summary>
        /// 摄像机名称
        /// </summary>

        private string _cameraName;
        public string CameraName
        {
            get { return StringFilter.SearchSql(_cameraName); }
            set { _cameraName = value; }
        }

        /// <summary>
        /// 摄像机编码
        /// </summary>

        private string _cameraCode;
        public string CameraCode
        {
            get { return StringFilter.SearchSql(_cameraCode); }
            set { _cameraCode = value; }
        }
    }
}
