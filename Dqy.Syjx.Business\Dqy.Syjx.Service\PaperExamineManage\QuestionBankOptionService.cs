﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.PaperExamineManage;
using Dqy.Syjx.Model.Param.PaperExamineManage;

namespace Dqy.Syjx.Service.PaperExamineManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2022-08-03 17:30
    /// 描 述：题库选项服务类
    /// </summary>
    public class QuestionBankOptionService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<QuestionBankOptionEntity>> GetList(QuestionBankOptionListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<QuestionBankOptionEntity>> GetPageList(QuestionBankOptionListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<QuestionBankOptionEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<QuestionBankOptionEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(QuestionBankOptionEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(QuestionBankOptionEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
               strSql = $" update cp_QuestionBankOption set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
               strSql = $"update cp_QuestionBankOption set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<QuestionBankOptionEntity, bool>> ListFilter(QuestionBankOptionListParam param)
        {
            var expression = LinqExtensions.True<QuestionBankOptionEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (param.QuestionBankId > 0)
                {
                    expression = expression.And(t => t.QuestionBankId == param.QuestionBankId);
                }
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
            }
            return expression;
        }
        #endregion
    }
}
