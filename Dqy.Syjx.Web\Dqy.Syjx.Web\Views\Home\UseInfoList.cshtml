﻿@{
    ViewBag.Title = "平台使用情况";
    Layout = "~/Views/Shared/_Index.cshtml";
}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .select-list li input {
        width: auto !important;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <input id="Name" col="Name" placeholder="关键字" style="width:150px;" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-default btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    $(function () {
        initGrid();
    });


    function initGrid() {
        var columns = [{ field: 'InstrumentNum', title: '仪器存量<br />（件）', sortable: true, halign: 'center', align: 'center', width: 40 },
                { field: 'InstrumentNewNum', title: '当年新增<br />（件）', halign: 'center', align: 'center', width: 40 },
                { field: 'FunroomNum', title: '实验室数量<br />（个）', sortable: true, halign: 'center', align: 'center', width: 40 },
                { field: 'FunroomUseNum', title: '使用频次<br />（次）', halign: 'center', align: 'center', width: 40 },
                { field: 'OptionalShowNum', title: '实验员数量<br />（人）', sortable: true, halign: 'center', align: 'center', width: 40 },
                { field: 'ExperimenterUserMajorNum', title: '专职人数<br />（人）', halign: 'center', align: 'center', width: 40 },
                { field: 'ExperimentNum', title: '应开实验数<br />（个）', sortable: true, halign: 'center', align: 'center', width: 40 },
                { field: 'PlanExperimentNum', title: '已编实验<br />计划数（个）', halign: 'center', align: 'center', width: 40 },
                { field: 'RecordExperimentNum', title: '已开实验数<br />（个）', halign: 'center', align: 'center', width: 40 },
                {
                    field: 'ExperimentRate', title: '实验开出率', halign: 'center', align: 'center', width: 40,
                    formatter: function (value, row, index) {
                        return value.toFixed(2) + '%';
                    }
                }];
        $('#gridTable').ysTable({
            showRefresh: false,
            showToggle: false,
            showColumns: false, 
            pageSize:15,
            sortable:false,
            url: '@Url.Content("~/Home/UseInfoDetail")',
            sortName: '',
            columns: [
                [
                    { field: 'Name', title: '<div style="width:150px;">单位名称</div>', sortable: true, halign: 'center', align: 'left', valign: 'middle', rowspan: 2, width: 120 },
                    { field: 'SchoolStageName', title: '学段', sortable: true, halign: 'center', align: 'center', valign: 'middle', rowspan: 2, width: 60 },
                    { title: '实验仪器', align: 'center', colspan: 2 },
                    { title: '实验室', align: 'center', colspan: 2 },
                    { title: '实验员', align: 'center', colspan: 2 },
                    { title: '本学期基础实验开出情况', align: 'center', colspan: 4 }
                ],
                columns
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
    } 
    function resetGrid() {
        //清空条件
        $('#Name').val('');
        $('#gridTable').ysTable('search');
    }
     
</script>
