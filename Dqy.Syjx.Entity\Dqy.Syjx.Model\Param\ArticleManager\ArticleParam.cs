﻿using System;
using System.Collections.Generic;
using Newtonsoft.Json;
using Dqy.Syjx.Util;

namespace Dqy.Syjx.Model.Param.ArticleManager
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2022-01-21 10:42
    /// 描 述：实体查询类
    /// </summary>
    public class ArticleListParam
    {
        private string _ids;
        public string Ids
        {
            get { return StringFilter.SearchSql(_ids); }
            set { _ids = value; }
        }

        public long? Cid { get; set; }

        private string _ClassName;
        public string ClassName
        {
            get { return StringFilter.SearchSql(_ClassName); }
            set { _ClassName = value; }
        }

        public string _Key;
        public string Key
        {
            get { return StringFilter.SearchSql(_Key); }
            set { _Key = value; }
        }


        /// <summary>
        /// 创建单位Id
        /// </summary>
        public long UnitId { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public int Statuz { get; set; } = -10000;

        /// <summary>
        /// 编码
        /// </summary>
        public string _configcode;
        /// <summary>
        /// 编码
        /// </summary>
        public string ConfigCode
        {
            get { return StringFilter.SearchSql(_configcode); }
            set { _configcode = value; }
        }
    }
}
