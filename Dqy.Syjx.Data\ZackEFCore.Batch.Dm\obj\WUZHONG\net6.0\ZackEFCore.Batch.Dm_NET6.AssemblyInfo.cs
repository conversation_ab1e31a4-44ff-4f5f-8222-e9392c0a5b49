//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Reflection;

[assembly: System.Reflection.AssemblyCompanyAttribute("ZackEFCore.Batch.Dm_NET6")]
[assembly: System.Reflection.AssemblyConfigurationAttribute("WUZHONG")]
[assembly: System.Reflection.AssemblyDescriptionAttribute("A Dm（达梦数据库）-specific version of Zack.EFCore.Batch.\nUsing this library, Entity Fra" +
    "mework Core users can delete or update multiple records from a LINQ Query in a S" +
    "QL statement without loading entities. This libary supports Entity Framework Cor" +
    "e 5.0.")]
[assembly: System.Reflection.AssemblyFileVersionAttribute("*******")]
[assembly: System.Reflection.AssemblyInformationalVersionAttribute("6.6.3")]
[assembly: System.Reflection.AssemblyProductAttribute("ZackEFCore.Batch.Dm_NET6")]
[assembly: System.Reflection.AssemblyTitleAttribute("ZackEFCore.Batch.Dm_NET6")]
[assembly: System.Reflection.AssemblyVersionAttribute("*******")]
[assembly: System.Reflection.AssemblyMetadataAttribute("RepositoryUrl", "https://github.com/yangzhongke/Zack.EFCore.Batch")]

// 由 MSBuild WriteCodeFragment 类生成。

