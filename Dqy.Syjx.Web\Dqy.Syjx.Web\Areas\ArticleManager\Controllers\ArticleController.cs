﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using System.Web;
using Microsoft.AspNetCore.Mvc;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Entity;
using Dqy.Syjx.Model;
using Dqy.Syjx.Web.Controllers;
using Dqy.Syjx.Entity.ArticleManager;
using Dqy.Syjx.Model.Input.ArticleManager;
using Dqy.Syjx.Business.ArticleManager;
using Dqy.Syjx.Model.Param.ArticleManager;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Util.Extension;

namespace Dqy.Syjx.Web.Areas.ArticleManager.Controllers
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2022-01-21 10:42
    /// 描 述：控制器类
    /// </summary>
    [Area("ArticleManager")]
    public class ArticleController :  BaseController
    {
        private ArticleBLL articleBLL = new ArticleBLL();

        #region 视图功能
        [AuthorizeFilter("articler:article:view")]
        public ActionResult ArticleIndex()
        {
            return View();
        }

        public ActionResult ArticleForm()
        {
            return View();
        }

        public ActionResult ArticleContextForm()
        {
            return View();
        }
        public ActionResult ArticleList()
        {
            return View();
        }
        public async Task<ActionResult> ArticleDetail(long id)
        {
            TData<ArticleEntity> obj =await articleBLL.GetEntity(id);
            ViewBag.Article = obj;
            return View();
        }
        #endregion

        #region 获取数据
        [HttpGet]
        [AuthorizeFilter("articler:article:search")]
        public async Task<ActionResult> GetListJson(ArticleListParam param)
        {
            TData<List<ArticleEntity>> obj = await articleBLL.GetList(param);
            return Json(obj);
        }

        [HttpGet]
        [AuthorizeFilter("articler:article:search")]
        public async Task<ActionResult> GetPageListJson(ArticleListParam param, Pagination pagination)
        {
            TData<List<ArticleEntity>> obj = await articleBLL.GetPageList(param, pagination);
            return Json(obj);
        }

        [HttpGet]
        public async Task<ActionResult> GetFormJson(long id)
        {
            TData<ArticleEntity> obj = await articleBLL.GetEntity(id);
            return Json(obj);
        }
        #endregion

        #region 提交数据
        [HttpPost]
        [AuthorizeFilter("articler:article:add,articler:article:edit")]
        public async Task<ActionResult> SaveFormJson(ArticleInputModel model)
        {
            TData<string> obj = await articleBLL.SaveForm(model);
            return Json(obj);
        }

        [HttpPost]
        [AuthorizeFilter("articler:article:delete")]
        public async Task<ActionResult> DeleteFormJson(string ids)
        {
            TData obj = await articleBLL.DeleteForm(ids);
            return Json(obj);
        }

        /// <summary>
        /// 推荐到首页
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("articler:article:edit")]
        public async Task<ActionResult> BatchRecommendIndex(string ids)
        {
            TData obj = await articleBLL.BatchRecommend(ids, true);
            return Json(obj);
        }

        /// <summary>
        /// 不推荐到首页
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("articler:article:edit")]
        public async Task<ActionResult> BatchRecommendNoIndex(string ids)
        {
            TData obj = await articleBLL.BatchRecommend(ids, false);
            return Json(obj);
        }

        /// <summary>
        /// 暂停发布
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("articler:article:edit")]
        public async Task<ActionResult> BatchSuspendPublish(string ids)
        {
            TData obj = await articleBLL.BatchSuspendPublish(ids, ArticleStatuzEnum.Save.ParseToInt());
            return Json(obj);
        }

        /// <summary>
        /// 重新发布
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("articler:article:edit")]
        public async Task<ActionResult> BatchPublish(string ids)
        {
            TData obj = await articleBLL.BatchSuspendPublish(ids, ArticleStatuzEnum.Publish.ParseToInt());
            return Json(obj);
        }
        #endregion
    }
}
