﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.ExperimentTeachManage;
using Dqy.Syjx.Model.Param.ExperimentTeachManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Model.Input.ExperimentTeachManage;
using Dqy.Syjx.Service.OrganizationManage;
using System.Data;
using Dqy.Syjx.Util.Tools;
using Dqy.Syjx.Enum.ExperimentTeachManage;

namespace Dqy.Syjx.Service.ExperimentTeachManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-12-21 14:18
    /// 描 述：服务类
    /// </summary>
    public class ExperimentAttendStaticService : RepositoryFactory
    {
        private UserSubjectSetService userSubjectSetService = new UserSubjectSetService();

        #region 获取数据
        public async Task<List<ExperimentAttendStaticEntity>> GetList(ExperimentAttendStaticListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<ExperimentAttendStaticEntity>> GetPageList(ExperimentAttendStaticListParam param, Pagination pagination)
        {
            StringBuilder sql = new StringBuilder();
            var expression = ListFilter(param, sql);
            var oldSort = pagination.Sort;
            if (pagination.Sort!=null && (pagination.Sort.ToLower().IndexOf("totalnum") >-1|| pagination.Sort.ToLower().IndexOf("totalnumed") > -1|| pagination.Sort.ToLower().IndexOf("totalratio") > -1))
            {
                pagination.Sort = "Id";
            }
            var list = await this.BaseRepository().FindList<ExperimentAttendStaticEntity>(sql.ToString(), expression.ToArray(), pagination);
            if (list != null)
            {
                list.ForEach(m =>
                {
                    m.NeedShowNumRatio = AlgorithmHelper.GetRatio(m.NeedShowNumed, m.NeedShowNum, 2);
                    m.NeedGroupNumRatio = AlgorithmHelper.GetRatio(m.NeedGroupNumed, m.NeedGroupNum, 2);
                    m.OptionalShowNumRatio = AlgorithmHelper.GetRatio(m.OptionalShowNumed, m.OptionalShowNum, 2);
                    m.OptionalGroupNumRatio = AlgorithmHelper.GetRatio(m.OptionalGroupNumed, m.OptionalGroupNum, 2);
                    m.TotalNum = (m.NeedShowNum + m.NeedGroupNum + m.OptionalShowNum + m.OptionalGroupNum);
                    m.TotalNumed = (m.NeedShowNumed + m.NeedGroupNumed + m.OptionalShowNumed + m.OptionalGroupNumed);
                    m.TotalRatio = AlgorithmHelper.GetRatio(m.TotalNumed, m.TotalNum, 2, 0);
                });
                //处理字段排序问题
                if (oldSort != null && (oldSort.ToLower().IndexOf("totalnum") > -1 || oldSort.ToLower().IndexOf("totalnumed") > -1 || oldSort.ToLower().IndexOf("totalratio") > -1))
                {
                    if (pagination.SortType != null && pagination.SortType.ToLower().IndexOf("desc") > -1)
                    {
                        if (oldSort.ToLower() == "totalnum")
                        {
                            list = (from item in list
                                    orderby item.TotalNum descending
                                    select item);
                        }
                        else if (oldSort.ToLower() == "totalnumed")
                        {
                            list = (from item in list
                                    orderby item.TotalNumed descending
                                    select item);
                        }
                        else if (oldSort.ToLower() == "totalratio")
                        {
                            list = (from item in list
                                    orderby item.TotalRatio descending
                                    select item);
                        }

                    }
                    else
                    {
                        if (oldSort.ToLower() == "totalnum")
                        {
                            list = (from item in list
                                    orderby item.TotalNum ascending
                                    select item);
                        }
                        else if (oldSort.ToLower() == "totalnumed")
                        {
                            list = (from item in list
                                    orderby item.TotalNumed ascending
                                    select item);
                        }
                        else if (oldSort.ToLower() == "totalratio")
                        {
                            list = (from item in list
                                    orderby item.TotalRatio ascending
                                    select item);
                        }
                    }
                }
            }
            return list.ToList();
        }

        public async Task<List<ExperimentAttendStaticEntity>> GetCountyPageList(ExperimentAttendStaticListParam param, Pagination pagination)
        {
            StringBuilder sql = new StringBuilder();
            var expression = CountyListFilter(param, sql);
            var list = await this.BaseRepository().FindList<ExperimentAttendStaticEntity>(sql.ToString(), expression.ToArray(), pagination);
            if (list != null)
            {
                list.ForEach(m =>
                {
                    m.NeedShowNumRatio = AlgorithmHelper.GetRatio(m.NeedShowNumed, m.NeedShowNum, 2);
                    m.NeedGroupNumRatio = AlgorithmHelper.GetRatio(m.NeedGroupNumed, m.NeedGroupNum, 2);
                    m.OptionalShowNumRatio = AlgorithmHelper.GetRatio(m.OptionalShowNumed, m.OptionalShowNum, 2);
                    m.OptionalGroupNumRatio = AlgorithmHelper.GetRatio(m.OptionalGroupNumed, m.OptionalGroupNum, 2);
                    m.TotalNum = (m.NeedShowNum + m.NeedGroupNum + m.OptionalShowNum + m.OptionalGroupNum);
                    m.TotalNumed = (m.NeedShowNumed + m.NeedGroupNumed + m.OptionalShowNumed + m.OptionalGroupNumed);
                    m.TotalRatio = AlgorithmHelper.GetRatio(m.TotalNumed,m.TotalNum, 2,0); 
                });
            }
            return list.ToList();
        }

        public async Task<List<ExperimentAttendStaticEntity>> GetCityPageList(ExperimentAttendStaticListParam param, Pagination pagination)
        {
            StringBuilder sql = new StringBuilder();
            var expression = CityListFilter(param, sql);
            var list = await this.BaseRepository().FindList<ExperimentAttendStaticEntity>(sql.ToString(), expression.ToArray(), pagination);
            if (list != null)
            {
                list.ForEach(m =>
                {
                    m.NeedShowNumRatio = AlgorithmHelper.GetRatio(m.NeedShowNumed, m.NeedShowNum, 2);
                    m.NeedGroupNumRatio = AlgorithmHelper.GetRatio(m.NeedGroupNumed, m.NeedGroupNum, 2);
                    m.OptionalShowNumRatio = AlgorithmHelper.GetRatio(m.OptionalShowNumed, m.OptionalShowNum, 2);
                    m.OptionalGroupNumRatio = AlgorithmHelper.GetRatio(m.OptionalGroupNumed, m.OptionalGroupNum, 2);
                    m.TotalNum = (m.NeedShowNum + m.NeedGroupNum + m.OptionalShowNum + m.OptionalGroupNum);
                    m.TotalNumed = (m.NeedShowNumed + m.NeedGroupNumed + m.OptionalShowNumed + m.OptionalGroupNumed);
                    m.TotalRatio = AlgorithmHelper.GetRatio(m.TotalNumed, m.TotalNum, 2, 0);
                });
            }
            return list.ToList();
        }

        public async Task<ExperimentAttendStaticEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<ExperimentAttendStaticEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(ExperimentAttendStaticEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(ExperimentAttendStaticEntity entity, Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids?.Trim(','));
            if (ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");

            // 使用参数化查询和跨数据库兼容的 IN 语法
            var parameters = new List<DbParameter>
            {
                DbParameterExtension.CreateDbParameter("@BaseIsDelete", 1)
            };

            string strSql = $"UPDATE ex_ExperimentAttendStatic SET BaseIsDelete = @BaseIsDelete WHERE Id IN ({ids})";
            await this.BaseRepository().ExecuteBySql(strSql, parameters.ToArray());
        }

        /// <summary>
        /// 删除当前单位 ，当前学期的数据。
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task Deletes(ExperimentAttendStaticInputModel model)
        {
            if (model != null && model.SchoolId > 0)
            {
                string strSql = $" DELETE FROM ex_ExperimentAttendStatic WHERE SchoolId = {model.SchoolId} AND SchoolYearStart = {model.SchoolYearStart} AND SchoolTerm = {model.SchoolTerm} ";
                await this.BaseRepository().ExecuteBySql(strSql);
            }
        }

        #endregion

        #region 私有方法
        private Expression<Func<ExperimentAttendStaticEntity, bool>> ListFilter(ExperimentAttendStaticListParam param)
        {
            var expression = LinqExtensions.True<ExperimentAttendStaticEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (param.GradeId > 0)
                {
                    expression = expression.And(t => t.GradeId == param.GradeId);
                }
                if (param.CourseId > 0)
                {
                    expression = expression.And(t => t.CourseId == param.CourseId);
                }
                if (param.SchoolId > 0)
                {
                    expression = expression.And(t => t.SchoolId == param.SchoolId);
                }
                if (param.SchoolYearStart > 0)
                {
                    expression = expression.And(t => t.SchoolYearStart == param.SchoolYearStart);
                }
                if (param.SchoolTerm > 0)
                {
                    expression = expression.And(t => t.SchoolTerm == param.SchoolTerm);
                }
            }
            return expression;
        }

        private List<DbParameter> ListFilter(ExperimentAttendStaticListParam param, StringBuilder strSql)
        {
            strSql.Append(@$" SELECT * From (
                               SELECT  ES.Id ,
                                       ES.DataStorageType ,ES.SchoolId ,ES.GradeId ,ES.CourseId ,
                               		   ES.SchoolYearStart ,ES.SchoolYearEnd ,ES.SchoolTerm ,ES.SourceType ,ES.CompulsoryType ,
                                       ES.NeedShowNum ,ES.NeedGroupNum ,ES.OptionalShowNum ,ES.OptionalGroupNum ,
                                       ES.NeedShowNumed ,ES.NeedGroupNumed ,ES.OptionalShowNumed ,ES.OptionalGroupNumed ,
                                       SD.DictionaryId AS SchoolStageId ,SD.DicName AS SchoolStageName ,D1.DicName AS GradeName ,D2.DicName AS CourseName ,
                                        UR.UnitId AS CountyId ,U.Name AS SchoolName ,U.Sort ,UR2.UnitId AS CityId
                               FROM  ex_ExperimentAttendStatic AS ES
                               INNER JOIN  up_Unit AS U ON ES.SchoolId = U.Id
                               INNER JOIN  sys_static_dictionary AS D1 ON ES.GradeId = D1.DictionaryId AND D1.TypeCode = '{DicTypeCodeEnum.Grade.ParseToInt()}'
                               INNER JOIN  sys_static_dictionary AS D2 ON ES.CourseId = D2.DictionaryId AND D2.TypeCode = '{DicTypeCodeEnum.Course.ParseToInt()}'
                               INNER JOIN  up_UnitRelation AS UR ON UR.ExtensionObjId = ES.SchoolId AND UR.ExtensionType = 3
	                           INNER JOIN  up_UnitRelation AS UR2 ON UR.UnitId = UR2.ExtensionObjId AND UR2.ExtensionType = 3 AND UR2.BaseIsDelete = 0
                               INNER JOIN  sys_dictionary_relation AS dr ON D1.DictionaryId = dr.DictionaryToId  AND dr.RelationType = 1 AND dr.BaseIsDelete = 0
					           INNER JOIN  sys_static_dictionary AS SD ON dr.DictionaryId = SD.DictionaryId AND SD.TypeCode = '{DicTypeCodeEnum.SchoolStage.ParseToInt()}' AND SD.BaseIsDelete = 0
                               WHERE ES.BaseIsDelete = 0
                          ) as T WHERE  1 = 1 ");
            strSql = SqlHelper.DmMoneyReplaceDecimal(strSql);
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (!param.SchoolId.IsNullOrZero())
                {
                    strSql.Append(" AND SchoolId = @SchoolId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
                }
                if (!param.CountyId.IsNullOrZero())
                {
                    strSql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
                if (param.CityId > 0)
                {
                    strSql.Append(" AND CityId = @CityId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CityId", param.CityId));
                }
                if (!param.SchoolYearStart.IsNullOrZero())
                {
                    strSql.Append(" AND SchoolYearStart = @SchoolYearStart ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolYearStart", param.SchoolYearStart));
                }
                if (!param.SchoolTerm.IsNullOrZero())
                {
                    strSql.Append(" AND SchoolTerm = @SchoolTerm ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolTerm", param.SchoolTerm));
                }
                if (!param.GradeId.IsNullOrZero())
                {
                    strSql.Append(" AND GradeId = @GradeId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@GradeId", param.GradeId));
                }
                if (!param.CourseId.IsNullOrZero())
                {
                    strSql.Append(" AND CourseId = @CourseId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (!param.SourceType.IsNullOrZero())
                {
                    strSql.Append(" AND SourceType = @SourceType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SourceType", param.SourceType));
                }
                if (!param.CompulsoryType.IsNullOrZero()) //教材类型
                {
                    strSql.Append(" AND CompulsoryType = @CompulsoryType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CompulsoryType", param.CompulsoryType));
                }
                if (!param.SchoolStageId.IsNullOrZero()) //学段
                {
                    strSql.Append(" AND SchoolStageId = @SchoolStageId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStageId", param.SchoolStageId));
                }
                if (param.MenuType > 0)
                {
                    if (param.MenuType == 1)
                    {
                        strSql.Append($" AND SchoolStageId <> {SchoolStageEnum.GaoZhong.ParseToInt()} ");
                    }
                    else if (param.MenuType == 2)
                    {
                        strSql.Append($" AND SchoolStageId = {SchoolStageEnum.GaoZhong.ParseToInt()} ");
                    }
                }
                if (!param.GradeName.IsEmpty())
                {
                    strSql.Append(" AND GradeName = @GradeName ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@GradeName", param.GradeName));
                }
                if (!param.CourseName.IsEmpty())
                {
                    strSql.Append(" AND CourseName = @CourseName ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseName", param.CourseName));
                }
                if (param.SetUserId > 0)
                {

                    //判断用户是否设置了学段、学科
                    var listStage = userSubjectSetService.GetUserSetStageList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listStage.Count > 0)
                    {
                        string stageId = string.Join(",", listStage.Select(f => f.DictionaryId));
                        strSql.Append($" AND SchoolStageId IN ({stageId})");
                    }

                    var listCourse = userSubjectSetService.GetUserSetCourseList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listCourse.Count > 0)
                    {
                        string courseId = string.Join(",", listCourse.Select(f => f.DictionaryId));
                        strSql.Append($" AND CourseId IN ({courseId})");
                    }

                }
            }
            return parameter;
        }

        private List<DbParameter> CountyListFilter(ExperimentAttendStaticListParam param, StringBuilder strSql)
        {
            string whereSchool = "";
            if (param != null && param.SchoolId > 0)
            {
                whereSchool = string.Format(" AND SchoolId = {0} ", param.SchoolId);
            }
            strSql.Append(@$" SELECT * From (
                               SELECT  UR.UnitId ,ES.GradeId ,ES.CourseId ,
                               		   ES.SchoolYearStart ,ES.SchoolYearEnd ,ES.SchoolTerm ,ES.SourceType ,ES.CompulsoryType ,
                                       SD.DictionaryId AS SchoolStageId ,SD.DicName AS SchoolStageName ,D1.DicName AS GradeName ,D2.DicName AS CourseName ,
									   SUM(ES.NeedShowNum) AS NeedShowNum ,SUM(ES.NeedGroupNum) AS NeedGroupNum ,SUM(ES.OptionalShowNum) AS OptionalShowNum ,SUM(OptionalGroupNum) AS OptionalGroupNum ,
									   SUM(ES.NeedShowNumed) AS NeedShowNumed ,SUM(ES.NeedGroupNumed) AS NeedGroupNumed ,SUM(ES.OptionalShowNumed) AS OptionalShowNumed ,SUM(OptionalGroupNumed) AS OptionalGroupNumed ,
									   UR.UnitId AS CountyId ,sa.AreaName AS CountyName ,UR2.UnitId AS CityId
                               FROM  ex_ExperimentAttendStatic AS ES
                               INNER JOIN  up_Unit AS U ON ES.SchoolId = U.Id
                               INNER JOIN  sys_static_dictionary AS D1 ON ES.GradeId = D1.DictionaryId AND D1.TypeCode = '{DicTypeCodeEnum.Grade.ParseToInt()}'
                               INNER JOIN  sys_static_dictionary AS D2 ON ES.CourseId = D2.DictionaryId AND D2.TypeCode = '{DicTypeCodeEnum.Course.ParseToInt()}'
                               INNER JOIN  up_UnitRelation AS UR ON UR.ExtensionObjId = ES.SchoolId AND UR.ExtensionType = 3
                               INNER JOIN  sys_dictionary_relation AS dr ON D1.DictionaryId = dr.DictionaryToId  AND dr.RelationType = 1 AND dr.BaseIsDelete = 0
					           INNER JOIN  sys_static_dictionary AS SD ON dr.DictionaryId = SD.DictionaryId AND SD.TypeCode = '{DicTypeCodeEnum.SchoolStage.ParseToInt()}' AND SD.BaseIsDelete = 0
                               INNER JOIN  up_UnitRelation AS UR2 ON UR.UnitId = UR2.ExtensionObjId AND UR2.ExtensionType = 3 AND UR2.BaseIsDelete = 0
							   INNER JOIN  up_Unit AS u2 ON ur.UnitId = u2.Id AND u2.BaseIsDelete = 0
							   LEFT JOIN  SysArea AS sa ON sa.AreaCode = u2.AreaId AND sa.BaseIsDelete = 0
                               WHERE ES.BaseIsDelete = 0 {whereSchool}
							   GROUP BY UR2.UnitId ,UR.UnitId ,sa.AreaName ,ES.SchoolYearStart ,ES.SchoolYearEnd ,ES.SchoolTerm ,ES.GradeId ,ES.CourseId ,ES.SourceType ,ES.CompulsoryType
							   ,SD.DictionaryId ,SD.DicName ,D1.DicName ,D2.DicName
                          ) as T WHERE  1 = 1 ");
            strSql = SqlHelper.DmMoneyReplaceDecimal(strSql);
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (!param.CityId.IsNullOrZero())
                {
                    strSql.Append(" AND CityId = @CityId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CityId", param.CityId));
                }
                if (!param.CountyId.IsNullOrZero())
                {
                    strSql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
                if (!param.SchoolYearStart.IsNullOrZero())
                {
                    strSql.Append(" AND SchoolYearStart = @SchoolYearStart ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolYearStart", param.SchoolYearStart));
                }
                if (!param.SchoolTerm.IsNullOrZero())
                {
                    strSql.Append(" AND SchoolTerm = @SchoolTerm ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolTerm", param.SchoolTerm));
                }
                if (!param.GradeId.IsNullOrZero())
                {
                    strSql.Append(" AND GradeId = @GradeId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@GradeId", param.GradeId));
                }
                if (!param.CourseId.IsNullOrZero())
                {
                    strSql.Append(" AND CourseId = @CourseId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (!param.SourceType.IsNullOrZero()) //统计口径
                {
                    strSql.Append(" AND SourceType = @SourceType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SourceType", param.SourceType));
                }
                if (!param.CompulsoryType.IsNullOrZero()) //教材类型
                {
                    strSql.Append(" AND CompulsoryType = @CompulsoryType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CompulsoryType", param.CompulsoryType));
                }
                if (!param.SchoolStageId.IsNullOrZero()) //学段
                {
                    strSql.Append(" AND SchoolStageId = @SchoolStageId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStageId", param.SchoolStageId));
                }
                if (param.MenuType > 0)
                {
                    if (param.MenuType == 1)
                    {
                        strSql.Append($" AND SchoolStageId <> {SchoolStageEnum.GaoZhong.ParseToInt()} ");
                    }
                    else if (param.MenuType == 2)
                    {
                        strSql.Append($" AND SchoolStageId = {SchoolStageEnum.GaoZhong.ParseToInt()} ");
                    }
                }
                if (param.SetUserId > 0)
                {
                    //判断用户是否设置了学段、学科
                    var listStage = userSubjectSetService.GetUserSetStageList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listStage.Count > 0)
                    {
                        string stageId = string.Join(",", listStage.Select(f => f.DictionaryId));
                        strSql.Append($" AND SchoolStageId IN ({stageId})");
                    }

                    var listCourse = userSubjectSetService.GetUserSetCourseList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listCourse.Count > 0)
                    {
                        string courseId = string.Join(",", listCourse.Select(f => f.DictionaryId));
                        strSql.Append($" AND CourseId IN ({courseId})");
                    }
                }
            }
            return parameter;
        }


        private List<DbParameter> CityListFilter(ExperimentAttendStaticListParam param, StringBuilder strSql)
        {
            strSql.Append(@$" SELECT *  From (
                               SELECT  ES.GradeId ,ES.CourseId ,
                               		   ES.SchoolYearStart ,ES.SchoolYearEnd ,ES.SchoolTerm ,ES.SourceType ,ES.CompulsoryType ,
                                       SD.DictionaryId AS SchoolStageId ,SD.DicName AS SchoolStageName ,D1.DicName AS GradeName ,D2.DicName AS CourseName ,
									   SUM(ES.NeedShowNum) AS NeedShowNum ,SUM(ES.NeedGroupNum) AS NeedGroupNum ,SUM(ES.OptionalShowNum) AS OptionalShowNum ,SUM(OptionalGroupNum) AS OptionalGroupNum ,
									   SUM(ES.NeedShowNumed) AS NeedShowNumed ,SUM(ES.NeedGroupNumed) AS NeedGroupNumed ,SUM(ES.OptionalShowNumed) AS OptionalShowNumed ,SUM(OptionalGroupNumed) AS OptionalGroupNumed ,
									   UR2.UnitId AS CityId
                               FROM  ex_ExperimentAttendStatic AS ES
                               INNER JOIN  up_Unit AS U ON ES.SchoolId = U.Id
                               INNER JOIN  sys_static_dictionary AS D1 ON ES.GradeId = D1.DictionaryId AND D1.TypeCode = '{DicTypeCodeEnum.Grade.ParseToInt()}'
                               INNER JOIN  sys_static_dictionary AS D2 ON ES.CourseId = D2.DictionaryId AND D2.TypeCode = '{DicTypeCodeEnum.Course.ParseToInt()}'
                               INNER JOIN  up_UnitRelation AS UR ON UR.ExtensionObjId = ES.SchoolId AND UR.ExtensionType = 3
                               INNER JOIN  sys_dictionary_relation AS dr ON D1.DictionaryId = dr.DictionaryToId  AND dr.RelationType = 1 AND dr.BaseIsDelete = 0
					           INNER JOIN  sys_static_dictionary AS SD ON dr.DictionaryId = SD.DictionaryId AND SD.TypeCode = '{DicTypeCodeEnum.SchoolStage.ParseToInt()}' AND SD.BaseIsDelete = 0
                               INNER JOIN  up_UnitRelation AS UR2 ON UR.UnitId = UR2.ExtensionObjId AND UR2.ExtensionType = 3 AND UR2.BaseIsDelete = 0
							   INNER JOIN  up_Unit AS u2 ON ur.UnitId = u2.Id AND u2.BaseIsDelete = 0
                               WHERE ES.BaseIsDelete = 0
							   GROUP BY UR2.UnitId ,ES.SchoolYearStart ,ES.SchoolYearEnd ,ES.SchoolTerm ,ES.GradeId ,ES.CourseId ,ES.SourceType ,ES.CompulsoryType
							   ,SD.DictionaryId ,SD.DicName ,D1.DicName ,D2.DicName
                          ) as T WHERE  1 = 1 ");
            strSql = SqlHelper.DmMoneyReplaceDecimal(strSql);
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (!param.CityId.IsNullOrZero())
                {
                    strSql.Append(" AND CityId = @CityId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CityId", param.CityId));
                }
                if (!param.SchoolYearStart.IsNullOrZero())
                {
                    strSql.Append(" AND SchoolYearStart = @SchoolYearStart ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolYearStart", param.SchoolYearStart));
                }
                if (!param.SchoolTerm.IsNullOrZero())
                {
                    strSql.Append(" AND SchoolTerm = @SchoolTerm ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolTerm", param.SchoolTerm));
                }
                if (!param.GradeId.IsNullOrZero())
                {
                    strSql.Append(" AND GradeId = @GradeId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@GradeId", param.GradeId));
                }
                if (!param.CourseId.IsNullOrZero())
                {
                    strSql.Append(" AND CourseId = @CourseId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (!param.SourceType.IsNullOrZero()) //统计口径
                {
                    strSql.Append(" AND SourceType = @SourceType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SourceType", param.SourceType));
                }
                if (!param.CompulsoryType.IsNullOrZero()) //教材类型
                {
                    strSql.Append(" AND CompulsoryType = @CompulsoryType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CompulsoryType", param.CompulsoryType));
                }
                if (!param.SchoolStageId.IsNullOrZero()) //学段
                {
                    strSql.Append(" AND SchoolStageId = @SchoolStageId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStageId", param.SchoolStageId));
                }
                if (param.MenuType > 0)
                {
                    if (param.MenuType == 1)
                    {
                        strSql.Append($" AND SchoolStageId <> {SchoolStageEnum.GaoZhong.ParseToInt()} ");
                    }
                    else if (param.MenuType == 2)
                    {
                        strSql.Append($" AND SchoolStageId = {SchoolStageEnum.GaoZhong.ParseToInt()} ");
                    }
                }
            }
            return parameter;
        }
        
        #endregion
    }
}
