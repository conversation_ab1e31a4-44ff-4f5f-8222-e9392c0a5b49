﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.ExperimentTeachManage;
using Dqy.Syjx.Model.Param.ExperimentTeachManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Model.Input.ExperimentTeachManage;
using Dqy.Syjx.Service.OrganizationManage;
using System.Data;
using Dqy.Syjx.Util.Tools;
using Dqy.Syjx.Enum.ExperimentTeachManage;

namespace Dqy.Syjx.Service.ExperimentTeachManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-12-21 14:18
    /// 描 述：服务类
    /// </summary>
    public class ExperimentAttendStaticService : RepositoryFactory
    {
        private UserSubjectSetService userSubjectSetService = new UserSubjectSetService();

        #region 获取数据
        public using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.ExperimentTeachManage;
using Dqy.Syjx.Model.Param.ExperimentTeachManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Model.Input.ExperimentTeachManage;
using Dqy.Syjx.Service.OrganizationManage;
using System.Data;
using Dqy.Syjx.Util.Tools;
using Dqy.Syjx.Enum.ExperimentTeachManage;

namespace Dqy.Syjx.Service.ExperimentTeachManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-12-21 14:18
    /// 描 述：服务类
    /// </summary>
    public class ExperimentAttendStaticService : RepositoryFactory
    {
        private UserSubjectSetService userSubjectSetService = new UserSubjectSetService();

        #region 获取数据
        public async Task<List<ExperimentAttendStaticEntity>> GetList(ExperimentAttendStaticListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<ExperimentAttendStaticEntity>> GetPageList(ExperimentAttendStaticListParam param, Pagination pagination)
        {
            StringBuilder sql = new StringBuilder();
            var expression = ListFilter(param, sql);
            var oldSort = pagination.Sort;
            if (pagination.Sort!=null && (pagination.Sort.ToLower().IndexOf("totalnum") >-1|| pagination.Sort.ToLower().IndexOf("totalnumed") > -1|| pagination.Sort.ToLower().IndexOf("totalratio") > -1))
            {
                pagination.Sort = "Id";
            }
            var list = await this.BaseRepository().FindList<ExperimentAttendStaticEntity>(sql.ToString(), expression.ToArray(), pagination);
            if (list != null)
            {
                list.ForEach(m =>
                {
                    m.TotalNum = (m.NeedShowNum + m.NeedGroupNum + m.OptionalShowNum + m.OptionalGroupNum);
                    m.TotalNumed = (m.NeedShowNumed + m.NeedGroupNumed + m.OptionalShowNumed + m.OptionalGroupNumed);
                    m.TotalRatio = ((m.NeedShowNum + m.NeedGroupNum + m.OptionalShowNum + m.OptionalGroupNum) > 0 ? (((m.NeedShowNumed + m.NeedGroupNumed + m.OptionalShowNumed + m.OptionalGroupNumed) * (decimal)1.00) / (m.NeedShowNum + m.NeedGroupNum + m.OptionalShowNum + m.OptionalGroupNum)) * 100 : 0);
                });
                //处理字段排序问题
                if (oldSort != null && (oldSort.ToLower().IndexOf("totalnum") > -1 || oldSort.ToLower().IndexOf("totalnumed") > -1 || oldSort.ToLower().IndexOf("totalratio") > -1))
                {
                    if (pagination.SortType != null && pagination.SortType.ToLower().IndexOf("desc") > -1)
                    {
                        if (oldSort.ToLower() == "totalnum")
                        {
                            list = (from item in list
                                    orderby item.TotalNum descending
                                    select item);
                        }
                        else if (oldSort.ToLower() == "totalnumed")
                        {
                            list = (from item in list
                                    orderby item.TotalNumed descending
                                    select item);
                        }
                        else if (oldSort.ToLower() == "totalratio")
                        {
                            list = (from item in list
                                    orderby item.TotalRatio descending
                                    select item);
                        }

                    }
                    else
                    {
                        if (oldSort.ToLower() == "totalnum")
                        {
                            list = (from item in list
                                    orderby item.TotalNum ascending
                                    select item);
                        }
                        else if (oldSort.ToLower() == "totalnumed")
                        {
                            list = (from item in list
                                    orderby item.TotalNumed ascending
                                    select item);
                        }
                        else if (oldSort.ToLower() == "totalratio")
                        {
                            list = (from item in list
                                    orderby item.TotalRatio ascending
                                    select item);
                        }
                    }
                }
            }
            return list.ToList();
        }

        public async Task<List<ExperimentAttendStaticEntity>> GetCountyPageList(ExperimentAttendStaticListParam param, Pagination pagination)
        {
            StringBuilder sql = new StringBuilder();
            var expression = CountyListFilter(param, sql);
            var list = await this.BaseRepository().FindList<ExperimentAttendStaticEntity>(sql.ToString(), expression.ToArray(), pagination);
            if (list != null)
            {
                list.ForEach(m =>
                {
                    m.TotalNum = (m.NeedShowNum + m.NeedGroupNum + m.OptionalShowNum + m.OptionalGroupNum);
                    m.TotalNumed = (m.NeedShowNumed + m.NeedGroupNumed + m.OptionalShowNumed + m.OptionalGroupNumed);
                    m.TotalRatio = ((m.NeedShowNum + m.NeedGroupNum + m.OptionalShowNum + m.OptionalGroupNum) > 0 ? (((m.NeedShowNumed + m.NeedGroupNumed + m.OptionalShowNumed + m.OptionalGroupNumed) * (decimal)1.00) / (m.NeedShowNum + m.NeedGroupNum + m.OptionalShowNum + m.OptionalGroupNum)) * 100 : 0);
                });
            }
            return list.ToList();
        }

        public async Task<List<ExperimentAttendStaticEntity>> GetCityPageList(ExperimentAttendStaticListParam param, Pagination pagination)
        {
            StringBuilder sql = new StringBuilder();
            var expression = CityListFilter(param, sql);
            var list = await this.BaseRepository().FindList<ExperimentAttendStaticEntity>(sql.ToString(), expression.ToArray(), pagination);
            if (list != null)
            {
                list.ForEach(m =>
                {
                    m.TotalNum = (m.NeedShowNum + m.NeedGroupNum + m.OptionalShowNum + m.OptionalGroupNum);
                    m.TotalNumed = (m.NeedShowNumed + m.NeedGroupNumed + m.OptionalShowNumed + m.OptionalGroupNumed);
                    m.TotalRatio = ((m.NeedShowNum + m.NeedGroupNum + m.OptionalShowNum + m.OptionalGroupNum) > 0 ? (((m.NeedShowNumed + m.NeedGroupNumed + m.OptionalShowNumed + m.OptionalGroupNumed) * (decimal)1.00) / (m.NeedShowNum + m.NeedGroupNum + m.OptionalShowNum + m.OptionalGroupNum))*100 : 0);
                });
            }
            return list.ToList();
        }

        public async Task<ExperimentAttendStaticEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<ExperimentAttendStaticEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(ExperimentAttendStaticEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(ExperimentAttendStaticEntity entity, Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids?.Trim(','));
            if (ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");

            // 使用参数化查询和跨数据库兼容的 IN 语法
            var parameters = new List<DbParameter>
            {
                DbParameterExtension.CreateDbParameter("@BaseIsDelete", 1)
            };

            string strSql = $"UPDATE ex_ExperimentAttendStatic SET BaseIsDelete = @BaseIsDelete WHERE Id IN ({ids})";
            await this.BaseRepository().ExecuteBySql(strSql, parameters.ToArray());
        }

        /// <summary>
        /// 删除当前单位 ，当前学期的数据。
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task Deletes(ExperimentAttendStaticInputModel model)
        {
            if (model != null && model.SchoolId > 0)
            {
                string strSql = $" DELETE FROM ex_ExperimentAttendStatic WHERE SchoolId = {model.SchoolId} AND SchoolYearStart = {model.SchoolYearStart} AND SchoolTerm = {model.SchoolTerm} ";
                await this.BaseRepository().ExecuteBySql(strSql);
            }
        }

        #endregion

        #region 私有方法
        private Expression<Func<ExperimentAttendStaticEntity, bool>> ListFilter(ExperimentAttendStaticListParam param)
        {
            var expression = LinqExtensions.True<ExperimentAttendStaticEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (param.GradeId > 0)
                {
                    expression = expression.And(t => t.GradeId == param.GradeId);
                }
                if (param.CourseId > 0)
                {
                    expression = expression.And(t => t.CourseId == param.CourseId);
                }
                if (param.SchoolId > 0)
                {
                    expression = expression.And(t => t.SchoolId == param.SchoolId);
                }
                if (param.SchoolYearStart > 0)
                {
                    expression = expression.And(t => t.SchoolYearStart == param.SchoolYearStart);
                }
                if (param.SchoolTerm > 0)
                {
                    expression = expression.And(t => t.SchoolTerm == param.SchoolTerm);
                }
            }
            return expression;
        }

        private List<DbParameter> ListFilter(ExperimentAttendStaticListParam param, StringBuilder strSql)
        {
            strSql.Append(@$" SELECT * From (
                               SELECT  ES.Id ,
                                       ES.DataStorageType ,ES.SchoolId ,ES.GradeId ,ES.CourseId ,
                               		   ES.SchoolYearStart ,ES.SchoolYearEnd ,ES.SchoolTerm ,ES.SourceType ,ES.CompulsoryType ,
                                       ES.NeedShowNum ,ES.NeedGroupNum ,ES.OptionalShowNum ,ES.OptionalGroupNum ,
                                       ES.NeedShowNumed ,ES.NeedGroupNumed ,ES.OptionalShowNumed ,ES.OptionalGroupNumed ,
                                       SD.DictionaryId AS SchoolStageId ,SD.DicName AS SchoolStageName ,D1.DicName AS GradeName ,D2.DicName AS CourseName ,
                            (CASE WHEN ES.NeedShowNum > 0 THEN (CONVERT(MONEY,ES.NeedShowNumed) / CONVERT(MONEY,ES.NeedShowNum) * 100) WHEN ES.NeedShowNumed > 0 THEN 100 ELSE 0 END) AS NeedShowNumRatio ,
			                (CASE WHEN ES.NeedGroupNum > 0 THEN (CONVERT(MONEY,ES.NeedGroupNumed) / CONVERT(MONEY,ES.NeedGroupNum) * 100) WHEN ES.NeedGroupNumed > 0 THEN 100 ELSE 0  END ) AS NeedGroupNumRatio ,
			                (CASE WHEN ES.OptionalShowNum > 0 THEN (CONVERT(MONEY,ES.OptionalShowNumed) / CONVERT(MONEY,ES.OptionalShowNum) * 100) WHEN ES.OptionalShowNumed > 0 THEN 100 ELSE 0  END ) AS OptionalShowNumRatio ,
			                (CASE WHEN ES.OptionalGroupNum > 0 THEN (CONVERT(MONEY,ES.OptionalGroupNumed) / CONVERT(MONEY,ES.OptionalGroupNum) * 100) WHEN ES.OptionalGroupNumed > 0 THEN 100 ELSE 0 END) AS OptionalGroupNumRatio ,
                                        UR.UnitId AS CountyId ,U.Name AS SchoolName ,U.Sort ,UR2.UnitId AS CityId
                               FROM  ex_ExperimentAttendStatic AS ES
                               INNER JOIN  up_Unit AS U ON ES.SchoolId = U.Id
                               INNER JOIN  sys_static_dictionary AS D1 ON ES.GradeId = D1.DictionaryId AND D1.TypeCode = '{DicTypeCodeEnum.Grade.ParseToInt()}'
                               INNER JOIN  sys_static_dictionary AS D2 ON ES.CourseId = D2.DictionaryId AND D2.TypeCode = '{DicTypeCodeEnum.Course.ParseToInt()}'
                               INNER JOIN  up_UnitRelation AS UR ON UR.ExtensionObjId = ES.SchoolId AND UR.ExtensionType = 3
	                           INNER JOIN  up_UnitRelation AS UR2 ON UR.UnitId = UR2.ExtensionObjId AND UR2.ExtensionType = 3 AND UR2.BaseIsDelete = 0
                               INNER JOIN  sys_dictionary_relation AS dr ON D1.DictionaryId = dr.DictionaryToId  AND dr.RelationType = 1 AND dr.BaseIsDelete = 0
					           INNER JOIN  sys_static_dictionary AS SD ON dr.DictionaryId = SD.DictionaryId AND SD.TypeCode = '{DicTypeCodeEnum.SchoolStage.ParseToInt()}' AND SD.BaseIsDelete = 0
                               WHERE ES.BaseIsDelete = 0
                          ) as T WHERE  1 = 1 ");
            strSql = SqlHelper.DmMoneyReplaceDecimal(strSql);
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (!param.SchoolId.IsNullOrZero())
                {
                    strSql.Append(" AND SchoolId = @SchoolId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
                }
                if (!param.CountyId.IsNullOrZero())
                {
                    strSql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
                if (param.CityId > 0)
                {
                    strSql.Append(" AND CityId = @CityId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CityId", param.CityId));
                }
                if (!param.SchoolYearStart.IsNullOrZero())
                {
                    strSql.Append(" AND SchoolYearStart = @SchoolYearStart ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolYearStart", param.SchoolYearStart));
                }
                if (!param.SchoolTerm.IsNullOrZero())
                {
                    strSql.Append(" AND SchoolTerm = @SchoolTerm ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolTerm", param.SchoolTerm));
                }
                if (!param.GradeId.IsNullOrZero())
                {
                    strSql.Append(" AND GradeId = @GradeId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@GradeId", param.GradeId));
                }
                if (!param.CourseId.IsNullOrZero())
                {
                    strSql.Append(" AND CourseId = @CourseId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (!param.SourceType.IsNullOrZero())
                {
                    strSql.Append(" AND SourceType = @SourceType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SourceType", param.SourceType));
                }
                if (!param.CompulsoryType.IsNullOrZero()) //教材类型
                {
                    strSql.Append(" AND CompulsoryType = @CompulsoryType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CompulsoryType", param.CompulsoryType));
                }
                if (!param.SchoolStageId.IsNullOrZero()) //学段
                {
                    strSql.Append(" AND SchoolStageId = @SchoolStageId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStageId", param.SchoolStageId));
                }
                if (param.MenuType > 0)
                {
                    if (param.MenuType == 1)
                    {
                        strSql.Append($" AND SchoolStageId <> {SchoolStageEnum.GaoZhong.ParseToInt()} ");
                    }
                    else if (param.MenuType == 2)
                    {
                        strSql.Append($" AND SchoolStageId = {SchoolStageEnum.GaoZhong.ParseToInt()} ");
                    }
                }
                if (!param.GradeName.IsEmpty())
                {
                    strSql.Append(" AND GradeName = @GradeName ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@GradeName", param.GradeName));
                }
                if (!param.CourseName.IsEmpty())
                {
                    strSql.Append(" AND CourseName = @CourseName ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseName", param.CourseName));
                }
                if (param.SetUserId > 0)
                {

                    //判断用户是否设置了学段、学科
                    var listStage = userSubjectSetService.GetUserSetStageList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listStage.Count > 0)
                    {
                        string stageId = string.Join(",", listStage.Select(f => f.DictionaryId));
                        strSql.Append($" AND SchoolStageId IN ({stageId})");
                    }

                    var listCourse = userSubjectSetService.GetUserSetCourseList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listCourse.Count > 0)
                    {
                        string courseId = string.Join(",", listCourse.Select(f => f.DictionaryId));
                        strSql.Append($" AND CourseId IN ({courseId})");
                    }

                }
            }
            return parameter;
        }

        private List<DbParameter> CountyListFilter(ExperimentAttendStaticListParam param, StringBuilder strSql)
        {
            string whereSchool = "";
            if (param != null && param.SchoolId > 0)
            {
                whereSchool = string.Format(" AND SchoolId = {0} ", param.SchoolId);
            }
            strSql.Append(@$" SELECT * From (
                               SELECT  UR.UnitId ,ES.GradeId ,ES.CourseId ,
                               		   ES.SchoolYearStart ,ES.SchoolYearEnd ,ES.SchoolTerm ,ES.SourceType ,ES.CompulsoryType ,
                                       SD.DictionaryId AS SchoolStageId ,SD.DicName AS SchoolStageName ,D1.DicName AS GradeName ,D2.DicName AS CourseName ,
									   SUM(ES.NeedShowNum) AS NeedShowNum ,SUM(ES.NeedGroupNum) AS NeedGroupNum ,SUM(ES.OptionalShowNum) AS OptionalShowNum ,SUM(OptionalGroupNum) AS OptionalGroupNum ,
									   SUM(ES.NeedShowNumed) AS NeedShowNumed ,SUM(ES.NeedGroupNumed) AS NeedGroupNumed ,SUM(ES.OptionalShowNumed) AS OptionalShowNumed ,SUM(OptionalGroupNumed) AS OptionalGroupNumed ,
									   (CASE WHEN SUM(ES.NeedShowNum) > 0 THEN (CONVERT(MONEY, SUM(ES.NeedShowNumed)) / CONVERT(MONEY,SUM(ES.NeedShowNum)) * 100) WHEN SUM(ES.NeedShowNumed) > 0 THEN 100 ELSE 0 END) AS NeedShowNumRatio ,
									   (CASE WHEN SUM(ES.NeedGroupNum) > 0 THEN (CONVERT(MONEY,SUM(ES.NeedGroupNumed)) / CONVERT(MONEY,SUM(ES.NeedGroupNum)) * 100) WHEN SUM(ES.NeedGroupNumed) > 0 THEN 100 ELSE 0 END ) AS NeedGroupNumRatio ,
									   (CASE WHEN SUM(ES.OptionalShowNum) > 0 THEN (CONVERT(MONEY,SUM(ES.OptionalShowNumed)) / CONVERT(MONEY,SUM(ES.OptionalShowNum)) * 100) WHEN SUM(ES.OptionalShowNumed) > 0 THEN 100 ELSE 0 END ) AS OptionalShowNumRatio ,
									   (CASE WHEN SUM(ES.OptionalGroupNum) > 0 THEN (CONVERT(MONEY,SUM(ES.OptionalGroupNumed)) / CONVERT(MONEY,SUM(ES.OptionalGroupNum)) * 100) WHEN SUM(ES.OptionalGroupNumed) > 0 THEN 100 ELSE 0 END) AS OptionalGroupNumRatio ,
									   UR.UnitId AS CountyId ,sa.AreaName AS CountyName ,UR2.UnitId AS CityId
                               FROM  ex_ExperimentAttendStatic AS ES
                               INNER JOIN  up_Unit AS U ON ES.SchoolId = U.Id
                               INNER JOIN  sys_static_dictionary AS D1 ON ES.GradeId = D1.DictionaryId AND D1.TypeCode = '{DicTypeCodeEnum.Grade.ParseToInt()}'
                               INNER JOIN  sys_static_dictionary AS D2 ON ES.CourseId = D2.DictionaryId AND D2.TypeCode = '{DicTypeCodeEnum.Course.ParseToInt()}'
                               INNER JOIN  up_UnitRelation AS UR ON UR.ExtensionObjId = ES.SchoolId AND UR.ExtensionType = 3
                               INNER JOIN  sys_dictionary_relation AS dr ON D1.DictionaryId = dr.DictionaryToId  AND dr.RelationType = 1 AND dr.BaseIsDelete = 0
					           INNER JOIN  sys_static_dictionary AS SD ON dr.DictionaryId = SD.DictionaryId AND SD.TypeCode = '{DicTypeCodeEnum.SchoolStage.ParseToInt()}' AND SD.BaseIsDelete = 0
                               INNER JOIN  up_UnitRelation AS UR2 ON UR.UnitId = UR2.ExtensionObjId AND UR2.ExtensionType = 3 AND UR2.BaseIsDelete = 0
							   INNER JOIN  up_Unit AS u2 ON ur.UnitId = u2.Id AND u2.BaseIsDelete = 0
							   LEFT JOIN  SysArea AS sa ON sa.AreaCode = u2.AreaId AND sa.BaseIsDelete = 0
                               WHERE ES.BaseIsDelete = 0 {whereSchool}
							   GROUP BY UR2.UnitId ,UR.UnitId ,sa.AreaName ,ES.SchoolYearStart ,ES.SchoolYearEnd ,ES.SchoolTerm ,ES.GradeId ,ES.CourseId ,ES.SourceType ,ES.CompulsoryType
							   ,SD.DictionaryId ,SD.DicName ,D1.DicName ,D2.DicName
                          ) as T WHERE  1 = 1 ");
            strSql = SqlHelper.DmMoneyReplaceDecimal(strSql);
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (!param.CityId.IsNullOrZero())
                {
                    strSql.Append(" AND CityId = @CityId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CityId", param.CityId));
                }
                if (!param.CountyId.IsNullOrZero())
                {
                    strSql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
                if (!param.SchoolYearStart.IsNullOrZero())
                {
                    strSql.Append(" AND SchoolYearStart = @SchoolYearStart ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolYearStart", param.SchoolYearStart));
                }
                if (!param.SchoolTerm.IsNullOrZero())
                {
                    strSql.Append(" AND SchoolTerm = @SchoolTerm ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolTerm", param.SchoolTerm));
                }
                if (!param.GradeId.IsNullOrZero())
                {
                    strSql.Append(" AND GradeId = @GradeId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@GradeId", param.GradeId));
                }
                if (!param.CourseId.IsNullOrZero())
                {
                    strSql.Append(" AND CourseId = @CourseId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (!param.SourceType.IsNullOrZero()) //统计口径
                {
                    strSql.Append(" AND SourceType = @SourceType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SourceType", param.SourceType));
                }
                if (!param.CompulsoryType.IsNullOrZero()) //教材类型
                {
                    strSql.Append(" AND CompulsoryType = @CompulsoryType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CompulsoryType", param.CompulsoryType));
                }
                if (!param.SchoolStageId.IsNullOrZero()) //学段
                {
                    strSql.Append(" AND SchoolStageId = @SchoolStageId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStageId", param.SchoolStageId));
                }
                if (param.MenuType > 0)
                {
                    if (param.MenuType == 1)
                    {
                        strSql.Append($" AND SchoolStageId <> {SchoolStageEnum.GaoZhong.ParseToInt()} ");
                    }
                    else if (param.MenuType == 2)
                    {
                        strSql.Append($" AND SchoolStageId = {SchoolStageEnum.GaoZhong.ParseToInt()} ");
                    }
                }
                if (param.SetUserId > 0)
                {
                    //判断用户是否设置了学段、学科
                    var listStage = userSubjectSetService.GetUserSetStageList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listStage.Count > 0)
                    {
                        string stageId = string.Join(",", listStage.Select(f => f.DictionaryId));
                        strSql.Append($" AND SchoolStageId IN ({stageId})");
                    }

                    var listCourse = userSubjectSetService.GetUserSetCourseList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listCourse.Count > 0)
                    {
                        string courseId = string.Join(",", listCourse.Select(f => f.DictionaryId));
                        strSql.Append($" AND CourseId IN ({courseId})");
                    }
                }
            }
            return parameter;
        }


        private List<DbParameter> CityListFilter(ExperimentAttendStaticListParam param, StringBuilder strSql)
        {
            strSql.Append(@$" SELECT *  From (
                               SELECT  ES.GradeId ,ES.CourseId ,
                               		   ES.SchoolYearStart ,ES.SchoolYearEnd ,ES.SchoolTerm ,ES.SourceType ,ES.CompulsoryType ,
                                       SD.DictionaryId AS SchoolStageId ,SD.DicName AS SchoolStageName ,D1.DicName AS GradeName ,D2.DicName AS CourseName ,
									   SUM(ES.NeedShowNum) AS NeedShowNum ,SUM(ES.NeedGroupNum) AS NeedGroupNum ,SUM(ES.OptionalShowNum) AS OptionalShowNum ,SUM(OptionalGroupNum) AS OptionalGroupNum ,
									   SUM(ES.NeedShowNumed) AS NeedShowNumed ,SUM(ES.NeedGroupNumed) AS NeedGroupNumed ,SUM(ES.OptionalShowNumed) AS OptionalShowNumed ,SUM(OptionalGroupNumed) AS OptionalGroupNumed ,
									   (CASE WHEN SUM(ES.NeedShowNum) > 0 THEN (CONVERT(MONEY, SUM(ES.NeedShowNumed)) / CONVERT(MONEY,SUM(ES.NeedShowNum)) * 100) ELSE 0 END) AS NeedShowNumRatio ,
									   (CASE WHEN SUM(ES.NeedGroupNum) > 0 THEN (CONVERT(MONEY,SUM(ES.NeedGroupNumed)) / CONVERT(MONEY,SUM(ES.NeedGroupNum)) * 100) ELSE 0 END ) AS NeedGroupNumRatio ,
									   (CASE WHEN SUM(ES.OptionalShowNum) > 0 THEN (CONVERT(MONEY,SUM(ES.OptionalShowNumed)) / CONVERT(MONEY,SUM(ES.OptionalShowNum)) * 100) ELSE 0 END ) AS OptionalShowNumRatio ,
									   UR2.UnitId AS CityId
                               FROM  ex_ExperimentAttendStatic AS ES
                               INNER JOIN  up_Unit AS U ON ES.SchoolId = U.Id
                               INNER JOIN  sys_static_dictionary AS D1 ON ES.GradeId = D1.DictionaryId AND D1.TypeCode = '{DicTypeCodeEnum.Grade.ParseToInt()}'
                               INNER JOIN  sys_static_dictionary AS D2 ON ES.CourseId = D2.DictionaryId AND D2.TypeCode = '{DicTypeCodeEnum.Course.ParseToInt()}'
                               INNER JOIN  up_UnitRelation AS UR ON UR.ExtensionObjId = ES.SchoolId AND UR.ExtensionType = 3
                               INNER JOIN  sys_dictionary_relation AS dr ON D1.DictionaryId = dr.DictionaryToId  AND dr.RelationType = 1 AND dr.BaseIsDelete = 0
					           INNER JOIN  sys_static_dictionary AS SD ON dr.DictionaryId = SD.DictionaryId AND SD.TypeCode = '{DicTypeCodeEnum.SchoolStage.ParseToInt()}' AND SD.BaseIsDelete = 0
                               INNER JOIN  up_UnitRelation AS UR2 ON UR.UnitId = UR2.ExtensionObjId AND UR2.ExtensionType = 3 AND UR2.BaseIsDelete = 0
							   INNER JOIN  up_Unit AS u2 ON ur.UnitId = u2.Id AND u2.BaseIsDelete = 0
                               WHERE ES.BaseIsDelete = 0
							   GROUP BY UR2.UnitId ,ES.SchoolYearStart ,ES.SchoolYearEnd ,ES.SchoolTerm ,ES.GradeId ,ES.CourseId ,ES.SourceType ,ES.CompulsoryType
							   ,SD.DictionaryId ,SD.DicName ,D1.DicName ,D2.DicName
                          ) as T WHERE  1 = 1 ");
            strSql = SqlHelper.DmMoneyReplaceDecimal(strSql);
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (!param.CityId.IsNullOrZero())
                {
                    strSql.Append(" AND CityId = @CityId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CityId", param.CityId));
                }
                if (!param.SchoolYearStart.IsNullOrZero())
                {
                    strSql.Append(" AND SchoolYearStart = @SchoolYearStart ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolYearStart", param.SchoolYearStart));
                }
                if (!param.SchoolTerm.IsNullOrZero())
                {
                    strSql.Append(" AND SchoolTerm = @SchoolTerm ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolTerm", param.SchoolTerm));
                }
                if (!param.GradeId.IsNullOrZero())
                {
                    strSql.Append(" AND GradeId = @GradeId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@GradeId", param.GradeId));
                }
                if (!param.CourseId.IsNullOrZero())
                {
                    strSql.Append(" AND CourseId = @CourseId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (!param.SourceType.IsNullOrZero()) //统计口径
                {
                    strSql.Append(" AND SourceType = @SourceType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SourceType", param.SourceType));
                }
                if (!param.CompulsoryType.IsNullOrZero()) //教材类型
                {
                    strSql.Append(" AND CompulsoryType = @CompulsoryType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CompulsoryType", param.CompulsoryType));
                }
                if (!param.SchoolStageId.IsNullOrZero()) //学段
                {
                    strSql.Append(" AND SchoolStageId = @SchoolStageId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStageId", param.SchoolStageId));
                }
                if (param.MenuType > 0)
                {
                    if (param.MenuType == 1)
                    {
                        strSql.Append($" AND SchoolStageId <> {SchoolStageEnum.GaoZhong.ParseToInt()} ");
                    }
                    else if (param.MenuType == 2)
                    {
                        strSql.Append($" AND SchoolStageId = {SchoolStageEnum.GaoZhong.ParseToInt()} ");
                    }
                }
            }
            return parameter;
        }

        /// <summary>
        /// 实验开出统计-班级明细
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<List<ExperimentBookingEntity>> GetExperimentAttendStaticDetailFilter(int sourceType, ExperimentBookingListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            var parameter = new List<DbParameter>();
            string needShowNumSqlStr = ""; //演示必做(应开)
            string needGroupNumSqlStr = ""; //分组必做(应开)
            string optionalShowNumSqlStr = ""; //演示选做(应开)
            string optionalGroupNumSqlStr = ""; //分组选做(应开)

            string needShowNumedWhere = ""; //演示必做（实开）
            string needGroupNumedWhere = ""; //分组必做（实开）
            string optionShowNumedWhere = ""; //演示选做（实开）
            string optionalGroupNumedWhere = ""; //分组选做（实开）

            string allNumSqlStr = ""; //小计(应开)

            //查询实验计划应开数
            string planInfoSql = @"SELECT TOP 1 {0} FROM ex_ExperimentBooking EBS
                                        INNER JOIN  ex_PlanInfo P ON EBS.PlanInfoId = P.Id
                                        WHERE EBS.SchoolId = EB.SchoolId AND EBS.SchoolYearStart = EB.StaticSchoolYearStart AND EBS.SchoolTerm = EB.StaticSchoolTerm
                                        AND EBS.CourseId = EB.CourseId AND EBS.Statuz = 100 AND EBS.BaseIsDelete = 0 AND P.GradeId = EB.StaticGradeId
                                        AND EBS.SourceType = 1 AND EBS.TextbookVersionDetailId > 0";

            //查询实验目录应开数
            string textbookVersionSql = @"SELECT TOP 1 {0} FROM  ex_TextbookVersionParameter TVP
                                            WHERE TVP.GradeId = EB.StaticGradeId AND TVP.SchoolTerm = EB.StaticSchoolTerm
                                            AND TVP.CourseId =  EB.CourseId AND TVP.Statuz = 1 AND TVP.BaseIsDelete = 0 AND EB.SourceType = 2";

            //查询校本实验应开数
            string schoolPlanInfoSql = @"SELECT TOP 1 {0} FROM ex_ExperimentBooking EBS
                                            INNER JOIN  ex_PlanInfo P ON EBS.PlanInfoId = P.Id
                                            WHERE EBS.SchoolId = EB.SchoolId AND EBS.SchoolYearStart = EB.StaticSchoolYearStart AND EBS.SchoolTerm = EB.StaticSchoolTerm
                                            AND EBS.CourseId = EB.CourseId AND EBS.Statuz = 100 AND EBS.BaseIsDelete = 0 AND P.GradeId = EB.StaticGradeId
                                            AND EBS.SourceType = 1 AND EBS.TextbookVersionDetailId = 0";


            switch (sourceType) //统计口径
            {
                case 1:  //实验计划
                    needShowNumSqlStr = string.Format(planInfoSql, "NeedShowNum");
                    needGroupNumSqlStr = string.Format(planInfoSql, "NeedGroupNum");
                    optionalShowNumSqlStr = string.Format(planInfoSql, "OptionalShowNum");
                    optionalGroupNumSqlStr = string.Format(planInfoSql, "OptionalGroupNum");
                    allNumSqlStr = string.Format(planInfoSql, "(NeedGroupNum+NeedShowNum+OptionalGroupNum+OptionalShowNum)");
                    needShowNumedWhere = " AND EBS.SourceType = 1 AND EBS.TextbookVersionDetailId > 0  AND PD.IsNeedDo = 1 ";
                    needGroupNumedWhere = " AND EBS.SourceType = 1 AND EBS.TextbookVersionDetailId > 0  AND PD.IsNeedDo = 1 ";
                    optionShowNumedWhere = " AND EBS.SourceType = 1 AND EBS.TextbookVersionDetailId > 0 AND PD.IsNeedDo = 2 ";
                    optionalGroupNumedWhere = " AND EBS.SourceType = 1 AND EBS.TextbookVersionDetailId > 0 AND PD.IsNeedDo = 2 ";
                    break;
                case 2: //实验目录
                    needShowNumSqlStr = string.Format(textbookVersionSql, "StandardNeedShowNum");
                    needGroupNumSqlStr = string.Format(textbookVersionSql, "StandardNeedGroupNum");
                    optionalShowNumSqlStr = string.Format(textbookVersionSql, "StandardOptionalShowNum");
                    optionalGroupNumSqlStr = string.Format(textbookVersionSql, "StandardOptionalGroupNum");
                    allNumSqlStr = string.Format(textbookVersionSql, "(StandardNeedShowNum+StandardNeedGroupNum+StandardOptionalShowNum+StandardOptionalGroupNum)");
                    needShowNumedWhere = " AND EBS.SourceType = 2 AND TBV.IsNeedDo = 1 ";
                    needGroupNumedWhere = " AND EBS.SourceType = 2 AND TBV.IsNeedDo = 1 ";
                    optionShowNumedWhere = " AND EBS.SourceType = 2 AND TBV.IsNeedDo = 2 ";
                    optionalGroupNumedWhere = " AND EBS.SourceType = 2 AND TBV.IsNeedDo = 2 ";
                    break;
                case 3: //校本实验
                    needShowNumSqlStr = string.Format(schoolPlanInfoSql, "SchoolNeedShowNum");
                    needGroupNumSqlStr = string.Format(schoolPlanInfoSql, "SchoolNeedGroupNum");
                    optionalShowNumSqlStr = string.Format(schoolPlanInfoSql, "SchoolOptionalShowNum");
                    optionalGroupNumSqlStr = string.Format(schoolPlanInfoSql, "SchoolOptionalGroupNum");
                    allNumSqlStr = string.Format(schoolPlanInfoSql, "(SchoolNeedGroupNum+SchoolNeedShowNum+SchoolOptionalGroupNum+SchoolOptionalShowNum)");
                    needShowNumedWhere = " AND EBS.SourceType = 1 AND EBS.TextbookVersionDetailId = 0  AND PD.IsNeedDo = 1 ";
                    needGroupNumedWhere = " AND EBS.SourceType = 1 AND EBS.TextbookVersionDetailId = 0  AND PD.IsNeedDo = 1 ";
                    optionShowNumedWhere = " AND EBS.SourceType = 1 AND EBS.TextbookVersionDetailId = 0 AND PD.IsNeedDo = 2 ";
                    optionalGroupNumedWhere = " AND EBS.SourceType = 1 AND EBS.TextbookVersionDetailId = 0 AND PD.IsNeedDo = 2 ";
                    break;
            }


            strSql.Append(@$"SELECT DISTINCT {sourceType} AS SourceType ,CountyId ,SchoolId ,SchoolName ,StaticSchoolYearStart ,StaticSchoolYearEnd ,StaticSchoolTerm ,SchoolStageName ,
                              GradeName ,ClassDesc ,CourseName ,StaticGradeId ,CourseId ,SchoolGradeClassId
                                 ,ISNULL(NeedShowNum,0) AS NeedShowNum ,ISNULL(NeedShowNumed,0) AS NeedShowNumed ,ISNULL(NeedGroupNum,0) AS NeedGroupNum ,ISNULL(NeedGroupNumed,0) AS NeedGroupNumed
                              ,ISNULL(OptionalShowNum,0) AS OptionalShowNum ,ISNULL(OptionalShowNumed,0) AS OptionalShowNumed ,ISNULL(OptionalGroupNum,0) AS OptionalGroupNum ,ISNULL(OptionalGroupNumed,0) AS OptionalGroupNumed ,ISNULL(AllNum,0) AS AllNum ,ISNULL(AllNumed,0) AS AllNumed
                     FROM (
                         SELECT UR.UnitId AS CountyId ,EB.SchoolId ,UT.Name AS SchoolName ,EB.StaticSchoolYearStart ,EB.StaticSchoolYearEnd ,EB.StaticSchoolTerm ,SD.DicName AS SchoolStageName ,
                          D1.DicName AS GradeName ,GC.ClassDesc ,D2.DicName AS CourseName ,EB.StaticGradeId ,EB.CourseId ,EB.SchoolGradeClassId
                             ,EB.SourceType ,EB.TextbookVersionDetailId
                             ,({needShowNumSqlStr}) AS NeedShowNum
                         ,( SELECT COUNT(1) FROM ex_ExperimentBooking EBS
                            LEFT JOIN  ex_PlanDetail AS PD ON PD.Id = EBS.PlanDetailId AND PD.BaseIsDelete = 0
                            LEFT JOIN ex_TextbookVersionDetail AS TBV ON TBV.Id = EBS.TextbookVersionDetailId AND TBV.BaseIsDelete = 0
                            WHERE EBS.SchoolId = EB.SchoolId AND EBS.SchoolGradeClassId = EB.SchoolGradeClassId AND EBS.SchoolYearStart = EB.StaticSchoolYearStart
                            AND EBS.SchoolYearEnd = EB.StaticSchoolYearEnd AND EBS.SchoolTerm = EB.StaticSchoolTerm AND EBS.CourseId = EB.CourseId AND EBS.Statuz = 100 AND EBS.BaseIsDelete = 0
                            AND EBS.ExperimentType = 1
                            {needShowNumedWhere}
                           ) AS NeedShowNumed
                           ,({needGroupNumSqlStr})  AS NeedGroupNum
                          ,( SELECT COUNT(1) FROM ex_ExperimentBooking EBS
                            LEFT JOIN  ex_PlanDetail AS PD ON PD.Id = EBS.PlanDetailId AND PD.BaseIsDelete = 0
                            LEFT JOIN ex_TextbookVersionDetail AS TBV ON TBV.Id = EBS.TextbookVersionDetailId AND TBV.BaseIsDelete = 0
                            WHERE EBS.SchoolId = EB.SchoolId AND EBS.SchoolGradeClassId = EB.SchoolGradeClassId AND EBS.SchoolYearStart = EB.StaticSchoolYearStart
                            AND EBS.SchoolYearEnd = EB.StaticSchoolYearEnd AND EBS.SchoolTerm = EB.StaticSchoolTerm AND EBS.CourseId = EB.CourseId AND EBS.Statuz = 100 AND EBS.BaseIsDelete = 0
                            AND EBS.ExperimentType = 2
                            {needGroupNumedWhere}
                           ) AS NeedGroupNumed
                           ,({optionalShowNumSqlStr}) AS OptionalShowNum
                           ,( SELECT COUNT(1) FROM ex_ExperimentBooking EBS
                            LEFT JOIN  ex_PlanDetail AS PD ON PD.Id = EBS.PlanDetailId AND PD.BaseIsDelete = 0
                            LEFT JOIN ex_TextbookVersionDetail AS TBV ON TBV.Id = EBS.TextbookVersionDetailId AND TBV.BaseIsDelete = 0
                            WHERE EBS.SchoolId = EB.SchoolId AND EBS.SchoolGradeClassId = EB.SchoolGradeClassId AND EBS.SchoolYearStart = EB.StaticSchoolYearStart
                            AND EBS.SchoolYearEnd = EB.StaticSchoolYearEnd AND EBS.SchoolTerm = EB.StaticSchoolTerm AND EBS.CourseId = EB.CourseId AND EBS.Statuz = 100 AND EBS.BaseIsDelete = 0
                            AND EBS.ExperimentType = 1
                            {optionShowNumedWhere}
                           ) AS OptionalShowNumed
                           ,({optionalGroupNumSqlStr}) AS OptionalGroupNum
                          ,( SELECT COUNT(1) FROM ex_ExperimentBooking EBS
                            LEFT JOIN  ex_PlanDetail AS PD ON PD.Id = EBS.PlanDetailId AND PD.BaseIsDelete = 0
                            LEFT JOIN ex_TextbookVersionDetail AS TBV ON TBV.Id = EBS.TextbookVersionDetailId AND TBV.BaseIsDelete = 0
                            WHERE EBS.SchoolId = EB.SchoolId AND EBS.SchoolGradeClassId = EB.SchoolGradeClassId AND EBS.SchoolYearStart = EB.StaticSchoolYearStart
                            AND EBS.SchoolYearEnd = EB.StaticSchoolYearEnd AND EBS.SchoolTerm = EB.StaticSchoolTerm AND EBS.CourseId = EB.CourseId AND EBS.Statuz = 100 AND EBS.BaseIsDelete = 0
                            AND EBS.ExperimentType = 2
                            {optionalGroupNumedWhere}
                           ) AS OptionalGroupNumed
                           ,({allNumSqlStr}) AS AllNum
                        ,COUNT(1) AS AllNumed
                      FROM  ex_ExperimentBooking AS EB
                      INNER JOIN  up_SchoolGradeClass AS GC ON EB.SchoolGradeClassId = GC.Id
                      INNER JOIN  sys_static_dictionary AS D13 ON GC.SchoolStage = D13.DictionaryId AND D13.TypeCode = '{DicTypeCodeEnum.SchoolStage.ParseToInt()}'
                      INNER JOIN  sys_static_dictionary AS D1 ON EB.StaticGradeId = D1.DictionaryId AND D1.TypeCode = '{DicTypeCodeEnum.Grade.ParseToInt()}'
                      INNER JOIN  sys_static_dictionary AS D2 ON EB.CourseId = D2.DictionaryId AND D2.TypeCode = '{DicTypeCodeEnum.Course.ParseToInt()}'
                         INNER JOIN  sys_dictionary_relation AS dr ON D1.DictionaryId = dr.DictionaryToId  AND dr.RelationType = 1 AND dr.BaseIsDelete = 0
                         INNER JOIN  sys_static_dictionary AS SD ON dr.DictionaryId = SD.DictionaryId AND SD.TypeCode = '{DicTypeCodeEnum.SchoolStage.ParseToInt()}' AND SD.BaseIsDelete = 0
                      INNER JOIN  up_UnitRelation AS UR ON UR.ExtensionObjId = EB.SchoolId AND UR.ExtensionType = 3 AND UR.BaseIsDelete = 0
                      INNER JOIN  up_Unit AS UT ON EB.SchoolId = UT.Id
                      LEFT JOIN  ex_ExperimentRecord AS ER ON EB.Id = ER.ExperimentBookingId AND ER.BaseIsDelete = 0
                      LEFT JOIN  ex_PlanDetail AS PD ON PD.Id = EB.PlanDetailId AND PD.BaseIsDelete = 0
                         LEFT JOIN ex_TextbookVersionDetail AS TBV ON TBV.Id = EB.TextbookVersionDetailId AND TBV.BaseIsDelete = 0
                      WHERE EB.BaseIsDelete = 0 AND EB.Statuz = 100");
            if (param != null)
            {
                if (!param.SchoolId.IsNullOrZero())
                {
                    strSql.Append($" AND EB.SchoolId = {param.SchoolId}");
                }
                if (!param.CountyId.IsNullOrZero())
                {
                    strSql.Append($" AND UR.UnitId = {param.CountyId}");
                    if (!param.SchoolId.IsNullOrZero())
                    {
                        strSql.Append($" AND EB.SchoolId = {param.SchoolId}");
                    }
                }
                if (!param.CityId.IsNullOrZero())
                {
                    strSql.Append($" AND UR2.UnitId = {param.CityId}");
                    if (!param.CountyId.IsNullOrZero())
                    {
                        strSql.Append($" AND UR.UnitId = {param.CountyId}");
                    }
                    if (!param.SchoolId.IsNullOrZero())
                    {
                        strSql.Append($" AND EB.SchoolId = {param.SchoolId}");
                    }
                }
                if (!param.SchoolYearStart.IsNullOrZero())
                {
                    strSql.Append($" AND EB.StaticSchoolYearStart = {param.SchoolYearStart}"); //开始学年
                }
                if (!param.SchoolTerm.IsNullOrZero())
                {
                    strSql.Append($" AND EB.StaticSchoolTerm = {param.SchoolTerm}"); //学期
                }
                if (!param.SchoolStageId.IsNullOrZero())
                {
                    strSql.Append($" AND SD.DictionaryId = {param.SchoolStageId}"); //学段
                }
                if (!param.GradeId.IsNullOrZero())
                {
                    strSql.Append($" AND EB.StaticGradeId = {param.GradeId}"); //年级
                }
                if (!param.SchoolGradeClassId.IsNullOrZero())
                {
                    strSql.Append($" AND EB.SchoolGradeClassId = {param.SchoolGradeClassId}"); //班级
                }
                if (!param.CourseId.IsNullOrZero())
                {
                    strSql.Append($" AND EB.CourseId = {param.CourseId}"); //学科
                }
                if (!param.SourceType.IsNullOrZero()) //统计口径
                {
                    if (param.SourceType.Value == 1)  //实验计划
                    {
                        strSql.Append($" AND EB.SourceType = 1 AND Eb.TextbookVersionDetailId > 0 ");
                    }
                    else if (param.SourceType.Value == 2) //实验目录
                    {
                        strSql.Append($" AND EB.SourceType = 2 ");
                    }
                    else if (param.SourceType.Value == 3) //校本实验
                    {
                        strSql.Append($" AND EB.SourceType = 1 AND Eb.TextbookVersionDetailId = 0 ");
                    }
                }
            }
            strSql.Append(" GROUP BY UR.UnitId ,EB.SchoolId ,UT.Name ,EB.StaticSchoolYearStart ,EB.StaticSchoolYearEnd ,EB.StaticSchoolTerm ,EB.SourceType, EB.StaticGradeId, EB.ClassId, EB.SchoolGradeClassId, D1.DicName, SD.DicName, ClassDesc, EB.CourseId, D2.DicName, EB.TextbookVersionDetailId");
            strSql.Append(" ) t");
            var list = await this.BaseRepository().FindList<ExperimentBookingEntity>(strSql.ToString(), parameter.ToArray(), pagination);
            return list.ToList();
        }
        #endregion
    }
}
