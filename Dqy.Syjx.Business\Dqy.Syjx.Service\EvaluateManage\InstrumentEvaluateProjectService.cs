﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.EvaluateManage;
using Dqy.Syjx.Model.Param.EvaluateManage;

namespace Dqy.Syjx.Service.EvaluateManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-11-26 15:10
    /// 描 述：达标参数设置服务类
    /// </summary>
    public class InstrumentEvaluateProjectService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<InstrumentEvaluateProjectEntity>> GetList(InstrumentEvaluateProjectListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<InstrumentEvaluateProjectEntity>> GetPageList(InstrumentEvaluateProjectListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<InstrumentEvaluateProjectEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<InstrumentEvaluateProjectEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(InstrumentEvaluateProjectEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(InstrumentEvaluateProjectEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update eq_InstrumentEvaluateProject set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
                strSql = $"update eq_InstrumentEvaluateProject set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }


        public async Task DeleteEvaluateProjectVersionForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");

            // 使用跨数据库兼容的 IN 语法替代 f_split
            string strSql = $"UPDATE eq_InstrumentEvaluateProjectVersion SET BaseIsDelete = 1 WHERE Id IN ({ids})";
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<InstrumentEvaluateProjectEntity, bool>> ListFilter(InstrumentEvaluateProjectListParam param)
        {
            var expression = LinqExtensions.True<InstrumentEvaluateProjectEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (param.IsDefaultEv.HasValue)
                {
                    expression = expression.And(t => t.IsDefaultEv == param.IsDefaultEv);
                }
            }
            return expression;
        }
        #endregion
    }
}
