﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.SystemManage;
using Dqy.Syjx.Model.Param.SystemManage;
using Dqy.Syjx.Util.Tools;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Dqy.Syjx.Entity.ExperimentTeachManage;
using Dqy.Syjx.Enum.ExperimentTeachManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Model.Param.ExperimentTeachManage;

namespace Dqy.Syjx.Service.SystemManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2024-06-13 14:26
    /// 描 述：备份数据服务类
    /// </summary>
    public class StatisticsBackupsService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<StatisticsBackupsEntity>> GetList(StatisticsBackupsListParam param)
  
        }

        public async Task<List<StatisticsBackupsEntity>> GetList(StatisticsBackupsListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<StatisticsBackupsEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<StatisticsBackupsEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(StatisticsBackupsEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }
        public async Task SaveAddListForm(List<StatisticsBackupsEntity> list)
        {
            list.ForEach(async m => await m.Create());
            //foreach (var item in list)
            //{
            //    await item.Create();
            //}
            await this.BaseRepository().Insert(list);
        }

        public async Task SaveUpdateListForm(List<StatisticsBackupsEntity> list, List<string> fields)
        {
            list.ForEach(async m => await m.Modify());
            await this.BaseRepository().Update(list, fields);
        }

        public async Task SaveTransForm(StatisticsBackupsEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update bn_StatisticsBackups set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
                strSql = $"update bn_StatisticsBackups set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<StatisticsBackupsEntity, bool>> ListFilter(StatisticsBackupsListParam param)
        {
            var expression = LinqExtensions.True<StatisticsBackupsEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (!string.IsNullOrEmpty(param.PageCode))
                {
                    expression = expression.And(t => t.PageCode == param.PageCode);
                }
                if (param.ModuleId > 0)
                {
                    expression = expression.And(t => t.ModuleId == param.ModuleId);
                }
                if (param.BackupYear > 0)
                {
                    expression = expression.And(t => t.BackupYear == param.BackupYear);
                }
                if (param.BackupYearge > 0)
                {
                    expression = expression.And(t => t.BackupYear >= param.BackupYearge);
                }
                if (param.BackupType > 0)
                {
                    expression = expression.And(t => t.BackupType == param.BackupType);
                }
                if (param.SchoolId > 0)
                {
                    expression = expression.And(t => t.UnitId == param.SchoolId);
                }
            }
            return expression;
        }


        /// <summary>
        /// 获取实验目录预约列表
        /// </summary>
        /// <param name="param">查询参数</param>
        /// <param name="pagination">分页参数</param>
        /// <returns></returns>
        public async Task<List<StatisticsBackupsEntity>> GetPageList(StatisticsBackupsListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            var parameter = new List<DbParameter>();
            strSql.Append($@" SELECT * FROM (
                SELECT bk.* , u.Name AS SchoolName ,u.Sort AS UnitSort ,se.SchoolProp ,ur.UnitId AS CountyId
                FROM bn_StatisticsBackups AS bk
                INNER JOIN up_Unit AS u ON u.BaseIsDelete = 0 AND bk.UnitId = u.Id
                INNER JOIN up_UnitRelation AS ur ON ur.BaseIsDelete = 0 AND bk.UnitId = ur.ExtensionObjId
                LEFT JOIN up_SchoolExtension  AS se ON u.Id = se.UnitId
             ) AS tab01 where 1= 1 ");
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = 0 ");
                if (param.SchoolId > 0)
                {
                    strSql.Append(" AND UnitId = @SchoolId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
                }
                if (param.CountyId > 0)
                {
                    strSql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
                if (param.ModuleId > 0)
                {
                    strSql.Append(" AND ModuleId = @ModuleId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ModuleId", param.ModuleId));
                }
                if (!string.IsNullOrEmpty(param.PageCode))
                {
                    strSql.Append(" AND PageCode = @PageCode ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@PageCode", param.PageCode));
                }
                if (param.BackupType > 0)
                {
                    strSql.Append(" AND BackupType = @BackupType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@BackupType", param.BackupType));
                }
                if (param.BackupYear > 0)
                {
                    strSql.Append(" AND BackupYear = @BackupYear ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@BackupYear", param.BackupYear));
                }
            }
            var list = await this.BaseRepository().FindList<StatisticsBackupsEntity>(strSql.ToString(), parameter.ToArray(), pagination);
            return list.ToList();
        }

        /// <summary>
        /// 获取实验目录预约列表
        /// </summary>
        /// <param name="param">查询参数</param>
        /// <param name="pagination">分页参数</param>
        /// <returns></returns>
        public async Task<List<StatisticsBackupsEntity>> GetAllUnitPageList(StatisticsBackupsListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            var parameter = new List<DbParameter>();
            string whereBackup = "";
            if (param.ModuleId > 0)
            {
                whereBackup += " AND bk.ModuleId = @ModuleId ";
                parameter.Add(DbParameterExtension.CreateDbParameter("@ModuleId", param.ModuleId));
            }
            if (!string.IsNullOrEmpty(param.PageCode))
            {
                whereBackup += " AND bk.PageCode = @PageCode ";
                parameter.Add(DbParameterExtension.CreateDbParameter("@PageCode", param.PageCode));
            }
            if (param.BackupType > 0)
            {
                whereBackup += " AND bk.BackupType = @BackupType ";
                parameter.Add(DbParameterExtension.CreateDbParameter("@BackupType", param.BackupType));
            }
            if (param.BackupYear > 0)
            {
                whereBackup += " AND bk.BackupYear = @BackupYear ";
                parameter.Add(DbParameterExtension.CreateDbParameter("@BackupYear", param.BackupYear));
            }

            strSql.Append($@" SELECT * FROM (
                 SELECT bk.* ,u.Id AS SchoolId , u.Name AS SchoolName ,u.Code AS SchoolCode,u.Sort AS UnitSort ,se.SchoolProp ,ur.UnitId AS CountyId
                 FROM up_Unit AS u
                 INNER JOIN up_UnitRelation AS ur ON ur.BaseIsDelete = 0 AND u.Id = ur.ExtensionObjId
                 LEFT JOIN up_SchoolExtension  AS se ON u.Id = se.UnitId
                 LEFT JOIN bn_StatisticsBackups AS bk ON bk.BaseIsDelete = 0 AND u.Id = bk.UnitId {whereBackup}
                 where  u.BaseIsDelete = 0  
             ) AS tab01 where 1= 1 ");
            if (param != null)
            {
                if (param.SchoolId > 0)
                {
                    strSql.Append(" AND UnitId = @SchoolId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
                }
                if (param.CountyId > 0)
                {
                    strSql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                } 
            }
            var list = await this.BaseRepository().FindList<StatisticsBackupsEntity>(strSql.ToString(), parameter.ToArray(), pagination);
            return list.ToList();
        }
        #endregion
    }
}

