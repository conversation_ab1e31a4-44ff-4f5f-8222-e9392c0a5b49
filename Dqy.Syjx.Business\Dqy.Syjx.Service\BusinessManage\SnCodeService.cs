﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.BusinessManage;
using Dqy.Syjx.Model.Param.BusinessManage;
using NPOI.POIFS.FileSystem;

namespace Dqy.Syjx.Service.BusinessManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2023-06-26 09:24
    /// 描 述：二维码存储服务类
    /// </summary>
    public class SnCodeService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<SnCodeEntity>> GetList(SnCodeListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<SnCodeEntity>> GetPageList(SnCodeListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<SnCodeEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<SnCodeEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(SnCodeEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(SnCodeEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
               strSql = $" update bn_SnCode set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
               strSql = $"update bn_SnCode set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }

        /// <summary>
        /// 获取并锁定指定数量的二维码
        /// </summary>
        /// <param name="num"></param>
        /// <param name="categoryCode"></param>
        /// <param name="unitId"></param>
        /// <returns></returns>
        public async Task<List<SnCodeEntity>> GetAndLockSnCodes(string categoryCode, int num, long unitId)
        {
            SnCodeListParam param = new SnCodeListParam() {
                UnitId = 0, CategoryCode = categoryCode
            };
            Pagination pagination = new Pagination() {
                PageIndex = 1,
                PageSize = num,
                Sort = "Sn510",
                SortType = "asc",
            };
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression, pagination);
            foreach (var item in list)
            {
                item.UnitId = unitId;
                await item.Modify();
                await base.BaseRepository().Update(item);
            }


            return list.ToList();
        }
        #endregion

        #region 私有方法
        private Expression<Func<SnCodeEntity, bool>> ListFilter(SnCodeListParam param)
        {
            var expression = LinqExtensions.True<SnCodeEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (!string.IsNullOrEmpty(param.CategoryCode))
                {
                    expression = expression.And(t => t.CategoryCode == param.CategoryCode);
                }
                if (!string.IsNullOrEmpty(param.Sn6))
                {
                    expression = expression.And(t => t.Sn6.Contains(param.Sn6));
                }
                if(param.UnitId != -1)
                {
                    expression = expression.And(t =>t.UnitId == param.UnitId);
                }
            }
            return expression;
        }
        #endregion
    }
}
