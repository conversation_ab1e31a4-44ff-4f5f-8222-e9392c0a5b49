﻿using Dqy.Syjx.Business.BusinessManage;
using Dqy.Syjx.Business.OrganizationManage;
using Dqy.Syjx.Business.PersonManage;
using Dqy.Syjx.Entity.BusinessManage;
using Dqy.Syjx.Entity.ExperimentTeachManage;
using Dqy.Syjx.Entity.InstrumentManage;
using Dqy.Syjx.Entity.OrganizationManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Enum.ExperimentTeachManage;
using Dqy.Syjx.Enum.InstrumentManage;
using Dqy.Syjx.Enum.OrganizationManage;
using Dqy.Syjx.Model.Param.BusinessManage;
using Dqy.Syjx.Model.Param.ExperimentTeachManage;
using Dqy.Syjx.Model.Param.InstrumentManage;
using Dqy.Syjx.Model.Param.OrganizationManage;
using Dqy.Syjx.Model.Param.QueryStatisticsManage;
using Dqy.Syjx.Model.Result.OrganizationManage;
using Dqy.Syjx.Service.BusinessManage;
using Dqy.Syjx.Service.ExperimentTeachManage;
using Dqy.Syjx.Service.InstrumentManage;
using Dqy.Syjx.Service.OrganizationManage;
using Dqy.Syjx.Service.QueryStatisticsManage;
using Dqy.Syjx.Service.SystemManage;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Web.Code;
using NetTaste;
using NPOI.OpenXmlFormats.Wordprocessing;
using Senparc.NeuChar.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dqy.Syjx.Business.QueryStatisticsManage
{
    public class WorkBLL
    {
        /// <summary>
        /// ApiToken值（仅WebApi端传入）
        /// </summary>
        public string ApiToken = string.Empty;

        private PlanInfoService planInfoService = new PlanInfoService();
        private PlanDetailService planDetailService = new PlanDetailService();
        private UserSchoolStageSubjectService userSchoolStageSubjectService = new UserSchoolStageSubjectService();
        private UserClassInfoBLL userClassInfoBLL = new UserClassInfoBLL();
        private ExperimentBookingService experimentBookingService = new ExperimentBookingService();

        private SchoolInstrumentService schoolInstrumentService = new SchoolInstrumentService();
        private FunRoomService funRoomService = new FunRoomService();

        private UserSubjectSetService userSubjectSetService = new UserSubjectSetService();
        private UserExperimenterSetService userExperimenterSetService = new UserExperimenterSetService();
        private SchoolGradeClassService schoolGradeClassService = new SchoolGradeClassService();
        private InstrumentService instrumentService = new InstrumentService();
        private InstrumentLendService instrumentLendService = new InstrumentLendService();
        private FunRoomUseService funRoomUseService = new FunRoomUseService();
        private UnitService unitService = new UnitService();

        #region 基础数据

        /// <summary>
        /// 获取已编制计划集合
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<List<PlanInfoEntity>> GetPlanedList(PlanInfoListParam param)
        {
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            param.UnitId = operatorinfo.UnitId.Value;
            param.UserId = operatorinfo.UserId.Value;
            param.UnitType = operatorinfo.UnitType;
            param.CurrentSchoolTerm = operatorinfo.SchoolTerm;
            param.CurrentSchoolTermStartYear = operatorinfo.SchoolTermStartYear;
            param.SetUserId = operatorinfo.UserId.Value;
            param.ActivityType = ActivityTypeEnum.InClass.ParseToInt();
            if (operatorinfo.RoleValues != null)
            {
                var listRole = operatorinfo.RoleValues.Split(",");
                string laboratory = RoleValueEnum.LaboratoryManager.ParseToInt().ToString();
                string teacher = RoleValueEnum.CommonPerson.ParseToInt().ToString();
                //string group = RoleValueEnum.GroupLeader.ParseToInt().ToString();
                //string business = RoleValueEnum.BusinessManager.ParseToInt().ToString();
                if (listRole.Where(m => m != laboratory && m != teacher).Count() > 0)
                {
                    //显示所有,存在其他角色。
                }
                else
                {
                    List<int> courseids = new List<int>();
                    List<int> gradeids = new List<int>();

                    //获取对应的学科，取交集。
                    if (listRole.Where(m => m == laboratory).Count() > 0)
                    {
                        //获取实验员对应的学科
                        var paramSubject = new UserSchoolStageSubjectListParam();
                        paramSubject.UserId = operatorinfo.UserId ?? 0;
                        paramSubject.UnitId = operatorinfo.UnitId ?? 0;
                        paramSubject.UnitType = operatorinfo.UnitType;
                        paramSubject.IsCurrentUnit = IsEnum.Yes.ParseToInt();
                        var listSubject = await userSchoolStageSubjectService.GetList(paramSubject);

                        if (listSubject != null && listSubject.Count > 0)
                        {  
                            courseids.AddRange(listSubject.Where(m => m.SubjectIdz > 0).Select(m => m.SubjectIdz).ToList());
                            if (listSubject.Select(m => m.SchoolStageIdz).Distinct().Count() == 1)
                            {                               
                                if (listSubject[0].SchoolStageIdz > 0)
                                {
                                    param.SchoolStageId = listSubject[0].SchoolStageIdz;
                                }
                            }
                        }
                    }
                    if (listRole.Where(m => m == teacher).Count() > 0)
                    {
                        ExperimentBookingListParam paramBooking = new ExperimentBookingListParam();
                        await userClassInfoBLL.GetTeachCourseGrades(paramBooking, operatorinfo);
                        if (paramBooking != null)
                        {
                            if (paramBooking.GradeIds != null)
                            {
                                gradeids.AddRange(paramBooking.GradeIds);
                            }
                            if (paramBooking.CourseIds != null)
                            {
                                courseids.AddRange(paramBooking.CourseIds);
                            }
                        }
                    }
                    param.GradeIds = gradeids;
                    param.CourseIds = courseids;
                }
            }
            var list = await planInfoService.GetPageList(param, new Pagination() { PageSize = int.MaxValue });
            return list;
        }

        /// <summary>
        /// 获取登记实验集合
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<List<ExperimentBookingEntity>> GetRecordList(ExperimentBookingListParam param)
        {
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            if (operatorinfo.UnitType ==UnitTypeEnum.School.ParseToInt())
            {
                param.SchoolId = operatorinfo.UnitId;
                param.BookUserId = operatorinfo.UserId;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                param.CountyId = operatorinfo.UnitId;
            }
            //param.RecordMode = ExperimentRecordModeEnum.BookIng.ParseToInt();
            param.ThanStatuz = ExperimentBookStatuzEnum.WaitRecord.ParseToInt();
            param.IsMain = 0;
            param.ActivityType = ActivityTypeEnum.InClass.ParseToInt();
            var list = await experimentBookingService.GetStaticPageList(param, null);
            return list;
        }

        /// <summary>
        /// 获取仪器数据
        /// </summary>
        /// <param name="param">参数（前段传递参数Statuz:30已入库）</param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<decimal> GetInstrumentList(SchoolInstrumentListParam param)
        {
            //List<SchoolInstrumentEntity>
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            param.SafeguardUserId = operatorinfo.UserId;
            param.UnitId = operatorinfo.UnitId ?? 0;
            if (operatorinfo.IsSystem == 1 || operatorinfo.UnitType == 0) //系统管理员、配置超管查看全部仪器
            {
                param.UnitId = 0;
                param.SafeguardUserId = 0;
            }

            //var list = await schoolInstrumentService.GetPageList(param, null);

            var StockNum = await schoolInstrumentService.GetTotalSum(param);
            return StockNum;
        }

        /// <summary>
        /// 实验（专用）室统计页面-学校端
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<FunRoomEntity> GetFunroomNum(FunRoomListParam param)
        {
            TData<List<FunRoomEntity>> obj = new TData<List<FunRoomEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            param.UserId = operatorinfo.UserId.Value;
            param.UnitType = operatorinfo.UnitType;
            if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                param.CountyId = operatorinfo.UnitId.Value;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                param.UnitId = operatorinfo.UnitId.Value;
            }
            var entity = await funRoomService.GetTotalSum(param, "", "");
            return entity;
        }

        /// <summary>
        /// 获取实验员集合
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<List<UserExperimenterSetEntity>> GetExperimenterUserList(UserExperimenterSetListParam param)
        {
            OperatorInfo user = await Operator.Instance.Current(ApiToken);
            param.IsExperimenter = IsEnum.Yes.ParseToInt();
            param.UnitId = user.UnitId;
            param.UnitType = user.UnitType;
            param.IsCurrentUnit = IsEnum.Yes.ParseToInt();

            if (user.UnitType == UnitTypeEnum.County.ParseToInt() || user.UnitType == UnitTypeEnum.City.ParseToInt())
            {
                //获取当前人是否设置了学段学科。
                var stagelist = await userSubjectSetService.GetUserSetStageList(user.UserId ?? 0);
                if (stagelist != null && stagelist.Count > 0)
                {
                    stagelist = stagelist.Where(m => m.UserSubjectSetId > 0).ToList();
                    if (param.SchoolPropList == null)
                    {
                        param.SchoolPropList = new List<int>();
                    }
                    foreach (var item in stagelist)
                    {
                        param.SchoolPropList.AddRange(UnitBLL.GetSchoolPropList(item.DictionaryId ?? 0));
                    }
                }
                if (!(param.SubjectId > 0))
                {
                    //获取当前人是否设置了学段学科。
                    var courselist = await userSubjectSetService.GetUserSetCourseList(user.UserId ?? 0, 1);
                    if (courselist != null && courselist.Count > 0)
                    {
                        param.SubjectList = courselist.Where(m => m.UserSubjectSetId > 0).Select(m => m.DictionaryId ?? 0).ToList();
                    }
                }
            }
            var list = await userExperimenterSetService.GetPageList(param, null);
            return list;
        }


        #endregion

        #region 待办事项

        /// <summary>
        /// 待安排实验集合
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<List<ExperimentBookingEntity>> GetArrangeList(ExperimentBookingListParam param)
        {
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);

            if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                param.SafeguardUserId = operatorinfo.UserId;
                param.SchoolId = operatorinfo.UnitId;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                param.CountyId = operatorinfo.UnitId;

            }
            else if (operatorinfo.UnitType == UnitTypeEnum.City.ParseToInt())
            {
                param.CityId = operatorinfo.UnitId;
            }
            param.RecordMode = ExperimentRecordModeEnum.BookIng.ParseToInt();
            param.Statuz = ExperimentBookStatuzEnum.WaitArrange.ParseToInt();
            param.IsMain = 1;
            param.ActivityType = ActivityTypeEnum.InClass.ParseToInt();

            var list = await experimentBookingService.GetArrangeList(param, new Pagination() { PageSize = int.MaxValue });
            return list;
        }

        // <summary>
        /// 实验待登记集合（根据实验的实验员查询）
        /// </summary>
        /// <param name="param">查询参数</param>
        /// <returns></returns>
        public async Task<List<ExperimentBookingEntity>> GetWaitRecordList(ExperimentBookingListParam param)
        {
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
 
            param.RecordMode = ExperimentRecordModeEnum.BookIng.ParseToInt();
            param.Statuz = ExperimentBookStatuzEnum.WaitRecord.ParseToInt();
            param.IsMain = 0;
            param.ActivityType = ActivityTypeEnum.InClass.ParseToInt();
            if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                param.SchoolId = operatorinfo.UnitId;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                param.CountyId = operatorinfo.UnitId;
            }

            var list = await experimentBookingService.GetStaticPageList(param, null);
            return list;
        }

        public async Task<List<SchoolInstrumentEntity>> GetWaitApproveInstrumentList(SchoolInstrumentListParam param)
        {
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);

            //查询当前用户创建的仪器
            param.IsShowAlreadyInputStorage = 0;
            if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                param.UserId = operatorinfo.UserId;
                param.UnitId = operatorinfo.UnitId;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                param.CountyId = operatorinfo.UnitId;
            }
            return await schoolInstrumentService.GetPageList(param, null);
        }

        public async Task<List<InstrumentLendEntity>> GetLendInstrumentList(InstrumentLendListParam param)
        {
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);  
            param.InstrumentAttribute = 1;
            if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                param.SchoolId = operatorinfo.UnitId;
                param.SafeguardUserId = operatorinfo.UserId;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                param.CountyId = operatorinfo.UnitId;
            }
            return await instrumentLendService.GetPageList(param, null);
        }

        public async Task<List<PlanInfoEntity>> GetBookingWeekList(PlanDetailListParam param)
        {
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            var paramPlan = new PlanDetailListParam();
            paramPlan.SchoolYearStart = operatorinfo.SchoolTermStartYear;
            paramPlan.SchoolTerm = operatorinfo.SchoolTerm;

            var weekNum = 0;
            if (operatorinfo.FirstWeekDate != null)
            {
                weekNum = GetWeekNumberOfYear(DateTime.Now, (DateTime)operatorinfo.FirstWeekDate);
            }
            if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                paramPlan.SchoolId = operatorinfo.UnitId.Value;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                paramPlan.CountyId = operatorinfo.UnitId.Value;
            }
            paramPlan.WeekNum = weekNum + 1;
            return await planDetailService.GetList(paramPlan, null);
        }

        private int GetWeekNumberOfYear(DateTime currentDate, DateTime firstWeekDate)
        {
            // 确保firstWeekDate是周一（如果不是，则找到那一周的周一）
            DayOfWeek firstDayOfWeek = firstWeekDate.DayOfWeek;
            int daysToFirstMonday = (firstDayOfWeek == DayOfWeek.Monday) ? 0 : (7 - (int)firstDayOfWeek + 1) % 7;
            DateTime actualFirstWeekMonday = firstWeekDate.AddDays(-daysToFirstMonday);

            // 计算从那一周的周一到当前日期之间的天数
            TimeSpan daysDifference = currentDate - actualFirstWeekMonday;

            // 将天数转换为周数（注意要加1，因为我们要包括第一周在内）
            int weekNumber = (int)Math.Ceiling(daysDifference.TotalDays / 7.0) + 1;

            // 如果计算出的周数小于1（理论上不应该发生，除非currentDate在firstWeekDate之前很久），则返回1
            // 或者，如果你想要处理跨年情况，你可能需要添加额外的逻辑来确保周数是正确的
            return Math.Max(1, weekNumber);
        }


        #endregion

        #region 超期预警、平台使用情况明细表、平台单位情况表

        /// <summary>
        /// 平台使用情况明细表
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<List<UnitStatisticModel>> GetUnitSchoolStageDetailList(UnitListParam param)
        {
            List<UnitStatisticModel> list = new List<UnitStatisticModel>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            var listSchool = await unitService.GetSchoolSchoolStageList(param, null);
            if (listSchool != null && listSchool.Count > 0)
            {
                //统计数据。
                SchoolInstrumentListParam paramInstrument = new SchoolInstrumentListParam();

                if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    paramInstrument.UnitId = operatorinfo.UnitId;
                }
                else if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    paramInstrument.CountyId = operatorinfo.UnitId;
                }
                var listInstrument = await schoolInstrumentService.GetPageList(paramInstrument, null);

                //实验室数量
                FunRoomListParam paramFunroom = new FunRoomListParam();
                paramFunroom.UserId = operatorinfo.UserId.Value;
                paramFunroom.UnitType = operatorinfo.UnitType;
                if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    paramFunroom.CountyId = operatorinfo.UnitId.Value;
                }
                else if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    paramFunroom.UnitId = operatorinfo.UnitId.Value;
                }
                paramFunroom.Nature = FunNatureTypeEnum.Room.ParseToInt();
                var listFunroom = await funRoomService.GetStatistics(paramFunroom, null);

                //使用登记记录
                FunRoomUseListParam paramFunrromUse = new FunRoomUseListParam();
                paramFunrromUse.UnitType = operatorinfo.UnitType;
                paramFunrromUse.SchoolId = operatorinfo.UnitId.Value;
                if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    paramFunrromUse.CountyId = operatorinfo.UnitId.Value;
                }
                else if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    paramFunrromUse.SchoolId = operatorinfo.UnitId.Value;
                }
                paramFunrromUse.NatureType = FunNatureTypeEnum.ZhuanRoom.ParseToInt();
                paramFunrromUse.CurrentSchoolTerm = operatorinfo.SchoolTerm;
                paramFunrromUse.CurrentSchoolTermStartYear = operatorinfo.SchoolTermStartYear;
                paramFunrromUse.CurrentSchoolTermStartDate = operatorinfo.SchoolTermStartDate.Value;
                paramFunrromUse.CurrentSchoolTermEndDate = operatorinfo.SchoolTermEndDate.Value;
                var listFunroomUse = await funRoomUseService.GetPageList(paramFunrromUse, null);

                //获取实验员信息
                var paramUserExt = new UserExtensionListParam();
                paramUserExt.UnitId = operatorinfo.UnitId ?? 0;
                paramUserExt.UnitType = operatorinfo.UnitType;
                paramUserExt.IsExperimenter = IsEnum.Yes.ParseToInt();
                paramUserExt.IsCurrentUnit = IsEnum.Yes.ParseToInt();
                var listUserExp = await userExperimenterSetService.GetSchoolStageCourseList(paramUserExt);

                //编制计划
                var paramPlan = new PlanInfoListParam();
                //paramPlan.UnitId = operatorinfo.UnitId.Value;
                //paramPlan.UnitType = operatorinfo.UnitType;
                //paramPlan.CurrentSchoolTerm = operatorinfo.SchoolTerm;
                //paramPlan.CurrentSchoolTermStartYear = operatorinfo.SchoolTermStartYear;
                //paramPlan.ActivityType = ActivityTypeEnum.InClass.ParseToInt();
                //var listPlan = await planInfoService.GetPageList(paramPlan, new Pagination() { PageSize = int.MaxValue });

                var paramGradeClass = new SchoolGradeClassListParam();
                paramPlan.UnitType = operatorinfo.UnitType;
                paramGradeClass.UnitType = operatorinfo.UnitType;
                if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    //param.SchoolStageList = operatorinfo.SchoolStageList;
                    paramPlan.UnitId = operatorinfo.UnitId ?? 0;
                    paramPlan.SchoolId = operatorinfo.UnitId ?? 0;
                    paramGradeClass.UnitId = operatorinfo.UnitId ?? 0;
                }
                else if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    paramPlan.UnitId = operatorinfo.UnitId ?? 0;

                    paramGradeClass.CountyId = operatorinfo.UnitId ?? 0;
                    paramGradeClass.UnitId = paramPlan.SchoolId;
                }

                paramPlan.ActivityType = ActivityTypeEnum.InClass.ParseToInt();
                var listPlan = await planInfoService.GetPageList(paramPlan, null);
                if (listPlan != null && listPlan.Count > 0)
                {
                    var pagePlanDetail = new Pagination();
                    pagePlanDetail.PageSize = int.MaxValue;
                    var listRecordDetail = await planInfoService.PlanExpPageList(paramPlan, pagePlanDetail);
                    IEnumerable<SchoolGradeClassEntity> listGradeClass = null;
                    listGradeClass = await schoolGradeClassService.GetStatistics(paramGradeClass);
                    listPlan.ForEach(m =>
                    {
                        m.NeedShowNum = m.NeedShowNum * GetExameClassNum(listGradeClass, m.GradeId, m.SchoolId, 0, m.CourseId, m.CompulsoryType ?? 0);
                        m.NeedGroupNum = m.NeedGroupNum * GetExameClassNum(listGradeClass, m.GradeId, m.SchoolId, 0, m.CourseId, m.CompulsoryType ?? 0);
                        m.OptionalShowNum = m.OptionalShowNum * GetExameClassNum(listGradeClass, m.GradeId, m.SchoolId, 0, m.CourseId, m.CompulsoryType ?? 0);
                        m.OptionalGroupNum = m.OptionalGroupNum * GetExameClassNum(listGradeClass, m.GradeId, m.SchoolId, 0, m.CourseId, m.CompulsoryType ?? 0);
                        m.NeedShowNumed = NeedShowNumed(listRecordDetail, m.Id ?? 0, m.SchoolId, m.GradeId, 0, listGradeClass, m.SchoolStageId, m.CourseId, m.CompulsoryType ?? 0);
                        m.NeedGroupNumed = NeedGroupNumed(listRecordDetail, m.Id ?? 0, m.SchoolId, m.GradeId, 0, listGradeClass, m.SchoolStageId, m.CourseId, m.CompulsoryType ?? 0);
                        m.OptionalShowNumed = OptionalShowNumed(listRecordDetail, m.Id ?? 0, m.SchoolId, m.GradeId, 0, listGradeClass, m.SchoolStageId, m.CourseId, m.CompulsoryType ?? 0);
                        m.OptionalGroupNumed = OptionalGroupNumed(listRecordDetail, m.Id ?? 0, m.SchoolId, m.GradeId, 0, listGradeClass, m.SchoolStageId, m.CourseId, m.CompulsoryType ?? 0);
                    });

                }
                //单位学段，转换输出实体
                list = listSchool.Select(n => new UnitStatisticModel()
                {
                    Id = n.Id ?? 0,
                    Name = n.Name,
                    SchoolStageName = n.SchoolStageName,
                    SchoolStageId = n.SchoolStageId,
                    Sort = n.Sort,
                    InstrumentNum = listInstrument.Where(m => m.SchoolId == n.Id && m.StageId == (n.SchoolStageId ?? 0).ToString()).Sum(n => n.StockNum),
                    InstrumentNewNum = listInstrument.Where(m => m.SchoolId == n.Id && m.StageId == (n.SchoolStageId ?? 0).ToString() && m.PurchaseDate != null && ((DateTime)m.PurchaseDate).Year == DateTime.Now.Year).Sum(n => n.StockNum),
                    FunroomNum = listFunroom.Where(m => m.UnitId == n.Id && m.SchoolStagez.Contains((n.SchoolStageId ?? 0).ToString())).Count(),
                    FunroomUseNum = listFunroomUse.Where(m => m.UnitId == n.Id && m.SchoolStage == (n.SchoolStageId ?? 0)).Count(),
                    ExperimenterUserNum = listUserExp.Where(m => m.UnitId == n.Id && m.SchoolStageIdz != null && m.SchoolStageIdz.Contains((n.SchoolStageId ?? 0).ToString())).Count(),
                    ExperimenterUserMajorNum = listUserExp.Where(m => m.UnitId == n.Id && m.ExperimenterNature == ExperimenterNatureEnum.FullTime.ParseToInt() && m.SchoolStageIdz != null && m.SchoolStageIdz.Contains((n.SchoolStageId ?? 0).ToString())).Count(),
                    ExperimentNum = listPlan.Where(m => m.SchoolId == n.Id && m.SchoolStageId == (n.SchoolStageId ?? 0)).Sum(j => j.TotalNum),
                    PlanExperimentNum = listPlan.Where(m => m.SchoolId == n.Id && m.SchoolStageId == (n.SchoolStageId ?? 0)).Sum(j => j.Num),
                    RecordExperimentNum = listPlan.Where(m => m.SchoolId == n.Id && m.SchoolStageId == (n.SchoolStageId ?? 0)).Sum(j => j.TotalNumed)
                    , ExperimentRate = listPlan.Where(m => m.SchoolId == n.Id && m.SchoolStageId == (n.SchoolStageId ?? 0)).Count()> 0?( listPlan.Where(m => m.SchoolId == n.Id && m.SchoolStageId == (n.SchoolStageId ?? 0)).Sum(j => j.TotalRatio)/ listPlan.Where(m => m.SchoolId == n.Id && m.SchoolStageId == (n.SchoolStageId ?? 0)).Count()):0

                }).ToList();

            }
            return list;
        }

        /// <summary>
        /// 平台使用情况表
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<List<UnitStatisticModel>> GetUnitSchoolStageList(UnitListParam param)
        {
            List<UnitStatisticModel> list = new List<UnitStatisticModel>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            var listSchool = await unitService.GetSchoolSchoolStageList(param, null);
            if (listSchool != null && listSchool.Count > 0)
            {
                //统计数据。
                SchoolInstrumentListParam paramInstrument = new SchoolInstrumentListParam();

                if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    paramInstrument.UnitId = operatorinfo.UnitId;
                }
                else if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    paramInstrument.CountyId = operatorinfo.UnitId;
                }
                var listInstrument = await schoolInstrumentService.GetPageList(paramInstrument, null);

                //实验室数量
                FunRoomListParam paramFunroom = new FunRoomListParam();
                //paramFunroom.UserId = operatorinfo.UserId.Value;
                paramFunroom.UnitType = operatorinfo.UnitType;
                if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    paramFunroom.CountyId = operatorinfo.UnitId.Value;
                }
                else if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    paramFunroom.UnitId = operatorinfo.UnitId.Value;
                }
                paramFunroom.Nature = FunNatureTypeEnum.Room.ParseToInt();
                var listFunroom = await funRoomService.GetStatistics(paramFunroom, null);

                //使用登记记录
                FunRoomUseListParam paramFunrromUse = new FunRoomUseListParam();
                paramFunrromUse.UnitType = operatorinfo.UnitType;
                paramFunrromUse.SchoolId = operatorinfo.UnitId.Value;
                if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    paramFunrromUse.CountyId = operatorinfo.UnitId.Value;
                }
                else if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    paramFunrromUse.SchoolId = operatorinfo.UnitId.Value;
                }
                paramFunrromUse.NatureType = FunNatureTypeEnum.ZhuanRoom.ParseToInt();
                paramFunrromUse.CurrentSchoolTerm = operatorinfo.SchoolTerm;
                paramFunrromUse.CurrentSchoolTermStartYear = operatorinfo.SchoolTermStartYear;
                paramFunrromUse.CurrentSchoolTermStartDate = operatorinfo.SchoolTermStartDate.Value;
                paramFunrromUse.CurrentSchoolTermEndDate = operatorinfo.SchoolTermEndDate.Value;
                var listFunroomUse = await funRoomUseService.GetPageList(paramFunrromUse, null);

                //获取实验员信息
                var paramUserExt = new UserExtensionListParam();
                paramUserExt.UnitId = operatorinfo.UnitId ?? 0;
                paramUserExt.UnitType = operatorinfo.UnitType;
                paramUserExt.IsExperimenter = IsEnum.Yes.ParseToInt();
                paramUserExt.IsCurrentUnit = IsEnum.Yes.ParseToInt();
                var listUserExp = await userExperimenterSetService.GetSchoolStageCourseList(paramUserExt);

                //编制计划
                var paramPlan = new PlanInfoListParam();
                //paramPlan.UnitId = operatorinfo.UnitId.Value;
                //paramPlan.UnitType = operatorinfo.UnitType;
                //paramPlan.CurrentSchoolTerm = operatorinfo.SchoolTerm;
                //paramPlan.CurrentSchoolTermStartYear = operatorinfo.SchoolTermStartYear;
                //paramPlan.ActivityType = ActivityTypeEnum.InClass.ParseToInt();
                //var listPlan = await planInfoService.GetPageList(paramPlan, new Pagination() { PageSize = int.MaxValue });

                var paramGradeClass = new SchoolGradeClassListParam();
                paramPlan.UnitType = operatorinfo.UnitType;
                paramGradeClass.UnitType = operatorinfo.UnitType;
                if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    //param.SchoolStageList = operatorinfo.SchoolStageList;
                    paramPlan.UnitId = operatorinfo.UnitId ?? 0;
                    paramPlan.SchoolId = operatorinfo.UnitId ?? 0;
                    paramGradeClass.UnitId = operatorinfo.UnitId ?? 0;
                }
                else if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    paramPlan.UnitId = operatorinfo.UnitId ?? 0;

                    paramGradeClass.CountyId = operatorinfo.UnitId ?? 0;
                    paramGradeClass.UnitId = paramPlan.SchoolId;
                }

                paramPlan.ActivityType = ActivityTypeEnum.InClass.ParseToInt();
                var listPlan = await planInfoService.GetPageList(paramPlan, null);
                if (listPlan != null && listPlan.Count > 0)
                {
                    var pagePlanDetail = new Pagination();
                    pagePlanDetail.PageSize = int.MaxValue;
                    var listRecordDetail = await planInfoService.PlanExpPageList(paramPlan, pagePlanDetail);
                    IEnumerable<SchoolGradeClassEntity> listGradeClass = null;
                    listGradeClass = await schoolGradeClassService.GetStatistics(paramGradeClass);
                    listPlan.ForEach(m =>
                    {
                        m.NeedShowNum = m.NeedShowNum * GetExameClassNum(listGradeClass, m.GradeId, m.SchoolId, 0, m.CourseId, m.CompulsoryType ?? 0);
                        m.NeedGroupNum = m.NeedGroupNum * GetExameClassNum(listGradeClass, m.GradeId, m.SchoolId, 0, m.CourseId, m.CompulsoryType ?? 0);
                        m.OptionalShowNum = m.OptionalShowNum * GetExameClassNum(listGradeClass, m.GradeId, m.SchoolId, 0, m.CourseId, m.CompulsoryType ?? 0);
                        m.OptionalGroupNum = m.OptionalGroupNum * GetExameClassNum(listGradeClass, m.GradeId, m.SchoolId, 0, m.CourseId, m.CompulsoryType ?? 0);
                        m.NeedShowNumed = NeedShowNumed(listRecordDetail, m.Id ?? 0, m.SchoolId, m.GradeId, 0, listGradeClass, m.SchoolStageId, m.CourseId, m.CompulsoryType ?? 0);
                        m.NeedGroupNumed = NeedGroupNumed(listRecordDetail, m.Id ?? 0, m.SchoolId, m.GradeId, 0, listGradeClass, m.SchoolStageId, m.CourseId, m.CompulsoryType ?? 0);
                        m.OptionalShowNumed = OptionalShowNumed(listRecordDetail, m.Id ?? 0, m.SchoolId, m.GradeId, 0, listGradeClass, m.SchoolStageId, m.CourseId, m.CompulsoryType ?? 0);
                        m.OptionalGroupNumed = OptionalGroupNumed(listRecordDetail, m.Id ?? 0, m.SchoolId, m.GradeId, 0, listGradeClass, m.SchoolStageId, m.CourseId, m.CompulsoryType ?? 0);
                    });

                }

                //单位学段，转换输出实体
                list = listSchool.Select(n => new UnitStatisticModel()
                {
                    Id = n.Id ?? 0,
                    Name = n.Name,
                    SchoolStageName = n.SchoolStageName,
                    SchoolStageId = n.SchoolStageId,
                    Sort = n.Sort,
                    InstrumentNum = listInstrument.Where(m => m.SchoolId == n.Id && m.StageId == (n.SchoolStageId ?? 0).ToString()).Sum(n => n.StockNum),
                    InstrumentNewNum = listInstrument.Where(m => m.SchoolId == n.Id && m.StageId == (n.SchoolStageId ?? 0).ToString() && m.PurchaseDate != null && ((DateTime)m.PurchaseDate).Year == DateTime.Now.Year).Sum(n => n.StockNum),
                    FunroomNum = listFunroom.Where(m => m.UnitId == n.Id && m.SchoolStagez.Contains((n.SchoolStageId ?? 0).ToString())).Count(),
                    FunroomUseNum = listFunroomUse.Where(m => m.UnitId == n.Id && m.SchoolStage == (n.SchoolStageId ?? 0)).Count(),
                    ExperimenterUserNum = listUserExp.Where(m => m.UnitId == n.Id && m.SchoolStageIdz.Contains((n.SchoolStageId ?? 0).ToString())).Count(),
                    ExperimenterUserMajorNum = listUserExp.Where(m => m.UnitId == n.Id && m.ExperimenterNature == ExperimenterNatureEnum.FullTime.ParseToInt() && m.SchoolStageIdz.Contains((n.SchoolStageId ?? 0).ToString())).Count(),
                    ExperimentNum = listPlan.Where(m => m.SchoolId == n.Id && m.SchoolStageId == (n.SchoolStageId ?? 0)).Sum(j => j.TotalNum),
                    PlanExperimentNum = listPlan.Where(m => m.SchoolId == n.Id && m.SchoolStageId == (n.SchoolStageId ?? 0)).Sum(j => j.Num),
                    RecordExperimentNum = listPlan.Where(m => m.SchoolId == n.Id && m.SchoolStageId == (n.SchoolStageId ?? 0)).Sum(j => j.TotalNumed),
                    ExperimentRate = listPlan.Where(m => m.SchoolId == n.Id && m.SchoolStageId == (n.SchoolStageId ?? 0)).Average(j => j.TotalRatio)

                }).ToList();

            }
            return list;
        }

        /// <summary>
        /// 实验开出超期(学校)
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<List<UnitStatisticModel>> GetOverDueList(UnitListParam param)
        {
            //List<UnitStatisticModel> list= new List<UnitStatisticModel>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            param.Statuz = 1;
            param.Id = operatorinfo.UnitId ?? 0;
            var list = await unitService.GetSchoolGradeCourseList(param, null);

            var paramGradeClass = new SchoolGradeClassListParam();
            paramGradeClass.UnitId = operatorinfo.UnitId ?? 0;
            paramGradeClass.UnitType = operatorinfo.UnitType;
            var listGradeClass = await schoolGradeClassService.GetStatistics(paramGradeClass);

            if (listGradeClass != null && listGradeClass.Count() > 0)
            {
                var weekNum = 1;//当前时间，根据开始周次计算，是第几周
                if (operatorinfo.FirstWeekDate != null)
                {
                    weekNum = GetWeekNumberOfYear(DateTime.Now, (DateTime)operatorinfo.FirstWeekDate);
                }
                var paramPlanDetail = new PlanDetailListParam();
                paramPlanDetail.SchoolYearStart = operatorinfo.SchoolTermStartYear;
                paramPlanDetail.SchoolTerm = operatorinfo.SchoolTerm;
                paramPlanDetail.UnitType = operatorinfo.UnitType;
                paramPlanDetail.WeekNumgt = weekNum;
                paramPlanDetail.SchoolId = operatorinfo.UnitId ?? 0;
                var listPlanDetail = await planDetailService.GetList(paramPlanDetail, null);

                var paramBooking = new PlanInfoListParam();
                paramBooking.UnitType = operatorinfo.UnitType;
                paramBooking.SchoolId = operatorinfo.UnitId ?? 0;
                paramBooking.WeekNumgt = weekNum;
                var listRecordDetail = await planInfoService.PlanExpPageList(paramBooking, null);

                for (int i = 0; i < listGradeClass.Count(); i++)
                {
                    //查询当前单位，当前年级、学科
                    var entitySchool = list[i];
                    //应开实验数
                    entitySchool.PlanExperimentNum = GetCourseGradePlanNum(listPlanDetail, entitySchool.Id, listGradeClass, entitySchool.GradeId, entitySchool.CourseId, entitySchool.SchoolStageId ?? 0);//)listPlanDetail.Where(m => m.SchoolId == entitySchool.Id && m.GradeId == entitySchool.GradeId && m.CourseId == entitySchool.CourseId).Count();

                    entitySchool.RecordExperimentNum = GetCourseGradeNum(listRecordDetail, entitySchool.Id, listGradeClass, entitySchool.GradeId, entitySchool.CourseId, entitySchool.SchoolStageId ?? 0);

                    entitySchool.ExperimentNum = entitySchool.PlanExperimentNum - entitySchool.RecordExperimentNum;
                }
            }
            return list;
        }

        /// <summary>
        /// 实验开出超期(区县)
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<List<UnitStatisticModel>> GetUnitSchoolOverDueList(UnitListParam param)
        {
            //List<UnitStatisticModel> list= new List<UnitStatisticModel>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            param.Statuz = 1;
            param.Pid = operatorinfo.UnitId ?? 0;
            var list = await unitService.GetSchoolGradeCourseList(param, null);
            if (list != null && list.Count > 0)
            {
                var weekNum = 1;//当前时间，根据开始周次计算，是第几周
                if (operatorinfo.FirstWeekDate != null)
                {
                    weekNum = GetWeekNumberOfYear(DateTime.Now, (DateTime)operatorinfo.FirstWeekDate);
                }
                var paramPlanDetail = new PlanDetailListParam();
                paramPlanDetail.SchoolYearStart = operatorinfo.SchoolTermStartYear;
                paramPlanDetail.SchoolTerm = operatorinfo.SchoolTerm;
                paramPlanDetail.UnitType = operatorinfo.UnitType;
                paramPlanDetail.WeekNumgt = weekNum;
                paramPlanDetail.CountyId = operatorinfo.UnitId ?? 0;
                var listPlanDetail = await planDetailService.GetList(paramPlanDetail, null);

                var paramBooking = new PlanInfoListParam();
                paramBooking.UnitType = operatorinfo.UnitType;
                paramBooking.CountyId = operatorinfo.UnitId ?? 0;
                paramBooking.WeekNumgt = weekNum;
                var listRecordDetail = await planInfoService.PlanExpPageList(paramBooking, null);

                var paramGradeClass = new SchoolGradeClassListParam();
                paramGradeClass.CountyId = operatorinfo.UnitId ?? 0;
                paramGradeClass.UnitType = operatorinfo.UnitType;
                var listGradeClass = await schoolGradeClassService.GetStatistics(paramGradeClass);

                for (int i = 0; i < list.Count; i++)
                {
                    //查询当前单位，当前年级、学科
                    var entitySchool = list[i];
                    //应开实验数
                    entitySchool.PlanExperimentNum = GetCourseGradePlanNum(listPlanDetail, entitySchool.Id, listGradeClass, entitySchool.GradeId, entitySchool.CourseId, entitySchool.SchoolStageId??0);//)listPlanDetail.Where(m => m.SchoolId == entitySchool.Id && m.GradeId == entitySchool.GradeId && m.CourseId == entitySchool.CourseId).Count();

                    entitySchool.RecordExperimentNum = GetCourseGradeNum(listRecordDetail, entitySchool.Id, listGradeClass, entitySchool.GradeId, entitySchool.CourseId, entitySchool.SchoolStageId??0);

                    entitySchool.ExperimentNum =entitySchool.PlanExperimentNum - entitySchool.RecordExperimentNum;
                }
                list = list.OrderByDescending(x => x.ExperimentNum).ToList();
            }
            return list;
        }
        #endregion

        #region 私有方法

        #region 编制计划实验达标 私有方法

        private int NeedShowNumed(IEnumerable<PlanInfoEntity> list, long id, long schoolid, long gradeid = 0, long schoolgradeclassid = 0, IEnumerable<SchoolGradeClassEntity> listclass = null, int schoolstage = 0, int courseid = 0, int compulsorytype = 0, long teacherid = 0)
        {
            int num = 0;
            if (list != null)
            {
                var listTemp = list.Where(n => n.Id == id && n.IsNeedDo == 1 && n.ExperimentType == 1);
                if (listclass != null && schoolstage > 0)
                {
                    var listClassTemp = listclass.Where(n => UnitBLL.GetSchoolPropList(schoolstage).Contains(n.SchoolProp));
                    if (gradeid >= GradeEnum.GaoYi.ParseToInt())
                    {
                        if (compulsorytype == ClassCompulsoryTypeEnum.Select.ParseToInt())
                        {
                            listClassTemp = listClassTemp.Where(m => m.SelectSubject != null && m.SelectSubject.Contains(courseid.ToString()));
                        }
                        else
                        {
                            listClassTemp = listClassTemp.Where(m => m.SelectSubject == null || !m.SelectSubject.Contains(courseid.ToString()));
                        }
                    }
                    listTemp = listTemp.Where(m => listClassTemp.Select(m => m.Id).Contains(m.SchoolGradeClassId));
                }
                if (schoolid > 0)
                {
                    listTemp = listTemp.Where(m => m.SchoolId == schoolid);
                }
                if (gradeid > 0)
                {
                    listTemp = listTemp.Where(m => m.GradeId == gradeid);
                }
                if (schoolgradeclassid > 0)
                {
                    listTemp = listTemp.Where(m => m.SchoolGradeClassId == schoolgradeclassid);
                }
                if (teacherid > 0)
                {
                    listTemp = listTemp.Where(m => m.TeacherId == teacherid);
                }
                num = listTemp.Count();
            }
            return num;
        }
        private int NeedGroupNumed(IEnumerable<PlanInfoEntity> list, long id, long schoolid, long gradeid = 0, long schoolgradeclassid = 0, IEnumerable<SchoolGradeClassEntity> listclass = null, int schoolstage = 0, int courseid = 0, int compulsorytype = 0, long teacherid = 0)
        {
            int num = 0;
            if (list != null)
            {
                var listTemp = list.Where(n => n.Id == id && n.IsNeedDo == 1 && n.ExperimentType == 2);
                if (listclass != null && schoolstage > 0)
                {
                    var listClassTemp = listclass.Where(n => UnitBLL.GetSchoolPropList(schoolstage).Contains(n.SchoolProp));
                    if (gradeid >= GradeEnum.GaoYi.ParseToInt())
                    {
                        if (compulsorytype == ClassCompulsoryTypeEnum.Select.ParseToInt())
                        {
                            listClassTemp = listClassTemp.Where(m => m.SelectSubject != null && m.SelectSubject.Contains(courseid.ToString()));
                        }
                        else
                        {
                            listClassTemp = listClassTemp.Where(m => m.SelectSubject == null || !m.SelectSubject.Contains(courseid.ToString()));
                        }
                    }
                    listTemp = listTemp.Where(m => listClassTemp.Select(m => m.Id).Contains(m.SchoolGradeClassId));
                }
                if (schoolid > 0)
                {
                    listTemp = listTemp.Where(m => m.SchoolId == schoolid);
                }
                if (gradeid > 0)
                {
                    listTemp = listTemp.Where(m => m.GradeId == gradeid);
                }
                if (schoolgradeclassid > 0)
                {
                    listTemp = listTemp.Where(m => m.SchoolGradeClassId == schoolgradeclassid);
                }
                if (teacherid > 0)
                {
                    listTemp = listTemp.Where(m => m.TeacherId == teacherid);
                }
                num = listTemp.Count();
            }
            return num;
        }
        private int OptionalShowNumed(IEnumerable<PlanInfoEntity> list, long id, long schoolid, long gradeid = 0, long schoolgradeclassid = 0, IEnumerable<SchoolGradeClassEntity> listclass = null, int schoolstage = 0, int courseid = 0, int compulsorytype = 0, long teacherid = 0)
        {
            int num = 0;
            if (list != null)
            {
                var listTemp = list.Where(n => n.Id == id && n.IsNeedDo == 2 && n.ExperimentType == 1);
                if (listclass != null && schoolstage > 0)
                {
                    var listClassTemp = listclass.Where(n => UnitBLL.GetSchoolPropList(schoolstage).Contains(n.SchoolProp));
                    if (gradeid >= GradeEnum.GaoYi.ParseToInt())
                    {
                        if (compulsorytype == ClassCompulsoryTypeEnum.Select.ParseToInt())
                        {
                            listClassTemp = listClassTemp.Where(m => m.SelectSubject != null && m.SelectSubject.Contains(courseid.ToString()));
                        }
                        else
                        {
                            listClassTemp = listClassTemp.Where(m => m.SelectSubject == null || !m.SelectSubject.Contains(courseid.ToString()));
                        }
                    }
                    listTemp = listTemp.Where(m => listClassTemp.Select(m => m.Id).Contains(m.SchoolGradeClassId));
                }
                if (schoolid > 0)
                {
                    listTemp = listTemp.Where(m => m.SchoolId == schoolid);
                }
                if (gradeid > 0)
                {
                    listTemp = listTemp.Where(m => m.GradeId == gradeid);
                }
                if (schoolgradeclassid > 0)
                {
                    listTemp = listTemp.Where(m => m.SchoolGradeClassId == schoolgradeclassid);
                }
                if (teacherid > 0)
                {
                    listTemp = listTemp.Where(m => m.TeacherId == teacherid);
                }
                num = listTemp.Count();
            }
            return num;
        }

        private int OptionalGroupNumed(IEnumerable<PlanInfoEntity> list, long id, long schoolid, long gradeid = 0, long schoolgradeclassid = 0, IEnumerable<SchoolGradeClassEntity> listclass = null, int schoolstage = 0, int courseid = 0, int compulsorytype = 0, long teacherid = 0)
        {
            int num = 0;
            if (list != null)
            {
                var listTemp = list.Where(n => n.Id == id && n.IsNeedDo == 2 && n.ExperimentType == 2);
                if (listclass != null && schoolstage > 0)
                {
                    var listClassTemp = listclass.Where(n => UnitBLL.GetSchoolPropList(schoolstage).Contains(n.SchoolProp));
                    if (gradeid >= GradeEnum.GaoYi.ParseToInt())
                    {
                        if (compulsorytype == ClassCompulsoryTypeEnum.Select.ParseToInt())
                        {
                            listClassTemp = listClassTemp.Where(m => m.SelectSubject != null && m.SelectSubject.Contains(courseid.ToString()));
                        }
                        else
                        {
                            listClassTemp = listClassTemp.Where(m => m.SelectSubject == null || !m.SelectSubject.Contains(courseid.ToString()));
                        }
                    }
                    listTemp = listTemp.Where(m => listClassTemp.Select(m => m.Id).Contains(m.SchoolGradeClassId));
                }
                if (schoolid > 0)
                {
                    listTemp = listTemp.Where(m => m.SchoolId == schoolid);
                }
                if (gradeid > 0)
                {
                    listTemp = listTemp.Where(m => m.GradeId == gradeid);
                }
                if (schoolgradeclassid > 0)
                {
                    listTemp = listTemp.Where(m => m.SchoolGradeClassId == schoolgradeclassid);
                }
                if (teacherid > 0)
                {
                    listTemp = listTemp.Where(m => m.TeacherId == teacherid);
                }
                num = listTemp.Count();
            }
            return num;
        }

        private int GetExameClassNum(IEnumerable<SchoolGradeClassEntity> list, int gradeid, long schoolid = 0, int schoolstage = 0, int courseid = 0, int compulsorytype = 0)
        {
            int num = 0;
            if (list != null)
            {
                var listTemp = list.Where(m => m.GradeId == gradeid);
                if (schoolid > 0)
                {
                    listTemp = listTemp.Where(m => m.UnitId == schoolid);
                }
                if (schoolstage > 0)
                {
                    listTemp = listTemp.Where(m => UnitBLL.GetSchoolPropList(schoolstage).Contains(m.SchoolProp));
                }
                if (gradeid >= GradeEnum.GaoYi.ParseToInt())
                {
                    if (compulsorytype == ClassCompulsoryTypeEnum.Select.ParseToInt())
                    {
                        listTemp = listTemp.Where(m => m.SelectSubject != null && m.SelectSubject.Contains(courseid.ToString()));
                    }
                    else
                    {
                        listTemp = listTemp.Where(m => m.SelectSubject == null || !m.SelectSubject.Contains(courseid.ToString()));
                    }
                }
                num = listTemp.Count();
            }
            return num;
        }

        private int GetCourseGradeNum(IEnumerable<PlanInfoEntity> list, long schoolid, IEnumerable<SchoolGradeClassEntity> listclass = null, long gradeid = 0, int courseid = 0, long schoolstage = 0, int compulsorytype = 0)
        {
            int num = 0;
            if (list != null)
            {
                var listTemp = list.Where(n => n.SchoolId == schoolid && n.GradeId == gradeid && n.CourseId == courseid);
                if (listclass != null && schoolstage > 0)
                {
                    var listClassTemp = listclass.Where(m => m.UnitId == schoolid && m.GradeId == gradeid).ToList();
                    if (gradeid >= GradeEnum.GaoYi.ParseToInt())
                    {
                        var listClassTemp1 = listClassTemp.Where(m => m.SelectSubject != null && m.SelectSubject.Contains(courseid.ToString()));
                        var num1 = listTemp.Where(m => listClassTemp1.Select(j => j.Id).Contains(m.SchoolGradeClassId)).Count();

                        var listClassTemp2 = listClassTemp.Where(m => m.SelectSubject == null || !m.SelectSubject.Contains(courseid.ToString()));
                        var num2 = listTemp.Where(m => listClassTemp2.Select(j => j.Id).Contains(m.SchoolGradeClassId)).Count();

                        num = num1 + num2;
                    }
                    else
                    {
                        num = listTemp.Where(m => listClassTemp.Select(m => m.Id).Contains(m.SchoolGradeClassId)).Count();
                    }
                }
            }
            return num;
        }

        private int GetCourseGradePlanNum(IEnumerable<PlanInfoEntity> list, long schoolid, IEnumerable<SchoolGradeClassEntity> listclass = null, long gradeid = 0, int courseid = 0, long schoolstage = 0, int compulsorytype = 0)
        {
            int num = 0;
            if (list != null)
            {
                var listTemp = list.Where(n => n.SchoolId == schoolid && n.GradeId == gradeid && n.CourseId == courseid).ToList();
                if (listclass != null && schoolstage > 0)
                {
                    var listClassTemp = listclass.Where(m => m.UnitId == schoolid && m.GradeId == gradeid).ToList();
                    if (gradeid >= GradeEnum.GaoYi.ParseToInt())
                    {
                        var gradeClassNum1 = listClassTemp.Where(m => m.SelectSubject != null && m.SelectSubject.Contains(courseid.ToString())).Count();
                        var num1 = listTemp.Where(m => m.CompulsoryType == ClassCompulsoryTypeEnum.Select.ParseToInt()).Count();
                        num += num1 * gradeClassNum1;

                        var gradeClassNum2 = listClassTemp.Where(m => m.SelectSubject == null || !m.SelectSubject.Contains(courseid.ToString())).Count();
                        var num2 = listTemp.Where(m => m.CompulsoryType == ClassCompulsoryTypeEnum.NonSelect.ParseToInt()).Count();
                        num += num2 * gradeClassNum2;

                    }
                    else
                    {
                        num = listTemp.Count * listClassTemp.Count;
                    }
                }
            }
            return num;
        }
        #endregion

        #endregion
    }

}
