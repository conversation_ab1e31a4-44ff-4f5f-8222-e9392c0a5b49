﻿using Dqy.Syjx.Business.BusinessManage;
using Dqy.Syjx.Business.InstrumentManage;
using Dqy.Syjx.Business.OrganizationManage;
using Dqy.Syjx.Business.PersonManage;
using Dqy.Syjx.Business.SystemManage;
using Dqy.Syjx.Entity.BusinessManage;
using Dqy.Syjx.Entity.ExperimentTeachManage;
using Dqy.Syjx.Entity.InstrumentManage;
using Dqy.Syjx.Entity.OrganizationManage;
using Dqy.Syjx.Entity.QueryStatisticsManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Enum.ExperimentTeachManage;
using Dqy.Syjx.Enum.InstrumentManage;
using Dqy.Syjx.Enum.OrganizationManage;
using Dqy.Syjx.Model.Param.BusinessManage;
using Dqy.Syjx.Model.Param.ExperimentTeachManage;
using Dqy.Syjx.Model.Param.InstrumentManage;
using Dqy.Syjx.Model.Param.OrganizationManage;
using Dqy.Syjx.Model.Param.QueryStatisticsManage;
using Dqy.Syjx.Model.Param.SystemManage;
using Dqy.Syjx.Model.Result.OrganizationManage;
using Dqy.Syjx.Service.BusinessManage;
using Dqy.Syjx.Service.ExperimentTeachManage;
using Dqy.Syjx.Service.InstrumentManage;
using Dqy.Syjx.Service.OrganizationManage;
using Dqy.Syjx.Service.PersonManage;
using Dqy.Syjx.Service.QueryStatisticsManage;
using Dqy.Syjx.Service.SystemManage;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Web.Code;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dqy.Syjx.Business.QueryStatisticsManage
{
    public class WorkBLL
    {
        /// <summary>
        /// ApiToken值（仅WebApi端传入）
        /// </summary>
        public string ApiToken = string.Empty;

        private PlanInfoService planInfoService = new PlanInfoService();
        private PlanDetailService planDetailService = new PlanDetailService();
        private UserSchoolStageSubjectService userSchoolStageSubjectService = new UserSchoolStageSubjectService();
        private UserClassInfoBLL userClassInfoBLL = new UserClassInfoBLL();
        private ExperimentBookingService experimentBookingService = new ExperimentBookingService();
        private PurchaseDeclarationService purchaseDeclarationService = new PurchaseDeclarationService();
        private SchoolInstrumentService schoolInstrumentService = new SchoolInstrumentService();
        private FunRoomService funRoomService = new FunRoomService();

        private UserSubjectSetService userSubjectSetService = new UserSubjectSetService();
        private UserExperimenterSetService userExperimenterSetService = new UserExperimenterSetService();
        private SchoolGradeClassService schoolGradeClassService = new SchoolGradeClassService();
        private InstrumentService instrumentService = new InstrumentService();
        private InstrumentLendService instrumentLendService = new InstrumentLendService();
        private FunRoomUseService funRoomUseService = new FunRoomUseService();
        private UnitService unitService = new UnitService();
        private StaticDictionaryService staticDictionaryService = new StaticDictionaryService();
        private PlanExamParameterService planExamParameterService = new PlanExamParameterService();
        private UserClassInfoService userClassInfoService = new UserClassInfoService();
        #region 基础数据

        /// <summary>
        /// 获取已编制计划集合
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<List<PlanInfoEntity>> GetPlanedList(PlanInfoListParam param)
        {
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            param.UnitId = operatorinfo.UnitId.Value;
            //param.UserId = operatorinfo.UserId.Value;
            param.UnitType = operatorinfo.UnitType;
            param.SchoolTerm = operatorinfo.SchoolTerm;
            param.SchoolYearStart = operatorinfo.SchoolTermStartYear;
            //param.SetUserId = operatorinfo.UserId.Value;
            param.ActivityType = ActivityTypeEnum.InClass.ParseToInt();
            /**** 基础数据统计全学科的，当前单位的。
            if (operatorinfo.RoleValues != null)
            {
                var listRole = operatorinfo.RoleValues.Split(",");
                string laboratory = RoleValueEnum.LaboratoryManager.ParseToInt().ToString();
                string teacher = RoleValueEnum.CommonPerson.ParseToInt().ToString();
                //string group = RoleValueEnum.GroupLeader.ParseToInt().ToString();
                //string business = RoleValueEnum.BusinessManager.ParseToInt().ToString();
                if (listRole.Where(m => m != laboratory && m != teacher).Count() > 0)
                {
                    //显示所有,存在其他角色。
                }
                else
                {
                    List<int> courseids = new List<int>();
                    List<int> gradeids = new List<int>();

                    //获取对应的学科，取交集。
                    if (listRole.Where(m => m == laboratory).Count() > 0)
                    {
                        //获取实验员对应的学科
                        var paramSubject = new UserSchoolStageSubjectListParam();
                        paramSubject.UserId = operatorinfo.UserId ?? 0;
                        paramSubject.UnitId = operatorinfo.UnitId ?? 0;
                        paramSubject.UnitType = operatorinfo.UnitType;
                        paramSubject.IsCurrentUnit = IsEnum.Yes.ParseToInt();
                        var listSubject = await userSchoolStageSubjectService.GetList(paramSubject);

                        if (listSubject != null && listSubject.Count > 0)
                        {
                            courseids.AddRange(listSubject.Where(m => m.SubjectIdz > 0).Select(m => m.SubjectIdz).ToList());
                            if (listSubject.Select(m => m.SchoolStageIdz).Distinct().Count() == 1)
                            {
                                if (listSubject[0].SchoolStageIdz > 0)
                                {
                                    param.SchoolStageId = listSubject[0].SchoolStageIdz;
                                }
                            }
                        }
                    }
                    if (listRole.Where(m => m == teacher).Count() > 0)
                    {
                        ExperimentBookingListParam paramBooking = new ExperimentBookingListParam();
                        await userClassInfoBLL.GetTeachCourseGrades(paramBooking, operatorinfo);
                        if (paramBooking != null)
                        {
                            if (paramBooking.GradeIds != null)
                            {
                                gradeids.AddRange(paramBooking.GradeIds);
                            }
                            if (paramBooking.CourseIds != null)
                            {
                                courseids.AddRange(paramBooking.CourseIds);
                            }
                        }
                    }
                    param.GradeIds = gradeids;
                    param.CourseIds = courseids;
                }
            }
            */
            var list = await planInfoService.GetPageList(param, new Pagination() { PageSize = int.MaxValue });
            return list;
        }

        /// <summary>
        /// 获取登记实验集合
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<List<ExperimentBookingEntity>> GetRecordList(ExperimentBookingListParam param)
        {
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            param.SchoolYearStart = operatorinfo.SchoolTermStartYear;
            param.SchoolTerm = operatorinfo.SchoolTerm;
            if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                param.SchoolId = operatorinfo.UnitId;
                //param.BookUserId = operatorinfo.UserId;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                param.CountyId = operatorinfo.UnitId;
            }
            //param.RecordMode = ExperimentRecordModeEnum.BookIng.ParseToInt();
            param.ThanStatuz = ExperimentBookStatuzEnum.WaitRecord.ParseToInt();
            param.IsMain = 0;
            param.ActivityType = ActivityTypeEnum.InClass.ParseToInt();
            var list = await experimentBookingService.GetStaticPageList(param, null);
            return list;
        }

        /// <summary>
        /// 获取仪器数据
        /// </summary>
        /// <param name="param">参数（前段传递参数Statuz:30已入库）</param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<decimal> GetInstrumentList(InstrumentParam param)
        {
            OperatorInfo user = await Operator.Instance.Current(ApiToken);
            param.IsUse = 1;//仪器
            //param.Statuz = 30;//已入库
          
            if (user.UnitType == UnitTypeEnum.City.ParseToInt())
            {
                param.CityId = user.UnitId;
            }
            else if (user.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                param.CountyId = user.UnitId;
            }
            else
            {
                param.UnitId = user.UnitId;
            }
            //param.SetUserId = user.UserId.Value;
            var pagination = new Pagination();
            pagination.TotalNum = -9999; //计算汇总统计
            await instrumentService.GetInstrumentList(param, pagination);
            //只要总计数量
            return pagination.TotalNum; 
        }

        /// <summary>
        /// 实验（专用）室统计页面-学校端
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<FunRoomEntity> GetFunroomNum(FunRoomListParam param)
        {
            TData<List<FunRoomEntity>> obj = new TData<List<FunRoomEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            param.UserId = operatorinfo.UserId.Value;
            param.UnitType = operatorinfo.UnitType;
            if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                param.CountyId = operatorinfo.UnitId.Value;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                param.UnitId = operatorinfo.UnitId.Value;
            }
            var entity = await funRoomService.GetTotalSum(param, "", "");
            return entity;
        }

        /// <summary>
        /// 获取实验员集合
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<List<UserExperimenterSetEntity>> GetExperimenterUserList(UserExperimenterSetListParam param)
        {
            OperatorInfo user = await Operator.Instance.Current(ApiToken);
            param.IsExperimenter = IsEnum.Yes.ParseToInt();
            param.UnitId = user.UnitId;
            param.UnitType = user.UnitType;
            param.IsCurrentUnit = IsEnum.Yes.ParseToInt();
            /****
             * 基础信息统计的都是单位的
            if (user.UnitType == UnitTypeEnum.County.ParseToInt() || user.UnitType == UnitTypeEnum.City.ParseToInt())
            {
                //获取当前人是否设置了学段学科。
                var stagelist = await userSubjectSetService.GetUserSetStageList(user.UserId ?? 0);
                if (stagelist != null && stagelist.Count > 0)
                {
                    stagelist = stagelist.Where(m => m.UserSubjectSetId > 0).ToList();
                    if (param.SchoolPropList == null)
                    {
                        param.SchoolPropList = new List<int>();
                    }
                    foreach (var item in stagelist)
                    {
                        param.SchoolPropList.AddRange(UnitBLL.GetSchoolPropList(item.DictionaryId ?? 0));
                    }
                }
                if (!(param.SubjectId > 0))
                {
                    //获取当前人是否设置了学段学科。
                    var courselist = await userSubjectSetService.GetUserSetCourseList(user.UserId ?? 0, 1);
                    if (courselist != null && courselist.Count > 0)
                    {
                        param.SubjectList = courselist.Where(m => m.UserSubjectSetId > 0).Select(m => m.DictionaryId ?? 0).ToList();
                    }
                }
            }
            */
            var list = await userExperimenterSetService.GetPageList(param, null);
            return list;
        }


        /// <summary>
        /// 获取已预约实验数量
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<List<ExperimentBookingEntity>> GetExperimentBookingBookingList(ExperimentBookingListParam param)
        {
            List<ExperimentBookingEntity> list = new List<ExperimentBookingEntity>();
            OperatorInfo user = await Operator.Instance.Current(ApiToken);

            param.SchoolYearStart = user.SchoolTermStartYear;
            param.SchoolTerm = user.SchoolTerm;
            if (user.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                //学校
                //param.BookUserId = user.UserId;//基础数据统计的都是单位的。
                param.IsMain = 0;
                param.ListType = 10;//查询已预约数据，查出的结果需要排除安排不通过退回的数据
                param.ActivityType = ActivityTypeEnum.InClass.ParseToInt();
                param.SchoolId = user.UnitId;
                list = await experimentBookingService.GetAllPageList(param, null);
            }
            else if (user.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                param.IsMain = 0;
                param.ListType = 10;//查询已预约数据，查出的结果需要排除安排不通过退回的数据
                param.ActivityType = ActivityTypeEnum.InClass.ParseToInt();
                param.CountyId = user.UnitId;

                /***
                 * 基础数据统计的都是单位的数据
                //获取当前人是否设置了学段学科。
                var stagelist = await userSubjectSetService.GetUserSetStageList(user.UserId ?? 0);
                if (stagelist != null && stagelist.Count > 0)
                {
                    param.SchoolStageList = stagelist.Where(m => m.UserSubjectSetId > 0).Select(m => m.DictionaryId ?? 0).ToList();
                }

                //获取当前人是否设置了学段学科。
                var courselist = await userSubjectSetService.GetUserSetCourseList(user.UserId ?? 0, 1);
                if (courselist != null && courselist.Count > 0)
                {
                    param.CourseIds = courselist.Where(m => m.UserSubjectSetId > 0).Select(m => m.DictionaryId ?? 0).ToList();
                }
                */
                list = await experimentBookingService.GetAllPageList(param, null);
            } 
            return list;
        }


        #endregion

        #region 待办事项

        /// <summary>
        /// 待安排实验集合
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<List<ExperimentBookingEntity>> GetArrangeList(ExperimentBookingListParam param)
        {
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);

            if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                param.SafeguardUserId = operatorinfo.UserId;
                param.SchoolId = operatorinfo.UnitId;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                param.CountyId = operatorinfo.UnitId;

            }
            else if (operatorinfo.UnitType == UnitTypeEnum.City.ParseToInt())
            {
                param.CityId = operatorinfo.UnitId;
            }
            param.RecordMode = ExperimentRecordModeEnum.BookIng.ParseToInt();
            param.Statuz = ExperimentBookStatuzEnum.WaitArrange.ParseToInt();
            param.IsMain = 1;
            param.ActivityType = ActivityTypeEnum.InClass.ParseToInt();
            param.SchoolYearStart = operatorinfo.SchoolTermStartYear;
            param.SchoolTerm = operatorinfo.SchoolTerm;
            var list = await experimentBookingService.GetArrangeList(param, new Pagination() { PageSize = int.MaxValue });
            return list;
        }

        // <summary>
        /// 实验待登记集合（根据实验的实验员查询）
        /// </summary>
        /// <param name="param">查询参数</param>
        /// <returns></returns>
        public async Task<List<ExperimentBookingEntity>> GetWaitRecordList(ExperimentBookingListParam param)
        {
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            param.SchoolYearStart = operatorinfo.SchoolTermStartYear;
            param.SchoolTerm = operatorinfo.SchoolTerm;
            param.RecordMode = ExperimentRecordModeEnum.BookIng.ParseToInt();
            param.Statuz = ExperimentBookStatuzEnum.WaitRecord.ParseToInt();
            param.IsMain = 0;
            param.ActivityType = ActivityTypeEnum.InClass.ParseToInt();
            if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                param.BookUserId = operatorinfo.UserId;
                param.SchoolId = operatorinfo.UnitId;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                param.CountyId = operatorinfo.UnitId;
            }

            var list = await experimentBookingService.GetStaticPageList(param, null);
            return list;
        }

        public async Task<int> GetWaitApproveInstrumentList(PurchaseDeclarationListParam param)
        {
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);

            param.IsAssociation = 1;
            param.Statuz = InstrumentAuditStatuzEnum.WaitSchoolAudit.ParseToInt();
            param.PurchaseYear = DateTime.Now.Year;
            if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                //判断角色 【教务主任】
                string laboratory = RoleValueEnum.BusinessManager.ParseToInt().ToString();
                var listRole = operatorinfo.RoleValues.Split(",");
                if (listRole == null || !listRole.Contains(laboratory))
                {
                    return 0;
                }
                param.SchoolId = operatorinfo.UnitId;            
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                param.CountyId = operatorinfo.UnitId;
            }
            var pagion = new Pagination() { };
            pagion.PageIndex = 1;
            pagion.PageSize = 1;
            var list = await purchaseDeclarationService.GetPageList(param, pagion);
            if (pagion.TotalCount>0)
            {
                return pagion.TotalCount;
            }
            return 0;
        }

        public async Task<List<InstrumentLendEntity>> GetLendInstrumentList(InstrumentLendListParam param)
        {
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            param.InstrumentAttribute = 1;
            if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                param.SafeguardUserId = operatorinfo.UserId;
                param.SchoolId = operatorinfo.UnitId;
                //param.LendUserId = operatorinfo.UserId;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                param.CountyId = operatorinfo.UnitId;
            }
            return await instrumentLendService.GetPageList(param, null);
        }
        /// <summary>
        /// 待预约实验
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<int> GetBookingWeekList(PlanDetailListParam param)
        {
            int waitBookingNum = 0;
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            var paramPlan = new PlanDetailListParam();
            paramPlan.SchoolYearStart = operatorinfo.SchoolTermStartYear;
            paramPlan.SchoolTerm = operatorinfo.SchoolTerm;

            var weekNum = 0;
            if (operatorinfo.FirstWeekDate != null)
            {
                weekNum = GetWeekNumberOfYear(DateTime.Now, (DateTime)operatorinfo.FirstWeekDate);
            }
            if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                paramPlan.SchoolId = operatorinfo.UnitId.Value;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                paramPlan.CountyId = operatorinfo.UnitId.Value;
            }
            paramPlan.WeekNum = weekNum + 1;
            var list = await planDetailService.GetList(paramPlan, null);
            
            if (list!=null && list.Count > 0)
            {
                List<SchoolGradeClassEntity> teachClassList = new List<SchoolGradeClassEntity>();
                List<int> teachCourseList = new List<int>();

                //计算待预约数量
                if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt() && operatorinfo.RoleValues != null)
                {
                    var listRole = operatorinfo.RoleValues.Split(",");
                    string laboratory = RoleValueEnum.LaboratoryManager.ParseToInt().ToString();
                    string teacher = RoleValueEnum.CommonPerson.ParseToInt().ToString();
                    if (listRole != null && listRole.Where(m => m == teacher).Count() > 0)
                    {
                        //获取当前用户，当前学科任课班级
                        var classInfo = await userClassInfoService.GetTeachInfoList(operatorinfo.UnitId.Value, 0, operatorinfo.UserId.Value);
                        if (classInfo != null && classInfo.Count > 0)
                        {
                            classInfo = classInfo.Where(m => m.CourseId >= 1005002 && m.CourseId <= 1005005).ToList();
                            if (classInfo != null && classInfo.Count > 0)
                            {
                                var gradeidz = string.Join(",", classInfo.Where(m =>list.Select(j=>j.CourseId).Contains(m.CourseId) && m.ClassIdz != null && m.ClassIdz.Length > 0).Select(m => m.ClassIdz));
                                if (gradeidz != null && gradeidz.Count() > 0)
                                {
                                    var listClass = await schoolGradeClassService.GetList(new SchoolGradeClassListParam() { UnitId = operatorinfo.UnitId ,Statuz =StatusEnum.Yes.ParseToInt()});
                                    if (listClass != null && listClass.Count > 0)
                                    {
                                        teachClassList = listClass.Where(m => gradeidz.Contains(m.Id.ToString())).ToList();
                                    }
                                }
                                teachCourseList = classInfo.Where(n => n.ClassIdz != null && n.ClassIdz.Length > 0).Select(m => m.CourseId).Distinct().ToList();
                            }
                        }

                    }
                    else
                    {
                        return waitBookingNum;
                    }
                }
                if (teachClassList.Count > 0 || teachCourseList.Count> 0)
                {
                    var paramBooking = new ExperimentBookingListParam();
                    paramBooking.SchoolId = list[0].SchoolId;
                    paramBooking.ExperimentIdList = list.Select(m => m.ExperimentId).ToList();
                    paramBooking.SchoolGradeClassIds = teachClassList.Select(m => m.Id.ToString()).ToList();
                    paramBooking.OptType = 22;
                    var listBooking = await experimentBookingService.GetList(paramBooking);

                    //小于高中的有多少班级
                    var gradeNoGao = teachClassList.Where(m => m.GradeId < GradeEnum.GaoYi.ParseToInt());
                    if (gradeNoGao!=null && gradeNoGao.Count()>0)
                    {
                        foreach (var item in list)
                        {
                            int planNum = 0;
                            //当前年级有几个班级。
                            var classTemp = teachClassList.Where(m => m.GradeId == item.GradeId);
                            if (item.GradeId >= GradeEnum.GaoYi.ParseToInt())
                            {
                                if (item.ClassIdz!=null)
                                {
                                    classTemp = classTemp.Where(m => item.ClassIdz.Contains(m.Id.ToString()));
                                    if (classTemp != null && classTemp.Count() > 0)
                                    {
                                        planNum += classTemp.Count();
                                    }
                                }
                            }
                            else
                            {
                                if (classTemp != null && classTemp.Count() > 0)
                                {
                                    planNum += classTemp.Count();
                                }
                            }
                            //当前实验预约了几个班级了。
                            if (listBooking!=null && listBooking.Count >0)
                            {
                                var bookingTemp = listBooking.Where(m => m.ExperimentId == item.ExperimentId);
                                if (classTemp != null && classTemp.Count() > 0 && bookingTemp != null && bookingTemp.Count() > 0)
                                {
                                    bookingTemp = bookingTemp.Where(m => classTemp.Select(n => n.Id).Contains(m.SchoolGradeClassId));
                                    if (bookingTemp!=null && bookingTemp.Count()>0)
                                    {
                                        planNum = planNum- bookingTemp.Count();
                                    }
                                }
                            }
                            if (planNum > 0)
                            {
                                waitBookingNum += planNum;
                            }
                       
                        }
                    }
                }
            }
            return waitBookingNum;
        }

        /// <summary>
        /// 获取待编制计划的实验数量
        /// </summary>
        /// <remarks>
        /// 1:备课组长角色权限。
        /// 2：授权学科，年级。
        /// 3：需编制实验数量，已编制实验数量，需编制实验数量（使用left join关联处理）。
        /// </remarks>
        /// <returns></returns>
        public async Task<int> GetWaitPlanExperimentNum()
        {
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            int num = 0;
            var paramPlan = new PlanExamListParameterListParam();
            paramPlan.SchoolYearStart = operatorinfo.SchoolTermStartYear;
            paramPlan.SchoolTerm = operatorinfo.SchoolTerm;
            if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                //是否有这个角色
                if (!(operatorinfo.RoleValues != null && operatorinfo.RoleValues.Contains(RoleValueEnum.GroupLeader.ParseToInt().ToString())))
                {
                    return num;//没有需要编制的计划
                }
                //查询授权学科、年级
                var userCourseGradeList = await userClassInfoService.GetTeachInfoList(operatorinfo.UnitId ?? 0, 0, operatorinfo.UserId ?? 0);
                if (userCourseGradeList == null || userCourseGradeList.Count == 0)
                {
                    return num;//没有设置授权的学科年级，所以待编制计划为0
                }
                userCourseGradeList = userCourseGradeList.Where(m => m.CourseId > 0 && m.GradeIdz != null && m.GradeIdz.Length > 0).ToList();
                if (userCourseGradeList.Count == 0)
                {
                    return num;//没有设置授权的学科年级，所以待编制计划为0
                }
                paramPlan.SchoolId = operatorinfo.UnitId;
                paramPlan.CourseIdList = userCourseGradeList.Select(m => m.CourseId).ToList();
                var list = await planExamParameterService.GetWaitPlanExam(paramPlan);
                if (list != null)
                {
                    for (var i = 0; i < userCourseGradeList.Count; i++)
                    {
                        var item = userCourseGradeList[i];
                        num += list.Where(m => m.CourseId == item.CourseId && item.GradeIdz.Contains(m.GradeId.ToString())).Count();
                    }
                }
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                //区县id
                paramPlan.UnitId = operatorinfo.UnitId;
                var list = await planExamParameterService.GetWaitPlanExam(paramPlan);
                if (list != null)
                {
                    num = list.Count;
                }
            }
            return num;
        }


        private int GetWeekNumberOfYear(DateTime currentDate, DateTime firstWeekDate)
        {
            // 1. 正确计算起始周的周一
            int daysToSubtract = (firstWeekDate.DayOfWeek - DayOfWeek.Monday + 7) % 7;
            DateTime actualFirstWeekMonday = firstWeekDate.AddDays(-daysToSubtract).Date;

            // 2. 对齐日期到午夜（避免时间部分影响）
            DateTime current = currentDate.Date;
            DateTime firstMonday = actualFirstWeekMonday.Date;

            // 3. 处理当前日期在起始周之前的情况
            if (current < firstMonday) return 0; // 或根据需求返回负数

            // 4. 正确计算周数（整数除法）
            int totalDays = (int)(current - firstMonday).TotalDays;
            int weekNumber = totalDays / 7 + 1; // 整数除法

            return weekNumber;

        }


        #endregion

        #region 超期预警、平台使用情况明细表、平台单位情况表

        /// <summary>
        /// 平台使用情况明细表
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<List<UnitStatisticModel>> GetUnitSchoolStageDetailList(UnitListParam param)
        {
            List<UnitStatisticModel> list = new List<UnitStatisticModel>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            param.Pid = operatorinfo.UnitId ?? 0;
            param.Statuz = 1;
            var listSchool = await unitService.GetSchoolSchoolStageList(param, null);
            if (listSchool != null && listSchool.Count > 0)
            {
                //统计数据。
                SchoolInstrumentListParam paramInstrument = new SchoolInstrumentListParam();
                paramInstrument.Statuz = InstrumentInputStatuzEnum.AlreadyInputStorage.ParseToInt();
                paramInstrument.InstrumentAttribute = 1;
                if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    paramInstrument.UnitId = operatorinfo.UnitId;
                }
                else if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    paramInstrument.CountyId = operatorinfo.UnitId;
                }
                var listInstrument = await schoolInstrumentService.GetPageList(paramInstrument, null);

                //实验室数量
                FunRoomListParam paramFunroom = new FunRoomListParam();
                paramFunroom.UserId = operatorinfo.UserId.Value;
                paramFunroom.UnitType = operatorinfo.UnitType;
                if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    paramFunroom.CountyId = operatorinfo.UnitId.Value;
                }
                else if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    paramFunroom.UnitId = operatorinfo.UnitId.Value;
                }
                paramFunroom.Nature = FunNatureTypeEnum.Room.ParseToInt();
                var listFunroom = await funRoomService.GetStatistics(paramFunroom, null);

                //使用登记记录
                FunRoomUseListParam paramFunrromUse = new FunRoomUseListParam();
                paramFunrromUse.UnitType = operatorinfo.UnitType;
                paramFunrromUse.SchoolId = operatorinfo.UnitId.Value;
                if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    paramFunrromUse.CountyId = operatorinfo.UnitId.Value;
                }
                else if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    paramFunrromUse.SchoolId = operatorinfo.UnitId.Value;
                }
                paramFunrromUse.NatureType = FunNatureTypeEnum.ZhuanRoom.ParseToInt();
                paramFunrromUse.CurrentSchoolTerm = operatorinfo.SchoolTerm;
                paramFunrromUse.CurrentSchoolTermStartYear = operatorinfo.SchoolTermStartYear;
                paramFunrromUse.CurrentSchoolTermStartDate = operatorinfo.SchoolTermStartDate.Value;
                paramFunrromUse.CurrentSchoolTermEndDate = operatorinfo.SchoolTermEndDate.Value;
                var listFunroomUse = await funRoomUseService.GetPageList(paramFunrromUse, null);

                //获取实验员信息
                var paramUserExt = new UserExtensionListParam();
                paramUserExt.UnitId = operatorinfo.UnitId ?? 0;
                paramUserExt.UnitType = operatorinfo.UnitType;
                paramUserExt.IsExperimenter = IsEnum.Yes.ParseToInt();
                paramUserExt.IsCurrentUnit = IsEnum.Yes.ParseToInt();
                var listUserExp = await userExperimenterSetService.GetSchoolStageCourseList(paramUserExt);

                //编制计划
                var paramPlan = new PlanInfoListParam();
                //paramPlan.UnitId = operatorinfo.UnitId.Value;
                //paramPlan.UnitType = operatorinfo.UnitType;
                //paramPlan.CurrentSchoolTerm = operatorinfo.SchoolTerm;
                //paramPlan.CurrentSchoolTermStartYear = operatorinfo.SchoolTermStartYear;
                //paramPlan.ActivityType = ActivityTypeEnum.InClass.ParseToInt();
                //var listPlan = await planInfoService.GetPageList(paramPlan, new Pagination() { PageSize = int.MaxValue });

                var paramGradeClass = new SchoolGradeClassListParam();
                paramPlan.UnitType = operatorinfo.UnitType;
                paramGradeClass.UnitType = operatorinfo.UnitType;
                if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    //param.SchoolStageList = operatorinfo.SchoolStageList;
                    paramPlan.UnitId = operatorinfo.UnitId ?? 0;
                    paramPlan.SchoolId = operatorinfo.UnitId ?? 0;
                    paramGradeClass.UnitId = operatorinfo.UnitId ?? 0;
                }
                else if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    paramPlan.UnitId = operatorinfo.UnitId ?? 0;

                    paramGradeClass.CountyId = operatorinfo.UnitId ?? 0;
                    paramGradeClass.UnitId = paramPlan.SchoolId;
                }

                paramPlan.ActivityType = ActivityTypeEnum.InClass.ParseToInt();
                var listPlan = await planInfoService.GetPageList(paramPlan, null);
                if (listPlan != null && listPlan.Count > 0)
                {
                    var pagePlanDetail = new Pagination();
                    pagePlanDetail.PageSize = int.MaxValue;
                    var listRecordDetail = await planInfoService.PlanExpPageList(paramPlan, pagePlanDetail);
                    IEnumerable<SchoolGradeClassEntity> listGradeClass = null;
                    listGradeClass = await schoolGradeClassService.GetStatistics(paramGradeClass);
                    listPlan.ForEach(m =>
                    {
                        m.NeedShowNum = m.NeedShowNum * GetExameClassNum(listGradeClass, m.GradeId, m.SchoolId, 0, m.CourseId, m.CompulsoryType ?? 0);
                        m.NeedGroupNum = m.NeedGroupNum * GetExameClassNum(listGradeClass, m.GradeId, m.SchoolId, 0, m.CourseId, m.CompulsoryType ?? 0);
                        m.OptionalShowNum = m.OptionalShowNum * GetExameClassNum(listGradeClass, m.GradeId, m.SchoolId, 0, m.CourseId, m.CompulsoryType ?? 0);
                        m.OptionalGroupNum = m.OptionalGroupNum * GetExameClassNum(listGradeClass, m.GradeId, m.SchoolId, 0, m.CourseId, m.CompulsoryType ?? 0);
                        m.NeedShowNumed = NeedShowNumed(listRecordDetail, m.Id ?? 0, m.SchoolId, m.GradeId, 0, listGradeClass, m.SchoolStageId, m.CourseId, m.CompulsoryType ?? 0);
                        m.NeedGroupNumed = NeedGroupNumed(listRecordDetail, m.Id ?? 0, m.SchoolId, m.GradeId, 0, listGradeClass, m.SchoolStageId, m.CourseId, m.CompulsoryType ?? 0);
                        m.OptionalShowNumed = OptionalShowNumed(listRecordDetail, m.Id ?? 0, m.SchoolId, m.GradeId, 0, listGradeClass, m.SchoolStageId, m.CourseId, m.CompulsoryType ?? 0);
                        m.OptionalGroupNumed = OptionalGroupNumed(listRecordDetail, m.Id ?? 0, m.SchoolId, m.GradeId, 0, listGradeClass, m.SchoolStageId, m.CourseId, m.CompulsoryType ?? 0);
                    });

                }
                //单位学段，转换输出实体
                list = listSchool.Select(n => new UnitStatisticModel()
                {
                    Id = n.Id ?? 0,
                    Name = n.Name,
                    SchoolStageName = n.SchoolStageName,
                    SchoolStageId = n.SchoolStageId,
                    Sort = n.Sort,
                    InstrumentNum = listInstrument.Where(m => m.SchoolId == n.Id && m.StageId == (n.SchoolStageId ?? 0).ToString()).Sum(n => n.StockNum),
                    InstrumentNewNum = listInstrument.Where(m => m.SchoolId == n.Id && m.StageId == (n.SchoolStageId ?? 0).ToString() && m.PurchaseDate != null && ((DateTime)m.PurchaseDate).Year == DateTime.Now.Year).Sum(n => n.StockNum),
                    FunroomNum = listFunroom.Where(m => m.UnitId == n.Id && m.SchoolStagez.Contains((n.SchoolStageId ?? 0).ToString())).Count(),
                    FunroomUseNum = listFunroomUse.Where(m => m.UnitId == n.Id && m.SchoolStage == (n.SchoolStageId ?? 0)).Count(),
                    ExperimenterUserNum = listUserExp.Where(m => m.UnitId == n.Id && m.SchoolStageIdz != null && m.SchoolStageIdz.Contains((n.SchoolStageId ?? 0).ToString())).Count(),
                    ExperimenterUserMajorNum = listUserExp.Where(m => m.UnitId == n.Id && m.ExperimenterNature == ExperimenterNatureEnum.FullTime.ParseToInt() && m.SchoolStageIdz != null && m.SchoolStageIdz.Contains((n.SchoolStageId ?? 0).ToString())).Count(),
                    ExperimentNum = listPlan.Where(m => m.SchoolId == n.Id && m.SchoolStageId == (n.SchoolStageId ?? 0)).Sum(j => j.TotalNum),
                    PlanExperimentNum = listPlan.Where(m => m.SchoolId == n.Id && m.SchoolStageId == (n.SchoolStageId ?? 0)).Sum(j => j.Num),
                    RecordExperimentNum = listPlan.Where(m => m.SchoolId == n.Id && m.SchoolStageId == (n.SchoolStageId ?? 0)).Sum(j => j.TotalNumed)
                    ,
                    ExperimentRate = listPlan.Where(m => m.SchoolId == n.Id && m.SchoolStageId == (n.SchoolStageId ?? 0)).Count() > 0 ? Math.Round(listPlan.Where(m => m.SchoolId == n.Id && m.SchoolStageId == (n.SchoolStageId ?? 0)).Sum(j => j.TotalRatio) / listPlan.Where(m => m.SchoolId == n.Id && m.SchoolStageId == (n.SchoolStageId ?? 0)).Count(), 2) : 0

                }).ToList();

            }
            return list;
        }

        /// <summary>
        /// 平台使用情况表
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<List<UnitStatisticModel>> GetUnitSchoolStageList(UnitListParam param)
        {
            List<UnitStatisticModel> list = new List<UnitStatisticModel>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            param.Pid = operatorinfo.UnitId ?? 0;
            param.Statuz = 1;
            var listSchool = await unitService.GetSchoolSchoolStageList(param, null);
            if (listSchool != null && listSchool.Count > 0)
            {
                //统计数据。
                SchoolInstrumentListParam paramInstrument = new SchoolInstrumentListParam();

                if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    paramInstrument.UnitId = operatorinfo.UnitId;
                }
                else if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    paramInstrument.CountyId = operatorinfo.UnitId;
                }
                var listInstrument = await schoolInstrumentService.GetPageList(paramInstrument, null);

                //实验室数量
                FunRoomListParam paramFunroom = new FunRoomListParam();
                //paramFunroom.UserId = operatorinfo.UserId.Value;
                paramFunroom.UnitType = operatorinfo.UnitType;
                if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    paramFunroom.CountyId = operatorinfo.UnitId.Value;
                }
                else if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    paramFunroom.UnitId = operatorinfo.UnitId.Value;
                }
                paramFunroom.Nature = FunNatureTypeEnum.Room.ParseToInt();
                var listFunroom = await funRoomService.GetStatistics(paramFunroom, null);

                //使用登记记录
                FunRoomUseListParam paramFunrromUse = new FunRoomUseListParam();
                paramFunrromUse.UnitType = operatorinfo.UnitType;
                paramFunrromUse.SchoolId = operatorinfo.UnitId.Value;
                if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    paramFunrromUse.CountyId = operatorinfo.UnitId.Value;
                }
                else if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    paramFunrromUse.SchoolId = operatorinfo.UnitId.Value;
                }
                paramFunrromUse.NatureType = FunNatureTypeEnum.ZhuanRoom.ParseToInt();
                paramFunrromUse.CurrentSchoolTerm = operatorinfo.SchoolTerm;
                paramFunrromUse.CurrentSchoolTermStartYear = operatorinfo.SchoolTermStartYear;
                paramFunrromUse.CurrentSchoolTermStartDate = operatorinfo.SchoolTermStartDate.Value;
                paramFunrromUse.CurrentSchoolTermEndDate = operatorinfo.SchoolTermEndDate.Value;
                var listFunroomUse = await funRoomUseService.GetPageList(paramFunrromUse, null);

                //获取实验员信息
                var paramUserExt = new UserExtensionListParam();
                paramUserExt.UnitId = operatorinfo.UnitId ?? 0;
                paramUserExt.UnitType = operatorinfo.UnitType;
                paramUserExt.IsExperimenter = IsEnum.Yes.ParseToInt();
                paramUserExt.IsCurrentUnit = IsEnum.Yes.ParseToInt();
                var listUserExp = await userExperimenterSetService.GetSchoolStageCourseList(paramUserExt);

                //编制计划
                var paramPlan = new PlanInfoListParam();
                //paramPlan.UnitId = operatorinfo.UnitId.Value;
                //paramPlan.UnitType = operatorinfo.UnitType;
                //paramPlan.CurrentSchoolTerm = operatorinfo.SchoolTerm;
                //paramPlan.CurrentSchoolTermStartYear = operatorinfo.SchoolTermStartYear;
                //paramPlan.ActivityType = ActivityTypeEnum.InClass.ParseToInt();
                //var listPlan = await planInfoService.GetPageList(paramPlan, new Pagination() { PageSize = int.MaxValue });

                var paramGradeClass = new SchoolGradeClassListParam();
                paramPlan.UnitType = operatorinfo.UnitType;
                paramGradeClass.UnitType = operatorinfo.UnitType;
                if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    //param.SchoolStageList = operatorinfo.SchoolStageList;
                    paramPlan.UnitId = operatorinfo.UnitId ?? 0;
                    paramPlan.SchoolId = operatorinfo.UnitId ?? 0;
                    paramGradeClass.UnitId = operatorinfo.UnitId ?? 0;
                }
                else if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    paramPlan.UnitId = operatorinfo.UnitId ?? 0;

                    paramGradeClass.CountyId = operatorinfo.UnitId ?? 0;
                    paramGradeClass.UnitId = paramPlan.SchoolId;
                }

                paramPlan.ActivityType = ActivityTypeEnum.InClass.ParseToInt();
                var listPlan = await planInfoService.GetPageList(paramPlan, null);
                if (listPlan != null && listPlan.Count > 0)
                {
                    var pagePlanDetail = new Pagination();
                    pagePlanDetail.PageSize = int.MaxValue;
                    var listRecordDetail = await planInfoService.PlanExpPageList(paramPlan, pagePlanDetail);
                    IEnumerable<SchoolGradeClassEntity> listGradeClass = null;
                    listGradeClass = await schoolGradeClassService.GetStatistics(paramGradeClass);
                    listPlan.ForEach(m =>
                    {
                        m.NeedShowNum = m.NeedShowNum * GetExameClassNum(listGradeClass, m.GradeId, m.SchoolId, 0, m.CourseId, m.CompulsoryType ?? 0);
                        m.NeedGroupNum = m.NeedGroupNum * GetExameClassNum(listGradeClass, m.GradeId, m.SchoolId, 0, m.CourseId, m.CompulsoryType ?? 0);
                        m.OptionalShowNum = m.OptionalShowNum * GetExameClassNum(listGradeClass, m.GradeId, m.SchoolId, 0, m.CourseId, m.CompulsoryType ?? 0);
                        m.OptionalGroupNum = m.OptionalGroupNum * GetExameClassNum(listGradeClass, m.GradeId, m.SchoolId, 0, m.CourseId, m.CompulsoryType ?? 0);
                        m.NeedShowNumed = NeedShowNumed(listRecordDetail, m.Id ?? 0, m.SchoolId, m.GradeId, 0, listGradeClass, m.SchoolStageId, m.CourseId, m.CompulsoryType ?? 0);
                        m.NeedGroupNumed = NeedGroupNumed(listRecordDetail, m.Id ?? 0, m.SchoolId, m.GradeId, 0, listGradeClass, m.SchoolStageId, m.CourseId, m.CompulsoryType ?? 0);
                        m.OptionalShowNumed = OptionalShowNumed(listRecordDetail, m.Id ?? 0, m.SchoolId, m.GradeId, 0, listGradeClass, m.SchoolStageId, m.CourseId, m.CompulsoryType ?? 0);
                        m.OptionalGroupNumed = OptionalGroupNumed(listRecordDetail, m.Id ?? 0, m.SchoolId, m.GradeId, 0, listGradeClass, m.SchoolStageId, m.CourseId, m.CompulsoryType ?? 0);
                    });

                }

                //单位学段，转换输出实体
                list = listSchool.Select(n => new UnitStatisticModel()
                {
                    Id = n.Id ?? 0,
                    Name = n.Name,
                    SchoolStageName = n.SchoolStageName,
                    SchoolStageId = n.SchoolStageId,
                    Sort = n.Sort,
                    InstrumentNum = listInstrument.Where(m => m.SchoolId == n.Id && m.StageId == (n.SchoolStageId ?? 0).ToString()).Sum(n => n.StockNum),
                    InstrumentNewNum = listInstrument.Where(m => m.SchoolId == n.Id && m.StageId == (n.SchoolStageId ?? 0).ToString() && m.PurchaseDate != null && ((DateTime)m.PurchaseDate).Year == DateTime.Now.Year).Sum(n => n.StockNum),
                    FunroomNum = listFunroom.Where(m => m.UnitId == n.Id && m.SchoolStagez.Contains((n.SchoolStageId ?? 0).ToString())).Count(),
                    FunroomUseNum = listFunroomUse.Where(m => m.UnitId == n.Id && m.SchoolStage == (n.SchoolStageId ?? 0)).Count(),
                    ExperimenterUserNum = listUserExp.Where(m => m.UnitId == n.Id && m.SchoolStageIdz != null && m.SchoolStageIdz.Contains((n.SchoolStageId ?? 0).ToString())).Count(),
                    ExperimenterUserMajorNum = listUserExp.Where(m => m.UnitId == n.Id && m.ExperimenterNature == ExperimenterNatureEnum.FullTime.ParseToInt() && m.SchoolStageIdz != null && m.SchoolStageIdz.Contains((n.SchoolStageId ?? 0).ToString())).Count(),
                    ExperimentNum = listPlan.Where(m => m.SchoolId == n.Id && m.SchoolStageId == (n.SchoolStageId ?? 0)).Sum(j => j.TotalNum),
                    PlanExperimentNum = listPlan.Where(m => m.SchoolId == n.Id && m.SchoolStageId == (n.SchoolStageId ?? 0)).Sum(j => j.Num),
                    RecordExperimentNum = listPlan.Where(m => m.SchoolId == n.Id && m.SchoolStageId == (n.SchoolStageId ?? 0)).Sum(j => j.TotalNumed),
                    ExperimentRate = listPlan.Where(m => m.SchoolId == n.Id && m.SchoolStageId == (n.SchoolStageId ?? 0)).Count() > 0 ? Math.Round(listPlan.Where(m => m.SchoolId == n.Id && m.SchoolStageId == (n.SchoolStageId ?? 0)).Sum(j => j.TotalRatio) / listPlan.Where(m => m.SchoolId == n.Id && m.SchoolStageId == (n.SchoolStageId ?? 0)).Count(), 2) : 0

                }).ToList();
            }
            return list;
        }

        /// <summary>
        /// 实验开出超期(学校)
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<List<UnitStatisticModel>> GetOverDueList(UnitListParam param)
        {
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            List<UnitStatisticModel> list = new List<UnitStatisticModel>();
            //获取学科
            var courseList = await staticDictionaryService.GetChildToList(new Model.Param.SystemManage.StaticDictionaryListParam() { Pids = string.Join(",", operatorinfo.SchoolStageList), TypeCode = DicTypeCodeEnum.Course.ParseToInt().ToString(), Nature = 1, Statuz = 1 });
            List<int> courseids = new List<int>();
            if (courseList != null && courseList.Count > 0)
            {
                courseids = courseList.Select(m => m.DictionaryId ?? 0).Distinct().ToList();
            }
            //获取学科对应的年级
            var gradeCorseList = await staticDictionaryService.GetChildList(new Model.Param.SystemManage.StaticDictionaryListParam() { Pids = string.Join(",", courseids), Statuz = 1, TypeCode = DicTypeCodeEnum.Grade.ParseToInt().ToString(), });

            var paramGradeClass = new SchoolGradeClassListParam();
            paramGradeClass.UnitId = operatorinfo.UnitId ?? 0;
            paramGradeClass.UnitType = operatorinfo.UnitType;
            var listGradeClass = await schoolGradeClassService.GetStatistics(paramGradeClass);

            if (listGradeClass != null && listGradeClass.Count() > 0)
            {
                var weekNum = 1;//当前时间，根据开始周次计算，是第几周
                if (operatorinfo.FirstWeekDate != null)
                {
                    weekNum = GetWeekNumberOfYear(DateTime.Now, (DateTime)operatorinfo.FirstWeekDate);
                }
                var paramPlanDetail = new PlanDetailListParam();
                paramPlanDetail.SchoolYearStart = operatorinfo.SchoolTermStartYear;
                paramPlanDetail.SchoolTerm = operatorinfo.SchoolTerm;
                paramPlanDetail.UnitType = operatorinfo.UnitType;
                paramPlanDetail.WeekNumlt = weekNum;
                paramPlanDetail.SchoolId = operatorinfo.UnitId ?? 0;
                var listPlanDetail = await planDetailService.GetList(paramPlanDetail, null);

                var paramBooking = new PlanInfoListParam();
                paramBooking.UnitType = operatorinfo.UnitType;
                paramBooking.SchoolId = operatorinfo.UnitId ?? 0;
                paramBooking.WeekNumgt = weekNum;
                var listRecordDetail = await planInfoService.PlanExpPageList(paramBooking, null);
                if (gradeCorseList != null)
                {
                    for (int i = 0; i < listGradeClass.Count(); i++)
                    {
                        for (int j = 0; j < gradeCorseList.Count; j++)
                        {
                            UnitStatisticModel model = new UnitStatisticModel();
                            //查询当前单位，当前班级
                            var entity = listGradeClass.ElementAt(i);
                            var entityCourseGrade = gradeCorseList.ElementAt(j);
                            if (entityCourseGrade.DictionaryId == entity.GradeId)
                            {
                                //获取当前学科，年级数据。
                                model.ClassName = entity.ClassDesc;
                                model.ClassId = entity.ClassId ?? 0;
                                model.CourseName = entityCourseGrade.PName;
                                model.GradeName = entityCourseGrade.DicName;
                                //应该开数大于0才存在超期数
                                model.PlanExperimentNum = GetCourseClassPlanNum(listPlanDetail, entity.GradeId ?? 0, entityCourseGrade.Pid ?? 0);
                                if (model.PlanExperimentNum > 0)
                                {
                                    model.RecordExperimentNum = GetCourseClassNum(listRecordDetail, entity.Id ?? 0, entity.GradeId ?? 0, entityCourseGrade.Pid ?? 0);
                                    //超期数
                                    if (model.PlanExperimentNum > model.RecordExperimentNum)
                                    {
                                        model.ExperimentNum = model.PlanExperimentNum - model.RecordExperimentNum;
                                        list.Add(model);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            return list;
        }

        /// <summary>
        /// 实验开出超期(区县)
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<List<UnitStatisticModel>> GetUnitSchoolOverDueList(UnitListParam param)
        {
            List<UnitStatisticModel> listdata = new List<UnitStatisticModel>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            param.Statuz = 1;
            param.Pid = operatorinfo.UnitId ?? 0;
            var list = await unitService.GetSchoolGradeCourseList(param, null);
            if (list != null && list.Count > 0)
            {
                var weekNum = 1;//当前时间，根据开始周次计算，是第几周
                if (operatorinfo.FirstWeekDate != null)
                {
                    weekNum = GetWeekNumberOfYear(DateTime.Now, (DateTime)operatorinfo.FirstWeekDate);
                }
                var paramPlanDetail = new PlanDetailListParam();
                paramPlanDetail.SchoolYearStart = operatorinfo.SchoolTermStartYear;
                paramPlanDetail.SchoolTerm = operatorinfo.SchoolTerm;
                paramPlanDetail.UnitType = operatorinfo.UnitType;
                paramPlanDetail.WeekNumlt = weekNum;
                paramPlanDetail.CountyId = operatorinfo.UnitId ?? 0;
                var listPlanDetail = await planDetailService.GetList(paramPlanDetail, null);

                var paramBooking = new PlanInfoListParam();
                paramBooking.UnitType = operatorinfo.UnitType;
                paramBooking.CountyId = operatorinfo.UnitId ?? 0;
                paramBooking.WeekNumgt = weekNum;
                var listRecordDetail = await planInfoService.PlanExpPageList(paramBooking, null);

                var paramGradeClass = new SchoolGradeClassListParam();
                paramGradeClass.CountyId = operatorinfo.UnitId ?? 0;
                paramGradeClass.UnitType = operatorinfo.UnitType;
                var listGradeClass = await schoolGradeClassService.GetStatistics(paramGradeClass);

                for (int i = 0; i < list.Count; i++)
                {
                    //查询当前单位，当前年级、学科
                    var entitySchool = list[i];
                    //应开实验数
                    entitySchool.PlanExperimentNum = GetCourseGradePlanNum(listPlanDetail, entitySchool.Id, listGradeClass, entitySchool.GradeId, entitySchool.CourseId, entitySchool.SchoolStageId ?? 0);//)listPlanDetail.Where(m => m.SchoolId == entitySchool.Id && m.GradeId == entitySchool.GradeId && m.CourseId == entitySchool.CourseId).Count();
                    if (entitySchool.PlanExperimentNum < 1)
                    {
                        continue;
                    }
                    entitySchool.RecordExperimentNum = GetCourseGradeNum(listRecordDetail, entitySchool.Id, listGradeClass, entitySchool.GradeId, entitySchool.CourseId, entitySchool.SchoolStageId ?? 0);

                    entitySchool.ExperimentNum = entitySchool.PlanExperimentNum - entitySchool.RecordExperimentNum;
                    if (entitySchool.ExperimentNum > 0)
                    {
                        listdata.Add(entitySchool);
                    }
                }
            }
            return listdata;
        }
        #endregion


        #region 实验开出率

        /// <summary>
        /// 获取区县实验教学达标看板数据
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<TData<object>> GetExperimentRatioList(PlanExamListParameterListParam param)
        {
            TData<object> obj = new TData<object>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            if (operatorinfo == null)
            {
                obj.Tag = -1;
                obj.Message = "登录信息失效！";
                return obj;
            }
            var paramGradeClass = new SchoolGradeClassListParam();
            paramGradeClass.UnitType = operatorinfo.UnitType;
            if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                var countyid = await unitService.GetParentUnitId(operatorinfo.UnitId ?? 0);
                param.SchoolId = operatorinfo.UnitId ?? 0;
                //param.SchoolStageList = operatorinfo.SchoolStageList;
                param.UnitId = countyid;

                paramGradeClass.UnitId = operatorinfo.UnitId ?? 0;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                param.UnitId = operatorinfo.UnitId ?? 0;
                paramGradeClass.CountyId = operatorinfo.UnitId ?? 0;
            }
            else
            {
                obj.Tag = 1;
                obj.Data = new List<PlanExamParameterEntity>();
                obj.Total = 0;
                obj.Message = "未查询到相关数据。";
                return obj;
            }

            var pagination = new Pagination();
            pagination.PageSize = int.MaxValue;
            var list = await planExamParameterService.GetPageList(param, pagination);
            IEnumerable<PlanExamParameterEntity> listRecordDetail = null;
            IEnumerable<SchoolGradeClassEntity> listGradeClass = null;
            if (list != null && list.Count() > 0)
            {
                obj.Data = list.ToList();
                listRecordDetail = await planExamParameterService.GetPlanExamExpRecordList(param, null);

                //每个考核数，要乘以年级中的班级数。
                listGradeClass = await schoolGradeClassService.GetStatistics(paramGradeClass);
            }
            //var paramSchoolStage = new StaticDictionaryListParam();
            //paramSchoolStage.TypeCode = DicTypeCodeEnum.SchoolStage.ParseToInt().ToString();
            //var listSchoolSatge = await staticDictionaryService.GetList(paramSchoolStage);

            var paramDicCourse = new StaticDictionaryListParam();
            paramDicCourse.TypeCode = DicTypeCodeEnum.Course.ParseToInt().ToString();
            paramDicCourse.PTypeCode = DicTypeCodeEnum.SchoolStage.ParseToInt().ToString();
            paramDicCourse.Nature = 1;
            paramDicCourse.Statuz = 1;
            paramDicCourse.Pid = param.SchoolStage ?? 0;
            var listCourse = await staticDictionaryService.GetAllList(paramDicCourse);
            List<int> courseids = new List<int>();
            if (listCourse != null && listCourse.Count > 0)
            {
                courseids = listCourse.Select(m => m.DictionaryId ?? 0).Distinct().ToList();
            }
            //获取学科对应的年级
            var gradeCorseList = await staticDictionaryService.GetChildList(new Model.Param.SystemManage.StaticDictionaryListParam() { Pids = string.Join(",", courseids), Statuz = 1, TypeCode = DicTypeCodeEnum.Grade.ParseToInt().ToString(), });
            //获取对应的年级。
            var gradeList = await staticDictionaryService.GetChildList(new Model.Param.SystemManage.StaticDictionaryListParam() { Pid = param.SchoolStage ?? 0, Statuz = 1, TypeCode = DicTypeCodeEnum.Grade.ParseToInt().ToString(), });

            List<object> objRatioList = new List<object>();
            if (listCourse != null && listCourse.Count > 0 && gradeList != null && gradeList.Count > 0)
            {
                listCourse = listCourse.OrderByDescending(m => m.DictionaryId).ToList();//年级从小到大
                gradeList = gradeList.OrderByDescending(m => m.DictionaryId).ToList();//年级从小到大
                List<int> couseTag = new List<int>();
                foreach (var course in listCourse)
                {
                    if (couseTag.Contains(course.DictionaryId ?? 0))
                    {
                        continue;
                    }
                    couseTag.Add(course.DictionaryId ?? 0);
                    List<int> gradeTag = new List<int>();
                    foreach (var grade in gradeList)
                    {
                        if (gradeTag.Contains(grade.DictionaryId ?? 0))
                        {
                            continue;
                        }
                        gradeTag.Add(grade.DictionaryId ?? 0);

                        //判断当前学科存在当前年级
                        if (gradeCorseList != null && gradeCorseList.Where(m => m.DictionaryId == grade.DictionaryId && m.Pid == course.DictionaryId).Count() > 0)
                        {
                            int num = 0;
                            int numed = 0;
                            if (list != null)
                            {
                                var listTemp = list.Where(m => m.GradeId == grade.DictionaryId && m.CourseId == course.DictionaryId);
                                if (listTemp != null)
                                {
                                    num = listTemp.Sum(m => m.NeedGroupNum * ExamGetExameClassNum(listGradeClass, m.GradeId, 0, m.SchoolStage, m.CourseId, m.CompulsoryType ?? 0)
                                        + m.NeedShowNum * ExamGetExameClassNum(listGradeClass, m.GradeId, 0, m.SchoolStage, m.CourseId, m.CompulsoryType ?? 0)
                                        + m.OptionalGroupNum * ExamGetExameClassNum(listGradeClass, m.GradeId, 0, m.SchoolStage, m.CourseId, m.CompulsoryType ?? 0)
                                        + m.OptionalShowNum * ExamGetExameClassNum(listGradeClass, m.GradeId, 0, m.SchoolStage, m.CourseId, m.CompulsoryType ?? 0));
                                    if (num > 0)
                                    {
                                        numed = listTemp.Sum(m =>
                                        ExamNeedShowNumed(listRecordDetail, m.Id ?? 0, 0, m.GradeId, 0, listGradeClass, m.SchoolStage, m.CourseId, m.CompulsoryType ?? 0)
                                        + ExamNeedGroupNumed(listRecordDetail, m.Id ?? 0, 0, m.GradeId, 0, listGradeClass, m.SchoolStage, m.CourseId, m.CompulsoryType ?? 0)
                                        + ExamOptionalShowNumed(listRecordDetail, m.Id ?? 0, 0, m.GradeId, 0, listGradeClass, m.SchoolStage, m.CourseId, m.CompulsoryType ?? 0)
                                        + ExamOptionalGroupNumed(listRecordDetail, m.Id ?? 0, 0, m.GradeId, 0, listGradeClass, m.SchoolStage, m.CourseId, m.CompulsoryType ?? 0));
                                    }

                                }
                            }
                            //都等于0的时候应该是100,两个值保持一致就是100
                            if (num == 0)
                            {
                                num = 1;
                                numed = 1;
                            }
                            objRatioList.Add(new { subject = course.DicName, grade = grade.DicName, value = AlgorithmHelper.Percentage(numed, num) });

                        }
                    }
                }
                obj.Tag = 1;
                obj.Data = objRatioList;
            }
            return obj;
        }

        #endregion

        #region 私有方法

        #region 编制计划实验达标 私有方法

        private int NeedShowNumed(IEnumerable<PlanInfoEntity> list, long id, long schoolid, long gradeid = 0, long schoolgradeclassid = 0, IEnumerable<SchoolGradeClassEntity> listclass = null, int schoolstage = 0, int courseid = 0, int compulsorytype = 0, long teacherid = 0)
        {
            int num = 0;
            if (list != null)
            {
                var listTemp = list.Where(n => n.Id == id && n.IsNeedDo == 1 && n.ExperimentType == 1);
                if (listclass != null && schoolstage > 0)
                {
                    var listClassTemp = listclass.Where(n => UnitBLL.GetSchoolPropList(schoolstage).Contains(n.SchoolProp));
                    if (gradeid >= GradeEnum.GaoYi.ParseToInt())
                    {
                        if (compulsorytype == ClassCompulsoryTypeEnum.Select.ParseToInt())
                        {
                            listClassTemp = listClassTemp.Where(m => m.SelectSubject != null && m.SelectSubject.Contains(courseid.ToString()));
                        }
                        else
                        {
                            listClassTemp = listClassTemp.Where(m => m.SelectSubject == null || !m.SelectSubject.Contains(courseid.ToString()));
                        }
                    }
                    listTemp = listTemp.Where(m => listClassTemp.Select(m => m.Id).Contains(m.SchoolGradeClassId));
                }
                if (schoolid > 0)
                {
                    listTemp = listTemp.Where(m => m.SchoolId == schoolid);
                }
                if (gradeid > 0)
                {
                    listTemp = listTemp.Where(m => m.GradeId == gradeid);
                }
                if (schoolgradeclassid > 0)
                {
                    listTemp = listTemp.Where(m => m.SchoolGradeClassId == schoolgradeclassid);
                }
                if (teacherid > 0)
                {
                    listTemp = listTemp.Where(m => m.TeacherId == teacherid);
                }
                num = listTemp.Count();
            }
            return num;
        }
        private int NeedGroupNumed(IEnumerable<PlanInfoEntity> list, long id, long schoolid, long gradeid = 0, long schoolgradeclassid = 0, IEnumerable<SchoolGradeClassEntity> listclass = null, int schoolstage = 0, int courseid = 0, int compulsorytype = 0, long teacherid = 0)
        {
            int num = 0;
            if (list != null)
            {
                var listTemp = list.Where(n => n.Id == id && n.IsNeedDo == 1 && n.ExperimentType == 2);
                if (listclass != null && schoolstage > 0)
                {
                    var listClassTemp = listclass.Where(n => UnitBLL.GetSchoolPropList(schoolstage).Contains(n.SchoolProp));
                    if (gradeid >= GradeEnum.GaoYi.ParseToInt())
                    {
                        if (compulsorytype == ClassCompulsoryTypeEnum.Select.ParseToInt())
                        {
                            listClassTemp = listClassTemp.Where(m => m.SelectSubject != null && m.SelectSubject.Contains(courseid.ToString()));
                        }
                        else
                        {
                            listClassTemp = listClassTemp.Where(m => m.SelectSubject == null || !m.SelectSubject.Contains(courseid.ToString()));
                        }
                    }
                    listTemp = listTemp.Where(m => listClassTemp.Select(m => m.Id).Contains(m.SchoolGradeClassId));
                }
                if (schoolid > 0)
                {
                    listTemp = listTemp.Where(m => m.SchoolId == schoolid);
                }
                if (gradeid > 0)
                {
                    listTemp = listTemp.Where(m => m.GradeId == gradeid);
                }
                if (schoolgradeclassid > 0)
                {
                    listTemp = listTemp.Where(m => m.SchoolGradeClassId == schoolgradeclassid);
                }
                if (teacherid > 0)
                {
                    listTemp = listTemp.Where(m => m.TeacherId == teacherid);
                }
                num = listTemp.Count();
            }
            return num;
        }
        private int OptionalShowNumed(IEnumerable<PlanInfoEntity> list, long id, long schoolid, long gradeid = 0, long schoolgradeclassid = 0, IEnumerable<SchoolGradeClassEntity> listclass = null, int schoolstage = 0, int courseid = 0, int compulsorytype = 0, long teacherid = 0)
        {
            int num = 0;
            if (list != null)
            {
                var listTemp = list.Where(n => n.Id == id && n.IsNeedDo == 2 && n.ExperimentType == 1);
                if (listclass != null && schoolstage > 0)
                {
                    var listClassTemp = listclass.Where(n => UnitBLL.GetSchoolPropList(schoolstage).Contains(n.SchoolProp));
                    if (gradeid >= GradeEnum.GaoYi.ParseToInt())
                    {
                        if (compulsorytype == ClassCompulsoryTypeEnum.Select.ParseToInt())
                        {
                            listClassTemp = listClassTemp.Where(m => m.SelectSubject != null && m.SelectSubject.Contains(courseid.ToString()));
                        }
                        else
                        {
                            listClassTemp = listClassTemp.Where(m => m.SelectSubject == null || !m.SelectSubject.Contains(courseid.ToString()));
                        }
                    }
                    listTemp = listTemp.Where(m => listClassTemp.Select(m => m.Id).Contains(m.SchoolGradeClassId));
                }
                if (schoolid > 0)
                {
                    listTemp = listTemp.Where(m => m.SchoolId == schoolid);
                }
                if (gradeid > 0)
                {
                    listTemp = listTemp.Where(m => m.GradeId == gradeid);
                }
                if (schoolgradeclassid > 0)
                {
                    listTemp = listTemp.Where(m => m.SchoolGradeClassId == schoolgradeclassid);
                }
                if (teacherid > 0)
                {
                    listTemp = listTemp.Where(m => m.TeacherId == teacherid);
                }
                num = listTemp.Count();
            }
            return num;
        }

        private int OptionalGroupNumed(IEnumerable<PlanInfoEntity> list, long id, long schoolid, long gradeid = 0, long schoolgradeclassid = 0, IEnumerable<SchoolGradeClassEntity> listclass = null, int schoolstage = 0, int courseid = 0, int compulsorytype = 0, long teacherid = 0)
        {
            int num = 0;
            if (list != null)
            {
                var listTemp = list.Where(n => n.Id == id && n.IsNeedDo == 2 && n.ExperimentType == 2);
                if (listclass != null && schoolstage > 0)
                {
                    var listClassTemp = listclass.Where(n => UnitBLL.GetSchoolPropList(schoolstage).Contains(n.SchoolProp));
                    if (gradeid >= GradeEnum.GaoYi.ParseToInt())
                    {
                        if (compulsorytype == ClassCompulsoryTypeEnum.Select.ParseToInt())
                        {
                            listClassTemp = listClassTemp.Where(m => m.SelectSubject != null && m.SelectSubject.Contains(courseid.ToString()));
                        }
                        else
                        {
                            listClassTemp = listClassTemp.Where(m => m.SelectSubject == null || !m.SelectSubject.Contains(courseid.ToString()));
                        }
                    }
                    listTemp = listTemp.Where(m => listClassTemp.Select(m => m.Id).Contains(m.SchoolGradeClassId));
                }
                if (schoolid > 0)
                {
                    listTemp = listTemp.Where(m => m.SchoolId == schoolid);
                }
                if (gradeid > 0)
                {
                    listTemp = listTemp.Where(m => m.GradeId == gradeid);
                }
                if (schoolgradeclassid > 0)
                {
                    listTemp = listTemp.Where(m => m.SchoolGradeClassId == schoolgradeclassid);
                }
                if (teacherid > 0)
                {
                    listTemp = listTemp.Where(m => m.TeacherId == teacherid);
                }
                num = listTemp.Count();
            }
            return num;
        }

        private int GetExameClassNum(IEnumerable<SchoolGradeClassEntity> list, int gradeid, long schoolid = 0, int schoolstage = 0, int courseid = 0, int compulsorytype = 0)
        {
            int num = 0;
            if (list != null)
            {
                var listTemp = list.Where(m => m.GradeId == gradeid);
                if (schoolid > 0)
                {
                    listTemp = listTemp.Where(m => m.UnitId == schoolid);
                }
                if (schoolstage > 0)
                {
                    listTemp = listTemp.Where(m => UnitBLL.GetSchoolPropList(schoolstage).Contains(m.SchoolProp));
                }
                if (gradeid >= GradeEnum.GaoYi.ParseToInt())
                {
                    if (compulsorytype == ClassCompulsoryTypeEnum.Select.ParseToInt())
                    {
                        listTemp = listTemp.Where(m => m.SelectSubject != null && m.SelectSubject.Contains(courseid.ToString()));
                    }
                    else
                    {
                        listTemp = listTemp.Where(m => m.SelectSubject == null || !m.SelectSubject.Contains(courseid.ToString()));
                    }
                }
                num = listTemp.Count();
            }
            return num;
        }

        private int GetCourseGradeNum(IEnumerable<PlanInfoEntity> list, long schoolid, IEnumerable<SchoolGradeClassEntity> listclass = null, long gradeid = 0, int courseid = 0, long schoolstage = 0, int compulsorytype = 0)
        {
            int num = 0;
            if (list != null)
            {
                var listTemp = list.Where(n => n.SchoolId == schoolid && n.GradeId == gradeid && n.CourseId == courseid);
                if (listclass != null && schoolstage > 0)
                {
                    var listClassTemp = listclass.Where(m => m.UnitId == schoolid && m.GradeId == gradeid).ToList();
                    if (gradeid >= GradeEnum.GaoYi.ParseToInt())
                    {
                        var listClassTemp1 = listClassTemp.Where(m => m.SelectSubject != null && m.SelectSubject.Contains(courseid.ToString()));
                        var num1 = listTemp.Where(m => listClassTemp1.Select(j => j.Id).Contains(m.SchoolGradeClassId)).Count();

                        var listClassTemp2 = listClassTemp.Where(m => m.SelectSubject == null || !m.SelectSubject.Contains(courseid.ToString()));
                        var num2 = listTemp.Where(m => listClassTemp2.Select(j => j.Id).Contains(m.SchoolGradeClassId)).Count();

                        num = num1 + num2;
                    }
                    else
                    {
                        num = listTemp.Where(m => listClassTemp.Select(m => m.Id).Contains(m.SchoolGradeClassId)).Count();
                    }
                }
            }
            return num;
        }

        private int GetCourseGradePlanNum(IEnumerable<PlanInfoEntity> list, long schoolid, IEnumerable<SchoolGradeClassEntity> listclass = null, long gradeid = 0, int courseid = 0, long schoolstage = 0, int compulsorytype = 0)
        {
            int num = 0;
            if (list != null)
            {
                var listTemp = list.Where(n => n.SchoolId == schoolid && n.GradeId == gradeid && n.CourseId == courseid).ToList();
                if (listclass != null && schoolstage > 0)
                {
                    var listClassTemp = listclass.Where(m => m.UnitId == schoolid && m.GradeId == gradeid).ToList();
                    if (gradeid >= GradeEnum.GaoYi.ParseToInt())
                    {
                        var gradeClassNum1 = listClassTemp.Where(m => m.SelectSubject != null && m.SelectSubject.Contains(courseid.ToString())).Count();
                        var num1 = listTemp.Where(m => m.CompulsoryType == ClassCompulsoryTypeEnum.Select.ParseToInt()).Count();
                        num += num1 * gradeClassNum1;

                        var gradeClassNum2 = listClassTemp.Where(m => m.SelectSubject == null || !m.SelectSubject.Contains(courseid.ToString())).Count();
                        var num2 = listTemp.Where(m => m.CompulsoryType == ClassCompulsoryTypeEnum.NonSelect.ParseToInt()).Count();
                        num += num2 * gradeClassNum2;

                    }
                    else
                    {
                        num = listTemp.Count * listClassTemp.Count;
                    }
                }
            }
            return num;
        }

        /// <summary>
        /// 班级应做数
        /// </summary>
        /// <param name="list"></param>
        /// <param name="schoolid"></param>
        /// <param name="listclass"></param>
        /// <param name="gradeid"></param>
        /// <param name="courseid"></param>
        /// <param name="schoolstage"></param>
        /// <param name="compulsorytype"></param>
        /// <returns></returns>
        private int GetCourseClassPlanNum(IEnumerable<PlanInfoEntity> list, long gradeid = 0, int courseid = 0)
        {
            int num = 0;
            if (list != null)
            {
                var listTemp = list.Where(n => n.GradeId == gradeid && n.CourseId == courseid).ToList();

                if (gradeid >= GradeEnum.GaoYi.ParseToInt())
                {
                    var num1 = listTemp.Where(m => m.CompulsoryType == ClassCompulsoryTypeEnum.Select.ParseToInt()).Count();
                    num += num1;

                    var num2 = listTemp.Where(m => m.CompulsoryType == ClassCompulsoryTypeEnum.NonSelect.ParseToInt()).Count();
                    num += num2;
                }
                else
                {
                    num = listTemp.Count;
                }
            }
            return num;
        }

        private int GetCourseClassNum(IEnumerable<PlanInfoEntity> list, long gradeclassid = 0, long gradeid = 0, int courseid = 0, int compulsorytype = 0)
        {
            int num = 0;
            if (list != null)
            {
                var listTemp = list.Where(n => n.SchoolGradeClassId == gradeclassid && n.CourseId == courseid);

                if (gradeid >= GradeEnum.GaoYi.ParseToInt())
                {
                    var num1 = listTemp.Where(m => m.SelectSubject != null && m.SelectSubject.Contains(courseid.ToString())).Count();

                    var num2 = listTemp.Where(m => m.SelectSubject == null || !m.SelectSubject.Contains(courseid.ToString())).Count();

                    num = num1 + num2;
                }
                else
                {
                    num = listTemp.Count();
                }
            }
            return num;
        }

        #endregion

        #endregion

        #region 实验达标 私有方法
        private int ExamNeedShowNumed(IEnumerable<PlanExamParameterEntity> list, long id, long schoolid, long gradeid = 0, long schoolgradeclassid = 0, IEnumerable<SchoolGradeClassEntity> listclass = null, int schoolstage = 0, int courseid = 0, int compulsorytype = 0, long teacherid = 0)
        {
            int num = 0;
            if (list != null)
            {
                var listTemp = list.Where(n => n.Id == id && n.IsNeedDo == 1 && n.ExperimentType == 1);
                if (listclass != null && schoolstage > 0)
                {
                    var listClassTemp = listclass.Where(n => UnitBLL.GetSchoolPropList(schoolstage).Contains(n.SchoolProp));
                    if (schoolstage == SchoolStageEnum.GaoZhong.ParseToInt())
                    {
                        if (compulsorytype == ClassCompulsoryTypeEnum.Select.ParseToInt())
                        {
                            listClassTemp = listClassTemp.Where(m => m.SelectSubject != null && m.SelectSubject.Contains(courseid.ToString()));
                        }
                        else
                        {
                            listClassTemp = listClassTemp.Where(m => m.SelectSubject == null || !m.SelectSubject.Contains(courseid.ToString()));
                        }
                    }
                    listTemp = listTemp.Where(m => listClassTemp.Select(m => m.Id).Contains(m.SchoolGradeClassId));
                }
                if (schoolid > 0)
                {
                    listTemp = listTemp.Where(m => m.SchoolId == schoolid);
                }
                if (gradeid > 0)
                {
                    listTemp = listTemp.Where(m => m.GradeId == gradeid);
                }
                if (schoolgradeclassid > 0)
                {
                    listTemp = listTemp.Where(m => m.SchoolGradeClassId == schoolgradeclassid);
                }
                if (teacherid > 0)
                {
                    listTemp = listTemp.Where(m => m.TeacherId == teacherid);
                }
                num = listTemp.Count();
            }
            return num;
        }
        private int ExamNeedGroupNumed(IEnumerable<PlanExamParameterEntity> list, long id, long schoolid, long gradeid = 0, long schoolgradeclassid = 0, IEnumerable<SchoolGradeClassEntity> listclass = null, int schoolstage = 0, int courseid = 0, int compulsorytype = 0, long teacherid = 0)
        {
            int num = 0;
            if (list != null)
            {
                var listTemp = list.Where(n => n.Id == id && n.IsNeedDo == 1 && n.ExperimentType == 2);
                if (listclass != null && schoolstage > 0)
                {
                    var listClassTemp = listclass.Where(n => UnitBLL.GetSchoolPropList(schoolstage).Contains(n.SchoolProp) && n.GradeId == gradeid);
                    if (schoolstage == SchoolStageEnum.GaoZhong.ParseToInt())
                    {
                        if (compulsorytype == ClassCompulsoryTypeEnum.Select.ParseToInt())
                        {
                            listClassTemp = listClassTemp.Where(m => m.SelectSubject != null && m.SelectSubject.Contains(courseid.ToString()));
                        }
                        else
                        {
                            listClassTemp = listClassTemp.Where(m => m.SelectSubject == null || !m.SelectSubject.Contains(courseid.ToString()));
                        }
                    }
                    listTemp = listTemp.Where(m => listClassTemp.Select(n => n.Id).Contains(m.SchoolGradeClassId));
                }
                if (schoolid > 0)
                {
                    listTemp = listTemp.Where(m => m.SchoolId == schoolid);
                }
                if (gradeid > 0)
                {
                    listTemp = listTemp.Where(m => m.GradeId == gradeid);
                }
                if (schoolgradeclassid > 0)
                {
                    listTemp = listTemp.Where(m => m.SchoolGradeClassId == schoolgradeclassid);
                }
                if (teacherid > 0)
                {
                    listTemp = listTemp.Where(m => m.TeacherId == teacherid);
                }
                num = listTemp.Count();
            }
            return num;
        }
        private int ExamOptionalShowNumed(IEnumerable<PlanExamParameterEntity> list, long id, long schoolid, long gradeid = 0, long schoolgradeclassid = 0, IEnumerable<SchoolGradeClassEntity> listclass = null, int schoolstage = 0, int courseid = 0, int compulsorytype = 0, long teacherid = 0)
        {
            int num = 0;
            if (list != null)
            {
                var listTemp = list.Where(n => n.Id == id && n.IsNeedDo == 2 && n.ExperimentType == 1);
                if (listclass != null && schoolstage > 0)
                {
                    var listClassTemp = listclass.Where(n => UnitBLL.GetSchoolPropList(schoolstage).Contains(n.SchoolProp));
                    if (schoolstage == SchoolStageEnum.GaoZhong.ParseToInt())
                    {
                        if (compulsorytype == ClassCompulsoryTypeEnum.Select.ParseToInt())
                        {
                            listClassTemp = listClassTemp.Where(m => m.SelectSubject != null && m.SelectSubject.Contains(courseid.ToString()));
                        }
                        else
                        {
                            listClassTemp = listClassTemp.Where(m => m.SelectSubject == null || !m.SelectSubject.Contains(courseid.ToString()));
                        }
                    }
                    listTemp = listTemp.Where(m => listClassTemp.Select(m => m.Id).Contains(m.SchoolGradeClassId));
                }
                if (schoolid > 0)
                {
                    listTemp = listTemp.Where(m => m.SchoolId == schoolid);
                }
                if (gradeid > 0)
                {
                    listTemp = listTemp.Where(m => m.GradeId == gradeid);
                }
                if (schoolgradeclassid > 0)
                {
                    listTemp = listTemp.Where(m => m.SchoolGradeClassId == schoolgradeclassid);
                }
                if (teacherid > 0)
                {
                    listTemp = listTemp.Where(m => m.TeacherId == teacherid);
                }
                num = listTemp.Count();
            }
            return num;
        }

        private int ExamOptionalGroupNumed(IEnumerable<PlanExamParameterEntity> list, long id, long schoolid, long gradeid = 0, long schoolgradeclassid = 0, IEnumerable<SchoolGradeClassEntity> listclass = null, int schoolstage = 0, int courseid = 0, int compulsorytype = 0, long teacherid = 0)
        {
            int num = 0;
            if (list != null)
            {
                var listTemp = list.Where(n => n.Id == id && n.IsNeedDo == 2 && n.ExperimentType == 2);
                if (listclass != null && schoolstage > 0)
                {
                    var listClassTemp = listclass.Where(n => UnitBLL.GetSchoolPropList(schoolstage).Contains(n.SchoolProp));
                    if (schoolstage == SchoolStageEnum.GaoZhong.ParseToInt())
                    {
                        if (compulsorytype == ClassCompulsoryTypeEnum.Select.ParseToInt())
                        {
                            listClassTemp = listClassTemp.Where(m => m.SelectSubject != null && m.SelectSubject.Contains(courseid.ToString()));
                        }
                        else
                        {
                            listClassTemp = listClassTemp.Where(m => m.SelectSubject == null || !m.SelectSubject.Contains(courseid.ToString()));
                        }
                    }
                    listTemp = listTemp.Where(m => listClassTemp.Select(m => m.Id).Contains(m.SchoolGradeClassId));
                }
                if (schoolid > 0)
                {
                    listTemp = listTemp.Where(m => m.SchoolId == schoolid);
                }
                if (gradeid > 0)
                {
                    listTemp = listTemp.Where(m => m.GradeId == gradeid);
                }
                if (schoolgradeclassid > 0)
                {
                    listTemp = listTemp.Where(m => m.SchoolGradeClassId == schoolgradeclassid);
                }
                if (teacherid > 0)
                {
                    listTemp = listTemp.Where(m => m.TeacherId == teacherid);
                }
                num = listTemp.Count();
            }
            return num;
        }

        private int ExamGetExameClassNum(IEnumerable<SchoolGradeClassEntity> list, int gradeid, long schoolid = 0, int schoolstage = 0, int courseid = 0, int compulsorytype = 0)
        {
            int num = 0;
            if (list != null)
            {
                var listTemp = list.Where(m => m.UnitId > 0);
                if (gradeid > 0)
                {
                    listTemp = listTemp.Where(m => m.GradeId == gradeid);
                }
                if (schoolid > 0)
                {
                    listTemp = listTemp.Where(m => m.UnitId == schoolid);
                }
                if (schoolstage > 0)
                {
                    listTemp = listTemp.Where(m => UnitBLL.GetSchoolPropList(schoolstage).Contains(m.SchoolProp));
                }
                if (gradeid >= GradeEnum.GaoYi.ParseToInt())
                {
                    if (compulsorytype == ClassCompulsoryTypeEnum.Select.ParseToInt())
                    {
                        listTemp = listTemp.Where(m => m.SelectSubject != null && m.SelectSubject.Contains(courseid.ToString()));
                    }
                    else
                    {
                        listTemp = listTemp.Where(m => m.SelectSubject == null || !m.SelectSubject.Contains(courseid.ToString()));
                    }
                }
                num = listTemp.Count();
            }
            return num;
        }

        #endregion
    }

}
