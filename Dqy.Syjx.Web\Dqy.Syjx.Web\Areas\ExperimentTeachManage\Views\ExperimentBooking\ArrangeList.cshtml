﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .select-list li input {
        width: auto !important;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <span id="schoolYearStart" col="SchoolYearStart" style="display:inline-block;width:80px;"></span>
                    </li>
                    <li>
                        <span id="schoolTerm" col="SchoolTerm" style="display:inline-block;width:80px;"></span>
                    </li>
                    <li>
                        <span id="schoolGradeClassId" col="SchoolGradeClassId" style="display:inline-block;width:160px;"></span>
                    </li>
                    <li>
                        <span id="courseId" col="CourseId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <span id="sourceType" col="SourceType" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <span id="experimentType" col="ExperimentType" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <input id="experimentName" col="ExperimentName" placeholder="实验名称" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-default btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group-sm hidden-xs" id="toolbar">
            <a id="btnBatch" class="btn btn-success" onclick="openBatchForm()"><i class="fa fa-bars"></i> 批量安排</a>
            <span style="color:#999;">“批量安排”只能选择相同的预约实验。</span>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    var Com_SchoolTermYear = new Date().getFullYear();
    var Com_SchoolTerm = 1;
    $(function () {
        loadSearchCombo();
        ys.ajax({
            url: '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/GetSchoolTermInfo")',
            type: 'get',
            async: false,
            success: function (obj) {
                if (obj.Tag == 1 && obj.Data != undefined) {
                    if (obj.Data.SchoolTermStartYear != undefined && parseInt(obj.Data.SchoolTermStartYear) > 0) {
                        Com_SchoolTermYear = obj.Data.SchoolTermStartYear;
                        $("#schoolYearStart").ysComboBox('setValue', Com_SchoolTermYear);
                    }
                    if (obj.Data.SchoolTerm == 1 || obj.Data.SchoolTerm == 2) {
                        Com_SchoolTerm = obj.Data.SchoolTerm;
                        $("#schoolTerm").ysComboBox('setValue', Com_SchoolTerm);
                    }
                }
            }
        });
        initGrid();


    });

    function initGrid() {
        var queryUrl = '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/GetArrangeListJson")' + '?ListType=1';
        $('#gridTable').ysTable({
            url: queryUrl,
            columns: [
                {
                    field: '', title: '操作', halign: 'center', align: 'center', width: 60,
                    formatter: function (value, row, index) {
                        return $.Format('<a class="btn btn-success btn-xs" href="#" onclick="arrange(this)" value="{0}"><i class="fa fa-edit"></i>安排</a> ', row.Id);
                    }
                },
                { checkbox: true, visible: true },
                { field: 'CourseName', title: '学科', sortable: true, halign: 'center', align: 'center', width: 50 },
                {
                    field: 'ExperimentName', title: '实验名称', halign: 'center', align: 'left', width: 200,
                    formatter: function (value, row, index) {
                        var html = '';
                        if (row.Id) {
                            if (row.ObjectList != undefined && row.ObjectList.length > 0) {
                                for (let i = 0; i < row.ObjectList.length; i++) {
                                    const element = row.ObjectList[i];
                                    switch (element.ExperimentType) {
                                        case @ExperimentTypeEnum.Demo.ParseToInt():
                                            html += Syjx.GetCircleDemoHtml();
                                            break;
                                        case @ExperimentTypeEnum.Group.ParseToInt():
                                            html += Syjx.GetCircleGroupHtml();
                                            break;

                                    }
                                    html += element.ExperimentName;
                                    if ((i + 1) != row.ObjectList.length) {
                                        html += '<br/>';
                                    }
                                }
                            }
                        }
                        return html;
                    }
                },
                {
                    field: 'ClassName', title: '上课班级', sortable: false, halign: 'center', align: 'center', width: 120, formatter: function (value, row, index) {
                        var html = '';
                        if (row.Id) {
                            html = '--';
                            if (row.ClassName != undefined && row.ClassName.length > 0) {
                                html = row.ClassName;
                            }
                        }
                        return html;
                    }
                },
                {
                    field: 'ClassTime', title: '上课时间', sortable: true, halign: 'center', align: 'center', width: 110,
                    formatter: function (value, row, index) {
                        var html = '';
                        if (row.Id) {
                            html = '--';
                            if (parseInt(row.SectionIndex) > 0) {
                                html = $.Format("{0}(第{1}节)", ys.formatDate(row.ClassTime, "yyyy-MM-dd"), row.SectionIndex);
                            }
                        }
                        return html;
                    }
                },
                {
                    field: 'FunRoomId', title: '上课地点', sortable: true, halign: 'center', align: 'left', width: 140,
                    formatter: function (value, row, index) {
                        var html = '';
                        if (row.Id) {
                            html = '--';
                            if (row.FunRoomId == 1) {
                                html = '普通教室';
                            }
                            else if (row.FunRoomName != undefined && row.FunRoomName.length > 0) {
                                html = row.FunRoomName;
                            }
                        }
                        return html;
                    }
                },
                { field: 'BookUserName', title: '预约人', sortable: true, halign: 'center', align: 'center', width: 80 },
                {
                    field: 'SourceType', title: '实验来源', sortable: true, halign: 'center', align: 'center', width: 50,
                    formatter: function (value, row, index) {
                        switch (value) {
                            case @SourceTypeEnum.ExperimentPlan.ParseToInt():
                                return "@SourceTypeEnum.ExperimentPlan.GetDescription()";
                            case @SourceTypeEnum.ExperimentList.ParseToInt():
                                return "@SourceTypeEnum.ExperimentList.GetDescription()";
                            default:
                                return '实验计划/实验目录';
                        }
                    }
                }
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function resetGrid() {
        //清空条件
        $('#schoolYearStart').ysComboBox('setValue', Com_SchoolTermYear);
        $('#schoolTerm').ysComboBox('setValue', Com_SchoolTerm);
        $('#schoolGradeClassId').ysComboBox('setValue', -1);
        $('#courseId').ysComboBox('setValue', -1);
        $('#sourceType').ysComboBox('setValue', -1);
        $('#experimentType').ysComboBox('setValue', -1);
        $('#experimentName').val('');
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function loadSearchCombo() {
        ComBox.SchoolTermYear($('#schoolYearStart'), undefined, '学年');
        $("#schoolTerm").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(SchoolTermEnum).EnumToDictionaryString())), defaultName: '学期' });
        $('#courseId').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildToListJson")' + '?TypeCode=@DicTypeCodeEnum.Course.ParseToInt()&Ids=1005002,1005003,1005004,1005005',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '学科'
        });
        $("#sourceType").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(SourceTypeEnum).EnumToDictionaryString())), defaultName: '实验来源' });
        $("#experimentType").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(ExperimentTypeEnum).EnumToDictionaryString())), defaultName: '实验类型' });
        ys.ajax({
            url: '@Url.Content("~/OrganizationManage/SchoolGradeClass/GetListJson")' + '?IsGraduate=0',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    $('#schoolGradeClassId').ysComboBox({
                        data: obj.Data,
                        key: 'Id',
                        value: 'ClassName',
                        defaultName: '班级'
                    });
                }
            }
        });
    }

    function arrange(obj) {
        var id = $(obj).attr('value');
        var url = '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/Arrange")' + '?id=' + id;
        createMenuItem(url, "实验安排");
    }

    function openBatchForm() {
        var ids = '';
        var selectedRow = $('#gridTable').bootstrapTable('getSelections');
        if (ys.checkRowDelete(selectedRow)) {
            let isTrue = selectedRow.filter(item => item.ExperimentIds !== selectedRow[0].ExperimentIds)
            if (isTrue.length > 0) {
                ys.msgError("请选择相同实验批量安排");
                return false;
            }
            ids = ys.getIds(selectedRow);
        }
        else {
            ys.msgError("请选择需要安排的数据。");
            return;
        }
        var url = '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/ArrangeBatch")' + '?ids=' + ids;
        createMenuItem(url, "实验安排");
    }
</script>
