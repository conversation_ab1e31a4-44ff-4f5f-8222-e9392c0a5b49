{
  "Logging": {
    "LogLevel": {
      "Default": "Warning"
    }
  },
  "AllowedHosts": "*",
  "CasBaseUrl": "http://**************:13307", //CAS服务端登录地址
  "SystemConfig": {
    "Demo": false, // 是否是演示模式
    "LoginMultiple": true, // 是否允许一个账户在多处登录
    "LoginProvider": "Cookie", // 登录信息保存方式 Cookie Session WebApi
    "SnowFlakeWorkerId": 1, // SnowFlake 节点序号
    "ApiSite": "https://localhost:5200",
    "WebSite": "https://localhost:5200",
    "VirtualDirectory": "", // 虚拟目录
    
    //"DBProvider": "Dm",
    //"DBConnectionString": "SERVER=***********;PORT=5236;USER=****;PASSWORD=****;DATABASE=****", //达梦
    //"DBConnectionString2": "Data Source=***********;Initial Catalog=Syjx;Persist Security Info=True;User ID=****;Password=****", //海康使用数据库
    ////"DBConnectionString": "Data Source=***********;Initial Catalog=Syjx;Persist Security Info=True;User ID=****;Password=****", //实验教学平台数据库

    "DBProvider": "SqlServer",
    "DBConnectionString2": "Data Source=***********;Initial Catalog=syjx;Persist Security Info=True;User ID=****;Password=****;TrustServerCertificate=true;", //海康使用数据库
    "DBConnectionString": "Data Source=***********;Initial Catalog=syjx;Persist Security Info=True;User ID=****;Password=****;TrustServerCertificate=true;", //实验教学平台数据库

    //"DBProvider": "MySql",
    //"DBConnectionString": "server=***********;database=syjx;user=root;password=******;port=3306;pooling=true;max pool size=20;persist security info=True;charset=utf8mb4;",
    //"DBConnectionString2": "server=***********;database=syjx;user=root;password=******;port=3306;pooling=true;max pool size=20;persist security info=True;charset=utf8mb4;",

    "DBCommandTimeout": 180, // 数据库超时时间，单位秒
    "DBBackup": "d:\\Backup", // 数据库备份路径

    "CacheProvider": "Memory", // 缓存使用方式 Memory Redis
    "RedisConnectionString": "127.0.0.1:6379",
    "CameraCollectionPath": "http://localhost:6017/home/<USER>", // 获取关联的摄像头采信息。
    "WxOpenAppId": "wx8b5b7ff0c0cbe8bb", //小程序扫码登录用
    "WxOpenAppSecret": "cbcc08f10f5a658050bb054ca024bb01", //小程序扫码登录用未设置
    "InstrumentApiSite": "http://w.api.51jyyq.com/api/", //仪器商城Api地址，仪器选型调用
    "MallId": 1, //仪器商城api对应商城Id
    "InstrumentWebSite": "http://www.51jyyq.com", //仪器商城Web端地址

    "HaiKangAPPkey": "21019000", //海康合作方APPKey
    "HaiKangAPPsecret": "BYYfzwD436X4mRdzdWp4", //海康合作方APPsecret
    "HaiKangIP": "*************", //海康综合安防管理平台IP地址
    "HaiKangPort": 1443, //海康综合安防管理平台端口
    "IsValidPicPeople": 3, //采集的摄像头图片人头数是否有效
    "IsHttps": 1, //海康综合平台是否启用HTTPS协议
    "HaiKangReplaceHttpUrl": "http://************:18001/",

    "DahuaApi": "https://***************:9999", //大华智慧校园综合管理平台Api地址
    "DahuaIsHttps": true, //是否启用HTTPS协议
    "DahuaClientId": "Junfei", //大华平台ClientId
    "DahuaClientSecret": "fb01cb7f-4e05-4eec-b197-96e49d513764", //大华平台ClientSecret
    "DahuaSysUsername": "system", //系统账号
    "DahuaSysPwd": "Admin123", //系统密码
    "DahuaVideoIP": "***************", //大华视频播放替换地址
    "VideoPlatform": 2, //采集平台(1、海康 2、大华 3、其他)

    "IsOpenThird": 0, //是否启用第三方登录

    "ThirdDirectUrl": "~/accountthird/auth_tx", //重定向第三方的url地址

    "IsOpenScanCodeLogin": 0, //是否启用扫码登录（0: 不启用；1: 开启）
    "MobileClientType": 0 //移动客户端类型 0: H5；1：微信小程序；2：钉钉小程序；3：企业微信
  },
  "AuthCenter": {
    "remark": "认证中心",
    "authHost": "https://localhost:7280",
    "dataHost": "http://localhost:9291",
    "dataHost2": "",
    "clientID": "02869c67-aaed-40b2-80e0-923635306ced",
    "clientSecret": "3c9d77f2-3b74-4ac5-91ea-ae503dfb282e",
    "callBackUrl": "http://localhost:5200/accountthird/authcentercallback",
    "logout": "../AccountThird/Logout_gsq",
    "root": "",
    "key": "",
    "userScope": "userScope",
    "dataScope": "read:api.****.core"
  },
  "WxSign": {
    "Url": "http://localhost:5002"
  },
  //"sso": {
  //  "remark": "钉钉免登",
  //  "authHost": "https://api.dingtalk.com",
  //  "dataHost": "https://oapi.dingtalk.com",
  //  "dataHost2": "",
  //  "clientID": "ding0bv9qbncij6roir5",
  //  "clientSecret": "9TyoQ02lMJtc0M9br-lKBMmIO95i_ShTMMKv78PlYfrNpJCTMLrsNGZTB9IudKEd",
  //  "callBackUrl": "",
  //  "logout": "",
  //  "root": "",
  //  "key": "",
  //  "userScope": "userScope",
  //  "dataScope": ""
  //},
  //"sso": {
  //  "remark": "姑苏区",
  //  "authHost": "https://www.gusuedu.cn", //教育云地址
  //  "dataHost": "https://www.gusuedu.cn", //教育云接口
  //  "dataHost2": "",
  //  "clientID": "Laboratory_Teaching",
  //  "clientSecret": "Laboratory_Teaching",
  //  "callBackUrl": "http://syjx.gusuedu.cn/AccountThird/Auth_gsq",
  //  "logout": "../AccountThird/Logout_gsq",
  //  "root": "",
  //  "key": "",
  //  "userScope": "userScope",
  //  "dataScope": ""
  //},
  //"sso": {
  //  "remark": "富阳区",
  //  "authHost": "https://cube.zjedu.com",
  //  "dataHost": "https://cube.zjedu.com",
  //  "dataHost2": "",
  //  "clientID": "suiteg9bo72qa8f2xur3f",
  //  "clientSecret": "5Yy2wP3eoplfTv2xos1V78MdDVpDXGDrvfJ1Eo-RvFMtYpPPfqbnJUbmDa-4qXmn",
  //  "callBackUrl": "",
  //  "logout": "",
  //  "root": "",
  //  "key": "",
  //  "userScope": "userScope",
  //  "dataScope": ""
  //},
  "authvisit": {
    "remark": "研发管理平台账号授权访问对接",
    "authHost": "http://*************:8089/api",
    "dataHost": "http://*************:8089/api",
    "dataHost2": "",
    "clientID": "561650890260353024",
    "clientSecret": "NTYxNjUwODkwMjYwMzUzMDI1",
    "callBackUrl": "http://localhost:5200/AccountThird/Auth_Visit",
    "logout": "",
    "root": "",
    "key": "",
    "userScope": "",
    "dataScope": ""
  },
  //"sso": {
  //  "remark": "常熟市",
  //  "authHost": "http://csjy.cssxsjy.cn/api",
  //  "dataHost": "https://csjy.cssxsjy.cn",
  //  "dataHost2": "",
  //  "clientID": "D3CF6EC459C848DDEC6C35DB00C3243A",
  //  "clientSecret": "e9495f6cbbb64a3c99892c4bd68b9234",
  //  "callBackUrl": "http://syjx.jscsedu.cn/AccountThird/AuthCss",
  //  "logout": "https://csjy.cssxsjy.cn/space/index.php?r=portal/user/login",
  //  "root": "",
  //  "key": "",
  //  "userScope": "userScope",
  //  "dataScope": ""
  //}
  //"sso": {
  //  "remark": "吴中区",
  //  "authHost": "http://www.szwzedu.cn", //教育云地址
  //  "dataHost": "http://zzxx.szwzedu.cn:10080", //教育云接口
  //  "dataHost2": "",
  //  "clientID": "20232310081052577709125",
  //  "clientSecret": "c2VjcmV0MjAyMzEwMDgxMDUyNTc4Nzc4NTUz",
  //  "callBackUrl": "",
  //  "logout": "http://www.szwzedu.cn/logout?service=http://www.szwzedu.cn/",
  //  "root": "443",
  //  "key": "",
  //  "userScope": "userScope",
  //  "dataScope": ""
  //},
  //"sso": {
  //  "remark": "昆山市",
  //  "authHost": "https://sso.ksedu.cn",
  //  "dataHost": "https://sso.ksedu.cn", //教育云接口
  //  "dataHost2": "https://api.ksedu.cn/edu/openapi/index", //凤凰接口
  //  "clientID": "****FWPT2308120001",
  //  "clientSecret": "b2792acc-a249-40da-9235-02606a415391",
  //  "callBackUrl": "http://syjx.ksedu.cn/AccountThird/Authkss",
  //  "logout": "https://uia.ksedu.cn",
  //  "root": "443",
  //  "key": "",
  //  "userScope": "userScope",
  //  "dataScope": "",
  //  "accountid": "D3F982B69CA54526B73F8DA81420213E",
  //  "accountpwd": "42C28F4A3CE045A99ABEEC4F09B84498",
  //  "countyusercode": ",************,************,************,************,************,320583001100320583001040,**********,************," //区教育局用户id
  //},
  //"sso": {
  //  "remark": "南通市通州区",
  //  "authHost": "https://yun.jstzjy.net",
  //  "dataHost": "http://open.yun.jstzjy.net",
  //  "dataHost2": "http://yun.jstzjy.net",
  //  "clientID": "093e6d86f4bb41c09c8b6109d703d42e",
  //  "clientSecret": "11a550f6df4232d18eb9a7d2993fd700",
  //  "callBackUrl": "https://sygl.jstzjy.net/AccountThird/Auth_tz?ifuseriflyssost",
  //  "logout": "http://yun.jstzjy.net/",
  //  "root": "443",
  //  "key": "305C300D06092A864886F70D0101010500034B003048024100CE5B225EC3CDD2C7980657EA3FEFC8D2B94673BC237F82460400742AC1293833F66E12BCC7E26A4AFAEEF1FB98F31DA8D851A5403EA106E98A18AECDCCEF6B050203010001",
  //  "userScope": "userScope",
  //  "dataScope": ""
  //}
  //"sso": {
  //  "remark": "内部统一身份认证",
  //  "authHost": "http://localhost:6200",
  //  "dataHost": "http://localhost:5110",
  //  "dataHost2": "",
  //  "clientID": "561650890260353024",
  //  "clientSecret": "NTYxNjUwODkwMjYwMzUzMDI1",
  //  "callBackUrl": "http://localhost:5200/accountthird/auth_self",
  //  "logout": "http://localhost:6200/loginoff",
  //  "root": "",
  //  "key": "",
  //  "userScope": "",
  //  "dataScope": ""
  //}
  //"sso": {
  //  "remark": "成华区",
  //  "authHost": "https://api.cdchjyy.com",
  //  "dataHost": "https://cdchjyy.com",
  //  "dataHost2": "",
  //  "clientID": "********************************",
  //  "clientSecret": "f8a948eb6c7f47619441d88bde6c260c",
  //  "callBackUrl": "https://syjx.cdchjyy.com/accountthird/auth_chq",
  //  "root": "",
  //  "key": "",
  //  "userScope": "userScope",
  //  "dataScope": ""
  //}
  "sso": {
    "remark": "常州市智慧教育应用基础服务平台",
    "authHost": "https://dw.czerc.com",
    "dataHost": "https://dw.czerc.com",
    "dataHost2": "",
    "clientID": "64ab5a5cefef484bad0311a52648b519",
    "clientSecret": "1329fee9506345d1aca00e30521a1e03",
    "callBackUrl": "https://syjx.cdchjyy.com/accountthird/Auth_czs",
    "root": "",
    "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCjwgZr6tPCBfhZBdSOzNRSrYNrji4I9yUknp/cWcVflokbUrf1rOajDs2Q9Ysc71/rU9Sq/o0WVT8xHSaxkoTCJ5GDvd6Pps1YQt6aDjc9IlkDkUFPJEgIuNLiHVu74xDPCeZfexsENLr+To3hEHt75UYMpZMJmR9hsOddNBHcPQIDAQAB",
    "privatekey": "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAKPCBmvq08IF+FkF1I7M1FKtg2uOLgj3JSSen9xZxV+WiRtSt/Ws5qMOzZD1ixzvX+tT1Kr+jRZVPzEdJrGShMInkYO93o+mzVhC3poONz0iWQORQU8kSAi40uIdW7vjEM8J5l97GwQ0uv5OjeEQe3vlRgylkwmZH2Gw5100Edw9AgMBAAECgYA8J2fIeKh021CREw0zr5ZHEKn39nB+ppqkSwiaHvfb1yZiPQ6KVosaVrO4jhod9OidP1wdvAgDrijaV8UA8buGJ+KnZTQumJCc34EArHlUenp2EuPEK+XnfmCvpFr+yRkuPPmE5C9OIjFzoHvrpfCEIUWOAozlWh/i2NnkxEDIAQJBANman7xf1/QeQ0iFyY8mtCXP3+76UDuA9jSqvesXagDWLp8MmToCMzOOFXIgm1W6K692p9GzJvkS3rH5P+1xywECQQDApx941Tc2JfmFHtC8YSaq69u2lvj1N+zu4GI+nd3CwMprZ/5MSBzO5yHLpsH6LICIZWycStIRagQE+8so1X09AkEAv6Oaa/bopFiGnvccVNMcMnTJXZjbUhw4OJfqXIGpXIVEV9RrmZfYad4G6xTmWHB2CrhwAZFKkG7a61h8wWvHAQJAAuqEr2+KxmcMv9cXkcJms2+eg8UFP7D5BSSgWBJLqQcCCV4pmz38MaWN/WcoyICj8WyXmEYJp/nS1cB58sRIgQJAQWxfYWWtiByntjpTWw0f503yq7D9aIpYuRz9YyvhMXAxPJ1HA9sWc+IMis5cEe811Fq1E+BF/yzsv+fMA/VG/g==",
    "userScope": "userScope",
    "dataScope": ""
  }
  //"sso": {
  //  "remark": "泰州泰兴市",
  //  "authHost": "http://www.jyyun.com",
  //  "dataHost": "https://open.jyyun.com",
  //  "dataHost2": "http://txzhjy.cn",
  //  "clientID": "a8f3835670c94732865f693955ad812a",
  //  "clientSecret": "243d57171b1a87e08afbe7781d7ba72f",
  //  "callBackUrl": "http://syjx.txzhjy.cn/accountthird/auth_tx?ifuseriflyssost",
  //  "root": "443",
  //  "key": "305C300D06092A864886F70D0101010500034B003048024100C21DCE7B408FFB4E6040079CD4A4C4F20AAE57344773BB50201D0F8BFD344427BB113DE759C242A9A9F1D9E5E6F48E0A88906A5A968976DE4708B256E160E6E30203010001",
  //  "userScope": "userScope",
  //  "dataScope": ""
  //}
  //"sso": {
  //  "remark": "泰州市",
  //  "authHost": "http://api.tze.cn",
  //  "dataHost": "http://www.tze.cn/oauth/oauthserver/index.action",
  //  "dataHost2": "",
  //  "clientID": "9DFD2EEB37D0375554AC7EFF8BBE1E29",
  //  "clientSecret": "d35e4023ded54f4c928f37a87e39348d",
  //  "callBackUrl": "http://syjx.tze.cn/accountthird/auth_tzs"
  //  "root": "",
  //  "key": "",
  //  "userScope": "userScope",
  //  "dataScope": "",
  //  "logout": "http://www.tze.cn/space//index.php?r=portal/user/login"
  //}
}