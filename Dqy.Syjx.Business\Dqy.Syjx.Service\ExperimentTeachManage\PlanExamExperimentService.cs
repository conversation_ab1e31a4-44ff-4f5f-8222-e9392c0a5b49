﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.ExperimentTeachManage;
using Dqy.Syjx.Model.Param.ExperimentTeachManage;
using NPOI.SS.Formula.Functions;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Service.OrganizationManage;
using Dqy.Syjx.Enum.ExperimentTeachManage;

namespace Dqy.Syjx.Service.ExperimentTeachManage
{
    /// <summary>
    /// 创 建：陶瑞
    /// 日 期：2024-03-08 13:38
    /// 描 述：考核实验清单服务类
    /// </summary>
    public class PlanExamExperimentService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<PlanExamExperimentEntity>> GetList(PlanExamExperimentListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<PlanExamExperimentEntity>> GetPageList(PlanExamExperimentListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<PlanExamExperimentEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<PlanExamExperimentEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(PlanExamExperimentEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(PlanExamExperimentEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update ex_PlanExamExperiment set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
                strSql = $"update ex_PlanExamExperiment set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<PlanExamExperimentEntity, bool>> ListFilter(PlanExamExperimentListParam param)
        {
            var expression = LinqExtensions.True<PlanExamExperimentEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (param.PlanParameterSetId > 0)
                {
                    expression = expression.And(t => t.PlanParameterSetId == param.PlanParameterSetId);
                }
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (!string.IsNullOrEmpty(param.Name))
                {
                    expression = expression.And(t => t.ExperimentName.Contains(param.Name) ||  t.Chapter.Contains(param.Name));
                }
                if (param.ExperimentType > 0)
                {
                    expression = expression.And(t => t.ExperimentType == param.ExperimentType);
                }
                if (param.IsNeedDo > 0)
                {
                    expression = expression.And(t => t.IsNeedDo == param.IsNeedDo);
                }
                if (param.IsEvaluate>0)
                {
                    expression = expression.And(t => t.IsEvaluate == param.IsEvaluate);
                }
                if (param.VersionId>0)
                {
                    expression = expression.And(t => t.VersionBaseId == param.VersionId);
                }
            }
            return expression;
        }
        #endregion

        #region sql语句查询方法

        /// <summary>
        /// 实验预约查询
        /// </summary>
        /// <param name="param">查询参数</param>
        /// <param name="pagination">分页参数</param>
        /// <returns></returns>
        public async Task<List<TextbookVersionBaseEntity>> GetVersionList(PlanExamExperimentListParam param)
        {
            StringBuilder strSql = new StringBuilder();
            var parameter = new List<DbParameter>();

            strSql.Append(@$" SELECT VersionBaseId AS Id ,VersionName From (
                                       SELECT e1.* ,v2.VersionName ,ep3.UnitId 
                                       FROM ex_PlanExamExperiment AS e1
                                       INNER JOIN ex_PlanExamParameter AS ep3 ON e1.PlanParameterSetId = ep3.Id
                                       INNER JOIN ex_TextbookVersionBase AS v2 ON v2.BaseIsDelete = 0 AND e1.VersionBaseId = v2.Id
                                       WHERE e1.BaseIsDelete = 0                               
                          ) as T WHERE  1 = 1 ");
            if (param != null)
            {
                if (!param.PlanParameterSetId.IsNullOrZero())
                {
                    strSql.Append($" AND PlanParameterSetId = @PlanParameterSetId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@PlanParameterSetId", param.PlanParameterSetId));
                }
                if (!param.UnitId.IsNullOrZero())
                {
                    strSql.Append($" AND UnitId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));

                }
            }
            strSql.Append(" GROUP BY VersionBaseId , VersionName ");
            var list = await this.BaseRepository().FindList<TextbookVersionBaseEntity>(strSql.ToString(), parameter.ToArray());
            return list.ToList();
        }
        #endregion
    }
}
