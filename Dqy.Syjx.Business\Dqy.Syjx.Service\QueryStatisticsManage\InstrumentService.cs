﻿using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.QueryStatisticsManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Enum.InstrumentManage;
using Dqy.Syjx.Model.Param.QueryStatisticsManage;
using Dqy.Syjx.Service.OrganizationManage;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Util.Tools;
using NetTaste;
using NPOI.SS.Formula.Functions;
using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.Data.Common;
using System.Data.SqlTypes;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dqy.Syjx.Service.QueryStatisticsManage
{
    public class InstrumentService: RepositoryFactory
    {

        private UserSubjectSetService userSubjectSetService = new UserSubjectSetService();


        /// <summary>
        /// 仪器明细查询列表
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<List<InstrumentEntity>> GetInstrumentList(InstrumentParam param, Pagination pagination)
        {
            StringBuilder sbSql = new StringBuilder();
            var parameter = new List<DbParameter>();
            
            sbSql.Append(@$" FROM  eq_SchoolInstrument AS A 
                                INNER JOIN  up_Unit AS U ON A.SchoolId = U.Id
                                INNER JOIN  up_UnitRelation AS UR ON U.Id = UR.ExtensionObjId AND UR.ExtensionType = {UnitRelationTypeEnum.Unit.ParseToInt()}
						        INNER JOIN  up_UnitRelation AS UR2 ON UR.UnitId = UR2.ExtensionObjId AND UR2.ExtensionType = {UnitRelationTypeEnum.Unit.ParseToInt()}
						        INNER JOIN  up_Unit AS U2 ON UR.UnitId = U2.Id						            
				                INNER JOIN  eq_InstrumentStandard AS D ON A.InstrumentStandardId = D.Id
                                INNER JOIN  sys_static_dictionary AS SD ON D.VarietyAttribute = SD.DictionaryId AND SD.TypeCode = '1060' AND SD.BaseIsDelete = 0
                                LEFT JOIN  bn_FunRoom AS C ON A.FunRoomId = C.Id
                                LEFT JOIN  bn_Cupboard AS B ON A.CupboardId = B.Id                        
				                LEFT JOIN  SysDepartment AS E ON C.SysDepartmentId = E.Id
				                LEFT JOIN  SysUser AS F ON C.SafeguardUserId = F.Id
                                LEFT JOIN SysArea AS Area ON U2.AreaId = Area.AreaCode
                                WHERE A.BaseIsDelete = 0 AND A.StockNum > 0 ");
                              
            if (param != null)
            {
                sbSql.Append($" AND A.Statuz = {InstrumentInputStatuzEnum.AlreadyInputStorage.ParseToInt()}");
                if (param.UnitId > 0)
                {
                    sbSql.Append(" AND A.SchoolId = @UnitId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                if (param.CountyId > 0)
                {
                    sbSql.Append(" AND UR.UnitId = @CountyId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
                if (!param.CityId.IsNullOrZero())
                {
                    sbSql.Append(" AND UR2.UnitId = @CityId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CityId", param.CityId));
                }
                if (!string.IsNullOrEmpty(param.StartTime.ParseToString()))
                {
                    sbSql.Append(" AND PurchaseDate >= @StartTime ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@StartTime", param.StartTime));
                }
                if (!string.IsNullOrEmpty(param.EndTime.ParseToString()))
                {
                    param.EndTime = param.EndTime.Value.Date.Add(new TimeSpan(23, 59, 59));
                    sbSql.Append(" AND A.PurchaseDate <= @EndTime ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@EndTime", param.EndTime));
                }
                if (!param.Attribute.IsEmpty() && !param.Attribute.Equals("-1"))
                {
                    sbSql.Append(" AND D.VarietyAttribute = @VarietyAttribute");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@VarietyAttribute", param.Attribute));
                }
                if (param.StageId > 0)
                {
                    sbSql.Append($" AND A.StageId  LIKE '{param.StageId}'");
                }
                if (param.CourseId > -1)
                {
                    sbSql.Append(" AND A.CourseId = @CourseId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                //if (param.SysDepartmentId > -1)
                //{
                //    strSql.Append(" AND SysDepartmentId = @SysDepartmentId");
                //    parameter.Add(DbParameterExtension.CreateDbParameter("@SysDepartmentId", param.SysDepartmentId));
                //}
                if (!string.IsNullOrEmpty(param.SysDepartmentId))
                {
                    long sysdepartmentid = 0;
                    if (param.SysDepartmentId.IndexOf(',') > -1)
                    {
                        var temArr = param.SysDepartmentId.Split(',');
                        long.TryParse(temArr.Last(), out sysdepartmentid);
                    }
                    else
                    {
                        long.TryParse(param.SysDepartmentId, out sysdepartmentid);
                    }
                    if (sysdepartmentid > 0)
                    {
                        sbSql.Append(" AND C.SysDepartmentId = @SysDepartmentId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@SysDepartmentId", sysdepartmentid));
                    }
                }
                if (param.SysUserId > -1)
                {
                    sbSql.Append(" AND C.SafeguardUserId = @SysUserId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SysUserId", param.SysUserId));
                }
                if (!param.StoragePlace.IsEmpty() && !"-1".Equals(param.StoragePlace))
                {
                    if (param.StoragePlace.IndexOf(',') != -1)
                    {
                        sbSql.Append(" AND A.CupboardId = @CupboardId");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@CupboardId", param.StoragePlace.Split(',')[1]));
                    }
                    else
                    {
                        sbSql.Append(" AND A.FunRoomId = @FunRoomId");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@FunRoomId", param.StoragePlace));
                    }
                }
                if (param.IsSelfMade.HasValue && param.IsSelfMade > -1)
                {
                    sbSql.Append(" AND A.IsSelfMade = @IsSelfMade");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@IsSelfMade", param.IsSelfMade));
                }
                if (!string.IsNullOrEmpty(param.Name))
                {
                    sbSql.Append(" AND (A.OriginalCode like @Name OR A.Name LIKE @Name)");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Name", $"%{param.Name}%"));
                }
                if (param.IsUse > 0)
                {
                    sbSql.Append($" AND A.StockNum > 0 AND (D.VarietyAttribute = '{VarietyAttributeEnum.FixedAssets.ParseToInt()}' OR D.VarietyAttribute = '{VarietyAttributeEnum.LowValueDurable.ParseToInt()}' OR D.VarietyAttribute = '{VarietyAttributeEnum.IntangibleAssets.ParseToInt()}')");
                }
                
                if (param.SetUserId > 0)
                {
                    //判断用户是否设置了学段、学科
                    var listStage = userSubjectSetService.GetUserSetStageList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listStage.Count > 0)
                    {
                        string where = "";
                        int stageLength = listStage.Count;
                        int index = 1;
                        foreach (var stage in listStage)
                        {
                            where += $" A.StageId LIKE '{stage.DictionaryId}' ";
                            if (index != stageLength)
                            {
                                where += " OR ";
                            }
                            index++;
                        }
                        sbSql.Append($" AND ({where})");
                    }
                    var listCourse = userSubjectSetService.GetUserSetCourseList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listCourse.Count > 0)
                    {
                        string courseId = string.Join(",", listCourse.Select(f => f.DictionaryId));
                        sbSql.Append($" AND A.CourseId IN ({courseId})");
                    }
                }
            }
            IEnumerable<InstrumentEntity> list = null;

            var strSqlList = @"SELECT  A.Id ,
                                    A.BaseIsDelete ,A.BaseCreateTime ,A.BaseModifyTime ,A.BaseCreatorId ,A.BaseModifierId ,A.BaseVersion ,
                                    A.InstrumentStandardId ,A.Name ,A.Code ,A.ModelStandardId ,A.Model ,A.UnitName ,A.InputNum 
                                    ,A.StockNum, A.LendNum,A.Num
                                    ,A.Price ,
                                    A.Stage ,A.StageId ,A.Course ,A.CourseId ,A.PurchaseDate ,A.WarrantyMonth ,A.SupplierName ,A.Brand ,
                                    A.Statuz ,A.SchoolId ,A.InputType ,A.CupboardId ,A.Floor ,A.SourceSchoolInstrumentId ,A.IsSelfMade ,
                                    ISNULL(C.Name,'') AS FunRoom ,ISNULL(B.Name ,'') AS Cupboard ,ISNULL(B.FunRoomId ,0) AS FunRoomId,
				                    ISNULL(D.VarietyAttribute,'') AS VarietyAttribute,ISNULL(E.DepartmentName,'') AS DepartmentName,
				                    ISNULL(F.RealName,'') AS RealName,ISNULL(F.UserName,'') AS UserName
                                    ,C.SysDepartmentId,C.SafeguardUserId AS SysUserId,UR.UnitId AS CountyId,U.Name AS SchoolName ,SD.DicName AS VarietyAttributeName ,U.Sort ,D.IsDangerChemical ,
						            UR2.UnitId AS CityId ,Area.AreaName,U2.Sort AS CountySort,A.OriginalCode ";


            list = await this.BaseRepository().FindList<InstrumentEntity>(strSqlList + sbSql.ToString(), parameter.ToArray(), pagination);
            list.ForEach(f => { f.Num = f.StockNum + f.LendNum; f.SumMoney = (f.StockNum + f.LendNum) * f.Price; });

            if(pagination.TotalNum == -9999 || pagination.TotalSum == -9999)
            {
                var strSqlTotal = @"SELECT SUM(StockNum) AS StockNum, SUM(LendNum) AS LendNum, SUM(StockNum * Price) AS StockSum, SUM(LendNum * Price) AS LendSum";
                var listTotal = await this.BaseRepository().FindList<InstrumentEntity>(strSqlTotal + sbSql.ToString(), parameter.ToArray());
                if(listTotal.Count() > 0)
                {
                    var totalData = listTotal.FirstOrDefault();
                    pagination.TotalNum = totalData.StockNum + totalData.LendNum;
                    pagination.TotalSum = totalData.StockSum + totalData.LendSum;
                }
                else
                {
                    pagination.TotalNum = 0;
                    pagination.TotalSum = 0;
                }
            }

            //}
            //else
            //{
            //    list = await this.BaseRepository().FindList<InstrumentEntity>(strSql.ToString(), parameter.ToArray());
            //}
          
            return list.ToList();
        }

        /// <summary>
        /// 获取仪器汇总统计数据
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        public async Task<List<InstrumentEntity>> GetSummaryStatistic(InstrumentParam param, Pagination pagination,int type=1)
        {
            StringBuilder strSql = new StringBuilder();
            var parameter = new List<DbParameter>();
            strSql.Append(@$"SELECT  SchoolId,Sort,SchoolName,Course,Name,Code,Model,UnitName,Sum(StockNum) as StockNum,Sum(LendNum) as LendNum  FROM 
                             (SELECT A.Id , A.Name ,A.Model ,A.UnitName 
                                    ,A.StockNum, A.LendNum,A.Num,                                  
                                    A.StageId ,A.Course ,A.CourseId ,A.PurchaseDate ,
                                    A.Statuz ,A.SchoolId , A.IsSelfMade 
                                   ,A.FunRoomId,A.Code,
				                    ISNULL(D.VarietyAttribute,'') AS VarietyAttribute,ISNULL(E.DepartmentName,'') AS DepartmentName,
				                    ISNULL(F.RealName,'') AS RealName,ISNULL(F.UserName,'') AS UserName
                                    ,C.SysDepartmentId,F.Id AS SysUserId,UR.UnitId AS CountyId,U.Name AS SchoolName ,SD.DicName AS VarietyAttributeName ,U.Sort ,D.IsDangerChemical ,
						            UR2.UnitId AS CityId ,
									
									U2.Sort AS CountySort,A.OriginalCode
				                    FROM  eq_SchoolInstrument AS A 
                                    INNER JOIN  up_Unit AS U ON A.SchoolId = U.Id
                                    INNER JOIN  up_UnitRelation AS UR ON U.Id = UR.ExtensionObjId AND UR.ExtensionType = {UnitRelationTypeEnum.Unit.ParseToInt()}
						            INNER JOIN  up_UnitRelation AS UR2 ON UR.UnitId = UR2.ExtensionObjId AND UR2.ExtensionType = {UnitRelationTypeEnum.Unit.ParseToInt()}
						            INNER JOIN  up_Unit AS U2 ON UR.UnitId = U2.Id
						      
				                    INNER JOIN  eq_InstrumentStandard AS D ON A.InstrumentStandardId = D.Id
                                    INNER JOIN  sys_static_dictionary AS SD ON D.VarietyAttribute = SD.DictionaryId AND SD.TypeCode = '1060' AND SD.BaseIsDelete = 0
                                    LEFT JOIN  bn_FunRoom AS C ON A.FunRoomId = C.Id
                                    LEFT JOIN  bn_Cupboard AS B ON A.CupboardId = B.Id                        
				                    LEFT JOIN  SysDepartment AS E ON C.SysDepartmentId = E.Id
				                    LEFT JOIN  SysUser AS F ON C.SafeguardUserId = F.Id
                                    WHERE A.BaseIsDelete = 0
                        ) AS T WHERE 1 = 1 AND StockNum > 0 ");

            if (param != null)
            {
                strSql.Append($" AND Statuz = {InstrumentInputStatuzEnum.AlreadyInputStorage.ParseToInt()}");
                if (param.UnitId > 0)
                {
                    strSql.Append(" AND SchoolId = @UnitId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                if (param.CountyId > 0)
                {
                    strSql.Append(" AND CountyId = @CountyId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
                if (!param.CityId.IsNullOrZero())
                {
                    strSql.Append(" AND CityId = @CityId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CityId", param.CityId));
                }
                if (!string.IsNullOrEmpty(param.StartTime.ParseToString()))
                {
                    strSql.Append(" AND PurchaseDate >= @StartTime ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@StartTime", param.StartTime));
                }
                if (!string.IsNullOrEmpty(param.EndTime.ParseToString()))
                {
                    param.EndTime = param.EndTime.Value.Date.Add(new TimeSpan(23, 59, 59));
                    strSql.Append(" AND PurchaseDate <= @EndTime ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@EndTime", param.EndTime));
                }
                if (!param.Attribute.IsEmpty() && !param.Attribute.Equals("-1"))
                {
                    strSql.Append(" AND VarietyAttribute = @VarietyAttribute");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@VarietyAttribute", param.Attribute));
                }
                if (param.StageId > 0)
                {
                    strSql.Append($" AND StageId  LIKE '{param.StageId}'");
                }
                if (param.CourseId > -1)
                {
                    strSql.Append(" AND CourseId = @CourseId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (!string.IsNullOrEmpty(param.SysDepartmentId))
                {
                    long sysdepartmentid = 0;
                    if (param.SysDepartmentId.IndexOf(',') > -1)
                    {
                        var temArr = param.SysDepartmentId.Split(',');
                        long.TryParse(temArr.Last(), out sysdepartmentid);
                    }
                    else
                    {
                        long.TryParse(param.SysDepartmentId, out sysdepartmentid);
                    }
                    if (sysdepartmentid > 0)
                    {
                        strSql.Append(" AND SysDepartmentId = @SysDepartmentId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@SysDepartmentId", sysdepartmentid));
                    }
                }
                if (param.SysUserId > -1)
                {
                    strSql.Append(" AND SysUserId = @SysUserId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SysUserId", param.SysUserId));
                }
                if (!param.StoragePlace.IsEmpty() && !"-1".Equals(param.StoragePlace))
                {
                    if (param.StoragePlace.IndexOf(',') != -1)
                    {
                        strSql.Append(" AND CupboardId = @CupboardId");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@CupboardId", param.StoragePlace.Split(',')[1]));
                    }
                    else
                    {
                        strSql.Append(" AND FunRoomId = @FunRoomId");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@FunRoomId", param.StoragePlace));
                    }
                }
                if (param.IsSelfMade.HasValue && param.IsSelfMade > -1)
                {
                    strSql.Append(" AND IsSelfMade = @IsSelfMade");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@IsSelfMade", param.IsSelfMade));
                }
                if (!string.IsNullOrEmpty(param.Name))
                {
                    strSql.Append(" AND (OriginalCode like @Name OR Name LIKE @Name)");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Name", $"%{param.Name}%"));
                }
                if (param.IsUse > 0)
                {
                    strSql.Append(" AND StockNum > 0 AND VarietyAttribute <> '1060003' AND VarietyAttribute <> '1060005'");
                }

                if (param.SetUserId > 0)
                {
                    //判断用户是否设置了学段、学科
                    var listStage = userSubjectSetService.GetUserSetStageList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listStage.Count > 0)
                    {
                        string where = "";
                        int stageLength = listStage.Count;
                        int index = 1;
                        foreach (var stage in listStage)
                        {
                            where += $" StageId LIKE '{stage.DictionaryId}' ";
                            if (index != stageLength)
                            {
                                where += " OR ";
                            }
                            index++;
                        }
                        strSql.Append($" AND ({where})");
                    }
                    var listCourse = userSubjectSetService.GetUserSetCourseList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listCourse.Count > 0)
                    {
                        string courseId = string.Join(",", listCourse.Select(f => f.DictionaryId));
                        strSql.Append($" AND CourseId IN ({courseId})");
                    }
                }
            }

            strSql.Append("   group by SchoolName,Course,Code,Name,Model,UnitName,SchoolId,Sort ");

            IEnumerable<InstrumentEntity> list = null;
            if (type == 1)
            {
                list = await this.BaseRepository().FindList<InstrumentEntity>(strSql.ToString(), parameter.ToArray(), pagination);
            }
            else
            {
                list = await this.BaseRepository().FindList<InstrumentEntity>(strSql.ToString(), parameter.ToArray());
            }
            list.ForEach(f => { f.Num = f.StockNum + f.LendNum; });
            return list.ToList();
        }



        public async Task<List<InstrumentEntity>> GetInstrumentStatList(InstrumentParam param, Pagination pagination, string funSql = "*")
        {
            StringBuilder strSql = new StringBuilder();
            var parameter = new List<DbParameter>();
            strSql.Append(@$"SELECT {funSql} FROM 
                             (SELECT  A.Id ,
                                    A.BaseIsDelete ,A.BaseCreateTime ,A.BaseModifyTime ,A.BaseCreatorId ,A.BaseModifierId ,A.BaseVersion ,
                                    A.InstrumentStandardId ,A.Name ,A.Code ,A.ModelStandardId ,A.Model ,A.UnitName ,A.InputNum 
                                    ,A.StockNum, A.LendNum,A.Num
                                    ,A.Price ,
                                    A.Stage ,A.StageId ,A.Course ,A.CourseId ,A.PurchaseDate ,A.WarrantyMonth ,A.SupplierName ,A.Brand ,
                                    A.Statuz ,A.SchoolId ,A.InputType ,A.CupboardId ,A.Floor ,A.SourceSchoolInstrumentId ,A.IsSelfMade ,
                                    ISNULL(C.Name,'') AS FunRoom ,ISNULL(B.Name ,'') AS Cupboard ,ISNULL(B.FunRoomId ,0) AS FunRoomId,
				                    ISNULL(D.VarietyAttribute,'') AS VarietyAttribute,ISNULL(E.DepartmentName,'') AS DepartmentName,
				                    ISNULL(F.RealName,'') AS RealName,ISNULL(F.UserName,'') AS UserName
                                    ,C.SysDepartmentId,F.Id AS SysUserId,UR.UnitId AS CountyId,U.Name AS SchoolName ,SD.DicName AS VarietyAttributeName ,U.Sort ,D.IsDangerChemical ,
						            UR2.UnitId AS CityId ,AR.AreaName ,U2.Sort AS CountySort
				                    FROM  eq_SchoolInstrument AS A 
                                    INNER JOIN  up_Unit AS U ON A.SchoolId = U.Id
                                    INNER JOIN  up_UnitRelation AS UR ON U.Id = UR.ExtensionObjId AND UR.ExtensionType = {UnitRelationTypeEnum.Unit.ParseToInt()}
						            INNER JOIN  up_UnitRelation AS UR2 ON UR.UnitId = UR2.ExtensionObjId AND UR2.ExtensionType = {UnitRelationTypeEnum.Unit.ParseToInt()}
						            INNER JOIN  up_Unit AS U2 ON UR.UnitId = U2.Id
						            INNER JOIN  SysArea AS AR ON U2.AreaId = AR.AreaCode
				                    INNER JOIN  eq_InstrumentStandard AS D ON A.InstrumentStandardId = D.Id
                                    INNER JOIN  sys_static_dictionary AS SD ON D.VarietyAttribute = SD.DictionaryId AND SD.TypeCode = '1060' AND SD.BaseIsDelete = 0
                                    LEFT JOIN  bn_FunRoom AS C ON A.FunRoomId = C.Id
                                    LEFT JOIN  bn_Cupboard AS B ON A.CupboardId = B.Id                        
				                    LEFT JOIN  SysDepartment AS E ON C.SysDepartmentId = E.Id
				                    LEFT JOIN  SysUser AS F ON C.SafeguardUserId = F.Id
                                    WHERE A.BaseIsDelete = 0
   ) as                 T WHERE 1 = 1 AND StockNum > 0 ");
            if (param != null)
            {
                strSql.Append($" AND Statuz = {InstrumentInputStatuzEnum.AlreadyInputStorage.ParseToInt()}");
                if (param.UnitId > 0)
                {
                    strSql.Append(" AND SchoolId = @UnitId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                if (param.CountyId > 0)
                {
                    strSql.Append(" AND CountyId = @CountyId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
                if (!param.CityId.IsNullOrZero())
                {
                    strSql.Append(" AND CityId = @CityId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CityId", param.CityId));
                }
                if (!string.IsNullOrEmpty(param.StartTime.ParseToString()))
                {
                    strSql.Append(" AND PurchaseDate >= @StartTime ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@StartTime", param.StartTime));
                }
                if (!string.IsNullOrEmpty(param.EndTime.ParseToString()))
                {
                    param.EndTime = param.EndTime.Value.Date.Add(new TimeSpan(23, 59, 59));
                    strSql.Append(" AND PurchaseDate <= @EndTime ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@EndTime", param.EndTime));
                }
                if (!param.Attribute.IsEmpty() && !param.Attribute.Equals("-1"))
                {
                    strSql.Append(" AND VarietyAttribute = @VarietyAttribute");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@VarietyAttribute", param.Attribute));
                }
                if (param.StageId > 0)
                {
                    strSql.Append($" AND StageId  LIKE '{param.StageId}'");
                }
                if (param.CourseId > -1)
                {
                    strSql.Append(" AND CourseId = @CourseId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (!string.IsNullOrEmpty(param.SysDepartmentId))
                {
                    long sysdepartmentid = 0;
                    if (param.SysDepartmentId.IndexOf(',') > -1)
                    {
                        var temArr = param.SysDepartmentId.Split(',');
                        long.TryParse(temArr.Last(), out sysdepartmentid);
                    }
                    else
                    {
                        long.TryParse(param.SysDepartmentId, out sysdepartmentid);
                    }
                    if (sysdepartmentid > 0)
                    {
                        strSql.Append(" AND SysDepartmentId = @SysDepartmentId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@SysDepartmentId", sysdepartmentid));
                    }
                }
                if (param.SysUserId > -1)
                {
                    strSql.Append(" AND SysUserId = @SysUserId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SysUserId", param.SysUserId));
                }
                if (!param.StoragePlace.IsEmpty() && !"-1".Equals(param.StoragePlace))
                {
                    if (param.StoragePlace.IndexOf(',') != -1)
                    {
                        strSql.Append(" AND CupboardId = @CupboardId");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@CupboardId", param.StoragePlace.Split(',')[1]));
                    }
                    else
                    {
                        strSql.Append(" AND FunRoomId = @FunRoomId");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@FunRoomId", param.StoragePlace));
                    }
                }
                if (param.IsSelfMade.HasValue && param.IsSelfMade > -1)
                {
                    strSql.Append(" AND IsSelfMade = @IsSelfMade");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@IsSelfMade", param.IsSelfMade));
                }
                if (!string.IsNullOrEmpty(param.Name))
                {
                    strSql.Append(" AND (Code like @Name OR Name LIKE @Name)");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Name", $"%{param.Name}%"));
                }
                if (param.IsUse > 0)
                {
                    strSql.Append(" AND StockNum > 0 AND VarietyAttribute <> '1060003' AND VarietyAttribute <> '1060005'");
                }

                if (param.SetUserId > 0)
                {
                    //判断用户是否设置了学段、学科
                    var listStage = userSubjectSetService.GetUserSetStageList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listStage.Count > 0)
                    {
                        string where = "";
                        int stageLength = listStage.Count;
                        int index = 1;
                        foreach (var stage in listStage)
                        {
                            where += $" StageId LIKE '{stage.DictionaryId}' ";
                            if (index != stageLength)
                            {
                                where += " OR ";
                            }
                            index++;
                        }
                        strSql.Append($" AND ({where})");
                    }
                    var listCourse = userSubjectSetService.GetUserSetCourseList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listCourse.Count > 0)
                    {
                        string courseId = string.Join(",", listCourse.Select(f => f.DictionaryId));
                        strSql.Append($" AND CourseId IN ({courseId})");
                    }
                }
            }
            IEnumerable<InstrumentEntity> list = null;
            if ("*".Equals(funSql))
            {
                list = await this.BaseRepository().FindList<InstrumentEntity>(strSql.ToString(), parameter.ToArray(), pagination);
            }
            else
            {
                list = await this.BaseRepository().FindList<InstrumentEntity>(strSql.ToString(), parameter.ToArray());
            }
            list.ForEach(f => { f.SumMoney = f.Num * f.Price; });
            return list.ToList();
        }

        /// <summary>
        /// 根据Id获取仪器信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<InstrumentEntity> GetInstrumentById(long id)
        {
            StringBuilder strSql = new StringBuilder();
            var parameter = new List<DbParameter>();
            strSql.Append(@"SELECT * FROM 
                             (SELECT  A.Id ,
                        A.BaseIsDelete ,A.BaseCreateTime ,A.BaseModifyTime ,A.BaseCreatorId ,A.BaseModifierId ,A.BaseVersion ,
                        A.InstrumentStandardId ,A.Name ,A.Code ,A.ModelStandardId ,A.Model ,A.UnitName ,A.InputNum ,A.StockNum,A.LendNum,A.Price ,
                        A.Stage ,A.StageId ,A.Course ,A.CourseId ,A.PurchaseDate ,A.WarrantyMonth ,A.SupplierName ,A.Brand ,
                        A.Statuz ,A.SchoolId ,A.InputType ,A.CupboardId ,A.Floor ,A.SourceSchoolInstrumentId ,
                        ISNULL(C.Name,'') AS FunRoom ,ISNULL(B.Name ,'') AS Cupboard ,ISNULL(A.FunRoomId ,0) AS FunRoomId,
				        ISNULL(D.VarietyAttribute,'') AS VarietyAttribute,ISNULL(E.DepartmentName,'') AS DepartmentName,
				        ISNULL(F.RealName,'') AS RealName,ISNULL(F.UserName,'') AS UserName,
                        C.SysDepartmentId,C.SysUserId  ,SD.DicName AS VarietyAttributeName
				        FROM  eq_SchoolInstrument AS A 
				        INNER JOIN  eq_InstrumentStandard AS D ON A.InstrumentStandardId = D.Id
                        INNER JOIN  sys_static_dictionary AS SD ON D.VarietyAttribute = SD.DictionaryId AND SD.TypeCode = '1060' AND SD.BaseIsDelete = 0
                        LEFT JOIN  bn_Cupboard AS B ON A.CupboardId = B.Id
                        LEFT JOIN  bn_FunRoom AS C ON A.FunRoomId = C.Id
				        LEFT JOIN  SysDepartment AS E ON C.SysDepartmentId = E.Id
				        LEFT JOIN  SysUser AS F ON C.SafeguardUserId = F.Id
                        WHERE A.BaseIsDelete = 0
                        ) as  T ");
            strSql.Append($" WHERE Id = {id} AND BaseIsDelete = 0");
            var list = await this.BaseRepository().FindList<InstrumentEntity>(strSql.ToString());
            return list.FirstOrDefault();
        }

        public async Task<InstrumentEntity> GetStatInstrumentById(long id)
        {
            StringBuilder strSql = new StringBuilder();
            var parameter = new List<DbParameter>();
            strSql.Append(@"SELECT * FROM 
                             (SELECT  A.Id ,
                        A.BaseIsDelete ,A.BaseCreateTime ,A.BaseModifyTime ,A.BaseCreatorId ,A.BaseModifierId ,A.BaseVersion ,
                        A.InstrumentStandardId ,A.Name ,A.Code ,A.ModelStandardId ,A.Model ,A.UnitName ,A.InputNum ,A.Num,A.StockNum,A.LendNum ,A.Price ,
                        A.Stage ,A.StageId ,A.Course ,A.CourseId ,A.PurchaseDate ,A.WarrantyMonth ,A.SupplierName ,A.Brand ,
                        A.Statuz ,A.SchoolId ,A.InputType ,A.CupboardId ,A.Floor ,A.SourceSchoolInstrumentId ,
                        ISNULL(C.Name,'') AS FunRoom ,ISNULL(B.Name ,'') AS Cupboard ,ISNULL(A.FunRoomId ,0) AS FunRoomId,
				        ISNULL(D.VarietyAttribute,'') AS VarietyAttribute,ISNULL(E.DepartmentName,'') AS DepartmentName,
				        ISNULL(F.RealName,'') AS RealName,ISNULL(F.UserName,'') AS UserName,(A.Num * A.Price) AS SumMoney,
                        C.SysDepartmentId,C.SysUserId  ,SD.DicName AS VarietyAttributeName
				        FROM  eq_SchoolInstrument AS A 
				        INNER JOIN  eq_InstrumentStandard AS D ON A.InstrumentStandardId = D.Id
                        INNER JOIN  sys_static_dictionary AS SD ON D.VarietyAttribute = SD.DictionaryId AND SD.TypeCode = '1060' AND SD.BaseIsDelete = 0
                        LEFT JOIN  bn_Cupboard AS B ON A.CupboardId = B.Id
                        LEFT JOIN  bn_FunRoom AS C ON A.FunRoomId = C.Id
				        LEFT JOIN  SysDepartment AS E ON C.SysDepartmentId = E.Id
				        LEFT JOIN  SysUser AS F ON C.SafeguardUserId = F.Id
                        WHERE A.BaseIsDelete = 0
                        ) as  T ");
            strSql.Append($" WHERE Id = {id} AND BaseIsDelete = 0");
            var list = await this.BaseRepository().FindList<InstrumentEntity>(strSql.ToString());
            return list.FirstOrDefault();
        }


        /// <summary>
        /// 安全保障列表
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<List<SafeGuaranteeEntity>> GetSafeGuaranteeList(SafeGuaranteeParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            var parameter = new List<DbParameter>();
            strSql.Append(" SELECT * FROM (SELECT U.Id,U.Name,UR.UnitId AS CountyId,ISNULL(BFC.Id,0) AS BaseFieldConfigId,ISNULL(TotalEdu,0) AS  TotalEdu,ISNULL(TotalPlan,0) AS TotalPlan ,ISNULL(LastEduTime,'') AS LastEduTime ,ISNULL(LastPlanTime,'') AS LastPlanTime");
            strSql.Append(" FROM  up_Unit AS U");
            strSql.AppendFormat(" INNER JOIN  up_UnitRelation AS UR ON U.Id = UR.ExtensionObjId AND UR.ExtensionType = {0}", UnitRelationTypeEnum.Unit.ParseToInt());
            strSql.Append(" LEFT JOIN  bn_BaseFieldConfig AS BFC ON U.Id = BFC.UnitId");
            strSql.Append(" LEFT JOIN (");
            strSql.Append(" SELECT UnitId,COUNT(Id) AS TotalEdu ,MAX(EffectiveDate) AS LastEduTime FROM  bn_TrainSafeEducation WHERE BaseIsDelete = 0");
            if (param.Year > 0)
            {
                strSql.AppendFormat(" AND YEAR(EffectiveDate) = {0}", param.Year);
            }
            strSql.Append(" GROUP BY UnitId) AS A ON U.Id = A.UnitId");
            strSql.Append(" LEFT JOIN (");
            strSql.Append(" SELECT UnitId,COUNT(Id) AS TotalPlan ,MAX(EffectiveDate) AS LastPlanTime FROM  bn_EmergencyPlan WHERE BaseIsDelete = 0");
            if (param.Year > 0)
            {
                strSql.AppendFormat(" AND YEAR(EffectiveDate) = {0}", param.Year);
            }
            strSql.Append(" GROUP BY UnitId) AS B ON U.Id = B.UnitId) C WHERE 1 = 1");
            if (param.UnitId > 0)
            {
                strSql.AppendFormat(" AND Id = {0}", param.UnitId);
            }
            if (param.CountyId > 0)
            {
                strSql.AppendFormat(" AND CountyId = {0}", param.CountyId);
            }
            var list = await this.BaseRepository().FindList<SafeGuaranteeEntity>(strSql.ToString(), parameter.ToArray(), pagination);
            return list.ToList();
        }


        /// <summary>
        /// 减少仪器统计列表
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<List<ReduceInstrumentEntity>> GetReduceList(ReduceParam param, Pagination pagination)
        {
            var parameter = new List<DbParameter>();
            int beginNumber = (pagination.PageIndex - 1) * pagination.PageSize;
            int endNumber = pagination.PageIndex * pagination.PageSize;
            if (pagination.Sort.ToUpper().IndexOf("ASC") > -1 || pagination.Sort.ToUpper().IndexOf("DESC") > -1)
            {
                pagination.SortType = "";
            }
            string sql = "";
            if (GlobalContext.SystemConfig.DBProvider.Equals("Dm") || GlobalContext.SystemConfig.DBProvider.Equals("MySql"))
            {
                sql += " SELECT ";
            }
            else
            {
                sql += $" SELECT    ROW_NUMBER() OVER ( ORDER BY {pagination.Sort} {pagination.SortType}) AS ROWNUM ,";
            }
            sql += $@" * FROM 
                            (SELECT  A.Id,A.BaseCreatorId AS UserId,
	                                A.SchoolId ,A.OutNum AS Num,A.OutDate AS RegData,
                                    B.Name ,B.Model ,B.UnitName ,B.Price, 
                                    B.Course,SD.DicName AS VarietyAttributeName,
                                    ISNULL(E.DepartmentName,'') AS DepartmentName,
                                    ISNULL(F.RealName,'') AS RealName,
                                    ISNULL(D.VarietyAttribute,'') AS VarietyAttribute,
                                    C.SysDepartmentId,F.Id AS SysUserId,B.CourseId,
                                    U1.Name AS SchoolName,U2.Id AS CountyId,B.Code
                            FROM  eq_InstrumentOutList AS A
                            INNER JOIN  eq_SchoolInstrument AS B ON A.SchoolInstrumentId = B.Id
                            INNER JOIN  up_Unit AS U1 ON U1.Id = B.SchoolId
                            INNER JOIN  up_UnitRelation AS UR ON U1.Id = UR.ExtensionObjId AND UR.ExtensionType = 3
                            INNER JOIN  up_UnitRelation AS UR2 ON UR.UnitId = UR2.ExtensionObjId AND UR2.ExtensionType = 3
                            INNER JOIN  up_Unit AS U2 ON UR.UnitId = U2.Id
                            INNER JOIN  SysUser AS U ON A.BaseCreatorId = U.Id
                            INNER JOIN  eq_InstrumentStandard AS D ON B.InstrumentStandardId = D.Id
                            INNER JOIN  sys_static_dictionary AS SD ON D.VarietyAttribute = SD.DictionaryId AND SD.TypeCode = '1060' AND SD.BaseIsDelete = 0
                            LEFT JOIN  bn_FunRoom AS C ON B.FunRoomId = C.Id
                            LEFT JOIN  SysDepartment AS E ON C.SysDepartmentId = E.Id
		                    LEFT JOIN  SysUser AS F ON C.SafeguardUserId = F.Id
                            WHERE A.BaseIsDelete = 0 AND A.OutNum > 0
                            UNION 
                            SELECT  A.Id,A.BaseCreatorId AS UserId,
		                            A.SchoolId,A.ScrapNum AS Num,A.ScrapTime AS RegDate ,
		                            B.Name ,B.Model,B.UnitName ,B.Price,
                                    B.Course,SD.DicName AS VarietyAttributeName,
                                    ISNULL(E.DepartmentName,'') AS DepartmentName,
                                    ISNULL(F.RealName,'') AS RealName,
                                    ISNULL(D.VarietyAttribute,'') AS VarietyAttribute,
                                    C.SysDepartmentId,F.Id AS SysUserId,B.CourseId,
                                    U1.Name AS SchoolName,U2.Id AS CountyId,B.Code
                            FROM  eq_InstrumentScrap AS A
                            INNER JOIN  eq_SchoolInstrument AS B ON A.SchoolInstrumentId = B.Id
                            INNER JOIN  up_Unit AS U1 ON U1.Id = B.SchoolId
                            INNER JOIN  up_UnitRelation AS UR ON U1.Id = UR.ExtensionObjId AND UR.ExtensionType = 3
                            INNER JOIN  up_UnitRelation AS UR2 ON UR.UnitId = UR2.ExtensionObjId AND UR2.ExtensionType = 3
                            INNER JOIN  up_Unit AS U2 ON UR.UnitId = U2.Id
                            INNER JOIN  SysUser AS U ON A.BaseCreatorId = U.Id
                            INNER JOIN  eq_InstrumentStandard AS D ON B.InstrumentStandardId = D.Id
                            INNER JOIN  sys_static_dictionary AS SD ON D.VarietyAttribute = SD.DictionaryId AND SD.TypeCode = '1060' AND SD.BaseIsDelete = 0
                            LEFT JOIN  bn_FunRoom AS C ON B.FunRoomId = C.Id
                            LEFT JOIN  SysDepartment AS E ON C.SysDepartmentId = E.Id
		                    LEFT JOIN  SysUser AS F ON C.SafeguardUserId = F.Id
                            WHERE A.BaseIsDelete = 0) A WHERE 1 = 1 ";

            if(param.SchoolId != null && param.SchoolId > 0)
            {
                sql += $" AND SchoolId = {param.SchoolId}";
            }
            if (param.CountyId!= null && param.CountyId > 0)
            {
                sql += $" AND CountyId = {param.CountyId}";
            }
            if (!string.IsNullOrEmpty(param.Name))
            {
                sql += $" AND Name LIKE '%{param.Name}%'";
            }
            if (!string.IsNullOrEmpty(param.StartTime.ParseToString()))
            {
                sql += $" AND RegData >= '{param.StartTime}' ";
            }
            if (!string.IsNullOrEmpty(param.EndTime.ParseToString()))
            {
                param.EndTime = param.EndTime.Value.Date.Add(new TimeSpan(23, 59, 59));
                sql += $"  AND RegData <= '{param.EndTime}' ";
            }

            if (!param.Attribute.IsEmpty() && !param.Attribute.Equals("-1"))
            {
                sql += $" AND VarietyAttribute = {param.Attribute}";
            }
            if (param.CourseId > -1)
            {
                sql += $" AND CourseId = {param.CourseId}";
            }
            if (!string.IsNullOrEmpty(param.SysDepartmentId))
            {
                long sysdepartmentid = 0;
                if (param.SysDepartmentId.IndexOf(',') > -1)
                {
                    var temArr = param.SysDepartmentId.Split(',');
                    long.TryParse(temArr.Last(), out sysdepartmentid);
                }
                else
                {
                    long.TryParse(param.SysDepartmentId, out sysdepartmentid);
                }
                if (sysdepartmentid > 0)
                {
                    sql += $" AND SysDepartmentId = {sysdepartmentid}";
                }
            }
            if (param.SysUserId > -1)
            {
                sql += $" AND SysUserId = {param.SysUserId}";
            }


            StringBuilder searchSql = new StringBuilder();
            searchSql.Append("SELECT * FROM (");
            searchSql.Append(sql);
            if (GlobalContext.SystemConfig.DBProvider.Equals("Dm") || GlobalContext.SystemConfig.DBProvider.Equals("MySql"))
            {
                searchSql.AppendFormat(" ) N limit {0},{1}", beginNumber, pagination.PageSize);
            }
            else
            {
                searchSql.AppendFormat(" ) N WHERE ROWNUM > {0} AND ROWNUM <= {1}", beginNumber, endNumber);
            }
            var list = await this.BaseRepository().FindList<ReduceInstrumentEntity>(searchSql.ToString(), parameter.ToArray());

            var totalList = await this.BaseRepository().FindList<ReduceInstrumentEntity>(sql, parameter.ToArray());
            pagination.TotalCount = totalList.Count();

            ReduceInstrumentEntity entity = new ReduceInstrumentEntity();
            entity.Num = totalList.Sum(f => f.Num);
            entity.SumMoney = totalList.Sum(f => f.Num * f.Price);
            pagination.obj = entity;

            return list.ToList();
        }


        /// <summary>
        /// 区县查询仪器汇总清单
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<List<CountyInstrumentEntity>> GetCountyInstrumentStatistics(CountyInstrumentParam param, Pagination pagination)
        {
            StringBuilder sbSql = new StringBuilder();
            var parameter = new List<DbParameter>();
            IEnumerable<CountyInstrumentEntity> list = null;
            sbSql.Append(@"SELECT M.SchoolId AS Id,M.SchoolId,Sort,SchoolName,CountyId,ISNULL(AddNum,0) AS AddNum,ISNULL(OutNum,0) AS OutNum,(ISNULL(Num,0)-ISNULL(MinusNum,0)) AS Num  FROM 
                            (
	                            SELECT U.Id AS SchoolId,U.Sort,U.Name AS SchoolName,U2.Id AS CountyId,SUM(A.Num) AS AddNum
	                            FROM up_Unit AS U
	                            INNER JOIN  up_UnitRelation AS UR ON U.Id = UR.ExtensionObjId AND UR.ExtensionType = 3
	                            INNER JOIN  up_UnitRelation AS UR2 ON UR.UnitId = UR2.ExtensionObjId AND UR2.ExtensionType = 3
	                            INNER JOIN  up_Unit AS U2 ON UR.UnitId = U2.Id
	                            LEFT JOIN eq_SchoolInstrument AS A ON U.Id = A.SchoolId AND A.BaseIsDelete = 0 AND A.StockNum > 0 AND A.Statuz = 30
                            ");
            if (param.PlanYear > 0)
            {
                sbSql.Append($" AND YEAR(A.PurchaseDate) = {param.PlanYear}");
            }
            sbSql.Append(" GROUP BY U.Id,U.Sort,U.Name,U2.Id");
            sbSql.Append(" ) AS M ");

            sbSql.Append(@" LEFT JOIN
                            (
	                            SELECT SchoolId,SUM(OutNum) AS OutNum FROM
	                            (
		                            SELECT A.Id,A.SchoolId,A.OutNum 
		                            FROM eq_InstrumentOutList  AS A
		                            INNER JOIN  eq_SchoolInstrument AS B ON A.SchoolInstrumentId = B.Id
		                            WHERE A.BaseIsDelete = 0 AND A.OutNum > 0   
		                           ");
            if (param.PlanYear > 0)
            {
                sbSql.Append($" AND YEAR(A.OutDate) = {param.PlanYear}");
            }
            sbSql.Append(@$"  UNION 
		                            SELECT A.Id,A.SchoolId,A.ScrapNum AS OutNum
		                            FROM eq_InstrumentScrap AS A
		                            INNER JOIN  eq_SchoolInstrument AS B ON A.SchoolInstrumentId = B.Id
		                            WHERE A.BaseIsDelete = 0
	                           ");
            if (param.PlanYear > 0)
            {
                sbSql.Append($" AND YEAR(A.ScrapTime) = {param.PlanYear}");
            }
            sbSql.Append(@"  ) AS S
	
	                            GROUP BY S.SchoolId
                            ) AS N ON M.SchoolId = N.SchoolId");

            sbSql.Append(@" LEFT JOIN
	                    (
		                    SELECT SchoolId,SUM(Num) AS Num FROM eq_SchoolInstrument
		                    WHERE BaseIsDelete = 0 AND StockNum > 0 AND Statuz = 30 
		                   ");
            if (param.PlanYear > 0)
            {
                sbSql.Append($" AND PurchaseDate <= '{param.PlanYear}-12-31' ");
            }
            sbSql.Append(@" GROUP BY SchoolId
	                    ) AS G ON G.SchoolId = M.SchoolId ");

            sbSql.Append(@" LEFT JOIN
	                (
		                SELECT SchoolId,SUM(OutNum) AS MinusNum FROM
		                (
			                SELECT A.Id,A.SchoolId,A.OutNum 
			                FROM eq_InstrumentOutList  AS A
			                INNER JOIN  eq_SchoolInstrument AS B ON A.SchoolInstrumentId = B.Id
			                WHERE A.BaseIsDelete = 0 AND A.OutNum > 0   
			                    ");
            if (param.PlanYear > 0)
            {
                sbSql.Append($" AND A.OutDate <= '{param.PlanYear}-12-31'");
            }
            sbSql.Append(@" UNION 
			                SELECT A.Id,A.SchoolId,A.ScrapNum AS OutNum
			                FROM eq_InstrumentScrap AS A
			                INNER JOIN  eq_SchoolInstrument AS B ON A.SchoolInstrumentId = B.Id
			                WHERE A.BaseIsDelete = 0
		                    ");
            if (param.PlanYear > 0)
            {
                sbSql.Append($" AND A.ScrapTime <= '{param.PlanYear}-12-31'");
            }
            sbSql.Append(@"  ) AS S
	
		                GROUP BY S.SchoolId
	                ) AS F ON M.SchoolId = F.SchoolId ");

            sbSql.Append($"  WHERE M.CountyId = {param.UnitId} ");
            if (param.SchoolId != null && param.SchoolId.Value > 0)
            {
                sbSql.Append($" AND M.SchoolId = {param.SchoolId.Value}");
            }
            list = await this.BaseRepository().FindList<CountyInstrumentEntity>(sbSql.ToString(), parameter.ToArray(), pagination);
            //list.ForEach(f => { f.StockNum = f.StockNum + f.LendNum; });

            //统计
            StringBuilder sqlSum = new StringBuilder();
            sqlSum.Append(" SELECT SUM(Num) AS Num,SUM(AddNum) AS AddNum,SUM(OutNum) AS OutNum  FROM ");
            sqlSum.Append(" (");
            sqlSum.Append(sbSql.ToString());
            sqlSum.Append("  ) A ");
            var totalList = await this.BaseRepository().FindList<CountyInstrumentEntity>(sqlSum.ToString(), parameter.ToArray());

            if (totalList.Count() > 0)
            {
                var totalData = totalList.FirstOrDefault();
                CountyInstrumentEntity entity = new CountyInstrumentEntity();
                entity.AddNum = totalData.AddNum;
                entity.SchoolName = "总计：";
                entity.Num = totalData.Num;
                entity.OutNum = totalData.OutNum;
                pagination.obj = entity;
            }

            return list.ToList();

            //StringBuilder sbSql = new StringBuilder();
            //var parameter = new List<DbParameter>();
            //IEnumerable<CountyInstrumentEntity> list = null;
            //sbSql.Append(@$"SELECT U.Id AS SchoolId,U.Id,U.Sort,U.Name AS SchoolName,U2.Id AS CountyId,SUM(A.Num) AS Num,SUM(A.StockNum) AS StockNum,SUM(A.LendNum) AS LendNum,SUM(OutNum) AS OutNum
            //                FROM up_Unit AS U
            //                INNER JOIN  up_UnitRelation AS UR ON U.Id = UR.ExtensionObjId AND UR.ExtensionType = 3
            //                INNER JOIN  up_UnitRelation AS UR2 ON UR.UnitId = UR2.ExtensionObjId AND UR2.ExtensionType = 3
            //                INNER JOIN  up_Unit AS U2 ON UR.UnitId = U2.Id
            //                LEFT JOIN eq_SchoolInstrument AS A ON U.Id = A.SchoolId AND A.BaseIsDelete = 0 AND A.StockNum > 0 AND A.Statuz = 30 
            //                ");
            //if (param != null)
            //{
            //    if (param.PlanYear > 0)
            //    {
            //        sbSql.Append($" AND YEAR(A.PurchaseDate) = {param.PlanYear}");
            //    }
            //}

            //sbSql.Append($"  WHERE U2.Id = {param.UnitId} ");
            //if (param.SchoolId != null && param.SchoolId.Value > 0)
            //{
            //    sbSql.Append($" AND U.Id = {param.SchoolId.Value}");
            //}

            //sbSql.Append(" GROUP BY U.Id,U.Sort,U.Name,U2.Id ");
            //list = await this.BaseRepository().FindList<CountyInstrumentEntity>(sbSql.ToString(), parameter.ToArray(), pagination);
            //list.ForEach(f => { f.StockNum = f.StockNum + f.LendNum; });

            ////统计
            //StringBuilder sqlSum = new StringBuilder();
            //sqlSum.Append(" SELECT SUM(Num) AS Num,SUM(StockNum) AS StockNum,SUM(LendNum) AS LendNum,SUM(OutNum) AS OutNum  FROM ");
            //sqlSum.Append(" (");
            //sqlSum.Append(sbSql.ToString());
            //sqlSum.Append("  ) A ");
            //var totalList = await this.BaseRepository().FindList<CountyInstrumentEntity>(sqlSum.ToString(), parameter.ToArray());

            //if (totalList.Count() > 0)
            //{
            //    var totalData = totalList.FirstOrDefault();
            //    CountyInstrumentEntity entity = new CountyInstrumentEntity();
            //    entity.StockNum = totalData.StockNum + totalData.LendNum;
            //    entity.SchoolName = "总计：";
            //    entity.Num = totalData.Num;
            //    entity.OutNum = totalData.OutNum;
            //    pagination.obj = entity;
            //}

            //return list.ToList();
        }


        /// <summary>
        /// 区县待入库汇总清单
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<List<CountyInstrumentEntity>> GetCountyStorageStatistics(CountyInstrumentParam param, Pagination pagination)
        {
            StringBuilder sbSql = new StringBuilder();
            var parameter = new List<DbParameter>();
            IEnumerable<CountyInstrumentEntity> list = null;
            sbSql.Append(@$"SELECT U.Id AS SchoolId,U.Id,U.Name AS SchoolName,U.Sort,U2.Id AS CountyId,SUM(A.Num) AS Num 
                            FROM up_Unit AS U
                            INNER JOIN  up_UnitRelation AS UR ON U.Id = UR.ExtensionObjId AND UR.ExtensionType = 3
                            INNER JOIN  up_UnitRelation AS UR2 ON UR.UnitId = UR2.ExtensionObjId AND UR2.ExtensionType = 3
                            INNER JOIN  up_Unit AS U2 ON UR.UnitId = U2.Id
                            LEFT JOIN eq_SchoolInstrument AS A ON A.SchoolId = U.Id AND A.BaseIsDelete = 0 AND A.Statuz > 0 AND A.Statuz <> 30
                            ");

            sbSql.Append($"  WHERE U2.Id = {param.UnitId} ");
            if (param.SchoolId != null && param.SchoolId.Value > 0)
            {
                sbSql.Append($" AND U.Id = {param.SchoolId.Value}");
            }

            sbSql.Append(" GROUP BY U.Id,U.Name,U.Sort,U2.Id ");
            list = await this.BaseRepository().FindList<CountyInstrumentEntity>(sbSql.ToString(), parameter.ToArray(), pagination);

            //统计
            StringBuilder sqlSum = new StringBuilder();
            sqlSum.Append(" SELECT SUM(Num) AS Num  FROM ");
            sqlSum.Append(" (");
            sqlSum.Append(sbSql.ToString());
            sqlSum.Append("  ) A ");
            var totalList = await this.BaseRepository().FindList<CountyInstrumentEntity>(sqlSum.ToString(), parameter.ToArray());

            if (totalList.Count() > 0)
            {
                var totalData = totalList.FirstOrDefault();
                CountyInstrumentEntity entity = new CountyInstrumentEntity();
                entity.SchoolName = "总计：";
                entity.Num = totalData.Num;
                pagination.obj = entity;
            }
            return list.ToList();
        }
    }
}
