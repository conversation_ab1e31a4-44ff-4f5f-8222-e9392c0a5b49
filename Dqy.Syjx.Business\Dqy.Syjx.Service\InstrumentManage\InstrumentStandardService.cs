﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.InstrumentManage;
using Dqy.Syjx.Model.Param.InstrumentManage;
using System.Data;
using Dqy.Syjx.Model.Result;
using Dqy.Syjx.Entity.OrganizationManage;
using Dqy.Syjx.Model.Param.OrganizationManage;
using Dqy.Syjx.Model.Result.InstrumentManage;
using NPOI.SS.Formula.Functions;
using static NPOI.HSSF.Util.HSSFColor;

namespace Dqy.Syjx.Service.InstrumentManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-09-27 13:32
    /// 描 述：服务类
    /// </summary>
    public class InstrumentStandardService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<InstrumentStandardEntity>> GetList(InstrumentStandardListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.OrderBy(p => p.Sort).ToList();
        }

        public async Task<List<InstrumentStandardEntity>> GetPageList(InstrumentStandardListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<InstrumentStandardEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<InstrumentStandardEntity>(id);
        }

        public async Task<InstrumentStandardEntity> GetEntityByCode(string code)
        {
            return await this.BaseRepository().FindEntity<InstrumentStandardEntity>(f => f.Code == code);
        }

        /// <summary>
        /// 查询仪器分类的select2数据
        /// </summary>
        /// <param name="q"></param>
        /// <returns></returns>
        public async Task<IEnumerable<ComboBoxInfo>> GetSelectList(string q)
        {
            StringBuilder sql = new StringBuilder();
            //sql.Append("SELECT * FROM (");
            sql.Append("    SELECT a.Id ,");
            sql.Append("          (CASE a.Depth WHEN 3 THEN d.Name + '>' + c.Name + '>' + b.Name + '>' + a.Name");
            sql.Append("                        WHEN 2 THEN c.Name + '>' + b.Name + '>' + a.Name");
            sql.Append("                        WHEN 1 THEN b.Name + '>' + a.Name");
            sql.Append("                        WHEN 0 THEN a.Name END ) AS Name ");
            //sql.Append("          (CASE a.Depth WHEN 3 THEN d.Code + '>' + c.Code + '>' + b.Code + '>' + a.Code");
            //sql.Append("                        WHEN 2 THEN c.Code + '>' + b.Code + '>' + a.Code");
            //sql.Append("                        WHEN 1 THEN b.Code + '>' + a.Code");
            //sql.Append("                        WHEN 0 THEN a.Code END ) AS Code ,");
            //sql.Append("          (CASE a.Depth WHEN 3 THEN d.PinYin + '>' + c.PinYin + '>' + b.PinYin + '>' + a.PinYin");
            //sql.Append("                        WHEN 2 THEN c.PinYin + '>' + b.PinYin + '>' + a.PinYin");
            //sql.Append("                        WHEN 1 THEN b.PinYin + '>' + a.PinYin");
            //sql.Append("                        WHEN 0 THEN a.PinYin END ) AS PinYin");
            sql.Append("    FROM  eq_InstrumentStandard AS a");
            sql.Append("    INNER JOIN  eq_InstrumentStandard AS b ON a.Pid = b.Id");
            sql.Append("    LEFT JOIN  eq_InstrumentStandard AS c ON b.Pid = c.Id");
            sql.Append("    LEFT JOIN  eq_InstrumentStandard AS d ON c.Pid = d.Id");
            sql.Append("    WHERE a.IsLast = 1 AND a.ClassType = 1 AND a.Statuz = 1 AND a.BaseIsDelete = 0");
            sql.Append("    ORDER BY a.Sort ASC ,b.Sort ASC ,c.Sort ASC ,d.Sort ASC");
            //sql.Append(")T WHERE 1 = 1 ");
            //if (!string.IsNullOrWhiteSpace(q))
            //{
            //    var qArray = q.Split(' ');
            //    qArray.ToList().ForEach(f =>
            //    {
            //        if (q.IsNumber())
            //        {
            //            sql.AppendFormat(" AND Code LIKE '%{0}%'", q);
            //        }
            //        else if (q.IsZm())
            //        {
            //            sql.AppendFormat(" AND PinYin LIKE '%{0}%'", q);
            //        }
            //        else
            //        {
            //            sql.AppendFormat(" AND Name LIKE '%{0}%'", q);
            //        }
            //    });
            //}
            return await this.BaseRepository().FindList<ComboBoxInfo>(sql.ToString());
        }

        /// <summary>
        /// 根据仪器编码或名称查询三级仪器信息
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<List<InstrumentStandard>> GetInstrumentStandardList(InstrumentStandardListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            var parameter = new List<DbParameter>();
            strSql.Append(@"SELECT StandardOne.Id AS OneId,StandardOne.Name AS OneName, StandardTwo.Id AS TwoId,StandardTwo.Name AS TwoName,
	                               StandardThree.Id AS ThreeId,StandardThree.Name AS ThreeName,StandardFour.Id,StandardFour.Name,
	                               StandardFour.UnitName,StandardFour.Code,StandardFour.PinYin,StandardFour.Model
                            FROM  eq_InstrumentStandard AS StandardFour
                            INNER JOIN  eq_InstrumentStandard AS StandardThree ON StandardFour.Pid = StandardThree.Id
                            INNER JOIN  eq_InstrumentStandard AS StandardTwo ON StandardThree.Pid = StandardTwo.Id
                            INNER JOIN  eq_InstrumentStandard AS StandardOne ON StandardTwo.Pid = StandardOne.Id
                            WHERE StandardFour.IsLast = 1 AND StandardFour.BaseIsDelete = 0 AND StandardFour.Statuz = 1");
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Key))
                {
                    strSql.Append(" AND (StandardFour.Name LIKE @Key OR StandardFour.Code LIKE @Key)");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Key", $"%{param.Key}%"));
                }

            }
            var list = await this.BaseRepository().FindList<InstrumentStandard>(strSql.ToString(), parameter.ToArray(), pagination);
            return list.ToList();
        }


        /// <summary>
        /// 查询仪器及规格属性信息
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<List<InstrumentModel>> GetInstrumentModelList(InstrumentStandardListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            var parameter = new List<DbParameter>();
            strSql.Append(@"
                            SELECT A.Id,A.Id AS InstrumentStandardId,A.Name,ISNULL(B.Model,'') AS Model,
	                               (CASE WHEN B.Code IS NOT NULL THEN B.Code ELSE A.Code END) AS Code,
	                               (CASE WHEN B.UnitName IS NOT NULL THEN B.UnitName ELSE A.UnitName END) AS UnitName
                                    ,ISNULL(B.Id,0) AS ModelStandardId,A.Pid AS ThreeId,IS1.Pid AS TwoId,IS2.Pid AS OneId
                            FROM
                            (SELECT * FROM  eq_InstrumentStandard WHERE ClassType = 1 AND IsLast = 1) AS A
                            LEFT JOIN (SELECT Id,Pid,Code,Name,Model,UnitName FROM  eq_InstrumentStandard WHERE ClassType = 2) AS B ON A.Id = B.Pid
                            LEFT JOIN  eq_InstrumentStandard AS IS1 ON A.Pid = IS1.Id
                            LEFT JOIN  eq_InstrumentStandard AS IS2 ON IS1.Pid = IS2.Id
                            WHERE 1 = 1 ");

            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Code))
                {
                    strSql.Append(" AND ((CASE WHEN B.Code IS NOT NULL THEN B.Code ELSE A.Code END) LIKE @Code)");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Code", $"{param.Code}%"));
                }

                if (!string.IsNullOrEmpty(param.Name))
                {
                    strSql.Append(" AND A.Name LIKE @Name");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Name", $"%{param.Name}%"));
                }

                if(param.Pid > 0)
                {
                    strSql.Append($" AND (A.Pid = {param.Pid} OR IS1.Pid = {param.Pid} OR IS2.Pid = {param.Pid}) ");
                }
            }
            var list = await this.BaseRepository().FindList<InstrumentModel>(strSql.ToString(), parameter.ToArray(), pagination);
            return list.ToList();
        }

        /// <summary>
        /// 输入编码或名称查询数据
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<List<InstrumentModel>> GetShowList(InstrumentStandardListParam param)
        {
            StringBuilder strSql = new StringBuilder();
            var parameter = new List<DbParameter>();
            strSql.Append(@"
                            SELECT top 10 * FROM
                            (
	                            SELECT A.Id,A.Id AS InstrumentStandardId,A.Name,ISNULL(B.Model,'') AS Model,
		                               (CASE WHEN B.Code IS NOT NULL THEN B.Code ELSE A.Code END) AS Code,
		                               (CASE WHEN B.UnitName IS NOT NULL THEN B.UnitName ELSE A.UnitName END) AS UnitName
                                        ,ISNULL(B.Id,0) AS ModelStandardId
	                            FROM
	                            (SELECT * FROM  eq_InstrumentStandard WHERE ClassType = 1 AND IsLast = 1) AS A
	                            LEFT JOIN (SELECT Id,Pid,Code,Name,Model,UnitName FROM  eq_InstrumentStandard WHERE ClassType = 2) AS B ON A.Id = B.Pid
                            ) A WHERE 1 = 1 ");

            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Code))
                {
                    strSql.Append(" AND Code LIKE @Code");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Code", $"{param.Code}%"));
                }

                if (!string.IsNullOrEmpty(param.Name))
                {
                    strSql.Append(" AND Name LIKE @Name");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Name", $"%{param.Name}%"));
                }
            }
            var list = await this.BaseRepository().FindList<InstrumentModel>(strSql.ToString(), parameter.ToArray());
            return list.ToList();
        }

        /// <summary>
        /// 根据提交的编码集合获取传入正确的编码集合
        /// </summary>
        /// <param name="strCodes">编码集合逗号分隔</param>
        /// <returns></returns>
        public async Task<List<InstrumentModel>> GetCheckCodeList(string strCodes)
        {
            strCodes = StringFilter.ValidateAndCleanCodes(strCodes);
            if(strCodes == null)
                throw new ArgumentException("无效的参数: 只允许字母数字和逗号组合");

            // 使用参数化查询和跨数据库兼容的 IN 语法
            var codeList = strCodes.Split(',', StringSplitOptions.RemoveEmptyEntries);
            var parameters = new List<DbParameter>();
            var placeholders = new List<string>();

            for (int i = 0; i < codeList.Length; i++)
            {
                placeholders.Add($"@Code{i}");
                parameters.Add(DbParameterExtension.CreateDbParameter($"@Code{i}", codeList[i].Trim()));
            }

            string strSql = $@"
                SELECT Id,Code,Name,UnitName,ClassType,IsLast,Pid
                FROM eq_InstrumentStandard AS IStandard
                WHERE IStandard.Code IN ({string.Join(",", placeholders)})
                AND ((IStandard.ClassType = 1 AND IStandard.IsLast = 1) OR IStandard.ClassType = 2)";

            var list = await this.BaseRepository().FindList<InstrumentModel>(strSql, parameters.ToArray());
            return list.ToList();
        }

        /// <summary>
        /// 获取所有编码集合
        /// </summary>
        /// <returns></returns>
        public async Task<List<InstrumentModel>> GetCheckCodeList()
        {
            string sql = @"SELECT Id,Code,Name,UnitName,ClassType,IsLast,Pid  FROM  eq_InstrumentStandard WHERE (ClassType = 1 AND IsLast = 1 OR ClassType = 2)";
            var list = await this.BaseRepository().FindList<InstrumentModel>(sql);
            return list.ToList();
        }

        /// <summary>
        /// 获取选择Tab数据
        /// </summary>
        /// <returns></returns>
        public async Task<List<InstrumentStandardTab>> GetTabList()
        {
            StringBuilder strSql = new StringBuilder();
            var parameter = new List<DbParameter>();
            strSql.Append(@"SELECT Id,Pid,Name,Depth FROM  eq_InstrumentStandard WHERE ClassType = 1 AND Depth < 3");
            var list = await this.BaseRepository().FindList<InstrumentStandardTab>(strSql.ToString());
            return list.ToList();
            #endregion

        }

        #region 提交数据
            public async Task SaveForm(InstrumentStandardEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            long[] idArr = TextHelper.SplitToArray<long>(ids, ',');
            await this.BaseRepository().Delete<InstrumentStandardEntity>(idArr);
        }
        #endregion

        #region 私有方法
        private Expression<Func<InstrumentStandardEntity, bool>> ListFilter(InstrumentStandardListParam param)
        {
            var expression = LinqExtensions.True<InstrumentStandardEntity>();
            expression = expression.And(t => t.BaseIsDelete == 0);
            if (param != null)
            {
                if (!param.Pid.IsNullOrZero())
                {
                    expression = expression.And(t => t.Pid == param.Pid);
                }
                if (param.Depth == -1)
                {
                    expression = expression.And(t => t.Depth < 3);
                }
                else
                {
                    if (param.Depth.HasValue)
                    {
                        expression = expression.And(t => t.Depth == param.Depth);
                    }
                }
                if (!param.ClassType.IsNullOrZero())
                {
                    expression = expression.And(t => t.ClassType == param.ClassType);
                }
                if (param.IsLast.HasValue)
                {
                    expression = expression.And(t => t.IsLast == (param.IsLast.Value == 1));
                }
                if (param.Statuz.HasValue)
                {
                    expression = expression.And(t => t.Statuz == param.Statuz);
                }
                if (!param.Code.IsEmpty())
                {
                    expression = expression.And(t => t.Code.Contains(param.Code));
                }
                if (!param.Name.IsEmpty())
                {
                    expression = expression.And(t => t.Name.Contains(param.Name) || t.Model.Contains(param.Name));
                }

            }
            return expression;
        }
        #endregion
    }
}
