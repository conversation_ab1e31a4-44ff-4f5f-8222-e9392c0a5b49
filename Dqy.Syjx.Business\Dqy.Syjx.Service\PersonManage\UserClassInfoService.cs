﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.PersonManage;
using Dqy.Syjx.Model.Param.PersonManage;
using Dqy.Syjx.Enum;

namespace Dqy.Syjx.Service.PersonManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-10-15 11:39
    /// 描 述：服务类
    /// </summary>
    public class UserClassInfoService : RepositoryFactory
    {
        #region 获取数据
        public async Task<List<UserClassInfoEntity>> GetList(UserClassInfoListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<UserClassInfoEntity>> GetPa
        }

        public async Task<List<UserClassInfoEntity>> GetList(UserClassInfoListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<UserClassInfoEntity>> GetPageList(UserClassInfoListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }
        public async Task<UserClassInfoEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<UserClassInfoEntity>(id);
        }

        /// <summary>
        /// 获取非本人任课的某课程的班级
        /// </summary>
        /// <param name="unitId"></param>
        /// <param name="userId"></param>
        /// <param name="courseId"></param>
        /// <returns></returns>
        public async Task<List<UserClassInfoEntity>> GetOtherTeachClassInfoList(long unitId, long userId, int courseId, string classid = "")
        {
            string sql = $@"
                SELECT U.RealName as UserName ,UCT.ClassIdz
                FROM  up_UserClassInfo AS UCI
                INNER JOIN  SysUser AS U ON UCI.UserId = U.Id
                INNER JOIN  up_UserTeachClass AS UCT ON UCI.Id = UCT.UserClassInfoId AND UCT.BaseIsDelete = 0
                WHERE UCI.BaseIsDelete = 0 AND UCI.UnitId = {unitId} AND UCI.IsCurrentUnit = 1 AND UCT.CourseId = {courseId} AND UCI.UserId <> {userId}
            ";
            if (!string.IsNullOrWhiteSpace(classid))
            {
                sql += string.Format(" AND IsNull(UCT.ClassIdz,'') like '%{0}%' ", classid);
            }
            var list = await this.BaseRepository().FindList<UserClassInfoEntity>(sql);
            return list.ToList();
        }

        /// <summary>
        /// 获取非本人任课的某课程的班级      UserTeachClass
        /// </summary>
        /// <param name="unitId">单位Id</param>
        /// <param name="userId">用户Id</param>
        /// <param name="courseId">学科Id</param>
        /// <returns></returns>
        public async Task<List<UserTeachClassEntity>> GetTeachInfoList(long unitId, int courseid = 0, long userid = 0)
        {
            string sql = $@"
                SELECT UCI.Id , UCI.UserId , U.RealName as UserName , UCT.CourseId  ,UCT.ClassIdz ,UCT.GradeIdz ,IsNull(UCT.Id,0) AS UserTeachClassId
                FROM  up_UserClassInfo AS UCI
                INNER JOIN  SysUser AS U ON UCI.UserId = U.Id
                LEFT JOIN  up_UserTeachClass AS UCT ON UCI.Id = UCT.UserClassInfoId AND UCT.BaseIsDelete = 0
                WHERE UCI.BaseIsDelete = 0 AND UCI.UnitId = {unitId} AND UCI.IsCurrentUnit = 1 ";
            if (courseid > 0)
            {
                sql += $" AND UCT.CourseId = {courseid} ";
            }
            if (userid > 0)
            {
                sql += $" AND UCI.UserId = {userid} ";
            }
            var list = await this.BaseRepository().FindList<UserTeachClassEntity>(sql);
            return list.ToList();
        }
        #endregion

        #region 提交数据
        /// <summary>
        /// 修改是否当前单位
        /// </summary>
        /// <param name="unitId"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task UpdateIsIsCurrentUnit(long unitId, long userId)
        {
            string sql = $" update  up_UserClassInfo set IsCurrentUnit = {IsEnum.No.ParseToInt()} where IsCurrentUnit = {IsEnum.Yes.ParseToInt()} and UserId = {userId} and UnitId <> {unitId}";
            await this.BaseRepository().ExecuteBySql(sql);
        }

        public async Task SaveForm(UserClassInfoEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(UserClassInfoEntity entity, Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        /// <summary>
        /// 清除个人任课信息（理化生科学的任课信息不清除）
        /// </summary>
        /// <param name="unitId"></param>
        /// <param name="userId"></param>
        /// <param name="subjectArrStr"></param>
        /// <returns></returns>
        public async Task ClearForm(long unitId, long userId,string subjectArrStr)
        {
            string strSql = $@"
            UPDATE  up_UserTeachClass SET BaseIsDelete = 1 WHERE UserClassInfoId IN (SELECT id FROM  up_UserClassInfo WHERE BaseIsDelete = 0 AND UnitId = {unitId} AND UserId = {userId} AND IsCurrentUnit = 1) AND CourseId NOT IN (1005002,1005003,1005004,1005005);
            UPDATE  up_UserClassInfo SET SubjectIdz = '{subjectArrStr}' WHERE BaseIsDelete = 0 AND UnitId = {unitId} AND UserId = {userId} AND IsCurrentUnit = 1; ";
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<UserClassInfoEntity, bool>> ListFilter(UserClassInfoListParam param)
        {
            var expression = LinqExtensions.True<UserClassInfoEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (!param.UnitId.IsNullOrZero())
                {
                    expression = expression.And(t => t.UnitId == param.UnitId);
                }
                if (!param.UserId.IsNullOrZero())
                {
                    expression = expression.And(t => t.UserId == param.UserId);
                }
                if (param.IsCurrentUnit > -1)
                {
                    expression = expression.And(t => t.IsCurrentUnit == param.IsCurrentUnit);
                }
            }
            return expression;
        }

        #endregion
    }
}

