﻿using System;
using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations.Schema;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using System.ComponentModel;
using NPOI.SS.UserModel;

namespace Dqy.Syjx.Entity.ExperimentTeachManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-12-08 17:34
    /// 描 述：编制实验计划实体类
    /// </summary>
    [Table("ex_PlanInfo")]
    public class PlanInfoEntity : BaseExtensionEntity
    {
        /// <summary>
        /// 学校Id
        /// </summary>
        /// <returns></returns>
        public long SchoolId { get; set; }
        /// <summary>
        /// 年级（字典表sys_static_dictionary 字段：DictionaryId）
        /// </summary>
        /// <returns></returns>
        public int GradeId { get; set; }
        /// <summary>
        /// 学科
        /// </summary>
        /// <returns></returns>
        public int CourseId { get; set; }
        /// <summary>
        /// 学年起始年度
        /// </summary>
        /// <returns></returns>
        public int SchoolYearStart { get; set; }
        /// <summary>
        /// 学年终止年度
        /// </summary>
        /// <returns></returns>
        public int SchoolYearEnd { get; set; }
        /// <summary>
        /// 学期
        /// </summary>
        /// <returns></returns>
        public int SchoolTerm { get; set; }
        /// <summary>
        /// 实验计划名称
        /// </summary>
        /// <returns></returns>
        [Description("实验计划名称")]
        [ExportExcelAttribute(HorizontalAlignment.Left, 30)]
        public string PlanName { get; set; }
        /// <summary>
        /// 实验数量
        /// </summary>
        /// <returns></returns>
        [Description("实验数量")]
        [ExportExcelAttribute(HorizontalAlignment.Right, 12)]
        public int Num { get; set; }
        /// <summary>
        /// 班级
        /// </summary>
        /// <returns></returns>
        public string ClassIdz { get; set; }
        /// <summary>
        /// 实验目录必做演示实验数量
        /// </summary>
        public int NeedShowNum { get; set; }
        /// <summary>
        /// 实验目录必做分组实验数量
        /// </summary>
        public int NeedGroupNum { get; set; }
        /// <summary>
        /// 实验目录选做演示实验数量
        /// </summary>
        public int OptionalShowNum { get; set; }
        /// <summary>
        /// 实验目录选做分组实验数量
        /// </summary>
        public int OptionalGroupNum { get; set; }
        /// <summary>
        /// 校本必做演示实验数量
        /// </summary>
        public int SchoolNeedShowNum { get; set; }
        /// <summary>
        /// 校本必做分组实验数量
        /// </summary>
        public int SchoolNeedGroupNum { get; set; }
        /// <summary>
        /// 校本选做演示实验数量
        /// </summary>
        public int SchoolOptionalShowNum { get; set; }
        /// <summary>
        /// 校本选做分组实验数量
        /// </summary>
        public int SchoolOptionalGroupNum { get; set; }
        /// <summary>
        /// 版本Id
        /// </summary>
        [JsonConverter(typeof(StringJsonConverter))]
        public long VersionCurrentId { get; set; }

        /// <summary>
        /// 教材类型（1：必修（选修）  2：选择性必修（非选修））
        /// </summary>
        public int? CompulsoryType { get; set; }

        /// <summary>
        /// 课程活动类型(1: 课内 2:课外)
        /// </summary>
        public int ActivityType { get; set; } = 1;
        /// <summary>
        /// 状态（0:待提交 1：正常、待发布 2：已发布）
        /// </summary>
        public int Statuz { get; set; } 
        /// <summary>
        /// 发布时间
        /// </summary>
        public DateTime? PublishDate { get; set; }
        /// <summary>
        /// 血奶奶学期
        /// </summary>
        [NotMapped]
        [Description("学期")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 20)]
        public string SchoolYearTerm { get; set; }

        /// <summary>
        /// 班级
        /// </summary>
        /// <returns></returns>
        [NotMapped]
        public string ClassNamez { get; set; }
        [NotMapped]
        [Description("年级")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 10)]
        public string GradeName { get; set; }
        [NotMapped]
        [Description("课程")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 10)]
        public string CourseName { get; set; }
        [NotMapped]
        [Description("编制人")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 15)]
        public string RealName { get; set; }
        /// <summary>
        /// 任课老师名称
        /// </summary>
        [NotMapped]
        [Description("任课老师")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 15)]
        public string TeacherName { get; set; }
        /// <summary>
        /// 任课老师Id
        /// </summary>
        [NotMapped]
        public long TeacherId { get; set; }
        [NotMapped]
        [Description("单位名称")]
        [ExportExcelAttribute(HorizontalAlignment.Left, 25)]
        public string SchoolName { get; set; }
        /// <summary>
        /// 是否过期 1：过期  2：不过期
        /// </summary>
        [NotMapped]
        public int IsExpiry { get; set; }

        /// <summary>
        /// 学段Id
        /// </summary>
        [NotMapped]
        public int SchoolStageId { get; set; }

        /// <summary>
        /// 学段名称
        /// </summary>
        [NotMapped]
        [Description("学段")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 10)]
        public string SchoolStageName { get; set; }

        /// <summary>
        /// 区县Id
        /// </summary>
        [NotMapped]
        public long CountyId { get; set; }

        /// <summary>
        /// 区县名称
        /// </summary>
        [NotMapped]
        [Description("区县名称")]
        [ExportExcelAttribute(HorizontalAlignment.Left, 20)]
        public string CountyName { get; set; }
        /// <summary>
        /// 班级Id
        /// </summary>
        [NotMapped]
        [JsonConverter(typeof(DateTimeJsonConverter))]
        public long SchoolGradeClassId { get; set; }
        /// <summary>
        /// 选修学科
        /// </summary>
        [NotMapped]
        public string SelectSubject { get; set; }
        /// <summary>
        /// 实验类型
        /// </summary>
        /// <returns></returns>
        [NotMapped]
        public int? ExperimentType { get; set; }
        /// <summary>
        /// 实验要求
        /// </summary>
        /// <returns></returns>
        [NotMapped]
        public int? IsNeedDo { get; set; }
        /// <summary>
        /// 是否考核
        /// </summary>
        /// <returns></returns>
        [NotMapped]
        public int? IsEvaluate { get; set; }
        /// <summary>
        /// 班级名称
        /// </summary>
        /// <returns></returns>
        [NotMapped]
        public string ClassDesc { get; set; }
        /// <summary>
        /// 已做必做演示实验数量
        /// </summary>
        /// <returns></returns>
        [NotMapped]

        public int NeedShowNumed { get; set; }
        /// <summary>
        /// 已做必做分组实验数量
        /// </summary>
        /// <returns></returns>
        [NotMapped]
        public int NeedGroupNumed { get; set; }
        /// <summary>
        /// 已做选做演示实验数量
        /// </summary>
        /// <returns></returns>
        [NotMapped]
        public int OptionalShowNumed { get; set; }
        /// <summary>
        /// 已做选做分组实验数量
        /// </summary>
        /// <returns></returns>
        [NotMapped]
        public int OptionalGroupNumed { get; set; }
        /// <summary>
        /// 必做演示实验开出率
        /// </summary>
        [NotMapped]
        public decimal NeedShowNumRatio { get { return AlgorithmHelper.Percentage(NeedShowNumed, NeedShowNum, 2); } }
        /// <summary>
        /// 必做分组实验开出率
        /// </summary>
        [NotMapped]
        public decimal NeedGroupNumRatio { get { return AlgorithmHelper.Percentage(NeedGroupNumed, NeedGroupNum, 2); } }
        /// <summary>
        /// 选做演示实验开出率
        /// </summary>
        [NotMapped]
        public decimal OptionalShowNumRatio { get { return AlgorithmHelper.Percentage(OptionalShowNumed, OptionalShowNum, 2); } }
        /// <summary>
        /// 选做分组实验开出率
        /// </summary>
        [NotMapped]
        public decimal OptionalGroupNumRatio { get { return AlgorithmHelper.Percentage(OptionalGroupNumed, OptionalGroupNum, 2); } }
        /// <summary>
        /// 小计应开数
        /// </summary>
        [NotMapped]
        public int TotalNum { get { return (NeedGroupNum + NeedShowNum + OptionalGroupNum + OptionalShowNum); } }
        /// <summary>
        /// 小计实开数
        /// </summary>
        [NotMapped]
        public int TotalNumed { get { return (NeedGroupNumed + NeedShowNumed + OptionalGroupNumed + OptionalShowNumed); } }
        /// <summary>
        /// 小计开出率
        /// </summary>
        [NotMapped]
        public decimal TotalRatio { get { return AlgorithmHelper.Percentage(TotalNumed, TotalNum, 2); } }
        /// <summary>
        /// 状态名称
        /// </summary>
        [NotMapped]
        public string StatuzName { get; set; }
        /// <summary>
        /// 实验Id
        /// </summary>
        [NotMapped]
        [JsonConverter(typeof(DateTimeJsonConverter))]
        public long ExperimentId { get; set; }
    }
}
