#
# This properties file is used to initialize the default
# java.awt.datatransfer.SystemFlavorMap. It contains the Win32 platform-
# specific, default mappings between common Win32 Clipboard atoms and platform-
# independent MIME type strings, which will be converted into
# java.awt.datatransfer.DataFlavors.
#
# These default mappings may be augmented by specifying the
#
#       AWT.DnD.flavorMapFileURL 
#
# property in the appropriate awt.properties file. The specified properties URL
# will be loaded into the SystemFlavorMap.
#
# The standard format is:
#
# <native>=<MIME type>
#
# <native> should be a string identifier that the native platform will
# recognize as a valid data format. <MIME type> should specify both a MIME
# primary type and a MIME subtype separated by a '/'. The MIME type may include
# parameters, where each parameter is a key/value pair separated by '=', and
# where each parameter to the MIME type is separated by a ';'.
#
# Because SystemFlavorMap implements FlavorTable, developers are free to
# duplicate both native keys and DataFlavor values. If a mapping contains a
# duplicate key or value, earlier mappings which included this key or value
# will be preferred.
#
# Mappings whose values specify DataFlavors with primary MIME types of
# "text", and which support the charset parameter, should specify the exact
# format in which the native platform expects the data. The "charset"
# parameter specifies the char to byte encoding, the "eoln" parameter
# specifies the end-of-line marker, and the "terminators" parameter specifies
# the number of terminating NUL bytes. Note that "eoln" and "terminators"
# are not standardized MIME type parameters. They are specific to this file
# format ONLY. They will not appear in any of the DataFlavors returned by the
# SystemFlavorMap at the Java level.
#
# If the "charset" parameter is omitted, or has zero length, the platform
# default encoding is assumed. If the "eoln" parameter is omitted, or has
# zero length, "\n" is assumed. If the "terminators" parameter is omitted,
# or has a value less than zero, zero is assumed.
#
# Upon initialization, the data transfer subsystem will record the specified
# details of the native text format, but the default SystemFlavorMap will
# present a large set of synthesized DataFlavors which map, in both
# directions, to the native. After receiving data from the application in one
# of the synthetic DataFlavors, the data transfer subsystem will transform
# the data stream into the format specified in this file before passing the
# transformed stream to the native system.
#
# Mappings whose values specify DataFlavors with primary MIME types of
# "text", but which do not support the charset parameter, will be treated as
# opaque, 8-bit data. They will not undergo any transformation process, and
# any "charset", "eoln", or "terminators" parameters specified in this file
# will be ignored.
#
# See java.awt.datatransfer.DataFlavor.selectBestTextFlavor for a list of
# text flavors which support the charset parameter.

UNICODE\ TEXT=text/plain;charset=utf-16le;eoln="\r\n";terminators=2
TEXT=text/plain;eoln="\r\n";terminators=1
HTML\ Format=text/html;charset=utf-8;eoln="\r\n";terminators=1
Rich\ Text\ Format=text/rtf
HDROP=application/x-java-file-list;class=java.util.List
PNG=image/x-java-image;class=java.awt.Image
JFIF=image/x-java-image;class=java.awt.Image
DIB=image/x-java-image;class=java.awt.Image
ENHMETAFILE=image/x-java-image;class=java.awt.Image
METAFILEPICT=image/x-java-image;class=java.awt.Image
LOCALE=application/x-java-text-encoding;class="[B"
UniformResourceLocator=application/x-java-url;class=java.net.URL
UniformResourceLocator=text/uri-list;eoln="\r\n";terminators=1
UniformResourceLocator=text/plain;eoln="\r\n";terminators=1
FileGroupDescriptorW=application/x-java-file-list;class=java.util.List
FileGroupDescriptor=application/x-java-file-list;class=java.util.List
