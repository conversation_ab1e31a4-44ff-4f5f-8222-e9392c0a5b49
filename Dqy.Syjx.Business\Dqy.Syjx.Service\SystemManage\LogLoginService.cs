using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.SystemManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Model.Param.SystemManage;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using System;
using System.Collections.Generic;
using System.Data.Common;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dqy.Syjx.Service.SystemManage
{
    public class LogLoginService : RepositoryFactory
    {
        #region 获取数据
        public async Task<List<LogLoginEntity>> GetList(LogLoginListParam param)
        {
            var strSql = new StringBuilder();
            List<DbParameter> filter = ListFilter(param, strSql);
            var list = await this.BaseRepository().FindList<LogLoginEntity>(strSql.ToString(), filter.ToArray());
            return list.ToList();
        }

        public async Task<List<LogLoginEntity>> GetPageList(LogLoginListParam param, Pagination pagination)
        {
            var strSql = new StringBuildeusing Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.SystemManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Model.Param.SystemManage;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;

        }

        public async Task<List<LogLoginEntity>> GetList(LogLoginListParam param)
        {
            var strSql = new StringBuilder();
            List<DbParameter> filter = ListFilter(param, strSql);
            var list = await this.BaseRepository().FindList<LogLoginEntity>(strSql.ToString(), filter.ToArray());
            return list.ToList();
        }

        public async Task<List<LogLoginEntity>> GetPageList(LogLoginListParam param, Pagination pagination)
        {
            var strSql = new StringBuilder();
            List<DbParameter> filter = ListFilter(param, strSql);
            var list = await this.BaseRepository().FindList<LogLoginEntity>(strSql.ToString(), filter.ToArray(), pagination);
            return list.ToList();
        }

        public async Task<LogLoginEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<LogLoginEntity>(id);
        }

        ///// <summary>
        ///// 鏍规嵁IP鍦板潃鑾峰彇褰撳ぉ鏈€杩戣繛缁?娆＄櫥褰曠殑淇℃伅
        ///// </summary>
        ///// <param name="ipAddress">Ip鍦板潃</param>
        ///// <returns></returns>
        //public async Task<List<LogLoginEntity>> GetLoginList(string ipAddress)
        //{
        //    var strSql = new StringBuilder();
        //    strSql.Append(" SELECT Id,LogStatus");
        //    strSql.Append($" FROM  SysLogLogin  WHERE IpAddress ='{ipAddress}'");
        //    strSql.Append(" AND  DateDiff(dd,BaseCreateTime,getdate())=0 ");
        //    strSql.Append(" ORDER BY BaseCreateTime DESC");
        //    var list = await this.BaseRepository().FindList<LogLoginEntity>(strSql.ToString());
        //    return list.Take(3).ToList();
        //}
        /// <summary>
        /// 鏍规嵁IP鍦板潃鑾峰彇褰撳ぉ鏈€杩戣繛缁?娆＄櫥褰曠殑淇℃伅
        /// </summary>
        /// <param name="ipAddress">Ip鍦板潃</param>
        /// <returns>褰撳ぉ鏈€杩?娆＄櫥褰曡褰曞垪琛?/returns>
        public async Task<List<LogLoginEntity>> GetLoginList(string ipAddress)
        {
            // 鑾峰彇褰撳ぉ寮€濮嬫椂闂村拰娆℃棩寮€濮嬫椂闂?            var startOfDay = DateTime.Today;
            var startOfNextDay = startOfDay.AddDays(1);

            // 浣跨敤鍙傛暟鍖栨煡璇㈤槻姝QL娉ㄥ叆
            var strSql = new StringBuilder();
            strSql.Append("SELECT Id, LogStatus, BaseCreateTime");
            strSql.Append(" FROM SysLogLogin");
            strSql.Append(" WHERE IpAddress = @IpAddress");
            strSql.Append(" AND BaseCreateTime >= @StartOfDay");
            strSql.Append(" AND BaseCreateTime < @StartOfNextDay");
            strSql.Append(" ORDER BY BaseCreateTime DESC"); // 鎸夋椂闂撮檷搴忕‘淇濇渶鏂拌褰曞湪鍓?
            // 鍙傛暟鍖栨煡璇?            var parameter = new List<DbParameter>
            {
                DbParameterExtension.CreateDbParameter("@IpAddress", ipAddress),
                DbParameterExtension.CreateDbParameter("@StartOfDay", startOfDay),
                DbParameterExtension.CreateDbParameter("@StartOfNextDay", startOfNextDay)
            };

            // 鑾峰彇褰撳ぉ鎵€鏈夎褰曞苟鎸夋椂闂存帓搴忥紝鐒跺悗鍙栧墠3鏉?            Pagination pagination = new Pagination() { PageSize = 3 };
            var list = await this.BaseRepository()
                .FindList<LogLoginEntity>(strSql.ToString(), parameter.ToArray());

            return list.ToList();
        }


        /// <summary>
        /// 鏌ヨ绯荤粺鐧诲綍鏃ュ織
        /// </summary>
        /// <param name="querytype">鏌ヨ绫诲瀷锛?銆佸綋澶╋紝2銆?4灏忔椂涔嬪唴锛?/param>
        /// <param name="ipAddress">IP鍦板潃</param>
        /// <param name="remark">鐧诲綍澶囨敞</param>
        /// <param name="userid">鐢ㄦ埛Id</param>
        /// <param name="logstatus">鐧诲綍鐘舵€?/param>
        /// <param name="lastloginid">鏈€鍚庝竴娆＄櫥褰旾d</param>
        /// <returns></returns>
        public async Task<List<LogLoginEntity>> GetSysLogLogin(int querytype = 0, string ipAddress = "", string remark = "", long userid = 0, int logstatus = -1, long lastloginid = 0)
        {
            var strSql = new StringBuilder();
            var parameters = new List<DbParameter>();

            strSql.Append("SELECT Id, LogStatus, Remark, BaseCreateTime");
            strSql.Append(" FROM SysLogLogin WHERE 1=1");

            // 澶勭悊鏃堕棿鏌ヨ鏉′欢
            if (querytype == 1) // 褰撳ぉ
            {
                var startOfDay = DateTime.Today;
                var startOfNextDay = startOfDay.AddDays(1);
                strSql.Append(" AND BaseCreateTime >= @StartOfDay AND BaseCreateTime < @StartOfNextDay");
                parameters.Add(DbParameterExtension.CreateDbParameter("@StartOfDay", startOfDay));
                parameters.Add(DbParameterExtension.CreateDbParameter("@StartOfNextDay", startOfNextDay));
            }
            else if (querytype == 2) // 24灏忔椂涔嬪唴
            {
                var twentyFourHoursAgo = DateTime.Now.AddDays(-1);
                strSql.Append(" AND BaseCreateTime > @TwentyFourHoursAgo");
                parameters.Add(DbParameterExtension.CreateDbParameter("@TwentyFourHoursAgo", twentyFourHoursAgo));
            }

            // 澶勭悊鍏朵粬鏌ヨ鏉′欢
            if (!string.IsNullOrEmpty(ipAddress))
            {
                strSql.Append(" AND IpAddress = @IpAddress");
                parameters.Add(DbParameterExtension.CreateDbParameter("@IpAddress", ipAddress));
            }

            if (!string.IsNullOrEmpty(remark))
            {
                strSql.Append(" AND Remark = @Remark");
                parameters.Add(DbParameterExtension.CreateDbParameter("@Remark", remark));
            }

            if (userid > 0)
            {
                strSql.Append(" AND BaseCreatorId = @UserId");
                parameters.Add(DbParameterExtension.CreateDbParameter("@UserId", userid));
            }

            if (logstatus > -1)
            {
                strSql.Append(" AND LogStatus = @LogStatus");
                parameters.Add(DbParameterExtension.CreateDbParameter("@LogStatus", logstatus));
            }

            if (lastloginid > 0)
            {
                strSql.Append(" AND Id > @LastLoginId");
                parameters.Add(DbParameterExtension.CreateDbParameter("@LastLoginId", lastloginid));
            }

            strSql.Append(" ORDER BY BaseCreateTime DESC");

            var list = await this.BaseRepository().FindList<LogLoginEntity>(strSql.ToString(), parameters.ToArray());
            return list?.ToList() ?? new List<LogLoginEntity>();
        }

        /// <summary>
        /// 鑾峰彇鐢ㄦ埛鏈€鍚庝竴娆＄櫥褰曟垚鍔熺殑鏃ュ織
        /// </summary>
        /// <param name="userid">鐢ㄦ埛Id</param>
        /// <param name="remark">鐧诲綍澶囨敞</param>
        /// <returns></returns>
        public async Task<LogLoginEntity> GetUserLastLoginLog(long userid, string remark)
        {
            var strSql = new StringBuilder();
            var parameters = new List<DbParameter>();
            var twentyFourHoursAgo = DateTime.Now.AddDays(-1);

            strSql.Append("SELECT ISNULL(MAX(Id),0) AS Id FROM SysLogLogin");
            strSql.Append(" WHERE BaseCreatorId = @UserId");
            strSql.Append(" AND BaseCreateTime > @TwentyFourHoursAgo");
            strSql.Append(" AND LogStatus = @LogStatus AND Remark LIKE @Remark");

            parameters.Add(DbParameterExtension.CreateDbParameter("@UserId", userid));
            parameters.Add(DbParameterExtension.CreateDbParameter("@TwentyFourHoursAgo", twentyFourHoursAgo));
            parameters.Add(DbParameterExtension.CreateDbParameter("@LogStatus", OperateStatusEnum.Success.ParseToInt()));
            parameters.Add(DbParameterExtension.CreateDbParameter("@Remark", $"%{remark}%"));

            var list = await this.BaseRepository().FindList<LogLoginEntity>(strSql.ToString(), parameters.ToArray());
            return list?.LastOrDefault();
        }
        #endregion

        #region 鎻愪氦鏁版嵁
        public async Task SaveForm(LogLoginEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            long[] idArr = TextHelper.SplitToArray<long>(ids, ',');
            await this.BaseRepository().Delete<LogLoginEntity>(idArr);
        }

        public async Task RemoveAllForm()
        {
            await this.BaseRepository().ExecuteBySql("truncate table SysLogLogin");
        }
        #endregion

        #region 绉佹湁鏂规硶
        private List<DbParameter> ListFilter(LogLoginListParam param, StringBuilder strSql)
        {
            strSql.Append(@"SELECT  a.Id,
                                    a.BaseCreateTime,
                                    a.BaseCreatorId,
                                    a.LogStatus,
                                    a.IpAddress,
                                    a.IpLocation,
                                    a.Browser,
                                    a.OS,
                                    a.Remark,
                                    b.UserName
                            FROM    SysLogLogin a
                                    LEFT JOIN SysUser b ON a.BaseCreatorId = b.Id
                            WHERE   1 = 1");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.UserName))
                {
                    strSql.Append(" AND b.UserName like @UserName");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UserName", '%' + param.UserName + '%'));
                }
                if (param.LogStatus > -1)
                {
                    strSql.Append(" AND a.LogStatus = @LogStatus");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@LogStatus", param.LogStatus));
                }
                if (!string.IsNullOrEmpty(param.IpAddress))
                {
                    strSql.Append(" AND a.IpAddress like @IpAddress");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@IpAddress", '%' + param.IpAddress + '%'));
                }
                if (!string.IsNullOrEmpty(param.StartTime.ParseToString()))
                {
                    strSql.Append(" AND a.BaseCreateTime >= @StartTime");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@StartTime", param.StartTime));
                }
                if (!string.IsNullOrEmpty(param.EndTime.ParseToString()))
                {
                    param.EndTime = param.EndTime.Value.Date.Add(new TimeSpan(23, 59, 59));
                    strSql.Append(" AND a.BaseCreateTime <= @EndTime");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@EndTime", param.EndTime));
                }
            }
            return parameter;
        }
        #endregion
    }
}

