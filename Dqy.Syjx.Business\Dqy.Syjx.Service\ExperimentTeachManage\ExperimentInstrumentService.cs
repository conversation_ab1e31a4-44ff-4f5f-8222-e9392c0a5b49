﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.ExperimentTeachManage;
using Dqy.Syjx.Model.Param.ExperimentTeachManage;
using Dqy.Syjx.Entity.InstrumentManage;
using Dqy.Syjx.Enum;
using Microsoft.Extensions.Primitives;

namespace Dqy.Syjx.Service.ExperimentTeachManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2023-06-27 17:06
    /// 描 述：ddd服务类
    /// </summary>
    public class ExperimentInstrumentService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<ExperimentInstrumentEntity>> GetList(ExperimentInstrumentListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<ExperimentInstrumentEntity>> GetPageList(ExperimentInstrumentListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<ExperimentInstrumentEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<ExperimentInstrumentEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(ExperimentInstrumentEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(ExperimentInstrumentEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }
        public async Task UpdateForm(ExperimentInstrumentEntity entity, List<string> fields, Repository db)
        {
            if (fields != null)
            {
                await entity.Modify();
                fields.Add("BaseModifyTime");
                fields.Add("BaseModifierId");
            }
            if (db != null)
            {
                await db.Update(entity, fields);
            }
            else
            {
                await this.BaseRepository().Update(entity, fields);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update ex_ExperimentInstrument set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
                strSql = $"update ex_ExperimentInstrument set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }

        public async Task DeleteByBookingid(long bookingid)
        {
            string strSql = $" update ex_ExperimentInstrument set BaseIsDelete = 1 where ExperimentBookingId = {bookingid} ";
            await this.BaseRepository().ExecuteBySql(strSql);
        }

        public async Task DeleteByBookingid(long bookingid, Repository db, int sourcetype = 0)
        {
            string strSql = $" update ex_ExperimentInstrument set BaseIsDelete = 1 where ExperimentBookingId = {bookingid} ";
            if (sourcetype > 0)
            {
                strSql += string.Format(" AND SourceType = {0} ", sourcetype);
            }
            await db.ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<ExperimentInstrumentEntity, bool>> ListFilter(ExperimentInstrumentListParam param)
        {
            var expression = LinqExtensions.True<ExperimentInstrumentEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (param.ExperimentBookingIds != null && param.ExperimentBookingIds.Count > 0)
                {
                    expression = expression.And(t => param.ExperimentBookingIds.Contains(t.ExperimentBookingId));
                }
                if (param.ExperimentBookingId > 0)
                {
                    expression = expression.And(t => t.ExperimentBookingId == param.ExperimentBookingId);
                }
                if (param.SchoolInstrumentId > 0)
                {
                    expression = expression.And(t => t.SchoolInstrumentId == param.SchoolInstrumentId );
                }
                if (param.BatchNo > 0)
                {
                    expression = expression.And(t => t.BatchNo == param.BatchNo);
                }
                if (param.SourceType > 0)
                {
                    expression = expression.And(t => t.SourceType == param.SourceType);
                }
            }
            return expression;
        }
        #endregion

        #region 扩展方法
        /// <summary>
        /// 查询当前预约实验仪器
        /// </summary>
        /// <param name="bookingids">排序</param>
        /// <param name="sourcetype">类型  1:预约  2：安排    </param>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     </param>
        /// <returns></returns>
        public async Task<List<ExperimentInstrumentEntity>> GetListById(List<long> bookingids, int sourcetype = 2)
        {
            long resultNum = 0;
            StringBuilder sql = new StringBuilder();
            sql.Append($@"
                SELECT ei.* ,si.FunRoomId ,fr.SafeguardUserId ,eb.FunRoomId AS BookingFunRoomId 
                ,CASE WHEN eb.FunRoomId = 1 THEN eb.ArrangerId ELSE bookfr.SafeguardUserId END AS ArrangerId
                FROM ex_ExperimentInstrument AS ei
                INNER JOIN eq_SchoolInstrument AS si ON si.BaseIsDelete = 0 AND ei.SchoolInstrumentId = si.Id
                INNER JOIN bn_FunRoom AS fr ON  fr.BaseIsDelete = 0 AND si.FunRoomId = fr.Id
                INNER JOIN ex_ExperimentBooking AS eb ON eb.BaseIsDelete = 0 AND ei.ExperimentBookingId = eb.Id
                LEFT JOIN bn_FunRoom AS bookfr ON  bookfr.BaseIsDelete = 0 AND eb.FunRoomId = bookfr.Id
                WHERE ei.BaseIsDelete = 0 AND ({string.Join(" OR ", bookingids.Select(m => string.Format(" ei.ExperimentBookingId = {0} ", m)))})    
            ");
            sql.Append($" AND ei.SourceType = {sourcetype} ");
           
            var list = await this.BaseRepository().FindList<ExperimentInstrumentEntity>(sql.ToString());
            if (list != null)
            {
                resultNum = list.Count();
            }
            return list.ToList();
        }
        #endregion
    }
}
