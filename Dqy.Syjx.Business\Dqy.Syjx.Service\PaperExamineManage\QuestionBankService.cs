﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.PaperExamineManage;
using Dqy.Syjx.Model.Param.PaperExamineManage;
using Dqy.Syjx.Enum;

namespace Dqy.Syjx.Service.PaperExamineManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2022-08-02 16:16
    /// 描 述：自编题库服务类
    /// </summary>
    public class QuestionBankService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<QuestionBankEntity>> GetList(QuestionBankListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List
        }

        public async Task<List<QuestionBankEntity>> GetList(QuestionBankListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<QuestionBankEntity>> GetPageList(QuestionBankListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ListFilter(param, strSql);
            var list = await this.BaseRepository().FindList<QuestionBankEntity>(strSql.ToString(), filter.ToArray(), pagination);
            return list.ToList();
        }

        public async Task<QuestionBankEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<QuestionBankEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(QuestionBankEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(QuestionBankEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task UpdateStatuzForm(string ids,int statuz)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update cp_QuestionBank set Statuz = {statuz} where Id in ({ids}) ";
            }
            else
            {
                strSql = $"update cp_QuestionBank set Statuz = {statuz} where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update cp_QuestionBank set BaseIsDelete = 1 where Id in ({ids}) AND id not in (SELECT QuestionBankId FROM cp_PaperQuestionBank where BaseIsDelete = 0)";
            }
            else
            {
                strSql = $"update cp_QuestionBank set BaseIsDelete = 1 where id = {ids} AND id not in (SELECT QuestionBankId FROM cp_PaperQuestionBank where BaseIsDelete = 0)";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<QuestionBankEntity, bool>> ListFilter(QuestionBankListParam param)
        {
            var expression = LinqExtensions.True<QuestionBankEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Classifys))
                {
                    int[] classifyArr = TextHelper.SplitToArray<int>(param.Classifys, ',');
                    expression = expression.And(t => classifyArr.Contains(t.Classify));
                }
                if(param.UnitId > 0)
                {
                    expression = expression.And(t => t.UnitId == param.UnitId);
                }
                if(param.BaseIsDelete > -1)
                {
                    expression = expression.And(t => t.BaseIsDelete == param.BaseIsDelete);
                }
                if (param.Statuz > 0)
                {
                    expression = expression.And(t => t.Statuz == param.Statuz);
                }
                if(param.QuestionType > 0)
                {
                    expression = expression.And(t => t.QuestionType == param.QuestionType);
                }
                if (param.UseObj > 0)
                {
                    expression = expression.And(t => t.UseObj == param.UseObj);
                }
                if (param.Nature > -1)
                {
                    expression = expression.And(t => t.Nature == param.Nature);
                }
                if (param.CourseId > 0) //学科（含全部）
                {
                    expression = expression.And(t => t.CourseId == param.CourseId || t.CourseId == 0);
                }
                if (param.SchoolStage > 0) //学段（含全部）
                {
                    expression = expression.And(t => t.SchoolStage == param.SchoolStage || t.SchoolStage == 0);
                }
                if (param.GradeId > 0) //年级（含全部）
                {
                    expression = expression.And(t => t.GradeId == param.GradeId || t.GradeId == 0);
                }
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (!string.IsNullOrEmpty(param.NotInIds))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.NotInIds, ',');
                    expression = expression.And(t => !idArr.Contains(t.Id.Value));
                }
                if (param.UnitId >0)
                {
                    expression = expression.And(t => t.UnitId == param.UnitId);
                }
            }
            return expression;
        }

        /// <summary>
        /// 题库查询
        /// </summary>
        /// <param name="param"></param>
        /// <param name="strSql"></param>
        /// <returns></returns>
        private List<DbParameter> ListFilter(QuestionBankListParam param,StringBuilder strSql)
        {
            StringBuilder strBody = new StringBuilder();
            StringBuilder strExt = new StringBuilder();
            strBody.Append($@" Select qb1.Id ,qb1.BaseIsDelete ,qb1.BaseCreateTime ,qb1.BaseModifyTime ,qb1.BaseCreatorId ,qb1.BaseModifierId ,qb1.BaseVersion
            ,qb1.Code ,qb1.QuestionType ,qb1.Classify ,qb1.CourseId ,qb1.UseObj ,qb1.UseObjNature ,qb1.SchoolStage ,qb1.GradeId
            ,qb1.Title ,qb1.Remark ,qb1.Statuz ,qb1.UnitId ,qb1.Tag ,qb1.Nature
            ,u2.UnitType
            ,CASE WHEN qb1.UnitId <> {param.UnitId} Then 0 ElSE  1 END AS IsCurrentUnit
            ,CASE WHEN qb1.UnitId <> {param.UnitId} Then 0 ElSE (CASE WHEN (SELECT TOP(1) temp01.Id FROM  cp_PaperQuestionBank as temp01	WHERE temp01.QuestionBankId = qb1.Id) > 0 THEN 0 ELSE 1 END) END AS IsDeletable
            ,CASE WHEN  qb1.QuestionType = 0 THEN '全部' ELSE ISNULL(dic1.DicName,'') END AS QuestionTypeName
            ,CASE WHEN  qb1.Classify = 0 THEN '全部' ELSE ISNULL(dic2.DicName,'') END AS ClassifyName
            ,CASE WHEN  qb1.UseObj = 0 THEN '全部' ELSE ISNULL(dic3.DicName,'') END AS UseObjName
            ,CASE WHEN  qb1.CourseId = 0 THEN '全部' ELSE ISNULL(dic4.DicName,'') END AS CourseName
            ,CASE WHEN  qb1.SchoolStage = 0 THEN '全部' ELSE ISNULL(dic5.DicName,'') END AS SchoolStageName
            ,CASE WHEN  qb1.GradeId = 0 THEN '全部' ELSE ISNULL(dic6.DicName,'') END AS GradeName
            ,CASE u2.UnitType WHEN 1 THEN u2.Id ELSE 0 END AS  CityId ");
            if (param.UnitType== UnitTypeEnum.School.ParseToInt())
            {
                strBody.Append(@" ,CASE u2.UnitType WHEN 1 THEN ur1.ExtensionObjId WHEN 2 THEN u2.Id ELSE 0 END AS  CountyId
                                  ,CASE u2.UnitType WHEN 1 THEN ur2.ExtensionObjId WHEN 2 THEN ur21.ExtensionObjId WHEN 3 THEN u2.Id ELSE 0 END AS  SchoolId ");
                strExt.Append(@" LEFT JOIN  up_UnitRelation AS ur1 ON u2.UnitType = 1 AND ur1.ExtensionType = 3 AND u2.Id = ur1.UnitId
                                 LEFT JOIN up_UnitRelation AS ur2 ON ur2.ExtensionType = 3 AND ur1.ExtensionObjId = ur2.UnitId
                                 LEFT JOIN  up_UnitRelation AS ur21 ON   u2.UnitType = 2 AND ur21.ExtensionType = 3 AND u2.Id = ur21.UnitId");
            }
            else if (param.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                strBody.Append(@" ,CASE u2.UnitType WHEN 1 THEN ur1.ExtensionObjId WHEN 2 THEN u2.Id ELSE 0 END AS  CountyId   ");
                strExt.Append(@" LEFT JOIN  up_UnitRelation AS ur1 ON u2.UnitType = 1 AND ur1.ExtensionType = 3 AND u2.Id = ur1.UnitId ");
            }
            else if (param.UnitType == UnitTypeEnum.City.ParseToInt())
            {
                //
            }
            strBody.Append(@" FROM  cp_QuestionBank AS qb1
                            INNER JOIN  up_Unit AS u2 ON qb1.UnitId = u2.Id
                            LEFT JOIN  sys_static_dictionary AS dic1 ON dic1.BaseIsDelete = 0 AND dic1.TypeCode = '2002' AND dic1.DictionaryId = qb1.QuestionType
                            LEFT JOIN  sys_static_dictionary AS dic2 ON dic2.BaseIsDelete = 0 AND dic2.TypeCode = '2001' AND dic2.DictionaryId = qb1.Classify
                            LEFT JOIN  sys_static_dictionary AS dic3 ON dic3.BaseIsDelete = 0 AND dic3.TypeCode = '2003' AND dic3.DictionaryId = qb1.UseObj
                            LEFT JOIN  sys_static_dictionary AS dic4 ON dic4.BaseIsDelete = 0 AND dic4.TypeCode = '1005' AND dic4.DictionaryId = qb1.CourseId
                            LEFT JOIN  sys_static_dictionary AS dic5 ON dic5.BaseIsDelete = 0 AND dic5.TypeCode = '1002' AND dic5.DictionaryId = qb1.SchoolStage
                            LEFT JOIN  sys_static_dictionary AS dic6 ON dic6.BaseIsDelete = 0 AND dic6.TypeCode = '1003' AND dic6.DictionaryId = qb1.GradeId
                            ");

            strSql.Append(" Select * From ( ");
            strSql.Append(strBody.ToString());
            strSql.Append(strExt.ToString());
            strSql.Append(" ) AS tb_1 Where 1 = 1 ");

            var parameter = new List<DbParameter>();
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = @BaseIsDelete ");
                parameter.Add(DbParameterExtension.CreateDbParameter("@BaseIsDelete", param.BaseIsDelete));
                if (param.UnitId > 0)
                {
                    if (param.UnitType == UnitTypeEnum.City.ParseToInt())
                    {
                        strSql.Append(" AND CityId = @CityId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@CityId", param.UnitId));
                    }
                    else if (param.UnitType == UnitTypeEnum.County.ParseToInt())
                    {
                        strSql.Append(" AND CountyId = @CountyId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.UnitId));
                    }
                    else if (param.UnitType == UnitTypeEnum.School.ParseToInt())
                    {
                        strSql.Append(" AND SchoolId = @SchoolId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.UnitId));
                    }
                    else if (param.UnitType == UnitTypeEnum.System.ParseToInt())
                    {

                    }
                    else
                    {
                        strSql.Append(" AND 1 <> 1 ");
                    }
                }
                else
                {
                    strSql.Append(" AND 1 <> 1 ");
                }


                if (param.CourseId >= 0)
                {
                    strSql.Append(" AND (CourseId = @CourseId OR CourseId = 0) "); //学科可查全部
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (param.SchoolStage >= 0)
                {
                    strSql.Append(" AND (SchoolStage = @SchoolStage OR SchoolStage = 0)"); //学段可查全部
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStage", param.SchoolStage));
                }
                if (param.GradeId >= 0)
                {
                    strSql.Append(" AND (GradeId = @GradeId OR GradeId = 0)"); //年级可查全部
                    parameter.Add(DbParameterExtension.CreateDbParameter("@GradeId", param.GradeId));
                }
                if (param.Nature >= 0)
                {
                    strSql.Append(" AND Nature = @Nature ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Nature", param.Nature));
                }
                if (param.QuestionType >= 0)
                {
                    strSql.Append(" AND QuestionType = @QuestionType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@QuestionType", param.QuestionType));
                }
                if (param.Classify >= 0)
                {
                    strSql.Append(" AND Classify = @Classify ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Classify", param.Classify));
                }
                if (param.UseObj >= 0)
                {
                    strSql.Append(" AND UseObj = @UseObj ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UseObj", param.UseObj));
                }
                if (param.Statuz > 0)
                {
                    strSql.Append(" AND Statuz = @Statuz ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Statuz", param.Statuz));
                }
                if (!string.IsNullOrEmpty(param.Keyword))
                {
                    strSql.Append(" AND ( Tag like @Keyword OR Title like @Keyword OR Code like @Keyword ) ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Keyword", $"%{param.Keyword}%"));
                }
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    strSql.Append($" AND ID NOT IN ({param.Ids})");
                }
            }
            return parameter;
        }
        #endregion
    }
}

