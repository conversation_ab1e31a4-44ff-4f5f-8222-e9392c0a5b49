﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.CameraManage;
using Dqy.Syjx.Model.Param.CameraManage;
using Dqy.Syjx.Enum;

namespace Dqy.Syjx.Service.CameraManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2022-01-10 10:02
    /// 描 述：摄像头管理服务类
    /// </summary>
    public class SchoolCameraService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<SchoolCameraEntity>> GetList(SchoolCameraListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<SchoolCameraEntity>> GetPageList(SchoolCameraListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ListFilter(param, strSql);
            var list = await this.BaseRepository().FindList<SchoolCameraEntity>(strSql.ToString(), filter.ToArray(), pagination);
            return list.ToList();
        }

        public async Task<SchoolCameraEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<SchoolCameraEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(SchoolCameraEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(SchoolCameraEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
               strSql = $" update bn_SchoolCamera set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
               strSql = $"update bn_SchoolCamera set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }

        public async Task CancelRelationForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update bn_SchoolCamera set FunRoomId = 0 where Id in ({ids}) ";
            }
            else
            {
                strSql = $"update bn_SchoolCamera set FunRoomId = 0 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<SchoolCameraEntity, bool>> ListFilter(SchoolCameraListParam param)
        {
            var expression = LinqExtensions.True<SchoolCameraEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (!string.IsNullOrEmpty(param.Name))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Name, ',');
                    expression = expression.And(t => t.SrcName.Contains(param.Name) || t.Address.Contains(param.Name));
                }
                if (param.UnitId > 0)
                {
                    expression = expression.And(t => t.UnitId == param.UnitId);
                }
                if (param.FunRoomId > 0)
                {
                    expression = expression.And(t => t.FunRoomId == param.FunRoomId);
                }
                if (param.Statuz > 0)
                {
                    expression = expression.And(t => t.Statuz == param.Statuz);
                }
            }
            return expression;
        }


        private List<DbParameter> ListFilter(SchoolCameraListParam param, StringBuilder strSql)
        {
            strSql.Append($@"  SELECT * From (
                 SELECT  sc1.Id ,
                         sc1.BaseIsDelete ,
                         sc1.BaseCreateTime ,
                         sc1.BaseModifyTime ,
                         sc1.BaseCreatorId ,
                         sc1.BaseModifierId ,
                         sc1.BaseVersion ,
                         sc1.UnitId ,
                         sc1.SchoolName ,
                         sc1.SrcName ,
                         sc1.SrcIndex ,
                         sc1.Address ,
                         sc1.Remark ,
                         sc1.LinkUrl ,
                         sc1.Statuz ,
                         sc1.FunRoomId ,
                         sc1.OptUserId ,
		                 u2.Name AS UnitName,
                         u2.Sort ,
		                 fr3.Name AS FunRoomName
                         ,ISNULL(ad1.Name,'') AS RoomName
						 ,IsNULL(ad2.Name,'') AS HouseName,
						 ur1.UnitId AS CountyId,
		                u3.Name AS CountyName
                FROM bn_SchoolCamera AS sc1
                INNER JOIN  up_Unit AS u2 ON sc1.UnitId = u2.Id AND u2.Statuz = {StatusEnum.Yes.ParseToInt()} AND u2.BaseIsDelete = 0
				INNER JOIN  up_UnitRelation AS ur1 ON sc1.UnitId = ur1.ExtensionObjId AND ur1.ExtensionType = 3 AND ur1.BaseIsDelete = 0
                LEFT JOIN  up_Unit AS u3 ON ur1.UnitId = u3.Id
                LEFT JOIN  bn_FunRoom AS fr3 ON sc1.FunRoomId = fr3.Id AND fr3.Statuz = {StatusEnum.Yes.ParseToInt()} AND fr3.BaseIsDelete = 0
				LEFT JOIN  up_Address AS ad1 ON fr3.Address = CAST(ad1.Id AS CHAR(128))
				LEFT JOIN  up_Address AS ad2 ON ad2.Id = ad1.Pid
            ) as tb1 WHERE   1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = @BaseIsDelete ");
                parameter.Add(DbParameterExtension.CreateDbParameter("@BaseIsDelete", param.BaseIsDelete));

                if(!string.IsNullOrEmpty(param.CountyName))
                {
                    strSql.Append(" AND CountyName like @CountyName");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyName", $"%{param.CountyName}%"));
                }
                if (!string.IsNullOrEmpty(param.CameraName))
                {
                    strSql.Append(" AND SrcName like @CameraName");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CameraName", $"%{param.CameraName}%"));
                }
                if (!string.IsNullOrEmpty(param.CameraCode))
                {
                    strSql.Append(" AND SrcIndex like @CameraCode");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CameraCode", $"%{param.CameraCode}%"));
                }
                if (param.UnitId > 0)
                {
                    strSql.Append(" AND UnitId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                if(param.CountyId > 0)
                {
                    strSql.Append(" AND CountyId = @CountyId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
                if (param.Statuz > 0)
                {
                    strSql.Append(" AND Statuz = @Statuz ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Statuz", param.Statuz));
                }
                if (!string.IsNullOrEmpty(param.Name))
                {
                    strSql.Append(" AND (SrcName like @Name OR SrcIndex like @Name OR Address like @Name  )");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Name", $"%{param.Name}%"));
                }
                if (param.FunRoomId >0)
                {
                    if (param.FunRoomId==1)
                    {
                        strSql.Append(" AND FunRoomId > 0 ");
                    }
                    else
                    {
                        strSql.Append(" AND FunRoomId = 0 ");
                    }
                }
            }

            return parameter;
        }

        #endregion
    }
}
