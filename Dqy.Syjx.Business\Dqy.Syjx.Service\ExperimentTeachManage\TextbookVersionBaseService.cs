﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.ExperimentTeachManage;
using Dqy.Syjx.Model.Param.ExperimentTeachManage;
using Dqy.Syjx.Enum;

namespace Dqy.Syjx.Service.ExperimentTeachManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-12-08 15:36
    /// 描 述：服务类
    /// </summary>
    public class TextbookVersionBaseService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<TextbookVersionBaseEntity>> GetList(TextbookVersionBaseListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<TextbookVersionBaseEntity>> GetPageList(TextbookVersionBaseListParam param, Pagination pagination)
        {
            StringBuilder sql = new StringBuilder();
            var expression = ListFilter(param, sql);
            var list = await this.BaseRepository().FindList<TextbookVersionBaseEntity>(sql.ToString(), expression.ToArray(), pagination);
            return list.ToList();
        }
         
        public async Task<TextbookVersionBaseEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<TextbookVersionBaseEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(TextbookVersionBaseEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(TextbookVersionBaseEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update ex_TextbookVersionBase set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
                strSql = $"update ex_TextbookVersionBase set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<TextbookVersionBaseEntity, bool>> ListFilter(TextbookVersionBaseListParam param)
        {
            var expression = LinqExtensions.True<TextbookVersionBaseEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (!param.Statuz.IsNullOrZero())
                {
                    expression = expression.And(t => t.Statuz == param.Statuz);
                }
                if (!param.VersionName.IsEmpty())
                {
                    expression = expression.And(t => t.VersionName.Equals(param.VersionName));
                }
                if (!param.SchoolStage.IsNullOrZero())
                {
                    expression = expression.And(t => t.SchoolStage == param.SchoolStage);
                }
                if (!param.GradeId.IsNullOrZero())
                {
                    expression = expression.And(t => t.GradeId == param.GradeId);
                }
                if (!param.CourseId.IsNullOrZero())
                {
                    expression = expression.And(t => t.CourseId == param.CourseId);
                }
                if (!param.SchoolTerm.IsNullOrZero())
                {
                    expression = expression.And(t => t.SchoolTerm == param.SchoolTerm);
                }
            }
            return expression;
        }

        private List<DbParameter> ListFilter(TextbookVersionBaseListParam param, StringBuilder strSql)
        {
            strSql.Append(@$" SELECT * From (
                               SELECT  TB.Id ,TB.BaseCreateTime ,TB.BaseCreatorId ,
                                       TB.VersionName ,TB.SchoolStage ,TB.GradeId ,TB.CourseId ,TB.SchoolTerm ,TB.Statuz ,
                                       TB.NeedShowNum ,TB.NeedGroupNum ,TB.OptionalShowNum ,TB.OptionalGroupNum ,TB.CompulsoryType ,
                                       D1.DicName AS SchoolStageName , D2.DicName AS GradeName ,D3.DicName AS CourseName                                       
                               FROM  ex_TextbookVersionBase AS TB
                               INNER JOIN  sys_static_dictionary AS D1 ON TB.SchoolStage = D1.DictionaryId 
                                                                    AND TypeCode = '{DicTypeCodeEnum.SchoolStage.ParseToInt()}' AND D1.BaseIsDelete = 0 
                               LEFT JOIN  sys_static_dictionary AS D2 ON TB.GradeId =D2.DictionaryId 
                                                                    AND D2.TypeCode = '{DicTypeCodeEnum.Grade.ParseToInt()}' AND D2.BaseIsDelete = 0 
                               INNER JOIN  sys_static_dictionary AS D3 ON TB.CourseId = D3.DictionaryId 
                                                                    AND D3.TypeCode = '{DicTypeCodeEnum.Course.ParseToInt()}' AND D3.BaseIsDelete = 0 
                               WHERE TB.BaseIsDelete = 0                               
                          ) as T WHERE  1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (!param.SchoolStage.IsNullOrZero())
                {
                    strSql.Append(" AND SchoolStage = @SchoolStage ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStage", param.SchoolStage));
                }
                if (!param.GradeId.IsNullOrZero())
                {
                    strSql.Append(" AND GradeId = @GradeId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@GradeId", param.GradeId));
                }
                if (!param.CourseId.IsNullOrZero())
                {
                    strSql.Append(" AND CourseId = @CourseId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (!param.SchoolTerm.IsNullOrZero())
                {
                    strSql.Append(" AND SchoolTerm = @SchoolTerm ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolTerm", param.SchoolTerm));
                }
                if (!param.VersionName.IsEmpty())
                {
                    strSql.Append($" AND VersionName LIKE '%{param.VersionName.Trim()}%'");
                }
            }
            return parameter;
        }
        
        #endregion
    }
}
