﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.SystemManage;
using Dqy.Syjx.Model.Param.SystemManage;

namespace Dqy.Syjx.Service.SystemManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2023-08-10 13:30
    /// 描 述：服务类
    /// </summary>
    public class AppManageService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<AppManageEntity>> GetList(AppManageListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<AppManageEntity>> GetPageList(AppManageListParam param, Pagi
        }

        public async Task<List<AppManageEntity>> GetList(AppManageListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<AppManageEntity>> GetPageList(AppManageListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<AppManageEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<AppManageEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(AppManageEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(AppManageEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update sys_AppManage set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
                strSql = $"update sys_AppManage set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<AppManageEntity, bool>> ListFilter(AppManageListParam param)
        {
            var expression = LinqExtensions.True<AppManageEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Equals(t.Id.Value));
                }
                if(param.IsMain > 0)
                {
                    expression = expression.And(t => t.IsMain == param.IsMain);
                }
                if (!string.IsNullOrEmpty(param.AppCode))
                {
                    expression = expression.And(t => t.AppCode.Equals(param.AppCode));
                }
                if (!string.IsNullOrEmpty(param.AppName))
                {
                    expression = expression.And(t => t.AppName.Contains(param.AppName));
                }
                if (!string.IsNullOrEmpty(param.CorpId))
                {
                    expression = expression.And(t => t.CorpId.Equals(param.CorpId));
                }
                if (!string.IsNullOrEmpty(param.AngentId))
                {
                    expression = expression.And(t => t.AngentId.Equals(param.AngentId));
                }
                if (!string.IsNullOrEmpty(param.Secret))
                {
                    expression = expression.And(t => t.Secret.Equals(param.Secret));
                }
                if (!string.IsNullOrEmpty(param.AppKey))
                {
                    expression = expression.And(t => t.AppKey.Equals(param.AppKey));
                }
                if (!string.IsNullOrEmpty(param.ClientId))
                {
                    expression = expression.And(t => t.ClientId.Equals(param.ClientId));
                }
                if (param.AppType > 0)
                {
                    expression = expression.And(t => t.AppType == param.AppType);
                }
            }
            return expression;
        }
        #endregion
    }
}

