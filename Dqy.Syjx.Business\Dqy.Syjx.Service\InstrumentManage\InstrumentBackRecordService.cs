﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.InstrumentManage;
using Dqy.Syjx.Model.Param.InstrumentManage;

namespace Dqy.Syjx.Service.InstrumentManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2022-06-07 13:53
    /// 描 述：服务类
    /// </summary>
    public class InstrumentBackRecordService :  RepositoryFactory
    {
        #region 获取数据
        /// <summary>
        /// 根据出库Id查询退回记录
        /// </summary>
        /// <param name="outListId"></param>
        /// <param name="unitId"></param>
        /// <returns></returns>
        public async Task<List<InstrumentBackRecordEntity>> GetListByOutId(long outListId, long unitId)
        {
            StringBuilder sql = new StringBuilder();
            sql.Append($@"
                SELECT  R.Id ,
                        U.RealName AS ReceiveUserName ,
                        R.BaseCreateTime ,
                        R.BackNum ,
                        I.UnitName ,
                        U2.RealName AS OperatorUserName
                FROM     eq_InstrumentBackRecord AS R
                        INNER JOIN  eq_InstrumentOutList AS L ON R.InstrumentOutListId = L.Id        
                        INNER JOIN  eq_SchoolInstrument AS I ON L.SchoolInstrumentId = I.Id
		                INNER JOIN  SysUser AS U ON L.ReceiveUserId = U.Id
                        INNER JOIN  SysUser AS U2 ON R.BaseCreatorId = U2.Id
                WHERE R.BaseIsDelete = 0 AND R.InstrumentOutListId = {outListId} AND L.SchoolId = {unitId}
                ORDER BY R.Id DESC
            ");
            var list = await this.BaseRepository().FindList<InstrumentBackRecordEntity>(sql.ToString());
            return list.ToList();
        }
        #endregion

        #region 提交数据

        public async Task SaveTransForm(InstrumentBackRecordEntity entity, Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        #endregion
    }
}
 