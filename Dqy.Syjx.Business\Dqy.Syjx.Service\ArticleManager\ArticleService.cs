﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.ArticleManager;
using Dqy.Syjx.Model.Param.ArticleManager;

namespace Dqy.Syjx.Service.ArticleManager
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2022-01-21 10:42
    /// 描 述：服务类
    /// </summary>
    public class ArticleService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<ArticleEntity>> GetList(ArticleListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<ArticleEntity>> GetPageList(ArticleListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ListFilter(param, strSql);
            var list = await this.BaseRepository().FindList<ArticleEntity>(strSql.ToString(), filter.ToArray(), pagination);
            return list.ToList();
        }

        public async Task<ArticleEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<ArticleEntity>(id);
        }

        /// <summary>
        /// 根据资讯分类获取前几条条资讯信息
        /// </summary>
        /// <param name="articleCategoryId">资讯分类Id</param>
        /// <param name="num">显示条数</param>
        /// <returns></returns>
        public async Task<List<ArticleEntity>> GetIndexArticleList(long articleCategoryId,int num)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append($"SELECT TOP {num} Id,Cid,Title,ShortTitle,Sort FROM  d_Article WHERE BaseIsDelete = 0 AND Cid = {articleCategoryId} AND IsRecommend = 1 AND Statuz = 2 ORDER BY Sort ASC");
            var list = await this.BaseRepository().FindList<ArticleEntity>(strSql.ToString());
            return list.ToList();
        }

        /// <summary>
        /// 根据资讯分类获取前几条条资讯信息
        /// </summary>
        /// <param name="articleCategoryId">资讯分类Id</param>
        /// <param name="num">显示条数</param>
        /// <returns></returns>
        public async Task<List<ArticleEntity>> GetArticleList(int num)
        {
            var page = new Pagination() { PageIndex = 1, PageSize = num };
            page.Sort = "BaseModifyTime";
            page.SortType = "Desc";
            StringBuilder strSql = new StringBuilder();
            strSql.Append($" SELECT * FROM d_Article WHERE BaseIsDelete = 0 AND IsRecommend = 1 AND Statuz = 2 ");
            var list = await this.BaseRepository().FindList<ArticleEntity>(strSql.ToString(),null, page);
            return list.ToList();
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(ArticleEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(ArticleEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update d_Article set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
                strSql = $"update d_Article set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<ArticleEntity, bool>> ListFilter(ArticleListParam param)
        {
            var expression = LinqExtensions.True<ArticleEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
            }
            return expression;
        }

        private List<DbParameter> ListFilter(ArticleListParam param, StringBuilder strSql)
        {
            strSql.Append($@"  SELECT * From (
                SELECT A.Id,A.BaseIsDelete,A.BaseModifyTime ,A.Cid,A.Title,A.ShortTitle,A.ImageUrl,A.Statuz,AC.Name AS ClassName,A.IsRecommend,A.Sort,A.UnitId 
                ,AC.ConfigCode
                FROM  d_Article AS A
                INNER JOIN  d_ArticleCategory AS AC ON A.Cid = AC.Id
            ) as tb1 WHERE   BaseIsDelete = 0 ");
            var parameter = new List<DbParameter>();

            if (param.UnitId != 100000000000000001)
            {
                strSql.Append(" AND UnitId = @UnitId ");
                parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));

            }

            if (param != null)
            {
                if (param.Cid != null && param.Cid != -1)
                {
                    strSql.Append(" AND Cid = @Cid ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Cid", param.Cid));
                }

                if (!string.IsNullOrEmpty(param.Key))
                {
                    strSql.Append(" AND (Title LIKE @Name OR ShortTitle LIKE @Name) ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Name", $"%{param.Key}%"));
                }

                if (param.Statuz != -10000)
                {
                    strSql.Append(" AND Statuz = @Statuz ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Statuz", param.Statuz));
                }
                if (param.ConfigCode != null && param.ConfigCode.Length > 0)
                {
                    strSql.Append(" AND ConfigCode = @ConfigCode ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ConfigCode", param.ConfigCode));
                }
            }
            return parameter;
        }
        #endregion
    }
}
