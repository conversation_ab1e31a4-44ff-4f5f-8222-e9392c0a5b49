﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.SystemManage;
using Dqy.Syjx.Model.Param.SystemManage;
using Dqy.Syjx.Util.Tools;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Model.Result.OrganizationManage;
using Senparc.Weixin.WxOpen.AdvancedAPIs.Template;
using Microsoft.Extensions.Primitives;
using Dqy.Syjx.Service.OrganizationManage;
using Dqy.Syjx.Model.Param.OrganizationManage;
using Dqy.Syjx.Entity.OrganizationManage;
using NPOI.SS.UserModel;
using Dynamitey.DynamicObjects;

namespace Dqy.Syjx.Service.SystemManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-09-10 17:20
    /// 描 述：服务类
    /// </summary>
    public class StaticDictionaryService : RepositoryFactory
    {
        private CourseSectionService courseSectionService = new CourseSectionService();

        #region 获取数据
        public async Task<List<StaticDictionaryEntity>> GetList(StaticDictionaryListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<StaticDictionaryEntity>> GetPageList(StaticDictionaryListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        /// <summary>
        /// 获取字典集合 （可以根据课程、获取对应分类）
        /// 2：关系表DictionaryToId 存的是获取列表的Id ，DictionaryId存在的类似Pid
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<List<StaticDictionaryEntity>> GetChildList(StaticDictionaryListParam param)
        {
            var strSql = new StringBuilder();
            List<DbParameter> filter = ListFilter(param, strSql);
            var list = await this.BaseRepository().FindList<StaticDictionaryEntity>(strSql.ToString(), filter.ToArray());
            return list.ToList();
        }

        /// <summary>
        /// 获取字典集合（可以班级、获取学科）
        /// 2：关系表DictionaryId 存的是获取列表的Id ，DictionaryToId存在的类似Pid
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<List<StaticDictionaryEntity>> GetChildToList(StaticDictionaryListParam param)
        {
            var strSql = new StringBuilder();
            List<DbParameter> filter = ListFilterTo(param, strSql);
            var list = await this.BaseRepository().FindList<StaticDictionaryEntity>(strSql.ToString(), filter.ToArray());
            var list2 = new List<StaticDictionaryEntity>();
            if (list != null && list.Count() > 0)
            {
                if (param.OptType == 7)
                {
                    var expression = ListFilter(new StaticDictionaryListParam() { TypeCode = param.TypeCode, Ids = string.Join(',', list.Select(m => m.DictionaryPid).Distinct()) });
                    var templist = await this.BaseRepository().FindList(expression);
                    list2 = templist.ToList();
                }
                else
                {
                    list2 = (from item in list
                             group item by item.DictionaryId into itemnew
                             orderby itemnew.FirstOrDefault().Sequence
                             select new StaticDictionaryEntity()
                             {
                                 DictionaryId = itemnew.FirstOrDefault().DictionaryId,
                                 DicName = itemnew.FirstOrDefault().DicName,
                                 Nature = itemnew.FirstOrDefault().Nature,
                                 Sequence = itemnew.FirstOrDefault().Sequence,
                                 TypeCode = itemnew.FirstOrDefault().TypeCode,
                                 Pid = itemnew.FirstOrDefault().Pid
                             }).ToList();
                }
            }
            return list2;
        }

        /// <summary>
        /// 获取字典集合（可以班级、获取学科）
        /// 2：关系表DictionaryId 存的是获取列表的Id ，DictionaryToId存在的类似Pid
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<List<StaticDictionaryEntity>> GetAllList(StaticDictionaryListParam param)
        {
            var strSql = new StringBuilder();
            List<DbParameter> filter = ListFilterTo(param, strSql);
            var list = await this.BaseRepository().FindList<StaticDictionaryEntity>(strSql.ToString(), filter.ToArray());
            return list.ToList();
        }

        public async Task<StaticDictionaryEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<StaticDictionaryEntity>(id);
        }

        /// <summary>
        /// 根据年级名称获取年级信息
        /// </summary>
        /// <param name="gradeName"></param>
        /// <returns></returns>
        public async Task<StaticDictionaryEntity> GetEntityByGradeName(string gradeName)
        {
            return await this.BaseRepository().FindEntity<StaticDictionaryEntity>(f => f.TypeCode == "1003" && f.DicName == gradeName);
        }

        /// <summary>
        /// 获取实体
        /// </summary>
        /// <param name="id">根据字典DictionaryId获取</param>
        /// <param name="typecode">数据类型</param>
        /// <returns></returns>
        public async Task<StaticDictionaryEntity> GetEntityByDictionaryId(long id, string typecode)
        {
            var expression = LinqExtensions.True<StaticDictionaryEntity>();
            expression.And(m => m.BaseIsDelete == 0);
            expression.And(m => m.DictionaryId == id);
            expression.And(m => m.TypeCode == typecode);
            return await this.BaseRepository().FindEntity<StaticDictionaryEntity>(expression);
        }

        public async Task<int> GetMaxKey(StaticDictionaryListParam param)
        {
            var parameter = new List<DbParameter>();
            parameter.Add(DbParameterExtension.CreateDbParameter("@TypeCode", param.TypeCode));
            object result = await this.BaseRepository().FindObject(" SELECT MAX(DictionaryId) FROM  sys_static_dictionary Where BaseIsDelete = 0 AND TypeCode = @TypeCode ", parameter.ToArray());
            int key = result.ParseToInt();
            key++;
            return key;
        }

        public async Task<List<StaticDictionaryEntity>> GetSubjectIdzList(StaticDictionaryListParam param)
        {
            var strSql = new StringBuilder();
            List<DbParameter> filter = SubjectIdzListFilter(param, strSql);
            var list = await this.BaseRepository().FindList<StaticDictionaryEntity>(strSql.ToString(), filter.ToArray());
            if (list != null)
            {
                list = list.DistinctBy(m => m.Id).ToList();
            }
            return list.ToList();
        }

        public async Task<List<StaticDictionaryEntity>> GetSchoolStageIdzList(StaticDictionaryListParam param)
        {
            var strSql = new StringBuilder();
            List<DbParameter> filter = GetSchoolStageIdzList(param, strSql);
            var list = await this.BaseRepository().FindList<StaticDictionaryEntity>(strSql.ToString(), filter.ToArray());
            if (list != null)
            {
                list = list.DistinctBy(m => m.Id).ToList();
            }
            return list.ToList();
        }

        public async Task<List<StaticDictionaryEntity>> GetClassBySubjectList(StaticDictionaryListParam param)
        {
            var strSql = new StringBuilder();
            List<DbParameter> filter = ClassBySubjectListFilter(param, strSql);
            var list = await this.BaseRepository().FindList<StaticDictionaryEntity>(strSql.ToString(), filter.ToArray());
            if (list != null)
            {
                list = list.DistinctBy(m => m.Id).ToList();
            }
            var list2 = list.ToList();
            if (list != null && list.Count() > 0)
            {
                //添加父级
                var expression = ListFilter(new StaticDictionaryListParam() { TypeCode = param.TypeCode, Ids = string.Join(',', list.Select(m => m.Pid).Distinct()) });
                var pList = await this.BaseRepository().FindList(expression);
                if (pList != null && pList.Count() > 0)
                {
                    list2.AddRange(pList);
                }
            }
            return list2;
        }


        /// <summary>
        /// 根据学校属性获取所有年级
        /// </summary>
        /// <param name="schoolProp"></param>
        /// <returns></returns>
        public async Task<IEnumerable<StaticDictionaryEntity>> GetGradeBySchoolProp(int schoolProp)
        {
            string sql = @$"
                SELECT D2.DictionaryId ,D2.DicName
                FROM  sys_dictionary_relation AS DR
                INNER JOIN  sys_static_dictionary AS D1 ON DR.DictionaryId = D1.DictionaryId
                        AND D1.TypeCode = '{DicTypeCodeEnum.SchoolStage.ParseToInt()}' AND D1.BaseIsDelete = 0
                inner JOIN  sys_static_dictionary AS D2 ON DR.DictionaryToId = D2.DictionaryId
                        AND D2.TypeCode = '{DicTypeCodeEnum.Grade.ParseToInt()}' AND D2.BaseIsDelete = 0
                WHERE D1.DictionaryId IN (
	                SELECT SD.DictionaryId
	                FROM  sys_dictionary_relation AS SDR
	                INNER JOIN  sys_static_dictionary AS SD ON SDR.DictionaryToId = SD.DictionaryId
                        AND SD.TypeCode = '{DicTypeCodeEnum.SchoolStage.ParseToInt()}' AND SD.BaseIsDelete = 0
	                WHERE SDR.DictionaryId = '{schoolProp}'
                )
            ";
            return await this.BaseRepository().FindList<StaticDictionaryEntity>(sql);
        }

        /// <summary>
        /// 根据字典值与字典类型查找字典数据
        /// </summary>
        /// <param name="dictionaryId"></param>
        /// <param name="typeCode"></param>
        /// <returns></returns>
        public async Task<StaticDictionaryEntity> GetEntityByDictionaryId(int dictionaryId, string typeCode)
        {
            var expression = LinqExtensions.True<StaticDictionaryEntity>();
            expression = expression.And(m => m.BaseIsDelete == IsEnum.No.ParseToInt());
            expression = expression.And(m => m.DictionaryId == dictionaryId);
            expression = expression.And(m => m.TypeCode == typeCode);
            return await this.BaseRepository().FindEntity<StaticDictionaryEntity>(expression);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(StaticDictionaryEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if (ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update sys_static_dictionary set BaseIsDelete = 1 where DictionaryId in ({ids}) ";
            }
            else
            {
                strSql = $"update sys_static_dictionary set BaseIsDelete = 1 where DictionaryId = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        /// <summary>
        /// 获取字典查询条件
        /// </summary>
        /// <param name="param">OptType=1:标识</param>
        /// <returns></returns>
        private Expression<Func<StaticDictionaryEntity, bool>> ListFilter(StaticDictionaryListParam param)
        {
            var expression = LinqExtensions.True<StaticDictionaryEntity>();
            expression = expression.And(m => m.BaseIsDelete == IsEnum.No.ParseToInt());
            if (param != null)
            {
                if (!param.TypeCode.IsEmpty())
                {
                    expression = expression.And(m => m.TypeCode == param.TypeCode && m.DictionaryId != 1002000);

                    if (param.OptType == 1)
                    {
                        expression = expression.And(m => m.DictionaryId > param.DictionaryId);
                    }
                    else if (param.OptType == 3 && param.Id != null && param.Id > 0)
                    {
                        expression = expression.And(m => m.Id != param.Id);
                    }
                    else if (param.OptType == 4)
                    {
                        expression = expression.And(m => m.Pid > 0);
                    }
                    else if (param.OptType == 5)
                    {
                        if (param.Pid > 0)
                        {
                            expression = expression.And(m => m.Pid == param.Pid);
                        }
                        else
                        {
                            expression = expression.And(m => m.Pid == param.Pid || m.Pid == null);
                        }
                    }
                    else if (param.OptType == 6)
                    {
                        //根据属性获取值
                        expression = expression.And(m => m.Nature == param.Nature);
                    }
                }
                if (param.OptType != 1 && param.DictionaryId != 0)
                {
                    expression = expression.And(m => m.DictionaryId == param.DictionaryId);
                }
                if (param.DicName != null && param.DicName.Length > 0)
                {
                    expression = expression.And(m => m.DicName.Contains(param.DicName));
                }
                if (param.Memo != null && param.Memo.Length > 0)
                {
                    expression = expression.And(m => m.Memo.Contains(param.Memo));
                }
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    int[] idArr = TextHelper.SplitToArray<int>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.DictionaryId.Value));
                }
                if (!string.IsNullOrEmpty(param.DictionaryIds))
                {
                    int[] idArr = TextHelper.SplitToArray<int>(param.DictionaryIds, ',');
                    expression = expression.And(t => idArr.Contains(t.DictionaryId.Value));
                }
                if (param.Nature > 0)
                {
                    expression = expression.And(m => m.Nature == param.Nature);
                }
                if (param.TypeCodeArr != null && param.TypeCodeArr.Count > 0)
                {
                    expression = expression.And(t => param.TypeCodeArr.Contains(t.TypeCode));
                }
                if (param.Pidgt != -10000 && param.Pidgt != -1)
                {
                    expression = expression.And(m => m.Pid > param.Pidgt);
                }
            }
            return expression;
        }
        /// <summary>
        /// 获取字典集合
        /// 1：比如根据学段，获取班级，
        /// 2：关心表DictionaryId 存的是获取列表的Id ，DictionaryToId存在的类似Pid
        /// </summary>
        /// <param name="param"></param>
        /// <param name="strSql"></param>
        /// <returns></returns>
        private List<DbParameter> ListFilter(StaticDictionaryListParam param, StringBuilder strSql)
        {
            strSql.Append(@"  SELECT * From (
                              SELECT  c3.Id ,
                              c3.BaseIsDelete ,
                              c3.BaseCreateTime ,
                              c3.BaseModifyTime ,
                              c3.BaseCreatorId ,
                              c3.BaseModifierId ,
                              c3.BaseVersion ,
                              c3.DictionaryId ,
                              c3.TypeCode ,
                              c3.TypeName ,
                              c3.DicName ,
                              c3.Memo ,
                              c3.Sequence ,
                              c3.Nature ,
                              c3.Statuz ,
		                      a1.DictionaryId AS Pid ,
		                      a1.Statuz AS Pstatuz ,
		                      a1.TypeCode AS PTypeCode ,
                              a1.DicName AS PName
                              FROM  sys_static_dictionary AS a1
                              INNER JOIN  sys_dictionary_relation AS b2 ON a1.DictionaryId = b2.DictionaryId
                              INNER JOIN  sys_static_dictionary AS c3 ON b2.DictionaryToId = c3.DictionaryId
                          ) as tb1 WHERE BaseIsDelete = 0 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.TypeCode))
                {
                    strSql.Append(" AND TypeCode = @TypeCode");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@TypeCode", param.TypeCode));
                }
                if (!string.IsNullOrEmpty(param.PTypeCode))
                {
                    strSql.Append(" AND PTypeCode = @PTypeCode");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@PTypeCode", param.PTypeCode));
                }
                if (param.DictionaryId > 0)
                {
                    strSql.Append(" AND DictionaryId = @DictionaryId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId", param.DictionaryId));
                }
                if (param.OptType == 201)
                {
                    if (param.Pid > 0)
                    {
                        strSql.Append(" AND Pid = @Pid ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@Pid", param.Pid));
                    }
                    else
                    {
                        if (!param.Pids.IsEmpty())
                        {
                            var pidArr = param.Pids.Split(",");
                            if (pidArr != null && param.Pids.Count() > 0)
                            {
                                List<string> pidList = new List<string>();
                                foreach (var item in pidArr)
                                {
                                    if (item != null && item.Length > 0)
                                    {
                                        long tempPid = 0;
                                        long.TryParse(item, out tempPid);
                                        if (tempPid > 0)
                                        {
                                            pidList.Add(string.Format(" Pid = {0} ", item));
                                        }
                                    }
                                }
                                if (pidList.Count > 0)
                                {
                                    strSql.Append($" AND ({string.Join(" OR ", pidList)}) ");
                                }
                            }
                        }
                    }

                }
                else
                {
                    if (param.Pid > 0)
                    {
                        strSql.Append(" AND Pid = @Pid ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@Pid", param.Pid));
                    }
                    if (!param.Pids.IsEmpty())
                    {
                        var pidArr = param.Pids.Split(",");
                        if (pidArr != null && param.Pids.Count() > 0)
                        {
                            List<string> pidList = new List<string>();
                            foreach (var item in pidArr)
                            {
                                if (item != null && item.Length > 0)
                                {
                                    long tempPid = 0;
                                    long.TryParse(item, out tempPid);
                                    if (tempPid > 0)
                                    {
                                        pidList.Add(string.Format(" Pid = {0} ", item));
                                    }
                                }
                            }
                            if (pidList.Count > 0)
                            {
                                strSql.Append($" AND ({string.Join(" OR ", pidList)}) ");
                            }
                        }
                    }
                }
                if (param.Statuz > -1)
                {
                    strSql.Append(" AND Statuz = @Statuz");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Statuz", param.Statuz));
                }
                if (param.Ids != null && param.Ids.Length > 0)
                {
                    var idArr = param.Ids.Split(",");
                    List<string> idList = new List<string>();
                    foreach (var item in idArr)
                    {
                        if (item != null && item.Length > 0)
                        {
                            long tempPid = 0;
                            long.TryParse(item, out tempPid);
                            if (tempPid > 0)
                            {
                                idList.Add(string.Format(" DictionaryId = {0} ", item));
                            }
                        }
                    }
                    if (idList.Count > 0)
                    {
                        strSql.Append($" AND ({string.Join(" OR ", idList)}) ");
                    }
                }
            }
            return parameter;
        }
        /// <summary>
        /// 获取字典集合
        /// 2：关系表DictionaryToId 存的是获取列表的Id ，DictionaryId存在的类似Pid
        /// </summary>
        /// <param name="param"></param>
        /// <param name="strSql"></param>
        /// <returns></returns>
        private List<DbParameter> ListFilterTo(StaticDictionaryListParam param, StringBuilder strSql)
        {
            strSql.Append(@"  SELECT * From (
                              SELECT  a1.Id ,
                              a1.BaseIsDelete ,
                              a1.BaseCreateTime ,
                              a1.BaseModifyTime ,
                              a1.BaseCreatorId ,
                              a1.BaseModifierId ,
                              a1.BaseVersion ,
                              a1.DictionaryId ,
                              a1.TypeCode ,
                              a1.TypeName ,
                              a1.DicName ,
                              a1.Memo ,
                              a1.Sequence ,
                              a1.Nature ,
                              a1.Statuz ,
                              a1.Pid AS DictionaryPid,
		                      c3.DictionaryId AS Pid ,
		                      c3.Statuz AS Pstatuz ,
		                      c3.TypeCode AS PtypeCode
                              FROM  sys_static_dictionary AS a1
                              INNER JOIN  sys_dictionary_relation AS b2 ON a1.DictionaryId = b2.DictionaryId
                              INNER JOIN  sys_static_dictionary AS c3 ON b2.DictionaryToId = c3.DictionaryId
                          ) as tb1 WHERE BaseIsDelete = 0 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.TypeCode))
                {
                    strSql.Append(" AND TypeCode = @TypeCode");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@TypeCode", param.TypeCode));
                }
                if (!string.IsNullOrEmpty(param.PTypeCode))
                {
                    strSql.Append(" AND PTypeCode = @PTypeCode");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@PTypeCode", param.PTypeCode));
                }
                if (param.Pid > 0)
                {
                    strSql.Append(" AND Pid = @Pid ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Pid", param.Pid));
                }
                if (!string.IsNullOrEmpty(param.Pids))
                {
                    var tempIds = param.Pids.Split(",").ToList().ConvertAll(m => m.ParseToInt());// < int>   //( param.Pids.Split(",");
                    if (tempIds != null && tempIds.Count > 0)
                    {
                        var tempwhere = "";
                        for (int i = 0; i < tempIds.Count; i++)
                        {
                            if (i > 0)
                            {
                                tempwhere += " OR ";
                            }
                            tempwhere += $" Pid = @Pid_{i} ";
                            parameter.Add(DbParameterExtension.CreateDbParameter($"@Pid_{i}", tempIds[i]));
                        }
                        strSql.Append($" AND ({tempwhere}) ");
                    }
                }
                if (param.Statuz > -1)
                {
                    strSql.Append(" AND Statuz = @Statuz");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Statuz", param.Statuz));
                }
                if (param.Ids != null && param.Ids.Length > 0)
                {
                    strSql.Append($" AND DictionaryId IN ({param.Ids})");
                }
                if (param.DictionaryId > 0)
                {
                    strSql.Append(" AND DictionaryId = @DictionaryId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId", param.DictionaryId));
                }
                if (param.DictionaryPid > 0)
                {
                    strSql.Append(" AND DictionaryPid = @DictionaryPid");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryPid", param.DictionaryPid));
                }
                if (param.OptType == 9)
                {
                    if (param.Nature > 0)
                    {
                        strSql.Append(" AND  ISNULL(Nature,0) <> @Nature");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@Nature", param.Nature));
                    }
                }
                else
                {
                    if (param.Nature > 0)
                    {
                        strSql.Append(" AND Nature = @Nature");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@Nature", param.Nature));
                    }
                }
            }
            return parameter;
        }


        private List<DbParameter> SubjectIdzListFilter(StaticDictionaryListParam param, StringBuilder strSql)
        {
            strSql.Append($@"  SELECT * From (
                               SELECT a1.Id ,
                              a1.BaseIsDelete ,
                              a1.BaseCreateTime ,
                              a1.BaseModifyTime ,
                              a1.BaseCreatorId ,
                              a1.BaseModifierId ,
                              a1.BaseVersion ,
                              a1.DictionaryId ,
                              a1.TypeCode ,
                              a1.TypeName ,
                              a1.DicName ,
                              a1.Memo ,
                              a1.Sequence ,
                              a1.Nature ,
                              a1.Statuz
                              FROM  sys_static_dictionary AS a1
                              INNER JOIN  bn_UserSchoolStageSubject AS course ON course.BaseIsDelete = 0 AND course.IsCurrentUnit = 1 AND course.UnitId = {param.UnitId.Value} AND course.UserId = {param.UserId.Value} AND course.SubjectIdz = a1.DictionaryId
                              INNER JOIN  sys_dictionary_relation AS dr3 ON a1.DictionaryId = dr3.DictionaryToId
							  INNER JOIN  sys_static_dictionary AS sd4 ON dr3.DictionaryId = sd4.DictionaryId
            ");
            if (param.DictionaryId > 0)
            {
                strSql.Append($@" WHERE sd4.DictionaryId = {param.DictionaryId} ");
            }
            strSql.Append($@" ) as tb1 WHERE   1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = @BaseIsDelete");
                parameter.Add(DbParameterExtension.CreateDbParameter("@BaseIsDelete", param.BaseIsDelete));
                if (param.Statuz > -1)
                {
                    strSql.Append(" AND Statuz = @Statuz");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Statuz", param.Statuz));
                }
            }
            return parameter;
        }


        private List<DbParameter> GetSchoolStageIdzList(StaticDictionaryListParam param, StringBuilder strSql)
        {
            strSql.Append($@"  SELECT * From (
                              SELECT a1.Id ,
                              a1.BaseIsDelete ,
                              a1.BaseCreateTime ,
                              a1.BaseModifyTime ,
                              a1.BaseCreatorId ,
                              a1.BaseModifierId ,
                              a1.BaseVersion ,
                              a1.DictionaryId ,
                              a1.TypeCode ,
                              a1.TypeName ,
                              a1.DicName ,
                              a1.Memo ,
                              a1.Sequence ,
                              a1.Nature ,
                              a1.Statuz
                              FROM  sys_static_dictionary AS a1
                              INNER JOIN  bn_UserSchoolStageSubject AS uss11 ON uss11.BaseIsDelete = 0 AND uss11.IsCurrentUnit = 1 AND uss11.UnitId = {param.UnitId.Value} AND uss11.UserId =  {param.UserId.Value} AND uss11.SubjectIdz IS NOT NULL AND  CONVERT(NVARCHAR(255),a1.DictionaryId) = uss11.SchoolStageIdz
							  INNER JOIN  sys_dictionary_relation AS d3 ON a1.DictionaryId = d3.DictionaryToId ");
            if (param.DictionaryId > 0)
            {
                strSql.Append($@" Where  d3.DictionaryId = {param.DictionaryId} ");
            }
            strSql.Append($@" ) as tb1 WHERE   1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = @BaseIsDelete");
                parameter.Add(DbParameterExtension.CreateDbParameter("@BaseIsDelete", param.BaseIsDelete));
                if (param.Statuz > -1)
                {
                    strSql.Append(" AND Statuz = @Statuz");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Statuz", param.Statuz));
                }
            }
            return parameter;
        }

        private List<DbParameter> ClassBySubjectListFilter(StaticDictionaryListParam param, StringBuilder strSql)
        {
            // 验证和清理 param.Ids
            var cleanIds = StringFilter.ValidateAndCleanIds(param.Ids);
            if (cleanIds == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");

            strSql.Append($@" SELECT * From (
                              SELECT  a1.Id ,
                              a1.BaseIsDelete ,
                              a1.BaseCreateTime ,
                              a1.BaseModifyTime ,
                              a1.BaseCreatorId ,
                              a1.BaseModifierId ,
                              a1.BaseVersion ,
                              a1.DictionaryId ,
                              a1.TypeCode ,
                              a1.TypeName ,
                              a1.DicName ,
                              a1.Memo ,
                              a1.Sequence ,
                              a1.Nature ,
                              a1.Statuz ,
                              a1.Pid ,
                              sd5.DicName AS NatureName ,
                              IsNull(sd5.Nature,0) AS NatureType
                              FROM  sys_static_dictionary AS a1
                              INNER JOIN  sys_dictionary_relation AS b2 ON a1.DictionaryId = b2.DictionaryId
                              INNER JOIN  sys_static_dictionary AS c3 ON b2.DictionaryToId = c3.DictionaryId
                              INNER JOIN  sys_static_dictionary AS sd5 ON ISNULL(a1.Nature,0) = sd5.DictionaryId AND sd5.TypeCode = '{DicTypeCodeEnum.Nature.ParseToInt()}'
                              WHERE c3.DictionaryId IN ({cleanIds})
            ) as tb1 WHERE   1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = @BaseIsDelete");
                parameter.Add(DbParameterExtension.CreateDbParameter("@BaseIsDelete", param.BaseIsDelete));
                if (param.Statuz > -1)
                {
                    strSql.Append(" AND Statuz = @Statuz");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Statuz", param.Statuz));
                }
            }
            return parameter;
        }


        /// <summary>
        /// 实验（专用）室二级分类
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<List<StaticDictionaryEntity>> GetFunroomTwoClassify(StaticDictionaryListParam param)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(@$"  SELECT  * From (
                    SELECT
                    sd1.Id ,
				    sd1.BaseIsDelete ,
				    sd1.BaseCreateTime ,
				    sd1.BaseModifyTime ,
				    sd1.BaseCreatorId ,
				    sd1.BaseModifierId ,
				    sd1.BaseVersion ,
				    sd1.DictionaryId ,
				    sd1.TypeCode ,
				    sd1.TypeName ,
				    sd1.DicName ,
				    sd1.Memo ,
				    sd1.Sequence ,
				    sd1.Pid ,
				    sd1.Statuz
                    ,sdr3.DictionaryToId AS SchoolStage
				    ,nat4.Nature AS Nature
                    FROM  sys_static_dictionary AS sd1
					INNER JOIN  sys_static_dictionary AS nat4 ON sd1.Nature = nat4.DictionaryId AND nat4.BaseIsDelete = 0
					INNER JOIN  sys_dictionary_relation AS sdr2 ON sd1.DictionaryId = sdr2.DictionaryId AND sdr2.BaseIsDelete = 0 
					INNER JOIN  sys_dictionary_relation AS sdr3 ON sdr2.DictionaryToId = sdr3.DictionaryId AND sdr3.BaseIsDelete = 0
                    WHERE sd1.BaseIsDelete = 0
                ) as tb1 WHERE 1 = 1 AND tb1.Statuz = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.Pid > 0)
                {
                    strSql.Append(" AND SchoolStage = @SchoolStage ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStage", param.Pid));
                }
                if (param.OptType == 1)
                {
                    if (param.Nature > 0)
                    {
                        strSql.Append(" AND Nature <> @Nature ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@Nature", param.Nature));
                    }
                }
            }
            var list = await this.BaseRepository().FindList<StaticDictionaryEntity>(strSql.ToString(), parameter.ToArray());
            return list.ToList();
        }


        /// <summary>
        /// 根据单位Id获取单位年级信息
        /// </summary>
        /// <param name="unitId">单位Id</param>
        /// <param name="dictionaryId">字典值Id</param>
        /// <returns></returns>
        public async Task<List<StaticDictionaryEntity>> GetUnitGradeList(long unitId, int dictionaryId)
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat(@$"
                    SELECT D3.DictionaryId,D3.DicName,ISNULL(US.IsSet,0) AS DictionaryPid
                    FROM  up_SchoolExtension AS SE
                    INNER JOIN  sys_static_dictionary AS D1 ON SE.SchoolProp = D1.DictionaryId AND TypeCode = '1001'
                    INNER JOIN  sys_dictionary_relation AS DR1 ON D1.DictionaryId = DR1.DictionaryId
                    INNER JOIN  sys_static_dictionary AS D2 ON DR1.DictionaryToId = D2.DictionaryId AND D2.TypeCode = '1002'
                    INNER JOIN  sys_dictionary_relation AS DR2 ON D2.DictionaryId = DR2.DictionaryId
                    INNER JOIN  sys_static_dictionary AS D3 ON DR2.DictionaryToId = D3.DictionaryId AND D3.TypeCode = '1003'
                    LEFT JOIN (SELECT DISTINCT GradeId,UnitId,IsSet FROM  up_UnitSchedule) AS US ON D3.DictionaryId = US.GradeId  AND US.UnitId = {unitId}
                    WHERE SE.UnitId = {unitId}");
            if (dictionaryId > 0)
            {
                sb.AppendFormat($" AND D3.DictionaryId <> {dictionaryId}");
            }
            var listDic = await this.BaseRepository().FindList<StaticDictionaryEntity>(sb.ToString());

            var listCourse = await courseSectionService.GetList(new CourseSectionListParam() { UnitId = unitId, Statuz = 1 });
            var courseList = listCourse.GroupBy(x => x.GradeId)
                  .Select(g => new
                  {
                      GradeId = g.Key,
                      WeekIds = String.Join(",", g.Where(f => f.GradeId > 0).Select(x => x.WeekId)),
                  })
                  .ToList();

            var list = listDic.Join(courseList, p1 => p1.DictionaryId, p2 => p2.GradeId, (p1, p2) =>
          new StaticDictionaryEntity()
          {
              DictionaryId = p1.DictionaryId,
              DicName = p1.DicName,
              DictionaryPid = p1.DictionaryPid,
              WeekIds = p2.WeekIds

          }).ToList();
            var listAll = listDic.Select(f => new { DictionaryId = f.DictionaryId, DicName = f.DicName }).ToList();
            var listNew = list.Select(f => new { DictionaryId = f.DictionaryId, DicName = f.DicName }).ToList();
            var listAdd = Enumerable.Except(listAll, listNew).ToList();
            foreach (var item in listAdd)
            {
                list.Add(new StaticDictionaryEntity()
                {
                    DictionaryId = item.DictionaryId,
                    DicName = item.DicName,
                    DictionaryPid = 0,
                    WeekIds = ""
                });
            }
            list = list.OrderBy(f => f.DictionaryId).ToList();

            return list;
        }

        /// <summary>
        /// 查询单位年级信息
        /// </summary>
        /// <param name="unitId">单位Id</param>
        /// <returns></returns>
        public async Task<List<GradeModel>> GetUnitGradeList(long unitId)
        {
            StringBuilder sb = new StringBuilder();
            sb.Append(@$"SELECT D3.DictionaryId AS GradeId,D3.DicName AS GradeName
                        FROM  up_SchoolExtension AS SE
                        INNER JOIN  sys_static_dictionary AS D1 ON SE.SchoolProp = D1.DictionaryId AND TypeCode = '1001'
                        INNER JOIN  sys_dictionary_relation AS DR1 ON D1.DictionaryId = DR1.DictionaryId
                        INNER JOIN  sys_static_dictionary AS D2 ON DR1.DictionaryToId = D2.DictionaryId AND D2.TypeCode = '1002'
                        INNER JOIN  sys_dictionary_relation AS DR2 ON D2.DictionaryId = DR2.DictionaryId
                        INNER JOIN  sys_static_dictionary AS D3 ON DR2.DictionaryToId = D3.DictionaryId AND D3.TypeCode = '1003'
                        WHERE SE.UnitId = {unitId}");
            var list = await this.BaseRepository().FindList<GradeModel>(sb.ToString());
            return list.ToList();
        }

        /// <summary>
        /// 查询周信息
        /// </summary>
        /// <returns></returns>
        public async Task<List<WeekModel>> GetWeekList()
        {
            string sb = @"SELECT DictionaryId AS WeekId,DicName AS WeekName
                         FROM sys_static_dictionary WHERE TypeCode = '1015' AND Statuz = 1";
            var list = await this.BaseRepository().FindList<WeekModel>(sb.ToString());
            return list.ToList();
        }


        /// <summary>
        /// 根据单位Id获取单位学段信息
        /// </summary>
        /// <param name="unitId">单位Id</param>
        /// <returns></returns>
        public async Task<List<StaticDictionaryEntity>> GetSchoolStageList(long unitId)
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat(@$"
            SELECT a1.SchoolNature ,a1.SchoolProp ,b1.DicName AS SchoolPropName ,dicStoge.DictionaryId, dicStoge.DicName AS StogeName
            FROM  up_SchoolExtension AS a1
            INNER JOIN  sys_static_dictionary AS b1 ON b1.TypeCode = '{DicTypeCodeEnum.SchoolProp.ParseToInt()}' AND a1.SchoolProp = b1.DictionaryId
            INNER JOIN  sys_dictionary_relation AS dicrel ON b1.DictionaryId = dicrel.DictionaryId AND dicrel.RelationType = 1
            INNER JOIN  sys_static_dictionary AS dicStoge ON dicStoge.TypeCode = '{DicTypeCodeEnum.SchoolStage.ParseToInt()}' AND dicrel.DictionaryToId = dicStoge.DictionaryId
            WHERE a1.BaseIsDelete = 0 AND b1.BaseIsDelete = 0 AND a1.UnitId = {unitId}");

            var list = await this.BaseRepository().FindList<StaticDictionaryEntity>(sb.ToString());
            return list.ToList();
        }


        #endregion
    }
}
