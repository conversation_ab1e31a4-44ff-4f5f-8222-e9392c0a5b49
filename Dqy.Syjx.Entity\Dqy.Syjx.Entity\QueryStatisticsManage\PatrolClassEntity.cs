﻿using System;
using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations.Schema;
using Dqy.Syjx.Util;
using Dynamitey.DynamicObjects;
using System.Net.Mail;
using System.Collections.Generic;
using Dqy.Syjx.Entity.BusinessManage;

namespace Dqy.Syjx.Entity.QueryStatisticsManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2022-05-19 14:08
    /// 描 述：实体类
    /// </summary>
    [Table("bn_PatrolClass")]
    public class PatrolClassEntity : BaseExtensionEntity
    {
        /// <summary>
        /// 所在单位id
        /// </summary>
        /// <returns></returns>
        [JsonConverter(typeof(StringJsonConverter))]
        public long UnitId { get; set; }

        [NotMapped]
        public string UnitName { get; set; }
        /// <summary>
        /// 功能室Id
        /// </summary>
        /// <returns></returns>
        [JsonConverter(typeof(StringJsonConverter))]
        public long FunRoomId { get; set; }

        [NotMapped]
        public string FunRoomName { get; set; }
        /// <summary>
        /// 一级分类（字典表sys_static_dictionary  typecode=1006 A字段：DictionaryId）
        /// </summary>
        /// <returns></returns>
        public int DictionaryId1006A { get; set; }
        [NotMapped]
        public string ClassNameA { get; set; }
        /// <summary>
        /// 二级分类（字典表sys_static_dictionary  typecode=1006 字段：DictionaryId）
        /// </summary>
        /// <returns></returns>
        public int DictionaryId1006B { get; set; }
        [NotMapped]
        public string ClassNameB { get; set; }
        /// <summary>
        /// 学期
        /// </summary>
        /// <returns></returns>
        public string SchoolTermName { get; set; }
        /// <summary>
        /// 属性
        /// </summary>
        /// <returns></returns>
        public int RoomAttribute { get; set; }
        [NotMapped]
        public string RoomAttributeName { get; set; }
        /// <summary>
        /// 适用学段（字典表sys_static_dictionary  typecode=1002字段：DictionaryId）
        /// </summary>
        /// <returns></returns>
        public string SchoolStage { get; set; }
        /// <summary>
        /// 学段名称
        /// </summary>
        [NotMapped]
        public string StageName { get; set; }
        /// <summary>
        /// 任课学科（字典表sys_static_dictionary typecode=1005 字段：DictionaryId）
        /// </summary>
        /// <returns></returns>
        public int DictionaryId1005 { get; set; }
        [NotMapped]
        public string SubjectName { get; set; }
        /// <summary>
        /// 任课节次
        /// </summary>
        /// <returns></returns>
        [JsonConverter(typeof(StringJsonConverter))]
        public long CourseSectionId { get; set; }

        [NotMapped]
        public string CourseSectionNo { get; set; }
        /// <summary>
        /// 节次开始时间
        /// </summary>
        /// <returns></returns>
        public string BeginTime { get; set; }
        /// <summary>
        /// 节次结束时间
        /// </summary>
        /// <returns></returns>
        public string EndTime { get; set; }
        /// <summary>
        /// 冬季开始时间
        /// </summary>
        [NotMapped]
        public string DjBeginTime { get; set; }
        /// <summary>
        /// 冬季结束时间
        /// </summary>
        [NotMapped]
        public string DjEndTime { get; set; }
        /// <summary>
        /// 使用日期
        /// </summary>
        /// <returns></returns>
        public DateTime UseDate { get; set; }
        /// <summary>
        /// 是否正常开课
        /// </summary>
        /// <returns></returns>
        public int IsClass { get; set; }
        /// <summary>
        /// 摄像头Id
        /// </summary>
        /// <returns></returns>
        [JsonConverter(typeof(StringJsonConverter))]
        public long? CameraId { get; set; }
        /// <summary>
        /// 摄像头编号
        /// </summary>
        /// <returns></returns>
        public string SrcName { get; set; }
        /// <summary>
        /// 摄像头IndexCode
        /// </summary>
        /// <returns></returns>
        public string CameraIndexCode { get; set; }
        /// <summary>
        /// 状态(0禁用 1启用)
        /// </summary>
        /// <returns></returns>
        public int Statuz { get; set; }

        ///// <summary>
        ///// 是否当前课程节次(0 否  1 是)
        ///// </summary>
        //public int IsCurrenSection { get; set; }

        /// <summary>
        /// 正常开课次数
        /// </summary>
        [NotMapped]
        public int NormalClass { get; set; }

        /// <summary>
        /// 区县Id
        /// </summary>
        [NotMapped]
        [JsonConverter(typeof(StringJsonConverter))]
        public long CountyId { get; set; }

        /// <summary>
        /// 区县名称
        /// </summary>
        [NotMapped]
        public string CountyName { get; set; }

        /// <summary>
        /// 市级Id
        /// </summary>
        [NotMapped]
        [JsonConverter(typeof(StringJsonConverter))]
        public long CityId { get; set; }

        /// <summary>
        /// 市级名称
        /// </summary>
        [NotMapped]
        public string CityName { get; set; }

        /// <summary>
        /// 学期开始年度
        /// </summary>
        [NotMapped]
        public int SchoolYearStart { get; set; }
        /// <summary>
        /// 学期
        /// </summary>
        [NotMapped]
        public int SchoolTerm { get; set; }
        /// <summary>
        /// 巡课图片列表
        /// </summary>
        [NotMapped]
        public List<AttachmentEntity> PicPath { get; set; }

        /// <summary>
        /// 年级Id
        /// </summary>
        [NotMapped]
        public int GradeId { get; set; }
        /// <summary>
        /// 年级名称
        /// </summary>
        [NotMapped]
        public string GradeName {
            get { return UseDate.Date.DayOfWeek.ToString(); }
        }
        /// <summary>
        /// 星期Id
        /// </summary>
        [NotMapped]
        public int WeekId { get; set;}
        /// <summary>
        /// 星期名称
        /// </summary>
        [NotMapped]
        public string WeekName { get; set; }

        /// <summary>
        /// 最大人数
        /// </summary>
        public int MaxPeopleNumber { get; set; }
    }
}
