﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using System.Web;
using Microsoft.AspNetCore.Mvc;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Entity;
using Dqy.Syjx.Model;
using Dqy.Syjx.Web.Controllers;
using Dqy.Syjx.Entity.OrganizationManage;
using Dqy.Syjx.Model.Input.OrganizationManage;
using Dqy.Syjx.Business.OrganizationManage;
using Dqy.Syjx.Model.Param.OrganizationManage;
using Dqy.Syjx.Model.Param;
using Dqy.Syjx.Model.Result;

namespace Dqy.Syjx.Web.Areas.OrganizationManage.Controllers
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-09-29 17:19
    /// 描 述：地址管理控制器类
    /// </summary>
    [Area("OrganizationManage")]
    public class AddressController :  BaseController
    {
        private AddressBLL addressBLL = new();

        #region 视图功能
        [AuthorizeFilter("organization:address:view")]
        public ActionResult AddressIndex()
        {
            return View();
        }
        public ActionResult HouseForm()
        {
            return View();
        }
        public ActionResult RoomForm()
        {
            return View();
        }
        public IActionResult AddressImport()
        {
            return View();
        }
        public ActionResult SetDepartmentForm()      
        {
            return View();
        }

        public ActionResult AddRoom() 
        {
            return View();
        }
        #endregion

        #region 获取数据
        [HttpGet]
        [AuthorizeFilter("organization:address:search")]
        public async Task<ActionResult> GetListJson(AddressListParam param)
        {
            TData<List<AddressEntity>> obj = await addressBLL.GetList(param);
            return Json(obj);
        }

        [HttpGet]
        public async Task<ActionResult> GetFormJson(long id)
        {
            TData<AddressEntity> obj = await addressBLL.GetEntity(id);
            return Json(obj);
        }

        [HttpGet]
        public async Task<ActionResult> GetZtreeListJson(AddressListParam param)
        {
            TData<List<ZtreeInfo>> obj = await addressBLL.GetZtreeList(param);
            return Json(obj);
        }

        /// <summary>
        /// 查询地址列表信息
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpGet]
        [AuthorizeFilter("organization:address:search")]
        public async Task<ActionResult> GetAddressListJson(AddressListParam param)
        {

            TData<List<AddressEntity>> obj = await addressBLL.GetAddressList(param);
            return Json(obj);
        }
        #endregion

        #region 提交数据
        [HttpPost]
        [AuthorizeFilter("organization:address:add,organization:address:edit")]
        public async Task<ActionResult> SaveFormJson(AddressInputModel model)
        {
            TData<string> obj = await addressBLL.SaveForm(model);
            return Json(obj); 
        }
        [HttpPost]
        [AuthorizeFilter("organization:address:add,organization:address:edit")]
        public async Task<ActionResult> SaveDepartmentFormJson(string ids,long departmentid)
        {
            TData obj = await addressBLL.SaveDepartmentForm(ids, departmentid);
            return Json(obj); 
             }
        [HttpPost]
        [AuthorizeFilter("organization:address:delete")]
        public async Task<ActionResult> DeleteFormJson(string ids)
        {
            TData obj = await addressBLL.DeleteForm(ids);
            return Json(obj);
        }
        [HttpPost]
        public async Task<IActionResult> ImportAddressJson(ImportParam param)
        {
            List<AddressEntity> list = new ExcelHelper<AddressEntity>().ImportFromExcel(param.FilePath);
            TData obj = await addressBLL.ImportAddress(param, list);
            return Json(obj);
        }

        [HttpPost]
        [AuthorizeFilter("organization:address:delete")]
        public async Task<ActionResult> DeleteByIds(string ids)
        {
            TData obj = await addressBLL.DeleteByIds(ids);
            return Json(obj);
        }
        #endregion
    }
}
