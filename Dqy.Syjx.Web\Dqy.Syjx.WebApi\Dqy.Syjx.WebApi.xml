<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Dqy.Syjx.WebApi</name>
    </assembly>
    <members>
        <member name="M:Dqy.Syjx.WebApi.Controllers.AController.R(System.String,System.String)">
            <summary>
            企业微信构造网页授权页面
            </summary>
            <param name="acode">应用Code</param>
            <param name="pcode">页面Code</param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.AController.Jump(System.String,System.String,System.Int32)">
            <summary>
            企业微信网页授权回调页面
            </summary>
            <param name="code">授权后返回的Code</param>
            <param name="state">授权页面返回的state参数值</param>
            <param name="pcode">页面Code</param>
            <returns></returns>
        </member>
        <member name="T:Dqy.Syjx.WebApi.Controllers.ConfigController">
            <summary>
            各项配置信息
            </summary>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ConfigController.GetSchoolStageList(System.String)">
            <summary>
            获取个人任课学段
            </summary>
            <param name="sessionId">sessionId</param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ConfigController.GetCourseList(System.Int32,System.Int32,System.String)">
            <summary>
            获取个人任课学科
            </summary>
            <param name="schoolStageId">学段Id</param>
            <param name="nature">0：不含理化生科学，1：仅含理化生科学</param>
            <param name="sessionId">sessionId</param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ConfigController.GetClassList(System.Int32,System.String)">
            <summary>
            根据课程获取年级班级
            </summary>
            <param name="courseid">学科Id</param>
            <param name="sessionId">sessionId</param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ConfigController.GetSectionList(System.Int32,System.String,System.String)">
            <summary>
            获取学科节次
            </summary>
            <param name="sessionId">sessionId</param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ConfigController.GetSchoolTermEntity">
            <summary>
            获取当前学期信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ConfigController.GetUserClassByUid(System.String)">
            <summary>
            获取用户个人任课信息
            </summary>
            <param name="sessionId">sessionid</param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ConfigController.GetDictionaryListJson(System.String,System.String)">
            <summary>
            获取字典值
            </summary>
            <param name="sessionId"></param>
            <param name="typecode"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ConfigController.GetChildListJson(System.String,System.String,System.String)">
            <summary>
            获取子级字典值
            </summary>
            <param name="sessionId"></param>
            <param name="typecode"></param>
            <param name="pids"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ConfigController.GetUserTeachClassList(System.String,System.String)">
            <summary>
            获取当前用户任课班级信息
            </summary>
            <param name="sessionId"></param>
            <param name="courseIdz"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ConfigController.CheckUserTeachClass(Dqy.Syjx.Entity.PersonManage.UserTeachClassEntity)">
            <summary>
            验证任课班级是否重复
            </summary>
            <param name="entity"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ConfigController.GetSchoolGradeClass(Dqy.Syjx.Model.Param.OrganizationManage.SchoolGradeClassListApiParam)">
            <summary>
            获取学校班级年级
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ConfigController.SaveUserClass(Dqy.Syjx.Model.Input.PersonManage.UserClassInfoInputModel)">
            <summary>
            保存用户任课信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ConfigController.ClearUserClass(Dqy.Syjx.Model.Input.PersonManage.UserClassInfoInputModel)">
            <summary>
            清除个人任课信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ConfigController.GetPageSetValid(Dqy.Syjx.Model.Param.SystemManage.ConfigSetListApiParam)">
            <summary>
            获取配置的必填项验证参数
            </summary>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ConfigController.GetComConfigSet(Dqy.Syjx.Model.Param.SystemManage.ConfigSetListApiParam)">
            <summary>
            获取公共配置参数
            </summary>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ConfigController.IsOpenExperiment(Dqy.Syjx.Model.Param.SystemManage.ConfigSetListApiParam)">
            <summary>
            是否开启实验目录选择
            </summary>
            <returns></returns>
        </member>
        <member name="T:Dqy.Syjx.WebApi.Controllers.ExperimentOutController">
            <summary>
            实验教学
            </summary>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentOutController.GetCatalogList(System.String)">
            <summary>
            获取学科信息
            </summary>
            <param name="sessionId">sessionId</param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentOutController.PlanExperimentList(Dqy.Syjx.Model.Param.ExperimentTeachManage.PlanDetailListApiParam)">
            <summary>
            查询发布计划实验列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentOutController.PlanExperimentInfo(System.Int64,System.String)">
            <summary>
            获取发布实验详情
            </summary>
            <param name="id">实验id</param>
            <param name="sessionId">参数</param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentOutController.BookingList(Dqy.Syjx.Model.Param.ExperimentTeachManage.ExperimentPublishApiParam)">
            <summary>
            获取当前发布预约列表(当前学年、学期)
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentOutController.ExperimentList(Dqy.Syjx.Model.Param.ExperimentTeachManage.PublishDetailApiParam)">
            <summary>
            实验清单。
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentOutController.Booking(Dqy.Syjx.Model.Input.ExperimentTeachManage.ExperimentCriticizeApiInputModel)">
            <summary>
            预约课程提交(当前学年、学期)
            </summary>
            <param name="model">发布课程Id，和选择家长学生Id</param>
            <remarks>
            1：必须登录
            2：必须有学生信息</remarks>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentOutController.Bookinged(Dqy.Syjx.Model.Param.ExperimentTeachManage.PublishDetailApiParam)">
            <summary>
            获取学生课程预约列表(当前学年、学期)
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentOutController.GetStudentList(System.String)">
            <summary>
            获取当前登录用户下的学生信息
            </summary>
            <param name="sessionId"></param>
            <remarks>
            1：必须登录
            2：必须有学生信息
            </remarks>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentOutController.ParentStudentAdd(Dqy.Syjx.Model.Input.PersonManage.ParentStudentInputModel)">
            <summary>
            添加学生信息
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentOutController.ParentStudentDel(Dqy.Syjx.Model.Input.PersonManage.ParentStudentDeleteModel)">
            <summary>
            删除学生信息
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="T:Dqy.Syjx.WebApi.Controllers.ExperimentTeachController">
            <summary>
            实验教学
            </summary>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentTeachController.GetCurrentSchoolTerm">
            <summary>
            获取当前学年学期数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentTeachController.GetArrangeUserList(System.Int32,System.Int32,System.String)">
            <summary>
            获取安排人
            </summary>
            <param name="schoolStageId">学段</param>
            <param name="courseId">学科</param>
            <param name="sessionId">sessionId</param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentTeachController.GetPlanExperimentList(Dqy.Syjx.Model.Param.ExperimentTeachManage.PlanDetailListApiParam)">
            <summary>
            获取实验计划列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentTeachController.GetCatalogExperimentList(Dqy.Syjx.Model.Param.ExperimentTeachManage.PlanDetailListApiParam)">
            <summary>
            获取实验目录列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentTeachController.GetReserveList(Dqy.Syjx.Model.Param.SerachApiParam)">
            <summary>
            已预约列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentTeachController.GetWaitArrangeList(Dqy.Syjx.Model.Param.SerachApiParam)">
            <summary>
            待安排列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentTeachController.GetAlreadyArrangeList(Dqy.Syjx.Model.Param.SerachApiParam)">
            <summary>
            已安排列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentTeachController.GetWaitRecordList(Dqy.Syjx.Model.Param.SerachApiParam)">
            <summary>
            待登记列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentTeachController.GetAlreadyRecordList(Dqy.Syjx.Model.Param.SerachApiParam)">
            <summary>
            已登记列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentTeachController.GetEasyAlreadyRecordList(Dqy.Syjx.Model.Param.SerachApiParam)">
            <summary>
            简易登记列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentTeachController.GetDetail(System.Int64,System.String)">
            <summary>
            获取实验预约详情
            </summary>
            <param name="id"></param>
            <param name="sessionId"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentTeachController.GetArrangerNeedValue(System.Int64,System.String)">
            <summary>
            获取实验安排时自动带出的所需仪器和实验材料值
            </summary>
            <param name="id"></param>
            <param name="sessionId"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentTeachController.VerifyGradeClass(Dqy.Syjx.Model.Input.ExperimentTeachManage.ExperimentBookingInputModel)">
            <summary>
            验证是否跨年级选择班级
            </summary>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentTeachController.SaveExperimentReserve(Dqy.Syjx.Model.Input.ExperimentTeachManage.ExperimentBookingInputModel)">
            <summary>
            提交实验预约
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentTeachController.ArrangeRecord(Dqy.Syjx.Model.Input.ExperimentTeachManage.ExperimentBookArrangeRecordInputModel)">
            <summary>
            实验安排登记
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentTeachController.SaveEasyRecord(Dqy.Syjx.Model.Input.ExperimentTeachManage.ExperimentBookingInputModel)">
            <summary>
            简易登记
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentTeachController.WithdrawArrangeRecord(Dqy.Syjx.Model.Input.ExperimentTeachManage.ArrageRecodeWithdrawInputModel)">
            <summary>
            实验安排、登记、预约撤回
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentTeachController.DeleteReserve(Dqy.Syjx.Model.Input.ExperimentTeachManage.ExperimentBookingInputModel)">
            <summary>
            实验预约删除
            </summary>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentTeachController.GetEditForm(System.Int64,System.String)">
            <summary>
            获取预约实体信息
            </summary>
            <param name="id">预约Id</param>
            <param name="sessionId"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentTeachController.GetSectionForm(System.Int64,System.Int64,System.DateTime,System.String)">
            <summary>
            获取预约实体信息
            </summary>
            <param name="id"></param>
            <param name="funroomid"></param>
            <param name="classtime"></param>
            <param name="sessionId"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentTeachController.GetBookingListed(Dqy.Syjx.Model.Param.SerachApiParam)">
            <summary>
            已预约列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentTeachController.GetDetailList(System.Int64,System.Int64,System.Int32,System.String)">
            <summary>
            获取预约详情
            </summary>
            <param name="id"></param>
            <param name="pid"></param>
            <param name="statuz"></param>
            <param name="sessionId"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentTeachController.SaveAddPlan(Dqy.Syjx.Model.Input.ExperimentTeachManage.ExperimentBookingInputModel)">
            <summary>
            实验选择班级预约保存
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentTeachController.SaveBooking(Dqy.Syjx.Model.Input.ExperimentTeachManage.ExperimentBookingInputModel)">
            <summary>
            保存预约信息
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentTeachController.GetSession(System.String)">
            <summary>
            获取Session
            </summary>
            <param name="key">键</param>
            <returns>返回对应的值</returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentTeachController.GetBookingArrangeList(Dqy.Syjx.Model.Param.ExperimentTeachManage.SerachApiBookingParam)">
            <summary>
            待安排列表，已安排列表
            </summary>
            <param name="param">待安排列表{ListType:1,Statuz:10} 已安排列表{ListType:0(或者不传递),Statuz:0或者不传递,ThanStatuz:10}</param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentTeachController.GetArrageJson(System.Int64,System.String)">
            <summary>
            获取安排实体信息
            </summary>
            <param name="id">安排Id</param>
            <param name="sessionId"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentTeachController.SaveArrange(Dqy.Syjx.Model.Input.ExperimentTeachManage.ExperimentBookingInputModel)">
            <summary>
            保存安排信息
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentTeachController.GetBookingRecordList(Dqy.Syjx.Model.Param.ExperimentTeachManage.SerachApiBookingParam)">
            <summary>
            待安排列表，已安排列表
            </summary>
            <param name="param">待登记列表{Statuz:20} 已登记列表{ThanStatuz:20}</param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentTeachController.GetRecordJson(System.Int64,System.String)">
            <summary>
            获取登记实体信息
            </summary>
            <param name="id">安排Id</param>
            <param name="sessionId"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentTeachController.SaveRecord(Dqy.Syjx.Model.Input.ExperimentTeachManage.ExperimentBookArrangeRecordInputModel)">
            <summary>
            保存登记信息
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentTeachController.GetBookingExperimentList(Dqy.Syjx.Model.Param.ExperimentTeachManage.SerachApiBookingParam)">
            <summary>
            获取当前学年学期数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentTeachController.GetInstrumentList(Dqy.Syjx.Model.Param.ExperimentTeachManage.SerachApiBookingParam)">
            <summary>
            获取仪器列表（本单位）
            </summary>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentTeachController.GetEasyListed(Dqy.Syjx.Model.Param.SerachApiParam)">
            <summary>
            已预约列表简易登记
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentTeachController.SaveRecordEasy(Dqy.Syjx.Model.Input.ExperimentTeachManage.ExperimentBookingInputModel)">
            <summary>
            保存登记信息
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentTeachController.LoanList(Dqy.Syjx.Model.Param.ExperimentTeachManage.ExperiInstruApiParam)">
            <summary>
            实验仪器借还列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentTeachController.GetLoanFormJson(System.Int64,System.String)">
            <summary>
            获取实验借出详情。
            </summary>
            <param name="id"></param>
            <param name="sessionId"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentTeachController.SaveLoanFormJson(Dqy.Syjx.Model.Input.ExperimentTeachManage.ExperiInstruLogInputModel)">
            <summary>
            确认保存借出
            </summary>
            <param name="modelz"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentTeachController.SaveLoanAddInstrmentJson(Dqy.Syjx.Model.Input.ExperimentTeachManage.ExperiInstruLogInputModel)">
            <summary>
            确认添加仪器
            </summary>
            <param name="modelz"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentTeachController.SaveLoanInstrmentModelJson(Dqy.Syjx.Model.Input.ExperimentTeachManage.ExperiInstruLogInputModel)">
            <summary>
            修改仪器型号
            </summary>
            <param name="modelz"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentTeachController.SaveLoanInstrmentunitJson(Dqy.Syjx.Model.Input.ExperimentTeachManage.ExperiInstruLogInputModel)">
            <summary>
            修改仪器单位名称
            </summary>
            <param name="modelz"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentTeachController.SaveLoanInstrmentnumJson(Dqy.Syjx.Model.Input.ExperimentTeachManage.ExperiInstruLogInputModel)">
            <summary>
            修改仪器数量
            </summary>
            <param name="modelz"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentTeachController.SaveLoanDelInstrmentJson(Dqy.Syjx.Model.Input.ExperimentTeachManage.ExperiInstruLogInputModel)">
            <summary>
            确认删除仪器
            </summary>
            <param name="modelz"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentTeachController.SaveBackFormJson(Dqy.Syjx.Model.Input.ExperimentTeachManage.ExperiInstruLogInputModel)">
            <summary>
            确认保存归还
            </summary>
            <param name="modelz">实体信息</param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentTeachController.GetVersionList(Dqy.Syjx.Model.Param.ExperimentTeachManage.TextbookVersionApiParam)">
            <summary>
            获取教材版本下拉集合
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.ExperimentTeachController.GetBookingGradeList(System.Int32,System.String)">
            <summary>
            获取预约年级下拉集合
            </summary>
            <param name="dictionaryId"></param>
            <param name="sessionId"></param>
            <returns></returns>
        </member>
        <member name="T:Dqy.Syjx.WebApi.Controllers.WxResult">
            <summary>
            无锡 推送消息，调用推送消息实验员
            </summary>
        </member>
        <member name="T:Dqy.Syjx.WebApi.Controllers.FunRoomController">
            <summary>
            功能室
            </summary>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.FunRoomController.GetFunRoomList(System.Int32,System.Int32,System.Int32,System.String)">
            <summary>
            获取功能室
            </summary>
            <param name="schoolStageId">学段Id</param>
            <param name="courseId">学科Id</param>
            <param name="nature">功能室类型（1：实验室，2：专用室）</param>
            <param name="sessionId">sessionId</param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.FunRoomController.GetFunRoomUserList(System.Int32,System.Int32,System.Int32,System.String)">
            <summary>
            获取功能室
            </summary>
            <param name="schoolStageId">学段Id</param>
            <param name="courseId">学科Id</param>
            <param name="nature">功能室类型（1：实验室，2：专用室）</param>
            <param name="sessionId">sessionId</param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.FunRoomController.GetFunRoomIsHaveCamera(System.Int64,System.String)">
            <summary>
            获取当前功能室是否已关联摄像头
            </summary>
            <param name="funroomid">功能室Id</param>
            <param name="sessionId">sessionId</param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.FunRoomController.GetFunRoomByCode(System.String,System.String)">
            <summary>
            根据二维码编号获取功能室信息
            </summary>
            <param name="code"></param>
            <param name="sessionId"></param>
            <returns></returns>
        </member>
        <member name="T:Dqy.Syjx.WebApi.Controllers.FunRoomUseController">
            <summary>
            专用室使用登记
            </summary>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.FunRoomUseController.GetFunRoomUseList(Dqy.Syjx.Model.Param.SerachApiParam)">
            <summary>
            使用登记列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.FunRoomUseController.GetDetail(System.Int64,System.String)">
            <summary>
            专用室使用登记详情
            </summary>
            <param name="id">专用室使用登记id</param>
            <param name="sessionId">sessionId</param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.FunRoomUseController.SaveFunRoomUse(Dqy.Syjx.Model.Input.BusinessManage.FunRoomUseInputModel)">
            <summary>
            保存专用室使用登记
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="T:Dqy.Syjx.WebApi.Controllers.InstrumentController">
            <summary>
            仪器模块
            </summary>
        </member>
        <member name="F:Dqy.Syjx.WebApi.Controllers.InstrumentController.instrumentLendBLL">
            <summary>
            
            </summary>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.InstrumentController.GetInstrumentLendList(Dqy.Syjx.Model.Param.SerachApiParam)">
            <summary>
            获取仪器借出列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.InstrumentController.GetUserList(System.String)">
            <summary>
            获取本单位借用人信息(使用Id与RealName字段)
            </summary>
            <param name="SessionId"> *SessionId：SessionId</param>
            <returns>
            </returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.InstrumentController.SaveBorrowDate(Dqy.Syjx.Model.Input.InstrumentManage.InstrumentLendInputModel)">
            <summary>
            仪器借出
            </summary>
            <param name="model">
            字段前面带有“*”的为必填，不带“*”的为非必填<br></br>
            *SessionId：SessionId<br></br>
            *LendUserId：借用人Id<br></br>
            *LendNum：借用数量<br></br>
            *LendDay:借用天数<br></br>
            Remark:备注<br></br>
            </param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.InstrumentController.GetConfirmList(Dqy.Syjx.Model.Param.SerachApiParam)">
            <summary>
            获取借用确认列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.InstrumentController.SaveConfirmDate(System.String,System.Int64)">
            <summary>
            借用确认
            </summary>
            <param name="SessionId">SessionId</param>
            <param name="id">仪器Id</param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.InstrumentController.GetInstrumentRevertList(Dqy.Syjx.Model.Param.SerachApiParam)">
            <summary>
            获取仪器归还列表
            </summary>
            <param name="param"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.InstrumentController.SaveRevertDate(System.String,System.String)">
            <summary>
            批量归还
            </summary>
            <param name="SessionId">SessionId</param>
            <param name="ids">仪器Id集合逗号分隔</param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.InstrumentController.RevokeData(System.String,System.Int64)">
            <summary>
            撤销借出
            </summary>
            <param name="SessionId">SessionId</param>
            <param name="id">仪器Id</param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.LoginThirdController.Auth_chq_h5(System.String)">
            <summary>
            用户登录
            </summary>
            <param name="code">code</param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.LoginThirdController.Auth_chq_h1(System.String)">
            <summary>
            用户登录
            </summary>
            <param name="code">code</param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.LoginThirdController.LoginUnion(System.String,System.Int32,System.String)">
            <summary>
            对接第三方统一身份认证：先通过h5页面对接，然后在h5页面中打开微信小程序
            </summary>
            <param name="token">token</param>
            <param name="clientType">客户端类型</param>
            <param name="code">code</param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.LoginThirdController.Auth_tzq_h5(System.String,System.String)">
            <summary>
            通州区统一身份认证
            </summary>
            <param name="iflyssost"></param>
            <param name="requesturl"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.LoginThirdController.getSchoolSection(System.String)">
            <summary>
            通州统一身份认证学段转码
            </summary>
            <param name="code"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.LoginThirdController.getSchoolProp(System.String)">
            <summary>
            将学段转换成平台的单位属性
            </summary>
            <param name="schoolSection"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.LoginThirdController.PostHelper(System.String,System.Net.Http.HttpContent)">
            <summary>
            使用HttpClient调用WebService
            </summary>
            <param name="url">URL地址</param>
            <param name="content">参数</param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.LoginThirdController.Auth_css_h5(System.String)">
            <summary>
            常熟市统一身份认证
            </summary>
            <param name="code"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.LoginThirdController.Auth_wzq_h5(Dqy.Syjx.Model.Param.OrganizationManage.WxLoginInfo)">
            <summary>
            吴中区统一身份认证
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.LoginThirdController.Auth_gsq_h5(System.String)">
            <summary>
            姑苏区移动端统一身份认证
            </summary>
            <param name="code"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.LoginThirdController.ddlogin(System.String)">
            <summary>
            钉钉用户自动登录
            </summary>
            <param name="code">code</param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.LoginThirdController.Syjx(Dqy.Syjx.WebApi.Models.SyjxModel)">
            <summary>
            实验教学平台统一身份认证对接
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.LoginThirdController.SetSession(System.String,System.String)">
            <summary>
            设置Session
            </summary>
            <param name="key">键</param>
            <param name="value">值</param>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.UserController.CheckRun">
            <summary>
            部署测试
            </summary>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.UserController.GetMenu(System.String)">
            <summary>
            获取小程序菜单
            </summary>
            <param name="sessionId">sessionId</param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.UserController.CheckIsOpenScanCodeLogin">
            <summary>
            是否启用扫码登录
            </summary>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.UserController.GetAccountByOpenid(System.String,System.String)">
            <summary>
            获取当前微信绑定的账号
            </summary>
            <param name="sessionId">sessionId</param>
            <param name="openId">openId</param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.UserController.GetLoginByCode(System.String)">
            <summary>
            通过code的判断是否绑定账号，如果微信曾经绑定过账号，自动登录，并返回sessionId
            </summary>
            <param name="code">小程序code</param>
            <returns>验证结果，如果登录成功，返回用户信息，及sessionId（存放在msg中）</returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.UserController.GetValidateCode(System.String,System.Int32)">
            <summary>
            获取手机验证码
            </summary>
            <param name="phoneNumber"></param>
            <param name="validateType">传3（家长、教师登录）</param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.UserController.LoginByMobile(Dqy.Syjx.Model.Param.OrganizationManage.MobileLoginInfo)">
            <summary>
            用户登录
            </summary>
            <param name="model">登录信息实体</param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.UserController.Login(Dqy.Syjx.Model.Param.OrganizationManage.WxLoginInfo)">
            <summary>
            用户登录
            </summary>
            <param name="model">登录信息实体</param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.UserController.LoginOff(System.String)">
            <summary>
            用户退出登录
            </summary>
            <param name="sessionId"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.UserController.SaveForm(Dqy.Syjx.Model.Input.UserThirdAuth)">
            <summary>
            统一身份认证保存方法
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.UserController.testdata">
            <summary>
            验证不存在时，userService.GetEntity 返回null
            </summary>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.UserController.LoginAuto(System.String,System.String)">
            <summary>
            测试登录
            </summary>
            <param name="userName"></param>
            <param name="password"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.UserController.GetLoginForPc(Dqy.Syjx.Model.Param.OrganizationManage.ScanCodeLogin)">
            <summary>
            扫码Pc端自动登录
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.UserController.SwitchAccount(Dqy.Syjx.Model.Param.OrganizationManage.SwitchAccountModel)">
            <summary>
            切换账号
            </summary>
            <param name="model">SwitchAccountModel</param> 
            <returns>1：成功；0：失败</returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.UserController.GetIsOpenEasyRegist(Dqy.Syjx.Web.Code.OperatorInfo)">
            <summary>
            获取是否显示简易模式登记菜单
            </summary>
            <param name="operatorinfo">当前登录信息实体</param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.UserController.GetIsOpenCatalog(Dqy.Syjx.Web.Code.OperatorInfo)">
            <summary>
            获取是否显示按目录预约，登记
            </summary>
            <param name="operatorinfo">当前登录信息实体</param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.UserController.RemoveEasyRegistMenuList(System.Collections.Generic.List{Dqy.Syjx.Model.Result.SystemManage.WeiXinMenu})">
            <summary>
            获取去除简易模式后的菜单
            </summary>
            <param name="menuList">菜单集合</param>
            <returns></returns>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.UserController.RemoveCatalogMenuList(System.Collections.Generic.List{Dqy.Syjx.Model.Result.SystemManage.WeiXinMenu})">
            <summary>
            获取去按目录后的菜单
            </summary>
            <param name="menuList">菜单集合</param>
            <returns></returns>
        </member>
        <member name="T:Dqy.Syjx.WebApi.Controllers.AuthorizeFilterAttribute">
            <summary>
            验证token和记录日志
            </summary>
        </member>
        <member name="F:Dqy.Syjx.WebApi.Controllers.AuthorizeFilterAttribute.IgnoreToken">
            <summary>
            忽略token的方法
            </summary>
        </member>
        <member name="M:Dqy.Syjx.WebApi.Controllers.AuthorizeFilterAttribute.OnActionExecutionAsync(Microsoft.AspNetCore.Mvc.Filters.ActionExecutingContext,Microsoft.AspNetCore.Mvc.Filters.ActionExecutionDelegate)">
            <summary>
            异步接口日志
            </summary>
            <param name="context"></param>
            <param name="next"></param>
            <returns></returns>
        </member>
        <member name="T:Dqy.Syjx.WebApi.Models.GetUserInfoResult">
            <summary>
            企业微信获取到的用户基础信息
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.GetUserInfoResult.errcode">
            <summary>
            errcode
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.GetUserInfoResult.errmsg">
            <summary>
            errmsg
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.GetUserInfoResult.CorpId">
            <summary>
            用户所属企业的corpid
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.GetUserInfoResult.UserId">
            <summary>
            员工UserID
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.GetUserInfoResult.openid">
            <summary>
            非企业成员的OpenId
            （此属性在Work最新文档中没有）
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.GetUserInfoResult.DeviceId">
            <summary>
            手机设备号(由微信在安装时随机生成) 
            （此属性在Work最新文档中没有）
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.GetUserInfoResult.user_ticket">
            <summary>
            成员票据，最大为512字节。
            scope为snsapi_userinfo或snsapi_privateinfo，且用户在应用可见范围之内时返回此参数。
            后续利用该参数可以获取用户信息或敏感信息。
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.GetUserInfoResult.expires_in">
            <summary>
            user_token的有效时间（秒），随user_ticket一起返回
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.GetUserInfoResult.external_userid">
            <summary>
            外部联系人id，当且仅当用户是企业的客户，且跟进人在应用的可见范围内时返回。如果是第三方应用调用，针对同一个客户，同一个服务商不同应用获取到的id相同
            <para>https://work.weixin.qq.com/api/doc/90000/90135/91023</para>
            </summary>
        </member>
        <member name="T:Dqy.Syjx.WebApi.Models.GetUserDetailResult">
            <summary>
             企业微信获取到的用户详细信息
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.GetUserDetailResult.errcode">
            <summary>
            errcode
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.GetUserDetailResult.errmsg">
            <summary>
            errmsg
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.GetUserDetailResult.userid">
            <summary>
            成员UserID
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.GetUserDetailResult.name">
            <summary>
            成员姓名
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.GetUserDetailResult.department">
            <summary>
            成员所属部门
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.GetUserDetailResult.position">
            <summary>
            职位信息
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.GetUserDetailResult.mobile">
            <summary>
            成员手机号，仅在用户同意snsapi_privateinfo授权时返回
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.GetUserDetailResult.gender">
            <summary>
            性别。0表示未定义，1表示男性，2表示女性
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.GetUserDetailResult.email">
            <summary>
            成员邮箱，仅在用户同意snsapi_privateinfo授权时返回
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.GetUserDetailResult.avatar">
            <summary>
            头像url。注：如果要获取小图将url最后的”/0”改成”/64”即可
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.GetUserDetailResult.qr_code">
            <summary>
            qr_code
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.GetUserDetailResult.biz_mail">
            <summary>
            biz_mail
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.GetUserDetailResult.address">
            <summary>
            地址
            </summary>
        </member>
        <member name="T:Dqy.Syjx.WebApi.Models.GetUrlSchemeResult">
            <summary>
            小程序链接
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.GetUrlSchemeResult.errcode">
            <summary>
            errcode
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.GetUrlSchemeResult.errmsg">
            <summary>
            errmsg
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.GetUrlSchemeResult.openlink">
            <summary>
            生成的小程序 scheme 码 
            </summary>
        </member>
        <member name="T:Dqy.Syjx.WebApi.Models.ProviderToken">
            <summary>
            服务商凭证Token
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.ProviderToken.provider_access_token">
            <summary>
            服务商的access_token
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.ProviderToken.expires_in">
            <summary>
            有效期
            </summary>
        </member>
        <member name="T:Dqy.Syjx.WebApi.Models.Corpid_to_Opencorpid">
            <summary>
            corpid转换
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.Corpid_to_Opencorpid.errcode">
            <summary>
            返回码
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.Corpid_to_Opencorpid.errmsg">
            <summary>
            错误结果
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.Corpid_to_Opencorpid.open_corpid">
            <summary>
            该服务商第三方应用下的企业ID
            </summary>
        </member>
        <member name="T:Dqy.Syjx.WebApi.Models.SuiteToken">
            <summary>
            代开发应用模板凭证Token
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.SuiteToken.errcode">
            <summary>
            返回结果code
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.SuiteToken.errmsg">
            <summary>
            返回结果
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.SuiteToken.suite_access_token">
            <summary>
            第三方或者代开发应用模板access_token
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.SuiteToken.expires_in">
            <summary>
            有效期
            </summary>
        </member>
        <member name="T:Dqy.Syjx.WebApi.Models.GetPermanentCodeResult">
            <summary>
            代开发授权应用返回结果
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.GetPermanentCodeResult.access_token">
            <summary>
            授权方（企业）access_token
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.GetPermanentCodeResult.expires_in">
            <summary>
            授权方（企业）access_token超时时间
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.GetPermanentCodeResult.permanent_code">
            <summary>
            企业号永久授权码，即为代开发应用的secret。
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.GetPermanentCodeResult.auth_corp_info">
            <summary>
            授权方企业信息
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.GetPermanentCodeResult.auth_info">
            <summary>
            授权信息
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.GetPermanentCodeResult.auth_user_info">
            <summary>
            授权管理员的信息
            </summary>
        </member>
        <member name="T:Dqy.Syjx.WebApi.Models.ThirdParty_AuthCorpInfo">
            <summary>
            ThirdParty_AuthCorpInfo
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.ThirdParty_AuthCorpInfo.corpid">
            <summary>
            授权方企业号id
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.ThirdParty_AuthCorpInfo.corp_name">
            <summary>
            授权方企业号名称
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.ThirdParty_AuthCorpInfo.corp_type">
            <summary>
            授权方企业号类型，认证号：verified, 注册号：unverified，体验号：test
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.ThirdParty_AuthCorpInfo.corp_round_logo_url">
            <summary>
            授权方企业号圆形头像
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.ThirdParty_AuthCorpInfo.corp_square_logo_url">
            <summary>
            授权方企业号方形头像
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.ThirdParty_AuthCorpInfo.corp_user_max">
            <summary>
            授权方企业号用户规模
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.ThirdParty_AuthCorpInfo.corp_full_name">
            <summary>
            所绑定的企业微信主体名称
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.ThirdParty_AuthCorpInfo.subject_type">
            <summary>
            企业类型，1. 企业; 2. 政府以及事业单位; 3. 其他组织, 4.团队号
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.ThirdParty_AuthCorpInfo.corp_wxqrcode">
            <summary>
            授权方企业微信二维码
            </summary>
        </member>
        <member name="T:Dqy.Syjx.WebApi.Models.ThirdParty_AuthInfo">
            <summary>
            授权应用信息
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.ThirdParty_AuthInfo.agent">
            <summary>
            授权的应用信息
            </summary>
        </member>
        <member name="T:Dqy.Syjx.WebApi.Models.GetPermanentCodeResult_AuthUserInfo">
            <summary>
            授权管理员的信息
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.GetPermanentCodeResult_AuthUserInfo.userid">
            <summary>
            授权管理员的userid，可能为空（内部管理员一定有，不可更改）
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.GetPermanentCodeResult_AuthUserInfo.name">
            <summary>
            授权管理员的name，可能为空（内部管理员一定有，不可更改）
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.GetPermanentCodeResult_AuthUserInfo.avatar">
            <summary>
            授权管理员的头像url
            </summary>
        </member>
        <member name="T:Dqy.Syjx.WebApi.Models.ThirdParty_Agent">
            <summary>
            ThirdParty_Agent
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.ThirdParty_Agent.agentid">
            <summary>
            授权方应用id
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.ThirdParty_Agent.name">
            <summary>
            授权方应用名字
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.ThirdParty_Agent.is_customized_app">
            <summary>
            是否为代开发自建应用
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.ThirdParty_Agent.privilege">
            <summary>
            应用对应的权限
            </summary>
        </member>
        <member name="T:Dqy.Syjx.WebApi.Models.ThirdParty_Agent_Privilege">
            <summary>
            ThirdParty_Agent_Privilege
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.ThirdParty_Agent_Privilege.level">
            <summary>
            权限等级。
            1:通讯录基本信息只读
            2:通讯录全部信息只读
            3:通讯录全部信息读写
            4:单个基本信息只读
            5:通讯录全部信息只写
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.ThirdParty_Agent_Privilege.allow_party">
            <summary>
            应用可见范围（部门）
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.ThirdParty_Agent_Privilege.allow_user">
            <summary>
            应用可见范围（成员）
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.ThirdParty_Agent_Privilege.allow_tag">
            <summary>
            应用可见范围（标签）
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.ThirdParty_Agent_Privilege.extra_party">
            <summary>
            额外通讯录（部门）
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.ThirdParty_Agent_Privilege.extra_user">
            <summary>
            额外通讯录（成员）
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.ThirdParty_Agent_Privilege.extra_tag">
            <summary>
            额外通讯录（标签）
            </summary>
        </member>
        <member name="T:Dqy.Syjx.WebApi.Models.UserInfoObj">
            <summary>
            
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.UserInfoObj.errcode">
            <summary>
            errcode
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.UserInfoObj.errmsg">
            <summary>
            errmsg
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.UserInfoObj.userid">
            <summary>
            员工UserID
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.UserInfoObj.name">
            <summary>
            成员名称
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.UserInfoObj.department">
            <summary>
            成员所属部门id列表
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.UserInfoObj.order">
            <summary>
            部门内的排序值，默认为0。数量必须和department一致，数值越大排序越前面。值范围是[0, 2^32)
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.UserInfoObj.position">
            <summary>
            职位信息
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.UserInfoObj.mobile">
            <summary>
            手机号码
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.UserInfoObj.gender">
            <summary>
            性别。gender=0表示男，=1表示女
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.UserInfoObj.email">
            <summary>
            邮箱
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.UserInfoObj.biz_mail">
            <summary>
            企业邮箱
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.UserInfoObj.is_leader_in_dept">
            <summary>
            上级字段，标识是否为上级。第三方暂不支持
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.UserInfoObj.direct_leader">
            <summary>
            直属上级
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.UserInfoObj.avatar">
            <summary>
            头像url。注：小图将url最后的"/0"改成"/64"
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.UserInfoObj.alias">
            <summary>
            成员别名。长度1~32个utf8（非必须） ### https://work.weixin.qq.com/api/doc#90000/90135/90195
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.UserInfoObj.status">
            <summary>
            激活状态: 1=已激活，2=已禁用，4=未激活，5=退出企业。 已激活代表已激活企业微信或已关注微信插件。未激活代表既未激活企业微信又未关注微信插件。
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.UserInfoObj.telephone">
            <summary>
            座机。第三方暂不支持
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.UserInfoObj.english_name">
            <summary>
            英文名。第三方暂不支持
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.UserInfoObj.open_userid">
            <summary>
            全局唯一。对于同一个服务商，不同应用获取到企业内同一个成员的open_userid是相同的，最多64个字节。仅第三方应用可获取
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.UserInfoObj.main_department">
            <summary>
            主部门
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.UserInfoObj.enable">
            <summary>
            启用/禁用成员，第三方不可获取。1表示启用成员，0表示禁用成员
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.UserInfoObj.wxplugin_status">
            <summary>
            关注微信插件的状态: 1=已关注，0=未关注
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.UserInfoObj.qr_code">
            <summary>
            员工个人二维码，扫描可添加为外部联系人(注意返回的是一个url，可在浏览器上打开该url以展示二维码)；第三方仅通讯录应用可获取
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.UserInfoObj.external_position">
            <summary>
            对外职务
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.UserInfoObj.address">
            <summary>
            地址。长度最大128个字符
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.SyjxModel.userId">
            <summary>
            用户Id
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.SyjxModel.token">
            <summary>
            token值
            </summary>
        </member>
        <member name="P:Dqy.Syjx.WebApi.Models.SyjxModel.appId">
            <summary>
            appid值
            </summary>
        </member>
    </members>
</doc>
