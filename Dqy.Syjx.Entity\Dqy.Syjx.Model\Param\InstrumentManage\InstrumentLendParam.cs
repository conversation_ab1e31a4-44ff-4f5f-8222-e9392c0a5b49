﻿using System;
using System.Collections.Generic;
using Newtonsoft.Json;
using Dqy.Syjx.Util;

namespace Dqy.Syjx.Model.Param.InstrumentManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-11-09 15:30
    /// 描 述：实体查询类
    /// </summary>
    public class InstrumentLendListParam
    {
        private string _ids;
        public string Ids
        {
            get { return StringFilter.SearchSql(_ids); }
            set { _ids = value; }
        }

        /// <summary>
        /// 学校仪器库Id
        /// </summary>
        public long? SchoolInstrumentId { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public List<int> StatuzList { get; set; }

        /// <summary>
        /// 借出开始时间
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// 借出结束时间
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// 借用人
        /// </summary>
        public long? LendUserId { get; set; }

        /// <summary>
        /// 借出人
        /// </summary>
        public long? GiveUserId { get; set; }

        /// <summary>
        /// 学校Id
        /// </summary>
        public long? SchoolId { get; set; }
        /// <summary>
        /// 区县Id
        /// </summary>
        public long? CountyId { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public int? Statuz { get; set; }

        /// <summary>
        /// 关键词搜索
        /// </summary>
        private string _KeyWord;
        public string KeyWord
        {
            set { _KeyWord = value; }
            get { return StringFilter.SearchSql(_KeyWord); }
        }

        /// <summary>
        /// 存放地
        /// </summary>
        private string _StoragePlace;
        public string StoragePlace
        {
            set { _StoragePlace = value; }
            get { return StringFilter.SearchSql(_StoragePlace); }
        }

        /// <summary>
        /// 查询类型（0：默认，1：根据学段、科目管理权限判断）
        /// </summary>
        public int ListType { get; set; }

        /// <summary>
        /// 管理授权学段
        /// </summary>
        public string SchoolStageIdz { get; set; }

        /// <summary>
        /// 管理授权科目
        /// </summary>
        public string SubjectIdz { get; set; }

        /// <summary>
        /// 仪器属性（1：仪器，2：耗材）
        /// </summary>
        public int? InstrumentAttribute { get; set; }

        /// <summary>
        /// 功能室管理人Id（取当前用户Id）
        /// </summary>
        public long? SafeguardUserId { get; set; }
    }
}
