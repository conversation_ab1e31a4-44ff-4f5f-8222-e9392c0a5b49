﻿using System.Net.Http;
using Dqy.Syjx.Cache.Factory;
using Newtonsoft.Json;
using Refit;
using System.Threading.Tasks;
using System.Threading;
using Dqy.Syjx.Util;
using System;

namespace Dqy.Syjx.Service.HttpService
{
    public class HttpClientBusinessHandler : DelegatingHandler
    {
        private readonly string _businessExceptionMsg = "业务异常，请稍后再试！";

        public HttpClientBusinessHandler()
        {
            InnerHandler = new HttpClientHandler();
        }

        public HttpClientBusinessHandler(string businessExceptionMsg)
        {
            _businessExceptionMsg = businessExceptionMsg;
            InnerHandler = new HttpClientHandler();
        }

        protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
        {
            LogHelper.Info($"---->开始请求：{request.RequestUri}");

            HttpResponseMessage responseMessage = null;

            try
            {
                responseMessage = await base.SendAsync(request, cancellationToken);
                responseMessage.EnsureSuccessStatusCode();
            }
            catch (System.Exception ex)
            {
                LogHelper.Error($"---->请求失败：{request.RequestUri}，错误信息：{ex.Message}");
                throw new BusinessException(_businessExceptionMsg);
            }

            var content = await responseMessage.Content.ReadAsStringAsync(cancellationToken);

            LogHelper.Info($"---->请求接受返回信息为：{content}");

            return responseMessage;
        }
    }
}