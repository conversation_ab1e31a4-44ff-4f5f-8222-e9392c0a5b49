﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Model.Input.ArticleManager;
using Dqy.Syjx.Entity.ArticleManager;
using Dqy.Syjx.Model.Param.ArticleManager;
using Dqy.Syjx.Service.ArticleManager;
using Dqy.Syjx.Web.Code;
using Dqy.Syjx.Enum;

namespace Dqy.Syjx.Business.ArticleManager
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2022-01-21 10:43
    /// 描 述：业务类
    /// </summary>
    public class ArticleCategoryBLL
    {
        /// <summary>
        /// ApiToken值（仅WebApi端传入）
        /// </summary>
        public string ApiToken = string.Empty;

        private ArticleCategoryService articleCategoryService = new ArticleCategoryService();
        private ArticleService articleService = new ArticleService();

        #region 获取数据
        public async Task<TData<List<ArticleCategoryEntity>>> GetList(ArticleCategoryListParam param)
        {
            OperatorInfo user = await Operator.Instance.Current(ApiToken);
            TData<List<ArticleCategoryEntity>> obj = new TData<List<ArticleCategoryEntity>>();
            param.UnitId = user.UnitId.Value;
            param.IsSystem = user.IsSystem;
            obj.Data = await articleCategoryService.GetList(param);
            obj.Total = obj.Data.Count;
            obj.Tag = 1;
            return obj;
        }

        public async Task<TData<List<ArticleCategoryEntity>>> GetPageList(ArticleCategoryListParam param, Pagination pagination)
        {
            OperatorInfo user = await Operator.Instance.Current(ApiToken);
            TData<List<ArticleCategoryEntity>> obj = new TData<List<ArticleCategoryEntity>>();
            param.UnitId = user.UnitId.Value;
            param.IsSystem = user.IsSystem;
            obj.Data = await articleCategoryService.GetPageList(param, pagination);
            obj.Total = pagination.TotalCount;
            obj.Tag = 1;
            return obj;
        }

        public async Task<TData<ArticleCategoryEntity>> GetEntity(long id)
        {
            TData<ArticleCategoryEntity> obj = new TData<ArticleCategoryEntity>();
            obj.Data = await articleCategoryService.GetEntity(id);
            if (obj.Data != null)
            {
                obj.Tag = 1;
            }
            return obj;
        }


        /// <summary>
        /// 查询首页资讯数据信息
        /// </summary>
        /// <returns></returns>
        public async Task<TData<List<ArticleCategoryEntity>>> GetIndexArticle()
        {
            TData<List<ArticleCategoryEntity>> obj = new TData<List<ArticleCategoryEntity>>();
            obj.Data = await articleCategoryService.GetIndexCategoryList(3);
            foreach(ArticleCategoryEntity category in obj.Data)
            {
                category.listArticle = await articleService.GetIndexArticleList(category.Id.Value, 4);
            }
            return obj;
        }

        /// <summary>
        /// 查询首页资讯数据信息
        /// </summary>
        /// <returns></returns>
        public async Task<TData<object>> GetArticle(int cateogrynum, int articlenum)
        {
            TData<object> obj = new TData<object>();
            var categoryList = await articleCategoryService.GetIndexCategoryList(cateogrynum);
            var listArticle = await articleService.GetArticleList(articlenum);
            obj.Data = new { CategoryList = categoryList, ArticleList = listArticle };
            obj.Tag = 1;
            obj.Message = "查询成功。";
            return obj;
        }

        public async Task<List<ArticleEntity>> GetPageArticleByCidList(ArticleListParam param, Pagination pagination)
        {
            return await articleService.GetPageList(param, pagination);
        }

        #endregion

        #region 提交数据
        public async Task<TData<string>> SaveForm(ArticleCategoryInputModel model)
        {
            TData<string> obj = new TData<string>();
            OperatorInfo user = await Operator.Instance.Current(ApiToken);
           
            var entity = new ArticleCategoryEntity();
            if (model.Id > 0)
            {
                entity = await articleCategoryService.GetEntity(model.Id);
            }
            entity.Id = model.Id;
            entity.Pid = model.Pid;
            entity.Name = model.Name;
            if (model.Pid == 0)
            {
                entity.Depth = 1;
            }
            else
            {
                entity.Depth = 2;
            }
            entity.Path = model.Path;
            entity.Sort = model.Sort;
            entity.Icon1 = model.Icon1;
            entity.Icon2 = model.Icon2;
            entity.ConfigCode = model.ConfigCode;
            entity.CateType = model.CateType;
            entity.UserId = user.UserId.Value;
            entity.Sort = model.Sort;
            entity.UnitId = user.UnitId.Value;
            await articleCategoryService.SaveForm(entity);
            obj.Data = entity.Id.ParseToString();
            obj.Tag = 1;
            return obj;
        }

        public async Task<TData> DeleteForm(string ids)
        {
            TData obj = new TData();
            OperatorInfo user = await Operator.Instance.Current(ApiToken);
           
        
            if (ids.IsEmpty()){ return obj; }
            ArticleCategoryListParam param = new ArticleCategoryListParam { Ids = ids };
            param.UnitId = user.UnitId.Value;
            var list = await articleCategoryService.GetList(param);
            ids = "";
            foreach (var m in list)
            {
                 //此处需增加校验是否满足删除条件
            
                 ids += ", " + m.Id.Value;
             }
            obj.Tag = 1;
            if (ids.Length > 1)
                await articleCategoryService.DeleteForm(ids);
            else
                 obj.Tag = 0;
            return obj;
        }
        #endregion

        #region 私有方法
        #endregion
    }
}
