{"urls": "http://*:5002", "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "SystemConfig": {"DBProvider": "SqlServer", "DBConnectionString": "Data Source=***********;Initial Catalog=syjx;Persist Security Info=True;User ID=****;Password=****;TrustServerCertificate=true;", "DBCommandTimeout": 180}, "SOF": {"Url": "http://*************:8085/", "KeyIndex": "3", "KeyValue": "604ed3c7bd044bd59c7dba00ef0d863f", "ContainName": "abff8fc185174ff1b8c4f95e3b2d49b5_3", "PolicyName": "00243a3b-b4f1-4c9d-9231-2c558b2b9e29"}, "Quartz": {"LoginLogCron": "0 0 1 * * ?", "OperationLogCron": "0 5 1 * * ?", "InstrumentPurchaseStatisticsCron": "0 10 1 * * ?", "QueryStatisticsFunRoomUseCron": "0 15 1 * * ?", "QueryStatisticsExperimentRecordCron": "0 20 1 * * ?", "MenuListCron": "0 25 1 * * ?", "UserListCron": "0 30 1 * * ?"}}