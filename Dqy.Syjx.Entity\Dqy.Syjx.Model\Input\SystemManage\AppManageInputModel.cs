﻿using System;
using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations;
using Dqy.Syjx.Util;

namespace Dqy.Syjx.Model.Input.SystemManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2023-08-10 13:30
    /// 描 述：InputModel
    /// </summary>
    public class AppManageInputModel 
    {
        /// <summary>
        /// 主键
        /// </summary>
        /// <returns></returns>
        [Display(Name = "主键")]
        public long Id { get; set; }
        /// <summary>
        /// 类型(1：PC端；2：移动端)
        /// </summary>
        /// <returns></returns>
        [Display(Name = "类型(1：PC端；2：移动端)")]
        [Required(ErrorMessage = @"请输入{0}")] 
        public int AppType { get; set; }
        private string _AppName;
        /// <summary>
        /// 应用/平台名称
        /// </summary>
        /// <returns></returns>
        [Display(Name = "应用/平台名称")]
        [Required(ErrorMessage = @"请输入{0}")] 
        [StringLength(510, ErrorMessage = "{0}输入的字数超长，请控制在{2}到{1}", MinimumLength = 1)] 
        public string AppName
        { 
           get { return StringFilter.SearchSql(_AppName); }
           set { _AppName = value; }
        }
        private string _AppKey;
        /// <summary>
        /// AppKey
        /// </summary>
        /// <returns></returns>
        [Display(Name = "AppKey")]
        [StringLength(510, ErrorMessage = "{0}输入的字数超长，请控制在{2}到{1}", MinimumLength = 0)] 
        public string AppKey
        { 
           get { return StringFilter.SearchSql(_AppKey); }
           set { _AppKey = value; }
        }
        private string _Memo;
        /// <summary>
        /// 应用描述
        /// </summary>
        /// <returns></returns>
        [Display(Name = "应用描述")]
        [StringLength(1024, ErrorMessage = "{0}输入的字数超长，请控制在{2}到{1}", MinimumLength = 0)] 
        public string Memo
        { 
           get { return StringFilter.SearchSql(_Memo); }
           set { _Memo = value; }
        }
        private string _AppSecret;
        /// <summary>
        /// AppSecret
        /// </summary>
        /// <returns></returns>
        [Display(Name = "AppSecret")]
        [StringLength(510, ErrorMessage = "{0}输入的字数超长，请控制在{2}到{1}", MinimumLength = 0)] 
        public string AppSecret
        { 
           get { return StringFilter.SearchSql(_AppSecret); }
           set { _AppSecret = value; }
        }
        private string _AngentId;
        /// <summary>
        /// AngentId
        /// </summary>
        /// <returns></returns>
        [Display(Name = "AngentId")]
        [StringLength(510, ErrorMessage = "{0}输入的字数超长，请控制在{2}到{1}", MinimumLength = 0)] 
        public string AngentId
        { 
           get { return StringFilter.SearchSql(_AngentId); }
           set { _AngentId = value; }
        }
        private string _Secret;
        /// <summary>
        /// Secret
        /// </summary>
        /// <returns></returns>
        [Display(Name = "Secret")]
        [StringLength(510, ErrorMessage = "{0}输入的字数超长，请控制在{2}到{1}", MinimumLength = 0)] 
        public string Secret
        { 
           get { return StringFilter.SearchSql(_Secret); }
           set { _Secret = value; }
        }
        private string _CorpId;
        /// <summary>
        /// CorpId
        /// </summary>
        /// <returns></returns>
        [Display(Name = "CorpId")]
        [StringLength(510, ErrorMessage = "{0}输入的字数超长，请控制在{2}到{1}", MinimumLength = 0)] 
        public string CorpId
        { 
           get { return StringFilter.SearchSql(_CorpId); }
           set { _CorpId = value; }
        }
        private string _CorpSecret;
        /// <summary>
        /// CorpSecret
        /// </summary>
        /// <returns></returns>
        [Display(Name = "CorpSecret")]
        [StringLength(510, ErrorMessage = "{0}输入的字数超长，请控制在{2}到{1}", MinimumLength = 0)] 
        public string CorpSecret
        { 
           get { return StringFilter.SearchSql(_CorpSecret); }
           set { _CorpSecret = value; }
        }
        /// <summary>
        /// 单位类型
        /// </summary>
        /// <returns></returns>
        [Display(Name = "单位类型")]
        public int? UnitType { get; set; }
        /// <summary>
        /// 单位Id
        /// </summary>
        /// <returns></returns>
        [Display(Name = "单位Id")]
        public long? UnitId { get; set; }
        private string _AppCode;
        /// <summary>
        /// 应用编码（唯一字符串，自动生成）
        /// </summary>
        /// <returns></returns>
        [Display(Name = "应用编码（唯一字符串，自动生成）")]
        [StringLength(510, ErrorMessage = "{0}输入的字数超长，请控制在{2}到{1}", MinimumLength = 0)] 
        public string AppCode
        { 
           get { return StringFilter.SearchSql(_AppCode); }
           set { _AppCode = value; }
        }
        private string _ClientId;
        /// <summary>
        /// ClientID
        /// </summary>
        /// <returns></returns>
        [Display(Name = "ClientID")]
        [StringLength(510, ErrorMessage = "{0}输入的字数超长，请控制在{2}到{1}", MinimumLength = 0)] 
        public string ClientId
        { 
           get { return StringFilter.SearchSql(_ClientId); }
           set { _ClientId = value; }
        }
        private string _ClientSecret;
        /// <summary>
        /// ClientSecret
        /// </summary>
        /// <returns></returns>
        [Display(Name = "ClientSecret")]
        [StringLength(1024, ErrorMessage = "{0}输入的字数超长，请控制在{2}到{1}", MinimumLength = 0)] 
        public string ClientSecret
        { 
           get { return StringFilter.SearchSql(_ClientSecret); }
           set { _ClientSecret = value; }
        }
        private string _CallBackUrl;
        /// <summary>
        /// 回调地址
        /// </summary>
        /// <returns></returns>
        [Display(Name = "回调地址")]
        [StringLength(510, ErrorMessage = "{0}输入的字数超长，请控制在{2}到{1}", MinimumLength = 0)] 
        public string CallBackUrl
        { 
           get { return _CallBackUrl; }
           set { _CallBackUrl = value; }
        }
        private string _AuthHost;
        /// <summary>
        /// 统一身份认证接口地址
        /// </summary>
        /// <returns></returns>
        [Display(Name = "统一身份认证接口地址")]
        [StringLength(510, ErrorMessage = "{0}输入的字数超长，请控制在{2}到{1}", MinimumLength = 0)] 
        public string AuthHost
        { 
           get { return _AuthHost; }
           set { _AuthHost = value; }
        }

        private string _DataHost;
        /// <summary>
        /// 数据同步接口地址
        /// </summary>
        /// <returns></returns>
        [Display(Name = "数据同步接口地址")]
        [StringLength(510, ErrorMessage = "{0}输入的字数超长，请控制在{2}到{1}", MinimumLength = 0)]
        public string DataHost
        {
            get { return _DataHost; }
            set { _DataHost = value; }
        }

        private string _WebHost;
        /// <summary>
        /// 网站地址
        /// </summary>
        /// <returns></returns>
        [Display(Name = "网站地址")]
        [StringLength(510, ErrorMessage = "{0}输入的字数超长，请控制在{2}到{1}", MinimumLength = 0)]
        public string WebHost
        {
            get { return _WebHost; }
            set { _WebHost = value; }
        }

        private string _ApiName;
        /// <summary>
        /// 接口名称（例如：企业微信通讯录同步接口）
        /// </summary>
        /// <returns></returns>
        [Display(Name = "接口名称（例如：企业微信通讯录同步接口）")]
        [StringLength(510, ErrorMessage = "{0}输入的字数超长，请控制在{2}到{1}", MinimumLength = 0)] 
        public string ApiName
        { 
           get { return StringFilter.SearchSql(_ApiName); }
           set { _ApiName = value; }
        }
        private string _ApiSecret;
        /// <summary>
        /// 接口Secret（说明：企业微信通讯录同步接口必须使用接口的Secret才可调用）
        /// </summary>
        /// <returns></returns>
        [Display(Name = "接口Secret（说明：企业微信通讯录同步接口必须使用接口的Secret才可调用）")]
        [StringLength(1024, ErrorMessage = "{0}输入的字数超长，请控制在{2}到{1}", MinimumLength = 0)] 
        public string ApiSecret
        { 
           get { return StringFilter.SearchSql(_ApiSecret); }
           set { _ApiSecret = value; }
        }
        private string _GenerateAppKey;
        /// <summary>
        /// 生成的AppKey
        /// </summary>
        /// <returns></returns>
        [Display(Name = "生成的AppKey")]
        [StringLength(510, ErrorMessage = "{0}输入的字数超长，请控制在{2}到{1}", MinimumLength = 0)] 
        public string GenerateAppKey
        { 
           get { return StringFilter.SearchSql(_GenerateAppKey); }
           set { _GenerateAppKey = value; }
        }
        private string _GenerateAppSecret;
        /// <summary>
        /// 生成的AppSecret
        /// </summary>
        /// <returns></returns>
        [Display(Name = "生成的AppSecret")]
        [StringLength(1024, ErrorMessage = "{0}输入的字数超长，请控制在{2}到{1}", MinimumLength = 0)] 
        public string GenerateAppSecret
        { 
           get { return StringFilter.SearchSql(_GenerateAppSecret); }
           set { _GenerateAppSecret = value; }
        }

        /// <summary>
        /// 密钥类型（1：密钥主表  2：第三方密钥表）
        /// </summary>
        [Display(Name = "类型(1：密钥主表；2：第三方密钥表)")]
        [Required(ErrorMessage = @"请输入{0}")]
        public int IsMain { get; set; }
    }
}
