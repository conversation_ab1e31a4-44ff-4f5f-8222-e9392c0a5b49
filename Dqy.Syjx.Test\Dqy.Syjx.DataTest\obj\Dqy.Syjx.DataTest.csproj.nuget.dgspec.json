{"format": 1, "restore": {"D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Test\\Dqy.Syjx.DataTest\\Dqy.Syjx.DataTest.csproj": {}}, "projects": {"D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Business\\Dqy.Syjx.Service\\Dqy.Syjx.Service.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Business\\Dqy.Syjx.Service\\Dqy.Syjx.Service.csproj", "projectName": "Dqy.Syjx.Service", "projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Business\\Dqy.Syjx.Service\\Dqy.Syjx.Service.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Business\\Dqy.Syjx.Service\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["c:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Data\\Dqy.Syjx.Data.Repository\\Dqy.Syjx.Data.Repository.csproj": {"projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Data\\Dqy.Syjx.Data.Repository\\Dqy.Syjx.Data.Repository.csproj"}, "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Entity\\Dqy.Syjx.Entity\\Dqy.Syjx.Entity.csproj": {"projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Entity\\Dqy.Syjx.Entity\\Dqy.Syjx.Entity.csproj"}, "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Entity\\Dqy.Syjx.Model\\Dqy.Syjx.Model.csproj": {"projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Entity\\Dqy.Syjx.Model\\Dqy.Syjx.Model.csproj"}, "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Util\\Dqy.Syjx.IdGenerator\\Dqy.Syjx.IdGenerator.csproj": {"projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Util\\Dqy.Syjx.IdGenerator\\Dqy.Syjx.IdGenerator.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Refit": {"target": "Package", "version": "[8.0.0, )"}, "Refit.Newtonsoft.Json": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.428\\RuntimeIdentifierGraph.json"}}}, "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.Cache.Factory\\Dqy.Syjx.Cache.Factory.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.Cache.Factory\\Dqy.Syjx.Cache.Factory.csproj", "projectName": "Dqy.Syjx.Cache.Factory", "projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.Cache.Factory\\Dqy.Syjx.Cache.Factory.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.Cache.Factory\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["c:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.Cache.Interface\\Dqy.Syjx.Cache.Interface.csproj": {"projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.Cache.Interface\\Dqy.Syjx.Cache.Interface.csproj"}, "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.MemoryCache\\Dqy.Syjx.MemoryCache.csproj": {"projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.MemoryCache\\Dqy.Syjx.MemoryCache.csproj"}, "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.RedisCache\\Dqy.Syjx.RedisCache.csproj": {"projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.RedisCache\\Dqy.Syjx.RedisCache.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.428\\RuntimeIdentifierGraph.json"}}}, "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.Cache.Interface\\Dqy.Syjx.Cache.Interface.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.Cache.Interface\\Dqy.Syjx.Cache.Interface.csproj", "projectName": "Dqy.Syjx.Cache.Interface", "projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.Cache.Interface\\Dqy.Syjx.Cache.Interface.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.Cache.Interface\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["c:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.428\\RuntimeIdentifierGraph.json"}}}, "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.MemoryCache\\Dqy.Syjx.MemoryCache.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.MemoryCache\\Dqy.Syjx.MemoryCache.csproj", "projectName": "Dqy.Syjx.MemoryCache", "projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.MemoryCache\\Dqy.Syjx.MemoryCache.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.MemoryCache\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["c:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.Cache.Interface\\Dqy.Syjx.Cache.Interface.csproj": {"projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.Cache.Interface\\Dqy.Syjx.Cache.Interface.csproj"}, "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Util\\Dqy.Syjx.Util\\Dqy.Syjx.Util.csproj": {"projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Util\\Dqy.Syjx.Util\\Dqy.Syjx.Util.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.Extensions.Caching.Memory": {"target": "Package", "version": "[7.0.0, )"}, "MySqlConnector": {"target": "Package", "version": "[2.2.5, )"}, "Oracle.ManagedDataAccess.Core": {"target": "Package", "version": "[3.21.100, )"}, "Senparc.Weixin.WxOpen": {"target": "Package", "version": "[3.15.13, )"}, "System.Drawing.Common": {"target": "Package", "version": "[7.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.428\\RuntimeIdentifierGraph.json"}}}, "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.RedisCache\\Dqy.Syjx.RedisCache.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.RedisCache\\Dqy.Syjx.RedisCache.csproj", "projectName": "Dqy.Syjx.RedisCache", "projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.RedisCache\\Dqy.Syjx.RedisCache.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.RedisCache\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["c:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.Cache.Interface\\Dqy.Syjx.Cache.Interface.csproj": {"projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.Cache.Interface\\Dqy.Syjx.Cache.Interface.csproj"}, "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Util\\Dqy.Syjx.Util\\Dqy.Syjx.Util.csproj": {"projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Util\\Dqy.Syjx.Util\\Dqy.Syjx.Util.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"StackExchange.Redis": {"target": "Package", "version": "[2.2.50, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.428\\RuntimeIdentifierGraph.json"}}}, "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Data\\Dqy.Syjx.Data.EF\\Dqy.Syjx.Data.EF.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Data\\Dqy.Syjx.Data.EF\\Dqy.Syjx.Data.EF.csproj", "projectName": "Dqy.Syjx.Data.EF", "projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Data\\Dqy.Syjx.Data.EF\\Dqy.Syjx.Data.EF.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Data\\Dqy.Syjx.Data.EF\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["c:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Data\\Dqy.Syjx.Data\\Dqy.Syjx.Data.csproj": {"projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Data\\Dqy.Syjx.Data\\Dqy.Syjx.Data.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.EntityFrameworkCore.Relational": {"target": "Package", "version": "[6.0.19, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[6.0.19, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "Oracle.ManagedDataAccess.Core": {"target": "Package", "version": "[3.21.100, )"}, "Pomelo.EntityFrameworkCore.MySql": {"target": "Package", "version": "[6.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.428\\RuntimeIdentifierGraph.json"}}}, "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Data\\Dqy.Syjx.Data.Repository\\Dqy.Syjx.Data.Repository.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Data\\Dqy.Syjx.Data.Repository\\Dqy.Syjx.Data.Repository.csproj", "projectName": "Dqy.Syjx.Data.Repository", "projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Data\\Dqy.Syjx.Data.Repository\\Dqy.Syjx.Data.Repository.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Data\\Dqy.Syjx.Data.Repository\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["c:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Data\\Dqy.Syjx.Data.EF\\Dqy.Syjx.Data.EF.csproj": {"projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Data\\Dqy.Syjx.Data.EF\\Dqy.Syjx.Data.EF.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.428\\RuntimeIdentifierGraph.json"}}}, "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Data\\Dqy.Syjx.Data\\Dqy.Syjx.Data.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Data\\Dqy.Syjx.Data\\Dqy.Syjx.Data.csproj", "projectName": "Dqy.Syjx.Data", "projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Data\\Dqy.Syjx.Data\\Dqy.Syjx.Data.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Data\\Dqy.Syjx.Data\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["c:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Data\\ZackEFCore.Batch.Dm\\ZackEFCore.Batch.Dm_NET6.csproj": {"projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Data\\ZackEFCore.Batch.Dm\\ZackEFCore.Batch.Dm_NET6.csproj"}, "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Util\\Dqy.Syjx.Util\\Dqy.Syjx.Util.csproj": {"projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Util\\Dqy.Syjx.Util\\Dqy.Syjx.Util.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.EntityFrameworkCore.Relational": {"target": "Package", "version": "[6.0.19, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[6.0.19, )"}, "MySqlConnector": {"target": "Package", "version": "[2.2.5, )"}, "Oracle.ManagedDataAccess.Core": {"target": "Package", "version": "[3.21.100, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.428\\RuntimeIdentifierGraph.json"}}}, "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Data\\Zack.EFCore.Batch\\Zack.EFCore.Batch_NET6.csproj": {"version": "6.1.3", "restore": {"projectUniqueName": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Data\\Zack.EFCore.Batch\\Zack.EFCore.Batch_NET6.csproj", "projectName": "Zack.EFCore.Batch_NET6", "projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Data\\Zack.EFCore.Batch\\Zack.EFCore.Batch_NET6.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Data\\Zack.EFCore.Batch\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["c:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.EntityFrameworkCore.Relational": {"target": "Package", "version": "[6.0.19, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.428\\RuntimeIdentifierGraph.json"}}}, "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Data\\ZackEFCore.Batch.Dm\\ZackEFCore.Batch.Dm_NET6.csproj": {"version": "6.6.3", "restore": {"projectUniqueName": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Data\\ZackEFCore.Batch.Dm\\ZackEFCore.Batch.Dm_NET6.csproj", "projectName": "ZackEFCore.Batch.Dm_NET6", "projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Data\\ZackEFCore.Batch.Dm\\ZackEFCore.Batch.Dm_NET6.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Data\\ZackEFCore.Batch.Dm\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["c:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Data\\Zack.EFCore.Batch\\Zack.EFCore.Batch_NET6.csproj": {"projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Data\\Zack.EFCore.Batch\\Zack.EFCore.Batch_NET6.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.EntityFrameworkCore.Relational": {"target": "Package", "version": "[6.0.19, )"}, "dmdbms.Microsoft.EntityFrameworkCore.Dm": {"target": "Package", "version": "[6.0.16.16649, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.428\\RuntimeIdentifierGraph.json"}}}, "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Entity\\Dqy.Syjx.Entity\\Dqy.Syjx.Entity.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Entity\\Dqy.Syjx.Entity\\Dqy.Syjx.Entity.csproj", "projectName": "Dqy.Syjx.Entity", "projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Entity\\Dqy.Syjx.Entity\\Dqy.Syjx.Entity.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Entity\\Dqy.Syjx.Entity\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["c:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Util\\Dqy.Syjx.IdGenerator\\Dqy.Syjx.IdGenerator.csproj": {"projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Util\\Dqy.Syjx.IdGenerator\\Dqy.Syjx.IdGenerator.csproj"}, "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Web\\Dqy.Syjx.Web.Code\\Dqy.Syjx.Web.Code.csproj": {"projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Web\\Dqy.Syjx.Web.Code\\Dqy.Syjx.Web.Code.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.428\\RuntimeIdentifierGraph.json"}}}, "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Entity\\Dqy.Syjx.Enum\\Dqy.Syjx.Enum.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Entity\\Dqy.Syjx.Enum\\Dqy.Syjx.Enum.csproj", "projectName": "Dqy.Syjx.Enum", "projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Entity\\Dqy.Syjx.Enum\\Dqy.Syjx.Enum.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Entity\\Dqy.Syjx.Enum\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["c:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.428\\RuntimeIdentifierGraph.json"}}}, "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Entity\\Dqy.Syjx.Model\\Dqy.Syjx.Model.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Entity\\Dqy.Syjx.Model\\Dqy.Syjx.Model.csproj", "projectName": "Dqy.Syjx.Model", "projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Entity\\Dqy.Syjx.Model\\Dqy.Syjx.Model.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Entity\\Dqy.Syjx.Model\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["c:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Entity\\Dqy.Syjx.Entity\\Dqy.Syjx.Entity.csproj": {"projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Entity\\Dqy.Syjx.Entity\\Dqy.Syjx.Entity.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.428\\RuntimeIdentifierGraph.json"}}}, "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Test\\Dqy.Syjx.DataTest\\Dqy.Syjx.DataTest.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Test\\Dqy.Syjx.DataTest\\Dqy.Syjx.DataTest.csproj", "projectName": "Dqy.Syjx.DataTest", "projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Test\\Dqy.Syjx.DataTest\\Dqy.Syjx.DataTest.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Test\\Dqy.Syjx.DataTest\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["c:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Business\\Dqy.Syjx.Service\\Dqy.Syjx.Service.csproj": {"projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Business\\Dqy.Syjx.Service\\Dqy.Syjx.Service.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"NUnit3TestAdapter": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[3.16.1, )"}, "nunit": {"target": "Package", "version": "[3.12.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.428\\RuntimeIdentifierGraph.json"}}}, "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Util\\Dqy.Syjx.IdGenerator\\Dqy.Syjx.IdGenerator.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Util\\Dqy.Syjx.IdGenerator\\Dqy.Syjx.IdGenerator.csproj", "projectName": "Dqy.Syjx.IdGenerator", "projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Util\\Dqy.Syjx.IdGenerator\\Dqy.Syjx.IdGenerator.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Util\\Dqy.Syjx.IdGenerator\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["c:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Util\\Dqy.Syjx.Util\\Dqy.Syjx.Util.csproj": {"projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Util\\Dqy.Syjx.Util\\Dqy.Syjx.Util.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.428\\RuntimeIdentifierGraph.json"}}}, "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Util\\Dqy.Syjx.Util\\Dqy.Syjx.Util.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Util\\Dqy.Syjx.Util\\Dqy.Syjx.Util.csproj", "projectName": "Dqy.Syjx.Util", "projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Util\\Dqy.Syjx.Util\\Dqy.Syjx.Util.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Util\\Dqy.Syjx.Util\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["c:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Entity\\Dqy.Syjx.Enum\\Dqy.Syjx.Enum.csproj": {"projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Entity\\Dqy.Syjx.Enum\\Dqy.Syjx.Enum.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[10.1.1, )"}, "Castle.Core.AsyncInterceptor": {"target": "Package", "version": "[1.7.0, )"}, "EFCore.Sharding.SqlServer": {"target": "Package", "version": "[5.0.16, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[6.0.11, )"}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson": {"target": "Package", "version": "[6.0.19, )"}, "NLog.Web.AspNetCore": {"target": "Package", "version": "[4.14.0, )"}, "NPOI": {"target": "Package", "version": "[2.6.2, )"}, "NetDevPack.Security.JwtExtensions": {"target": "Package", "version": "[6.0.2, )"}, "QRCoder": {"target": "Package", "version": "[1.4.1, )"}, "SkiaSharp": {"target": "Package", "version": "[2.88.8, )"}, "System.Drawing.Common": {"target": "Package", "version": "[7.0.0, )"}, "System.Linq.Dynamic.Core": {"target": "Package", "version": "[1.3.5, )"}, "System.ServiceModel.Primitives": {"target": "Package", "version": "[4.8.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.428\\RuntimeIdentifierGraph.json"}}}, "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Web\\Dqy.Syjx.Web.Code\\Dqy.Syjx.Web.Code.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Web\\Dqy.Syjx.Web.Code\\Dqy.Syjx.Web.Code.csproj", "projectName": "Dqy.Syjx.Web.Code", "projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Web\\Dqy.Syjx.Web.Code\\Dqy.Syjx.Web.Code.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Web\\Dqy.Syjx.Web.Code\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["c:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.Cache.Factory\\Dqy.Syjx.Cache.Factory.csproj": {"projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.Cache.Factory\\Dqy.Syjx.Cache.Factory.csproj"}, "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Data\\Dqy.Syjx.Data.Repository\\Dqy.Syjx.Data.Repository.csproj": {"projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Data\\Dqy.Syjx.Data.Repository\\Dqy.Syjx.Data.Repository.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.428\\RuntimeIdentifierGraph.json"}}}}}