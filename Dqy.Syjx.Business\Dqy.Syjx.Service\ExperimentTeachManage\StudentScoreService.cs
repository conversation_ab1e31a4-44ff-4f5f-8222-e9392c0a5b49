﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.ExperimentTeachManage;
using Dqy.Syjx.Model.Param.ExperimentTeachManage;
using Dqy.Syjx.Entity.PersonManage;
using Dqy.Syjx.Model.Param.PersonManage;

namespace Dqy.Syjx.Service.ExperimentTeachManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2024-12-04 13:33
    /// 描 述：待评价实验服务类
    /// </summary>
    public class StudentScoreService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<StudentScoreEntity>> GetList(StudentScoreListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<StudentScoreEntity>> GetPageList(StudentScoreListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<StudentScoreEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<StudentScoreEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(StudentScoreEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(StudentScoreEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task UpdateForm(StudentScoreEntity entity, List<string> fields, Repository db)
        {
            if (fields != null)
            {
                await entity.Modify();
                fields.Add("BaseModifyTime");
                fields.Add("BaseModifierId");
            }
            if (db != null)
            {
                await db.Update(entity, fields);
            }
            else
            {
                await this.BaseRepository().Update(entity, fields);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update up_StudentScore set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
                strSql = $"update up_StudentScore set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<StudentScoreEntity, bool>> ListFilter(StudentScoreListParam param)
        {
            var expression = LinqExtensions.True<StudentScoreEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
            }
            return expression;
        }

        /// <summary>
        /// 实验预约查询
        /// </summary>
        /// <param name="param">查询参数</param>
        /// <param name="pagination">分页参数</param>
        /// <returns></returns>
        public async Task<List<StudentScoreEntity>> GetList(StudentScoreListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            var parameter = new List<DbParameter>();

            strSql.Append(@$" SELECT *  From ( 
                              SELECT ss1.* 
						  ,eb3.ExperimentName
						  ,eb3.ExperimentType
						  ,eb3.ClassTime ,eb3.SectionIndex ,eb3.SectionShow 
						  ,d4.DicName AS SchoolStageName
                          ,c3.DicName as GradeName
                          ,eb3.CourseId
                          ,c6.DicName as CourseName
						  FROM dbo.up_StudentScore AS ss1
						  INNER JOIN dbo.up_Student AS s2  ON s2.BaseIsDelete = 0 AND ss1.StudentId = s2.Id
						  INNER JOIN up_SchoolGradeClass AS sg1 ON sg1.BaseIsDelete = 0 AND s2.SchoolGradeClassId = sg1.Id
						  INNER JOIN  sys_static_dictionary AS c3 ON ss1.GradeId = c3.DictionaryId
						  INNER JOIN  sys_static_dictionary AS d4 ON ss1.SchoolStage = d4.DictionaryId 
						  INNER JOIN dbo.ex_ExperimentBooking AS eb3 ON eb3.BaseIsDelete = 0 AND ss1.ExperimentBookingId = eb3.Id
				          INNER JOIN  sys_static_dictionary AS c6 ON eb3.CourseId = c6.DictionaryId
						  ) AS tb1 Where BaseIsDelete = 0 ");
            if (param != null)
            { 
                if (!param.SchoolId.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolId = {param.SchoolId}");//根据用户登录的单位ID进行查询
                } 
                if (!param.ExperimentName.IsNullOrZero())
                {
                    strSql.Append($" AND ExperimentName like '%{param.ExperimentName}%' ");//根据用户登录的单位ID进行查询
                }
                if (!param.SchoolGradeClassId.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolGradeClassId = {param.SchoolGradeClassId} "); //年级班级Id
                }
                if (param.StudentId > 0)
                {
                    strSql.Append($" AND StudentId = {param.StudentId} ");
                }
                if (param.GradeId > 0)
                {
                    strSql.Append($" AND GradeId = {param.GradeId} ");
                }
                if (param.CourseId > 0)
                {
                    strSql.Append($" AND CourseId = {param.CourseId} ");
                }
                if (param.SchoolStageId > 0)
                {
                    strSql.Append($" AND SchoolStage = {param.SchoolStageId} ");
                } 
            }
            var list = await this.BaseRepository().FindList<StudentScoreEntity>(strSql.ToString(), parameter.ToArray(), pagination);
            return list.ToList();
        }
        #endregion
    }
}
