using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Dqy.Syjx.Web.Controllers;
using Dqy.Syjx.Business.OrganizationManage;
using Dqy.Syjx.Business.SystemManage;
using Dqy.Syjx.Entity.OrganizationManage;
using Dqy.Syjx.Model.Param;
using Dqy.Syjx.Model.Param.OrganizationManage;
using Dqy.Syjx.Model.Result;
using Dqy.Syjx.Model.Result.SystemManage;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Web.Code;
using Dqy.Syjx.Model.Input.OrganizationManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Entity.SystemManage;
using Dqy.Syjx.Model.Param.SystemManage;
using System.Linq;
using Dqy.Syjx.Enum.SystemManage;
using Dqy.Syjx.Business.PersonManage;
using Dqy.Syjx.Entity.PersonManage;
using Dqy.Syjx.Model.Param.PersonManage;
using Senparc.Weixin.WxOpen.Entities;

namespace Dqy.Syjx.Web.Areas.OrganizationManage.Controllers
{
    [Area("OrganizationManage")]
    public class UserController : BaseController
    {
        private StudentBLL studentBLL = new StudentBLL();
        private UserBLL userBLL = new UserBLL();
        private DepartmentBLL departmentBLL = new DepartmentBLL();
        private LogLoginBLL logLoginBLL = new LogLoginBLL();
        private RoleBLL roleBLL = new RoleBLL();

        private readonly NetHelper _netHelper;

        public UserController(NetHelper netHelper)
        {
            _netHelper = netHelper; // 注入 NetHelper
        }

        #region 视图功能
        [AuthorizeFilter("organization:user:view")]
        public IActionResult UserIndex()
        {
            return View();
        }

        public IActionResult UserForm()
        {
            return View();
        }

        public IActionResult UserDetail()
        {
       
         
            ViewBag.Ip = _netHelper.GetIp();
            return View();
        }

        public IActionResult ResetPassword()
        {
            return View();
        }

        public async Task<IActionResult> ChangePassword()
        {
            ViewBag.OperatorInfo = await Operator.Instance.Current();
            return View();
        }

        public IActionResult ChangeUser()
        {
            return View();
        }

        public async Task<IActionResult> UserPortrait()
        {
            ViewBag.OperatorInfo = await Operator.Instance.Current();
            return View();
        }

        public IActionResult UserImport()
        {
            return View();
        }

        public IActionResult XmSsStudentUpdate()
        {
            return View();
        }

        /// <summary>
        /// 用户列表页面(后加)
        /// </summary>
        /// <returns></returns>
        [AuthorizeFilter("organization:schoollistuser:view")]
        public async Task<IActionResult> SchoolMyUnitListIndex()
        {
            ViewBag.OperatorInfo = await Operator.Instance.Current();
            return View();
        }

        /// <summary>
        /// 用户列表添加页面(后加)
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> SchoolMyUnitListForm()
        {
            ViewBag.OperatorInfo = await Operator.Instance.Current();
            return View();
        }

        /// <summary>
        /// 部门批量设置页面
        /// </summary>
        /// <returns></returns>
        public IActionResult DepartmentForm()
        {
            return View();
        }

        /// <summary>
        /// 区县下属学校单位超管信息
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> SchoolListIndex()
        {
            ViewBag.OperatorInfo = await Operator.Instance.Current();
            return View();
        }

        /// <summary>
        /// 区县下属学校单位新增
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> SchoolListForm()
        {
            ViewBag.OperatorInfo = await Operator.Instance.Current();
            return View();
        }

        /// <summary>
        /// 市级下属区县单位超管信息
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> CountyListIndex()
        {
            ViewBag.OperatorInfo = await Operator.Instance.Current();
            return View();
        }

        /// <summary>
        /// 市级下属区县单位新增
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> CountyListForm()
        {
            ViewBag.OperatorInfo = await Operator.Instance.Current();
            return View();
        }

        /// <summary>
        /// 市级本单位用户信息
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> CityMyUnitListIndex()
        {
            ViewBag.OperatorInfo = await Operator.Instance.Current();
            return View();
        }

        /// <summary>
        /// 市级本单位用户信息新增
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> CityMyUnitListForm()
        {
            ViewBag.OperatorInfo = await Operator.Instance.Current();
            return View();
        }

        /// <summary>
        /// 区县本级用户信息
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> CountyMyUnitListIndex()
        {
            ViewBag.OperatorInfo = await Operator.Instance.Current();
            return View();
        }

        /// <summary>
        /// 区县本级用户信息新增
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> CountMyUnitListForm()
        {
            ViewBag.OperatorInfo = await Operator.Instance.Current();
            return View();
        }

        /// <summary>
        /// 用户信息导入
        /// </summary>
        /// <returns></returns>
        public IActionResult SchoolMyUnitImport()
        {
            return View();
        }

        /// <summary>
        /// 下属单位超管导入
        /// </summary>
        /// <returns></returns>
        public IActionResult SchoolImport()
        {
            return View();
        }

        /// <summary>
        /// 我的信息
        /// </summary>
        /// <returns></returns>
        public IActionResult UserInfo()
        {
            return View();
        }

        /// <summary>
        /// 用户修改密码
        /// </summary>
        /// <returns></returns>
        public IActionResult UserChangePassword()
        {
            return View();
        }


        public ActionResult SetStageForm()
        {
            return View();
        }

        public ActionResult SetCourseForm()
        {
            return View();
        }
        #endregion

        #region 获取数据
        [HttpGet]
        [AuthorizeFilter("organization:user:search")]
        public async Task<IActionResult> GetListJson(UserListParam param)
        {
            TData<List<UserEntity>> obj = await userBLL.GetList(param);
            return Json(obj);
        }

        [HttpGet]
        [AuthorizeFilter("organization:user:search")]
        public async Task<IActionResult> GetPaginationListJson(UserListParam param, Pagination pagination)
        {            
            param.IsSystem = 0;
            TData<List<UserEntity>> obj = await userBLL.GetAllPageList(param, pagination);
            return Json(obj);
        }

        [HttpGet]
        [AuthorizeFilter("organization:user:search")]
        public async Task<IActionResult> GetPageListJson(UserListParam param, Pagination pagination)
        {
            TData<List<UserEntity>> obj = await userBLL.GetUserAllList(param, pagination);
            if (obj.Total > 0)
            {
                obj.Data.ForEach(a =>
                {
                    a.Mobile = StringFilter.ReturnPhoneNO(a.Mobile);
                    //a.RealName = StringFilter.ReplaceWithSpecialChar(a.RealName, 1, 0);
                });
            }
            return Json(obj);
        }

        [HttpGet]
        public async Task<IActionResult> GetFormJson(long id)
        {
            //TData<UserEntity> obj = await userBLL.GetEntity(id);
            TData<UserEntity> obj = await userBLL.GetUserEntityById(id);
            return Json(obj);
        }

        [HttpGet]
        public async Task<IActionResult> GetUserAuthorizeJson()
        {
            TData<UserAuthorizeInfo> obj = new TData<UserAuthorizeInfo>();
            OperatorInfo operatorInfo = await Operator.Instance.Current();
            TData<List<MenuAuthorizeInfo>> objMenuAuthorizeInfo = await new MenuAuthorizeBLL().GetAuthorizeList(operatorInfo);
            obj.Data = new UserAuthorizeInfo();
            obj.Data.IsSystem = operatorInfo.IsSystem;
            if (objMenuAuthorizeInfo.Tag == 1)
            {
                obj.Data.MenuAuthorize = objMenuAuthorizeInfo.Data;
            }
            obj.Tag = 1;
            return Json(obj);
        }

        [HttpGet]
        public async Task<IActionResult> GetUnitLaboratoryUserListJson()
        {
            long laboratoryId = (RoleEnum.LaboratoryManager.ParseToInt() + 16508640061130100);
            OperatorInfo operatorInfo = await Operator.Instance.Current();
            UserListParam param = new UserListParam();
            param.UnitId = operatorInfo.UnitId;
            param.RoleId = laboratoryId;
            TData<List<UserEntity>> obj = await userBLL.GetComUserByRoleId(param);
            return Json(obj);
        }




        /// <summary>
        /// 获取用户列表信息(后加)
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        [HttpGet]
        [AuthorizeFilter("organization:user:search")]
        public async Task<IActionResult> GetUserListJson(UserListParam param, Pagination pagination, int isHide = 0)
        {
            TData<List<UserEntity>> obj = await userBLL.GetUserList(param, pagination);
            if (obj.Total > 0)
            {
                if (isHide == 0)
                {
                    obj.Data.ForEach(a =>
                    {
                        a.Mobile = StringFilter.ReturnPhoneNO(a.Mobile);
                        //a.RealName = StringFilter.ReplaceWithSpecialChar(a.RealName, 1, 0);
                    });
                }
            }
            return Json(obj);
        }

        /// <summary>
        /// 获取区县用户列表信息
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <param name="isHide"></param>
        /// <returns></returns>
        [HttpGet]
        [AuthorizeFilter("organization:user:search")]
        public async Task<IActionResult> GetCountyUserListJson(UserListParam param, Pagination pagination, int isHide = 0)
        {
            TData<List<UserEntity>> obj = await userBLL.GetCountyUserList(param, pagination);
            if (obj.Total > 0)
            {
                if (isHide == 0)
                {
                    obj.Data.ForEach(a =>
                    {
                        a.Mobile = StringFilter.ReturnPhoneNO(a.Mobile);
                        //a.RealName = StringFilter.ReplaceWithSpecialChar(a.RealName, 1, 0);
                    });
                }
            }
            return Json(obj);
        }

        /// <summary>
        /// 获取下属单位超管信息
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        [HttpGet]
        [AuthorizeFilter("organization:user:search")]
        public async Task<IActionResult> GetSuperUserList(UserListParam param, Pagination pagination)
        {
            OperatorInfo user = await Operator.Instance.Current();
            //if (user.UnitType == UnitTypeEnum.County.ParseToInt())
            //{
            //    param.RoleId = RoleEnum.SchoolManager.ParseToInt();
            //}
            //else if (user.UnitType == UnitTypeEnum.City.ParseToInt())
            //{
            //    param.RoleId = RoleEnum.CountyManager.ParseToInt();
            //}
            TData<List<UserEntity>> obj = await userBLL.GetSuperUserList(param, pagination);
            if (obj.Total > 0)
            {
                obj.Data.ForEach(a =>
                {
                    a.Mobile = StringFilter.ReturnPhoneNO(a.Mobile);
                    //a.RealName = StringFilter.ReplaceWithSpecialChar(a.RealName, 1, 0);
                });
            }
            return Json(obj);
        }
        [HttpGet]
        public async Task<IActionResult> GetAdminJson()
        {
            TData<string> obj = await userBLL.GetAdminJson();
            return Json(obj);
        }

        //
        [HttpGet]
        public async Task<IActionResult> GetUserByDepartmentList(UserListParam param)
        {
            TData<List<UserEntity>> obj = await userBLL.GetUserByDepartmentList(param);
            return Json(obj);
        }

        /// <summary>
        /// 获取用户下拉框信息不需要跟部门关联
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetUserListAll(UserListParam param)
        {
            param.IsNeedDepartment = false;
            TData<List<UserEntity>> obj = await userBLL.GetUserByDepartmentList(param);
            return Json(obj);
        }


        /// <summary>
        /// 根据单位Id，角色获取用户信息。 主要用于下拉框列表
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<IActionResult> GetUserListByRoleId(UserListParam param, Pagination pagination)
        {
            OperatorInfo user = await Operator.Instance.Current();
            pagination.PageSize = 100000;
            TData<List<UserEntity>> obj = await userBLL.GetUserList(param, pagination);
            obj.Message = user.UserId.ToString();
            return Json(obj);
        }

        /// <summary>
        /// 获取当前用户是否是第三方用户。
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> IsThirdUser()
        {
            TData<int> result = new TData<int>();
            OperatorInfo operatorInfo = await Operator.Instance.Current();
            TData<UserEntity> obj = await userBLL.GetEntity(operatorInfo.UserId.Value);
            if (obj != null && obj.Tag == 1 && obj.Data != null)
            {
                result.Description = "";
                result.Tag = 1;
                result.Data = 0;
                if (!string.IsNullOrEmpty(obj.Data.ThirdUserId))
                {
                    result.Data = 1;
                    //不转换，如果Id是string类型就会有问题。
                    //long tempUserId = 0;
                    //long.TryParse(obj.Data.ThirdUserId, out tempUserId);
                    //if (tempUserId > 0)
                    //{
                    //    result.Data = 1;
                    //}
                }
            }
            return Json(result);
        }

        /// <summary>
        ///  获取老师用户信息列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> GetTeacherListJson()
        {
            TData<object> obj = new TData<object>();
            var listTeach = await userBLL.GetListByRoleId(16508640061130153);
            obj.Data = listTeach.Data.Select(m => new { Id = m.Id.ToString(), RealName = m.RealName }).OrderBy(m => m.RealName);
            obj.Tag = 1;
            obj.Message = "查询成功。";
            return Json(obj);
        }

        /// <summary>
        ///  获取老师用户信息列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> GetTeacher31ListJson()
        {
            TData<object> obj = new TData<object>();
            UserListParam paramUser = new UserListParam();
            paramUser.RoleId = RoleValueEnum.CommonPerson.ParseToInt();
            paramUser.UserStatus = 1;
            var resultlist = await userBLL.GetUserRoleList(paramUser);
            if (resultlist != null && resultlist.Data != null && resultlist.Data.Count > 0)
            {
                obj.Data = resultlist.Data.Select(m => new { Id = m.Id.ToString(), RealName = m.RealName }).OrderBy(m => m.RealName);
                obj.Total = resultlist.Data.Count;
            }
            else
            {
                obj.Data = new List<object>();
            }
            obj.Tag = 1;
            obj.Message = "查询成功。";
            return Json(obj);
        }

        /// <summary>
        ///  获取备课组长集合
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> GetGroupLeaderListJson()
        {
            TData<object> obj = new TData<object>();
            UserListParam paramUser = new UserListParam();
            paramUser.RoleId = RoleValueEnum.GroupLeader.ParseToInt();
            paramUser.UserStatus = 1;
            var resultlist = await userBLL.GetUserRoleList(paramUser);
            if (resultlist != null && resultlist.Data != null && resultlist.Data.Count > 0)
            {
                obj.Data = resultlist.Data.Select(m => new { Id = m.Id.ToString(), RealName = m.RealName }).OrderBy(m => m.RealName);
                obj.Total = resultlist.Data.Count;
            }
            else
            {
                obj.Data = new List<object>();
            }
            obj.Tag = 1;
            obj.Message = "查询成功。";
            return Json(obj);
        }
        #endregion

        #region 获取管理员信息

        /// <summary>
        /// 是否是管理员，否：返回管理员名称。
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> GetIsAdminJson()
        {
            TData<object> obj = await userBLL.GetIsAdminInfo();
            return Json(obj);
        }

        /// <summary>
        /// 获取组装页面提示
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> GetPageMessage(int roleId, string fieldName, string title, string url)
        {
            string message = string.Empty;
            TData<List<UserEntity>> obj = await userBLL.GetIsRoleInfo(roleId, 2);
            string titlePage = "";
            if (roleId == RoleEnum.BusinessManager.ParseToInt())
            {
                titlePage = "「人员管理」-";
            }
            message += $"<span style='color: red'>{fieldName}</span>未配置。<br />";
            if (obj.Tag == 2)
            {
                //当前用户拥有指定角色                
                message += $"请先前往 {titlePage}「<a href='javascript:void(0);' url='{url}' title='{title}' onclick='goPage(this);'>{title}</a>」页面配置后再操作！";
            }
            else if (obj.Tag == 1)
            {
                message += $"请先联系“{((RoleEnum)roleId).GetDescription()}”前往 {titlePage}「{title}」页面配置后再操作！<br/>";
                if (obj.Data.Count > 0)
                {
                    message += "联系方式：";
                    foreach (var user in obj.Data)
                    {
                        message += $"{user.RealName}（{user.Mobile}）、";
                    }
                    message = message.TrimEnd('、');
                }
            }
            return Json(message);
        }
        #endregion

        #region 提交数据
        [HttpPost]
        [AuthorizeFilter("organization:user:add,organization:user:edit")]
        public async Task<IActionResult> SaveFormJson(UserInputModel model)
        {
            TData<string> obj = await userBLL.SaveForm(model);
            return Json(obj);
        }

        [HttpPost]
        [AuthorizeFilter("organization:user:delete")]
        public async Task<IActionResult> DeleteFormJson(string ids)
        {
            TData obj = await userBLL.DeleteForm(ids);
            return Json(obj);
        }

        [HttpPost]
        [AuthorizeFilter("organization:user:resetpassword")]
        public async Task<IActionResult> ResetPasswordJson(UserEntity entity)
        {
            TData<long> obj = await userBLL.ResetPassword(entity);
            return Json(obj);
        }

        [HttpPost]
        public async Task<IActionResult> ChangePasswordJson(ChangePasswordParam entity)
        {
            TData<long> obj = await userBLL.ChangePassword(entity);
            return Json(obj);
        }

        [HttpPost]
        public async Task<IActionResult> ChangeUserJson(UserEntity entity)
        {
            TData<long> obj = await userBLL.ChangeUser(entity);
            return Json(obj);
        }

        //[HttpPost]
        //public async Task<IActionResult> ImportUserJson(ImportParam param)
        //{
        //    List<UserEntity> list = new ExcelHelper<UserEntity>().ImportFromExcel(param.FilePath);
        //    TData obj = await userBLL.ImportUser(param, list);
        //    return Json(obj);
        //}

        [HttpPost]
        public async Task<IActionResult> ExportUserJson(UserListParam param)
        {
            TData<string> obj = new TData<string>();
            TData<List<UserEntity>> userObj = await userBLL.GetUserAllList(param, new Pagination() { PageSize = 100000000 });
            if (userObj.Tag == 1)
            {
                string file = new ExcelHelper<UserEntity>().ExportToExcel("用户列表.xls",
                                                                          "用户列表",
                                                                          userObj.Data,
                                                                          new string[] { "UnitName", "RealName", "Mobile", "UserName", "RoleNames", "DepartmentNames", "BaseModifyTime" });
                obj.Data = file;
                obj.Tag = 1;
            }
            return Json(obj);
        }


        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> DownLoadSuperUserJson()
        {
            TData<List<RoleEntity>> objRole = new TData<List<RoleEntity>>();
            TData<string> obj = new TData<string>();
            //获取平台角色（应陶瑞要求，去除管理员角色）
            objRole = await roleBLL.GetList(new RoleListParam() { ExcludeSystem = true });
            string[] strRoles = string.Join(",", objRole.Data.OrderBy(m => m.UnitType).ThenBy(m => m.RoleSort).Select(f => f.RoleName)).Split(',');
            //
            List<ExeclDownLoadModel> list = new List<ExeclDownLoadModel>();
            ExeclDownLoadModel m1 = new ExeclDownLoadModel() { ColumnName = "单位名称", ColumnWidth = 10000 };
            ExeclDownLoadModel m2 = new ExeclDownLoadModel() { ColumnName = "姓名", ColumnWidth = 6000 };
            ExeclDownLoadModel m3 = new ExeclDownLoadModel() { ColumnName = "手机号码", ColumnWidth = 6000 };
            ExeclDownLoadModel m4 = new ExeclDownLoadModel() { ColumnName = "账号", ColumnWidth = 6000 };
            ExeclDownLoadModel m5 = new ExeclDownLoadModel() { ColumnName = "密码", ColumnWidth = 6000 };
            ExeclDownLoadModel m6 = new ExeclDownLoadModel() { ColumnName = "角色", ColumnWidth = 8000, IsDropdown = true, DropdownData = strRoles };
            list.Add(m1); list.Add(m2); list.Add(m3); list.Add(m4); list.Add(m5); list.Add(m6);

            string file = new ExcelHelper<UserEntity>().ExportToComboxExcel("用户列表.xls", "用户列表", list);
            obj.Data = file;
            obj.Tag = 1;
            return Json(obj);
        }


        /// <summary>
        /// 学校导入用户列表下载
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> DownLoadSchoolUserJson()
        {
            TData<List<RoleEntity>> objRole = new TData<List<RoleEntity>>();
            TData<string> obj = new TData<string>();
            //获取平台角色
            objRole = await roleBLL.GetList(new RoleListParam() { UnitType = 3 });
            string[] strRoles = string.Join(",", objRole.Data.Select(f => f.RoleName)).Split(',');
            //
            List<ExeclDownLoadModel> list = new List<ExeclDownLoadModel>();
            ExeclDownLoadModel m2 = new ExeclDownLoadModel() { ColumnName = "姓名", ColumnWidth = 6000 };
            ExeclDownLoadModel m3 = new ExeclDownLoadModel() { ColumnName = "手机号码", ColumnWidth = 6000 };
            ExeclDownLoadModel m4 = new ExeclDownLoadModel() { ColumnName = "账号", ColumnWidth = 6000 };
            ExeclDownLoadModel m5 = new ExeclDownLoadModel() { ColumnName = "密码", ColumnWidth = 6000 };
            ExeclDownLoadModel m6 = new ExeclDownLoadModel() { ColumnName = "角色", ColumnWidth = 8000, IsDropdown = true, DropdownData = strRoles };
            list.Add(m2); list.Add(m3); list.Add(m4); list.Add(m5); list.Add(m6);

            string file = new ExcelHelper<UserEntity>().ExportToComboxExcel("学校用户信息.xls", "用户列表", list);
            obj.Data = file;
            obj.Tag = 1;
            return Json(obj);
        }


        /// <summary>
        /// 添加用户信息(后加)
        /// </summary>
        /// <param name="entity">用户实体</param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("organization:user:add,organization:user:edit")]
        public async Task<IActionResult> SaveUserFormJson(UserInputModel entity)
        {
            TData<string> obj = await userBLL.SaveUserForm(entity);
            return Json(obj);
        }

        /// <summary>
        /// 启用禁用用户(后加)
        /// </summary>
        /// <param name="id">用户id</param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("organization:user:edit")]
        public async Task<IActionResult> UpdateStatuzFormJson(long id)
        {
            TData obj = await userBLL.UpdateStatuzFormJson(id);
            return Json(obj);
        }

        /// <summary>
        /// 单条删除用户信息(后加)
        /// </summary>
        /// <param name="id">用户id</param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("organization:user:delete")]
        public async Task<IActionResult> DeleteUserFormJsonById(long id)
        {
            TData obj = await userBLL.DeleteUserFormById(id);
            return Json(obj);
        }

        /// <summary>
        /// 批量删除用户信息(后加)
        /// </summary>
        /// <param name="ids">用户Id集合</param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("organization:user:delete")]
        public async Task<IActionResult> DeleteUserFormJson(string ids)
        {
            TData obj = await userBLL.DeleteUserForm(ids);
            return Json(obj);
        }

        /// <summary>
        /// 获取单个用户信息(后加)
        /// </summary>
        /// <param name="id">用户Id</param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetFormJsonById(long id)
        {
            TData<UserEntity> obj = await userBLL.GetEntityById(id);
            return Json(obj);
        }

        /// <summary>
        /// 添加下属单位用户信息
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("organization:user:add,organization:user:edit")]
        public async Task<IActionResult> SaveSchoolUserJson(UserInputModel entity)
        {
            TData<string> obj = await userBLL.SaveSchoolUserForm(entity);
            return Json(obj);
        }

        /// <summary>
        /// 启用禁用学校单位用户信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("organization:user:edit")]
        public async Task<IActionResult> UpdateSchoolStatuzFormJson(long id)
        {
            TData obj = await userBLL.UpdateStatuzSchoolFormJson(id);
            return Json(obj);
        }

        /// <summary>
        /// 删除下属单位用户信息
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("organization:user:delete")]
        public async Task<IActionResult> DeleteSchoolUserFormJson(string ids)
        {
            TData obj = await userBLL.DeleteSchoolUserForm(ids);
            return Json(obj);
        }

        /// <summary>
        /// 超管启用禁用用户信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("organization:user:edit")]
        public async Task<IActionResult> UpdateStatuzBySystemSuper(long id)
        {
            TData obj = await userBLL.UpdateStatuzBySystemSuper(id);
            return Json(obj);
        }

        /// <summary>
        /// 超管删除用户信息
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("organization:user:delete")]
        public async Task<IActionResult> DeleteUserFormBySystemSuper(string ids)
        {
            TData obj = await userBLL.DeleteUserFormBySystemSuper(ids);
            return Json(obj);
        }

        /// <summary>
        /// 解锁用户
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("organization:user:unlock")]
        public async Task<IActionResult> UnlockUser(long id)
        {

            string userAgent = _netHelper.GetUserAgent(); 
            string ip = _netHelper.GetIp();
            string os = _netHelper.GetOSVersion(userAgent); 
            string browser = _netHelper.GetBrowser(userAgent); 

            TData obj = await userBLL.UnlockUser(id, ip, browser, os, userAgent);
            return Json(obj);
        }

        /// <summary>
        /// 学校解锁用户
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> SchoolUnlockUser(long id)
        {
            //NetHelper.SetConfigHttpContext(HttpContext);
            string userAgent = _netHelper.GetUserAgent();
            string ip = _netHelper.GetIp();
            string os = _netHelper.GetOSVersion(userAgent);
            string browser = _netHelper.GetBrowser(userAgent);

            TData obj = await userBLL.UnlockUser(id, ip, browser, os, userAgent);
            return Json(obj);
        }

        /// <summary>
        /// 学校导入用户信息
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> SchoolImportUserJson(ImportParam param)
        {
            List<UserEntity> list = new ExcelHelper<UserEntity>().ImportFromExcel(param.FilePath);
            TData obj = await userBLL.ImportSchoolUser(param, list);
            return Json(obj);
        }

        /// <summary>
        /// 区县导入下属单位超管信息
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> ImportSuperUserJson(ImportParam param)
        {
            List<UserEntity> list = new ExcelHelper<UserEntity>().ImportFromExcel(param.FilePath);
            TData obj = await userBLL.ImportBranchSuperUser(param, list);
            return Json(obj);
        }


        /// <summary>
        /// 获取当前登录用户信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetCurrentUserInfo()
        {
            TData<UserEntity> obj = await userBLL.GetCurrentSchoolUserInfo();
            return Json(obj);
        }

        /// <summary>
        /// 保存用户信息
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public async Task<IActionResult> SaveUserInfo(UserInfoInputModel entity)
        {
            TData<string> obj = await userBLL.SaveUserInfo(entity);
            return Json(obj);
        }

        /// <summary>
        /// 修改密码
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public async Task<IActionResult> SavePassWord(UserPswdInputModel entity)
        {
            TData<string> obj = await userBLL.SavePassWord(entity);
            return Json(obj);
        }

        /// <summary>
        /// 超级管理员导入用户信息
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> SuperImportUserJson(ImportParam param)
        {
            List<UserEntity> list = new ExcelHelper<UserEntity>().ImportFromExcel(param.FilePath);
            TData obj = await userBLL.SuperImportUser(param, list);
            return Json(obj);
        }


        [HttpGet]
        [AuthorizeFilter("organization:userstudent:search")]
        public async Task<ActionResult> GetStudentPageListJson(StudentListParam param, Pagination pagination)
        {
            TData<List<StudentEntity>> obj = await studentBLL.GetPageList(param, pagination);
            return Json(obj);
        }
        #endregion
    }
}