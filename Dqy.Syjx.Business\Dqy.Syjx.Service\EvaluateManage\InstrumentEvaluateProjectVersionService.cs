﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.EvaluateManage;
using Dqy.Syjx.Model.Param.EvaluateManage;

namespace Dqy.Syjx.Service.EvaluateManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-11-26 15:11
    /// 描 述：达标参数设置2服务类
    /// </summary>
    public class InstrumentEvaluateProjectVersionService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<InstrumentEvaluateProjectVersionEntity>> GetList(InstrumentEvaluateProjectVersionListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<InstrumentEvaluateProjectVersionEntity>> GetPageList(InstrumentEvaluateProjectVersionListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ListFilter(param, strSql);
            var list = await this.BaseRepository().FindList<InstrumentEvaluateProjectVersionEntity>(strSql.ToString(), filter.ToArray(), pagination);
            return list.ToList();
        }

        public async Task<InstrumentEvaluateProjectVersionEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<InstrumentEvaluateProjectVersionEntity>(id);
        }

        /// <summary>
        /// 获取评估项目名称下拉框数据
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<List<InstrumentEvaluateProjectVersionEntity>> GetProjectComboList(InstrumentEvaluateProjectVersionListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ListFilterCombo(param, strSql);
            var list = await this.BaseRepository().FindList<InstrumentEvaluateProjectVersionEntity>(strSql.ToString(), filter.ToArray(), pagination);
            return list.ToList();
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(InstrumentEvaluateProjectVersionEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(InstrumentEvaluateProjectVersionEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update eq_InstrumentEvaluateProjectVersion set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
                strSql = $"update eq_InstrumentEvaluateProjectVersion set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<InstrumentEvaluateProjectVersionEntity, bool>> ListFilter(InstrumentEvaluateProjectVersionListParam param)
        {
            var expression = LinqExtensions.True<InstrumentEvaluateProjectVersionEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (param.EvaluateStandardId != null)
                {
                    expression = expression.And(t => t.EvaluateStandardId == param.EvaluateStandardId.Value);
                }
            }
            return expression;
        }
        private List<DbParameter> ListFilter(InstrumentEvaluateProjectVersionListParam param, StringBuilder strSql)
        {
            strSql.Append(@"  SELECT * From (
                SELECT
                a1.Id ,
                a1.BaseIsDelete ,
                a1.BaseCreateTime ,
                a1.BaseModifyTime ,
                a1.BaseCreatorId ,
                a1.BaseModifierId ,
                a1.BaseVersion ,
                a1.EvaluateProjectId ,
                a1.SchoolStage ,
                a1.DictionaryId1005 ,
                a1.EvaluateStandardId ,
                a1.Remark ,
                a1.Statuz ,
                project2.EvaluateName ,
                dic2.DicName AS SchoolStageName ,
                dic3.DicName AS SubjectName ,
                standard5.VersionName ,
                project2.IsDefaultEv
                FROM  eq_InstrumentEvaluateProjectVersion AS a1
                INNER JOIN  eq_InstrumentEvaluateProject AS project2 ON a1.EvaluateProjectId = project2.Id
                INNER JOIN  sys_static_dictionary AS dic2 ON a1.SchoolStage = dic2.DictionaryId AND dic2.BaseIsDelete = 0
                INNER JOIN  sys_static_dictionary AS dic3 ON a1.DictionaryId1005  = dic3.DictionaryId AND dic3.BaseIsDelete = 0
                INNER JOIN  eq_InstrumentEvaluateStandard AS standard5 ON a1.EvaluateStandardId = standard5.Id
            ) as tb1 WHERE   1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = @BaseIsDelete ");
                parameter.Add(DbParameterExtension.CreateDbParameter("@BaseIsDelete", param.BaseIsDelete));
                if (param.SchoolStage > 0)
                {
                    strSql.Append(" AND SchoolStage = @SchoolStage ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStage", param.SchoolStage));
                }
                if (param.DictionaryId1005 > 0)
                {
                    strSql.Append(" AND DictionaryId1005 = @DictionaryId1005 ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId1005", param.DictionaryId1005));
                }
                if (param.EvaluateStandardId > 0)
                {
                    strSql.Append(" AND EvaluateStandardId = @EvaluateStandardId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@EvaluateStandardId", param.EvaluateStandardId));
                }
                if (!string.IsNullOrEmpty(param.EvaluateName))
                {
                    strSql.Append(" AND EvaluateName like @EvaluateName ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@EvaluateName", $"%{param.EvaluateName}%"));
                }

                if (param.SchoolStageList != null && param.SchoolStageList.Count > 0)
                {
                    for (int i = 0; i < param.SchoolStageList.Count; i++)
                    {
                        if (i == 0)
                        {
                            strSql.Append($" AND ( SchoolStage = {param.SchoolStageList[i]}");
                        }
                        else
                        {
                            strSql.Append($" OR SchoolStage = {param.SchoolStageList[i]} ");
                        }
                    }
                    strSql.Append(" )");
                }
                if (param.SubjectIdList != null && param.SubjectIdList.Count > 0)
                {
                    for (int i = 0; i < param.SubjectIdList.Count; i++)
                    {
                        if (i == 0)
                        {
                            strSql.Append($" AND ( DictionaryId1005 = {param.SubjectIdList[i]}");
                        }
                        else
                        {
                            strSql.Append($" OR DictionaryId1005 = {param.SubjectIdList[i]} ");
                        }
                    }
                    strSql.Append(" )");
                }
            }
            return parameter;
        }


        /// <summary>
        /// 获取评估项目名称下拉框数据
        /// </summary>
        /// <param name="param"></param>
        /// <param name="strSql"></param>
        /// <returns></returns>
        private List<DbParameter> ListFilterCombo(InstrumentEvaluateProjectVersionListParam param, StringBuilder strSql)
        {
            strSql.Append(@"  
                SELECT DISTINCT EP.Id ,EP.EvaluateName ,ISNULL(EP.IsDefaultEv ,0) AS IsDefaultEv 
                FROM  eq_InstrumentEvaluateProject AS EP
                INNER JOIN  eq_InstrumentEvaluateProjectVersion AS EPV ON EP.Id = EPV.EvaluateProjectId AND EPV.BaseIsDelete = 0
                INNER JOIN  eq_InstrumentEvaluateStandard AS ES ON EPV.EvaluateStandardId = ES.Id AND ES.BaseIsDelete = 0
                WHERE EP.BaseIsDelete = 0
             ");
            var parameter = new List<DbParameter>();
            if (param.SchoolStage > 0)
            {
                strSql.Append(" AND EPV.SchoolStage = @SchoolStage ");
                parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStage", param.SchoolStage));
            }
            if (param.SchoolStageList != null && param.SchoolStageList.Count > 0)
            {
                for (int i = 0; i < param.SchoolStageList.Count; i++)
                {
                    if (i == 0)
                    {
                        strSql.Append($" AND ( EPV.SchoolStage = {param.SchoolStageList[i]}");
                    }
                    else
                    {
                        strSql.Append($" OR EPV.SchoolStage = {param.SchoolStageList[i]} ");
                    }
                }
                strSql.Append(" )");
            }
            if (param.SubjectIdList != null && param.SubjectIdList.Count > 0)
            {
                for (int i = 0; i < param.SubjectIdList.Count; i++)
                {
                    if (i == 0)
                    {
                        strSql.Append($" AND ( EPV.DictionaryId1005 = {param.SubjectIdList[i]}");
                    }
                    else
                    {
                        strSql.Append($" OR EPV.DictionaryId1005 = {param.SubjectIdList[i]} ");
                    }
                }
                strSql.Append(" )");
            }
            return parameter;
        }

        /// <summary>
        /// 根据学校属性获取仪器标达标参数设置版本ID集合(废弃)
        /// </summary>
        /// <param name="schoolProp">学校属性</param>
        /// <returns></returns>
        public async Task<List<InstrumentEvaluateProjectVersionEntity>> GetEvaluateProjectId(int schoolProp)
        {
            string sql = string.Format(@"SELECT Id
                                        FROM eq_InstrumentEvaluateProjectVersion 
                                        WHERE BaseIsDelete = 0 AND SchoolStage IN 
                                        (
	                                        SELECT DR.DictionaryToId FROM sys_dictionary_relation AS DR 
	                                        INNER JOIN sys_static_dictionary AS D ON DR.DictionaryId = D.DictionaryId AND D.TypeCode = '1001' AND D.BaseIsDelete = 0
	                                        inner JOIN sys_static_dictionary AS D2 ON DR.DictionaryToId = D2.DictionaryId AND D2.TypeCode = '1002' AND D2.BaseIsDelete = 0
	                                        WHERE DR.DictionaryId = {0} AND DR.BaseIsDelete = 0
                                        )", schoolProp);
            var list = await this.BaseRepository().FindList<InstrumentEvaluateProjectVersionEntity>(sql);
            return list.ToList();
        }

        #endregion
    }
}
