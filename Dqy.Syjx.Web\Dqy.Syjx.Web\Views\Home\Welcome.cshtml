
@using Dqy.Syjx.Entity.ArticleManager
@using Dqy.Syjx.Util.Model
@{
    Layout = "~/Views/Shared/_FormGray.cshtml";
    OperatorInfo operatorInfo = ViewBag.OperatorInfo;
    string unitName = null;
    string realName = null;
    string roleNames = null;
    string roleIds = null;
    int unitType = 0;
    long userId = 0;
    if (operatorInfo != null)
    {
        unitName = operatorInfo.UnitName ?? "";
        realName = operatorInfo.RealName ?? "";
        roleNames = operatorInfo.RoleNames ?? "";
        unitType = operatorInfo.UnitType;
        roleIds = "," + operatorInfo.RoleValues + ",";
        userId = operatorInfo.UserId.Value;
    }
    TData<List<ArticleCategoryEntity>> list = ViewBag.Article as TData<List<ArticleCategoryEntity>>;

    List<MenuEntity> menuList = ViewBag.MenuList;
}
<style>

    #table-1 {
        height:95%;
        border-bottom-width: 1px;
        border-bottom-style: solid;
        border-bottom-color: #a8bfde;
    }

        #table-1 td, #table-1 th {
            padding: 0px 10px;
            font-size: 14px;
            /*font-family: Verdana;*/
            /* color: #5b7da3;*/
            /*line-height: 2.57;*/
        }

        #table-1 a {
            text-decoration: none;
            /*color: #06C;*/
            color: inherit;
        }

            #table-1 a:hover {
                text-decoration: underline;
                /*color: #FD942A;*/
                color: inherit;
            }

        #table-1 p {
            font-size: 16px;
        }

    .font1 {
        font-size: 16px;
        /*font-family: Verdana;*/
        /*color: #5b7da3;*/
    }

    .font2 {
        font-size: 16px;
        /*font-family: Verdana;*/
        /*color: rgb(95, 74, 121);*/
        /* color: rgb(149, 170, 109);*/
    }

    .font3 {
        font-size: 16px;
        /*font-weight: bold;*/
        /*font-family: Verdana;*/
        /* color: #FE804F;*/
        color: inherit;
    }

    .font4 {
        font-size: 16px;
        /*font-family: Verdana;*/
        /* color: #5b7da3;*/
        color: inherit;
    }




    #table-2 {
        border-bottom-width: 1px;
        border-bottom-style: solid;
        border-bottom-color: rgb(230, 189, 189);
    }

        /* Padding and font style */
        #table-2 td, #table-2 th {
            padding: 5px 10px;
            font-size: 12px;
            font-family: Verdana;
            color: rgb(177, 106, 104);
        }


    .news {
        width: 100%;
        /* border: 1px solid gray;*/
        /* padding: 20px 15px;*/
    }

        .news h2 {
            padding-bottom: 5px;
            color: white;
            font-weight: bold;
        }

        .news ul {
            padding: 5px 0px;
            background: white;
        }

            .news ul li {
                list-style: none;
                height: 30px;
                line-height: 30px;
                border-bottom: 1px dashed gray;
                text-indent: 15px;
                font-size: 14px;
                word-break: break-all;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                overflow: hidden;
            }

        .news a {
            text-decoration: none;
            /*color: #06C;*/
            color: inherit;
        }

            .news a:hover {
                text-decoration: underline;
                /*color: #FD942A;*/
                color: inherit;
            }

    h3 {
        font-size: 13px;
    }




    #table-4 {
        border-bottom-width: 1px;
        border-bottom-style: solid;
        border-bottom-color: rgb(211, 202, 221);
    }

        /* Padding and font style */
        #table-4 td, #table-4 th {
            padding: 5px 10px;
            font-size: 14px;
            font-family: Verdana;
            /* color: #06C;*/
            color: inherit;
        }


        #table-4 a {
            text-decoration: none;
            /* color: #06C;*/
            color: inherit;
        }

            #table-4 a:hover {
                text-decoration: underline;
                /*  color: #FD942A;*/
                color: inherit;
            }

    #Marquee_x a {
        color: inherit;
    }

        #Marquee_x a:hover {
            font-size: 13px;
            text-decoration: none;
            /* color: #FD942A;*/
            color: inherit;
        }


    img {
        border: 0;
        vertical-align: middle
    }

    .clear {
        clear: both;
        height: 1px;
        width: 100%;
        overflow: hidden;
        margin-top: -1px
    }

    a {
        color: #000000;
        text-decoration: none
    }

        a:hover {
            color: #BA2636;
            text-decoration: underline
        }

    #mrq {
        width: 925px;
        margin: 20px auto;
        border: 1px solid #000;
        padding: 2px
    }

    #Marquee_x {
        overflow: hidden;
        width: 100%;
    }

        #Marquee_x div {
            float: left;
            line-height: 25px;
            height: 141px;
            overflow: hidden;
        }

            #Marquee_x div img {
                height: 115px;
                display: block
            }

            #Marquee_x div span {
                display: block;
            }

    .aPicButton span {
        opacity: 0.4;
        filter: alpha(opacity=40);
    }
</style>

<div class="wrapper wrapper-content">
    @if (unitType == 3)
    {
        <div class="col-sm-12">
            <div class="row">
                <div class="col-sm-9">

                    <div class="row">
                        <div class="col-sm-6">
                            <div class="col-sm-12" style="margin-left:-15px;">

                                <div class="panel panel-default" style="margin-left: -15px; margin-right: -15px;height:635px;">
                                    <div class="panel-heading">
                                        <b><i class="fa fa-plane"></i><span style="padding-left:15px;">快捷入口</span></b>
                                    </div>
                                    <div class="panel-body" style="height:100%">
                                        <table id="table-1" style="width: 100%; border-bottom: none !important; ">
                                            <tr>
                                                <td>
                                                    <span>学科教师</span>
                                                </td>
                                                @if (menuList.Exists(f => f.MenuName.Equals("按计划预约")))
                                                {
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/ExperimentTeachManage/ExperimentBooking/PlanList")', '按计划预约')">按计划预约</a></td>
                                                }
                                                else
                                                {
                                                    <td><span style="color:#999;">按计划预约</span></td>
                                                }
                                                @if (menuList.Exists(f => f.MenuName.Equals("待登记实验")))
                                                {
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/ExperimentTeachManage/ExperimentBooking/RecordList")', '待登记实验')">待登记实验</a></td>
                                                }
                                                else
                                                {
                                                    <td><span style="color:#999;">待登记实验</span></td>
                                                }
                                                @if (menuList.Exists(f => f.MenuName.Equals("专用室使用登记")))
                                                {
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/BusinessManage/FunRoomUse/AddForm")', '专用室使用登记')">专用室使用登记</a></td>
                                                }
                                                else
                                                {
                                                    <td><span style="color:#999;">专用室使用登记</span></td>
                                                }
                                            </tr>

                                       
                                            <tr>
                                                <td colspan="4" style="border-bottom:dashed 1px #a8bfde;"></td>
                                            </tr>


                                            <tr>
                                                <td rowspan="2">
                                                    <span>实验室管理员</span>
                                                </td>
                                                @if (menuList.Exists(f => f.MenuName.Equals("待安排实验")))
                                                {
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/ExperimentTeachManage/ExperimentBooking/ArrangeList")', '待安排实验')">待安排实验</a></td>
                                                }
                                                else
                                                {
                                                    <td><span style="color:#999;">待安排实验</span></td>
                                                }

                                                @if (menuList.Exists(f => f.MenuName.Equals("填报计划")))
                                                {
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/InstrumentManage/PurchaseDeclaration/PurchaseFilling")', '填报计划')">填报计划</a></td>
                                                }
                                                else
                                                {
                                                    <td><span style="color:#999;">填报计划</span></td>
                                                }

                                                @if (menuList.Exists(f => f.MenuName.Equals("仪器清单")))
                                                {
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/InstrumentManage/SchoolInstrument/EditInfoIndex")', '仪器清单')">仪器清单</a></td>
                                                }
                                                else
                                                {
                                                    <td><span style="color:#999;">仪器清单</span></td>
                                                }
                                            </tr>
                                            <tr>
                                                @if (menuList.Exists(f => f.MenuName.Equals("实验室列表")))
                                                {
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/BusinessManage/FunRoom/Index")', '实验室列表')">实验室列表</a></td>
                                                }
                                                else
                                                {
                                                    <td><span style="color:#999;">实验室列表</span></td>
                                                }
                                            </tr>
                                          
                                            <tr>
                                                <td colspan="4" style="border-bottom: dashed 1px #a8bfde;"></td>
                                            </tr>
                                            <tr>
                                                <td rowspan="2"><p>教务主任</p></td>
                                                @if (menuList.Exists(f => f.MenuName.Equals("教师任课设置")))
                                                {
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/OrganizationManage/SchoolGradeClass/CourseTeachList")', '教师任课设置')">教师任课设置</a></td>
                                                }
                                                else
                                                {
                                                    <td><span style="color:#999;">教师任课设置</span></td>
                                                }
                                                @if (menuList.Exists(f => f.MenuName.Equals("备课组长设置")))
                                                {
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/PersonManage/UserClassInfo/UserGradeTeach")', '备课组长设置')">备课组长设置</a></td>
                                                }
                                                else
                                                {
                                                    <td><span style="color:#999;">备课组长设置</span></td>
                                                }
                                                @if (menuList.Exists(f => f.MenuName.Equals("待审批计划")))
                                                {
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/InstrumentManage/PurchaseAudit/PurchaseAuditIndex")', '待审批计划')">待审批计划</a></td>
                                                }
                                                else
                                                {
                                                    <td><span style="color:#999;">待审批计划</span></td>
                                                }
                                            </tr>
                                            <tr>
                                                @if (menuList.Exists(f => f.MenuName.Equals("实验员授权")))
                                                {
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/BusinessManage/UserSchoolStageSubject/StageSubjectInput")', '实验员授权')">实验员授权</a></td>
                                                }
                                                else
                                                {
                                                    <td><span style="color:#999;">实验员授权</span></td>
                                                }
                                                @if (menuList.Exists(f => f.MenuName.Equals("实验员更换")))
                                                {
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/BusinessManage/FunRoom/SafeguardUserIndex")', '实验员更换')">实验员更换</a></td>
                                                }
                                                else
                                                {
                                                    <td><span style="color:#999;">实验员更换</span></td>
                                                }
                                                @if (menuList.Exists(f => f.MenuName.Equals("必填项配置")))
                                                {
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/SystemManage/ConfigSet/PageValidSet")', '必填项配置')">必填项配置</a></td>
                                                }
                                                else
                                                {
                                                    <td><span style="color:#999;">必填项配置</span></td>
                                                }
                                            </tr>
                                            <tr>
                                                <td colspan="4" style="border-bottom: dashed 1px #a8bfde;"></td>
                                            </tr>
                                            <tr>
                                                <td><span>备课组长</span></td>
                                                @if (menuList.Exists(f => f.MenuName.Equals("编制计划")))
                                                {
                                                    <td colspan="2"><a href="#" onclick="openMenu('@Url.Content("~/ExperimentTeachManage/PlanInfo/List")', '编制计划')">编制计划</a></td>
                                                }
                                                else
                                                {
                                                    <td><span style="color:#999;">编制计划</span></td>
                                                }

                                            </tr>
                                            <tr>
                                                <td colspan="4" style="border-bottom: dashed 1px #a8bfde;"></td>
                                            </tr>

                                            <tr>
                                                <td><span>安全管理员</span></td>
                                                @if (menuList.Exists(f => f.MenuName.Equals("安全排查与整改")))
                                                {
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/BusinessManage/CheckRectification/CheckRectificationIndex")', '安全排查与整改')">安全排查与整改</a></td>
                                                }
                                                else
                                                {
                                                    <td><span style="color:#999;">安全排查与整改</span></td>
                                                }
                                                @if (menuList.Exists(f => f.MenuName.Equals("安全教育与培训")))
                                                {
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/BusinessManage/TrainSafeEducation/TrainSafeEducationIndex")', '安全教育与培训')">安全教育与培训</a></td>
                                                }
                                                else
                                                {
                                                    <td><span style="color:#999;">安全教育与培训</span></td>
                                                }
                                                @if (menuList.Exists(f => f.MenuName.Equals("应急预案与演练")))
                                                {
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/BusinessManage/EmergencyPlan/EmergencyPlanIndex")', '应急预案与演练')">应急预案与演练</a></td>
                                                }
                                                else
                                                {
                                                    <td><span style="color:#999;">应急预案与演练</span></td>
                                                }
                                            </tr>
                                            <tr>
                                                <td colspan="4" style="border-bottom: dashed 1px #a8bfde;"></td>
                                            </tr>

                                            <tr>
                                                <td rowspan="2"><span>系统管理员</span></td>
                                                @if (menuList.Exists(f => f.MenuName.Equals("用户信息")))
                                                {
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/OrganizationManage/User/SchoolMyUnitListIndex")', '用户信息')">用户信息</a></td>
                                                }
                                                else
                                                {
                                                    <td><span style="color:#999;">用户信息</span></td>
                                                }
                                                @if (menuList.Exists(f => f.MenuName.Equals("组织架构")))
                                                {
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/OrganizationManage/Department/DepartmentIndex")', '组织架构')">组织架构</a></td>
                                                }
                                                else
                                                {
                                                    <td><span style="color:#999;">组织架构</span></td>
                                                }
                                                @if (menuList.Exists(f => f.MenuName.Equals("地点管理")))
                                                {
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/OrganizationManage/Address/AddressIndex")', '地点管理')">地点管理</a></td>
                                                }
                                                else
                                                {
                                                    <td><span style="color:#999;">地点管理</span></td>
                                                }

                                            </tr>
                                            <tr>
                                                @if (menuList.Exists(f => f.MenuName.Equals("年级班级信息")))
                                                {
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/OrganizationManage/SchoolGradeClass/SchoolGradeClassIndex")', '年级班级信息')">年级班级信息</a></td>
                                                }
                                                else
                                                {
                                                    <td><span style="color:#999;">年级班级信息</span></td>
                                                }
                                                @if (menuList.Exists(f => f.MenuName.Equals("课程节次表")))
                                                {
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/OrganizationManage/CourseSection/CourseSectionIndex")', '课程节次表')">课程节次表</a></td>
                                                }
                                                else
                                                {
                                                    <td><span style="color:#999;">课程节次表</span></td>
                                                }
                                                @if (menuList.Exists(f => f.MenuName.Equals("单位信息")))
                                                {
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/OrganizationManage/Unit/UnitIndex")', '单位信息')">单位信息</a></td>
                                                }
                                                else
                                                {
                                                    <td><span style="color:#999;">单位信息</span></td>
                                                }
                                            </tr>

                                        </table>
                                    </div>
                                </div>

                            </div>
                        </div>
                        <div class="col-sm-6" style="margin-left:-15px;margin-right:-15px;">
                            <div class="col-sm-12" style="margin-right: -15px;">
                                <div class="panel panel-default" style="height:407px;margin-left:-30px;margin-right:-30px;">
                                    <div class="panel-heading">
                                        <b><i class="fa fa-desktop"></i><span style="padding-left:15px;">产品简介</span></b>
                                    </div>
                                    <div class="panel-body">

                                        <div id="myCarousel" class="carousel slide" data-ride="carousel" data-interval="5000">
                                            <!-- 轮播（Carousel）指标 -->
                                            <ol class="carousel-indicators">
                                                <li data-target="#myCarousel" data-slide-to="0" class="active"></li>
                                                <li data-target="#myCarousel" data-slide-to="1"></li>
                                                <li data-target="#myCarousel" data-slide-to="2"></li>
                                                <li data-target="#myCarousel" data-slide-to="3"></li>
                                                <li data-target="#myCarousel" data-slide-to="4"></li>
                                                <li data-target="#myCarousel" data-slide-to="5"></li>
                                            </ol>
                                            <!-- 轮播（Carousel）项目 -->
                                            <div class="carousel-inner">
                                                <div class="item active">
                                                    <img src="~/image/1.jpg" style="height:340px;width:600px;" alt="First slide">
                                                    @*<div class="carousel-caption">产品简介1</div>*@
                                                </div>
                                                <div class="item">
                                                    <img src="~/image/2.jpg" style="height: 340px; width: 600px;" alt="Second slide">
                                                    @*<div class="carousel-caption">产品简介2</div>*@
                                                </div>
                                                <div class="item">
                                                    <img src="~/image/3.jpg" style="height: 340px; width: 600px;" alt="Third slide">
                                                    @*<div class="carousel-caption">产品简介3</div>*@
                                                </div>
                                                <div class="item">
                                                    <img src="~/image/4.jpg" style="height: 340px; width: 600px;" alt="Third slide">
                                                    @*<div class="carousel-caption">产品简介3</div>*@
                                                </div>
                                                <div class="item">
                                                    <img src="~/image/5.jpg" style="height: 340px; width: 600px;" alt="Third slide">
                                                    @*<div class="carousel-caption">产品简介3</div>*@
                                                </div>
                                                <div class="item">
                                                    <img src="~/image/6.jpg" style="height: 340px; width: 600px;" alt="Third slide">
                                                    @*<div class="carousel-caption">产品简介3</div>*@
                                                </div>
                                            </div>
                                            <!-- 轮播（Carousel）导航 -->
                                            <a class="left carousel-control aPicButton" href="#myCarousel" role="button" data-slide="prev">
                                                <span class="glyphicon glyphicon-chevron-left" aria-hidden="true"></span>
                                                <span class="sr-only">Previous</span>
                                            </a>
                                            <a class="right carousel-control aPicButton" href="#myCarousel" role="button" data-slide="next">
                                                <span class="glyphicon glyphicon-chevron-right" aria-hidden="true"></span>
                                                <span class="sr-only">Next</span>
                                            </a>
                                        </div>


                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-12" style="margin-right: -15px;">
                                <div class="panel panel-default" style="height: 208px; margin-left: -30px; margin-right: -30px; ">
                                    <div class="panel-heading">
                                        <b><i class="fa fa-pie-chart"></i><span style="padding-left:15px;">仪器展示</span></b>
                                    </div>
                                    <div class="panel-body">
                                        <div class="row" id="Marquee_x">
                                            <div class="col-sm-3">
                                                <a href="http://www.51jyyq.com/" target="_blank">
                                                    <img src="~/image/i1.jpg" />
                                                    <span style="text-align:center;">物理-化学-生物</span>
                                                </a>
                                            </div>
                                            <div class="col-sm-3">
                                                <a href="http://www.51jyyq.com/" target="_blank">
                                                    <img src="~/image/i4.jpg" />
                                                    <span style="text-align:center;">历史-地理-语言</span>
                                                </a>
                                            </div>
                                            <div class="col-sm-3">
                                                <a href="http://www.51jyyq.com/" target="_blank">
                                                    <img src="~/image/i2.jpg" />
                                                    <span style="text-align:center;">音乐-体育-美术</span>
                                                </a>
                                            </div>
                                            <div class="col-sm-3">
                                                <a href="http://www.51jyyq.com/" target="_blank">
                                                    <img src="~/image/i3.jpg" />
                                                    <span style="text-align:center;">科学-信息-实践</span>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12" style="height:auto">
                            <div class="panel panel-default" style="height:140px;margin-left:-15px;">
                                <div class="panel-heading">
                                    <b><i class="fa fa-user"></i><span style="padding-left:15px;">个人信息</span></b>
                                </div>
                                <div class="panel-body">
                                    <p>
                                        <span class="font3" id="pUserName" style="padding-left:50px;cursor:pointer;" onclick="showChangeUserForm()">@realName (@unitName)</span>
                                    </p>
                                    <p>
                                        <span style="padding-left:50px;">@roleNames</span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-3" style="margin-left:-15px;margin-right:-30px;">
                    <div class="col-sm-12" style="margin-left: -15px; margin-right: -30px;">
                        <div class="panel panel-default" style="height: 634px; margin-right: -45px;">
                            <div class="panel-heading">
                                <b><i class="fa fa-navicon"></i><span style="padding-left:15px;">资讯信息</span></b>
                            </div>
                            <div class="panel-body">

                                @if (list.Data != null && list.Data.Count > 0)
                                {
                                    int index = 0;
                                    foreach (ArticleCategoryEntity articleCate in list.Data)
                                    {
                                        if (index == 0)
                                        {
                                            <div class="news" style="height:180px;">
                                                <h3>@articleCate.Name</h3>
                                                <ul>
                                                    @foreach (ArticleEntity article in articleCate.listArticle)
                                                    {
                                                        <li><a href="@Url.Content("/ArticleManager/Article/ArticleDetail/"+article.Id)" title="@article.ShortTitle" target="_blank">@article.ShortTitle</a></li>
                                                    }
                                                </ul>
                                            </div>
                                        }
                                        else
                                        {
                                            <div class="news" style="margin-top: 15px; height: 180px;">
                                                <h3>@articleCate.Name</h3>
                                                <ul>
                                                    @foreach (ArticleEntity article in articleCate.listArticle)
                                                    {
                                                        <li><a href="@Url.Content("/ArticleManager/Article/ArticleDetail/"+article.Id)" title="@article.ShortTitle" target="_blank">@article.ShortTitle</a></li>
                                                    }
                                                </ul>
                                            </div>
                                        }
                                        index++;
                                    }
                                }

                            </div>
                        </div>
                    </div>

                    <!--根据唐雅婷先暂时隐藏20220420 by zyf-->
                    @*<div class="col-sm-12" style="margin-left: -15px; margin-right: -30px;">
                            <div class="panel panel-default" style="height: 140px; margin-right: -45px;">

                                <div class="panel-heading">
                                    <b><i class="fa fa-vcard-o"></i><span style="padding-left:15px;">客服信息</span></b>
                                </div>
                                <div class="panel-body">
                                    <table id="table-4" style="width:100%;">
                                        <tr>
                                            <td>联系QQ：</td>
                                            <td><a href="tencent://message/?uin=874242713&amp;Site=有事Q我&amp;Menu=yes">874242713</a></td>
                                            <td><a href="tencent://message/?uin=3148703411&amp;Site=有事Q我&amp;Menu=yes">3148703411</a></td>
                                        </tr>
                                    </table>
                                    <table id="table-4" style="width: 100%; margin-top: 5px; border-bottom: none !important;">
                                        <tr>
                                            <td>联系电话：</td>
                                            <td>15261852677 </td>
                                            <td> 18175369365</td>
                                        </tr>

                                    </table>
                                </div>
                            </div>
                        </div>*@
                </div>
            </div>
        </div>
    }
    else if (unitType == 2)
    {
        <div class="col-sm-12">
            <div class="row">
                <div class="col-sm-9">

                    <div class="row">
                        <div class="col-sm-6">
                            <div class="col-sm-12" style="margin-left:-15px;">

                                <div class="panel panel-default" style="margin-left: -15px; margin-right: -15px;height:475px;">
                                    <div class="panel-heading">
                                        <b><i class="fa fa-plane"></i><span style="padding-left:15px;">快捷入口</span></b>
                                    </div>
                                    <div class="panel-body">
                                        <table id="table-1" style="width: 100%; border-bottom: none !important; line-height:3; ">
                                            <tr>
                                                <td><p>实验仪器</p></td>
                                                @if (menuList.Exists(f => f.MenuName.Equals("计划查看")))
                                                {
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/InstrumentManage/PurchaseStatistics/PurchaseGroupStaticIndex")', '计划查看')">计划查看</a></td>
                                                }
                                                else
                                                {
                                                    <td><span style="color:#999;">计划查看</span></td>
                                                }
                                                @if (menuList.Exists(f => f.MenuName.Equals("仪器清单")))
                                                {
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/QueryStatisticsManage/Instrument/CountyStockDetail")', '仪器清单')">仪器清单</a></td>
                                                }
                                                else
                                                {
                                                    <td><span style="color:#999;">仪器清单</span></td>
                                                }
                                                @if (menuList.Exists(f => f.MenuName.Equals("达标结果查看")))
                                                {
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/EvaluateManage/InstrumentAttendStatic/InstrumentGroupStaticIndex")', '达标结果查看')">达标结果查看</a></td>
                                                }
                                                else
                                                {
                                                    <td><span style="color:#999;">达标结果查看</span></td>
                                                }
                                            </tr>
                                            <tr>
                                                <td colspan="4" style="border-bottom: dashed 1px #a8bfde;"></td>
                                            </tr>
                                            <tr>
                                                <td rowspan="2">
                                                    <p>实验室</p>
                                                </td>
                                                @if (menuList.Exists(f => f.MenuName.Equals("实验室统计")))
                                                {
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/QueryStatisticsManage/FunRoom/CountyFunRoomStatistics")', '实验室统计')">实验室统计</a></td>
                                                }
                                                else
                                                {
                                                    <td><span style="color:#999;">实验室统计</span></td>
                                                }
                                                @if (menuList.Exists(f => f.MenuName.Equals("达标结果查看")))
                                                {
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/EvaluateManage/InstrumentAttendStatic/InstrumentGroupStaticIndex")', '达标结果查看')">达标结果查看</a></td>
                                                }
                                                else
                                                {
                                                    <td><span style="color:#999;">达标结果查看</span></td>
                                                }
                                                @if (menuList.Exists(f => f.MenuName.Equals("使用记录统计")))
                                                {
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/QueryStatisticsManage/FunRoomUse/CountyRecordList")', '使用记录统计')">使用记录统计</a></td>
                                                }
                                                else
                                                {
                                                    <td><span style="color:#999;">使用记录统计</span></td>
                                                }

                                            </tr>
                                            <tr>
                                                @if (menuList.Exists(f => f.MenuName.Equals("管理制度查询")))
                                                {
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/QueryStatisticsManage/FunRoom/CountyInstitution")', '管理制度查询')">管理制度查询</a></td>
                                                }
                                                else
                                                {
                                                    <td><span style="color:#999;">管理制度查询</span></td>
                                                }
                                                @if (menuList.Exists(f => f.MenuName.Equals("安全排查查询")))
                                                {
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/QueryStatisticsManage/FunRoom/CountySafetyCheck")', '安全排查查询')">安全排查查询</a></td>
                                                }
                                                else
                                                {
                                                    <td><span style="color:#999;">安全排查查询</span></td>
                                                }
                                                @if (menuList.Exists(f => f.MenuName.Equals("安全保障查询")))
                                                {
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/QueryStatisticsManage/FunRoom/CountySafeGuarantee")', '安全保障查询')">安全保障查询</a></td>
                                                }
                                                else
                                                {
                                                    <td><span style="color:#999;">安全保障查询</span></td>
                                                }

                                            </tr>
                                            <tr>
                                                <td colspan="4" style="border-bottom: dashed 1px #a8bfde;"></td>
                                            </tr>
                                            <tr>
                                                <td><p>实验教学</p></td>
                                                 @if (menuList.Exists(f => f.MenuName.Equals("已编实验计划")))
                                                {
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/QueryStatisticsManage/ExperimentTeach/CountyPlanInfoList")', '已编实验计划')">已编实验计划</a></td>
                                                }
                                                else
                                                {
                                                    <td><span style="color:#999;">已编实验计划</span></td>
                                                }
                                                 @if (menuList.Exists(f => f.MenuName.Equals("实验开出记录")))
                                                {
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/QueryStatisticsManage/ExperimentTeach/ExperimentRecordStatistics")', '实验开出记录')">实验开出记录</a></td>
                                                }
                                                else
                                                {
                                                    <td><span style="color:#999;">实验开出记录</span></td>
                                                }
                                                @if (menuList.Exists(f => f.MenuName.Equals("实验开出统计")))
                                                {
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/EvaluateManage/ExperimentAttendStatic/ExperimentAttendStaticIndex")', '实验开出统计')">实验开出统计</a></td>
                                                }
                                                else
                                                {
                                                    <td><span style="color:#999;">实验开出统计</span></td>
                                                }
                                                
                                            </tr>
                                            <tr>
                                                <td colspan="4" style="border-bottom: dashed 1px #a8bfde;"></td>
                                            </tr>
                                            <tr>
                                                <td><p>实验员</p></td>
                                                @if (menuList.Exists(f => f.MenuName.Equals("实验员信息")))
                                                {
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/QueryStatisticsManage/UserInfo/CountyExperimenterList")', '实验员信息')">实验员信息</a></td>
                                                }
                                                else
                                                {
                                                    <td><span style="color:#999;">实验员信息</span></td>
                                                }
                                                @if (menuList.Exists(f => f.MenuName.Equals("培训信息")))
                                                {
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/QueryStatisticsManage/UserInfo/CountyTrainList")', '培训信息')">培训信息</a></td>
                                                }
                                                else
                                                {
                                                    <td><span style="color:#999;">培训信息</span></td>
                                                }
                                                @if (menuList.Exists(f => f.MenuName.Equals("汇总信息")))
                                                {
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/QueryStatisticsManage/UnitInfo/SummaryList")', '汇总信息')">汇总信息</a></td>
                                                }
                                                else
                                                {
                                                    <td><span style="color:#999;">汇总信息</span></td>
                                                }

                                            </tr>
                                            <tr>
                                                <td colspan="4" style="border-bottom: dashed 1px #a8bfde;"></td>
                                            </tr>
                                            <tr>
                                                <td rowspan="2"><p>系统设置</p></td>
                                                @if (menuList.Exists(f => f.MenuName.Equals("下属单位管理")))
                                                {
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/OrganizationManage/Unit/UnitChildren")', '下属单位管理')">下属单位管理</a></td>
                                                }
                                                else
                                                {
                                                    <td><span style="color:#999;">下属单位管理</span></td>
                                                }
                                                @if (menuList.Exists(f => f.MenuName.Equals("下属单位超管信息")))
                                                {
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/OrganizationManage/User/SchoolListIndex")', '下属单位超管信息')">下属单位超管信息</a></td>
                                                }
                                                else
                                                {
                                                    <td><span style="color:#999;">下属单位超管信息</span></td>
                                                }
                                                @if (menuList.Exists(f => f.MenuName.Equals("区县本级用户信息")))
                                                {
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/OrganizationManage/User/CountyMyUnitListIndex")', '区县本级用户信息')">区县本级用户信息</a></td>
                                                }
                                                else
                                                {
                                                    <td><span style="color:#999;">区县本级用户信息</span></td>
                                                }
                                                
                                            </tr>
                                            <tr>
                                                @if (menuList.Exists(f => f.MenuName.Equals("必填项配置")))
                                                {
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/SystemManage/ConfigSet/PageValidSet")', '必填项配置')">必填项配置</a></td>
                                                }
                                                else
                                                {
                                                    <td><span style="color:#999;">必填项配置</span></td>
                                                }
                                                @if (menuList.Exists(f => f.MenuName.Equals("实验教学设置")))
                                                {
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/SystemManage/ConfigSet/ConfigSetIndex?m=1003&t=实验教学设置")', '实验教学设置')">实验教学设置</a></td>
                                                }
                                                else
                                                {
                                                    <td><span style="color:#999;">实验教学设置</span></td>
                                                }
                                            </tr>
                                           @* <tr>
                                                <td colspan="4" style="border-bottom: dashed 1px #a8bfde;"></td>
                                            </tr>*@
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <div class="col-sm-12" style="margin-left: -15px;">
                                <div class="panel panel-default" style="height: 140px; margin-left: -15px; margin-right: -15px;">
                                    <div class="panel-heading">
                                        <b><i class="fa fa-user"></i><span style="padding-left:15px;">个人信息</span></b>
                                    </div>
                                    <div class="panel-body">
                                        <p>
                                            <span class="font3" id="pUserName" style="padding-left:50px;cursor:pointer;" onclick="showChangeUserForm()">@realName (@unitName)</span>
                                        </p>
                                        <p>
                                            <span style="padding-left:50px;">@roleNames</span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-sm-6" style="margin-left:-15px;margin-right:-15px;">
                            <div class="col-sm-12" style="margin-right: -15px;">
                                <div class="panel panel-default" style="height:407px;margin-left:-30px;margin-right:-30px;">
                                    <div class="panel-heading">
                                        <b><i class="fa fa-desktop"></i><span style="padding-left:15px;">产品简介</span></b>
                                    </div>
                                    <div class="panel-body">

                                        <div id="myCarousel" class="carousel slide" data-ride="carousel" data-interval="5000">
                                            <!-- 轮播（Carousel）指标 -->
                                            <ol class="carousel-indicators">
                                                <li data-target="#myCarousel" data-slide-to="0" class="active"></li>
                                                <li data-target="#myCarousel" data-slide-to="1"></li>
                                                <li data-target="#myCarousel" data-slide-to="2"></li>
                                                <li data-target="#myCarousel" data-slide-to="3"></li>
                                                <li data-target="#myCarousel" data-slide-to="4"></li>
                                                <li data-target="#myCarousel" data-slide-to="5"></li>
                                            </ol>
                                            <!-- 轮播（Carousel）项目 -->
                                            <div class="carousel-inner">
                                                <div class="item active">
                                                    <img src="~/image/1.jpg" style="height:340px;width:600px;" alt="First slide">
                                                    @*<div class="carousel-caption">产品简介1</div>*@
                                                </div>
                                                <div class="item">
                                                    <img src="~/image/2.jpg" style="height: 340px; width: 600px;" alt="Second slide">
                                                    @*<div class="carousel-caption">产品简介2</div>*@
                                                </div>
                                                <div class="item">
                                                    <img src="~/image/3.jpg" style="height: 340px; width: 600px;" alt="Third slide">
                                                    @*<div class="carousel-caption">产品简介3</div>*@
                                                </div>
                                                <div class="item">
                                                    <img src="~/image/4.jpg" style="height: 340px; width: 600px;" alt="Third slide">
                                                    @*<div class="carousel-caption">产品简介3</div>*@
                                                </div>
                                                <div class="item">
                                                    <img src="~/image/5.jpg" style="height: 340px; width: 600px;" alt="Third slide">
                                                    @*<div class="carousel-caption">产品简介3</div>*@
                                                </div>
                                                <div class="item">
                                                    <img src="~/image/6.jpg" style="height: 340px; width: 600px;" alt="Third slide">
                                                    @*<div class="carousel-caption">产品简介3</div>*@
                                                </div>
                                            </div>
                                            <!-- 轮播（Carousel）导航 -->
                                            <a class="left carousel-control aPicButton" href="#myCarousel" role="button" data-slide="prev">
                                                <span class="glyphicon glyphicon-chevron-left" aria-hidden="true"></span>
                                                <span class="sr-only">Previous</span>
                                            </a>
                                            <a class="right carousel-control aPicButton" href="#myCarousel" role="button" data-slide="next">
                                                <span class="glyphicon glyphicon-chevron-right" aria-hidden="true"></span>
                                                <span class="sr-only">Next</span>
                                            </a>
                                        </div>


                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-12" style="margin-right: -15px;">
                                <div class="panel panel-default" style="height: 208px; margin-left: -30px; margin-right: -30px; ">
                                    <div class="panel-heading">
                                        <b><i class="fa fa-pie-chart"></i><span style="padding-left:15px;">仪器展示</span></b>
                                    </div>
                                    <div class="panel-body">
                                        <div class="row" id="Marquee_x">
                                            <div class="col-sm-3">
                                                <a href="http://www.51jyyq.com/" target="_blank">
                                                    <img src="~/image/i1.jpg" />
                                                    <span style="text-align:center;">物理-化学-生物</span>
                                                </a>
                                            </div>
                                            <div class="col-sm-3">
                                                <a href="http://www.51jyyq.com/" target="_blank">
                                                    <img src="~/image/i4.jpg" />
                                                    <span style="text-align:center;">历史-地理-语言</span>
                                                </a>
                                            </div>
                                            <div class="col-sm-3">
                                                <a href="http://www.51jyyq.com/" target="_blank">
                                                    <img src="~/image/i2.jpg" />
                                                    <span style="text-align:center;">音乐-体育-美术</span>
                                                </a>
                                            </div>
                                            <div class="col-sm-3">
                                                <a href="http://www.51jyyq.com/" target="_blank">
                                                    <img src="~/image/i3.jpg" />
                                                    <span style="text-align:center;">科学-信息-实践</span>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-sm-3" style="margin-left:-15px;margin-right:-30px;">
                    <div class="col-sm-12" style="margin-left: -15px; margin-right: -30px;">
                        <div class="panel panel-default" style="height: 634px; margin-right: -45px;">
                            <div class="panel-heading">
                                <b><i class="fa fa-navicon"></i><span style="padding-left:15px;">资讯信息</span></b>
                            </div>
                            <div class="panel-body">

                                @if (list.Data != null && list.Data.Count > 0)
                                {
                                    int index = 0;
                                    foreach (ArticleCategoryEntity articleCate in list.Data)
                                    {
                                        if (index == 0)
                                        {
                                            <div class="news" style="height:180px;">
                                                <h3>@articleCate.Name</h3>
                                                <ul>
                                                    @foreach (ArticleEntity article in articleCate.listArticle)
                                                    {
                                                        <li><a href="@Url.Content("/ArticleManager/Article/ArticleDetail/"+article.Id)" title="@article.ShortTitle" target="_blank">@article.ShortTitle</a></li>
                                                    }
                                                </ul>
                                            </div>
                                        }
                                        else
                                        {
                                            <div class="news" style="margin-top: 15px; height: 180px;">
                                                <h3>@articleCate.Name</h3>
                                                <ul>
                                                    @foreach (ArticleEntity article in articleCate.listArticle)
                                                    {
                                                        <li><a href="@Url.Content("/ArticleManager/Article/ArticleDetail/"+article.Id)" title="@article.ShortTitle" target="_blank">@article.ShortTitle</a></li>
                                                    }
                                                </ul>
                                            </div>
                                        }
                                        index++;
                                    }
                                }

                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    }
    else if (unitType == 1)
    {
        <div class="col-sm-12">
            <div class="row">
                <div class="col-sm-9">

                    <div class="row">
                        <div class="col-sm-6">
                            <div class="col-sm-12" style="margin-left:-15px;">

                                <div class="panel panel-default" style="margin-left: -15px; margin-right: -15px;height:475px;">
                                    <div class="panel-heading">
                                        <b><i class="fa fa-plane"></i><span style="padding-left:15px;">快捷入口</span></b>
                                    </div>
                                    <div class="panel-body">
                                        <table id="table-1" style="width: 100%; border-bottom: none !important;line-height:3; ">
                                            @if (roleIds.Contains(",10,"))
                                            {
                                                <tr>
                                                    <td rowspan="2">
                                                        <p>教学仪器管理</p>
                                                    </td>
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/InstrumentManage/PurchaseStatistics/PurchaseGroupStaticIndex")', '采购计划查看')">采购计划查看</a></td>
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/InstrumentManage/PurchaseStatistics/PurchasePlanAnalyseIndex")', '采购计划分析')">采购计划分析</a></td>
                                                </tr>
                                                <tr>
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/QueryStatisticsManage/Instrument/CountyStockDetail")', '仪器明细查询')">仪器明细查询</a></td>
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/EvaluateManage/InstrumentAttendStatic/StandardResultAnalyseIndex")', '达标结果分析')">达标结果分析</a></td>
                                                </tr>
                                            }
                                            else
                                            {
                                                <tr>
                                                    <td rowspan="2">
                                                        <p>教学仪器管理</p>
                                                    </td>
                                                    <td><span style="color:#999;">采购计划查看</span></td>
                                                    <td><span style="color:#999;">采购计划分析</span></td>
                                                </tr>
                                                <tr>
                                                    <td><span style="color:#999;">仪器明细查询</span></td>
                                                    <td><span style="color:#999;">达标结果分析</span></td>
                                                </tr>
                                            }
                                            <tr>
                                                <td colspan="3" style="border-bottom: dashed 1px #a8bfde;"></td>
                                            </tr>
                                            @if (roleIds.Contains(",10,"))
                                            {
                                                <tr>
                                                    <td>
                                                        <p>实验（专用）室管理</p>
                                                    </td>
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/QueryStatisticsManage/FunRoomUse/CountyRecordList")', '使用记录统计')">使用记录统计</a></td>
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/EvaluateManage/FunRoomAttendStatic/CityFunRoomAttendStatic")', '达标结果查看')">达标结果查看</a></td>
                                                </tr>
                                            }
                                            else
                                            {
                                                <tr>
                                                    <td>
                                                        <p>实验（专用）室管理</p>
                                                    </td>
                                                    <td><span style="color:#999;">使用记录统计</span></td>
                                                    <td><span style="color:#999;">达标结果查看</span></td>
                                                </tr>
                                            }

                                            <tr>
                                                <td colspan="3" style="border-bottom: dashed 1px #a8bfde;"></td>
                                            </tr>
                                            @if (roleIds.Contains(",10,"))
                                            {
                                                <tr>
                                                    <td rowspan="2">
                                                        <p>实验教学管理</p>
                                                    </td>
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/QueryStatisticsManage/ExperimentTeach/CountyPlanInfoList")', '已编实验计划')">已编实验计划</a></td>
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/QueryStatisticsManage/ExperimentTeach/ExperimentRecordList")', '实验明细')">实验明细</a></td>
                                                </tr>
                                                <tr>
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/QueryStatisticsManage/ExperimentTeach/ExperimentRecordStatistics")', '实验开出记录')">实验开出记录</a></td>
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/EvaluateManage/ExperimentAttendStatic/ExperimentAttendStaticIndex")', '实验开出统计')">实验开出统计</a></td>
                                                </tr>
                                                
                                            }
                                            else
                                            {
                                                <tr>
                                                    <td rowspan="2">
                                                        <p>实验教学管理</p>
                                                    </td>
                                                    <td><span style="color:#999;">已编实验计划</span></td>
                                                    <td><span style="color:#999;">实验明细</span></td>
                                                </tr>
                                                <tr>
                                                    <td><span style="color:#999;">实验开出记录</span></td>
                                                    <td><span style="color:#999;">实验开出统计</span></td>
                                                </tr>
                                            }
                                            <tr>
                                                <td colspan="3" style="border-bottom: dashed 1px #a8bfde;"></td>
                                            </tr>
                                            <tr>
                                                <td><p>系统设置</p></td>
                                                @if (roleIds.Contains(",10,"))
                                                {
                                                    <td><a href="#" onclick="openMenu('@Url.Content("~/OrganizationManage/User/CountyListIndex")', '区县单位超管信息')">区县单位超管信息</a></td>
                                                }
                                                else
                                                {
                                                    <td><span style="color:#999;">区县单位超管信息</span></td>
                                                }
                                                <td><a href="#" onclick="openMenu('@Url.Content("~/OrganizationManage/SchoolTerm/Index")', '学年学期列表')">学年学期列表</a></td>
                                            </tr>

                                        </table>
                                    </div>
                                </div>
                            </div>

                            <div class="col-sm-12" style="margin-left: -15px;">
                                <div class="panel panel-default" style="height: 140px; margin-left: -15px; margin-right: -15px;">
                                    <div class="panel-heading">
                                        <b><i class="fa fa-user"></i><span style="padding-left:15px;">个人信息</span></b>
                                    </div>
                                    <div class="panel-body">
                                        <p>
                                            <span class="font3" id="pUserName" style="padding-left:50px;cursor:pointer;" onclick="showChangeUserForm()">@realName (@unitName)</span>
                                        </p>
                                        <p>
                                            <span style="padding-left:50px;">@roleNames</span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-sm-6" style="margin-left:-15px;margin-right:-15px;">
                            <div class="col-sm-12" style="margin-right: -15px;">
                                <div class="panel panel-default" style="height:407px;margin-left:-30px;margin-right:-30px;">
                                    <div class="panel-heading">
                                        <b><i class="fa fa-desktop"></i><span style="padding-left:15px;">产品简介</span></b>
                                    </div>
                                    <div class="panel-body">

                                        <div id="myCarousel" class="carousel slide" data-ride="carousel" data-interval="5000">
                                            <!-- 轮播（Carousel）指标 -->
                                            <ol class="carousel-indicators">
                                                <li data-target="#myCarousel" data-slide-to="0" class="active"></li>
                                                <li data-target="#myCarousel" data-slide-to="1"></li>
                                                <li data-target="#myCarousel" data-slide-to="2"></li>
                                                <li data-target="#myCarousel" data-slide-to="3"></li>
                                                <li data-target="#myCarousel" data-slide-to="4"></li>
                                                <li data-target="#myCarousel" data-slide-to="5"></li>
                                            </ol>
                                            <!-- 轮播（Carousel）项目 -->
                                            <div class="carousel-inner">
                                                <div class="item active">
                                                    <img src="~/image/1.jpg" style="height:340px;width:600px;" alt="First slide">
                                                    @*<div class="carousel-caption">产品简介1</div>*@
                                                </div>
                                                <div class="item">
                                                    <img src="~/image/2.jpg" style="height: 340px; width: 600px;" alt="Second slide">
                                                    @*<div class="carousel-caption">产品简介2</div>*@
                                                </div>
                                                <div class="item">
                                                    <img src="~/image/3.jpg" style="height: 340px; width: 600px;" alt="Third slide">
                                                    @*<div class="carousel-caption">产品简介3</div>*@
                                                </div>
                                                <div class="item">
                                                    <img src="~/image/4.jpg" style="height: 340px; width: 600px;" alt="Third slide">
                                                    @*<div class="carousel-caption">产品简介3</div>*@
                                                </div>
                                                <div class="item">
                                                    <img src="~/image/5.jpg" style="height: 340px; width: 600px;" alt="Third slide">
                                                    @*<div class="carousel-caption">产品简介3</div>*@
                                                </div>
                                                <div class="item">
                                                    <img src="~/image/6.jpg" style="height: 340px; width: 600px;" alt="Third slide">
                                                    @*<div class="carousel-caption">产品简介3</div>*@
                                                </div>
                                            </div>
                                            <!-- 轮播（Carousel）导航 -->
                                            <a class="left carousel-control aPicButton" href="#myCarousel" role="button" data-slide="prev">
                                                <span class="glyphicon glyphicon-chevron-left" aria-hidden="true"></span>
                                                <span class="sr-only">Previous</span>
                                            </a>
                                            <a class="right carousel-control aPicButton" href="#myCarousel" role="button" data-slide="next">
                                                <span class="glyphicon glyphicon-chevron-right" aria-hidden="true"></span>
                                                <span class="sr-only">Next</span>
                                            </a>
                                        </div>


                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-12" style="margin-right: -15px;">
                                <div class="panel panel-default" style="height: 208px; margin-left: -30px; margin-right: -30px; ">
                                    <div class="panel-heading">
                                        <b><i class="fa fa-pie-chart"></i><span style="padding-left:15px;">仪器展示</span></b>
                                    </div>
                                    <div class="panel-body">
                                        <div class="row" id="Marquee_x">
                                            <div class="col-sm-3">
                                                <a href="http://www.51jyyq.com/" target="_blank">
                                                    <img src="~/image/i1.jpg" />
                                                    <span style="text-align:center;">物理-化学-生物</span>
                                                </a>
                                            </div>
                                            <div class="col-sm-3">
                                                <a href="http://www.51jyyq.com/" target="_blank">
                                                    <img src="~/image/i4.jpg" />
                                                    <span style="text-align:center;">历史-地理-语言</span>
                                                </a>
                                            </div>
                                            <div class="col-sm-3">
                                                <a href="http://www.51jyyq.com/" target="_blank">
                                                    <img src="~/image/i2.jpg" />
                                                    <span style="text-align:center;">音乐-体育-美术</span>
                                                </a>
                                            </div>
                                            <div class="col-sm-3">
                                                <a href="http://www.51jyyq.com/" target="_blank">
                                                    <img src="~/image/i3.jpg" />
                                                    <span style="text-align:center;">科学-信息-实践</span>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-sm-3" style="margin-left:-15px;margin-right:-30px;">
                    <div class="col-sm-12" style="margin-left: -15px; margin-right: -30px;">
                        <div class="panel panel-default" style="height: 634px; margin-right: -45px;">
                            <div class="panel-heading">
                                <b><i class="fa fa-navicon"></i><span style="padding-left:15px;">资讯信息</span></b>
                            </div>
                            <div class="panel-body">

                                @if (list.Data != null && list.Data.Count > 0)
                                {
                                    int index = 0;
                                    foreach (ArticleCategoryEntity articleCate in list.Data)
                                    {
                                        if (index == 0)
                                        {
                                            <div class="news" style="height:180px;">
                                                <h3>@articleCate.Name</h3>
                                                <ul>
                                                    @foreach (ArticleEntity article in articleCate.listArticle)
                                                    {
                                                        <li><a href="@Url.Content("/ArticleManager/Article/ArticleDetail/"+article.Id)" title="@article.ShortTitle" target="_blank">@article.ShortTitle</a></li>
                                                    }
                                                </ul>
                                            </div>
                                        }
                                        else
                                        {
                                            <div class="news" style="margin-top: 15px; height: 180px;">
                                                <h3>@articleCate.Name</h3>
                                                <ul>
                                                    @foreach (ArticleEntity article in articleCate.listArticle)
                                                    {
                                                        <li><a href="@Url.Content("/ArticleManager/Article/ArticleDetail/"+article.Id)" title="@article.ShortTitle" target="_blank">@article.ShortTitle</a></li>
                                                    }
                                                </ul>
                                            </div>
                                        }
                                        index++;
                                    }
                                }

                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    }
    else
    {
        <div class="col-sm-12">
            <div class="row">
                <div class="col-sm-9">

                    <div class="row">
                        <div class="col-sm-6">
                            <div class="col-sm-12" style="margin-left:-15px;">

                                <div class="panel panel-default" style="margin-left: -15px; margin-right: -15px;height:475px;">
                                    <div class="panel-heading">
                                        <b><i class="fa fa-plane"></i><span style="padding-left:15px;">快捷入口</span></b>
                                    </div>
                                    <div class="panel-body">
                                        <table id="table-1" style="width: 100%; border-bottom: none !important; line-height:3;">
                                            <tr>
                                                <td><p>系统管理</p></td>
                                                <td><a href="#" onclick="openMenu('@Url.Content("~/SystemManage/Role/RoleIndex")', '角色管理')">角色管理</a></td>
                                                <td><a href="#" onclick="openMenu('@Url.Content("~/SystemManage/Menu/MenuIndex")', '菜单管理')">菜单管理</a></td>
                                            </tr>
                                            <tr>
                                                <td colspan="3" style="border-bottom: dashed 1px #a8bfde;"></td>
                                            </tr>
                                            <tr>
                                                <td><p>系统工具</p></td>
                                                <td colspan="2"><a href="#" onclick="openMenu('@Url.Content("~/ToolManage/Server/ServerIndex")', '服务器信息')">服务器信息</a></td>
                                            </tr>
                                            <tr>
                                                <td colspan="3" style="border-bottom: dashed 1px #a8bfde;"></td>
                                            </tr>
                                           
                                            <tr>
                                                <td rowspan="2">
                                                    <p>资讯管理</p>
                                                </td>
                                                <td><a href="#" onclick="openMenu('@Url.Content("~/ArticleManager/ArticleCategory/ArticleCategoryIndex")', '资讯分类')">资讯分类</a></td>
                                                <td><a href="#" onclick="openMenu('@Url.Content("~/ArticleManager/Article/ArticleForm")', '发布资讯')">发布资讯</a></td>
                                            </tr>
                                            <tr>
                                                <td><a href="#" onclick="openMenu('@Url.Content("~/ArticleManager/Article/ArticleIndex")', '资讯列表')">资讯列表</a></td>
                                                <td></td>
                                            </tr>
                                            <tr>
                                                <td colspan="3" style="border-bottom: dashed 1px #a8bfde;"></td>
                                            </tr>
                                            <tr>
                                                <td><p>系统设置</p></td>
                                                <td><a href="#" onclick="openMenu('@Url.Content("~/OrganizationManage/Unit/UnitSysIndex")', '单位管理')">单位管理</a></td>
                                                <td><a href="#" onclick="openMenu('@Url.Content("~/OrganizationManage/User/UserIndex")', '所有单位用户管理')">所有单位用户管理</a></td>
                                            </tr>

                                        </table>
                                    </div>
                                </div>
                            </div>

                            <div class="col-sm-12" style="margin-left: -15px;">
                                <div class="panel panel-default" style="height: 140px; margin-left: -15px; margin-right: -15px;">
                                    <div class="panel-heading">
                                        <b><i class="fa fa-user"></i><span style="padding-left:15px;">个人信息</span></b>
                                    </div>
                                    <div class="panel-body">
                                        <p>
                                            <span class="font3" id="pUserName" style="padding-left:50px;cursor:pointer;" onclick="showChangeUserForm()">@realName (@unitName)</span>
                                        </p>
                                        <p>
                                            <span style="padding-left:50px;">@roleNames</span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-sm-6" style="margin-left:-15px;margin-right:-15px;">
                            <div class="col-sm-12" style="margin-right: -15px;">
                                <div class="panel panel-default" style="height:407px;margin-left:-30px;margin-right:-30px;">
                                    <div class="panel-heading">
                                        <b><i class="fa fa-desktop"></i><span style="padding-left:15px;">产品简介</span></b>
                                    </div>
                                    <div class="panel-body">

                                        <div id="myCarousel" class="carousel slide" data-ride="carousel" data-interval="5000">
                                            <!-- 轮播（Carousel）指标 -->
                                            <ol class="carousel-indicators">
                                                <li data-target="#myCarousel" data-slide-to="0" class="active"></li>
                                                <li data-target="#myCarousel" data-slide-to="1"></li>
                                                <li data-target="#myCarousel" data-slide-to="2"></li>
                                                <li data-target="#myCarousel" data-slide-to="3"></li>
                                                <li data-target="#myCarousel" data-slide-to="4"></li>
                                                <li data-target="#myCarousel" data-slide-to="5"></li>
                                            </ol>
                                            <!-- 轮播（Carousel）项目 -->
                                            <div class="carousel-inner">
                                                <div class="item active">
                                                    <img src="~/image/1.jpg" style="height:340px;width:600px;" alt="First slide">
                                                    @*<div class="carousel-caption">产品简介1</div>*@
                                                </div>
                                                <div class="item">
                                                    <img src="~/image/2.jpg" style="height: 340px; width: 600px;" alt="Second slide">
                                                    @*<div class="carousel-caption">产品简介2</div>*@
                                                </div>
                                                <div class="item">
                                                    <img src="~/image/3.jpg" style="height: 340px; width: 600px;" alt="Third slide">
                                                    @*<div class="carousel-caption">产品简介3</div>*@
                                                </div>
                                                <div class="item">
                                                    <img src="~/image/4.jpg" style="height: 340px; width: 600px;" alt="Third slide">
                                                    @*<div class="carousel-caption">产品简介3</div>*@
                                                </div>
                                                <div class="item">
                                                    <img src="~/image/5.jpg" style="height: 340px; width: 600px;" alt="Third slide">
                                                    @*<div class="carousel-caption">产品简介3</div>*@
                                                </div>
                                                <div class="item">
                                                    <img src="~/image/6.jpg" style="height: 340px; width: 600px;" alt="Third slide">
                                                    @*<div class="carousel-caption">产品简介3</div>*@
                                                </div>
                                            </div>
                                            <!-- 轮播（Carousel）导航 -->
                                            <a class="left carousel-control aPicButton" href="#myCarousel" role="button" data-slide="prev">
                                                <span class="glyphicon glyphicon-chevron-left" aria-hidden="true"></span>
                                                <span class="sr-only">Previous</span>
                                            </a>
                                            <a class="right carousel-control aPicButton" href="#myCarousel" role="button" data-slide="next">
                                                <span class="glyphicon glyphicon-chevron-right" aria-hidden="true"></span>
                                                <span class="sr-only">Next</span>
                                            </a>
                                        </div>


                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-12" style="margin-right: -15px;">
                                <div class="panel panel-default" style="height: 208px; margin-left: -30px; margin-right: -30px; ">
                                    <div class="panel-heading">
                                        <b><i class="fa fa-pie-chart"></i><span style="padding-left:15px;">仪器展示</span></b>
                                    </div>
                                    <div class="panel-body">
                                        <div class="row" id="Marquee_x">
                                            <div class="col-sm-3">
                                                <a href="http://www.51jyyq.com/" target="_blank">
                                                    <img src="~/image/i1.jpg" />
                                                    <span style="text-align:center;">物理-化学-生物</span>
                                                </a>
                                            </div>
                                            <div class="col-sm-3">
                                                <a href="http://www.51jyyq.com/" target="_blank">
                                                    <img src="~/image/i4.jpg" />
                                                    <span style="text-align:center;">历史-地理-语言</span>
                                                </a>
                                            </div>
                                            <div class="col-sm-3">
                                                <a href="http://www.51jyyq.com/" target="_blank">
                                                    <img src="~/image/i2.jpg" />
                                                    <span style="text-align:center;">音乐-体育-美术</span>
                                                </a>
                                            </div>
                                            <div class="col-sm-3">
                                                <a href="http://www.51jyyq.com/" target="_blank">
                                                    <img src="~/image/i3.jpg" />
                                                    <span style="text-align:center;">科学-信息-实践</span>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-sm-3" style="margin-left:-15px;margin-right:-30px;">
                    <div class="col-sm-12" style="margin-left: -15px; margin-right: -30px;">
                        <div class="panel panel-default" style="height: 634px; margin-right: -45px;">
                            <div class="panel-heading">
                                <b><i class="fa fa-navicon"></i><span style="padding-left:15px;">资讯信息</span></b>
                            </div>
                            <div class="panel-body">

                                @if (list.Data != null && list.Data.Count > 0)
                                {
                                    int index = 0;
                                    foreach (ArticleCategoryEntity articleCate in list.Data)
                                    {
                                        if (index == 0)
                                        {
                                            <div class="news" style="height:180px;">
                                                <h3>@articleCate.Name</h3>
                                                <ul>
                                                    @foreach (ArticleEntity article in articleCate.listArticle)
                                                    {
                                                        <li><a href="@Url.Content("/ArticleManager/Article/ArticleDetail/"+article.Id)" title="@article.ShortTitle" target="_blank">@article.ShortTitle</a></li>
                                                    }
                                                </ul>
                                            </div>
                                        }
                                        else
                                        {
                                            <div class="news" style="margin-top: 15px; height: 180px;">
                                                <h3>@articleCate.Name</h3>
                                                <ul>
                                                    @foreach (ArticleEntity article in articleCate.listArticle)
                                                    {
                                                        <li><a href="@Url.Content("/ArticleManager/Article/ArticleDetail/"+article.Id)" title="@article.ShortTitle" target="_blank">@article.ShortTitle</a></li>
                                                    }
                                                </ul>
                                            </div>
                                        }
                                        index++;
                                    }
                                }

                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    }
</div>
<script>
    $(function () {

        //$('#Marquee_x').jcMarquee({ 'marquee': 'x', 'margin_right': '10px', 'speed': 20 });

        //$(".modal").appendTo("body"), $("[data-toggle=popover]").popover(), $(".collapse-link").click(function () {
        //    var div_ibox = $(this).closest("div.ibox"),
        //        e = $(this).find("i"),
        //        i = div_ibox.find("div.ibox-content");
        //    i.slideToggle(200),
        //        e.toggleClass("fa-chevron-up").toggleClass("fa-chevron-down"),
        //        div_ibox.toggleClass("").toggleClass("border-bottom"),
        //        setTimeout(function () {
        //            div_ibox.resize();
        //        }, 50);
        //}), $(".close-link").click(function () {
        //    var div_ibox = $(this).closest("div.ibox");
        //    div_ibox.remove()
        //});

        //$('#pay-qrcode').click(function () {
        //    var html = $(this).html();
        //    ys.openDialogContent({
        //        content: html,
        //        width: '600px',
        //        height: '321px'
        //    });
        //});

    });

    function openMenu(url, content) {
        createMenuItem(url, content);
        
    }

    function showChangeUserForm() {

        ys.openDialog({
            title: "修改信息",
            height: "450px",
            content: '@Url.Content("~/OrganizationManage/User/ChangeUser")' + '?id=@userId',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function getForm() {
        ys.ajax({
            url: '@Url.Content("~/OrganizationManage/User/GetFormJson")' + '?id=@userId',
            type: "get",
            success: function (obj) {
                if (obj.Tag == 1) {
                    var result = obj.Data;
                    $("#pUserName").html(result.RealName + "(@unitName)");
                }
            }
        });
    }

</script>
