﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.CameraManage;
using Dqy.Syjx.Model.Param.CameraManage;

namespace Dqy.Syjx.Service.CameraManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2023-08-01 10:03
    /// 描 述：服务类
    /// </summary>
    public class DahuaAlarmDataService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<DahuaAlarmDataEntity>> GetList(DahuaAlarmDataListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<DahuaAlarmDataEntity>> GetPageList(DahuaAlarmDataListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<DahuaAlarmDataEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<DahuaAlarmDataEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(DahuaAlarmDataEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {

                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(DahuaAlarmDataEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                entity.Create();
                await db.Insert(entity);
            }
            else
            {

                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
               strSql = $" update DahuaAlarmData set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
               strSql = $"update DahuaAlarmData set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<DahuaAlarmDataEntity, bool>> ListFilter(DahuaAlarmDataListParam param)
        {
            var expression = LinqExtensions.True<DahuaAlarmDataEntity>();
            //expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
            }
            return expression;
        }
        #endregion
    }
}
