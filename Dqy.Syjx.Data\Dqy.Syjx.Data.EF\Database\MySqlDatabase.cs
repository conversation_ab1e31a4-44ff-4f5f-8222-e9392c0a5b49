using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Data;
using System.Data.Common;
using Microsoft.EntityFrameworkCore.Storage;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;
using Dqy.Syjx.Util.Extension;
using Newtonsoft.Json;
using Dqy.Syjx.Util;

namespace Dqy.Syjx.Data.EF
{
    public class MySqlDatabase : IDatabase
    {
        #region 构造函数
        /// <summary>
        /// 构造方法
        /// </summary>
        public MySqlDatabase(string connString)
        {
            dbContext = new MySqlDbContext(connString);
        }
        #endregion

        #region 属性
        /// <summary>
        /// 获取 当前使用的数据访问上下文对象
        /// </summary>
        public DbContext dbContext { get; set; }
        /// <summary>
        /// 事务对象
        /// </summary>
        public IDbContextTransaction dbContextTransaction { get; set; }
        #endregion

        #region 事务提交
        /// <summary>
        /// 事务开始
        /// </summary>
        /// <returns></returns>
        public async Task<IDatabase> BeginTrans()
        {
            DbConnection dbConnection = dbContext.Database.GetDbConnection();
            if (dbConnection.State == ConnectionState.Closed)
            {
                await dbConnection.OpenAsync();
            }
            dbContextTransaction = await dbContext.Database.BeginTransactionAsync();
            return this;
        }
        /// <summary>
        /// 提交当前操作的结果
        /// </summary>
        public async Task<int> CommitTrans()
        {
            try
            {
                DbContextExtension.SetEntityDefaultValue(dbContext);

                int returnValue = await dbContext.SaveChangesAsync();
                if (dbContextTransaction != null)
                {
                    await dbContextTransaction.CommitAsync();
                    await this.Close();
                }
                else
                {
                    await this.Close();
                }
                return returnValue;
            }
            catch
            {
                throw;
            }
            finally
            {
                if (dbContextTransaction == null)
                {
                    await this.Close();
                }
            }
        }
        /// <summary>
        /// 把当前操作回滚成未提交状态
        /// </summary>
        public async Task RollbackTrans()
        {
            await this.dbContextTransaction.RollbackAsync();
            await this.dbContextTransaction.DisposeAsync();
            await this.Close();
        }
        /// <summary>
        /// 关闭连接 内存回收
        /// </summary>
        public async Task Close()
        {
            await dbContext.DisposeAsync();
        }
        #endregion

        #region 执行 SQL 语句
        public async Task<int> ExecuteBySql(string strSql)
        {
            strSql = SplitToMySql(strSql);
            try
            {
                if (dbContextTransaction == null)
                {
                    return await dbContext.Database.ExecuteSqlRawAsync(strSql);
                }
                else
                {
                    await dbContext.Database.ExecuteSqlRawAsync(strSql);
                    return dbContextTransaction == null ? await this.CommitTrans() : 0;
                }
            }
            catch (Exception exp)
            {
                FileLog.LogErrorSql(exp.Message);
                FileLog.LogErrorSql(strSql);
                return 0;
            }
        }
        public async Task<int> ExecuteBySql(string strSql, params DbParameter[] dbParameter)
        {
            strSql = SplitToMySql(strSql);
            try
            {
                if (dbContextTransaction == null)
                {
                    return await dbContext.Database.ExecuteSqlRawAsync(strSql, dbParameter);
                }
                else
                {
                    await dbContext.Database.ExecuteSqlRawAsync(strSql, dbParameter);
                    return dbContextTransaction == null ? await this.CommitTrans() : 0;
                }
            }
            catch (Exception exp)
            {
                FileLog.LogErrorSql(exp.Message);
                FileLog.LogErrorSql(strSql);
                return 0;
            }
        }
        public async Task<int> ExecuteByProc(string procName)
        {
            try
            {
                if (dbContextTransaction == null)
                {
                    return await dbContext.Database.ExecuteSqlRawAsync(DbContextExtension.BuilderProc(procName));
                }
                else
                {
                    await dbContext.Database.ExecuteSqlRawAsync(DbContextExtension.BuilderProc(procName));
                    return dbContextTransaction == null ? await this.CommitTrans() : 0;
                }
            }
            catch (Exception exp)
            {
                FileLog.LogErrorSql(exp.Message);
                FileLog.LogErrorSql(procName);
                return 0;
            }
        }
        public async Task<int> ExecuteByProc(string procName, params DbParameter[] dbParameter)
        {
            try
            {
                if (dbContextTransaction == null)
                {
                    return await dbContext.Database.ExecuteSqlRawAsync(DbContextExtension.BuilderProc(procName, dbParameter), dbParameter);
                }
                else
                {
                    await dbContext.Database.ExecuteSqlRawAsync(DbContextExtension.BuilderProc(procName, dbParameter), dbParameter);
                    return dbContextTransaction == null ? await this.CommitTrans() : 0;
                }
            }
            catch (Exception exp)
            {
                FileLog.LogErrorSql(exp.Message);
                FileLog.LogErrorSql(procName);
                return 0;
            }
        }
        #endregion

        #region 对象实体 添加、修改、删除
        public async Task<int> Insert<T>(T entity) where T : class
        {
            try
            {
                dbContext.Entry<T>(entity).State = EntityState.Added;
                return dbContextTransaction == null ? await this.CommitTrans() : 0;
            }
            catch (Exception exp)
            {
                FileLog.LogErrorSql(exp.Message);
                FileLog.LogErrorSql(Object2JSON(entity));
                return 0;
            }
        }
        public async Task<int> Insert<T>(IEnumerable<T> entities) where T : class
        {
            try
            {
                foreach (var entity in entities)
                {
                    dbContext.Entry<T>(entity).State = EntityState.Added;
                }
                return dbContextTransaction == null ? await this.CommitTrans() : 0;
            }
            catch (Exception exp)
            {
                FileLog.LogErrorSql(exp.Message);
                FileLog.LogErrorSql(Object2JSON(entities));
                return 0;
            }
        }

        public async Task<int> Delete<T>() where T : class
        {
            try
            {
                IEntityType entityType = DbContextExtension.GetEntityType<T>(dbContext);
                if (entityType != null)
                {
                    string tableName = entityType.GetTableName();
                    return await this.ExecuteBySql(DbContextExtension.DeleteSql(tableName));
                }
                return -1;
            }
            catch (Exception exp)
            {
                FileLog.LogErrorSql(exp.Message);
                FileLog.LogErrorSql(Object2JSON(dbContext));
                return 0;
            }
        }
        public async Task<int> Delete<T>(T entity) where T : class
        {
            try
            {
                dbContext.Set<T>().Attach(entity);
                dbContext.Set<T>().Remove(entity);
                return dbContextTransaction == null ? await this.CommitTrans() : 0;
            }
            catch (Exception exp)
            {
                FileLog.LogErrorSql(exp.Message);
                FileLog.LogErrorSql(Object2JSON(entity));
                return 0;
            }
        }
        public async Task<int> Delete<T>(IEnumerable<T> entities) where T : class
        {
            try
            {
                foreach (var entity in entities)
                {
                    dbContext.Set<T>().Attach(entity);
                    dbContext.Set<T>().Remove(entity);
                }
                return dbContextTransaction == null ? await this.CommitTrans() : 0;
            }
            catch (Exception exp)
            {
                FileLog.LogErrorSql(exp.Message);
                FileLog.LogErrorSql(Object2JSON(entities));
                return 0;
            }
        }
        public async Task<int> Delete<T>(Expression<Func<T, bool>> condition) where T : class, new()
        {
            try
            {
                IEnumerable<T> entities = await dbContext.Set<T>().Where(condition).ToListAsync();
                return entities.Count() > 0 ? await Delete(entities) : 0;
            }
            catch (Exception exp)
            {
                FileLog.LogErrorSql(exp.Message);
                FileLog.LogErrorSql(Object2JSON(condition));
                return 0;
            }
        }
        public async Task<int> Delete<T>(long keyValue) where T : class
        {
            try
            {
                IEntityType entityType = DbContextExtension.GetEntityType<T>(dbContext);
                if (entityType != null)
                {
                    string tableName = entityType.GetTableName();
                    string keyField = "Id";
                    return await this.ExecuteBySql(DbContextExtension.DeleteSql(tableName, keyField, keyValue));
                }
                return -1;
            }
            catch (Exception exp)
            {
                FileLog.LogErrorSql(exp.Message);
                FileLog.LogErrorSql(keyValue.ToString());
                return 0;
            }
        }
        public async Task<int> Delete<T>(long[] keyValue) where T : class
        {
            try
            {
                IEntityType entityType = DbContextExtension.GetEntityType<T>(dbContext);
                if (entityType != null)
                {
                    string tableName = entityType.GetTableName();
                    string keyField = "Id";
                    return await this.ExecuteBySql(DbContextExtension.DeleteSql(tableName, keyField, keyValue));
                }
                return -1;
            }
            catch (Exception exp)
            {
                FileLog.LogErrorSql(exp.Message);
                FileLog.LogErrorSql(keyValue.ToString());
                return 0;
            }
        }
        public async Task<int> Delete<T>(string propertyName, long propertyValue) where T : class
        {
            try
            {
                IEntityType entityType = DbContextExtension.GetEntityType<T>(dbContext);
                if (entityType != null)
                {
                    string tableName = entityType.GetTableName();
                    return await this.ExecuteBySql(DbContextExtension.DeleteSql(tableName, propertyName, propertyValue));
                }
                return -1;
            }
            catch (Exception exp)
            {
                FileLog.LogErrorSql(exp.Message);
                FileLog.LogErrorSql($"propertyName:{propertyName} , propertyValue:{propertyValue}");
                return 0;
            }
        }

        public async Task<int> Update<T>(T entity) where T : class
        {
            try
            {
                dbContext.Set<T>().Attach(entity);
                Hashtable props = DatabasesExtension.GetPropertyInfo<T>(entity);
                foreach (string item in props.Keys)
                {
                    if (item == "Id")
                    {
                        continue;
                    }
                    object value = dbContext.Entry(entity).Property(item).CurrentValue;

                    if (value != null)
                    {
                        dbContext.Entry(entity).Property(item).IsModified = true;
                    }
                }
                return dbContextTransaction == null ? await this.CommitTrans() : 0;
            }
            catch (Exception exp)
            {
                FileLog.LogErrorSql(exp.Message);
                FileLog.LogErrorSql(Object2JSON(entity));
                return 0;
            }
        }
        public async Task<int> Update<T>(IEnumerable<T> entities) where T : class
        {
            try
            {
                foreach (var entity in entities)
                {
                    dbContext.Entry<T>(entity).State = EntityState.Modified;
                }
                return dbContextTransaction == null ? await this.CommitTrans() : 0;
            }
            catch (Exception exp)
            {
                FileLog.LogErrorSql(exp.Message);
                FileLog.LogErrorSql(Object2JSON(entities));
                return 0;
            }
        }
        public async Task<int> UpdateAllField<T>(T entity) where T : class
        {
            try
            {
                dbContext.Set<T>().Attach(entity);
                dbContext.Entry(entity).State = EntityState.Modified;
                return dbContextTransaction == null ? await this.CommitTrans() : 0;
            }
            catch (Exception exp)
            {
                FileLog.LogErrorSql(exp.Message);
                FileLog.LogErrorSql(Object2JSON(entity));
                return 0;
            }
        }
        public async Task<int> Update<T>(Expression<Func<T, bool>> condition) where T : class, new()
        {
            try
            {
                IEnumerable<T> entities = await dbContext.Set<T>().Where(condition).ToListAsync();
                return entities.Count() > 0 ? await Update(entities) : 0;
            }
            catch (Exception exp)
            {
                FileLog.LogErrorSql(exp.Message);
                FileLog.LogErrorSql(Object2JSON(condition));
                return 0;
            }
        }
        public async Task<int> Update<T>(T entity, List<string> fields) where T : class, new()
        {
            try
            {
                dbContext.Set<T>().Attach(entity);
                Hashtable props = DatabasesExtension.GetPropertyInfo<T>(entity);
                foreach (string item in props.Keys)
                {
                    if (fields != null && fields.Contains(item))
                    {
                        if (item == "Id")
                        {
                            continue;
                        }
                        object value = dbContext.Entry(entity).Property(item).CurrentValue;

                        if (value != null)
                        {
                            dbContext.Entry(entity).Property(item).IsModified = true;
                        }
                    }
                }
                return dbContextTransaction == null ? await this.CommitTrans() : 0;
            }
            catch (Exception exp)
            {
                FileLog.LogErrorSql(exp.Message);
                FileLog.LogErrorSql(Object2JSON(entity));
                return 0;
            }
        }
        public IQueryable<T> IQueryable<T>(Expression<Func<T, bool>> condition) where T : class, new()
        {
            try
            {
                return dbContext.Set<T>().Where(condition);
            }
            catch (Exception exp)
            {
                FileLog.LogErrorSql(exp.Message);
                FileLog.LogErrorSql(Object2JSON(condition));
                return null;
            }
        }
        #endregion

        #region 对象实体 查询
        public async Task<T> FindEntity<T>(object keyValue) where T : class
        {
            try
            {
                return await dbContext.Set<T>().FindAsync(keyValue);
            }
            catch (Exception exp)
            {
                FileLog.LogErrorSql(exp.Message);
                FileLog.LogErrorSql(keyValue?.ToString() ?? "null");
                return null;
            }
        }
        public async Task<T> FindEntity<T>(Expression<Func<T, bool>> condition) where T : class, new()
        {
            try
            {
                return await dbContext.Set<T>().Where(condition).FirstOrDefaultAsync();
            }
            catch (Exception exp)
            {
                FileLog.LogErrorSql(exp.Message);
                FileLog.LogErrorSql(Object2JSON(condition));
                return null;
            }
        }

        public async Task<IEnumerable<T>> FindList<T>() where T : class, new()
        {
            try
            {
                return await dbContext.Set<T>().ToListAsync();
            }
            catch (Exception exp)
            {
                FileLog.LogErrorSql(exp.Message);
                return null;
            }
        }
        public async Task<IEnumerable<T>> FindList<T>(Func<T, object> orderby) where T : class, new()
        {
            try
            {
                var list = await dbContext.Set<T>().ToListAsync();
                list = list.OrderBy(orderby).ToList();
                return list;
            }
            catch (Exception exp)
            {
                FileLog.LogErrorSql(exp.Message);
                FileLog.LogErrorSql(Object2JSON(orderby));
                return null;
            }
        }
        public async Task<IEnumerable<T>> FindList<T>(Expression<Func<T, bool>> condition) where T : class, new()
        {
            try
            {
                return await dbContext.Set<T>().Where(condition).ToListAsync();
            }
            catch (Exception exp)
            {
                FileLog.LogErrorSql(exp.Message);
                FileLog.LogErrorSql(Object2JSON(condition));
                return null;
            }
        }
        public async Task<IEnumerable<T>> FindList<T>(string strSql) where T : class
        {
            try
            {
                return await FindList<T>(strSql, null);
            }
            catch (Exception exp)
            {
                FileLog.LogErrorSql(exp.Message);
                FileLog.LogErrorSql(strSql);
                return null;
            }
        }
        public async Task<IEnumerable<T>> FindList<T>(string strSql, DbParameter[] dbParameter) where T : class
        {
            strSql = SplitToMySql(strSql);
            try
            {
                using (var dbConnection = dbContext.Database.GetDbConnection())
                {
                    var reader = await new DbHelper(dbContext, dbConnection).ExecuteReadeAsync(CommandType.Text, strSql, dbParameter);
                    return DatabasesExtension.IDataReaderToList<T>(reader);
                }
            }
            catch (Exception exp)
            {
                FileLog.LogErrorSql(exp.Message);
                FileLog.LogErrorSql(strSql);
                return null;
            }
        }
        public async Task<(int total, IEnumerable<T> list)> FindList<T>(string sort, bool isAsc, int pageSize, int pageIndex) where T : class, new()
        {
            try
            {
                var tempData = dbContext.Set<T>().AsQueryable();
                return await FindList<T>(tempData, sort, isAsc, pageSize, pageIndex);
            }
            catch (Exception exp)
            {
                FileLog.LogErrorSql(exp.Message);
                FileLog.LogErrorSql($"sort:{sort}, isAsc:{isAsc}");
                return (0, null);
            }
        }
        public async Task<(int total, IEnumerable<T> list)> FindList<T>(Expression<Func<T, bool>> condition, string sort, bool isAsc, int pageSize, int pageIndex) where T : class, new()
        {
            try
            {
                var tempData = dbContext.Set<T>().Where(condition);
                return await FindList<T>(tempData, sort, isAsc, pageSize, pageIndex);
            }
            catch (Exception exp)
            {
                FileLog.LogErrorSql(exp.Message);
                FileLog.LogErrorSql($"condition:{Object2JSON(condition)},sort:{sort}, isAsc:{isAsc}");
                return (0, null);
            }
        }
        public async Task<(int total, IEnumerable<T>)> FindList<T>(string strSql, string sort, bool isAsc, int pageSize, int pageIndex) where T : class
        {
            try
            {
                return await FindList<T>(strSql, null, sort, isAsc, pageSize, pageIndex);
            }
            catch (Exception exp)
            {
                FileLog.LogErrorSql(exp.Message);
                FileLog.LogErrorSql(strSql);
                return (0, null);
            }
        }
        public async Task<(int total, IEnumerable<T>)> FindList<T>(string strSql, DbParameter[] dbParameter, string sort, bool isAsc, int pageSize, int pageIndex) where T : class
        {
            strSql = SplitToMySql(strSql);
            StringBuilder sb = new StringBuilder();
            try
            {
                using (var dbConnection = dbContext.Database.GetDbConnection())
                {
                    DbHelper dbHelper = new DbHelper(dbContext, dbConnection);
                    sb.Append(DatabasePageExtension.MySqlPageSql(strSql, dbParameter, sort, isAsc, pageSize, pageIndex));
                    object tempTotal = await dbHelper.ExecuteScalarAsync(CommandType.Text, DatabasePageExtension.GetCountSql(strSql), dbParameter);
                    int total = tempTotal.ParseToInt();
                    if (total > 0)
                    {
                        var reader = await dbHelper.ExecuteReadeAsync(CommandType.Text, sb.ToString(), dbParameter);
                        return (total, DatabasesExtension.IDataReaderToList<T>(reader));
                    }
                    else
                    {
                        return (total, new List<T>());
                    }
                }
            }
            catch (Exception exp)
            {
                FileLog.LogErrorSql(exp.Message);
                FileLog.LogErrorSql(sb.ToString());
                return (0, null);
            }
        }
        private async Task<(int total, IEnumerable<T> list)> FindList<T>(IQueryable<T> tempData, string sort, bool isAsc, int pageSize, int pageIndex)
        {
            try
            {
                tempData = DatabasesExtension.AppendSort<T>(tempData, sort, isAsc);
                var total = tempData.Count();
                if (total > 0)
                {
                    tempData = tempData.Skip<T>(pageSize * (pageIndex - 1)).Take<T>(pageSize).AsQueryable();
                    var list = await tempData.ToListAsync();
                    return (total, list);
                }
                else
                {
                    return (total, new List<T>());
                }
            }
            catch (Exception exp)
            {
                FileLog.LogErrorSql(exp.Message);
                FileLog.LogErrorSql($"condition:{Object2JSON(tempData)},sort:{sort}, isAsc:{isAsc}");
                return (0, new List<T>());
            }
        }
        #endregion

        #region 数据源查询
        public async Task<DataTable> FindTable(string strSql)
        {
            try
            {
                return await FindTable(strSql, null);
            }
            catch (Exception exp)
            {
                FileLog.LogErrorSql(exp.Message);
                FileLog.LogErrorSql(strSql);
                return null;
            }
        }
        public async Task<DataTable> FindTable(string strSql, DbParameter[] dbParameter)
        {
            strSql = SplitToMySql(strSql);
            try
            {
                using (var dbConnection = dbContext.Database.GetDbConnection())
                {
                    var reader = await new DbHelper(dbContext, dbConnection).ExecuteReadeAsync(CommandType.Text, strSql, dbParameter);
                    return DatabasesExtension.IDataReaderToDataTable(reader);
                }
            }
            catch (Exception exp)
            {
                FileLog.LogErrorSql(exp.Message);
                FileLog.LogErrorSql(strSql);
                return null;
            }
        }
        public async Task<(int total, DataTable)> FindTable(string strSql, string sort, bool isAsc, int pageSize, int pageIndex)
        {
            try
            {
                return await FindTable(strSql, null, sort, isAsc, pageSize, pageIndex);
            }
            catch (Exception exp)
            {
                FileLog.LogErrorSql(exp.Message);
                FileLog.LogErrorSql(strSql);
                return (0, null);
            }
        }
        public async Task<(int total, DataTable)> FindTable(string strSql, DbParameter[] dbParameter, string sort, bool isAsc, int pageSize, int pageIndex)
        {
            strSql = SplitToMySql(strSql);
            try
            {
                using (var dbConnection = dbContext.Database.GetDbConnection())
                {
                    DbHelper dbHelper = new DbHelper(dbContext, dbConnection);
                    StringBuilder sb = new StringBuilder();
                    sb.Append(DatabasePageExtension.MySqlPageSql(strSql, dbParameter, sort, isAsc, pageSize, pageIndex));
                    object tempTotal = await dbHelper.ExecuteScalarAsync(CommandType.Text, "SELECT COUNT(1) FROM (" + strSql + ") T", dbParameter);
                    int total = tempTotal.ParseToInt();
                    if (total > 0)
                    {
                        var reader = await dbHelper.ExecuteReadeAsync(CommandType.Text, sb.ToString(), dbParameter);
                        DataTable resultTable = DatabasesExtension.IDataReaderToDataTable(reader);
                        return (total, resultTable);
                    }
                    else
                    {
                        return (total, new DataTable());
                    }
                }
            }
            catch (Exception exp)
            {
                FileLog.LogErrorSql(exp.Message);
                FileLog.LogErrorSql($"strSql:{strSql}, sort:{sort}");
                return (0, null);
            }
        }

        public async Task<object> FindObject(string strSql)
        {
            try
            {
                return await FindObject(strSql, null);
            }
            catch (Exception exp)
            {
                FileLog.LogErrorSql(exp.Message);
                FileLog.LogErrorSql(strSql);
                return null;
            }
        }
        public async Task<object> FindObject(string strSql, DbParameter[] dbParameter)
        {
            strSql = SplitToMySql(strSql);
            try
            {
                using (var dbConnection = dbContext.Database.GetDbConnection())
                {
                    return await new DbHelper(dbContext, dbConnection).ExecuteScalarAsync(CommandType.Text, strSql, dbParameter);
                }
            }
            catch (Exception exp)
            {
                FileLog.LogErrorSql(exp.Message);
                FileLog.LogErrorSql(strSql);
                return null;
            }
        }
        public async Task<T> FindObject<T>(string strSql) where T : class
        {
            strSql = SplitToMySql(strSql);
            try
            {
                var list = await dbContext.SqlQuery<T>(strSql);
                return list.FirstOrDefault();
            }
            catch (Exception exp)
            {
                FileLog.LogErrorSql(exp.Message);
                FileLog.LogErrorSql(strSql);
                return null;
            }
        }


        #endregion
        /// <summary>
        /// Sql to MySql
        /// </summary>
        /// <param name="strSql"></param>
        /// <returns></returns>
        private string SplitToMySql(string strSql)
        {
            if (string.IsNullOrWhiteSpace(strSql))
                return strSql;

            // 1. 处理TOP语法（转换为LIMIT）
            var limitValue = "";
            strSql = Regex.Replace(strSql,
                @"\bTOP\s*\(\s*(\d+)\s*\)|\bTOP\s+(\d+)",
                match =>
                {
                    limitValue = !string.IsNullOrEmpty(match.Groups[1].Value)
                        ? match.Groups[1].Value
                        : match.Groups[2].Value;
                    return "";
                },
                RegexOptions.IgnoreCase);

            // 2. 数据类型转换函数
            strSql = Regex.Replace(strSql, @"\bCONVERT\s*\(\s*MONEY\s*,\s*([^)]+)\)", "CAST($1 AS DECIMAL(18,4))", RegexOptions.IgnoreCase);

            strSql = Regex.Replace(strSql,
    @"CONVERT\s*\(\s*DECIMAL\s*\(\s*(\d+)\s*,\s*(\d+)\s*\)\s*,\s*(\([^)]+\)|[^)]+)\s*\)",
    match => {
        var inner = match.Groups[3].Value.Trim();

        // 移除外部可能存在的括号
        if (inner.StartsWith("(") && inner.EndsWith(")"))
        {
            inner = inner.Substring(1, inner.Length - 2).Trim();
        }

        // 确保表达式有必要的括号（如果包含复杂运算）
        bool needsParens = inner.Contains(" ") ||
                          inner.Contains("+") ||
                          inner.Contains("-") ||
                          inner.Contains("*") ||
                          inner.Contains("/") ||
                          inner.Contains("CASE");

        if (needsParens)
        {
            return $"CAST(({inner}) AS DECIMAL({match.Groups[1].Value},{match.Groups[2].Value}))";
        }
        return $"CAST({inner} AS DECIMAL({match.Groups[1].Value},{match.Groups[2].Value}))";
    },
    RegexOptions.IgnoreCase);

            strSql = Regex.Replace(strSql, @"\bCONVERT\s*\(\s*NVARCHAR\s*\(\s*(\d+)\s*\)\s*,\s*([^)]+)\s*,\s*\d+\s*\)", "DATE_FORMAT($2, '%Y-%m-%d')", RegexOptions.IgnoreCase);
            strSql = Regex.Replace(strSql, @"\bCONVERT\s*\(\s*VARCHAR\s*\(\s*(\d+)\s*\)\s*,\s*([^)]+)\)", "CAST($2 AS CHAR($1))", RegexOptions.IgnoreCase);
            strSql = Regex.Replace(strSql, @"\bCONVERT\s*\(\s*NVARCHAR\s*\(\s*MAX\s*\)\s*,\s*([^)]+)\)", "CAST($1 AS CHAR)", RegexOptions.IgnoreCase);
            strSql = Regex.Replace(strSql, @"\bCAST\s*\(\s*([^)]+)\s+AS\s+DATETIME\s*\)", "CAST($1 AS DATETIME)", RegexOptions.IgnoreCase);

            // 3. 字符串函数
            strSql = Regex.Replace(strSql, @"\bLEN\s*\(", "CHAR_LENGTH(", RegexOptions.IgnoreCase);
            strSql = Regex.Replace(strSql, @"\bCHARINDEX\s*\(\s*([^,]+)\s*,\s*([^)]+)\)", "INSTR($2, $1)", RegexOptions.IgnoreCase);
            strSql = Regex.Replace(strSql, @"\bSUBSTRING\s*\(\s*([^,]+)\s*,\s*([^,]+)\s*,\s*([^)]+)\)", "SUBSTR($1, $2, $3)", RegexOptions.IgnoreCase);
            strSql = Regex.Replace(strSql, @"\bLEFT\s*\(\s*([^,]+)\s*,\s*([^)]+)\)", "LEFT($1, $2)", RegexOptions.IgnoreCase);
            strSql = Regex.Replace(strSql, @"\bRIGHT\s*\(\s*([^,]+)\s*,\s*([^)]+)\)", "RIGHT($1, $2)", RegexOptions.IgnoreCase);
            strSql = Regex.Replace(strSql, @"\bREPLACE\s*\(\s*([^,]+)\s*,\s*([^,]+)\s*,\s*([^)]+)\)", "REPLACE($1, $2, $3)", RegexOptions.IgnoreCase);

            // 4. 日期函数
            strSql = Regex.Replace(strSql, @"\bDATEADD\s*\(\s*(day|dd|d)\s*,\s*([^,]+)\s*,\s*([^)]+)\)", "DATE_ADD($3, INTERVAL $2 DAY)", RegexOptions.IgnoreCase);
            strSql = Regex.Replace(strSql, @"\bDATEADD\s*\(\s*(month|mm|m)\s*,\s*([^,]+)\s*,\s*([^)]+)\)", "DATE_ADD($3, INTERVAL $2 MONTH)", RegexOptions.IgnoreCase);
            strSql = Regex.Replace(strSql, @"\bDATEADD\s*\(\s*(year|yy|yyyy)\s*,\s*([^,]+)\s*,\s*([^)]+)\)", "DATE_ADD($3, INTERVAL $2 YEAR)", RegexOptions.IgnoreCase);
            strSql = Regex.Replace(strSql, @"\bGETDATE\s*\(\s*\)", "NOW()", RegexOptions.IgnoreCase);
            strSql = Regex.Replace(strSql, @"\bDATEDIFF\s*\(\s*(day|dd|d)\s*,\s*([^,]+)\s*,\s*([^)]+)\)", "DATEDIFF($3, $2)", RegexOptions.IgnoreCase);

            // 5. 空值处理
            strSql = Regex.Replace(strSql, @"\bISNULL\s*\(", "IFNULL(", RegexOptions.IgnoreCase);
            strSql = Regex.Replace(strSql, @"\bCOALESCE\s*\(", "COALESCE(", RegexOptions.IgnoreCase);

            // 6. 系统函数和变量
            strSql = Regex.Replace(strSql, @"\b@@IDENTITY\b", "LAST_INSERT_ID()", RegexOptions.IgnoreCase);
            strSql = Regex.Replace(strSql, @"\bSCOPE_IDENTITY\s*\(\s*\)", "LAST_INSERT_ID()", RegexOptions.IgnoreCase);
            strSql = Regex.Replace(strSql, @"\bNEWID\s*\(\s*\)", "UUID()", RegexOptions.IgnoreCase);

            // 7. 分页处理（ROW_NUMBER() OVER() 转换为 LIMIT）
            if (Regex.IsMatch(strSql, @"ROW_NUMBER\s*\(\s*\)\s*OVER\s*\(\s*ORDER BY", RegexOptions.IgnoreCase))
            {
                var match = Regex.Match(strSql, @"WHERE\s+ROW_NUMBER\s*\(\s*\)\s*OVER\s*\(\s*ORDER BY\s+([^)]+)\)\s+between\s+(\d+)\s+and\s+(\d+)", RegexOptions.IgnoreCase);
                if (match.Success)
                {
                    var orderBy = match.Groups[1].Value;
                    var start = int.Parse(match.Groups[2].Value);
                    var end = int.Parse(match.Groups[3].Value);
                    var limit = end - start + 1;
                    var offset = start - 1;

                    strSql = Regex.Replace(strSql, @"WHERE\s+ROW_NUMBER\s*\(\s*\)\s*OVER\s*\(\s*ORDER BY\s+[^)]+\)\s+between\s+\d+\s+and\s+\d+", "", RegexOptions.IgnoreCase);
                    strSql += $" ORDER BY {orderBy} LIMIT {offset}, {limit}";
                }
            }

            // 8. 其他特殊处理
            strSql = Regex.Replace(strSql, @"\bWITH\s*\(\s*NOLOCK\s*\)", "", RegexOptions.IgnoreCase);
            strSql = Regex.Replace(strSql, @"\bTRUNCATE\s+TABLE\s+", "TRUNCATE TABLE ", RegexOptions.IgnoreCase); // MySQL也支持TRUNCATE TABLE语法

            // 9. 最后添加LIMIT子句（如果之前有TOP）
            if (!string.IsNullOrEmpty(limitValue) && !Regex.IsMatch(strSql, @"\bLIMIT\s+\d+", RegexOptions.IgnoreCase))
            {
                strSql += $" LIMIT {limitValue}";
            }

            return strSql;
        }
        //private string SplitToMySql(string strSql)
        //{
        //    if (string.IsNullOrWhiteSpace(strSql))
        //        return strSql;

        //    // 1. 先处理TOP语法（提取数字后移除TOP子句）
        //    var limitValue = "";
        //    strSql = Regex.Replace(strSql,
        //        @"TOP\s*\(\s*(\d+)\s*\)|TOP\s+(\d+)",
        //        match =>
        //        {
        //            limitValue = !string.IsNullOrEmpty(match.Groups[1].Value)
        //                ? match.Groups[1].Value
        //                : match.Groups[2].Value;
        //            return ""; // 移除TOP子句
        //        },
        //        RegexOptions.IgnoreCase);

        //    // 2. 其他常规替换
        //    strSql = Regex.Replace(strSql, @"ISNULL\s*\(", "IFNULL(", RegexOptions.IgnoreCase);
        //    strSql = Regex.Replace(strSql, @"GETDATE\s*\(\s*\)", "CURDATE()", RegexOptions.IgnoreCase);

        //    // 3. 特殊处理SQL Server的DateDiff（参数顺序反转）
        //    strSql = Regex.Replace(strSql,
        //        @"DateDiff\s*\(\s*(day|dd|d)\s*,\s*([^,]+)\s*,\s*([^)]+)\s*\)",
        //        "DATEDIFF($3, $2)",
        //        RegexOptions.IgnoreCase);

        //    // 4. 最后添加LIMIT子句（如果存在TOP值）
        //    if (!string.IsNullOrEmpty(limitValue))
        //    {
        //        // 确保不重复添加LIMIT
        //        if (!Regex.IsMatch(strSql, @"\bLIMIT\s+\d+", RegexOptions.IgnoreCase))
        //        {
        //            strSql += $" LIMIT {limitValue}";
        //        }
        //    }

        //    return strSql;
        //}



        private string Object2JSON(object o, string props = null, bool isShortDate = false)
        {
            Newtonsoft.Json.JsonSerializerSettings settings = new Newtonsoft.Json.JsonSerializerSettings();
            if (props != null)
            {
                settings.ContractResolver = new LimitPropsContractResolver(props.Split(','));
            }
            settings.ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore;
            Newtonsoft.Json.Converters.IsoDateTimeConverter timeConverter = new Newtonsoft.Json.Converters.IsoDateTimeConverter();
            if (isShortDate)
            {
                timeConverter.DateTimeFormat = "yyyy-MM-dd";
            }
            else
            {
                timeConverter.DateTimeFormat = "yyyy-MM-dd HH:mm:ss";
            }
            settings.Converters.Add(timeConverter);
            string str = Newtonsoft.Json.JsonConvert.SerializeObject(o, Newtonsoft.Json.Formatting.None, settings);
            return str;
        }
    }
}
