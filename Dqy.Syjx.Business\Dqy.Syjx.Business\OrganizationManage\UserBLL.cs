using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Dqy.Syjx.Business.Cache;
using Dqy.Syjx.Business.SystemManage;
using Dqy.Syjx.Cache.Factory;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity;
using Dqy.Syjx.Entity.OrganizationManage;
using Dqy.Syjx.Entity.SystemManage;
using Dqy.Syjx.Entity.WxManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Enum.BusinessManage;
using Dqy.Syjx.Enum.OrganizationManage;
using Dqy.Syjx.Enum.SystemManage;
using Dqy.Syjx.Model;
using Dqy.Syjx.Model.Input.OrganizationManage;
using Dqy.Syjx.Model.Param;
using Dqy.Syjx.Model.Param.BusinessManage;
using Dqy.Syjx.Model.Param.OrganizationManage;
using Dqy.Syjx.Model.Param.SystemManage;
using Dqy.Syjx.Model.Param.WxManage;
using Dqy.Syjx.Service.BusinessManage;
using Dqy.Syjx.Service.OrganizationManage;
using Dqy.Syjx.Service.SystemManage;
using Dqy.Syjx.Service.WxManage;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Web.Code;
using NetTaste;
using Org.BouncyCastle.Crypto;
using static Dqy.Syjx.Util.ThirdOAuth.TztxSoftAcore2;

namespace Dqy.Syjx.Business.OrganizationManage
{
    public class UserBLL
    {
        /// <summary>
        /// ApiToken值（仅WebApi端传入）
        /// </summary>
        public string ApiToken = string.Empty;

        private UserService userService = new UserService();
        private UserBelongService userBelongService = new UserBelongService();
        private DepartmentService departmentService = new DepartmentService();
        private UserExtensionService userExtensionService = new UserExtensionService();
        private DepartmentBLL departmentBLL = new DepartmentBLL();
        private RoleService roleService = new RoleService();
        private UnitService unitService = new UnitService();
        private UnitRelationService unitRelationService = new UnitRelationService();
        private AttachmentService attachmentService = new AttachmentService();
        private ConfigSetService configSetService = new ConfigSetService();
        private ConfigSetBLL configSetBLL = new ConfigSetBLL();
        private UserBindOpenidService userBindOpenidService = new UserBindOpenidService();
        private LogLoginService logLoginService = new LogLoginService();
        private LogLoginBLL logLoginBLL = new LogLoginBLL();
        private SmsValidateBLL smsValidateBLL = new SmsValidateBLL();


        #region 获取数据
        public async Task<TData<List<UserEntity>>> GetList(UserListParam param)
        {
            TData<List<UserEntity>> obj = new TData<List<UserEntity>>();
            obj.Data = await userService.GetList(param);
            obj.Tag = 1;
            return obj;
        }

        public async Task<TData<List<UserEntity>>> GetPageList(UserListParam param, Pagination pagination)
        {
            TData<List<UserEntity>> obj = new TData<List<UserEntity>>();
            if (param?.DepartmentId != null)
            {
                param.ChildrenDepartmentIdList = await departmentBLL.GetChildrenDepartmentIdList(null, param.DepartmentId.Value);
            }
            else
            {
                OperatorInfo user = await Operator.Instance.Current(ApiToken);
                param.ChildrenDepartmentIdList = await departmentBLL.GetChildrenDepartmentIdList(null, user.DepartmentId.Value);
            }
            obj.Data = await userService.GetPageList(param, pagination);
            List<UserBelongEntity> userBelongList = await userBelongService.GetList(new UserBelongEntity { UserIds = obj.Data.Select(p => p.Id.Value).ParseToStrings<long>() });
            List<DepartmentEntity> departmentList = await departmentService.GetList(new DepartmentListParam { Ids = userBelongList.Select(p => p.BelongId.Value).ParseToStrings<long>() });
            foreach (UserEntity user in obj.Data)
            {
                user.DepartmentName = departmentList.Where(p => p.Id == user.DepartmentId).Select(p => p.DepartmentName).FirstOrDefault();
            }
            obj.Total = pagination.TotalCount;
            obj.Tag = 1;
            return obj;
        }

        public async Task<TData<UserEntity>> GetEntity(long id)
        {
            TData<UserEntity> obj = new TData<UserEntity>();
            obj.Data = await userService.GetEntity(id);

            await GetUserBelong(obj.Data);

            if (obj.Data.DepartmentId > 0)
            {
                DepartmentEntity departmentEntity = await departmentService.GetEntity(obj.Data.DepartmentId.Value);
                if (departmentEntity != null)
                {
                    obj.Data.DepartmentName = departmentEntity.DepartmentName;
                }
            }

            obj.Tag = 1;
            return obj;
        }

        public async Task<TData<UserEntity>> GetEntityByMobile(string mobile)
        {
            TData<UserEntity> obj = new TData<UserEntity>();
            var user =  await userService.GetEntityByMobile(mobile);

            if (user == null || user.Id == 0)
            {
                obj.Tag = 0;
            }
            else
            {
                obj.Tag = 1;
                obj.Data = user;
            }

           
            return obj;
        }

        /// <summary>
        /// 统一身份认证，不验证密码
        /// </summary>
        /// <param name="userName">用户名/手机号码</param>
        /// <param name="password">密码/验证码</param>
        /// <param name="platform"></param>
        /// <param name="validType">1：密码登录；2：手机验证码登录；3：第三方登录</param>
        /// <param name="clientType">客户端类型 0: 默认(H5)；1：微信小程序；2：钉钉小程序；3：企业微信</param>
        /// <param name="openId">第三方开放平台对应的id，如微信小程序openId</param>
        /// <param name="unionId">第三方开放平台对应的id，如微信小程序unionId</param>
        /// <param name="userType">5:家长；6：班主任(暂未实现)</param>
        /// <returns></returns>
        public async Task<TData<UserEntity>> CheckLogin(string userName, string password, int platform, int validType = 1, string ip = "", int clientType = 0, string openId = "", string unionId = "", long smsValidateId = 0, int userType = 0)
        {
            TData<UserEntity> obj = new TData<UserEntity>();
            obj.Tag = 0;

            //if (platform != PlatformEnum.ThirdSSO.ParseToInt() && platform != PlatformEnum.ThirdSSOApi.ParseToInt())
            if(validType == 1)
            {
                if (userName.IsEmpty() || password.IsEmpty())
                {
                    obj.Message = "用户名或密码不能为空";
                    return obj;
                }
                if (!string.IsNullOrEmpty(ip)) //IP不存在时，不校验，避免部分平台取不到IP造成用户无法登录问题，常州
                {
                    //同一IP，当日账号累积输错50次，禁止使用平台
                    var listLog = await logLoginService.GetSysLogLogin(1, ip, "登录失败。可能原因：用户名或密码错误");
                    if (listLog.Count > 200)
                    {
                        obj.Tag = 0;
                        obj.Message = $"您的操作，有侵入风险，系统已经拦截。如有疑问，请联系客服。您当前的IP地址是{ip}";
                        return obj;
                    }
                }
            }
            UserEntity user = await userService.CheckLogin(userName);

            bool bMobileLoginSucc = false; //通过手机登录结果
            //int cntainParent = 0;  //值为一，包含家长账号，包含家长账号，即使没有单位也能登录成功
            bool onlyContainParent = false; // 单位禁用，但是包含家长账号的情况

            if(validType == 2)
            {
                //验证用户登录信息
                var smsObj = await smsValidateBLL.GetEntity(smsValidateId);
                if (smsObj == null || smsObj.Tag != 1)
                {
                    obj.Tag = 0;
                    obj.Message = "未查询到获取验证码信息，登录失败";
                    return obj;
                }

                if (smsObj.Data.ErrorCount >= 3 || smsObj.Data.Status == 2)
                {
                    obj.Tag = 0;
                    obj.Message = "验证码已经失效，请重新获取";
                    return obj;
                }
                if(password == smsObj.Data.ValidateCode)
                {
                    bMobileLoginSucc = true;
                }
            }
            if ((user == null || user.Id == 0) && validType == 2 && bMobileLoginSucc && (userType == 5)) //自动创建学生家长账号
            {
                //cntainParent = 1;
                onlyContainParent = true;
                user = new UserEntity()
                {
                    UserName = userName,
                    Mobile = userName,
                    ContainParent = 1,
                    UserStatus = (int)StatusEnum.Yes,
                    UserValidate = DateTime.Now.AddYears(100)
                };
                //自动创建账号
                await userService.SaveUserEntity(user);
                //插入学生家长角色
                var roleId = await roleService.GetSysByRoleId(500);
                if (roleId != 0)
                {
                    await userBelongService.SaveForm(
                          new UserBelongEntity()
                          {
                              UserId = user.Id,
                              BelongId = roleId,
                              BelongType = 2
                          });
                }
            }

            if (user != null)
            {
                if(userType == 5 && user.ContainParent != 1) //追加家长角色
                {
                    var roleId = await roleService.GetSysByRoleId(500);
                    if (roleId != 0)
                    {
                        await userBelongService.SaveForm(
                              new UserBelongEntity()
                              {
                                  UserId = user.Id,
                                  BelongId = roleId,
                                  BelongType = 2
                              });
                    }
                    user.ContainParent = 1;
                    await userService.SaveUserEntity(user);

                }
                //cntainParent = user.ContainParent;
                //if (platform != PlatformEnum.ThirdSSO.ParseToInt() && platform != PlatformEnum.ThirdSSOApi.ParseToInt())
                if (validType == 1 || validType == 2)
                {
                    if (user.UserValidate != null && user.UserValidate < DateTime.Now.AddDays(-1))
                    {
                        obj.Tag = 0;
                        obj.Message = "您的账号已过有效期！";
                        return obj;
                    }
                }
                if (validType == 1) //密码登录
                {
                    //if (validType == 1)
                    //{
                    #region 判断24小时内连续登录密码错误是否超过5次
                    int errorNum = 0;
                    //获取最后一次登录正确登录的Id值
                    var userlastLog = await logLoginService.GetUserLastLoginLog(user.Id.Value, "成功");
                    if (userlastLog.Id > 0)
                    {
                        //获取大于最后一次成功登录后，错误的次数
                        var listLog = await logLoginService.GetSysLogLogin(0, "", "登录失败。可能原因：用户名或密码错误", user.Id.Value, 0, userlastLog.Id.Value);
                        errorNum = listLog.Count;
                    }
                    else
                    {
                        //24小时内没有登录成功过，获取24小时内错误次数
                        var listLog = await logLoginService.GetSysLogLogin(2, "", "登录失败。可能原因：用户名或密码错误", user.Id.Value, 0);
                        errorNum = listLog.Count;
                    }

                    //判断是否在24小时内连续登陆密码错误超过5次  
                    if (errorNum >= 5)
                    {
                        obj.Message = "登录失败。账号被锁定24小时,24小时后自动解除锁定或联系客服。";
                        return obj;
                    }
                    #endregion
                    //}
                }              

                user.UnitId = 0;
                if (user.UserStatus == (int)StatusEnum.Yes)
                {
                    var bHasUnit = true;
                    if (!onlyContainParent) //自动创建家长账号时，不做单位校验
                    {

                        var urList = await unitRelationService.GetList(new UnitRelationListParam { ExtensionObjId = user.Id.Value, ExtensionType = UnitRelationTypeEnum.User.ParseToInt() });
                        if (urList.Count > 0)
                        {
                            user.UnitId = urList.FirstOrDefault().UnitId.Value;
                            var unit = await unitService.GetEntity(user.UnitId.Value);
                            if (unit != null && unit.Statuz != (int)StatusEnum.Yes)
                            {
                                if (user.ContainParent != 1)
                                {
                                    obj.Message = string.Format("{0}被禁用，该单位下的所有账号都禁止登录。", unit.Name);
                                    return obj;
                                }
                                else
                                {
                                    user.OnlyContainParent = true;//仅家长登录
                                }
                            }

                        }
                        else
                        {
                            if (user.IsSystem != 1)
                            {
                                bHasUnit = false;
                                if (user.ContainParent != 1)
                                {
                                    obj.Message = "该账号不存在单位，无法登录，请联系客服协查你的账号。";
                                    return obj;
                                }
                                else
                                {
                                    user.OnlyContainParent = true;//仅家长登录
                                }
                            }


                        }
                    }
                        if (bMobileLoginSucc || bHasUnit)
                        {
                            string pswd = SecurityHelper.MD5ToHex(password + user.Salt).ToLower();
                            if (bMobileLoginSucc || user.Password == pswd || platform == PlatformEnum.ThirdSSO.ParseToInt() || platform == PlatformEnum.ThirdSSOApi.ParseToInt())
                            {
                                user.LoginCount++;
                                user.IsOnline = 1;

                                #region 设置日期
                                if (user.FirstVisit == GlobalConstant.DefaultTime)
                                {
                                    user.FirstVisit = DateTime.Now;
                                }
                                if (user.PreviousVisit == GlobalConstant.DefaultTime)
                                {
                                    user.PreviousVisit = DateTime.Now;
                                }
                                if (user.LastVisit != GlobalConstant.DefaultTime)
                                {
                                    user.PreviousVisit = user.LastVisit;
                                }
                                user.LastVisit = DateTime.Now;
                                #endregion

                                switch (platform)
                                {
                                    case (int)PlatformEnum.Web:
                                        if (GlobalContext.SystemConfig.LoginMultiple)
                                        {
                                            #region 多次登录用同一个token
                                            if (string.IsNullOrEmpty(user.WebToken))
                                            {
                                                user.WebToken = SecurityHelper.GetGuid(true);
                                            }
                                            #endregion
                                        }
                                        else
                                        {
                                            user.WebToken = SecurityHelper.GetGuid(true);
                                        }
                                        break;

                                    case (int)PlatformEnum.WebApi:
                                        if (GlobalContext.SystemConfig.LoginMultiplePhone)
                                        {
                                            //允许多次登录 ，多次登录用同一个token
                                            if (string.IsNullOrEmpty(user.ApiToken))
                                                user.ApiToken = SecurityHelper.GetGuid(true);
                                        }
                                        else
                                        {
                                            //只允许单台设备登录，删除原token缓存
                                            Operator.Instance.RemoveCurrent(user.ApiToken);
                                            user.ApiToken = SecurityHelper.GetGuid(true);
                                        }
                                        break;
                                    case (int)PlatformEnum.ThirdSSO: //第三方登录，多次登录用同一个token
                                        if (string.IsNullOrEmpty(user.WebToken))
                                        {
                                            user.WebToken = SecurityHelper.GetGuid(true);
                                        }
                                        break;
                                    case (int)PlatformEnum.ThirdSSOApi: //第三方登录手机端，多次登录用同一个token
                                        if (string.IsNullOrEmpty(user.ApiToken))
                                        {
                                            user.ApiToken = SecurityHelper.GetGuid(true);
                                        }
                                        break;
                                }
                                await GetUserBelong(user);

                                obj.Data = user;
                                obj.Message = "登录成功";
                                obj.Tag = 1;
                            }
                            else
                            {
                                obj.Data = user;
                                obj.Message = "登录失败。可能原因：用户名或密码错误";
                            }
                        }
                    
                    if (obj.Tag == 1 && clientType == ClientTypeEnum.MPWEIXIN.ParseToInt())
                    {
                        var uboList = await userBindOpenidService.GetList(new UserBindOpenidListParam
                        {
                            OpenId = openId,
                            //UserId = user.Id.Value
                        });
                       // LogHelper.Info($"即将插入：unionId,Openid:{unionId}_{openId}");
                        if (!uboList.Exists(m=> m.UserId == user.Id.Value))
                        {
                            await userBindOpenidService.SaveForm(new UserBindOpenidEntity
                            {
                                OpenId = openId,
                                UnionId = unionId,
                                UserId = user.Id.Value,
                                UnitId = user.UnitId.Value,
                                Statuz = 1,
                                RegTime = DateTime.Now,
                                IsDefaultLogin = 1                              
                            });   
                            if(uboList.Exists(m => m.IsDefaultLogin == 1))
                            {
                               var defaultUbo = uboList.First(m => m.IsDefaultLogin == 1);
                                defaultUbo.IsDefaultLogin = 0;
                                await userBindOpenidService.SaveForm(defaultUbo);
                            }
                        }
                        else
                        {
                            var currentUbo = uboList.First(m => m.UserId == user.Id.Value);
                            if(currentUbo.IsDefaultLogin != 1) //不是默认，更新为默认。
                            {
                                if (uboList.Exists(m => m.IsDefaultLogin == 1))
                                {
                                    var defaultUbo = uboList.First(m => m.IsDefaultLogin == 1);
                                    defaultUbo.IsDefaultLogin = 0;
                                    await userBindOpenidService.SaveForm(defaultUbo);
                                }

                                currentUbo.IsDefaultLogin = 1;
                                await userBindOpenidService.SaveForm(currentUbo);
                            }
                        }

                    }
                }
                else
                {
                    //if (platform != PlatformEnum.ThirdSSO.ParseToInt() && platform != PlatformEnum.ThirdSSOApi.ParseToInt())
                    if(validType == 1 || validType == 2)
                    {
                        obj.Message = "登录失败。可能原因：用户名或密码错误";
                    }
                    else
                    {
                        obj.Message = "该账号已被禁用登录。";
                    }
                }
            }
            else
            {
                //if (platform != PlatformEnum.ThirdSSO.ParseToInt() && platform != PlatformEnum.ThirdSSOApi.ParseToInt())
                if (validType == 1)
                {
                    obj.Message = "登录失败。可能原因：用户名或密码错误";
                }
                else
                {
                    obj.Message = "该账号信息不存在，已禁止登录。";
                }
            }
            return obj;
        }

        /// <summary>
        /// 钉钉、微信小程序自动登录
        /// </summary>
        /// <param name="userName"></param>
        /// <param name="platform"></param>
        /// <param name="openId"></param>
        /// <param name="unionId"></param>
        /// <returns></returns>
        public async Task<TData<UserEntity>> CheckLoginWx(long userId, int platform, string openId = "", string unionId = "")
        {
            TData<UserEntity> obj = new TData<UserEntity>();
            obj.Tag = 0;

            UserEntity user = await userService.GetEntity(userId);

            if (user == null || user.Id == 0)
            {
                obj.Message = "用户不存在";
                return obj;
            }

            user.UnitId = 0;
            if (user.UserStatus == (int)StatusEnum.Yes)
            {
                var urList = await unitRelationService.GetList(new UnitRelationListParam { ExtensionObjId = user.Id.Value, ExtensionType = UnitRelationTypeEnum.User.ParseToInt() });
                if (urList.Count > 0)
                {
                    user.UnitId = urList.FirstOrDefault().UnitId.Value;
                    var unit = await unitService.GetEntity(user.UnitId.Value);
                    if (unit != null && unit.Statuz != (int)StatusEnum.Yes)
                    {
                        if (user.ContainParent != 1)
                        {
                            obj.Message = string.Format("{0}被禁用，该单位下的所有账号都禁止登录。", unit.Name);
                            return obj;
                        }
                        else
                        {
                            user.OnlyContainParent = true;//仅家长登录
                        }
                    }

                }
                else
                {
                    if (user.IsSystem != 1)
                    {
                        if (user.ContainParent != 1)
                        {
                            obj.Message = "该账号不存在单位，无法登录，请联系客服协查你的账号。";
                            return obj;
                        }
                        else
                        {
                            user.OnlyContainParent = true;//仅家长登录
                        }
                    }

                }

                user.LoginCount++;
                user.IsOnline = 1;

                #region 设置日期
                if (user.FirstVisit == GlobalConstant.DefaultTime)
                {
                    user.FirstVisit = DateTime.Now;
                }
                if (user.PreviousVisit == GlobalConstant.DefaultTime)
                {
                    user.PreviousVisit = DateTime.Now;
                }
                if (user.LastVisit != GlobalConstant.DefaultTime)
                {
                    user.PreviousVisit = user.LastVisit;
                }
                user.LastVisit = DateTime.Now;
                #endregion

                switch (platform)
                {
                    case (int)PlatformEnum.Web:
                        if (GlobalContext.SystemConfig.LoginMultiple)
                        {
                            #region 多次登录用同一个token
                            if (string.IsNullOrEmpty(user.WebToken))
                            {
                                user.WebToken = SecurityHelper.GetGuid(true);
                            }
                            #endregion
                        }
                        else
                        {
                            user.WebToken = SecurityHelper.GetGuid(true);
                        }
                        break;

                    case (int)PlatformEnum.WebApi:
                        if (GlobalContext.SystemConfig.LoginMultiplePhone)
                        {
                            //允许多次登录 ，多次登录用同一个token
                            if (string.IsNullOrEmpty(user.ApiToken))
                                user.ApiToken = SecurityHelper.GetGuid(true);
                        }
                        else
                        {
                            //只允许单台设备登录，删除原token缓存
                            Operator.Instance.RemoveCurrent(user.ApiToken);
                            user.ApiToken = SecurityHelper.GetGuid(true);
                        }
                        break;
                    case (int)PlatformEnum.ThirdSSO: //第三方登录，多次登录用同一个token
                        if (string.IsNullOrEmpty(user.WebToken))
                        {
                            user.WebToken = SecurityHelper.GetGuid(true);
                        }
                        break;
                    case (int)PlatformEnum.ThirdSSOApi: //第三方登录手机端，多次登录用同一个token
                        if (string.IsNullOrEmpty(user.ApiToken))
                        {
                            user.ApiToken = SecurityHelper.GetGuid(true);
                        }
                        break;
                }
                await GetUserBelong(user);

                obj.Data = user;
                obj.Message = "登录成功";
                obj.Tag = 1;
            }
            return obj;
        }


        /// <summary>
        /// 切换用户登录
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="platform"></param>
        /// <param name="clientType"></param>
        /// <param name="openId"></param>
        /// <param name="unionId"></param>
        /// <returns></returns>
        public async Task<TData<UserEntity>> TurnUserLogin(long userId, int platform, int clientType = 0, string openId = "", string unionId = "")
        {
            TData<UserEntity> obj = new TData<UserEntity>();

          
            UserEntity user = await userService.GetEntity(userId);
            if (user != null)
            {
                if (platform != PlatformEnum.ThirdSSO.ParseToInt() && platform != PlatformEnum.ThirdSSOApi.ParseToInt())
                {
                    if (user.UserValidate != null && user.UserValidate < DateTime.Now.AddDays(-1))
                    {
                        obj.Tag = 0;
                        obj.Message = "您的账号已过有效期！";
                        return obj;
                    }

                }

                user.UnitId = 0;
                if (user.UserStatus == (int)StatusEnum.Yes)
                {
                    var bHasUnit = true;
                    var urList = await unitRelationService.GetList(new UnitRelationListParam { ExtensionObjId = user.Id.Value, ExtensionType = UnitRelationTypeEnum.User.ParseToInt() });
                    if (urList.Count > 0)
                    {
                        user.UnitId = urList.FirstOrDefault().UnitId.Value;
                        var unit = await unitService.GetEntity(user.UnitId.Value);
                        if (unit != null && unit.Statuz != (int)StatusEnum.Yes)
                        {
                            obj.Message = string.Format("{0}被禁用，该单位下的所有账号都禁止登录。", unit.Name);
                            return obj;
                        }
                    }
                    else
                    {
                        if (user.IsSystem != 1)
                        {
                            bHasUnit = false;
                            obj.Message = "该账号不存在单位，无法登录，请联系客服协查你的账号。";
                        }
                    }
                    if (bHasUnit)
                    {
                        user.LoginCount++;
                        user.IsOnline = 1;

                        #region 设置日期
                        if (user.FirstVisit == GlobalConstant.DefaultTime)
                        {
                            user.FirstVisit = DateTime.Now;
                        }
                        if (user.PreviousVisit == GlobalConstant.DefaultTime)
                        {
                            user.PreviousVisit = DateTime.Now;
                        }
                        if (user.LastVisit != GlobalConstant.DefaultTime)
                        {
                            user.PreviousVisit = user.LastVisit;
                        }
                        user.LastVisit = DateTime.Now;
                        #endregion

                        switch (platform)
                        {
                            case (int)PlatformEnum.Web:
                                if (GlobalContext.SystemConfig.LoginMultiple)
                                {
                                    #region 多次登录用同一个token
                                    if (string.IsNullOrEmpty(user.WebToken))
                                    {
                                        user.WebToken = SecurityHelper.GetGuid(true);
                                    }
                                    #endregion
                                }
                                else
                                {
                                    user.WebToken = SecurityHelper.GetGuid(true);
                                }
                                break;

                            case (int)PlatformEnum.WebApi:
                                if (GlobalContext.SystemConfig.LoginMultiplePhone)
                                {
                                    //允许多次登录 ，多次登录用同一个token
                                    if (string.IsNullOrEmpty(user.ApiToken))
                                        user.ApiToken = SecurityHelper.GetGuid(true);
                                }
                                else
                                {
                                    //只允许单台设备登录，删除原token缓存
                                    Operator.Instance.RemoveCurrent(user.ApiToken);
                                    user.ApiToken = SecurityHelper.GetGuid(true);
                                }
                                break;
                            case (int)PlatformEnum.ThirdSSO: //第三方登录，多次登录用同一个token
                                if (string.IsNullOrEmpty(user.WebToken))
                                {
                                    user.WebToken = SecurityHelper.GetGuid(true);
                                }
                                break;
                            case (int)PlatformEnum.ThirdSSOApi: //第三方登录手机端，多次登录用同一个token
                                if (string.IsNullOrEmpty(user.ApiToken))
                                {
                                    user.ApiToken = SecurityHelper.GetGuid(true);
                                }
                                break;
                        }
                        await GetUserBelong(user);

                        obj.Data = user;
                        obj.Message = "登录成功";
                        obj.Tag = 1;
                        await userService.TurnDefaultUser(user);

                    }

                    if (obj.Tag == 1 && clientType == ClientTypeEnum.MPWEIXIN.ParseToInt())
                    {
                        var uboList = await userBindOpenidService.GetList(new UserBindOpenidListParam
                        {
                            OpenId = openId,
                            UserId = user.Id.Value
                        });
                        // LogHelper.Info($"即将插入：unionId,Openid:{unionId}_{openId}");
                        if (uboList.Count() <= 0)
                        {
                            await userBindOpenidService.SaveForm(new UserBindOpenidEntity
                            {
                                OpenId = openId,
                                UnionId = unionId,
                                UserId = user.Id.Value,
                                UnitId = user.UnitId.Value,
                                Statuz = 1,
                                RegTime = DateTime.Now,
                                IsDefaultLogin = 1
                            });
                        }
                        else if (!string.IsNullOrEmpty(unionId)) //如果unionId 有值但是以前没有，需要更新一下
                        {
                            var ubo = uboList.FirstOrDefault();
                            if (string.IsNullOrEmpty(ubo.UnionId))
                            {
                                ubo.UnionId = unionId;
                                await userBindOpenidService.SaveForm(ubo);
                            }
                        }

                    }
                }
                else
                {
                    obj.Message = "登录失败。可能原因：用户已被禁用";
                }
            }
            else
            {
                obj.Message = "登录失败。可能原因：用户不存在";
            }
            return obj;
        }



        /// <summary>
        /// 获取用户列表
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<TData<List<UserEntity>>> GetUserList(UserListParam param, Pagination pagination)
        {
            OperatorInfo user = await Operator.Instance.Current(ApiToken);
            TData<List<UserEntity>> obj = new TData<List<UserEntity>>();
            param.UnitId = user.UnitId;
            if (param.DepartmentId != null && param.DepartmentId > 0)
            {
                param.ChildrenDepartmentIdList = await departmentBLL.GetChildrenDepartmentIdList(null, param.DepartmentId.Value);
            }
            obj.Data = await userService.GetUserList(param, pagination);
            obj.Total = pagination.TotalCount;
            obj.Tag = 1;
            return obj;
        }

        /// <summary>
        /// 获取区县用户列表
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<TData<List<UserEntity>>> GetCountyUserList(UserListParam param, Pagination pagination)
        {
            OperatorInfo user = await Operator.Instance.Current(ApiToken);
            TData<List<UserEntity>> obj = new TData<List<UserEntity>>();
            param.UnitId = user.UnitId;
            if (param.DepartmentId != null && param.DepartmentId > 0)
            {
                param.ChildrenDepartmentIdList = await departmentBLL.GetChildrenDepartmentIdList(null, param.DepartmentId.Value);
            }
            obj.Data = await userService.GetCountyUserList(param, pagination);
            obj.Total = pagination.TotalCount;
            obj.Tag = 1;
            return obj;
        }


        /// <summary>
        /// 获取下属单位超管信息
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<TData<List<UserEntity>>> GetSuperUserList(UserListParam param, Pagination pagination)
        {
            OperatorInfo user = await Operator.Instance.Current(ApiToken);
            TData<List<UserEntity>> obj = new TData<List<UserEntity>>();
            param.UnitId = user.UnitId;
            obj.Data = await userService.GetSuperUserList(param, pagination);
            obj.Total = pagination.TotalCount;
            obj.Tag = 1;
            return obj;
        }

        /// <summary>
        /// 查询所有单位用户信息
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<TData<List<UserEntity>>> GetUserAllList(UserListParam param, Pagination pagination)
        {
            OperatorInfo user = await Operator.Instance.Current(ApiToken);
            TData<List<UserEntity>> obj = new TData<List<UserEntity>>();
            ////角色Id
            //string SystemSuperId = (RoleEnum.SystemSuper.ParseToInt() + 16508640061130100).ToString(); //超管
            //string ConfigManagerId = (RoleEnum.ConfigManager.ParseToInt() + 385804331967844300).ToString(); //配置超管 

            //验证只有超管，配置超管才能查看
            if(user.UnitType != 0 || user.UnitId != 100000000000000001)
            {
                obj.Message = "非法操作";
                return obj;
            }
             

            if (user.IsSystem != 1)
            {
                param.IsSystem = 1;
            }
            if (param.DepartmentId != null)
            {
                param.ChildrenDepartmentIdList = await departmentBLL.GetChildrenDepartmentIdList(null, param.DepartmentId.Value);
            }
            obj.Data = await userService.GetUserAllList(param, pagination);
            obj.Total = pagination.TotalCount;
            obj.Tag = 1;
            return obj;
        }

        public async Task<TData<string>> GetAdminJson()
        {
            TData<string> obj = new TData<string>();
            OperatorInfo user = await Operator.Instance.Current(ApiToken);
            if (user.UnitType != UnitTypeEnum.School.ParseToInt())
            {
                obj.Message = "非法操作";
                return obj;
            }

            var list = await userService.GetUserList(new UserListParam() { UnitId = user.UnitId, RoleId = RoleEnum.SchoolManager.ParseToInt() }, new Pagination() { Sort = "Id", SortType = "DESC", PageSize = 3, PageIndex = 0 });
            if (list != null && list.Count > 0)
            {
                obj.Data = string.Join(',', list.Select(m => m.RealName));
                obj.Tag = 1;
            }
            return obj;
        }

        public async Task<TData<List<UserEntity>>> GetUserByDepartmentList(UserListParam param)
        {
            OperatorInfo user = await Operator.Instance.Current(ApiToken);
            TData<List<UserEntity>> obj = new TData<List<UserEntity>>();
            param.UnitId = user.UnitId;
            if (param.DepartmentId != null && param.DepartmentId > 0)
            {
                param.ChildrenDepartmentIdList = await departmentBLL.GetChildrenDepartmentIdList(null, param.DepartmentId.Value);
            }
            obj.Data = await userService.GetUserByDepartmentList(param);
            obj.Tag = 1;
            return obj;
        }

        public async Task<TData<List<UserEntity>>> GetListByRoleId(long roleid)
        {
            TData<List<UserEntity>> obj = new TData<List<UserEntity>>();
            OperatorInfo user = await Operator.Instance.Current(ApiToken);
            if (user.UnitType != UnitTypeEnum.School.ParseToInt())
            {
                obj.Message = "非法操作";
                return obj;
            }
            var param = new UserListParam();
            param.UnitId = user.UnitId;
            param.RoleId = roleid;
            param.UserStatus = 1;
            obj.Data = await userService.GetUserAllList(param, new Pagination() { PageSize = int.MaxValue });
            obj.Tag = 1;
            obj.Message = "查询成功。";
            return obj;
        }

        public async Task<TData<List<UserEntity>>> GetComUserByRoleId(UserListParam param)
        {
            TData<List<UserEntity>> obj = new TData<List<UserEntity>>();
            OperatorInfo user = await Operator.Instance.Current(ApiToken);
            if (user.UnitType != UnitTypeEnum.School.ParseToInt())
            {
                obj.Message = "非法操作";
                return obj;
            }
            obj.Data = await userService.GetComboxUserByRoleId(param.UnitId.Value, param.RoleId.Value);
            obj.Tag = 1;
            obj.Message = "查询成功。";
            return obj;
        }

        public async Task<TData<List<UserExtendModel>>> GetUserRoleList(UserListParam param)
        {
            TData<List<UserExtendModel>> obj = new TData<List<UserExtendModel>>();
            OperatorInfo operatorInfo = await Operator.Instance.Current(ApiToken);
            if (operatorInfo == null)
            {
                obj.Tag = -1;
                obj.Message = "登录信息失效";
                return obj;
            }
            param.UnitType = operatorInfo.UnitType;
            param.UnitId = operatorInfo.UnitId ?? 0;
            obj.Data = await userService.GetUserRoleList(param);
            obj.Tag = 1;
            obj.Message = "查询成功";
            return obj;
        }
        #endregion

        #region 获取当前用户信息
        /// <summary>
        /// 获取是否是管理员，不是返回2个管理员名称
        /// </summary>
        /// <returns></returns>
        public async Task<TData<object>> GetIsAdminInfo()
        {
            TData<object> obj = new TData<object>();
            obj.Message = "执行失败，数据异常。";
            OperatorInfo user = await Operator.Instance.Current(ApiToken);

            if (user.RoleValues != null && user.RoleValues.Contains(RoleValueEnum.SchoolManager.ParseToInt().ToString()))
            {
                //是管理，直接跳转到设置页面
                obj.Tag = 1;
                obj.Message = "执行成功。";
                obj.Data = new { IsAdmin = true, AdminName = "" };
            }
            else
            {
                //获取当前单位管理员2个。联系管理设置
                UserListParam userparam = new UserListParam();
                userparam.IsSystem = IsEnum.No.ParseToInt();
                userparam.UnitId = user.UnitId.Value;
                userparam.UserStatus = StatusEnum.Yes.ParseToInt();
                if (user.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    userparam.RoleId = RoleEnum.SchoolManager.ParseToInt();
                }
                else if (user.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    userparam.RoleId = RoleEnum.CountyManager.ParseToInt();
                }
                else if (user.UnitType == UnitTypeEnum.City.ParseToInt())
                {
                    userparam.RoleId = RoleEnum.CityManager.ParseToInt();
                }
                else if (user.UnitType == UnitTypeEnum.Company.ParseToInt())
                {
                    //userparam.RoleId = RoleEnum.Com.ParseToInt();
                }
                else if (user.UnitType == UnitTypeEnum.System.ParseToInt())
                {
                    userparam.IsSystem = IsEnum.Yes.ParseToInt();
                    userparam.RoleId = RoleEnum.SystemSuper.ParseToInt();
                }
                Pagination pagin = new Pagination();
                pagin.PageSize = 2;
                var list = await userService.GetUserAllList(userparam, pagin);
                string adminName = "";
                if (list != null && list.Count > 0)
                {
                    adminName = string.Join(",", list.Select(m => m.RealName));
                }
                obj.Tag = 1;
                obj.Message = "执行成功。";
                obj.Data = new { IsAdmin = false, AdminName = adminName };
            }
            return obj;
        }

        /// <summary>
        /// 查询当前用户是否拥有指定角色，
        /// 未拥有指定角色时返回该单位指定角色的用户信息
        /// </summary>
        /// <param name="roleId">角色Id</param>
        /// <param name="searchMaxCount">最大查询条数</param>
        /// <returns></returns>
        public async Task<TData<List<UserEntity>>> GetIsRoleInfo(int roleId, int searchMaxCount)
        {
            TData<List<UserEntity>> obj = new TData<List<UserEntity>>();
            if (roleId > 0)
            {
                OperatorInfo user = await Operator.Instance.Current(ApiToken);
                if (user != null)
                {
                    if (!user.RoleIds.IsEmpty() && user.RoleIds.Contains(roleId.ToString()))
                    {
                        //表示当前用户包含指定角色
                        obj.Tag = 2;
                        obj.Message = "当前用户包含指定角色！";
                    }
                    else
                    {
                        //查询指定角色用户
                        var list = await userService.GetUserAllList(new UserListParam
                        {
                            UnitId = user.UnitId,
                            UserStatus = StatusEnum.Yes.ParseToInt(),
                            RoleId = roleId
                        }, new Pagination { PageSize = searchMaxCount });
                        obj.Tag = 1;
                        obj.Message = "查询指定角色用户成功！";
                        obj.Data = list;
                    }
                }
            }
            else
            {
                obj.Tag = 2;
                obj.Message = "当前用户包含指定角色！";
            }
            return obj;
        }

        #endregion
        #region 提交数据

        /// <summary>
        /// 超管添加用户信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<TData<string>> SaveForm(UserInputModel model)
        {
            OperatorInfo user = await Operator.Instance.Current(ApiToken);
            TData<string> obj = new TData<string>();
            string SystemSuperId = (RoleEnum.SystemSuper.ParseToInt() + 16508640061130100).ToString();

            var entity = new UserEntity();
            entity.UnitId = model.UnitId;
            entity.Id = model.Id;
            entity.UserName = model.UserName;
            entity.RealName = model.RealName;
            entity.Gender = model.Gender;
            entity.Birthday = model.Birthday;
            entity.Email = model.Email;
            entity.Mobile = model.Mobile;
            entity.DepartmentIds = model.DepartmentIds;
            entity.RoleIds = model.RoleIds.TrimEnd(',');
            entity.UserStatus = model.UserStatus;
            entity.ThirdUserId = model.ThirdUserId;

            if (model.UserValidate != null)
            {
                entity.UserValidate = model.UserValidate;
            }
            

            if (userService.ExistUserName(entity))
            {
                obj.Message = "用户名已经存在！";
                return obj;
            }
            if (entity.Id.IsNullOrZero())
            {
                //验证手机号码必须唯一  zyf  20241121 根据需求修改
                var userEntity = await userService.GetEntityByMobile(entity.Mobile);
                if (userEntity != null)
                {
                    obj.Message = "手机号码已经存在";
                    return obj;
                }

                entity.IsChangeRole = true;
                entity.IsChangeDepartment = true;
                entity.Salt = GetPasswordSalt();
                entity.Password = SecurityHelper.MD5ToHex(model.Password + entity.Salt).ToLower();
            }
            else
            {
                UserEntity dbUserEntity = await userService.GetUserSingle(entity.Id.Value);
                if (dbUserEntity != null)
                {
                    if (dbUserEntity.RoleIds.Contains(SystemSuperId) && !model.RoleIds.Contains(SystemSuperId))
                    {
                        TData<List<UserEntity>> objUserList = await GetUserList(new UserListParam() { RoleId = long.Parse(SystemSuperId), UserStatus = 1, UnitId = user.UnitId.Value }, new Pagination() { });
                        if (objUserList.Data.Count <= 1)
                        {
                            obj.Message = "请至少保留一个“运营超管”！";
                            return obj;
                        }
                    }

                    //验证手机号码必须唯一  zyf  20241120 根据需求修改
                    var userEntity = await userService.GetEntityByMobile(entity.Id.Value,entity.Mobile);
                    if (userEntity != null)
                    {
                        obj.Message = "手机号码已经存在";
                        return obj;
                    }

                    if (dbUserEntity.UnitId != model.UnitId)
                    {
                        entity.IsChangeUnitId = true;
                    }
                    entity.IsChangeRole = false;
                    entity.IsChangeDepartment = false;

                    if (!model.Password.Equals("NoChange"))
                    {
                        entity.Salt = GetPasswordSalt();
                        entity.Password = SecurityHelper.MD5ToHex(model.Password + entity.Salt).ToLower();
                    }
                    else
                    {
                        model.Password = null;
                    }

                    if (string.IsNullOrEmpty(dbUserEntity.RoleIds))
                    {
                        entity.IsChangeRole = true;
                    }
                    else
                    {
                        if (!dbUserEntity.RoleIds.Equals(entity.RoleIds))
                        {
                            entity.IsChangeRole = true;
                        }
                    }

                    if (dbUserEntity.DepartmentIds == null)
                    {
                        dbUserEntity.DepartmentIds = "";
                    }
                    if (!string.IsNullOrEmpty(entity.DepartmentIds) && !entity.DepartmentIds.Equals(dbUserEntity.DepartmentIds))
                    {
                        entity.IsChangeDepartment = true;
                    }
                }
            }
            if (!entity.Birthday.IsEmpty())
            {
                entity.Birthday = entity.Birthday.ParseToDateTime().ToString("yyyy-MM-dd");
            }
            await userService.SaveUserForm(entity);
            obj.Data = entity.Id.ParseToString();
            obj.Tag = 1;
            return obj;
        }

        /// <summary>
        /// 一般用户保存自己信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<TData<string>> SaveExtForm(UserInfoInputModel model)
        {
            OperatorInfo user = await Operator.Instance.Current(ApiToken);
            TData<string> obj = new TData<string>();
            Repository db = userService.BaseRepository();
            try
            {
                /*新增后端填报配置验证*/
                var VerifyHintStr = "";
                TData<List<ConfigSetEntity>> configObj = await configSetBLL.GetListByCode(new ConfigSetListParam {TypeCode = "4RYGL-1GRDA-1JBXX", ModuleType = ConfigSetModuleTypeEnum.FormRequired.ParseToInt() }, user);
                if (configObj.Tag == 1 && configObj.Data.Count > 0)
                {
                    foreach (var item in configObj.Data)
                    {
                        if (item.ConfigValue == "1")
                        {
                            var typecode = item.TypeCode.Replace("4RYGL-1GRDA-1JBXX-", "");
                            PropertyInfo[] properties = model.GetType().GetProperties();
                            foreach (var pitem in properties)
                            {
                                if (pitem.Name.Contains(typecode)) //判断属性名称是否和数据库配置的验证字段相匹配
                                {
                                    object val = pitem.GetValue(model) == null ? "" : pitem.GetValue(model); //获取属性值
                                    if ((val.ToString().Length == 0) || (val.ToString() == "0"))
                                    {
                                        VerifyHintStr += item.VerifyHint + "<br/>";
                                    }
                                }
                            }
                        }
                    }
                    if (VerifyHintStr != "")
                    {
                        obj.Tag = 0;
                        obj.Message = VerifyHintStr;
                        return obj;
                    }
                }

                var entity = await userService.GetEntity(user.UserId.Value);
                if (entity == null)
                {
                    obj.Tag = 0;
                    obj.Message = "执行异常，用户信息不存在，请联系客服协助处理。";
                    return obj;
                }
                entity.RealName = model.RealName;
                entity.Gender = model.Gender;
                entity.Email = model.Email;
                entity.Mobile = model.Mobile;
                if (!model.Birthday.IsEmpty())
                {
                    entity.Birthday = model.Birthday.ParseToDateTime().ToString("yyyy-MM-dd");
                }
                else
                {
                    entity.Birthday = "";
                }
              
                var list = await userExtensionService.GetList(new UserExtensionListParam() { UserId = user.UserId, IsCurrentUnit = IsEnum.Yes.ParseToInt() });
                UserExtensionEntity userextEntity = new UserExtensionEntity();
                if (list != null && list.Count > 0)
                {
                    userextEntity = list.FirstOrDefault();
                }

                var attachmentlist = await attachmentService.GetList(new AttachmentListParam() { OptType = 1, FileCategory = FileCategoryEnum.WorkLicense.ParseToInt(), ObjectId = entity.Id, UserId = user.UserId, Ids = model.Imagez });
                //事务开始
                await db.BeginTrans();
                await userService.UpdateTransUser(entity, db);
                obj.Data = entity.Id.ParseToString();
                if (userextEntity.Id > 0)
                {
                    if (userextEntity.UnitId != user.UnitId)
                    {
                        //更新历史信息。
                        var extEntity = list.FirstOrDefault();
                        extEntity.IsCurrentUnit = IsEnum.No.ParseToInt();
                        await userExtensionService.SaveTranForm(extEntity, db);
                        userextEntity = new UserExtensionEntity();
                        userextEntity.IsCurrentUnit = IsEnum.Yes.ParseToInt();
                        userextEntity.SysUserId = extEntity.SysUserId;
                        userextEntity.UnitId = user.UnitId;
                        userextEntity.IsExperimenter = extEntity.IsExperimenter;
                        userextEntity.ExperimenterNature = extEntity.ExperimenterNature;
                        userextEntity.ExperimenterCard = extEntity.ExperimenterCard;
                        userextEntity.TotalSchoolHour = extEntity.TotalSchoolHour;
                        
                    }
                    else
                    {
                        userextEntity = list.FirstOrDefault();
                    }
                }
                else
                {
                    userextEntity = new UserExtensionEntity();
                    userextEntity.IsCurrentUnit = IsEnum.Yes.ParseToInt();
                    userextEntity.SysUserId = entity.Id;
                    userextEntity.UnitId = user.UnitId;
                    //userextEntity.IsExperimenter = model.IsExperimenter;
                    //userextEntity.ExperimenterNature = model.ExperimenterNature;
                    userextEntity.ExperimenterCard = model.ExperimenterCard;
                    //userextEntity.TotalSchoolHour = model.TotalSchoolHour;
                }
                userextEntity.Major = model.Major;
                userextEntity.EducationLevel = model.EducationLevel ?? 0;
                userextEntity.JobTitle = model.JobTitle ?? 0;
                await userExtensionService.SaveTranForm(userextEntity,db);

                //图片 ，获取的是funroomUseId =0 或者等于当前这个。
                if (attachmentlist != null && attachmentlist.Count > 0)
                {
                    if (!string.IsNullOrEmpty(model.Imagez))
                    {
                        foreach (var item in attachmentlist)
                        {
                            if (model.Imagez.Contains(item.Id.ToString()))
                            {
                                if (item.ObjectId == 0)
                                {
                                    item.ObjectId = entity.Id;
                                    await attachmentService.UpdateTransForm(item, db);
                                }
                            }
                            else
                            {
                                item.BaseIsDelete = IsEnum.Yes.ParseToInt();
                                await attachmentService.UpdateTransForm(item, db);
                            }
                        }
                    }
                } 
                await db.CommitTrans();
                obj.Tag = 1;
            }
            catch (Exception ex)
            {
                obj.Tag = 0;
                obj.Message = "执行异常，请联系客服协助处理。";
                await db.RollbackTrans();
                throw new Exception("程序出现异常，异常信息为：" + ex.Message);
            }
            return obj;
        }

        public async Task<TData> DeleteForm(string ids)
        {
            TData obj = new TData();
            if (ids.IsEmpty())
            {
                return obj;
            }
            if (string.IsNullOrEmpty(ids))
            {
                obj.Message = "参数不能为空";
                return obj;
            }
            await userService.DeleteForm(ids);

            await RemoveCacheById(ids);

            obj.Tag = 1;
            return obj;
        }

        public async Task<TData<long>> ResetPassword(UserEntity entity)
        {
            TData<long> obj = new TData<long>();
            if (entity.Id > 0)
            {
                UserEntity dbUserEntity = await userService.GetEntity(entity.Id.Value);
                if (dbUserEntity.Password == entity.Password)
                {
                    obj.Message = "密码未更改";
                    return obj;
                }
                entity.Salt = GetPasswordSalt();
                //entity.Password = EncryptUserPassword(entity.Password, entity.Salt);
                entity.Password = SecurityHelper.MD5ToHex(entity.Password + entity.Salt).ToLower();
                await userService.ResetPassword(entity);

                await RemoveCacheById(entity.Id.Value);

                obj.Data = entity.Id.Value;
                obj.Tag = 1;
            }
            return obj;
        }

        public async Task<TData<long>> ChangePassword(ChangePasswordParam param)
        {
            OperatorInfo user = await Operator.Instance.Current(ApiToken);
            TData<long> obj = new TData<long>();
            if (param.Id > 0)
            {
                //判断当前用户Id是否一致
                if (user.UserId != param.Id)
                {
                    obj.Message = "非法操作";
                    return obj;
                }

                if (string.IsNullOrEmpty(param.Password) || string.IsNullOrEmpty(param.NewPassword))
                {
                    obj.Message = "新密码不能为空";
                    return obj;
                }
                UserEntity dbUserEntity = await userService.GetEntity(param.Id.Value);
                if (dbUserEntity.Password != EncryptUserPassword(param.Password, dbUserEntity.Salt))
                {
                    obj.Message = "旧密码不正确";
                    return obj;
                }
                dbUserEntity.Salt = GetPasswordSalt();
                dbUserEntity.Password = EncryptUserPassword(param.NewPassword, dbUserEntity.Salt);
                await userService.ResetPassword(dbUserEntity);

                await RemoveCacheById(param.Id.Value);

                obj.Data = dbUserEntity.Id.Value;
                obj.Tag = 1;
            }
            return obj;
        }

        /// <summary>
        /// 用户自己修改自己的信息
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public async Task<TData<long>> ChangeUser(UserEntity entity)
        {
            OperatorInfo user = await Operator.Instance.Current(ApiToken);
            TData<long> obj = new TData<long>();
            if (entity.Id > 0)
            {
                //判断当前用户Id是否一致
                if(user.UserId != entity.Id)
                {
                    obj.Message = "非法操作";
                    return obj;
                }

                //验证手机号码必须唯一  zyf  20241120 根据需求修改
                var userEntity = await userService.GetEntityByMobile(entity.Id.Value, entity.Mobile);
                if (userEntity != null)
                {
                    obj.Message = "手机号码已经存在";
                    return obj;
                }

                await userService.ChangeUser(entity);
                await RemoveCacheById(entity.Id.Value);

                obj.Data = entity.Id.Value;
                obj.Tag = 1;
            }
            return obj;
        }

        public async Task<TData> UpdateUser(UserEntity entity)
        {
            TData obj = new TData();
            await userService.UpdateUser(entity);

            obj.Tag = 1;
            return obj;
        }

        public async Task<TData> ImportUser(ImportParam param, List<UserEntity> list)
        {
            TData obj = new TData();
            if (list.Any())
            {
                foreach (UserEntity entity in list)
                {
                    UserEntity dbEntity = await userService.GetEntity(entity.UserName);
                    if (dbEntity != null)
                    {
                        entity.Id = dbEntity.Id;
                        if (param.IsOverride == 1)
                        {
                            await userService.SaveForm(entity);
                            await RemoveCacheById(entity.Id.Value);
                        }
                    }
                    else
                    {
                        await userService.SaveForm(entity);
                        await RemoveCacheById(entity.Id.Value);
                    }
                }
                obj.Tag = 1;
            }
            else
            {
                obj.Message = " 未找到导入的数据";
            }
            return obj;
        }

        /// <summary>
        /// 添加用户信息(后加)
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public async Task<TData<string>> SaveUserForm(UserInputModel model)
        {
            OperatorInfo user = await Operator.Instance.Current(ApiToken);
            TData<string> obj = new TData<string>();
            //角色Id
            string SystemSuperId = (RoleEnum.SystemSuper.ParseToInt() + 16508640061130100).ToString();
            string CityManager = (RoleEnum.CityManager.ParseToInt() + 16508640061130100).ToString();
            string CountyManager = (RoleEnum.CountyManager.ParseToInt() + 16508640061130100).ToString();
            string SchoolManager = (RoleEnum.SchoolManager.ParseToInt() + 16508640061130100).ToString();
            string ConfigManagerId = (RoleEnum.ConfigManager.ParseToInt() + 385804331967844300).ToString(); //配置超管 
            
            if (user.RoleIds == null)
            {
                obj.Message = "非法操作";
                return obj;
            }
            //验证只有超管才能创建修改用户
            if (!user.RoleIds.Contains(SystemSuperId)
                && !user.RoleIds.Contains(CityManager)
                && !user.RoleIds.Contains(CountyManager)
                && !user.RoleIds.Contains(SchoolManager)
                && !user.RoleIds.Contains(ConfigManagerId)
                )
            {
                obj.Message = "只有系统超管、市级超管、区县超管、学校超管、配置超管才能创建用户";
                return obj;
            }

            //学校超管只能添加学校相关的角色用户，区县只能添加区县、学校用户信息，市级只能添加市级、区县用户信息
            if(user.RoleIds.Contains(SchoolManager))
            {
                if(model.RoleIds.Contains(SystemSuperId) || model.RoleIds.Contains(CityManager) || model.RoleIds.Contains(CountyManager))
                {
                    obj.Message = "您无权操作";
                    return obj;
                }
            }
            else if(user.RoleIds.Contains(CountyManager))
            {
                if (model.RoleIds.Contains(SystemSuperId) || model.RoleIds.Contains(CityManager))
                {
                    obj.Message = "您无权操作";
                    return obj;
                }
            }
            else if(user.RoleIds.Contains(CityManager))
            {
                if (model.RoleIds.Contains(SystemSuperId))
                {
                    obj.Message = "您无权操作";
                    return obj;
                }
            }

            var entity = new UserEntity();
            entity.UnitId = user.UnitId;
            entity.Id = model.Id;
            entity.UserName = model.UserName;
            entity.RealName = model.RealName;
            entity.Gender = model.Gender;
            entity.Birthday = model.Birthday;
            entity.Email = model.Email;
            entity.Mobile = model.Mobile;
            entity.DepartmentIds = model.DepartmentIds;
            entity.RoleIds = model.RoleIds;
            entity.UserStatus = 1;
            if (userService.ExistUserName(entity))
            {
                obj.Message = "用户名已经存在！";
                return obj;
            }
            if (entity.Id.IsNullOrZero())
            {
                entity.IsChangeRole = true;
                entity.IsChangeDepartment = true;
                entity.Salt = GetPasswordSalt();

                //需要判断添加的超管是否超过2个
                if (model.RoleIds.Contains(SchoolManager))
                {
                    TData<List<UserEntity>> objUserList = await GetUserList(new UserListParam() { RoleId = long.Parse(SchoolManager), UserStatus = 1, UnitId = user.UnitId.Value }, new Pagination() { });
                    if (objUserList.Data.Count >= 4)
                    {
                        obj.Message = "单位“系统管理员”不能超过4个！";
                        return obj;
                    }
                }

                //验证手机号码必须唯一  zyf  20241120 根据需求修改
                var userEntity = await userService.GetEntityByMobile(entity.Mobile);
                if(userEntity != null)
                {
                    obj.Message = "手机号码已经存在";
                    return obj;
                }


                entity.Password = SecurityHelper.MD5ToHex(model.Password + entity.Salt).ToLower();
            }
            else
            {
                UserEntity dbUserEntity = await userService.GetUserSingle(entity.Id.Value);
                if (dbUserEntity != null)
                {
                     //需要判断添加的超管是否超过2个
                    if (!dbUserEntity.RoleIds.Contains(SchoolManager) && entity.RoleIds.Contains(SchoolManager))
                    {
                        TData<List<UserEntity>> objUserList = await GetUserList(new UserListParam() { RoleId = long.Parse(SchoolManager), UserStatus = 1, UnitId = user.UnitId.Value }, new Pagination() { });
                        if (objUserList.Data.Count >= 4)
                        {
                            obj.Message = "单位“系统管理员”不能超过4个！";
                            return obj;
                        }
                    }

                    entity.IsChangeRole = true;
                    entity.IsChangeDepartment = true;

                    //修改时验证单位是否一致
                    if (dbUserEntity.UnitId != user.UnitId)
                    {
                        obj.Message = "您无权修改其他单位用户信息！";
                        return obj;
                    }
                    //需要判断是否只有最后一个学校超管，只有一个学校超管不允许去除勾选学校超管
                    if (dbUserEntity.RoleIds.Contains(SchoolManager) && !model.RoleIds.Contains(SchoolManager))
                    {
                        TData<List<UserEntity>> objUserList =await GetUserList(new UserListParam() { RoleId = long.Parse(SchoolManager), UserStatus = 1, UnitId = user.UnitId.Value },new Pagination() { });
                        if (objUserList.Data.Count <= 1)
                        {
                            obj.Message = "请至少保留一个“系统管理员”！";
                            return obj;
                        }
                    }
                    if(dbUserEntity.RoleIds.Contains(CountyManager) && !model.RoleIds.Contains(CountyManager))
                    {
                        TData<List<UserEntity>> objUserList = await GetUserList(new UserListParam() { RoleId = long.Parse(CountyManager), UserStatus = 1, UnitId = user.UnitId.Value }, new Pagination() { });
                        if (objUserList.Data.Count <= 1)
                        {
                            obj.Message = "请至少保留一个“区县管理员”！";
                            return obj;
                        }
                    }
                    if (dbUserEntity.RoleIds.Contains(CityManager) && !model.RoleIds.Contains(CityManager))
                    {
                        TData<List<UserEntity>> objUserList = await GetUserList(new UserListParam() { RoleId = long.Parse(CityManager), UserStatus = 1, UnitId = user.UnitId.Value }, new Pagination() { });
                        if (objUserList.Data.Count <= 1)
                        {
                            obj.Message = "请至少保留一个“市级管理员”！";
                            return obj;
                        }
                    }

                    //验证手机号码必须唯一  zyf  20241120 根据需求修改
                    var userEntity = await userService.GetEntityByMobile(dbUserEntity.Id.Value,entity.Mobile);
                    if (userEntity != null)
                    {
                        obj.Message = "手机号码已经存在";
                        return obj;
                    }


                    if (!model.Password.Equals("NoChange"))
                    {
                        entity.Salt = GetPasswordSalt();
                        entity.Password = SecurityHelper.MD5ToHex(model.Password + entity.Salt).ToLower();
                    }
                    else
                    {
                        model.Password = null;
                    }

                    if (dbUserEntity.RoleIds != null)
                    {
                        if (!dbUserEntity.RoleIds.Equals(entity.RoleIds))
                        {
                            entity.IsChangeRole = true;
                        }
                    }
                    if (dbUserEntity.DepartmentIds != null)
                    {
                        if (!dbUserEntity.DepartmentIds.Equals(entity.DepartmentIds))
                        {
                            entity.IsChangeDepartment = true;
                        }
                    }

                }
            }
            if (!entity.Birthday.IsEmpty())
            {
                entity.Birthday = entity.Birthday.ParseToDateTime().ToString("yyyy-MM-dd");
            }
            await userService.SaveUserForm(entity);
            obj.Data = entity.Id.ParseToString();
            obj.Tag = 1;
            return obj;
        }

        /// <summary>
        /// 启用禁用
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<TData> UpdateStatuzFormJson(long id)
        {
            OperatorInfo user = await Operator.Instance.Current(ApiToken);
            TData obj = new TData();
            //角色Id
            string SystemSuperId = (RoleEnum.SystemSuper.ParseToInt() + 16508640061130100).ToString();
            string CityManager = (RoleEnum.CityManager.ParseToInt() + 16508640061130100).ToString();
            string CountyManager = (RoleEnum.CountyManager.ParseToInt() + 16508640061130100).ToString();
            string SchoolManager = (RoleEnum.SchoolManager.ParseToInt() + 16508640061130100).ToString();
            string ConfigManagerId = (RoleEnum.ConfigManager.ParseToInt() + 385804331967844300).ToString(); //配置超管 

            //验证只有超管才能启用禁用
            if (!user.RoleIds.Contains(SystemSuperId)
                && !user.RoleIds.Contains(CityManager)
                && !user.RoleIds.Contains(CountyManager)
                && !user.RoleIds.Contains(SchoolManager)
                && !user.RoleIds.Contains(ConfigManagerId)
                )
            {
                obj.Message = "只有系统超管、市级超管、区县超管、学校超管、配置超管才能启用禁用用户";
                return obj;
            }

            if (id == user.UserId)
            {
                obj.Message = "您不能启用/禁用自己的账号信息！";
                return obj;
            }
            UserEntity dbUserEntity = await userService.GetUserSingle(id);
            if (dbUserEntity != null)
            {
                if (dbUserEntity.UnitId != user.UnitId)
                {
                    obj.Message = "您无权修改其他单位用户信息！";
                    return obj;
                }

                if (dbUserEntity.UserStatus == StatusEnum.Yes.ParseToInt())
                {
                    if (dbUserEntity.RoleIds.Contains(SchoolManager))
                    {
                        //不能禁用最后一个超管账号
                        TData<List<UserEntity>> objUserList = await GetUserList(new UserListParam() { RoleId = long.Parse(SchoolManager), UserStatus = 1, UnitId = user.UnitId.Value }, new Pagination() { });
                        if (objUserList.Data.Count <= 1)
                        {
                            obj.Message = "请至少保留一个“系统管理员”！";
                            return obj;
                        }
                    }

                    if (dbUserEntity.RoleIds.Contains(CountyManager))
                    {
                        TData<List<UserEntity>> objUserList = await GetUserList(new UserListParam() { RoleId = long.Parse(CountyManager), UserStatus = 1, UnitId = user.UnitId.Value }, new Pagination() { });
                        if (objUserList.Data.Count <= 1)
                        {
                            obj.Message = "请至少保留一个“区县管理员”！";
                            return obj;
                        }
                    }

                    if (dbUserEntity.RoleIds.Contains(CityManager))
                    {
                        TData<List<UserEntity>> objUserList = await GetUserList(new UserListParam() { RoleId = long.Parse(CityManager), UserStatus = 1, UnitId = user.UnitId.Value }, new Pagination() { });
                        if (objUserList.Data.Count <= 1)
                        {
                            obj.Message = "请至少保留一个“市级管理员”！";
                            return obj;
                        }
                    }


                    dbUserEntity.UserStatus = StatusEnum.No.ParseToInt();
                }
                else
                {

                    //需要判断添加的超管是否超过2个
                    if (dbUserEntity.RoleIds.Contains(SchoolManager))
                    {
                        TData<List<UserEntity>> objUserList = await GetUserList(new UserListParam() { RoleId = long.Parse(SchoolManager), UserStatus = 1, UnitId = user.UnitId.Value }, new Pagination() { });
                        if (objUserList.Data.Count >= 2)
                        {
                            obj.Message = "启用失败！单位“系统管理员”不能超过2个！";
                            return obj;
                        }
                    }
                    dbUserEntity.UserStatus = StatusEnum.Yes.ParseToInt();
                }
                await userService.UpdateUser(dbUserEntity);
                obj.Tag = 1;
            }
            return obj;
        }

        /// <summary>
        /// 单条删除用户信息(删除用户已废除)
        /// </summary>
        /// <param name="id">id</param>
        /// <returns></returns>
        public async Task<TData> DeleteUserFormById(long id)
        {
            OperatorInfo user = await Operator.Instance.Current(ApiToken);
            TData obj = new TData();
            //1.验证删除的是否有其它单位的Id
            UserEntity dbUserEntity = await userService.GetUserSingle(id);
            if (dbUserEntity != null)
            {
                if (dbUserEntity.UnitId != user.UnitId)
                {
                    obj.Message = "您无权删除其他单位用户信息！";
                    return obj;
                }
                //2.验证用户是否已经使用

                //
                //await userService.DeleteSchoolForm(id.ToString());
                obj.Tag = 1;
            }
            return obj;
        }

        /// <summary>
        /// 批量删除用户信息(删除用户已废除)
        /// </summary>
        /// <param name="ids">用户Id集合</param>
        /// <returns></returns>
        public async Task<TData> DeleteUserForm(string ids)
        {
            OperatorInfo user = await Operator.Instance.Current(ApiToken);
            TData obj = new TData();
            if (string.IsNullOrEmpty(ids))
            {
                obj.Message = "参数不能为空";
                return obj;
            }
            if (ids.Contains(user.UserId.ToString()))
            {
                obj.Message = "您不能删除自己的账号信息";
                return obj;
            }
            List<long> listIds = new List<long>();
            foreach (long userId in TextHelper.SplitToArray<long>(ids, ','))
            {
                UserEntity dbUserEntity = await userService.GetUserSingle(userId);
                if (dbUserEntity != null)
                {
                    if (dbUserEntity.UnitId == user.UnitId)
                    {
                        listIds.Add(userId);
                    }
                }

            }
            //2.验证用户是否已经使用

            if (listIds.Count > 0)
            {
                //await userService.DeleteSchoolForm(string.Join(',', listIds));
            }
            await RemoveCacheById(ids);

            obj.Tag = 1;
            return obj;
        }

        /// <summary>
        /// 获取单个用户信息
        /// </summary>
        /// <param name="id">用户id</param>
        /// <returns></returns>
        public async Task<TData<UserEntity>> GetUserEntityById(long id)
        {
            OperatorInfo user = await Operator.Instance.Current(ApiToken);
            TData<UserEntity> obj = new TData<UserEntity>();
            //角色Id
            string SystemSuperId = (RoleEnum.SystemSuper.ParseToInt() + 16508640061130100).ToString();
            string CityManager = (RoleEnum.CityManager.ParseToInt() + 16508640061130100).ToString();
            string CountyManager = (RoleEnum.CountyManager.ParseToInt() + 16508640061130100).ToString();
            string SchoolManager = (RoleEnum.SchoolManager.ParseToInt() + 16508640061130100).ToString();
            if (user.RoleIds == null)
            {
                obj.Message = "非法操作";
                return obj;
            }
            
            obj.Data = await userService.GetUserSingle(id);
            obj.Tag = 1;
            return obj;
        }

        /// <summary>
        /// 获取单个用户信息(后加)
        /// </summary>
        /// <param name="id">用户id</param>
        /// <returns></returns>
        public async Task<TData<UserEntity>> GetEntityById(long id)
        {
            OperatorInfo user = await Operator.Instance.Current(ApiToken);
            TData<UserEntity> obj = new TData<UserEntity>();
            //角色Id
            //string SystemSuperId = (RoleEnum.SystemSuper.ParseToInt() + 16508640061130100).ToString();
            //string CityManager = (RoleEnum.CityManager.ParseToInt() + 16508640061130100).ToString();
            //string CountyManager = (RoleEnum.CountyManager.ParseToInt() + 16508640061130100).ToString();
            //string SchoolManager = (RoleEnum.SchoolManager.ParseToInt() + 16508640061130100).ToString();
            if (user.RoleIds == null)
            {
                obj.Message = "非法操作";
                return obj;
            }
            //if (!string.IsNullOrEmpty(user.RoleIds))
            //{
            //    //验证只有超管才能启用禁用
            //    if (!user.RoleIds.Contains(SystemSuperId)
            //        && !user.RoleIds.Contains(CityManager)
            //        && !user.RoleIds.Contains(CountyManager)
            //        && !user.RoleIds.Contains(SchoolManager)
            //        )
            //    {
            //        obj.Message = "只有系统超管、市级超管、区县超管、学校超管才能查看单个用户信息";
            //        return obj;
            //    }
            //}
            obj.Data = await userService.GetUserSingle(id);
            obj.Tag = 1;
            return obj;
        }

        /// <summary>
        /// 获取单个用户信息,包含扩展的信息(后加)
        /// </summary>
        /// <param name="id">用户id</param>
        /// <returns></returns>
        public async Task<TData<UserEntity>> GetExtEntity(long id)
        {
            TData<UserEntity> obj = new TData<UserEntity>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            if (operatorinfo==null)
            {
                obj.Tag = -1;
                obj.Message = "登录失效。";
                return obj;
            }
            if (id == 0 && operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                id = operatorinfo.UserId.ParseToLong();
            }
            var entity = await userService.GetUserSingle(id);
            if (entity != null)
            {
                //获取扩展信息。
                var list = await userExtensionService.GetList(new UserExtensionListParam() { UserId = id, IsCurrentUnit = IsEnum.Yes.ParseToInt() });
                if (list != null && list.Count > 0)
                {
                    entity.JobTitle = list[0].JobTitle;
                    entity.EducationLevel = list[0].EducationLevel;
                    entity.IsExperimenter = list[0].IsExperimenter;
                    var experimenternature = "--";
                    if (list[0].ExperimenterNature == ExperimenterNatureEnum.FullTime.ParseToInt() || list[0].ExperimenterNature == ExperimenterNatureEnum.PartTime.ParseToInt())
                    {
                        experimenternature = ((ExperimenterNatureEnum)list[0].ExperimenterNature).GetDescription();
                    }
                    entity.ExperimenterNature = experimenternature;
                    entity.ExperimenterCard = list[0].ExperimenterCard;
                    if (entity.IsExperimenter == IsEnum.Yes.ParseToInt())
                    {
                        //获取证书文件。
                        var attachmentlist = await attachmentService.GetList(new Model.Param.BusinessManage.AttachmentListParam() { ObjectId = id, FileCategory = FileCategoryEnum.WorkLicense.ParseToInt() });
                        if (attachmentlist != null && attachmentlist.Count > 0)
                        {
                            entity.AttachmentList = attachmentlist;
                        }
                    }

                    entity.Major = list[0].Major;
                }
                obj.Data = entity;
                obj.Tag = 1;
            }
            else
            {
                obj.Tag = 0;
                obj.Message = "当前用户信息已不存在。";
            }
            return obj;
        } 
        /// <summary>
        /// 获取当前用户信息
        /// </summary>
        /// <returns></returns>
        public async Task<TData<UserEntity>> GetCurrentSchoolUserInfo()
        {
            OperatorInfo user = await Operator.Instance.Current(ApiToken);
            TData<UserEntity> obj = new TData<UserEntity>();
            obj.Data = await userService.GetUserSingle(user.UserId.Value);
            obj.Tag = 1;
            return obj;
        }
        #endregion

        #region 私有方法
        /// <summary>
        /// 密码MD5处理
        /// </summary>
        /// <param name="password"></param>
        /// <param name="salt"></param>
        /// <returns></returns>
        private string EncryptUserPassword(string password, string salt)
        {
            string md5Password = SecurityHelper.MD5ToHex(password);
            string encryptPassword = SecurityHelper.MD5ToHex(md5Password.ToLower() + salt).ToLower();
            return encryptPassword;
        }

        /// <summary>
        /// 密码盐
        /// </summary>
        /// <returns></returns>
        private string GetPasswordSalt()
        {
            return new Random().Next(1, 100000).ToString();
        }

        /// <summary>
        /// 移除缓存里面的token
        /// </summary>
        /// <param name="id"></param>
        private async Task RemoveCacheById(string ids)
        {
            foreach (long id in ids.Split(',').Select(p => long.Parse(p)))
            {
                await RemoveCacheById(id);
            }
        }

        private async Task RemoveCacheById(long id)
        {
            var dbEntity = await userService.GetEntity(id);
            if (dbEntity != null)
            {
                CacheFactory.Cache.RemoveCache(dbEntity.WebToken);
            }
        }

        /// <summary>
        /// 获取用户的职位和角色
        /// </summary>
        /// <param name="user"></param>
        private async Task GetUserBelong(UserEntity user)
        {
            if (user.OnlyContainParent)
            {
                var roleId = await roleService.GetSysByRoleId(500);
                if(roleId != 0)
                {
                    user.RoleIds = roleId.ToString();
                }
            }
            else
            {
                List<UserBelongEntity> userBelongList = await userBelongService.GetList(new UserBelongEntity { UserId = user.Id });

                List<UserBelongEntity> roleBelongList = userBelongList.Where(p => p.BelongType == UserBelongTypeEnum.Role.ParseToInt()).ToList();
                if (roleBelongList.Count > 0)
                {
                    user.RoleIds = string.Join(",", roleBelongList.Select(p => p.BelongId).ToList());
                }

                List<UserBelongEntity> positionBelongList = userBelongList.Where(p => p.BelongType == UserBelongTypeEnum.Position.ParseToInt()).ToList();
                if (positionBelongList.Count > 0)
                {
                    user.PositionIds = string.Join(",", positionBelongList.Select(p => p.BelongId).ToList());
                }
            }
        }

        /// <summary>
        /// 添加下属单位用户信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<TData<string>> SaveSchoolUserForm(UserInputModel model)
        {
            OperatorInfo user = await Operator.Instance.Current(ApiToken);
            TData<string> obj = new TData<string>();
            //角色Id
            string CityManager = (RoleEnum.CityManager.ParseToInt() + 16508640061130100).ToString();
            string CountyManager = (RoleEnum.CountyManager.ParseToInt() + 16508640061130100).ToString();
            if (user.RoleIds == null)
            {
                obj.Message = "非法操作";
                return obj;
            }

            //验证只有超管
            if (!user.RoleIds.Contains(CityManager)
                && !user.RoleIds.Contains(CountyManager)
                )
            {
                obj.Message = "只有市级超管、区县超管才能添加下属单位用户信息";
                return obj;
            }
            var entity = new UserEntity();
            entity.UnitId = model.UnitId;
            entity.Id = model.Id;
            entity.UserName = model.UserName;
            entity.RealName = model.RealName;
            entity.Gender = model.Gender;
            entity.Birthday = model.Birthday;
            entity.Email = model.Email;
            entity.Mobile = model.Mobile;
            entity.DepartmentIds = model.DepartmentIds;

            if (user.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                var roleList = await roleService.GetRoleListInUnitType(model.RoleIds, 3);
                if (roleList.Count > 0)
                {
                    obj.Message = "非法操作,您只能添加学校角色账号！";
                    return obj;
                }
                //entity.RoleIds = (RoleEnum.SchoolManager.ParseToInt() + 16508640061130100).ToString();
                entity.RoleIds = model.RoleIds;
            }
            else if (user.UnitType == UnitTypeEnum.City.ParseToInt())
            {
                entity.RoleIds = (RoleEnum.CountyManager.ParseToInt() + 16508640061130100).ToString();
            }

            entity.UserStatus = 1;
            entity.CreateUnitId = user.UnitId;
            if (userService.ExistUserName(entity))
            {
                obj.Message = "用户名已经存在！";
                return obj;
            }
            //验证是否已经添加超出2个超管
            List<UserEntity> listUser = new List<UserEntity>();
            UserListParam extensionParam = new UserListParam();
            extensionParam.UnitId = entity.UnitId;
            extensionParam.CreateUnitId = user.UnitId;
            extensionParam.Id = model.Id;
            
            if (user.UnitType == UnitTypeEnum.City.ParseToInt())
            {
                extensionParam.RoleId = RoleEnum.CountyManager.ParseToInt() + 16508640061130100;
                listUser = await userService.GetUserList(extensionParam);
                if (listUser.Count > 1)
                {
                    obj.Message = "每个单位只能添加2个超级管理员！";
                    return obj;
                }
            }
            else if(user.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                string checkRoleIds = "," + model.RoleIds + ",";
                long schoolManagerRoleId = RoleEnum.SchoolManager.ParseToInt() + 16508640061130100;
                if (checkRoleIds.Contains(","+ schoolManagerRoleId + ","))
                {
                    extensionParam.RoleId = schoolManagerRoleId;
                    listUser = await userService.GetUserList(extensionParam);
                    if (listUser.Count >= 4)
                    {
                        obj.Message = "每个单位只能添加4个超级管理员！";
                        return obj;
                    }
                }
            }
            if (entity.Id.IsNullOrZero())
            {
                //验证手机号码必须唯一  zyf  20241120 根据需求修改
                var userEntity = await userService.GetEntityByMobile(entity.Mobile);
                if (userEntity != null)
                {
                    obj.Message = "手机号码已经存在";
                    return obj;
                }

                entity.IsChangeRole = true;
                entity.Salt = GetPasswordSalt();
                entity.Password = SecurityHelper.MD5ToHex(model.Password + entity.Salt).ToLower();
            }
            else
            {

                //验证手机号码必须唯一  zyf  20241120 根据需求修改
                var userEntity = await userService.GetEntityByMobile(entity.Id.Value,entity.Mobile);
                if (userEntity != null)
                {
                    obj.Message = "手机号码已经存在";
                    return obj;
                }

                entity.IsChangeRole = true;
                UserEntity dbUserEntity = await userService.GetUserSingle(entity.Id.Value);
                if (dbUserEntity != null)
                {
                    //修改时验证单位是否一致
                    if (!model.Password.Equals("NoChange"))
                    {
                        entity.Salt = GetPasswordSalt();
                        entity.Password = SecurityHelper.MD5ToHex(model.Password + entity.Salt).ToLower();
                    }
                    else
                    {
                        model.Password = null;
                    }
                }
            }
            if (!entity.Birthday.IsEmpty())
            {
                entity.Birthday = entity.Birthday.ParseToDateTime().ToString("yyyy-MM-dd");
            }
            await userService.SaveChildUserForm(entity);
            obj.Data = entity.Id.ParseToString();
            obj.Tag = 1;
            return obj;
        }

        /// <summary>
        /// 区县启用禁用学校用户信息
        /// </summary>
        /// <param name="id">用户Id</param>
        /// <returns></returns>
        public async Task<TData> UpdateStatuzSchoolFormJson(long id)
        {
            OperatorInfo user = await Operator.Instance.Current(ApiToken);
            TData obj = new TData();
            if (id == user.UserId)
            {
                obj.Message = "您不能启用/禁用自己的账号信息";
                return obj;
            }
            UserEntity dbUserEntity = await userService.GetSuperUserSingle(id);
            if (dbUserEntity != null)
            {
                if (dbUserEntity.ParentUnitId != user.UnitId)
                {
                    obj.Message = "您无权修改其他区县用户信息！";
                    return obj;
                }
                if (dbUserEntity.UserStatus == StatusEnum.Yes.ParseToInt())
                {
                    dbUserEntity.UserStatus = StatusEnum.No.ParseToInt();
                }
                else
                {
                    dbUserEntity.UserStatus = StatusEnum.Yes.ParseToInt();
                }
                await userService.UpdateUser(dbUserEntity);
                obj.Tag = 1;
            }
            return obj;
        }


        /// <summary>
        /// 删除下属单位用户信息(删除用户已废除，调用删除方法注释)
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task<TData> DeleteSchoolUserForm(string ids)
        {
            TData obj = new TData();
            if (string.IsNullOrEmpty(ids))
            {
                obj.Message = "参数不能为空";
                return obj;
            }
            List<long> listIds = new List<long>();
            OperatorInfo user = await Operator.Instance.Current(ApiToken);
            if (ids.Contains(user.UserId.ToString()))
            {
                obj.Message = "您不能删除自己的账号信息";
                return obj;
            }

            foreach (long userId in TextHelper.SplitToArray<long>(ids, ','))
            {
                UserEntity dbUserEntity = await userService.GetSuperUserSingle(userId);
                if (dbUserEntity.ParentUnitId == user.UnitId)
                {
                    listIds.Add(userId);
                }
            }
            //1.验证用户是否已经使用

            if (listIds.Count > 0)
            {
                //await userService.DeleteSchoolForm(string.Join(',', listIds));
            }
            obj.Tag = 1;
            return obj;
        }


        /// <summary>
        /// 超管启用禁用
        /// </summary>
        /// <param name="id">用户Id</param>
        /// <returns></returns>
        public async Task<TData> UpdateStatuzBySystemSuper(long id)
        {
            OperatorInfo user = await Operator.Instance.Current(ApiToken);
            TData obj = new TData();
            //角色Id
            string SystemSuperId = (RoleEnum.SystemSuper.ParseToInt() + 16508640061130100).ToString();
            string ConfigManagerId = (RoleEnum.ConfigManager.ParseToInt() + 385804331967844300).ToString(); //配置超管 

            if (user.RoleIds == null)
            {
                obj.Message = "非法操作";
                return obj;
            }
            //验证只有超管才能启用禁用
            if (!user.RoleIds.Contains(SystemSuperId) && !user.RoleIds.Contains(ConfigManagerId))
            {
                obj.Message = "只有系统超管才能启用禁用用户";
                return obj;
            }
            if (id == user.UserId)
            {
                obj.Message = "您不能启用/禁用自己的账号信息";
                return obj;
            }
            UserEntity dbUserEntity = await userService.GetSuperUserSingle(id);
            if (dbUserEntity != null)
            {
                if (dbUserEntity.UserStatus == StatusEnum.Yes.ParseToInt())
                {
                    if (dbUserEntity.RoleIds.Contains(SystemSuperId))
                    {
                        TData<List<UserEntity>> objUserList = await GetUserList(new UserListParam() { RoleId = long.Parse(SystemSuperId), UserStatus = 1, UnitId = user.UnitId.Value }, new Pagination() { });
                        if (objUserList.Data.Count <= 1)
                        {
                            obj.Message = "请至少保留一个“运营超管”！";
                            return obj;
                        }
                    }
                    dbUserEntity.UserStatus = StatusEnum.No.ParseToInt();
                }
                else
                {
                    dbUserEntity.UserStatus = StatusEnum.Yes.ParseToInt();
                }
                await userService.UpdateUser(dbUserEntity);
                obj.Tag = 1;
            }
            return obj;
        }

        /// <summary>
        /// 超管删除用户信息(删除用户已废除，调用删除方法注释)
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task<TData> DeleteUserFormBySystemSuper(string ids)
        {
            TData obj = new TData();
            if (string.IsNullOrEmpty(ids))
            {
                obj.Message = "参数不能为空";
                return obj;
            }
            List<long> listIds = new List<long>();
            OperatorInfo user = await Operator.Instance.Current(ApiToken);
            if (user.IsSystem == 0)
            {
                obj.Message = "非法操作";
                return obj;
            }
            if (ids.Contains(user.UserId.ToString()))
            {
                obj.Message = "您不能删除自己的账号信息";
                return obj;
            }
            //1.验证用户是否已经使用


            //await userService.DeleteSchoolForm(ids);
            obj.Tag = 1;
            return obj;
        }

        /// <summary>
        /// 解锁用户
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<TData> UnlockUser(long id, string ip, string browser, string os, string userAgent)
        {
            TData obj = new TData();
            OperatorInfo user = await Operator.Instance.Current(ApiToken);
            //增加判断：学校管理员只能解锁自己单位的用户
            if (user.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                UserEntity userEntity = await userService.GetSuperUserSingle(id);
                if (userEntity != null)
                {
                    if(user.UnitId != userEntity.UnitId)
                    {
                        obj.Tag = 0;
                        obj.Message = "非法操作，你无权解锁其他单位用户。";
                        return obj;
                    }
                }
                else
                {
                    obj.Tag = 0;
                    obj.Message = "当前用户不存在。";
                    return obj;
                }
            }

            //string ip = NetHelper.Ip;
            //string browser = NetHelper.Browser;
            //string os = NetHelper.GetOSVersion();
            //string userAgent = NetHelper.UserAgent;
            LogLoginEntity logLoginEntity = new LogLoginEntity
            {
                LogStatus = OperateStatusEnum.Success.ParseToInt(),
                Remark = "解锁成功",
                IpAddress = ip,
                IpLocation = IpLocationHelper.GetIpLocation(ip), 
                Browser = browser,
                OS = os,
                ExtraRemark = $"操作人：{user.UserId ?? 0}-{user.RealName}",
                BaseCreatorId = id
            };

            // 让底层不用获取HttpContext
            logLoginEntity.BaseCreatorId = logLoginEntity.BaseCreatorId ?? 0;

            await logLoginBLL.SaveForm(logLoginEntity);
            obj.Tag = 1;
            return obj;
        }

        /// <summary>
        /// 学校导入用户信息
        /// </summary>
        /// <param name="param"></param>
        /// <param name="list"></param>
        /// <returns></returns>
        public async Task<TData<List<UserEntity>>> ImportSchoolUser(ImportParam param, List<UserEntity> list)
        {
            TData<List<UserEntity>> obj = new TData<List<UserEntity>>();
            if (list.Any())
            {
                list.Reverse();
                OperatorInfo user = await Operator.Instance.Current(ApiToken);

                //验证不能存在重复手机号
                //先判断list数据是否有重复的项
                var listPhone = list.GroupBy(f => new
                {
                    f.Mobile
                })
                .Select(group => new UserEntity
                {
                    Mobile = group.Key.Mobile,
                    Total = group.Count()
                }).ToList();

                if (listPhone.Exists(f => f.Total > 1))
                {
                    var listTip = listPhone.Where(f => f.Total > 1).Select(f => "【" + f.Mobile + "】").ToList();
                    string errorMsg = string.Join(";", listTip);
                    obj.Message = $"导入的数据中{errorMsg}存在重复";
                    return obj;
                }
                List<UserEntity> listAll = await userService.GetListAll();
                var listCommon = listAll.Join(list, p1 => p1.Mobile, p2 => p2.Mobile, (p1, p2) =>
                   new UserEntity()
                   {
                       Id = p1.Id,
                       Mobile = p1.Mobile,
                   }).ToList();
                if (listCommon.Count > 0)
                {
                    string strMobile = string.Join(",", listCommon.Select(f => f.Mobile).Distinct());
                    obj.Message = $"导入的数据中手机号码：{strMobile}已经存在";
                    return obj;
                }


                int index = 2;
                string tipMsg = "";
                string SchoolManager = (RoleEnum.SchoolManager.ParseToInt() + 16508640061130100).ToString();
                List<UserEntity> listUserError = new List<UserEntity>();
                foreach (UserEntity entity in list)
                {
                    tipMsg = "";
                    bool isSuccess = true;
                    index++;
                    if (string.IsNullOrEmpty(entity.RealName))
                    {
                        tipMsg += "姓名不能为空；<br/>";
                        isSuccess = false;
                        //break;
                    }
                    else
                    {
                        entity.RealName = StringFilter.SearchSql(entity.RealName);
                    }


                    if (string.IsNullOrEmpty(entity.Mobile))
                    {
                        tipMsg += "手机号码不能为空；<br/>";
                        isSuccess = false;
                        //break;
                    }
                    else
                    {
                        entity.Mobile = StringFilter.SearchSql(entity.Mobile);
                    }

                    if (string.IsNullOrEmpty(entity.UserName))
                    {
                        tipMsg += "账号不能为空；<br/>";
                        isSuccess = false;
                        //break;
                    }
                    else
                    {
                        entity.UserName = StringFilter.SearchSql(entity.UserName);
                    }

                    if (string.IsNullOrEmpty(entity.Password))
                    {
                        tipMsg += "密码不能为空；<br/>";
                        isSuccess = false;
                        //break;
                    }
                    //else
                    //{
                    //    entity.Password = StringFilter.SearchSql(entity.Password);
                    //}

                    if (string.IsNullOrEmpty(entity.RoleNames))
                    {
                        tipMsg += "角色不能为空；<br/>";
                        isSuccess = false;
                        //break;
                    }
                    else
                    {
                        entity.RoleNames = StringFilter.SearchSql(entity.RoleNames);
                    }

                    if (!string.IsNullOrEmpty(entity.RealName) && entity.RealName.Length > 20)
                    {
                        tipMsg += "姓名长度不能大于20；<br/>";
                        isSuccess = false;
                        //break;
                    }
                    if (!string.IsNullOrEmpty(entity.UserName) && entity.UserName.Length > 20)
                    {
                        tipMsg += "账号长度不能大于20；<br/>";
                        isSuccess = false;
                        //break;
                    }
                    if (!string.IsNullOrEmpty(entity.Password) && entity.Password.Length < 6)
                    {
                        tipMsg += "密码至少为6位；<br/>";
                        isSuccess = false;
                        //break;
                    }
                    if (!string.IsNullOrEmpty(entity.Password) && entity.Password.Length > 20)
                    {
                        tipMsg += "密码长度不能大于20；<br/>";
                        isSuccess = false;
                        //break;
                    }
                    if (!string.IsNullOrEmpty(entity.Password) && !ValidatorHelper.IsUserPassWord(entity.Password))
                    {
                        tipMsg += "密码至少6位，必须包含大写字母，小写字母，数字，特殊字符四种中的三种；<br/>";
                        isSuccess = false;
                    }
                    if (!string.IsNullOrEmpty(entity.Mobile) && !ValidatorHelper.IsPhone(entity.Mobile))
                    {
                        tipMsg += "手机号码输入有误；<br/>";
                        isSuccess = false;
                        //break;
                    }
                    //判断用户名是否存在
                    if (!string.IsNullOrEmpty(entity.UserName) && userService.ExistUserName(entity))
                    {
                        tipMsg += "账号已经存在！<br/>";
                        isSuccess = false;
                        //break;
                    }
                    //判断角色名称是否存在
                    //RoleEntity role = new RoleEntity();
                    //role.RoleName = entity.RoleNames;
                    //role.UnitType = UnitTypeEnum.School.ParseToInt();
                    //if (!roleService.ExistRoleName(role))
                    //{
                    //    tipMsg += "角色名称不存在！<br/>";
                    //    isSuccess = false;

                    //    //break;
                    //}

                    if (!string.IsNullOrEmpty(entity.RoleNames))
                    {
                        //根据角色名称获取角色Id
                        //RoleListParam roleParam = new RoleListParam();
                        //roleParam.RoleName = entity.RoleNames;
                        RoleEntity roleEntity = await roleService.GetEntity(entity.RoleNames, user.UnitType);
                        if (roleEntity == null)
                        {
                            tipMsg += "角色名称不存在！<br/>";
                            isSuccess = false;

                            //break;
                        }
                        entity.RoleIds = roleEntity.Id.ToString();
                    }
                    else
                    {
                        tipMsg += "请指定角色！<br/>";
                        isSuccess = false;
                    }
                    entity.UnitId = user.UnitId;
                    entity.IsChangeRole = true;
                    entity.IsChangeDepartment = true;
                    entity.UserStatus = StatusEnum.Yes.ParseToInt();
                    entity.Salt = GetPasswordSalt();
                    entity.ShowPassWord = entity.Password;
                    entity.Password = EncryptUserPassword(entity.Password ?? "", entity.Salt);

                    if (!isSuccess)
                    {
                        entity.Number = index;
                        entity.ErrorMsg = tipMsg;
                        listUserError.Add(entity);
                    }
                }

                if (listUserError.Count == 0)
                {
                    //验证单位超管不能超过2个
                    var listSuper = list.Where(f => f.RoleIds.Contains(SchoolManager)).ToList();
                    int superCount = listSuper.Count;
                    if (superCount > 2)
                    {
                        obj.Message = "单位“系统管理员”不能超过2个！";
                        return obj;
                    }

                    if (superCount > 0)
                    {
                        TData<List<UserEntity>> objUserList = await GetUserList(new UserListParam() { RoleId = long.Parse(SchoolManager), UserStatus = 1, UnitId = user.UnitId.Value }, new Pagination() { });
                        superCount = superCount + objUserList.Data.Count;
                        if (superCount >= 2)
                        {
                            obj.Message = "单位“系统管理员”不能超过2个！";
                            return obj;
                        }
                    }


                    foreach (UserEntity entity in list)
                    {
                        await userService.SaveUserForm(entity);
                    }
                    obj.Tag = 1;
                }
                else
                {
                    obj.Message = tipMsg;
                    obj.Data = listUserError;
                }
            }
            else
            {
                obj.Message = " 未找到导入的数据";
            }
            return obj;
        }


        /// <summary>
        /// 区县、市级导入学校超管信息
        /// </summary>
        /// <param name="param"></param>
        /// <param name="list"></param>
        /// <returns></returns>
        public async Task<TData<List<UserEntity>>> ImportBranchSuperUser(ImportParam param, List<UserEntity> list)
        {
            TData<List<UserEntity>> obj = new TData<List<UserEntity>>();
            if (list.Any())
            {
                list.Reverse();
                bool isSuccess = true;
                var listGroup = list.GroupBy(x => new { x.UnitName }).Select(g => new { g.Key.UnitName, g.ToList().Count }).ToList();
                foreach (var i in listGroup)
                {
                    if (i.Count > 2)
                    {
                        isSuccess = false;
                        break;
                    }
                }

                if (!isSuccess)
                {
                    obj.Message = " 导入的数据中每个单位不能超过2个超管！";
                    return obj;
                }

                //验证不能存在重复手机号
                //先判断list数据是否有重复的项
                var listPhone = list.GroupBy(f => new
                {
                    f.Mobile
                })
                .Select(group => new UserEntity
                {
                    Mobile = group.Key.Mobile,
                    Total = group.Count()
                }).ToList();

                if (listPhone.Exists(f => f.Total > 1))
                {
                    var listTip = listPhone.Where(f => f.Total > 1).Select(f => "【" + f.Mobile + "】").ToList();
                    string errorMsg = string.Join(";", listTip);
                    obj.Message = $"导入的数据中{errorMsg}存在重复";
                    return obj;
                }
                List<UserEntity> listAll = await userService.GetListAll();


                var listCommon = listAll.Join(list, p1 => p1.Mobile, p2 => p2.Mobile, (p1, p2) =>
                   new UserEntity()
                   {
                       Id = p1.Id,
                       Mobile = p1.Mobile,
                   }).ToList();
                if (listCommon.Count > 0)
                {
                    string strMobile = string.Join(",", listCommon.Select(f => f.Mobile).Distinct());
                    obj.Message = $"导入的数据中手机号码：{strMobile}已经存在";
                    return obj;
                }

                List <UserEntity> listUserError = new List<UserEntity>();
                OperatorInfo user = await Operator.Instance.Current(ApiToken);
                string tipMsg = "";
                int index = 2;
                foreach (UserEntity entity in list)
                {
                    try
                    {
                        tipMsg = "";
                        isSuccess = true;
                        index++;
                        if (string.IsNullOrEmpty(entity.UnitName))
                        {
                            tipMsg += "单位名称不能为空；<br/>";
                            isSuccess = false;
                            //break;
                        }
                        else
                        {
                            entity.UnitName = StringFilter.SearchSql(entity.UnitName);
                        }

                        if (string.IsNullOrEmpty(entity.RealName))
                        {
                            tipMsg += "姓名不能为空；<br/>";
                            isSuccess = false;
                            //break;
                        }
                        else
                        {
                            entity.RealName = StringFilter.SearchSql(entity.RealName);
                        }


                        if (string.IsNullOrEmpty(entity.Mobile))
                        {
                            tipMsg += "手机号码不能为空；<br/>";
                            isSuccess = false;
                            //break;
                        }
                        else
                        {
                            entity.Mobile = StringFilter.SearchSql(entity.Mobile);
                            ////判断手机号码是否已经存在
                            //if(listAll.Exists(f=>f.Mobile == entity.Mobile))
                            //{
                            //    tipMsg += "手机号码已经存在；<br/>";
                            //    isSuccess = false;
                            //}
                        }

                        if (string.IsNullOrEmpty(entity.UserName))
                        {
                            tipMsg += "账号不能为空；<br/>";
                            isSuccess = false;
                            //break;
                        }
                        else
                        {
                            entity.UserName = StringFilter.SearchSql(entity.UserName);
                        }

                        if (string.IsNullOrEmpty(entity.Password))
                        {
                            tipMsg += "密码不能为空；<br/>";
                            isSuccess = false;
                            //break;
                        }
                        //else
                        //{
                        //    entity.Password = StringFilter.SearchSql(entity.Password);
                        //}


                        if (!string.IsNullOrEmpty(entity.RealName) && entity.RealName.Length > 20)
                        {
                            tipMsg += "姓名长度不能大于20；<br/>";
                            isSuccess = false;
                            //break;
                        }
                        if (!string.IsNullOrEmpty(entity.UserName) && entity.UserName.Length > 20)
                        {
                            tipMsg += "账号长度不能大于20；<br/>";
                            isSuccess = false;
                            //break;
                        }
                        if (!string.IsNullOrEmpty(entity.Password) && entity.Password.Length < 6)
                        {
                            tipMsg += "密码至少为6位；<br/>";
                            isSuccess = false;
                            //break;
                        }
                        if (!string.IsNullOrEmpty(entity.Password) && entity.Password.Length > 20)
                        {
                            tipMsg += "密码长度不能大于20；<br/>";
                            isSuccess = false;
                            //break;
                        }
                        if (!string.IsNullOrEmpty(entity.Password) && !ValidatorHelper.IsUserPassWord(entity.Password))
                        {
                            tipMsg += "密码至少6位，必须包含大写字母，小写字母，数字，特殊字符四种中的三种；<br/>";
                            isSuccess = false;
                        }
                        if (!string.IsNullOrEmpty(entity.Mobile) && !ValidatorHelper.IsPhone(entity.Mobile))
                        {
                            tipMsg += "手机号码输入有误；<br/>";
                            isSuccess = false;
                            //break;
                        }
                        //判断用户名是否存在
                        if (!string.IsNullOrEmpty(entity.UserName) && userService.ExistUserName(entity))
                        {
                            tipMsg += "账号已经存在！<br/>";
                            isSuccess = false;
                            //break;
                        }

                        long? unitId = 0;
                        //判断单位名称是否存在
                        if (!string.IsNullOrEmpty(entity.UnitName))
                        {
                            UnitEntity unitEntity = await unitService.GetEntity(entity.UnitName);
                            if (unitEntity == null)
                            {
                                tipMsg += "单位名称不存在；<br/>";
                                isSuccess = false;
                            }

                            if (unitEntity != null)
                            {
                                unitId = unitEntity.Id;
                                //判断该单位是否为创建人单位的下属单位
                                UnitRelationEntity unitRelationEntity = await unitRelationService.GetEntity(user.UnitId.Value, unitEntity.Id.Value, UnitRelationTypeEnum.Unit.ParseToInt());
                                if (unitRelationEntity == null)
                                {
                                    tipMsg += "单位不是您的下属单位；<br/>";
                                    isSuccess = false;
                                }

                                //验证是否已经添加超出2个超管
                                UserListParam extensionParam = new UserListParam();
                                extensionParam.UnitId = unitEntity.Id;
                                extensionParam.CreateUnitId = user.UnitId;
                                if (user.UnitType == UnitTypeEnum.County.ParseToInt())
                                {
                                    extensionParam.RoleId = RoleEnum.SchoolManager.ParseToInt() + 16508640061130100;
                                }
                                else if (user.UnitType == UnitTypeEnum.City.ParseToInt())
                                {
                                    extensionParam.RoleId = RoleEnum.CountyManager.ParseToInt() + 16508640061130100;
                                }                        
                                //TData<List<UserEntity>> objExtension = new TData<List<UserEntity>>();
                                //objExtension.Data = await userService.GetUserList(extensionParam);
                                List<UserEntity> listUser = await userService.GetUserList(extensionParam);

                                int totalCount = listUser.Count;
                                var objGroup = listGroup.Where(a => a.UnitName == entity.UnitName).FirstOrDefault();
                                if (objGroup != null)
                                {
                                    totalCount += objGroup.Count;
                                }
                                if (totalCount > 2)
                                {
                                    tipMsg += $"单位“{unitEntity.Name}”只能添加2个超级管理员,目前已有“{listUser.Count}”个账号！<br/>";
                                    isSuccess = false;
                                }



                            }
                        }

                        entity.UnitId = unitId;
                        entity.IsChangeRole = true;
                        entity.UserStatus = StatusEnum.Yes.ParseToInt();
                        entity.Salt = GetPasswordSalt();
                        entity.ShowPassWord = entity.Password;
                        entity.Password = EncryptUserPassword(entity.Password ?? "", entity.Salt);
                        if (user.UnitType == UnitTypeEnum.County.ParseToInt())
                        {
                            entity.RoleIds = (RoleEnum.SchoolManager.ParseToInt() + 16508640061130100).ToString();
                        }
                        else if (user.UnitType == UnitTypeEnum.City.ParseToInt())
                        {
                            entity.RoleIds = (RoleEnum.CountyManager.ParseToInt() + 16508640061130100).ToString();
                        }
                        entity.CreateUnitId = user.UnitId;

                        if (!isSuccess)
                        {
                            entity.Number = index;
                            entity.ErrorMsg = tipMsg;
                            listUserError.Add(entity);
                        }
                    }
                    catch (Exception ex)
                    {
                        throw new Exception("程序出现异常，异常信息为：" + ex.Message);
                    }
                }

                if (listUserError.Count == 0)
                {
                    foreach (UserEntity entity in list)
                    {
                        await userService.SaveChildUserForm(entity);
                    }
                    obj.Tag = 1;
                }
                else
                {
                    obj.Message = tipMsg;
                    obj.Data = listUserError;
                }
            }
            else
            {
                obj.Message = " 未找到导入的数据";
            }
            return obj;
        }

        /// <summary>
        /// 保存用户信息(个人信息模块)
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<TData<string>> SaveUserInfo(UserInfoInputModel model)
        {
            OperatorInfo user = await Operator.Instance.Current(ApiToken);
            TData<string> obj = new TData<string>();
            var entity = new UserEntity();
            entity.Id = user.UserId;
            entity.RealName = model.RealName;
            entity.Mobile = model.Mobile;
            entity.QQ = model.QQ;
            UserEntity dbUserEntity = await userService.GetUserSingle(entity.Id.Value);
            if (dbUserEntity != null)
            {
                await userService.ChangeUser(entity);
                obj.Data = entity.Id.ParseToString();
                obj.Tag = 1;
            }
            return obj;
        }

        /// <summary>
        /// 修改密码(个人信息模块)
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<TData<string>> SavePassWord(UserPswdInputModel model)
        {
            OperatorInfo user = await Operator.Instance.Current(ApiToken);
            TData<string> obj = new TData<string>();
            var entity = new UserEntity();
            entity.Id = user.UserId;
            UserEntity dbUserEntity = await userService.GetEntity(user.UserId.Value);
            if (dbUserEntity != null)
            {
                //先验证原密码是否正确
                string oldPswd = SecurityHelper.MD5ToHex(model.OldPswd + dbUserEntity.Salt).ToLower();
                if (!oldPswd.Equals(dbUserEntity.Password))
                {
                    obj.Message = "原始密码输入有误";
                    return obj;
                }
                entity.Salt = GetPasswordSalt();
                entity.Password = SecurityHelper.MD5ToHex(model.NewPswd + entity.Salt).ToLower();
                await userService.ChangeUser(entity);
                obj.Data = entity.Id.ParseToString();
                obj.Tag = 1;
            }
            return obj;
        }

        /// <summary>
        /// 超管导入学校超管信息
        /// </summary>
        /// <param name="param"></param>
        /// <param name="list"></param>
        /// <returns></returns>
        public async Task<TData<List<UserEntity>>> SuperImportUser(ImportParam param, List<UserEntity> list)
        {
            TData<List<UserEntity>> obj = new TData<List<UserEntity>>();
            if (list.Any())
            {
                list.Reverse();
                OperatorInfo user = await Operator.Instance.Current(ApiToken);

                //验证不能存在重复手机号
                //先判断list数据是否有重复的项
                var listPhone = list.GroupBy(f => new
                {
                    f.Mobile
                })
                .Select(group => new UserEntity
                {
                    Mobile = group.Key.Mobile,
                    Total = group.Count()
                }).ToList();

                if (listPhone.Exists(f => f.Total > 1))
                {
                    var listTip = listPhone.Where(f => f.Total > 1).Select(f => "【" + f.Mobile + "】").ToList();
                    string errorMsg = string.Join(";", listTip);
                    obj.Message = $"导入的数据中{errorMsg}存在重复";
                    return obj;
                }
                List<UserEntity> listAll = await userService.GetListAll();


                var listCommon = listAll.Join(list, p1 => p1.Mobile, p2 => p2.Mobile, (p1, p2) =>
                   new UserEntity()
                   {
                       Id = p1.Id,
                       Mobile = p1.Mobile,
                   }).ToList();
                if (listCommon.Count > 0)
                {
                    string strMobile = string.Join(",", listCommon.Select(f => f.Mobile).Distinct());
                    obj.Message = $"导入的数据中手机号码：{strMobile}已经存在";
                    return obj;
                }


                string tipMsg = "";
                int index = 2;
                List<UserEntity> listUserError = new List<UserEntity>();
                foreach (UserEntity entity in list)
                {
                    try
                    {
                        bool isSuccess = true;
                        tipMsg = "";
                        index++;
                        if (string.IsNullOrEmpty(entity.UnitName))
                        {
                            tipMsg += "单位名称不能为空；<br/>";
                            isSuccess = false;
                            //break;
                        }
                        else
                        {
                            entity.UnitName = StringFilter.SearchSql(entity.UnitName);
                        }

                        if (string.IsNullOrEmpty(entity.RealName))
                        {
                            tipMsg += "姓名不能为空；<br/>";
                            isSuccess = false;
                            //break;
                        }
                        else
                        {
                            entity.RealName = StringFilter.SearchSql(entity.RealName);
                        }

                        if (string.IsNullOrEmpty(entity.Mobile))
                        {
                            tipMsg += "手机号码不能为空；<br/>";
                            isSuccess = false;
                            //break;
                        }
                        else
                        {
                            entity.Mobile = StringFilter.SearchSql(entity.Mobile);
                        }

                        if (string.IsNullOrEmpty(entity.UserName))
                        {
                            tipMsg += "账号不能为空；<br/>";
                            isSuccess = false;
                            //break;
                        }
                        else
                        {
                            entity.UserName = StringFilter.SearchSql(entity.UserName);
                        }

                        if (string.IsNullOrEmpty(entity.Password))
                        {
                            tipMsg += "密码不能为空；<br/>";
                            isSuccess = false;
                            //break;
                        }
                        //{
                        //    entity.Password = StringFilter.SearchSql(entity.Password);
                        //}


                        if (!string.IsNullOrEmpty(entity.RealName) && entity.RealName.Length > 20)
                        {
                            tipMsg += "姓名长度不能大于20；<br/>";
                            isSuccess = false;
                            //break;
                        }
                        if (!string.IsNullOrEmpty(entity.UserName) && entity.UserName.Length > 20)
                        {
                            tipMsg += "账号长度不能大于20；<br/>";
                            isSuccess = false;
                            //break;
                        }
                        if (!string.IsNullOrEmpty(entity.Password) && entity.Password.Length < 6)
                        {
                            tipMsg += "密码至少为6位；<br/>";
                            isSuccess = false;
                            //break;
                        }
                        if (!string.IsNullOrEmpty(entity.Password) && entity.Password.Length > 20)
                        {
                            tipMsg += "密码长度不能大于20；<br/>";
                            isSuccess = false;
                            //break;
                        }
                        if (!string.IsNullOrEmpty(entity.Password) && !ValidatorHelper.IsUserPassWord(entity.Password))
                        {
                            tipMsg += "密码至少6位，必须包含大写字母，小写字母，数字，特殊字符四种中的三种；<br/>";
                            isSuccess = false;
                        }
                        if (!string.IsNullOrEmpty(entity.Mobile) && !ValidatorHelper.IsPhone(entity.Mobile))
                        {
                            tipMsg += "手机号码输入有误；<br/>";
                            isSuccess = false;
                            //break;
                        }
                        //判断用户名是否存在
                        if (!string.IsNullOrEmpty(entity.UserName) && userService.ExistUserName(entity))
                        {
                            tipMsg += "账号已经存在！<br/>";
                            isSuccess = false;
                            //break;
                        }
                        //判断角色名称是否存在
                        RoleEntity role = new RoleEntity();
                        role.RoleName = entity.RoleNames;
                        if (!roleService.ExistRoleName(role))
                        {
                            tipMsg += "角色名称不存在！<br/>";
                            isSuccess = false;
                            //break;
                        }

                        long? unitId = 0;
                        long? roleId = 0;
                        //判断单位名称是否存在
                        if (!string.IsNullOrEmpty(entity.UnitName))
                        {
                            RoleEntity roleEntity = null;
                            UnitEntity unitEntity = await unitService.GetEntity(entity.UnitName);
                            if (unitEntity == null)
                            {
                                tipMsg += "单位名称不存在；<br/>";
                                isSuccess = false;
                            }

                            if (!string.IsNullOrEmpty(entity.RoleNames))
                            {
                                //根据角色名称获取角色Id
                                RoleListParam roleParam = new RoleListParam();
                                roleParam.RoleName = entity.RoleNames;
                                roleEntity = await roleService.GetEntity(roleParam);
                                if (roleEntity == null)
                                {
                                    tipMsg += "角色名称不存在！<br/>";
                                    isSuccess = false;
                                }
                                else
                                {
                                    roleId = roleEntity.Id;
                                }
                            }

                            if (unitEntity != null && roleEntity != null)
                            {
                                //验证单位与角色是否对应
                                if (unitEntity.UnitType != roleEntity.UnitType)
                                {
                                    tipMsg += "当前单位类型不存在此角色！<br/>";
                                    isSuccess = false;
                                }

                                unitId = unitEntity.Id;
                            }

                        }

                        entity.UnitId = unitId;
                        entity.IsChangeRole = true;
                        entity.IsChangeDepartment = true;
                        entity.UserStatus = StatusEnum.Yes.ParseToInt();
                        entity.Salt = GetPasswordSalt();
                        entity.ShowPassWord = entity.Password;
                        entity.Password = EncryptUserPassword(entity.Password ?? "", entity.Salt);
                        entity.RoleIds = roleId.ToString();

                        if (!isSuccess)
                        {
                            entity.Number = index;
                            entity.ErrorMsg = tipMsg;
                            listUserError.Add(entity);
                        }
                    }
                    catch (Exception ex)
                    {
                        throw new Exception("程序出现异常，异常信息为：" + ex.Message);
                    }
                }

                if (listUserError.Count == 0)
                {
                    foreach (UserEntity entity in list)
                    {
                        await userService.SaveUserForm(entity);
                    }
                    obj.Tag = 1;
                }
                else
                {
                    obj.Message = tipMsg;
                    obj.Data = listUserError;
                }
            }
            else
            {
                obj.Message = " 未找到导入的数据";
            }
            return obj;
        }

        #endregion
    }
}
