﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.PaperExamineManage;
using Dqy.Syjx.Model.Param.PaperExamineManage;
using Dqy.Syjx.Enum.PaperExamineManage;

namespace Dqy.Syjx.Service.PaperExamineManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2022-08-22 11:11
    /// 描 述：考核清单服务类
    /// </summary>
    public class ExamineListService : RepositoryFactory
    {
        #region 获取数据
        public async Task<List<ExamineListEntity>> GetList(ExamineListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<ExamineListEntity>> GetPageList(ExamineListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<ExamineListEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<ExamineListEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(ExamineListEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(ExamineListEntity entity, Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update cp_ExamineList set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
                strSql = $"update cp_ExamineList set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }

        public async Task UpdateAccuracy(long id)
        {
            string strSql = $@"UPDATE  cp_ExamineList SET Accuracy =(CASE WHEN e2.ExamineNum > 0 AND p3.TotalQuestionNum > 0  THEN  CAST(e2.ExamineNum*1./p3.TotalQuestionNum as decimal(18,4)) ELSE 0 END)
            FROM  cp_ExamineList AS e1
            INNER JOIN  cp_Paper AS p3 ON e1.PaperId = p3.Id AND p3.BaseIsDelete = 0
            INNER JOIN (
            SELECT el1.ExamineListId , COUNT(Id) AS ExamineNum FROM  cp_ExamineListDetail	AS el1 WHERE el1.ExamineListId = {id} AND el1.IsTrue = {QuestionAnswer.True.ParseToInt()} AND el1.BaseIsDelete = 0  GROUP BY el1.ExamineListId  ) AS e2 ON e1.Id = e2.ExamineListId
            WHERE e1.BaseIsDelete = 0 AND e1.Id = {id} ";
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<ExamineListEntity, bool>> ListFilter(ExamineListParam param)
        {
            var expression = LinqExtensions.True<ExamineListEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }

                if (param.ExamineUserCode > 0)
                {
                    expression = expression.And(t => t.ExamineUserCode == param.ExamineUserCode.ToString());
                }
                if (param.PaperId > 0)
                {
                    expression = expression.And(t => t.PaperId == param.PaperId);
                }
                if (param.ExamineId>0)
                {
                    expression = expression.And(t => t.ExamineId == param.ExamineId);
                }
                if (param.SysUserId > 0)
                {
                    expression = expression.And(t => t.SysUserId == param.SysUserId);
                }
                if (!string.IsNullOrEmpty(param.Mobile))
                {
                    expression = expression.And(t => t.Mobile == param.Mobile);
                }
                if (!string.IsNullOrEmpty(param.RealName))
                {
                    expression = expression.And(t => t.RealName == param.RealName);
                }
            }
            return expression;
        }
        #endregion
    }
}
