﻿using System;
using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations.Schema;
using Dqy.Syjx.Util;
using System.Collections.Generic;
using System.ComponentModel;
using Dqy.Syjx.Util.Extension;
using NPOI.SS.UserModel;

namespace Dqy.Syjx.Entity.BusinessManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-11-01 09:27
    /// 描 述：基本信息管理实体类
    /// </summary>
    [Table("bn_FunRoom")]
    public class FunRoomEntity : BaseExtensionEntity
    {
        /// <summary>
        /// 所在单位id
        /// </summary>
        /// <returns></returns>
        [JsonConverter(typeof(StringJsonConverter))]
        public long UnitId { get; set; }
        /// <summary>
        /// 适用学科（字典表sys_static_dictionary typecode=1005 字段：DictionaryId）
        /// </summary>
        /// <returns></returns>
        public int DictionaryId1005 { get; set; }
        /// <summary>
        /// 适用学段（字典表sys_static_dictionary  typecode=1002字段：DictionaryId）
        /// </summary>
        /// <returns></returns>
        public string SchoolStagez { get; set; }
        /// <summary>
        /// 一级分类（字典表sys_static_dictionary  typecode=1006 A字段：DictionaryId）
        /// </summary>
        /// <returns></returns>
        public int DictionaryId1006A { get; set; }
        /// <summary>
        /// 二级分类（字典表sys_static_dictionary  typecode=1006 字段：DictionaryId）
        /// </summary>
        /// <returns></returns>
        public int DictionaryId1006B { get; set; }
        /// <summary>
        /// 名称
        /// </summary>
        /// <returns></returns>
        [Description("实验（专用）室名称")]
        [ExportExcelAttribute(HorizontalAlignment.Left, 30)]
        public string Name { get; set; }
        /// <summary>
        /// 使用面积（㎡）
        /// </summary>
        /// <returns></returns>
        [Description("面积")]
        [ExportExcelAttribute(HorizontalAlignment.Right, 10)]
        public decimal UseArea { get; set; }
        /// <summary>
        /// 属性
        /// </summary>
        /// <returns></returns>
        public int RoomAttribute { get; set; }
        /// <summary>
        /// 座位数（个）
        /// </summary>
        /// <returns></returns>
        [Description("座位数")]
        [ExportExcelAttribute(HorizontalAlignment.Right, 10)]
        public int SeatNum { get; set; }
        /// <summary>
        /// 数字化
        /// </summary>
        /// <returns></returns>
        public int IsDigitalize { get; set; }
        /// <summary>
        /// 总投入金额
        /// </summary>
        public decimal? InvestmentAmount { get; set; }
        /// <summary>
        /// 管理部门
        /// </summary>
        /// <returns></returns>
        [JsonConverter(typeof(StringJsonConverter))]
        public long SysDepartmentId { get; set; }
        /// <summary>
        /// 管理人
        /// </summary>
        /// <returns></returns>
        [JsonConverter(typeof(StringJsonConverter))]
        public long SysUserId { get; set; }
        /// <summary>
        /// 起初建设时间
        /// </summary>
        /// <returns></returns>
        public DateTime? BuildTime { get; set; }
        /// <summary>
        /// 最新改造时间
        /// </summary>
        /// <returns></returns>
        public string ReformTime { get; set; }
        /// <summary>
        /// 地点
        /// </summary>
        /// <returns></returns>
        public long Address { get; set; }
        /// <summary>
        /// 全地点
        /// </summary>
        [NotMapped]
        public string AddressFull { get; set; }
        /// <summary>
        /// 状态(1：启用；2：禁用）
        /// </summary>
        /// <returns></returns>
        public int Statuz { get; set; }

        /// <summary>
        /// 填报状态
        /// </summary>
        public int FillInStatuz { get; set; }

        /// <summary>
        /// 二维码编码
        /// </summary>
        public string QRCode { get; set; }
        /// <summary>
        /// 维护人Id
        /// </summary>
        [JsonConverter(typeof(StringJsonConverter))]
        public long SafeguardUserId { get; set; }

        public int? UploadBriefInfoNum { get; set; }
        public int? UploadSystemNum { get; set; }
        public int? LaboratoryGroupNum { get; set; }
        [NotMapped]
        [Description("一级分类")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 15)]
        public string ClassNameA { get; set; }
        [NotMapped]
        [Description("二级分类")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 20)]
        public string ClassNameB { get; set; }
        [NotMapped]
        [Description("属性")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 15)]
        public string NatureName { get; set; }
        [NotMapped]
        [Description("适用学科")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 15)]
        public string SubjectName { get; set; }
        [NotMapped]
        public int SubjectId { get; set; }

        /// <summary>
        /// 管理部门
        /// </summary>
        [NotMapped]
        [Description("管理部门")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 30)]
        public string DepartmentName { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        [NotMapped]
        [Description("状态")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 10)]
        public string StrStatuz { get; set; }

        /// <summary>
        /// 管理人
        /// </summary>
        [Description("管理人")]
        [NotMapped]
        [ExportExcelAttribute(HorizontalAlignment.Center, 15)]
        public string RealName { get; set; }
        [NotMapped]
        public List<AttachmentEntity> AttachmentList { get; set; }
        [NotMapped]
        public string CupboardName { get; set; }
        [NotMapped]
        [JsonConverter(typeof(StringJsonConverter))]
        public long? CupboardId { get; set; }
        [NotMapped]
        public string RoomNatureName { get; set; }


        [Description("单位名称")]
        [NotMapped]
        [ExportExcelAttribute(HorizontalAlignment.Left, 30)]
        public string SchoolName { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [NotMapped]
        public int FunRoomNum { get; set; }
        /// <summary>
        /// 学段。
        /// </summary>
        [NotMapped]
        public string SchoolStageName { get; set; }

        /// <summary>
        /// 楼宇名称。
        /// </summary>
        [NotMapped]
        public string HouseName { get; set; }
        /// <summary>
        /// 房间名称。
        /// </summary>
        [Description("实验室地点")]
        [NotMapped]
        [ExportExcelAttribute(HorizontalAlignment.Left, 30)]
        public string RoomName { get; set; }

        [NotMapped]
        public string AddressName { get; set; }

        /// <summary>
        /// 摄像机名称
        /// </summary>
        [Description("摄像机名称")]
        [NotMapped]
        [ExportExcelAttribute(HorizontalAlignment.Left, 30)]
        public string SrcName { get; set; }

        /// <summary>
        /// 摄像机编号
        /// </summary>
        [Description("摄像机编号")]
        [NotMapped]
        [ExportExcelAttribute(HorizontalAlignment.Left, 30)]
        public string CameraIndexCode { get; set; }

        /// <summary>
        /// 区县Id
        /// </summary>
        [NotMapped]
        [JsonConverter(typeof(StringJsonConverter))]
        public long CountyId { get; set; }

        /// <summary>
        /// 区县名称
        /// </summary>
        [NotMapped]
        [Description("区县名称")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 30)]
        public string CountyName { get; set; }

        /// <summary>
        /// 市级Id
        /// </summary>
        [NotMapped]
        [JsonConverter(typeof(StringJsonConverter))]
        public long CityId { get; set; }

        /// <summary>
        /// 市级名称
        /// </summary>
        [NotMapped]
        public string CityName { get; set; }

        /// <summary>
        /// 使用面积总数
        /// </summary>
        [NotMapped]
        [Description("使用面积(㎡)")]
        [ExportExcelAttribute(HorizontalAlignment.Right, 15)]
        public decimal UseAreaTotal { get; set; }

        /// <summary>
        /// 实验(专用)室总数量
        /// </summary>
        [NotMapped]
        [Description("数量")]
        [ExportExcelAttribute(HorizontalAlignment.Right, 15)]
        public int FunRoomCount { get; set; }

        /// <summary>
        /// 行类型（-1：总计 ）
        /// </summary>
        [NotMapped]
        public int RowType { get; set; }

        /// <summary>
        /// 二维码图片二进制文件
        /// </summary>
        [NotMapped]
        public string QrCodeData { get; set; }
        /// <summary>
        /// 二维码图片内容地址
        /// </summary>
        [NotMapped]
        public string QrCodeUrl { get; set; }

        /// <summary>
        /// 摄像机状态（在线，不在线）
        /// </summary>
        [NotMapped]
        public int VideoStatuz { get; set; }

        /// <summary>
        /// 地点
        /// </summary>
        [NotMapped]
        public long AddressId { get; set; }

        /// <summary>
        /// 单位Logo
        /// </summary>
        [NotMapped]
        public string Logo { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        [NotMapped]
        public int? Sort { get; set; }

        /// <summary>
        /// 二维码图片内容地址
        /// </summary>
        [Description("仪器清单地址")]
        [NotMapped]
        [ExportExcelAttribute(HorizontalAlignment.Left, 50)]
        public string InstrumentListUrl { get; set; }

        /// <summary>
        /// 二维码图片内容地址
        /// </summary>
        [Description("实验室地址")]
        [NotMapped]
        [ExportExcelAttribute(HorizontalAlignment.Left, 50)]
        public string FunRoomUrl { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Description("备注")]
        [NotMapped]
        [ExportExcelAttribute(HorizontalAlignment.Left, 30)]
        public string Remark { get; set; }

        /// <summary>
        /// 序号
        /// </summary>
        [NotMapped]
        [Description("序号")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 7)]
        public string ROWNUM { get; set; }
    }
}
