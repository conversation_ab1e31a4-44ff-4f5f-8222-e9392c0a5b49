﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.ExperimentTeachManage;
using Dqy.Syjx.Model.Param.ExperimentTeachManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Enum.ExperimentTeachManage;

namespace Dqy.Syjx.Service.ExperimentTeachManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-12-09 13:09
    /// 描 述：服务类
    /// </summary>
    public class TextbookVersionDetailService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<TextbookVersionDetailEntity>> GetList(TextbookVersionDetailListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<TextbookVersionDetailEntity>> GetPageList(TextbookVersionDetailListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }
        /// <summary>
        /// 实验预约（实验目录选择，查询）
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<List<TextbookVersionDetailEntity>> GetSelectPageList(TextbookVersionDetailListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ListSelectFilter(param, strSql);
            var list = await this.BaseRepository().FindList<TextbookVersionDetailEntity>(strSql.ToString(), filter.ToArray(), pagination);
            return list.ToList();
        }

        public async Task<TextbookVersionDetailEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<TextbookVersionDetailEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(TextbookVersionDetailEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(TextbookVersionDetailEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update ex_TextbookVersionDetail set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
                strSql = $"update ex_TextbookVersionDetail set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }

        public async Task SetStatuzForm(string ids ,int statuz)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");

            // 使用参数化查询和跨数据库兼容的 IN 语法
            var parameters = new List<DbParameter>
            {
                DbParameterExtension.CreateDbParameter("@Statuz", statuz)
            };

            string strSql = $"UPDATE ex_TextbookVersionDetail SET Statuz = @Statuz WHERE Id IN ({ids}) AND BaseIsDelete = 0";
            await this.BaseRepository().ExecuteBySql(strSql, parameters.ToArray());
        }

        public async Task SetIsEvaluateForm(string ids ,int isEvaluate)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");

            // 使用参数化查询和跨数据库兼容的 IN 语法
            var parameters = new List<DbParameter>
            {
                DbParameterExtension.CreateDbParameter("@IsEvaluate", isEvaluate)
            };

            string strSql = $"UPDATE ex_TextbookVersionDetail SET IsEvaluate = @IsEvaluate WHERE Id IN ({ids}) AND BaseIsDelete = 0";
            await this.BaseRepository().ExecuteBySql(strSql, parameters.ToArray());
        }
        #endregion

        #region 私有方法
        private Expression<Func<TextbookVersionDetailEntity, bool>> ListFilter(TextbookVersionDetailListParam param)
        {
            var expression = LinqExtensions.True<TextbookVersionDetailEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (!param.TextbookVersionBaseId.IsNullOrZero())
                {
                    expression = expression.And(t => t.TextbookVersionBaseId == param.TextbookVersionBaseId);
                }
                if (!param.ExperimentType.IsNullOrZero())
                {
                    expression = expression.And(t => t.ExperimentType == param.ExperimentType);
                }
                if (!param.IsBase.IsNullOrZero())
                {
                    expression = expression.And(t => t.IsBase == param.IsBase);
                }
                if (!param.IsNeedDo.IsNullOrZero())
                {
                    expression = expression.And(t => t.IsNeedDo == param.IsNeedDo);
                }
                if (!param.IsEvaluate.IsNullOrZero())
                {
                    expression = expression.And(t => t.IsEvaluate == param.IsEvaluate);
                }
                if (!param.KeyWord.IsEmpty())
                {
                    expression = expression.And(t => t.ExperimentName.Contains(param.KeyWord.Trim())
                    || t.Chapter.Contains(param.KeyWord.Trim()));
                }
                if (!param.Statuz.IsNullOrZero())
                {
                    expression = expression.And(t => t.Statuz == param.Statuz);
                }
                if (param.TextbookVersionBaseIdList!=null && param.TextbookVersionBaseIdList.Count > 0)
                {
                    expression = expression.And(t => param.TextbookVersionBaseIdList.Contains(t.TextbookVersionBaseId.Value));
                }
            }
            return expression;
        }

        /// <summary>
        /// 实验预约、登记，实验目录选择列表查询
        /// 1：left join 预约表，已预约不可选择的做标记，查询了列表有条件学科，这个可以不加AND eb4.CourseId = {param.CourseId}
        /// 2：预约表查询条件验证1：同一实验室,同一班级，
        /// 3：预约表查询条件验证2：
        /// 4：同一时间，同一节次，同一实验室(这个验证，在选择室的时候就验证了，这里不处理。)
        /// 注：其他情况实验都可以选，时间和实验室提交验证，可以改，不用重新选实验。
        /// </summary>
        /// <param name="param"></param>
        /// <param name="strSql"></param>
        /// <returns></returns>
        private List<DbParameter> ListSelectFilter(TextbookVersionDetailListParam param, StringBuilder strSql)
        {
            strSql.Append($@"  SELECT * From (
                SELECT
				vd2.Id ,
				vd2.BaseIsDelete ,
				vd2.BaseCreateTime ,
				vd2.BaseModifyTime ,
				vd2.BaseCreatorId ,
				vd2.BaseModifierId ,
				vd2.BaseVersion ,
				vd2.TextbookVersionBaseId ,
				vd2.Chapter ,
				vd2.ExperimentCode ,
				vd2.ExperimentName ,
				vd2.ExperimentType ,
				vd2.IsNeedDo ,
				vd2.IsEvaluate ,
				vd2.IsBase ,
				vd2.EquipmentNeed ,
				vd2.MaterialNeed ,
				vd2.Remark ,
				vd2.Statuz ,
                vd2.ChapterSort,
				vb1.VersionName ,
				vc3.SchoolStage ,
				vc3.GradeId ,
				vc3.CourseId ,
				vc3.SchoolTerm ,
                vc3.Id AS TextbookVersionCurrentId ,
                CASE WHEN eb4.Id > 0 THEN 1 ELSE 0 END  AS IsSelected ,
	            1 AS SourcePath
		        FROM  ex_TextbookVersionDetail AS vd2
		        INNER JOIN  ex_TextbookVersionBase AS vb1 ON vd2.TextbookVersionBaseId = vb1.Id AND vb1.BaseIsDelete = 0 AND vb1.Statuz = {StatusEnum.Yes.ParseToInt()}
				INNER JOIN   ex_TextbookVersionCurrent AS vc3 ON vb1.Id = vc3.TextbookVersionBaseId AND vc3.BaseIsDelete = 0 AND vb1.Statuz = {StatusEnum.Yes.ParseToInt()}
                LEFT JOIN  ex_ExperimentBooking AS eb4 ON eb4.BaseIsDelete = 0 AND eb4.IsMain = 0  AND eb4.SchoolId = {param.SchoolId}
                                                        AND (vd2.Id = eb4.TextbookVersionDetailId AND eb4.CourseId =  {param.CourseId} AND eb4.SchoolYearStart = {param.SchoolYearStart} AND eb4.SchoolGradeClassId in ({param.SchoolGradeClassIdz}))
                WHERE vd2.Statuz = {StatusEnum.Yes.ParseToInt()} AND vc3.CountyIdz like '%{param.CountyId}%'

                UNION

				SELECT
				se21.Id ,
				se21.BaseIsDelete ,
				se21.BaseCreateTime ,
				se21.BaseModifyTime ,
				se21.BaseCreatorId ,
				se21.BaseModifierId ,
				se21.BaseVersion ,
				0 AS TextbookVersionBaseId,
				se21.Chapter ,
				se21.ExperimentCode ,
				se21.ExperimentName ,
				se21.ExperimentType ,
				se21.IsNeedDo ,
				-1 AS IsEvaluate ,
				-1 AS IsBase ,
				se21.EquipmentNeed ,
				se21.MaterialNeed ,
				se21.Remark ,
				se21.Statuz ,
				0 AS ChapterSort ,
				'校本实验' AS VersionName ,
				se21.SchoolStage ,
				se21.GradeId ,
				se21.CourseId ,
				se21.SchoolTerm ,
				0 AS TextbookVersionCurrentId ,
				CASE WHEN eb22.Id > 0 THEN 1 ELSE 0 END  AS IsSelected ,
				2 AS SourcePath
				FROM  ex_SchoolExperiment AS se21
				LEFT JOIN ex_ExperimentBooking  AS eb22 ON se21.Id = eb22.SchoolExperimentId
                                                        AND eb22.SourcePath = {SourcePathEnum.School.ParseToInt()}
                                                        AND se21.SchoolId = eb22.SchoolId
                                                        AND eb22.SchoolYearStart = {param.SchoolYearStart}
                                                        AND eb22.SchoolGradeClassId in ({param.SchoolGradeClassId})
				WHERE se21.SchoolId = {param.SchoolId}  AND se21.Statuz = {StatusEnum.Yes.ParseToInt()}

            ) as tb1 WHERE   1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = @BaseIsDelete ");
                parameter.Add(DbParameterExtension.CreateDbParameter("@BaseIsDelete", param.BaseIsDelete));

                if (param.IsNeedDo > 0)
                {
                    strSql.Append(" AND IsNeedDo = @IsNeedDo ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@IsNeedDo", param.IsNeedDo));
                }
                if (param.ExperimentType > 0)
                {
                    strSql.Append(" AND ExperimentType = @ExperimentType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ExperimentType", param.ExperimentType));
                }
                if (param.SourcePath > 0)
                {
                    strSql.Append(" AND SourcePath = @SourcePath ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SourcePath", param.SourcePath));
                }
                if (param.SourcePath == SourcePathEnum.Version.ParseToInt())
                {
                    if (param.TextbookVersionBaseId > 0)
                    {
                        strSql.Append(" AND TextbookVersionBaseId = @TextbookVersionBaseId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@TextbookVersionBaseId", param.TextbookVersionBaseId ?? 0));
                    }
                }
                if (!string.IsNullOrEmpty(param.Name))
                {
                    strSql.Append(" AND (VersionName like @Name OR ExperimentName like @Name OR Chapter like @Name ) ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Name", $"%{param.Name}%"));
                }

                if (param.GradeId > 0)
                {
                    strSql.Append(" AND GradeId = @GradeId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@GradeId", param.GradeId));
                }
                if (param.SchoolStage > 0)
                {
                    strSql.Append(" AND SchoolStage = @SchoolStage ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStage", param.SchoolStage));
                }
                if (param.CourseId > 0)
                {
                    strSql.Append(" AND CourseId = @CourseId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (param.SchoolTerm > 0)
                {
                    strSql.Append(" AND SchoolTerm = @SchoolTerm ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolTerm", param.SchoolTerm));
                }
            }
            return parameter;
        }
        #endregion
    }
}
