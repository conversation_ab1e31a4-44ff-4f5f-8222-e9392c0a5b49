﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.InstrumentManage;
using Dqy.Syjx.Model.Param.InstrumentManage;
using Dqy.Syjx.Enum.InstrumentManage;

namespace Dqy.Syjx.Service.InstrumentManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-11-26 14:52
    /// 描 述：服务类
    /// </summary>
    public class InstrumentOutListService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<InstrumentOutListEntity>> GetList(InstrumentOutListListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<InstrumentOutListEntity>> GetPageList(InstrumentOutListListParam param, Pagination pagination)
        {
            StringBuilder sql = new StringBuilder();
            var expression = ListFilter(param, sql);
            var list = await this.BaseRepository().FindList<InstrumentOutListEntity>(sql.ToString(), expression.ToArray(), pagination);
            return list.ToList();
        }

        public async Task<System.Data.DataTable> GetTotalRow(InstrumentOutListListParam param)
        {
            StringBuilder sql = new StringBuilder();
            var expression = ListFilter(param, sql , "ISNULL(SUM(OutNum),0) AS OutNum ,ISNULL(SUM(Sum),0) AS Sum");
            var list = await this.BaseRepository().FindTable(sql.ToString(), expression.ToArray());
            return list;
        }

        public async Task<InstrumentOutListEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<InstrumentOutListEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(InstrumentOutListEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(InstrumentOutListEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
               strSql = $" update eq_InstrumentOutList set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
               strSql = $"update eq_InstrumentOutList set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<InstrumentOutListEntity, bool>> ListFilter(InstrumentOutListListParam param)
        {
            var expression = LinqExtensions.True<InstrumentOutListEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (!param.SchoolInstrumentId.IsNullOrZero())
                {
                    expression = expression.And(t => t.SchoolInstrumentId == param.SchoolInstrumentId);
                }
            }
            return expression;
        }

        private List<DbParameter> ListFilter(InstrumentOutListListParam param, StringBuilder strSql ,string funSql = "*")
        {
            strSql.Append(@$" SELECT {funSql} From (
                               SELECT  A.Id ,A.BaseCreateTime ,A.BaseCreatorId ,
                                       A.SchoolId ,A.SchoolInstrumentId ,A.OutNum ,A.Content ,
                                       B.Code ,B.Name ,B.Model ,B.Price ,B.CupboardId ,B.UnitName ,B.StageId ,B.CourseId ,B.Course ,B.Floor ,
                                       U.RealName AS OutUserName ,ISNULL(D.Name,'') AS FunRoomName ,ISNULL(C.Name,'') AS CupboardName ,S.VarietyAttribute ,
                                       (A.OutNum * B.Price) AS Sum ,D.SafeguardUserId ,A.ReceiveUserId ,A.OutDate ,U2.RealName AS ReceiveUserName ,
                                        A.InitialOutNum ,A.BackNum ,S.IsDangerChemical
                               FROM  eq_InstrumentOutList AS A
                               INNER JOIN  eq_SchoolInstrument AS B ON A.SchoolInstrumentId = B.Id
                               INNER JOIN  SysUser AS U ON A.BaseCreatorId = U.Id
                               INNER JOIN  eq_InstrumentStandard AS S ON B.InstrumentStandardId = S.Id
                               LEFT JOIN  bn_FunRoom AS D ON B.FunRoomId = D.Id
                               LEFT JOIN  bn_Cupboard AS C ON B.CupboardId = C.Id
                               LEFT JOIN  SysUser AS U2 ON A.ReceiveUserId = U2.Id
                               WHERE A.BaseIsDelete = 0
                          ) as T WHERE  1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                //strSql.Append(new SchoolInstrumentService().GetUserSchoolStageSubjectSql(param.SchoolStageIdz, param.SubjectIdz));
                if (!param.SafeguardUserId.IsNullOrZero())
                {
                    strSql.Append(" AND SafeguardUserId = @SafeguardUserId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SafeguardUserId", param.SafeguardUserId));
                }
                if (!param.SchoolId.IsNullOrZero())
                {
                    strSql.Append(" AND SchoolId = @SchoolId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
                }
                if (!param.CourseId.IsNullOrZero() && param.CourseId > 0)
                {
                    strSql.Append($" AND CourseId = @CourseId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (param.OutUserId.HasValue && param.OutUserId > 0)
                {
                    strSql.Append(" AND BaseCreatorId = @OutUserId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@OutUserId", param.OutUserId));
                }
                if (!string.IsNullOrWhiteSpace(param.KeyWord))
                {
                    strSql.Append($" AND (Name LIKE '%{param.KeyWord.Trim()}%' OR Code LIKE '%{param.KeyWord.Trim()}%') ");
                }
                if (param.StartDate.HasValue)
                {
                    strSql.Append(" AND BaseCreateTime >= @StartDate");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@StartDate", param.StartDate));
                }
                if (param.EndDate.HasValue)
                {
                    strSql.Append(" AND BaseCreateTime <= @EndDate");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@EndDate", param.EndDate.Value.AddDays(1).AddSeconds(-1)));
                }
                if (!param.InstrumentAttribute.IsNullOrZero())
                {
                    if (param.InstrumentAttribute == 1)
                    {
                        //仪器
                        strSql.Append($" AND VarietyAttribute <> '{VarietyAttributeEnum.LowValueConsumable.ParseToInt()}' AND VarietyAttribute <> '{VarietyAttributeEnum.Reagent.ParseToInt()}' ");
                    }
                    else if (param.InstrumentAttribute == 2)
                    {
                        //耗材
                        strSql.Append($" AND (VarietyAttribute = '{VarietyAttributeEnum.LowValueConsumable.ParseToInt()}' OR VarietyAttribute = '{VarietyAttributeEnum.Reagent.ParseToInt()}')");
                    }
                }
                if (!param.ReceiveUserId.IsNullOrZero())
                {
                    strSql.Append(" AND ReceiveUserId = @ReceiveUserId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ReceiveUserId", param.ReceiveUserId));
                }
            }
            return parameter;
        }
        #endregion
    }
}
