﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.PersonManage;
using Dqy.Syjx.Model.Param.PersonManage;

namespace Dqy.Syjx.Service.PersonManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-10-18 13:44
    /// 描 述：服务类
    /// </summary>
    public class UserTrainInfoService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<UserTrainInfoEntity>> GetList(UserTrainInfoListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<UserTrainInfoEntity>> GetPageList(UserTrainInfoL
        }

        public async Task<List<UserTrainInfoEntity>> GetList(UserTrainInfoListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<UserTrainInfoEntity>> GetPageList(UserTrainInfoListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<UserTrainInfoEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<UserTrainInfoEntity>(id);
        }


        /// <summary>
        /// 培训信息查询列表
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<List<UserTrainInfoEntity>> GetTrainInfoList(UserTrainInfoListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            var parameter = new List<DbParameter>();
            strSql.Append(@"SELECT * FROM  (
                SELECT UTI.Id,UTI.UnitId,UTI.UserId,UTI.Name,UTI.SchoolHour,UTI.TrainDate,UTI.TrainAddresz,UTI.TrainInstitution
                ,UTI.TrainContent,UTI.Statuz,U.Name AS SchoolName,SU.RealName
                ,UR.UnitId AS CountyId ,u21.Name AS CountyName
                ,ur22.UnitId AS CityId
                ,se21.SchoolProp
                FROM  up_UserTrainInfo AS UTI
                INNER JOIN  up_Unit AS U ON UTI.UnitId = U.Id AND U.Statuz = 1 AND U.BaseIsDelete = 0
                LEFT JOIN up_SchoolExtension as se21 on U.Id = se21.UnitId AND se21.BaseIsDelete = 0
                INNER JOIN  SysUser AS SU ON UTI.UserId = SU.Id
                INNER JOIN  up_UnitRelation AS UR ON U.Id = UR.ExtensionObjId AND UR.ExtensionType = 3
                INNER JOIN  up_Unit AS u21 ON UR.UnitId = u21.Id AND u21.Statuz = 1 AND u21.BaseIsDelete = 0
                INNER JOIN  up_UnitRelation AS ur22 ON u21.Id = ur22.ExtensionObjId AND ur22.ExtensionType = 3 AND ur22.BaseIsDelete = 0
                WHERE UTI.BaseIsDelete = 0 AND UTI.IsValid = 1  
                ) as  T WHERE 1 = 1
            ");
            if (param != null)
            {
                if (param.UnitId > 0)
                {
                    strSql.Append(" AND UnitId = @UnitId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }

                if (!string.IsNullOrEmpty(param.TrainStartTime.ParseToString()))
                {
                    strSql.Append(" AND TrainDate >= @StartTime ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@StartTime", param.TrainStartTime));
                }
                if (!string.IsNullOrEmpty(param.TrainEndTime.ParseToString()))
                {
                    param.TrainEndTime = param.TrainEndTime.Value.Date.Add(new TimeSpan(23, 59, 59));
                    strSql.Append(" AND TrainDate <= @EndTime ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@EndTime", param.TrainEndTime));
                }
                if (param.Statuz > -1)
                {
                    strSql.Append(" AND Statuz = @Statuz ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Statuz", param.Statuz));
                }
                if (param.UserId > 0)
                {
                    strSql.Append(" AND UserId = @UserId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UserId", param.UserId));
                }
                if (param.CountyId > 0)
                {
                    strSql.Append(" AND CountyId = @CountyId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
                if (param.CityId > 0)
                {
                    strSql.Append(" AND CityId = @CityId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CityId", param.CityId));
                }
                if (!string.IsNullOrEmpty(param.UserName))
                {
                    strSql.Append($" AND RealName LIKE '%{param.UserName}%' ");
                }
                if (param.SchoolPropList != null && param.SchoolPropList.Count > 0)
                {
                    param.SchoolPropList = param.SchoolPropList.Distinct().ToList();
                    strSql.Append($" AND ({string.Join(" OR ", param.SchoolPropList.Select(m => string.Format(" SchoolProp = {0} ", m)))}) ");
                }
            }
            var list = await this.BaseRepository().FindList<UserTrainInfoEntity>(strSql.ToString(), parameter.ToArray(), pagination);


            var listSummary = await this.BaseRepository().FindList<UserTrainInfoEntity>(strSql.ToString(), parameter.ToArray());
            int summary = 0;
            if (listSummary != null && listSummary.Count() > 0)
            {
                summary = listSummary.Sum(m => m.SchoolHour ?? 0);
            }
            if (list!=null)
            {
                var templist = list.ToList();
                templist.Add(new UserTrainInfoEntity()
                {
                    SchoolHour = summary
                });
                return templist;
            }
            return list.ToList();
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(UserTrainInfoEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update up_UserTrainInfo set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
                strSql = $"update up_UserTrainInfo set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<UserTrainInfoEntity, bool>> ListFilter(UserTrainInfoListParam param)
        {
            var expression = LinqExtensions.True<UserTrainInfoEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if(param.UnitId > 0)
                {
                    expression = expression.And(t => t.UnitId == param.UnitId);
                }
                if (param.UserId > 0)
                {
                    expression = expression.And(t => t.UserId == param.UserId);
                }
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }

                if (param.Statuz > -1)
                {
                    expression = expression.And(t => t.Statuz == param.Statuz);
                }
                if (!string.IsNullOrEmpty(param.TrainStartTime.ParseToString()))
                {
                    expression = expression.And(t => t.TrainDate >= param.TrainStartTime);
                }
                if (!string.IsNullOrEmpty(param.TrainEndTime.ParseToString()))
                {
                    param.TrainEndTime = param.TrainEndTime.Value.Date.Add(new TimeSpan(23, 59, 59));
                    expression = expression.And(t => t.TrainDate <= param.TrainEndTime);
                }

            }
            return expression;
        }
        #endregion
    }
}

