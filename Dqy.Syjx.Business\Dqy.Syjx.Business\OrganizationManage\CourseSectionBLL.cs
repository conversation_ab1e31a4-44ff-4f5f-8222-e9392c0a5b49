﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Entity.OrganizationManage;
using Dqy.Syjx.Model.Param.OrganizationManage;
using Dqy.Syjx.Service.OrganizationManage;
using Dqy.Syjx.Web.Code;
using Dqy.Syjx.Service.SystemManage;
using Dqy.Syjx.Model.Param.SystemManage;
using Dqy.Syjx.Model.Input.OrganizationManage;
using Dqy.Syjx.Entity.SystemManage;
using NPOI.POIFS.FileSystem;
using Dqy.Syjx.Model.Result.OrganizationManage;
using Dynamitey.DynamicObjects;
using System.Collections;
using System.IO;
using Microsoft.VisualBasic;
using Newtonsoft.Json;
using Dqy.Syjx.Enum;
using Dqy.Syjx.IdGenerator;

namespace Dqy.Syjx.Business.OrganizationManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-09-24 10:32
    /// 描 述：业务类
    /// </summary>
    public class CourseSectionBLL
    {
        /// <summary>
        /// ApiToken值（仅WebApi端传入）
        /// </summary>
        public string ApiToken = string.Empty;

        private CourseSectionService courseSectionService = new CourseSectionService();
        private StaticDictionaryService staticDictionaryService = new StaticDictionaryService();
        private UnitScheduleService unitScheduleService = new UnitScheduleService();    

        #region 获取数据
        public async Task<TData<List<CourseSectionEntity>>> GetList(CourseSectionListParam param)
        {
            TData<List<CourseSectionEntity>> obj = new TData<List<CourseSectionEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            if (operatorinfo == null)
            {
                obj.Tag = -1;
                obj.Message = "登录超时！";
                return obj;
            }
            param.UnitId = operatorinfo.UnitId;
            obj.Data = await courseSectionService.GetCourseSectionList(param, new Pagination() { PageIndex = 1, PageSize = int.MaxValue, Sort = "BeginTime", SortType = "ASC" });
            obj.Total = obj.Data.Count;
            obj.Tag = 1;
            return obj;
        }
        /// <summary>
        /// 查询课程节次信息
        /// </summary>
        /// <param name="gradeId">年级Id</param>
        /// <param name="weekId">周Id</param>
        /// <returns></returns>
        public async Task<TData<List<CourseSectionEntity>>> GetCourseSectionList(int gradeId, int weekId)
        {
            TData<List<CourseSectionEntity>> obj = new TData<List<CourseSectionEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            obj.Data = await courseSectionService.GetCourseSectionList(operatorinfo.UnitId.Value, gradeId, weekId);
            obj.Total = obj.Data.Count;
            obj.Tag = 1;
            return obj;
        }
        
        /// <summary>
        /// 超管查询课程节次信息
        /// </summary>
        /// <returns></returns>
        public async Task<TData<List<CourseSectionEntity>>> GetCourseSuperSectionList()
        {
            TData<List<CourseSectionEntity>> obj = new TData<List<CourseSectionEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            obj.Data = await courseSectionService.GetCourseSectionList(0, 0, 0);
            obj.Total = obj.Data.Count;
            obj.Tag = 1;
            return obj;
        }

        /// <summary>
        /// 查询年级星期信息
        /// </summary>
        /// <param name="gradeId">年级Id</param>
        /// <param name="weekId">星期</param>
        /// <returns></returns>
        public async Task<TData<List<GradeModel>>> GetGradeWeekList(int gradeId, int weekId)
        {
            TData<List<GradeModel>> obj = new TData<List<GradeModel>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            //获取单位年级信息
            List<GradeModel> listGrade = await staticDictionaryService.GetUnitGradeList(operatorinfo.UnitId.Value);
            //获取星期信息
            List<WeekModel> listWeek = await staticDictionaryService.GetWeekList();
            foreach(GradeModel grade in listGrade)
            {
                if(grade.GradeId == gradeId)
                {
                    grade.WeekModels = listWeek.Where(a => a.WeekId != weekId).ToList();
                }
                else
                {
                    grade.WeekModels = listWeek;
                }
            }
            obj.Data = listGrade;
            obj.Total = obj.Data.Count;
            obj.Tag = 1;
            return obj;
        }

        /// <summary>
        /// 根据年级获取下拉框内容
        /// </summary>
        /// <param name="gradeId">年级Id</param>
        /// <returns></returns>
        public async Task<TData<List<CourseSelectModel>>> GetCourseSelectList(int gradeId,long unitid, string dtDate=null)
        {
            TData<List<CourseSelectModel>> obj = new TData<List<CourseSelectModel>>();
            int scheduleType = 0;
            int weekId = DateTime.Now.DayOfWeek.ParseToInt();
            var weekList =  await staticDictionaryService.GetList(new StaticDictionaryListParam() { TypeCode = DicTypeCodeEnum.Week.ParseToInt().ToString(), Nature = weekId,OptType= 6 });
            if (weekList!=null && weekList.Count > 0)
            {
                weekId = weekList.FirstOrDefault().DictionaryId ?? 0;
            }
            //根据单位、年级获取“单位作息时间设置表信息”
            UnitScheduleListParam scheduleParam = new UnitScheduleListParam();
            scheduleParam.UnitId = unitid;
            //scheduleParam.GradeId = gradeId;
            List <UnitScheduleEntity> listSchedule = await unitScheduleService.GetList(scheduleParam);
            if(listSchedule.Count == 0)
            {
                scheduleParam.UnitId = 0;
                //scheduleParam.GradeId = 0;
                listSchedule = await unitScheduleService.GetList(scheduleParam);
            }
            if (listSchedule.Count > 0)
            {
                string strBegin = listSchedule[0].BeginDate;
                string strEnd = listSchedule[0].EndDate;
                string djStrBegin = listSchedule[0].DjBeginTime;
                string dtStrEnd = listSchedule[0].DjEndTime;

                if (DateTime.Parse(strBegin) < DateTime.Parse(strEnd))
                {
                    strBegin = DateTime.Now.Year + "-" + strBegin;
                    strEnd = DateTime.Now.Year + "-" + strEnd;
                }
                else
                {
                    strBegin = DateTime.Now.AddYears(1).Year + "-" + strBegin;
                    strEnd = DateTime.Now.Year + "-" + strEnd;
                }

                if (DateTime.Parse(djStrBegin) < DateTime.Parse(dtStrEnd))
                {
                    djStrBegin = DateTime.Now.Year + "-" + djStrBegin;
                    dtStrEnd = DateTime.Now.Year + "-" + dtStrEnd;
                }
                else
                {
                    djStrBegin = DateTime.Now.Year + "-" + djStrBegin;
                    dtStrEnd = DateTime.Now.AddYears(1).Year + "-" + dtStrEnd;
                }

                if (DateTime.Now >= DateTime.Parse(strBegin) && DateTime.Now < DateTime.Parse(strEnd))
                {
                    scheduleType = 1;
                }
                else if (DateTime.Now >= DateTime.Parse(djStrBegin) && DateTime.Now < DateTime.Parse(dtStrEnd))
                {
                    scheduleType = 2;
                }
                if (!string.IsNullOrEmpty(dtDate))
                {
                    weekId = DateTime.Parse(dtDate).DayOfWeek.ParseToInt();
                    weekList = await staticDictionaryService.GetList(new StaticDictionaryListParam() { TypeCode = DicTypeCodeEnum.Week.ParseToInt().ToString(), Nature = weekId, OptType = 6 });
                    if (weekList != null && weekList.Count > 0)
                    {
                        weekId = weekList.FirstOrDefault().DictionaryId ?? 0;
                    }
                }
                obj.Data = await courseSectionService.GetCourseSelectList(scheduleType, unitid, gradeId, weekId);
                obj.Total = obj.Data.Count;
                obj.Tag = 1;
            }
            return obj;
        }
        #endregion

        #region 提交数据
          
        /// <summary>
        /// 批量保存
        /// </summary>
        /// <param name="list"></param>
        /// <param name="gradeId"></param>
        /// <param name="weekId"></param>
        /// <returns></returns>
        public async Task<TData<string>> SaveBatchForm(List<CourseSectionInputModel> list, int gradeId, int weekId)
        {
            TData<List<CourseSectionEntity>> listCourseSection = new TData<List<CourseSectionEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            var db = await courseSectionService.BaseRepository().BeginTrans();
            TData<string> obj = new TData<string>();
            try
            {
                //先判断该单位下是否有数据
                listCourseSection.Data = await courseSectionService.GetSectionList(operatorinfo.UnitId.Value, gradeId, weekId);
                if (listCourseSection.Data.Count == 0)
                {
                    //批量新增
                    foreach (CourseSectionInputModel model in list)
                    {
                        var entity = new CourseSectionEntity();
                        entity.Id = 0;
                        entity.DictionaryId = 0;
                        entity.RowIndex = model.RIndex;
                        entity.UnitId = operatorinfo.UnitId.Value;
                        entity.WeekId = weekId;
                        entity.GradeId = gradeId;
                        entity.BeginTime = model.BeginTime;
                        entity.EndTime = model.EndTime;
                        entity.DjBeginTime = model.DjBeginTime;
                        entity.DjEndTime = model.DjEndTime;
                        entity.Statuz = 1;
                        entity.Remark = "";
                        await courseSectionService.SaveTransForm(entity, db);
                    }

                    obj.Tag = 1;
                    await db.CommitTrans();
                }
                else
                {
                    List<int> listNew = list.Select(a => a.RIndex).ToList();
                    List<int> listOld = listCourseSection.Data.Select(a => a.RIndex).ToList();

                    //判断需要添加的数据
                    List<int> listAdd = Enumerable.Except(listNew, listOld).ToList();

                    //判断需要删除的数据
                    List<int> listDel = Enumerable.Except(listOld, listNew).ToList();

                    //判断需要更新的数据
                    List<int> listUp = listOld.Intersect(listNew).ToList();

                    //新增
                    if (listAdd.Count > 0)
                    {
                        foreach(int i in listAdd)
                        {
                            CourseSectionInputModel m = list.Where(a => a.RIndex == i).FirstOrDefault();
                            var entity = new CourseSectionEntity();
                            entity.Id = 0;
                            entity.DictionaryId = 0;
                            entity.RowIndex = m.RIndex;
                            entity.UnitId = operatorinfo.UnitId.Value;
                            entity.WeekId = weekId;
                            entity.GradeId = gradeId;
                            entity.BeginTime = m.BeginTime;
                            entity.EndTime = m.EndTime;
                            entity.DjBeginTime = m.DjBeginTime;
                            entity.DjEndTime = m.DjEndTime;
                            entity.Statuz = 1;
                            entity.Remark = "";
                            await courseSectionService.SaveTransForm(entity, db);
                        }
                    }

                    //删除
                    if (listDel.Count > 0)
                    {
                        List<CourseSectionEntity> listD = new List<CourseSectionEntity>();
                        foreach (int i in listDel)
                        {
                            CourseSectionEntity mOld = listCourseSection.Data.Where(a => a.RIndex == i).FirstOrDefault();
                            listD.Add(mOld);
                        }
                        string listIds = string.Join(",", listD.Select(a => a.Id));
                        await courseSectionService.DeleteTransForm(listIds,db);
                    }

                    //修改
                    if (listUp.Count > 0)
                    {
                        foreach (int i in listUp)
                        {
                            CourseSectionInputModel mNew = list.Where(a => a.RIndex == i).FirstOrDefault();
                            CourseSectionEntity mOld = listCourseSection.Data.Where(a => a.RIndex == i).FirstOrDefault();

                            mOld.BeginTime = mNew.BeginTime;
                            mOld.EndTime = mNew.EndTime;
                            mOld.DjBeginTime = mNew.DjBeginTime; 
                            mOld.DjEndTime = mNew.DjEndTime;

                            await courseSectionService.SaveTransForm(mOld, db);
                        }
                    }
                    obj.Tag = 1;
                    await db.CommitTrans();
                }
            }
            catch (Exception ex)
            {
                obj.Tag = 0;
                obj.Message = "执行异常，" + ex.Message;
                await db.RollbackTrans();
                throw new Exception("程序出现异常，异常信息为：" + ex.Message);
            }
            return obj;
        }

        /// <summary>
        /// 批量复制课程
        /// </summary>
        /// <param name="listGradeId">年级Id集合</param>
        /// <param name="listWeekId">星期Id集合</param>
        /// <param name="gradeId">年级Id</param>
        /// <param name="weekId">星期Id</param>
        /// <returns></returns>
        public async Task<TData<string>> SaveBatchCopyForm(List<int> listGradeId, List<int> listWeekId, int gradeId, int weekId)
        {
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            TData<string> obj = new TData<string>();
            //先查询该单位该年级该星期是否有数据
            List<CourseSectionEntity> listCurrent = await courseSectionService.GetSectionList(operatorinfo.UnitId.Value, gradeId, weekId);
            if (listCurrent.Count == 0)
            {
                obj.Tag = 0;
                obj.Message = "该年级星期下无数据可复制";
                return obj;
            }
            try
            {
                for(int i=0;i<listGradeId.Count;i++)
                {
                    //先判断该年级该星期下有无数据，没有直接新增
                    List<CourseSectionEntity> list = await courseSectionService.GetSectionList(operatorinfo.UnitId.Value, listGradeId[i], listWeekId[i]);
                    if (list.Count > 0)
                    {
                        string listIds = string.Join(",", list.Select(a => a.Id));
                        await courseSectionService.DeleteForm(listIds);
                    }

                    listCurrent.ForEach(a =>
                    {
                        a.Id = 0;
                        a.GradeId = listGradeId[i];
                        a.WeekId = listWeekId[i];
                    });

                    foreach (CourseSectionEntity cnt in listCurrent)
                    {
                        await courseSectionService.SaveForm(cnt);
                    }
                }
                obj.Tag = 1;
            }
            catch (Exception ex)
            {
                obj.Tag = 0;
                obj.Message = "执行异常，" + ex.Message;
                throw new Exception("程序出现异常，异常信息为：" + ex.Message);
            }
            return obj;
        }


        /// <summary>
        /// 超管配置课程节次
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        public async Task<TData<string>> SaveSuperBatchForm(List<CourseSectionInputModel> list)
        {
            TData<List<CourseSectionEntity>> listCourseSection = new TData<List<CourseSectionEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            TData<string> obj = new TData<string>();
            string strRoleValues = "," + operatorinfo.RoleValues + ",";
            if (!strRoleValues.Contains(",90,") && !strRoleValues.Contains(",91,"))
            {
                obj.Tag = 1;
                obj.Message = "非法操作！";
                return obj;
            }
            var db = await courseSectionService.BaseRepository().BeginTrans();
            try
            {
                //先判断该单位下是否有数据
                listCourseSection.Data = await courseSectionService.GetSectionList(0, 0, 0);
                if (listCourseSection.Data.Count == 0)
                {
                    //批量新增
                    foreach (CourseSectionInputModel model in list)
                    {
                        var entity = new CourseSectionEntity();
                        entity.Id = 0;
                        entity.DictionaryId = 0;
                        entity.RowIndex = model.RIndex;
                        entity.UnitId = 0;
                        entity.WeekId = 0;
                        entity.GradeId = 0;
                        entity.BeginTime = model.BeginTime;
                        entity.EndTime = model.EndTime;
                        entity.DjBeginTime = model.DjBeginTime;
                        entity.DjEndTime = model.DjEndTime;
                        entity.Statuz = 1;
                        entity.Remark = "";
                        await courseSectionService.SaveTransForm(entity, db);
                    }

                    obj.Tag = 1;
                    await db.CommitTrans();
                }
                else
                {
                    List<int> listNew = list.Select(a => a.RIndex).ToList();
                    List<int> listOld = listCourseSection.Data.Select(a => a.RIndex).ToList();

                    //判断需要添加的数据
                    List<int> listAdd = Enumerable.Except(listNew, listOld).ToList();

                    //判断需要删除的数据
                    List<int> listDel = Enumerable.Except(listOld, listNew).ToList();

                    //判断需要更新的数据
                    List<int> listUp = listOld.Intersect(listNew).ToList();

                    //新增
                    if (listAdd.Count > 0)
                    {
                        foreach (int i in listAdd)
                        {
                            CourseSectionInputModel m = list.Where(a => a.RIndex == i).FirstOrDefault();
                            var entity = new CourseSectionEntity();
                            entity.Id = 0;
                            entity.DictionaryId = 0;
                            entity.RowIndex = m.RIndex;
                            entity.UnitId = 0;
                            entity.WeekId = 0;
                            entity.GradeId = 0;
                            entity.BeginTime = m.BeginTime;
                            entity.EndTime = m.EndTime;
                            entity.DjBeginTime = m.DjBeginTime;
                            entity.DjEndTime = m.DjEndTime;
                            entity.Statuz = 1;
                            entity.Remark = "";
                            await courseSectionService.SaveTransForm(entity, db);
                        }
                    }

                    //删除
                    if (listDel.Count > 0)
                    {
                        List<CourseSectionEntity> listD = new List<CourseSectionEntity>();
                        foreach (int i in listDel)
                        {
                            CourseSectionEntity mOld = listCourseSection.Data.Where(a => a.RIndex == i).FirstOrDefault();
                            listD.Add(mOld);
                        }
                        string listIds = string.Join(",", listD.Select(a => a.Id));
                        await courseSectionService.DeleteForm(listIds);
                    }

                    //修改
                    if (listUp.Count > 0)
                    {
                        foreach (int i in listUp)
                        {
                            CourseSectionInputModel mNew = list.Where(a => a.RIndex == i).FirstOrDefault();
                            CourseSectionEntity mOld = listCourseSection.Data.Where(a => a.RIndex == i).FirstOrDefault();

                            mOld.BeginTime = mNew.BeginTime;
                            mOld.EndTime = mNew.EndTime;
                            mOld.DjBeginTime = mNew.DjBeginTime;
                            mOld.DjEndTime = mNew.DjEndTime;

                            await courseSectionService.SaveTransForm(mOld, db);
                        }
                    }
                    obj.Tag = 1;
                    await db.CommitTrans();
                }
            }
            catch (Exception ex)
            {
                obj.Tag = 0;
                obj.Message = "执行异常，" + ex.Message;
                await db.RollbackTrans();
                throw new Exception("程序出现异常，异常信息为：" + ex.Message);
            }
            return obj;
        }
        #endregion

        #region 私有方法
        #endregion
    }
}
