﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
namespace Dqy.Syjx.Util
{
    /// <summary>
    /// 算法
    /// </summary>
    public static class AlgorithmHelper
    {

        /// <summary>
        /// 除法：获取相除保留小数值
        /// </summary>
        /// <param name="dividend">被除数</param>
        /// <param name="divisor">除数</param>
        /// <param name="positionnum">保留小数位数，最大不超过4，默认2</param>
        /// <returns></returns>
        public static decimal Division(decimal dividend, decimal divisor, int positionnum = 2)
        {
            if (positionnum < 0)
            {
                positionnum = 2;
            }
            decimal val = 0;
            if (divisor > 0 && dividend > 0)
            {
                val = dividend / divisor;
            }
            return Math.Round(val, positionnum);
        }

        /// <summary>
        /// 除法：获取相除保留小数值
        /// </summary>
        /// <param name="dividend">被除数</param>
        /// <param name="divisor">除数</param>
        /// <param name="positionnum">保留小数位数，最大不超过4，默认2</param>
        /// <returns></returns>
        public static decimal Division(int dividend, decimal divisor, int positionnum = 2)
        {
            if (positionnum < 0)
            {
                positionnum = 2;
            }
            decimal val = 0;
            if (divisor > 0 && dividend > 0)
            {
                val = dividend / divisor;
            }
            return Math.Round(val, positionnum);
        }

        /// <summary>
        /// 除法：获取相除保留小数值
        /// </summary>
        /// <param name="dividend">被除数</param>
        /// <param name="divisor">除数</param>
        /// <param name="positionnum">保留小数位数，最大不超过4，默认2</param>
        /// <returns></returns>
        public static decimal Division(decimal dividend, int divisor, int positionnum = 2)
        {
            if (positionnum < 0)
            {
                positionnum = 2;
            }
            decimal val = 0;
            if (divisor > 0 && dividend > 0)
            {
                val = dividend / divisor;
            }
            return Math.Round(val, positionnum);
        }

        /// <summary>
        /// 除法：获取相除保留小数值
        /// </summary>
        /// <param name="dividend">被除数</param>
        /// <param name="divisor">除数</param>
        /// <param name="positionnum">保留小数位数，最大不超过4，默认2</param>
        /// <returns></returns>
        public static decimal Division(int dividend, int divisor, int positionnum = 2)
        {
            if (positionnum < 0)
            {
                positionnum = 2;
            }
            decimal val = 0;
            if (divisor > 0 && dividend > 0)
            {
                val = ((decimal)dividend) / divisor;
            }
            return Math.Round(val, positionnum);
        }

        /// <summary>
        /// 除法百分比：获取相除保留小数值
        /// </summary>
        /// <param name="dividend">被除数</param>
        /// <param name="divisor">除数</param>
        /// <param name="positionnum">保留小数位数，最大不超过4，默认2</param>
        /// <returns></returns>
        public static decimal Percentage(decimal dividend, decimal divisor, int positionnum = 2)
        {
            if (positionnum < 0)
            {
                positionnum = 2;
            }
            decimal val = 0;
            if (divisor > 0 && dividend > 0)
            {
                val = dividend / divisor;
                val = val * 100;
            }
            return Math.Round(val, positionnum);
        }

        /// <summary>
        /// 除法百分比：获取相除保留小数值
        /// </summary>
        /// <param name="dividend">被除数</param>
        /// <param name="divisor">除数</param>
        /// <param name="positionnum">保留小数位数，最大不超过4，默认2</param>
        /// <returns></returns>
        public static decimal Percentage(int dividend, decimal divisor, int positionnum = 2)
        {
            if (positionnum < 0)
            {
                positionnum = 2;
            }
            decimal val = 0;
            if (divisor > 0 && dividend > 0)
            {
                val = dividend / divisor;
                val = val * 100;
            }
            return Math.Round(val, positionnum);
        }

        /// <summary>
        /// 率：获取相除保留小数值，默认值100
        /// </summary>
        /// <param name="dividend">被除数</param>
        /// <param name="divisor">除数</param>
        /// <param name="positionnum">保留小数位数，最大不超过4，默认2</param>
        /// <returns></returns>
        public static decimal GetRatio(int dividend, int divisor, int decimalPlaces = 2,int defaultval=100)
        {
            decimalPlaces = Math.Clamp(decimalPlaces, 0, 4); 
            decimal val = defaultval;
            if (divisor > 0 && dividend > 0)
            {
                val = (decimal)dividend / divisor * 100;
            }
            return Math.Round(val, decimalPlaces);
        }

        /// <summary>
        /// 除法百分比：获取相除保留小数值
        /// </summary>
        /// <param name="dividend">被除数</param>
        /// <param name="divisor">除数</param>
        /// <param name="positionnum">保留小数位数，最大不超过4，默认2</param>
        /// <returns></returns>
        public static decimal Percentage(int dividend, int divisor, int positionnum = 2)
        {
            if (positionnum < 0)
            {
                positionnum = 2;
            }
            decimal val = 0;
            if (divisor > 0 && dividend > 0)
            {
                val = ((decimal)dividend) / divisor;
                val = val * 100;
            }
            return Math.Round(val, positionnum);
        }
    }
}
