﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>ZackEFCore.Batch.Dm_NET6</id>
    <version>6.6.3</version>
    <authors>ZackEFCore.Batch.Dm_NET6</authors>
    <projectUrl>https://github.com/yangzhongke/Zack.EFCore.Batch</projectUrl>
    <description>A Dm（达梦数据库）-specific version of Zack.EFCore.Batch.
Using this library, Entity Framework Core users can delete or update multiple records from a LINQ Query in a SQL statement without loading entities. This libary supports Entity Framework Core 5.0.</description>
    <repository url="https://github.com/yangzhongke/Zack.EFCore.Batch" />
    <dependencies>
      <group targetFramework="net6.0">
        <dependency id="Zack.EFCore.Batch_NET6" version="6.1.3" exclude="Build,Analyzers" />
        <dependency id="Microsoft.EntityFrameworkCore.Relational" version="6.0.19" exclude="Build,Analyzers" />
        <dependency id="dmdbms.Microsoft.EntityFrameworkCore.Dm" version="6.0.16.16649" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
  <files>
    <file src="D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Data\ZackEFCore.Batch.Dm\bin\WUZHONG\net6.0\ZackEFCore.Batch.Dm_NET6.dll" target="lib\net6.0\ZackEFCore.Batch.Dm_NET6.dll" />
  </files>
</package>