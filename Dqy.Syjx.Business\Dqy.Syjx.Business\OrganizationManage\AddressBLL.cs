﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Model.Input.OrganizationManage;
using Dqy.Syjx.Entity.OrganizationManage;
using Dqy.Syjx.Model.Param.OrganizationManage;
using Dqy.Syjx.Service.OrganizationManage;
using Dqy.Syjx.Web.Code;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Model.Param;
using Dqy.Syjx.Cache.Factory;
using Dqy.Syjx.Model.Result;
using Dqy.Syjx.Data.Repository;

namespace Dqy.Syjx.Business.OrganizationManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-09-29 17:19
    /// 描 述：地址管理业务类
    /// </summary>
    public class AddressBLL
    {
        /// <summary>
        /// ApiToken值（仅WebApi端传入）
        /// </summary>
        public string ApiToken = string.Empty;

        private AddressService addressService = new();
        private DepartmentService departmentService = new();
        private DepeartmentRelationService departmentRelationService = new();
        #region 获取数据
        public async Task<TData<List<AddressEntity>>> GetList(AddressListParam param)
        {
            TData<List<AddressEntity>> obj = new();
            OperatorInfo operatorInfo = await Operator.Instance.Current(ApiToken);
            param.CurrentUnitId = operatorInfo.UnitId;
            param.CurrentUnitType = operatorInfo.UnitType;
            obj.Data = await addressService.GetList(param);
            obj.Total = obj.Data.Count;
            obj.Tag = 1;
            return obj;
        }

        public async Task<TData<AddressEntity>> GetEntity(long id)
        {
            TData<AddressEntity> obj = new();
            obj.Data = await addressService.GetEntity(id);
            if (obj.Data != null)
            {
                //获取所属部门
                if (obj.Data.Pid > 0)
                {
                    var tempList = await departmentRelationService.GetList(new DepeartmentRelationListParam() { ExtensionType = DepartmentRelationTypeEnum.DepartRoom.ParseToInt(), ExtensionObjId = obj.Data.Id, UnitId = obj.Data.UnitId });
                    if (tempList != null && tempList.Count > 0)
                    {
                        if (tempList.FirstOrDefault().SysDepartmentId != null)
                        {
                            obj.Data.DepartmentIds = tempList.FirstOrDefault().SysDepartmentId.ToString();
                        }
                    }
                }
                obj.Tag = 1;
            }
            return obj;
        }

        public async Task<TData<List<ZtreeInfo>>> GetZtreeList(AddressListParam param)
        {
            OperatorInfo operatorInfo = await Operator.Instance.Current(ApiToken);
            param.CurrentUnitId = operatorInfo.UnitId;
            param.CurrentUnitType = operatorInfo.UnitType;
            var obj = new TData<List<ZtreeInfo>>();
            obj.Data = new List<ZtreeInfo>();
            List<AddressEntity> list = await addressService.GetList(param);
            foreach (AddressEntity item in list)
            {
                obj.Data.Add(new ZtreeInfo
                {
                    id = item.Id.ParseToLong(),
                    pId = item.Pid.ParseToLong(),
                    name = item.Name
                });
            }
            obj.Tag = 1;
            return obj;
        }

        /// <summary>
        /// 查询地址信息
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<TData<List<AddressEntity>>> GetAddressList(AddressListParam param)
        {
            OperatorInfo operatorInfo = await Operator.Instance.Current(ApiToken);
            TData<List<AddressEntity>> obj = new TData<List<AddressEntity>>();
            param.CurrentUnitId = operatorInfo.UnitId;
            obj.Data = await addressService.GetAddressList(param);
            obj.Total = obj.Data.Count;
            obj.Tag = 1;
            return obj;
        }
        #endregion

        #region 提交数据
        public async Task<TData<string>> SaveForm(AddressInputModel model)
        {
            TData<string> obj = new();
            Repository db = addressService.BaseRepository();
            try
            {
                OperatorInfo operatorInfo = await Operator.Instance.Current(ApiToken);
                AddressEntity entity = new();
                AddressListParam addressParam = new AddressListParam();
                addressParam.Name = model.Name;
                addressParam.Pid = model.Pid;
                addressParam.CurrentUnitId = operatorInfo.UnitId;
                addressParam.CurrentUnitType = operatorInfo.UnitType;
                addressParam.OptType = 1;
                if (model.Id > 0)
                {
                    addressParam.Id = model.Id ?? 0; 
                    entity = await addressService.GetEntity(model.Id.Value);
                    if (entity == null || entity.UnitId != operatorInfo.UnitId)
                    {
                        obj.Tag = 0;
                        obj.Message = "非法操作，此页无权。";
                        return obj;
                    }
                }
                else
                {
                    entity.UnitId = operatorInfo.UnitId;
                    entity.Statuz = StatusEnum.Yes.ParseToInt();
                }
                //验证名称重复
                var addressList = await addressService.GetList(addressParam);
                if (addressList != null && addressList.Count > 0)
                {
                    obj.Tag = 0;
                    obj.Message = "当前【楼宇（场地）】名称已存在，请更换名称。";
                    if (model.Pid > 0)
                    {
                        obj.Message = "当前【场所（室）】名称已存在，请更换名称。";
                    }
                    return obj;
                }
                entity.Name = model.Name;
                entity.AliasName = model.AliasName;
                entity.Pid = model.Pid;
                entity.Code = model.Code;
                entity.Address = model.Address;
                entity.Memo = model.Memo;
                entity.Depth = model.Depth;
                entity.Sort = model.Sort;
                entity.Path = model.Path;
                entity.AddressType = model.AddressType;
                await db.BeginTrans();
                await addressService.SaveTransForm(entity, db);
                obj.Data = entity.Id.ParseToString();
                //保存部门 
                long departmentid = 0;
                long.TryParse(model.DepartmentIds, out departmentid);
                if (departmentid > 0)
                {
                    //如果是1对多就要删除直接添加了。
                    DepeartmentRelationEntity departmentRelationEntity = null;
                    if (model.Id > 0)
                    {
                        var tempList = await departmentRelationService.GetList(new DepeartmentRelationListParam() { ExtensionType = DepartmentRelationTypeEnum.DepartRoom.ParseToInt(), ExtensionObjId = entity.Id, UnitId = operatorInfo.UnitId });
                        if (tempList != null && tempList.Count > 0)
                        {
                            departmentRelationEntity = tempList.FirstOrDefault();
                        }
                    }
                    if (departmentRelationEntity == null)
                    {
                        departmentRelationEntity = new();
                        departmentRelationEntity.UnitId = operatorInfo.UnitId;
                        departmentRelationEntity.ExtensionType = DepartmentRelationTypeEnum.DepartRoom.ParseToInt();
                        departmentRelationEntity.ExtensionObjId = entity.Id;
                    }
                    departmentRelationEntity.BaseVersion += 1;
                    departmentRelationEntity.SysDepartmentId = departmentid;
                    await departmentRelationService.SaveTransForm(departmentRelationEntity, db);
                }
                obj.Tag = 1;
                await db.CommitTrans();
            }
            catch (Exception ex)
            {
                obj.Tag = 0;
                obj.Message = "执行异常，请联系客服协助处理。";

                await db.RollbackTrans();
                throw new Exception("程序出现异常，异常信息为：" + ex.Message);
            }
            return obj;
        }

        public async Task<TData> SaveDepartmentForm(string ids, long departmentid)
        {
            TData obj = new();
            Repository db = addressService.BaseRepository();
            try
            {
                OperatorInfo operatorInfo = await Operator.Instance.Current(ApiToken);
                var departmentEntity = await departmentService.GetEntity(departmentid);
                if (departmentEntity == null || departmentEntity.UnitId != operatorInfo.UnitId)
                {
                    obj.Tag = 0;
                    obj.Message = "";
                    return obj;
                }

                var list = await addressService.GetList(new AddressListParam() { Ids = ids, CurrentUnitId = operatorInfo.UnitId, CurrentUnitType = operatorInfo.UnitType });
                ids = "";
                if (list != null && list.Count > 0)
                {
                    var departmentRelationList = await departmentRelationService.GetList(new DepeartmentRelationListParam() { ExtensionType = DepartmentRelationTypeEnum.DepartRoom.ParseToInt(), Ids = string.Join(',', list.Select(m => m.Id)), UnitId = operatorInfo.UnitId });
                    await db.BeginTrans();
                    foreach (var m in list)
                    {
                        //此处需增加校验是否满足删除条件
                        ids += "," + m.Id.Value;
                        DepeartmentRelationEntity departmentRelationEntity = null;
                        var tempList = departmentRelationList.Where(n => n.ExtensionObjId == m.Id).ToList();
                        if (tempList != null && tempList.Count > 0)
                        {
                            departmentRelationEntity = tempList.FirstOrDefault();
                        }
                        if (departmentRelationEntity == null)
                        {
                            departmentRelationEntity = new();
                            departmentRelationEntity.UnitId = operatorInfo.UnitId;
                            departmentRelationEntity.ExtensionType = DepartmentRelationTypeEnum.DepartRoom.ParseToInt();
                            departmentRelationEntity.ExtensionObjId = m.Id.Value;
                        }
                        departmentRelationEntity.BaseVersion += 1;
                        departmentRelationEntity.SysDepartmentId = departmentid;
                        await departmentRelationService.SaveForm(departmentRelationEntity);
                    }
                    obj.Tag = 1;
                    await db.CommitTrans();
                }
                else
                {
                    obj.Tag = 0;
                    obj.Message = "当前地址已不存在，请刷新重新操作。";
                }
            }
            catch (Exception ex)
            {
                obj.Tag = 0;
                obj.Message = "执行异常，请联系客服协助处理。";

                await db.RollbackTrans();
                throw new Exception("程序出现异常，异常信息为：" + ex.Message);
            }
            return obj;
        }
        public async Task<TData> DeleteForm(string ids)
        {
            TData obj = new();
            if (ids.IsEmpty())
            {
                return obj;
            }
            OperatorInfo operatorInfo = await Operator.Instance.Current(ApiToken);
            AddressListParam param = new() { Ids = ids, CurrentUnitId = operatorInfo.UnitId };
            var list = await addressService.GetList(param);
            ids = "";
            foreach (var m in list)
            {
                //此处需增加校验是否满足删除条件

                ids += ", " + m.Id.Value;
            }
            ids = ids.Substring(1);
            obj.Tag = 1;
            if (ids.Length > 1)
                await addressService.DeleteForm(ids);
            else
                obj.Tag = 0;
            return obj;
        }


        public async Task<TData> ImportAddress(ImportParam param, List<AddressEntity> list)
        {
            TData obj = new TData();
            OperatorInfo operatorInfo = await Operator.Instance.Current(ApiToken);
            if (list.Any() && list != null && list.Count > 0)
            {
                if (list.Where(m => string.IsNullOrEmpty(m.HouseName) && string.IsNullOrEmpty(m.Name)).Count() > 0)
                {
                    obj.Tag = 0;
                    obj.Message = "导入失败，楼宇和场所名称必须填写。";
                    return obj;
                }
                list = list.OrderBy(m => m.HouseName).ToList();
                string hoursname = "";
                long pid = 0;
                foreach (AddressEntity entity in list)
                {
                    if (entity.HouseName != null && entity.HouseName.Length > 0 && entity.Name != null && entity.Name.Length > 0)
                    {
                        if (hoursname != entity.HouseName)
                        {
                            AddressEntity houseEntity = new();
                            houseEntity.Depth = 0;
                            houseEntity.Sort = 9999;
                            houseEntity.Name = entity.HouseName;
                            houseEntity.UnitId = operatorInfo.UnitId;
                            await addressService.SaveForm(houseEntity);
                            pid = houseEntity.Id.Value;
                            hoursname = entity.HouseName;
                        }
                        entity.Depth = 1;
                        entity.Sort = 9999;
                        entity.Pid = pid;
                        entity.UnitId = operatorInfo.UnitId;
                        await addressService.SaveForm(entity);
                    }
                }
                obj.Tag = 1;
            }
            else
            {
                obj.Message = " 未找到导入的数据";
            }
            return obj;
        }
        private async Task RemoveCacheById(long id)
        {
            var dbEntity = await addressService.GetEntity(id);
            if (dbEntity != null)
            {
                CacheFactory.Cache.RemoveCache(dbEntity.Token);
            }
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task<TData> DeleteByIds(string ids)
        {
            TData obj = new();
            OperatorInfo operatorInfo = await Operator.Instance.Current(ApiToken);
            await addressService.DeleteByIds(ids, operatorInfo.UnitId.Value);
            obj.Tag = 1;
            return obj;
        }
        #endregion

        #region 私有方法
        #endregion
    }
}
