﻿using System;
using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations.Schema;
using Dqy.Syjx.Util;

namespace Dqy.Syjx.Entity.SystemManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2023-08-10 13:30
    /// 描 述：实体类
    /// </summary>
    [Table("sys_AppManage")]
    public class AppManageEntity : BaseExtensionEntity
    {
        /// <summary>
        /// 类型(1：PC端；2：移动端)
        /// </summary>
        /// <returns></returns>
        public int AppType { get; set; }
        /// <summary>
        /// 应用/平台名称
        /// </summary>
        /// <returns></returns>
        public string AppName { get; set; }
        /// <summary>
        /// AppKey
        /// </summary>
        /// <returns></returns>
        public string AppKey { get; set; }
        /// <summary>
        /// 应用描述
        /// </summary>
        /// <returns></returns>
        public string Memo { get; set; }
        /// <summary>
        /// AppSecret
        /// </summary>
        /// <returns></returns>
        public string AppSecret { get; set; }
        /// <summary>
        /// AngentId
        /// </summary>
        /// <returns></returns>
        public string AngentId { get; set; }
        /// <summary>
        /// Secret
        /// </summary>
        /// <returns></returns>
        public string Secret { get; set; }
        /// <summary>
        /// CorpId
        /// </summary>
        /// <returns></returns>
        public string CorpId { get; set; }
        /// <summary>
        /// CorpSecret
        /// </summary>
        /// <returns></returns>
        public string CorpSecret { get; set; }
        /// <summary>
        /// 单位类型
        /// </summary>
        /// <returns></returns>
        public int? UnitType { get; set; }
        /// <summary>
        /// 单位Id
        /// </summary>
        /// <returns></returns>
        [JsonConverter(typeof(StringJsonConverter))]
        public long? UnitId { get; set; }
        /// <summary>
        /// 应用编码（唯一字符串，自动生成）
        /// </summary>
        /// <returns></returns>
        public string AppCode { get; set; }
        /// <summary>
        /// ClientID
        /// </summary>
        /// <returns></returns>
        public string ClientId { get; set; }
        /// <summary>
        /// ClientSecret
        /// </summary>
        /// <returns></returns>
        public string ClientSecret { get; set; }
        /// <summary>
        /// 回调地址
        /// </summary>
        /// <returns></returns>
        public string CallBackUrl { get; set; }
        /// <summary>
        /// 统一身份认证接口地址
        /// </summary>
        /// <returns></returns>
        public string AuthHost { get; set; }

        /// <summary>
        /// 数据同步接口地址
        /// </summary>
        /// <returns></returns>
        public string DataHost { get; set; }

        /// <summary>
        /// 网站地址
        /// </summary>
        /// <returns></returns>
        public string WebHost { get; set; }

        /// <summary>
        /// 接口名称（例如：企业微信通讯录同步接口）
        /// </summary>
        /// <returns></returns>
        public string ApiName { get; set; }
        /// <summary>
        /// 接口Secret（说明：企业微信通讯录同步接口必须使用接口的Secret才可调用）
        /// </summary>
        /// <returns></returns>
        public string ApiSecret { get; set; }
        /// <summary>
        /// 生成的AppKey
        /// </summary>
        /// <returns></returns>
        public string GenerateAppKey { get; set; }
        /// <summary>
        /// 生成的AppSecret
        /// </summary>
        /// <returns></returns>
        public string GenerateAppSecret { get; set; }

        /// <summary>
        /// 密钥类型（1：密钥主表  2：第三方密钥表）
        /// </summary>
        public int IsMain { get; set; }
    }
}
