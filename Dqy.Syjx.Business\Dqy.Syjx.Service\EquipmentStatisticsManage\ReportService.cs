﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.EquipmentStatisticsManage;
using Dqy.Syjx.Model.Param.EquipmentStatisticsManage;

namespace Dqy.Syjx.Service.EquipmentStatisticsManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2024-01-29 10:50
    /// 描 述：上报主表服务类
    /// </summary>
    public class ReportService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<ReportEntity>> GetList(ReportListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<ReportEntity>> GetPageList(ReportListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<ReportEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<ReportEntity>(id);
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="schoolId"></param>
        /// <param name="reportDateId"></param>
        /// <returns></returns>
        public async Task<ReportEntity> GetEntity(long schoolId,long reportDateId)
        {
            return await this.BaseRepository().FindEntity<ReportEntity>(f => f.SchoolId == schoolId && f.ReportDateId == reportDateId);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(ReportEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(ReportEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task UpdateTransForm(ReportEntity entity,List<string> fields, Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                if (fields != null && fields.Count > 0)
                {
                    fields.Add("BaseModifierId");
                    fields.Add("BaseModifyTime");
                    fields.Add("BaseVersion");
                }
                if (fields != null && fields.Count > 0)
                {
                    await db.Update(entity, fields);
                }
                else
                {
                    //不存在更细所有
                    await db.Update(entity);
                }
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
               strSql = $" update zb_Report set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
               strSql = $"update zb_Report set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<ReportEntity, bool>> ListFilter(ReportListParam param)
        {
            var expression = LinqExtensions.True<ReportEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (param.ReportDateId>0)
                {
                    expression = expression.And(t => t.ReportDateId == param.ReportDateId);
                }
                if (param.SchoolId > 0)
                {
                    expression = expression.And(t => t.SchoolId == param.SchoolId);
                }
                if (param.IsCurrent > -1)
                {
                    expression = expression.And(t => t.IsCurrent == param.IsCurrent);
                }
            }
            return expression;
        }
        #endregion
    }
}
