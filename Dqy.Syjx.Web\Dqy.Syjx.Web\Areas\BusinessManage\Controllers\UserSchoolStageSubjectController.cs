﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using System.Web;
using Microsoft.AspNetCore.Mvc;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Entity;
using Dqy.Syjx.Model;
using Dqy.Syjx.Web.Controllers;
using Dqy.Syjx.Entity.BusinessManage;
using Dqy.Syjx.Model.Input.BusinessManage;
using Dqy.Syjx.Business.BusinessManage;
using Dqy.Syjx.Model.Param.BusinessManage;
using Dqy.Syjx.Web.Code;
using Dqy.Syjx.Entity.SystemManage;
using Dqy.Syjx.Entity.OrganizationManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Business.SystemManage;

namespace Dqy.Syjx.Web.Areas.BusinessManage.Controllers
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-10-12 16:43
    /// 描 述：控制器类
    /// </summary>
    [Area("BusinessManage")]
    public class UserSchoolStageSubjectController :  BaseController
    {
        private UserSchoolStageSubjectBLL userSchoolStageSubjectBLL = new UserSchoolStageSubjectBLL();
        private ConfigSetBLL configSetBLL = new ConfigSetBLL();

        #region 视图功能
        [AuthorizeFilter("business:userschoolstagesubject:view")]
        public ActionResult Index()
        {
            return View();
        }

        public ActionResult Form()
        {
            return View();
        }

        public ActionResult ChooseUserForm()
        {
            return View();
        }

        [AuthorizeFilter("business:userschoolstagesubject:view")]
        public ActionResult StageSubjectInput() 
        { 
            return View(); 
        }
        #endregion

        #region 获取数据
        [HttpGet]
        [AuthorizeFilter("business:userschoolstagesubject:search")]
        public async Task<ActionResult> GetListJson(UserSchoolStageSubjectListParam param)
        {
            TData<List<UserSchoolStageSubjectEntity>> obj = await userSchoolStageSubjectBLL.GetList(param);
            return Json(obj);
        }

        [HttpGet]
        [AuthorizeFilter("business:userschoolstagesubject:search")]
        public async Task<ActionResult> GetPageListJson(UserSchoolStageSubjectListParam param, Pagination pagination)
        {
            TData<List<UserSchoolStageSubjectEntity>> obj = await userSchoolStageSubjectBLL.GetPageList(param, pagination);
            return Json(obj);
        }

        [HttpGet]
        public async Task<ActionResult> GetFormJson(long id)
        {
            TData<UserSchoolStageSubjectEntity> obj = await userSchoolStageSubjectBLL.GetEntity(id);
            return Json(obj);
        }

        /// <summary>
        /// 获取个人管理学段
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> GetSchoolStageByUser(int? courseId)
        {
            TData<IEnumerable<StaticDictionaryEntity>> obj = await userSchoolStageSubjectBLL.GetSchoolStageByUser(courseId);
            return Json(obj);
        }

        /// <summary>
        /// 获取个人管理科目
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> GetSubjectByUser(int schoolStage)
        {
            var obj = await userSchoolStageSubjectBLL.GetSubjectByUser(schoolStage);
            obj.Data = obj.Data.ToList();
            return Json(obj);
        }


        /// <summary>
        /// 获取个人学科、学段信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> GetSchoolCourseByUser()
        {
            TData<List<StaticDictionaryEntity>> obj = await userSchoolStageSubjectBLL.GetSchoolCourseByUser();
            return Json(obj);
        }

        /// <summary>
        /// 获取实验员授权信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> GetStageSubjectList(UserSchoolStageSubjectListParam param)
        {
            TData<List<UserSchoolStageSubjectEntity>> obj = await userSchoolStageSubjectBLL.GetStageSubjectList(param);
            var list =await configSetBLL.GetListByTypeCode("1002_SYYXZ");
            if (list.Exists(f => f.ConfigValue.Equals("1")))
            {
                obj.Data.ForEach(f => f.Nature = 1);
            }
            return Json(obj);
        }

        /// <summary>
        /// 根据单位Id、用户Id、学校学段集合获取用户适用学科学段信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> GetUserSchoolStageSubject()
        {
            TData<List<StaticDictionaryEntity>> obj = await userSchoolStageSubjectBLL.GetUserSchoolStageSubject();
            return Json(obj);
        }

        [HttpGet]
        public async Task<ActionResult> GetSafeguardListJson(long excludeUserId,string subjectIdz)
        {
            long laboratoryId = (RoleEnum.LaboratoryManager.ParseToInt() + 16508640061130100);
            TData<List<UserEntity>> obj = await userSchoolStageSubjectBLL.GetUserChangeList(excludeUserId, laboratoryId, subjectIdz);
            return Json(obj);
        }
        #endregion

        #region 提交数据
        [HttpPost]
        [AuthorizeFilter("business:userschoolstagesubject:add,business:userschoolstagesubject:edit")]
        public async Task<ActionResult> SaveFormJson(UserSchoolStageSubjectInputModel model)
        {
            TData<string> obj = await userSchoolStageSubjectBLL.SaveForm(model);
            return Json(obj);
        }

        [HttpPost]
        [AuthorizeFilter("business:userschoolstagesubject:delete")]
        public async Task<ActionResult> DeleteFormJson(string ids)
        {
            TData obj = await userSchoolStageSubjectBLL.DeleteForm(ids);
            return Json(obj);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("business:userschoolstagesubject:add,business:userschoolstagesubject:edit")]
        public async Task<ActionResult> SaveStageSubjectFormJson(SchoolStageSubjectInputModel model)
        {
            TData<string> obj = await userSchoolStageSubjectBLL.SaveStageSubjectForm(model);
            return Json(obj);
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("business:userschoolstagesubject:delete")]
        public async Task<ActionResult> DeleteStageSubjectFormJson(long id)
        {
            TData obj = await userSchoolStageSubjectBLL.DeleteStageSubjectForm(id);
            return Json(obj);
        }
        
        #endregion
    }
}
