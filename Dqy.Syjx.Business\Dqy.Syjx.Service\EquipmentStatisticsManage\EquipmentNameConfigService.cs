﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.EquipmentStatisticsManage;
using Dqy.Syjx.Model.Param.EquipmentStatisticsManage;
using Org.BouncyCastle.Crypto;

namespace Dqy.Syjx.Service.EquipmentStatisticsManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2024-06-03 17:22
    /// 描 述：科创设备管理服务类
    /// </summary>
    public class EquipmentNameConfigService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<EquipmentNameConfigEntity>> GetList(EquipmentNameConfigListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<EquipmentNameConfigEntity>> GetPageList(EquipmentNameConfigListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<EquipmentNameConfigEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<EquipmentNameConfigEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(EquipmentNameConfigEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(EquipmentNameConfigEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
               strSql = $" update zb_EquipmentNameConfig set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
               strSql = $"update zb_EquipmentNameConfig set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
      
        #endregion

        #region 私有方法
        private Expression<Func<EquipmentNameConfigEntity, bool>> ListFilter(EquipmentNameConfigListParam param)
        {
            var expression = LinqExtensions.True<EquipmentNameConfigEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (param.UnitId > 0)
                {
                    expression = expression.And(t => t.UnitId == param.UnitId);
                }
                if (param.Statuz != -1)
                {
                    expression = expression.And(t => t.Statuz == param.Statuz);
                }
                if (!string.IsNullOrEmpty(param.Nameeq))
                {
                    expression = expression.And(t => t.Name == param.Nameeq);
                }
                if (!string.IsNullOrEmpty(param.Name))
                {
                    expression = expression.And(t => t.Name.Contains(param.Name));
                }
                if (!string.IsNullOrEmpty(param.Remark))
                {
                    expression = expression.And(t => t.Remark.Contains(param.Remark));
                }
                if (!string.IsNullOrEmpty(param.UnitName))
                {
                    expression = expression.And(t => t.UnitName.Contains(param.UnitName));
                }
            }
            return expression;
        }
        #endregion
    }
}
