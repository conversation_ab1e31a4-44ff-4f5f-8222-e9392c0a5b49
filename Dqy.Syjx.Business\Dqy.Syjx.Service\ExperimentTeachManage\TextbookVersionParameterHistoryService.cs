﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.ExperimentTeachManage;
using Dqy.Syjx.Model.Param.ExperimentTeachManage;

namespace Dqy.Syjx.Service.ExperimentTeachManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2024-05-22 16:57
    /// 描 述：历史参数服务类
    /// </summary>
    public class TextbookVersionParameterHistoryService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<TextbookVersionParameterHistoryEntity>> GetList(TextbookVersionListParameterListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<TextbookVersionParameterHistoryEntity>> GetPageList(TextbookVersionListParameterListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<TextbookVersionParameterHistoryEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<TextbookVersionParameterHistoryEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveAddForm(List<TextbookVersionParameterHistoryEntity> list)
        {
            if (list != null)
            {
                list.ForEach(async t => await t.Create());
            }
            await this.BaseRepository().Insert(list);
        }
        public async Task SaveForm(TextbookVersionParameterHistoryEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(TextbookVersionParameterHistoryEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update ex_TextbookVersionParameterHistory set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
                strSql = $"update ex_TextbookVersionParameterHistory set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<TextbookVersionParameterHistoryEntity, bool>> ListFilter(TextbookVersionListParameterListParam param)
        {
            var expression = LinqExtensions.True<TextbookVersionParameterHistoryEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.TextbookVersionParameterId));
                }
                if (!param.SchoolStage.IsNullOrZero())
                {
                    expression = expression.And(t => t.SchoolStage == param.SchoolStage);
                }
                if (!param.GradeId.IsNullOrZero())
                {
                    expression = expression.And(t => t.GradeId == param.GradeId);
                }
                if (!param.CourseId.IsNullOrZero())
                {
                    expression = expression.And(t => t.CourseId == param.CourseId);
                }
                if (!param.SchoolTerm.IsNullOrZero())
                {
                    expression = expression.And(t => t.SchoolTerm == param.SchoolTerm);
                }
                if (param.SchoolYearStart > 0)
                {
                    expression = expression.And(t => t.SchoolYearStart == param.SchoolYearStart);
                }
            }
            return expression;
        }
        #endregion
    }
}
