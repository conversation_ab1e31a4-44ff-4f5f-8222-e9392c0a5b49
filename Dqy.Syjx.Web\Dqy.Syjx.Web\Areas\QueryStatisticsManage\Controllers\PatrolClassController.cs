﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using System.Web;
using Microsoft.AspNetCore.Mvc;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Entity;
using Dqy.Syjx.Model;
using Dqy.Syjx.Web.Controllers;
using Dqy.Syjx.Entity.QueryStatisticsManage;
using Dqy.Syjx.Model.Input.QueryStatisticsManage;
using Dqy.Syjx.Business.QueryStatisticsManage;
using Dqy.Syjx.Model.Param.QueryStatisticsManage;
using Dqy.Syjx.Web.Code;
using Dqy.Syjx.Entity.BusinessManage;
using Dqy.Syjx.Model.Param.BusinessManage;
using Dqy.Syjx.Business.OrganizationManage;
using Dqy.Syjx.Business.ExperimentTeachManage;
using Dqy.Syjx.Model.Input.ExperimentTeachManage;
using Dqy.Syjx.Util.Extension;

namespace Dqy.Syjx.Web.Areas.QueryStatisticsManage.Controllers
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2022-05-19 14:08
    /// 描 述：控制器类
    /// </summary>
    [Area("QueryStatisticsManage")]
    public class PatrolClassController :  BaseController
    {
        private PatrolClassBLL patrolClassBLL = new PatrolClassBLL();
        private SchoolTermBLL schoolTermBLL = new SchoolTermBLL();
        private ExperimentAttendStaticBLL experimentAttendStaticBLL = new ExperimentAttendStaticBLL();
        #region 视图功能
        [AuthorizeFilter("querystatistics:patrolclass:view")]
        public async Task<ActionResult> PatrolClassIndex()
        {
            //初始化在线巡课数据
            //await InitData();
           
            //同步正常开课次数
            //await UpdateData();

            //获取单位类型
            OperatorInfo operatorinfo = await Operator.Instance.Current();
            ViewBag.UnitType = operatorinfo?.UnitType;

            return View();
        }

        [AuthorizeFilter("querystatistics:patrolclass:view")]
        public async Task<ActionResult> PatrolClassDetail()
        {
            //获取单位类型
            OperatorInfo operatorinfo = await Operator.Instance.Current();
            ViewBag.UnitType = operatorinfo?.UnitType;

            return View();
        }

        [AuthorizeFilter("querystatistics:patrolclass:view")]
        public async Task<ActionResult> PatrolVideo()
        {
            //var z = DateTime.Now.DayOfWeek.ParseToInt();
            //获取单位类型
            OperatorInfo operatorinfo = await Operator.Instance.Current();
            ViewBag.UnitType = operatorinfo?.UnitType;
            ViewBag.IsSystem = operatorinfo?.IsSystem;
            return View();
        }

        /// <summary>
        /// 海康视频预览
        /// </summary>
        /// <returns></returns>
        public ActionResult Preview()
        {
            return View();
        }

        /// <summary>
        /// 大华视频预览
        /// </summary>
        /// <returns></returns>
        public ActionResult DhPreview()
        {
            return View();
        }
        /// <summary>
        /// 正在上课
        /// </summary>
        /// <returns></returns>
        public async Task<ActionResult> PatrolFunroomVideo()
        {
            OperatorInfo operatorinfo = await Operator.Instance.Current();
            ViewBag.UnitType = operatorinfo?.UnitType;
            ViewBag.IsSystem = operatorinfo?.IsSystem;
            return View();
        }
        #endregion

        #region 获取数据
        /// <summary>
        /// 获取在线巡课的详细数据
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        [HttpGet]
        [AuthorizeFilter("querystatistics:patrolclass:view")]
        public async Task<ActionResult> GetDetailPageJson(PatrolClassListParam param, Pagination pagination)
        {
            TData<List<PatrolClassEntity>> obj = await patrolClassBLL.GetDetailPageList(param, pagination);
            return Json(obj);
        }

        [HttpGet]
        [AuthorizeFilter("querystatistics:patrolclass:view")]
        public async Task<ActionResult> GetListJson(PatrolClassListParam param)
        {
            TData<List<PatrolClassEntity>> obj = await patrolClassBLL.GetList(param);
            return Json(obj);
        }

        [HttpGet]
        [AuthorizeFilter("querystatistics:patrolclass:view")]
        public async Task<ActionResult> GetRoomVideoJson(FunRoomListParam param, Pagination pagination)
        {
            TData<List<FunRoomEntity>> obj = await patrolClassBLL.GetRoomVideo(param, pagination);
            return Json(obj);
        }

        [HttpGet]
        [AuthorizeFilter("querystatistics:patrolclass:view")]
        public async Task<ActionResult> GetStatisticsJson(PatrolClassListParam param, Pagination pagination)
        {
            TData<List<PatrolClassEntity>> obj = await patrolClassBLL.GetStatistics(param,pagination);
            return Json(obj);
        }

        [HttpGet]
        public async Task<ActionResult> GetFormJson(long id)
        {
            TData<PatrolClassEntity> obj = await patrolClassBLL.GetEntity(id);
            return Json(obj);
        }
        /// <summary>
        /// 正在上课获取json数据
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        [HttpGet]
        [AuthorizeFilter("querystatistics:patrolclass:view")]
        public async Task<ActionResult> GetFunroomVideoJson(FunRoomListParam param, Pagination pagination)
        {
            TData<List<FunRoomEntity>> obj = await patrolClassBLL.GetFunroomVideo(param, pagination);
            return Json(obj);
        }
        #endregion

        #region 提交数据
        [HttpPost]
        [AuthorizeFilter("querystatistics:patrolclass:add,querystatistics:patrolclass:edit")]
        public async Task<ActionResult> SaveFormJson(PatrolClassInputModel model)
        {
            TData<string> obj = await patrolClassBLL.SaveForm(model);
            return Json(obj);
        }

        [HttpPost]
        [AuthorizeFilter("querystatistics:patrolclass:delete")]
        public async Task<ActionResult> DeleteFormJson(string ids)
        {
            TData obj = await patrolClassBLL.DeleteForm(ids);
            return Json(obj);
        }

        [HttpPost]
        [AuthorizeFilter("querystatistics:patrolclass:view")]
        public async Task<ActionResult> SetStatuz(int statuz) 
        {
            TData obj = await patrolClassBLL.SetStatuz(statuz, DateTime.Now.ToString("yyyy-MM-dd")); //将当天数据设置为启用
            return Json(obj);
        }

        [HttpPost]
        [AuthorizeFilter("querystatistics:patrolclass:view")]
        public async Task<ActionResult> InitData()
        {
            TData obj = await patrolClassBLL.InitData(DateTime.Now.ToString("yyyy-MM-dd")); 
            return Json(obj);
        }

        [HttpPost]
        [AuthorizeFilter("querystatistics:patrolclass:view")]
        public async Task<ActionResult> UpdateData()
        {
            PatrolClassListParam param = new PatrolClassListParam();
            Pagination pagination = new Pagination();
            pagination.PageIndex = 1;
            pagination.PageSize = int.MaxValue;
            TData obj = await patrolClassBLL.UpdateData(param, pagination); //更新数据
            return Json(obj);
        }
        #endregion
    }
}
