﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.BusinessManage;
using Dqy.Syjx.Model.Param.BusinessManage;
using Dqy.Syjx.Enum;

namespace Dqy.Syjx.Service.BusinessManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-11-09 16:56
    /// 描 述：报修列表服务类
    /// </summary>
    public class FunRoomRepairService : RepositoryFactory
    {
        #region 获取数据
        public async Task<List<FunRoomRepairEntity>> GetList(FunRoomRepairListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<FunRoomRepairEntity>> GetPageList(FunRoomRepairListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ListFilter(param, strSql);
            var list = await this.BaseRepository().FindList<FunRoomRepairEntity>(strSql.ToString(), filter.ToArray(), pagination);
            return list.ToList();
        }

        public async Task<FunRoomRepairEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<FunRoomRepairEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(FunRoomRepairEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(FunRoomRepairEntity entity, Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update bn_FunRoomRepair set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
                strSql = $"update bn_FunRoomRepair set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<FunRoomRepairEntity, bool>> ListFilter(FunRoomRepairListParam param)
        {
            var expression = LinqExtensions.True<FunRoomRepairEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (!param.UnitId.IsNullOrZero())
                {
                    expression = expression.And(t => t.UnitId == param.UnitId);
                }
                if (param.FunRoomId > 0)
                {
                    expression = expression.And(t => t.FunRoomId == param.FunRoomId);
                }
                if (param.ObjectId > 0)
                {
                    expression = expression.And(t => t.RepairSource == param.RepairSource);
                    expression = expression.And(t => t.ObjectId == param.ObjectId);
                }
                if (!param.Statuz.IsNullOrZero())
                {
                    expression = expression.And(t => t.Statuz == param.Statuz);
                }
            }
            return expression;
        }

        private List<DbParameter> ListFilter(FunRoomRepairListParam param, StringBuilder strSql)
        {
            strSql.Append($@"  SELECT * From (
                SELECT
				fru7.Id ,
				fru7.BaseIsDelete ,
				fru7.BaseCreateTime ,
				fru7.BaseModifyTime ,
				fru7.BaseCreatorId ,
				fru7.BaseModifierId ,
				fru7.BaseVersion ,
				fru7.UnitId ,
				fru7.FunRoomId ,
				fru7.ObjectId ,
				fru7.RepairSource ,
				fru7.RepairUserId ,
				fru7.RepairDate ,
				fru7.RepairContent ,
				fru7.Statuz ,
				fru7.ServiceDate ,
				fru7.ServiceContent ,
				fru7.ServiceUserId ,
                fr1.DictionaryId1006A ,
                fr1.DictionaryId1006B ,
                fr1.DictionaryId1005 ,
                fr1.Name ,
                fr1.RoomAttribute ,
                fr1.SeatNum ,
                fr1.IsDigitalize ,
                fr1.SysDepartmentId ,
                fr1.SysUserId ,
                fr1.BuildTime ,
                fr1.ReformTime ,
                fr1.Address ,
                fr1.SafeguardUserId ,
                fr1.UploadBriefInfoNum ,
                fr1.UploadSystemNum ,
                fr1.LaboratoryGroupNum ,
                sd1.DicName AS ClassNameA ,
                sd2.DicName AS ClassNameB ,
                sd6.DicName AS NatureName ,
                sd3.DicName AS SubjectName,
                su5.RealName ,
                {param.UserId} AS LoginUserId
                FROM  bn_FunRoom AS fr1
                INNER JOIN  sys_static_dictionary AS sd1 ON fr1.DictionaryId1006A = sd1.DictionaryId AND sd1.BaseIsDelete = 0
		        INNER JOIN  sys_static_dictionary AS sd2 ON fr1.DictionaryId1006B = sd2.DictionaryId AND sd2.BaseIsDelete = 0
		        INNER JOIN  sys_static_dictionary AS sd3 ON fr1.DictionaryId1005 = sd3.DictionaryId AND sd3.BaseIsDelete = 0
                INNER JOIN  sys_static_dictionary AS sd6 ON sd2.Nature = sd6.DictionaryId AND sd6.BaseIsDelete = 0
                INNER JOIN  bn_FunRoomRepair AS fru7 ON fr1.Id = fru7.FunRoomId
                INNER JOIN  SysUser AS su5 ON fru7.RepairUserId = su5.Id AND su5.BaseIsDelete = 0
                where fr1.BaseIsDelete = 0
            ) as tb1 WHERE   1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = @BaseIsDelete ");
                parameter.Add(DbParameterExtension.CreateDbParameter("@BaseIsDelete", param.BaseIsDelete));
                if (param.OptType==1)
                {
                    strSql.Append(" AND (SafeguardUserId = @UserId OR RepairUserId = @UserId )");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UserId", param.UserId));
                }
                else if (param.OptType == 2)
                {
                    strSql.Append(" AND RepairUserId = @UserId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UserId", param.UserId));
                }
                else if (param.OptType == 3)
                {
                    strSql.Append(" AND SafeguardUserId = @UserId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UserId", param.UserId));
                }
                if (param.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    strSql.Append(" AND UnitId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                else
                {
                    strSql.Append(" AND 1 <> 1 ");
                }
                if (param.StartRepaieDate != null)
                {
                    strSql.Append(" AND RepairDate >= @StartTime ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@StartTime", param.StartRepaieDate));
                }
                if (param.EndRepaieDate != null)
                {
                    param.EndRepaieDate = param.EndRepaieDate.Value.Date.Add(new TimeSpan(23, 59, 59));
                    strSql.Append(" AND RepairDate <= @EndTime ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@EndTime", param.EndRepaieDate));
                }
                if (param.DictionaryId1006A > 0)
                {
                    strSql.Append(" AND DictionaryId1006A = @DictionaryId1006A ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId1006A", param.DictionaryId1006A));
                }
                if (param.DictionaryId1006B > 0)
                {
                    strSql.Append(" AND DictionaryId1006B = @DictionaryId1006B ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId1006B", param.DictionaryId1006B));
                }
                if (param.DictionaryId1005 > 0)
                {
                    strSql.Append(" AND DictionaryId1005 = @DictionaryId1005 ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId1005", param.DictionaryId1005));
                }
                if (param.Statuz > 0)
                {
                    strSql.Append(" AND Statuz = @Statuz ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Statuz", param.Statuz));
                }
                if (param.RepairUserId > 0)
                {
                    strSql.Append(" AND RepairUserId = @RepairUserId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@RepairUserId", param.RepairUserId));
                }
                if (!string.IsNullOrEmpty(param.Name))
                {
                    strSql.Append(" AND (Name like @Name OR RepairContent like @Name ) ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Name", $"%{param.Name}%"));
                }
            }
            return parameter;
        }
        #endregion
    }
}
