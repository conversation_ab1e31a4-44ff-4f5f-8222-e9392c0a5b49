﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.CameraManage;
using Dqy.Syjx.Model.Param.CameraManage;
using Org.BouncyCastle.Crypto;
using Dqy.Syjx.Data.EF;

namespace Dqy.Syjx.Service.CameraManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2023-03-28 13:45
    /// 描 述：服务类
    /// </summary>
    public class HaiKangCameraService :  RepositoryFactory
    {
       // string hkdb = GlobalContext.SystemConfig.DBConnectionString2; //haikang数据库连接
        #region 获取数据
        public async Task<List<CameraPointEntity>> GetList(HaiKangCameraListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<CameraPointEntity>> GetPageList(HaiKangCameraListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<CameraPointEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<CameraPointEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(CameraPointEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                string strSql = $" insert into HaiKangCamera(CameraIndexCode, CameraName, RegionIndexCode, RegionName, ParentIndexCode, RegionPath, RegionPathName, CreateTime, Status)" +
                                $"values('{entity.CameraIndexCode}', '{entity.CameraName}', '', '', '', '', '', '{DateTime.Now}', {entity.Status})";
                await this.BaseRepository().ExecuteBySql(strSql);
            }
            else
            {

                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(CameraPointEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                entity.Create();
                await db.Insert(entity);
            }
            else
            {

                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql = "";
            if (ids.IndexOf(',') != -1)
            {
                var idArrary = ids.Split(',');
                foreach (var id in idArrary)
                {
                    strSql += $" delete from HaiKangCamera where Id = {id};";
                }
            }
            else
            {
                strSql = $" delete from HaiKangCamera where Id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }


        #endregion

        #region 私有方法
        private Expression<Func<CameraPointEntity, bool>> ListFilter(HaiKangCameraListParam param)
        {
            var expression = LinqExtensions.True<CameraPointEntity>();
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.CameraName))
                {
                    expression = expression.And(t => t.CameraName.Contains(param.CameraName));
                }
                if (!string.IsNullOrEmpty(param.CameraIndexCode))
                {
                    expression = expression.And(t => t.CameraIndexCode.Contains(param.CameraIndexCode));
                }
            }
            return expression;
        }
        #endregion
    }
}
