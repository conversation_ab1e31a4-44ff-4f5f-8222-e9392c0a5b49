﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.BusinessManage;
using Dqy.Syjx.Model.Param.BusinessManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Enum.OrganizationManage;

namespace Dqy.Syjx.Service.BusinessManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-10-28 09:39
    /// 描 述：服务类
    /// </summary>
    public class UserReportService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<UserReportEntity>> GetList(UserReportListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<UserReportEntity>> GetPageList(UserReportListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ListFilter(param, strSql);
            var list = await this.BaseRepository().FindList<UserReportEntity>(strSql.ToString(), filter.ToArray(), pagination);
            return list.ToList();
        }

        public async Task<UserReportEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<UserReportEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(UserReportEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }
        public async Task SaveTranForm(UserReportEntity entity, Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }
        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
               strSql = $" update bn_UserReport set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
               strSql = $"update bn_UserReport set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<UserReportEntity, bool>> ListFilter(UserReportListParam param)
        {
            var expression = LinqExtensions.True<UserReportEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (param.IsCurrentUnit > -1)
                {
                    expression = expression.And(t => t.IsCurrentUnit == param.IsCurrentUnit);
                }
                if (param.UnitId > 0)
                {
                    expression = expression.And(t => t.UnitId == param.UnitId);
                }
                if (param.SysUserId > 0)
                {
                    expression = expression.And(t => t.SysUserId == param.SysUserId);
                }

            }
            return expression;
        }

        private List<DbParameter> ListFilter(UserReportListParam param, StringBuilder strSql)
        {
            strSql.Append($@"  SELECT * From (
                             SELECT ur1.Id ,
                              ur1.BaseIsDelete ,
                              ur1.BaseCreateTime ,
                              ur1.BaseModifyTime ,
                              ur1.BaseCreatorId ,
                              ur1.BaseModifierId ,
                              ur1.BaseVersion ,
                              ur1.UnitId ,
                              ur1.IsCurrentUnit ,
                              ur1.SysUserId ,
                              ur1.IsExperimenter ,
                              ur1.ExperimenterNature ,
                              ur1.ExperimenterType ,
		                      ue2.JobTitle ,
		                      ue2.TotalSchoolHour ,
		                      uc5.SubjectIdz ,
		                      su3.RealName ,
		                      su3.UserName ,
		                      su3.Gender ,
		                      su3.Birthday

                    FROM  bn_UserReport	 AS ur1
                    INNER JOIN  up_UserExtension AS ue2 ON ue2.IsCurrentUnit = 1 AND ur1.SysUserId = ue2.SysUserId
                    INNER JOIN  SysUser AS su3 ON ue2.SysUserId = su3.Id

                    LEFT JOIN  sys_static_dictionary AS dic4 ON ue2.JobTitle = dic4.DictionaryId
                    LEFT JOIN  up_UserClassInfo AS uc5 ON uc5.IsCurrentUnit = 1 AND ur1.SysUserId = uc5.UserId
                    WHERE ur1.IsCurrentUnit = 1
                          ) as tb1 WHERE   1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = @BaseIsDelete ");
                parameter.Add(DbParameterExtension.CreateDbParameter("@BaseIsDelete", param.BaseIsDelete));
                if (param.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    strSql.Append(" AND UnitId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                else if (param.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    //父级Id
                }
                if (param.Gender >= 0)
                {
                    strSql.Append(" AND Gender = @Gender ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Gender", param.Gender));
                }
                if (param.JobTitle > 0)
                {
                    strSql.Append(" AND JobTitle = @JobTitle ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@JobTitle", param.JobTitle));
                }
                if (param.SubjectId > 0)
                {
                    strSql.Append(" AND SubjectIdz = @SubjectId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SubjectId", param.SubjectId));
                }
                if (param.IsExperimenter == IsEnum.Yes.ParseToInt() || param.IsExperimenter == IsEnum.No.ParseToInt())
                {
                    strSql.Append(" AND IsExperimenter = @IsExperimenter ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@IsExperimenter", param.IsExperimenter));
                }
                if (param.ExperimenterNature == ExperimenterNatureEnum.FullTime.ParseToInt() || param.ExperimenterNature == ExperimenterNatureEnum.PartTime.ParseToInt())
                {
                    strSql.Append(" AND ExperimenterNature = @ExperimenterNature ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ExperimenterNature", param.ExperimenterNature));
                }
                if (!string.IsNullOrEmpty(param.RealName))
                {
                    strSql.Append(" AND RealName like @RealName ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@RealName", $"%{ param.RealName.Trim()}%"));
                }
                if (param.IsCurrentUnit >= 0)
                {
                    strSql.Append(" AND IsCurrentUnit = @IsCurrentUnit ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@IsCurrentUnit", param.IsCurrentUnit));
                }
            }
            return parameter;
        }
        #endregion
    }
}
