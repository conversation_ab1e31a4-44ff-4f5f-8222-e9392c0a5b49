using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.SystemManage;
using Dqy.Syjx.Model.Param.SystemManage;
using Dqy.Syjx.Model.Result.SystemManage;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;

namespace Dqy.Syjx.Service.SystemManage
{
    public class MenuService : RepositoryFactory
    {
        #region 获取数据
        public async Task<List<MenuEntity>> GetList(MenuListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList<MenuEntity>(expression);
            return list.OrderBy(p => p.MenuSort).ToList();
        }

        public async Task<MenuEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<MenuEntity>(id);
        }

        public async Task<int> GetMaxSort(long parentId)
  
        }

        public async Task<List<MenuEntity>> GetList(MenuListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList<MenuEntity>(expression);
            return list.OrderBy(p => p.MenuSort).ToList();
        }

        public async Task<MenuEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<MenuEntity>(id);
        }

        public async Task<int> GetMaxSort(long parentId)
        {
            string where = string.Empty;
            if (parentId > 0)
            {
                where += " AND ParentId = " + parentId;
            }
            object result = await this.BaseRepository().FindObject("SELECT MAX(MenuSort) FROM SysMenu where BaseIsDelete = 0 " + where);
            int sort = result.ParseToInt();
            sort++;
            return sort;
        }

        public bool ExistMenuName(MenuEntity entity)
        {
            var expression = LinqExtensions.True<MenuEntity>();
            expression = expression.And(t => t.BaseIsDelete == 0);
            if (entity.Id.IsNullOrZero())
            {
                expression = expression.And(t => t.MenuName == entity.MenuName && t.MenuType == entity.MenuType && t.ParentId == entity.ParentId);
            }
            else
            {
                expression = expression.And(t => t.MenuName == entity.MenuName && t.MenuType == entity.MenuType && t.Id != entity.Id && t.ParentId == entity.ParentId);
            }
            return this.BaseRepository().IQueryable(expression).Count() > 0 ? true : false;
        }

        /// <summary>
        /// 鑾峰彇灏忕▼搴忚彍鍗?
        /// </summary>
        /// <param name="roleIds">瑙掕壊Id</param>
        /// <returns></returns>
        public async Task<IEnumerable<WeiXinMenu>> GetWxMenuList(string roleIds)
        {
            StringBuilder sql = new StringBuilder();
            sql.AppendFormat(@"
                 SELECT  *
                    FROM    ( SELECT    p.FunId ,
                                        f.Pid AS FunPid ,
                                        f.Name AS FunName ,
                                        f.Url ,
                                        f.Icon ,
                                        f.SubOrder ,
                                        f.SqlText
                              FROM       wx_MenuPermission AS p
                                        INNER JOIN  wx_Menu AS f ON p.FunId = f.Id AND IsShow = 1
                                        INNER JOIN  SysRole AS r ON p.ObjId = r.RoleId
                              WHERE     p.ObjType = 2
                                        AND p.FunType = 2
                                        AND r.RoleId IN ( {0} )
                              GROUP BY  p.FunId ,
                                        f.Pid ,
                                        f.Name ,
                                        f.Url ,
                                        f.Icon ,
                                        f.SubOrder ,
                                        f.SqlText
                            ) t
                    ORDER BY SubOrder asc
            ", roleIds);
            return await this.BaseRepository().FindList<WeiXinMenu>(sql.ToString());
        }


        /// <summary>
        /// 鏍规嵁璇彞鑾峰彇寰呭鐞嗘暟閲?
        /// </summary>
        /// <param name="sqlText"></param>
        /// <returns></returns>
        public async Task<int> GetWxMenuCount(string sqlText)
        {
            int numCount = 0;
            DataTable dt = await this.BaseRepository().FindTable(sqlText);
            if(dt!= null && dt.Rows.Count > 0)
            {
                numCount = dt.Rows.Count;
            }
            return numCount;
        }
        #endregion

        #region 鎻愪氦鏁版嵁
        public async Task SaveForm(MenuEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            var db = await this.BaseRepository().BeginTrans();
            try
            {
                long[] idArr = TextHelper.SplitToArray<long>(ids, ',');
                await db.Delete<MenuEntity>(p => idArr.Contains(p.Id.Value) || idArr.Contains(p.ParentId.Value));
                await db.Delete<MenuAuthorizeEntity>(p => idArr.Contains(p.MenuId.Value));
                await db.CommitTrans();
            }
            catch(Exception ex)
            {
                await db.RollbackTrans();
                throw new Exception("绋嬪簭鍑虹幇寮傚父锛屽紓甯镐俊鎭负锛? + ex.Message);
            }
        }
        #endregion

        #region 绉佹湁鏂规硶
        private Expression<Func<MenuEntity, bool>> ListFilter(MenuListParam param)
        {
            var expression = LinqExtensions.True<MenuEntity>();
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.MenuName))
                {
                    expression = expression.And(t => t.MenuName.Contains(param.MenuName));
                }
                if (param.MenuStatus > -1)
                {
                    expression = expression.And(t => t.MenuStatus == param.MenuStatus);
                }
                if (param.ParentId > -1)
                {
                    expression = expression.And(t => t.ParentId == param.ParentId);
                }
                if (param.Id > 0)
                {
                    expression = expression.And(t => t.Id == param.Id);
                }
            }
            return expression;
        }
        #endregion

    }
}

