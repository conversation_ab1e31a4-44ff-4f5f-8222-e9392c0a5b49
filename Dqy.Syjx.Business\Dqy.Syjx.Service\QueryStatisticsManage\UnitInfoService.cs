﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.QueryStatisticsManage;
using Dqy.Syjx.Model.Param.QueryStatisticsManage;
using System.Data;
using Dqy.Syjx.Model.Result;
using Dqy.Syjx.Entity.PersonManage;

namespace Dqy.Syjx.Service.QueryStatisticsManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2022-06-29 17:10
    /// 描 述：服务类
    /// </summary>
    public class UnitInfoService : RepositoryFactory
    {
        #region 私有方法 
        public async Task<DataTable> GetSummaryList(UnitInfoListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            StringBuilder strBody = new StringBuilder();
            //string awardColl = "";
            //StringBuilder awardFrom = new StringBuilder();
            StringBuilder awardColl = new StringBuilder();
            //if (!string.IsNullOrEmpty(param.CollArrs))
            //{
            //    awardColl = " ,ua31.* ";
            //    awardFrom.Append($@"LEFT JOIN (
            //       SELECT *
            //          FROM 
            //          (
            //          SELECT a01.UnitId ,dic.DicName, COUNT(a01.Id) AS AwardNum  
            //          FROM  up_UserAwardInfo AS a01
            //          INNER JOIN  sys_static_dictionary AS dic ON a01.AwardCategory = dic.DictionaryId
            //          GROUP BY a01.UnitId ,dic.DicName
            //          ) tb1
            //           PIVOT( SUM(AwardNum) FOR DicName IN ({param.CollArrs})) b
            //        ) AS ua31 ON u1.Id = ua31.UnitId
            //    ");
            //    awardSumColl.Append($@" ,SUM(AwardNum) AS AwardNum
            //    ,0 AS UnitId ,{param.CollSumArrs}
            //    ");
            //}
            string[] listCollArr = null;

            if (!string.IsNullOrEmpty(param.CollArrs))
            {
                listCollArr = param.CollArrs.Split(new char[] { ',' });
                if (listCollArr.Length > 0)
                {
                    awardColl.Append(", 0 as ");
                    awardColl.Append(string.Join(", 0 as ", listCollArr));
                }

            }

            strBody.Append($@"
                SELECT u1.Id ,u1.Name ,u1.Sort ,u3.Id AS CountyId ,u3.Name AS CountyName , u3.Sort AS CountySort ,ur4.UnitId AS CityId 
                ,se21.SchoolProp
                ,ISNULL(ue5.ExperimenterNum,0) AS ExperimenterNum ,ISNULL(ue6.ExperimenterNumFull,0) AS ExperimenterNumFull ,ISNULL(ue7.ExperimenterPartNum,0) AS ExperimenterPartNum
                ,ISNULL(ut10.SchoolHour,0) AS SchoolHour ,ISNULL(ut10.BatchNo,0) AS BatchNo  , 0 AS total
                ,ISNULL(ua11.AwardNum,0) AS AwardNum
                {awardColl.ToString()}
                FROM  up_Unit AS u1	
                LEFT JOIN up_SchoolExtension as se21 on u1.Id = se21.UnitId AND se21.BaseIsDelete = 0
                INNER JOIN  up_UnitRelation AS ur2 ON ur2.ExtensionType = 3 AND u1.Id = ur2.ExtensionObjId AND ur2.BaseIsDelete = 0
                INNER JOIN  up_Unit AS u3 ON ur2.UnitId = u3.Id AND u3.Statuz = 1 AND u3.BaseIsDelete = 0 
                INNER JOIN  up_UnitRelation AS ur4 ON ur4.ExtensionType = 3 AND u3.Id = ur4.ExtensionObjId AND ur4.BaseIsDelete = 0
              
                LEFT JOIN (SELECT ue01.UnitId ,COUNT(ue01.Id) AS ExperimenterNum FROM  up_UserExtension AS ue01 
			                INNER JOIN  SysUser AS su02 ON ue01.SysUserId = su02.Id 
			                WHERE ue01.IsExperimenter = 1 
                            AND su02.UserStatus = 1 AND su02.BaseIsDelete = 0 AND ue01.BaseIsDelete = 0 AND ue01.IsCurrentUnit = 1
                            GROUP BY UnitId 
			                ) AS ue5	ON u1.Id = ue5.UnitId
                LEFT JOIN (SELECT ue01.UnitId ,COUNT(ue01.Id) AS ExperimenterNumFull FROM  up_UserExtension AS ue01 
			                INNER JOIN  SysUser AS su02 ON ue01.SysUserId = su02.Id 
		                   WHERE ue01.IsExperimenter = 1 AND ue01.ExperimenterNature = 1 
                            AND su02.UserStatus = 1 AND su02.BaseIsDelete = 0 AND ue01.BaseIsDelete = 0 AND ue01.IsCurrentUnit = 1
                            GROUP BY ue01.UnitId
		                   ) AS ue6 ON u1.Id = ue6.UnitId
                LEFT JOIN (SELECT ue01.UnitId ,COUNT(ue01.Id) AS ExperimenterPartNum FROM  up_UserExtension AS ue01 
			                INNER JOIN  SysUser AS su02 ON ue01.SysUserId = su02.Id 
		                   WHERE ue01.IsExperimenter = 2 AND ue01.ExperimenterNature = 1 
                            AND su02.UserStatus = 1 AND su02.BaseIsDelete = 0 AND ue01.BaseIsDelete = 0 AND ue01.IsCurrentUnit = 1
                            GROUP BY ue01.UnitId
		                   ) AS ue7 ON u1.Id = ue7.UnitId
              
                LEFT JOIN (SELECT ut01.UnitId ,SUM(ut01.SchoolHour) AS SchoolHour,COUNT(ut01.Id) AS BatchNo 
			                FROM  up_UserTrainInfo AS ut01 
			                INNER JOIN  SysUser AS su02 ON ut01.UserId = ut01.Id 
			                WHERE su02.UserStatus = 1 AND su02.BaseIsDelete = 0 AND ut01.IsValid = 1 AND (ut01.Statuz = 2 OR ut01.Statuz = 4) AND ut01.BaseIsDelete = 0 GROUP BY ut01.UnitId
			                ) AS ut10 ON u1.Id = ut10.UnitId
               
                LEFT JOIN (SELECT a01.UnitId ,COUNT(a01.Id) AS AwardNum 
			                FROM  up_UserAwardInfo AS a01
			                INNER JOIN  sys_static_dictionary AS dic ON a01.AwardCategory = dic.DictionaryId
			                GROUP BY a01.UnitId
			                ) AS ua11 ON u1.Id = ua11.UnitId
              
                WHERE u1.Statuz = 1 AND u1.BaseIsDelete = 0 
            ");
            strSql.Append(" SELECT * From ( ");
            strSql.Append(strBody.ToString());
            strSql.Append(" ) as tb1 ");
            var parameter = new List<DbParameter>();
            StringBuilder strSqlWhere = new StringBuilder(" WHERE   1 = 1");
            if (param != null)
            {
                if (param.UnitType == Enum.UnitTypeEnum.County.ParseToInt())
                {
                    strSqlWhere.Append(" AND CountyId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                else if (param.UnitType == Enum.UnitTypeEnum.City.ParseToInt())
                {
                    strSqlWhere.Append(" AND CityId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                else if (param.UnitType == Enum.UnitTypeEnum.System.ParseToInt())
                {
                    //超管查所有的。
                }
                else
                {
                    strSqlWhere.Append(" AND 1 <> 1 ");
                }

                //查询条件
                if (param.Id > 0)
                {
                    strSqlWhere.Append(" AND Id = @SchoolId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.Id));
                }
                if (param.CountyId > 0)
                {
                    strSqlWhere.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
                if (param.SchoolPropList != null && param.SchoolPropList.Count > 0)
                {
                    param.SchoolPropList = param.SchoolPropList.Distinct().ToList();
                    strSqlWhere.Append($" AND ({string.Join(" OR ", param.SchoolPropList.Select(m => string.Format(" SchoolProp = {0} ", m)))}) ");
                }
                strSql.Append(strSqlWhere);

            }
            var tb = await this.BaseRepository().FindTable(strSql.ToString(), parameter.ToArray(), pagination);
            if (tb != null && tb.Rows.Count > 0)
            {
                //获取分类汇总数据
                string strCategory = @$" SELECT  *
                    FROM    ( SELECT  u1.Id, a01.UnitId , se21.SchoolProp ,
                                    u3.Id AS CountyId ,
                                    ur4.UnitId AS CityId ,
                                    dic.DicName AS CategoryName ,
                                    COUNT(a01.Id) AS AwardNum
                          FROM      up_Unit AS u1
                                    LEFT JOIN up_SchoolExtension as se21 on u1.Id = se21.UnitId AND se21.BaseIsDelete = 0
                                    INNER JOIN up_UnitRelation AS ur2 ON ur2.ExtensionType = 3
                                                                         AND u1.Id = ur2.ExtensionObjId
                                                                         AND ur2.BaseIsDelete = 0
                                    INNER JOIN up_Unit AS u3 ON ur2.UnitId = u3.Id
                                                                AND u3.Statuz = 1
                                                                AND u3.BaseIsDelete = 0 
                                    INNER JOIN up_UnitRelation AS ur4 ON ur4.ExtensionType = 3
                                                                         AND u3.Id = ur4.ExtensionObjId
                                                                         AND ur4.BaseIsDelete = 0
                                    LEFT JOIN up_UserAwardInfo AS a01 ON u1.Id = a01.UnitId
                                    LEFT JOIN sys_static_dictionary AS dic ON a01.AwardCategory = dic.DictionaryId
                          GROUP BY  u1.Id, a01.UnitId  , se21.SchoolProp ,
                                    u3.Id ,
                                    ur4.UnitId ,
                                    dic.DicName
                        ) AS tb1
                    {strSqlWhere.ToString()} AND CategoryName IS NOT NULL ";

                var awardList = await this.BaseRepository().FindList<UserAwardInfoEntity>(strCategory, parameter.ToArray());
                //汇总统计
                StringBuilder strTotal = new StringBuilder();
                strTotal.Append(@$" SELECT  0 AS Id ,'总计：' AS Name ,0 AS Sort ,0 AS CountyId ,'' AS CountyName , 0 AS CountySort ,0 AS CityId , 0 AS SchoolProp
                    ,SUM(ExperimenterNum) AS ExperimenterNum,SUM(ExperimenterNumFull) AS ExperimenterNumFull,SUM(ExperimenterPartNum) AS ExperimenterPartNum
                    ,SUM(SchoolHour) AS SchoolHour ,SUM(BatchNo) AS BatchNo, COUNT(Id) AS total, 0 as AwardNum
                    {awardColl.ToString()}
                ");
                //strTotal.Append(awardSumColl.ToString());
                strTotal.Append(" From (  ");
                strTotal.Append(strBody.ToString());
                strTotal.Append(" ) as tb2 ");
                strTotal.Append(strSqlWhere);
                var tbTotal = await this.BaseRepository().FindTable(strTotal.ToString(), parameter.ToArray());
                int totalAward = 0;

                listCollArr = param.CollArrs.Replace("\"", "").Split(new char[] { ',' });

                if (awardList != null && awardList.Count() > 0)
                {
                    totalAward = awardList.Sum(m => m.AwardNum);
                    if (listCollArr != null && listCollArr.Length > 0)
                    {
                        for (int i = 0; i < tb.Rows.Count; i++)
                        {
                            foreach (var c in listCollArr)
                            {
                                long unitId = Convert.ToInt64(tb.Rows[i]["Id"]);
                                tb.Rows[i][c] = awardList.Where(m => m.UnitId == unitId && m.CategoryName == c).Select(f => f.AwardNum).FirstOrDefault();
                            }
                        }
                    }
                }

                if (tbTotal != null && tbTotal.Rows != null && tbTotal.Rows.Count > 0)
                {
                    tbTotal.Rows[0]["AwardNum"] = totalAward;
                    if (listCollArr != null && listCollArr.Length > 0)
                    {
                        foreach (var c in listCollArr)
                        {
                            tbTotal.Rows[0][c] = awardList.Where(m => m.CategoryName == c).Sum(m => m.AwardNum);
                        }
                    }
                    tb.Rows.Add(tbTotal.Rows[0].ItemArray);
                }
            }


            return tb;
        }

        #endregion
    }
}
