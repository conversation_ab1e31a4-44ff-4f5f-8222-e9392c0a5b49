using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.SystemManage;
using Dqy.Syjx.Enum.SystemManage;
using Dqy.Syjx.Model.Param.SystemManage;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Util;


namespace Dqy.Syjx.Service.SystemManage
{
    public class RoleService : RepositoryFactory
    {
        #region 获取数据
        public async Task<List<RoleEntity>> GetList(RoleListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<RoleEntity>> GetPageList(RoleListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression, pagination);
            return list.
        }

        public async Task<List<RoleEntity>> GetList(RoleListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<RoleEntity>> GetPageList(RoleListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<RoleEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<RoleEntity>(id);
        }

        public async Task<RoleEntity> GetEntity(RoleListParam param)
        {
            var expression = ListFilter(param);
            return await this.BaseRepository().FindEntity<RoleEntity>(expression);
        }

        public async Task<RoleEntity> GetEntity(string roleName,int unitType)
        {
            return await this.BaseRepository().FindEntity<RoleEntity>(p => p.RoleName == roleName && p.UnitType == unitType);
        }

        public async Task<int> GetMaxSort()
        {
            object result = await this.BaseRepository().FindObject("SELECT MAX(RoleSort) FROM SysRole");
            int sort = result.ParseToInt();
            sort++;
            return sort;
        }

        public bool ExistRoleName(RoleEntity entity)
        {
            var expression = LinqExtensions.True<RoleEntity>();
            expression = expression.And(t => t.BaseIsDelete == 0);
            if (entity.Id.IsNullOrZero())
            {
                expression = expression.And(t => t.RoleName == entity.RoleName);
            }
            else
            {
                expression = expression.And(t => t.RoleName == entity.RoleName && t.Id != entity.Id);
            }
            if (entity.UnitType > -1)
            {
                expression = expression.And(t => t.UnitType == entity.UnitType);
            }
            return this.BaseRepository().IQueryable(expression).Count() > 0 ? true : false;
        }

        public bool ExistRoleId(RoleEntity entity)
        {
            var expression = LinqExtensions.True<RoleEntity>();
            expression = expression.And(t => t.BaseIsDelete == 0);
            if (entity.Id.IsNullOrZero())
            {
                expression = expression.And(t => t.RoleId == entity.RoleId);
            }
            else
            {
                expression = expression.And(t => t.RoleId == entity.RoleId && t.Id != entity.Id);
            }
            return this.BaseRepository().IQueryable(expression).Count() > 0 ? true : false;
        }

        public async Task<long> GetSysByRoleId(int roleId)
        {
            var entity = await this.BaseRepository().FindEntity<RoleEntity>(p => p.RoleId == roleId);
            if (entity != null)
                return entity.Id.Value;
            else
                return 0;
        }

        /// <summary>
        /// 鏍规嵁浼犲叆瑙掕壊Id闆嗗悎涓庡崟浣嶇被鍒垽鏂坊鍔?淇敼瑙掕壊鏄惁姝ｇ‘
        /// </summary>
        /// <param name="roleIds">瑙掕壊Id闆嗗悎</param>
        /// <param name="unitType">鍗曚綅绫诲瀷</param>
        /// <returns></returns>
        public async Task<List<RoleEntity>> GetRoleListInUnitType(string roleIds,int unitType)
        {
            string sql = $"SELECT Id, RoleName, RoleId, UnitType FROM  SysRole WHERE Id IN({roleIds}) AND UnitType<> {unitType}";
            var list = await this.BaseRepository().FindList<RoleEntity>(sql);
            return list.ToList();
        }
        #endregion

        #region 鎻愪氦鏁版嵁
        public async Task SaveForm(RoleEntity entity)
        {
            var db = await this.BaseRepository().BeginTrans();
            try
            {
                if (entity.Id.IsNullOrZero())
                {
                    await entity.Create();
                    await db.Insert(entity);
                }
                else
                {
                    await db.Delete<MenuAuthorizeEntity>(t => t.AuthorizeId == entity.RoleId);
                    await entity.Modify();
                    await db.Update(entity);
                }
                // 瑙掕壊瀵瑰簲鐨勮彍鍗曘€侀〉闈㈠拰鎸夐挳鏉冮檺
                if (!string.IsNullOrEmpty(entity.MenuIds))
                {
                    foreach (long menuId in TextHelper.SplitToArray<long>(entity.MenuIds, ','))
                    {
                        MenuAuthorizeEntity menuAuthorizeEntity = new MenuAuthorizeEntity();
                        menuAuthorizeEntity.AuthorizeId = entity.RoleId;
                        menuAuthorizeEntity.MenuId = menuId;
                        menuAuthorizeEntity.AuthorizeType = AuthorizeTypeEnum.Role.ParseToInt();
                        await menuAuthorizeEntity.Create();
                        await db.Insert(menuAuthorizeEntity);
                    }
                }
                await db.CommitTrans();
            }
            catch(Exception ex)
            {
                await db.RollbackTrans();
                throw new Exception("绋嬪簭鍑虹幇寮傚父锛屽紓甯镐俊鎭负锛? + ex.Message);
            }
        }

        public async Task DeleteForm(string ids)
        {
            long[] idArr = TextHelper.SplitToArray<long>(ids, ',');
            await this.BaseRepository().Delete<RoleEntity>(idArr);
        }
        #endregion

        #region 绉佹湁鏂规硶
        private Expression<Func<RoleEntity, bool>> ListFilter(RoleListParam param)
        {
            var expression = LinqExtensions.True<RoleEntity>();
            expression = expression.And(t => t.BaseIsDelete == 0);
            
            if (param != null)
            {
                if (param.IsSystem != 1)
                {
                    expression = expression.And(t => t.RoleStatus == 1);
                }
                if (!string.IsNullOrEmpty(param.RoleName))
                {
                    expression = expression.And(t => t.RoleName.Contains(param.RoleName));
                }
                if (!string.IsNullOrEmpty(param.RoleIds))
                {
                    long[] roleIdArr = TextHelper.SplitToArray<long>(param.RoleIds, ',');
                    expression = expression.And(t => roleIdArr.Contains(t.Id.Value));
                }
                if (!string.IsNullOrEmpty(param.StartTime.ParseToString()))
                {
                    expression = expression.And(t => t.BaseModifyTime >= param.StartTime);
                }
                if (!string.IsNullOrEmpty(param.EndTime.ParseToString()))
                {
                    param.EndTime = param.EndTime.Value.Date.Add(new TimeSpan(23, 59, 59));
                    expression = expression.And(t => t.BaseModifyTime <= param.EndTime);
                }

                if (param.UnitType != null)
                {
                    expression = expression.And(t => t.UnitType == param.UnitType);
                }

                if (param.ExcludeSystem.HasValue && param.ExcludeSystem.Value)
                {
                    expression = expression.And(t => t.UnitType != 0);
                }
            }
            return expression;
        }
        #endregion
    }
}

