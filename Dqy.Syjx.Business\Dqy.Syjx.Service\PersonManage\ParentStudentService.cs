﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.PersonManage;
using Dqy.Syjx.Model.Param.PersonManage;
using Dqy.Syjx.Entity.ExperimentTeachManage;

namespace Dqy.Syjx.Service.PersonManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2024-10-25 17:58
    /// 描 述：家长学生表服务类
    /// </summary>
    public class ParentStudentService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<ParentStudentEntity>> GetList(ParentStudentListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public asy
        }

        public async Task<List<ParentStudentEntity>> GetList(ParentStudentListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<ParentStudentEntity>> GetPageList(ParentStudentListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<ParentStudentEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<ParentStudentEntity>(id);
        }

        public async Task<ParentStudentEntity> GetEntityByStudentId(long studentId)
        {
            return await this.BaseRepository().FindEntity<ParentStudentEntity>(f => f.StudentId == studentId);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(ParentStudentEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(ParentStudentEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }
        public async Task UpdateForm(ParentStudentEntity entity, List<string> fields, Repository db)
        {
            if (fields != null)
            {
                await entity.Modify();
                fields.Add("BaseModifyTime");
                fields.Add("BaseModifierId");
            }
            if (db == null)
            {
                await this.BaseRepository().Update(entity, fields);
            }
            else
            {
                await db.Update(entity, fields);
            }
        }
        public async Task DeleteForm(string ids, Repository db = null)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update up_ParentStudent set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
                strSql = $"update up_ParentStudent set BaseIsDelete = 1 where id = {ids}";
            }
            if (db == null)
            {
                await this.BaseRepository().ExecuteBySql(strSql);
            }
            else
            {
                await db.ExecuteBySql(strSql);
            }
        }
        #endregion

        #region 私有方法
        private Expression<Func<ParentStudentEntity, bool>> ListFilter(ParentStudentListParam param)
        {
            var expression = LinqExtensions.True<ParentStudentEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (param.UserId > 0)
                {
                    expression = expression.And(t => t.UserId == param.UserId);
                }
                if (!string.IsNullOrEmpty(param.StudentName))
                {
                    expression = expression.And(t => t.StudentName == param.StudentName);
                }
                if (!string.IsNullOrEmpty(param.IDCard))
                {
                    expression = expression.And(t => t.IDCard == param.IDCard);
                }
                if (param.AuthStatuz != -10000 && param.AuthStatuz != -1)
                {
                    expression = expression.And(t => t.AuthStatuz == param.AuthStatuz);
                }
            }
            return expression;
        }
        #endregion
    }
}

