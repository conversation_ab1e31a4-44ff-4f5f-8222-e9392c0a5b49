﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using System.Text.Json.Serialization;
using Dynamitey.DynamicObjects;

namespace Dqy.Syjx.Model.Result.OrganizationManage
{
    public class UnitStatisticModel
    {
        /// <summary>
        /// 单位Id
        /// </summary>
        [JsonConverter(typeof(StringJsonConverter))]
        public long Id { get; set; }
 
        /// <summary>
        /// 单位代码
        /// </summary>
        /// <returns></returns>
        [Description("单位代码")]
        public string Code { get; set; }
        /// <summary>
        /// 社会统一信用代码
        /// </summary>
        /// <returns></returns>
        [Description("社会统一信用代码")]
        public string OrganizationCode { get; set; }
        /// <summary>
        /// 单位名称
        /// </summary>
        /// <returns></returns>
        [Description("单位名称")]
        public string Name { get; set; }
        /// <summary>
        /// 简称
        /// </summary>
        /// <returns></returns>
        public string Brief { get; set; }
       
        /// <summary>
        /// 详细地址
        /// </summary>
        /// <returns></returns>
        [Description("详细地址")]
        public string Address { get; set; }
      
        /// <summary>
        /// 备注
        /// </summary>
        /// <returns></returns>
        public string Memo { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        /// <returns></returns>
        public int? Statuz { get; set; }
        /// <summary>
        /// 排序
        /// </summary>
        /// <returns></returns>
        public int? Sort { get; set; }
        /// <summary>
        /// 单位属性
        /// </summary>
        [NotMapped]
        public int? SchoolProp { get; set; }
        /// <summary>
        /// 单位属性
        /// </summary>
        [NotMapped]
        [Description("单位属性")]
        public string SchoolPropName { get; set; }
        /// <summary>
        /// 单位性质（1：公办  2：民办）
        /// </summary>
        /// <returns></returns>
        [NotMapped]
        public int? SchoolNature { get; set; }
        /// <summary>
        /// 单位性质
        /// </summary>
        [NotMapped]
        [Description("单位性质")]
        public string SchoolNatureName { get; set; }

        /// <summary>
        /// 学段
        /// </summary>
        [NotMapped]
        [Description("学段")]
        public string SchoolStageName { get; set; }
        /// <summary>
        /// 班级总数
        /// </summary>
        /// <returns></returns>
        [NotMapped]
        public int? ClassNum { get; set; }
        /// <summary>
        /// 教职工数
        /// </summary>
        /// <returns></returns>
        [NotMapped]
        public int? TeacherNum { get; set; }
        /// <summary>
        /// 学生总数
        /// </summary>
        /// <returns></returns>
        [NotMapped]
        public int? StudentNum { get; set; }
        /// <summary>
        /// 占地面积
        /// </summary>
        /// <returns></returns>
        [NotMapped]
        public decimal? FloorArea { get; set; }
        /// <summary>
        /// 建筑面积
        /// </summary>
        /// <returns></returns>
        [NotMapped]
        public decimal? BuildArea { get; set; }
        /// <summary>
        /// 父级Id
        /// </summary>
        /// <returns></returns>
        [NotMapped]
        public string PId { get; set; }

        /// <summary>
        /// 对接第三方单位Id
        /// </summary>        
        [Description("对接第三方单位Id")]
        public string ThirdUnitId { get; set; }

        /// <summary>
        /// 单位Logo
        /// </summary>
        public string Logo { get; set; }
        /// <summary>
        /// 轨
        /// </summary>
        [NotMapped]
        public int TrackNum { get; set; }
        /// <summary>
        /// 轨
        /// </summary>
        [NotMapped]
        public int? TracksNumStatic { get; set; }
        /// <summary>
        /// 区Id
        /// </summary>
        [NotMapped]
        [JsonConverter(typeof(StringJsonConverter))]
        public long? CountyId { get; set; }
        /// <summary>
        /// 学段
        /// </summary>
        [JsonConverter(typeof(StringJsonConverter))]
        public long? SchoolStageId { get; set; }
        /// <summary>
        /// 仪器存量
        /// </summary>
        public decimal InstrumentNum { get; set; }
        /// <summary>
        /// 当年新增
        /// </summary>
        public decimal InstrumentNewNum { get; set; }
        /// <summary>
        /// 实验室数量
        /// </summary>
        public int FunroomNum { get; set; }
        /// <summary>
        /// 使用频次
        /// </summary>
        public int FunroomUseNum { get; set; }
        /// <summary>
        /// 实验员数量
        /// </summary>
        public int ExperimenterUserNum { get; set; }
        /// <summary>
        /// 实验员专职数量
        /// </summary>
        public int ExperimenterUserMajorNum { get; set; }

        /// <summary>
        /// 应开实验数量
        /// </summary>
        public int ExperimentNum { get; set; }

        /// <summary>
        /// 计划实验数量
        /// </summary>
        public int PlanExperimentNum { get; set; }

        /// <summary>
        /// 已开实验数量（登记试验数量）
        /// </summary>
        public int RecordExperimentNum { get; set; }

        /// <summary>
        /// 实验开出率
        /// </summary>
        public decimal ExperimentRate { get; set; }
        /// <summary>
        /// 学科Id
        /// </summary>
        public int CourseId { get; set; }
        /// <summary>
        /// 学科名称
        /// </summary>
        public string CourseName{ get; set; }
        /// <summary>
        /// 年级Id
        /// </summary>
        public int GradeId { get; set; }
        /// <summary>
        /// 年级名称
        /// </summary>
        public string GradeName { get; set; }
        /// <summary>
        /// 班级名称
        /// </summary>
        public string ClassName { get; set; }
        /// <summary>
        /// 班级Id
        /// </summary>
        public int ClassId { get; set; }
    }
}
