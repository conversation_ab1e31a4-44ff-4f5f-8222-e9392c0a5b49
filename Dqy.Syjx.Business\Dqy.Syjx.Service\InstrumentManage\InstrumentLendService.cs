﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.InstrumentManage;
using Dqy.Syjx.Model.Param.InstrumentManage;
using Dqy.Syjx.Enum.InstrumentManage;

namespace Dqy.Syjx.Service.InstrumentManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-11-09 15:30
    /// 描 述：服务类
    /// </summary>
    public class InstrumentLendService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<InstrumentLendEntity>> GetList(InstrumentLendListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<InstrumentLendEntity>> GetPageList(InstrumentLendListParam param, Pagination pagination)
        {
            StringBuilder sql = new StringBuilder();
            var expression = ListFilter(param, sql);
            IEnumerable<InstrumentLendEntity> list = null;
            if (pagination!=null)
            {
                list = await this.BaseRepository().FindList<InstrumentLendEntity>(sql.ToString(), expression.ToArray(), pagination);
            }
            else
            {
                list = await this.BaseRepository().FindList<InstrumentLendEntity>(sql.ToString(), expression.ToArray());
            }
            var resultList = list.Select(item =>
            {
                if (item.BaseCreateTime.HasValue)
                {
                    // 计算预期归还日期
                    DateTime expectedReturnDate = item.BaseCreateTime.Value.AddDays(item.LendDay);
                    // 计算逾期天数
                    item.OverRevertDay = (int)(DateTime.Today - expectedReturnDate).TotalDays;
                }
                return item;
            }).ToList();
            return resultList;
        }

        public async Task<InstrumentLendEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<InstrumentLendEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(InstrumentLendEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(InstrumentLendEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
               strSql = $" update eq_InstrumentLend set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
               strSql = $"update eq_InstrumentLend set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<InstrumentLendEntity, bool>> ListFilter(InstrumentLendListParam param)
        {
            var expression = LinqExtensions.True<InstrumentLendEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (!param.SchoolInstrumentId.IsNullOrZero())
                {
                    expression = expression.And(t => t.SchoolInstrumentId == param.SchoolInstrumentId);
                }
                if (param.StatuzList != null)
                {
                    if (param.StatuzList.Count == 1)
                    {
                        expression = expression.And(t => t.Statuz == param.StatuzList[0]);
                    }
                    else if (param.StatuzList.Count == 2)
                    {
                        expression = expression.And(t => t.Statuz == param.StatuzList[0] || t.Statuz == param.StatuzList[1]);
                    }
                    else if (param.StatuzList.Count == 3)
                    {
                        expression = expression.And(t => t.Statuz == param.StatuzList[0] || t.Statuz == param.StatuzList[1] || t.Statuz == param.StatuzList[2]);
                    }
                }
            }
            return expression;
        }

        private List<DbParameter> ListFilter(InstrumentLendListParam param, StringBuilder strSql)
        {

            strSql.Append(@" SELECT * From (
                    SELECT  IL.Id ,IL.BaseCreateTime ,IL.BaseCreatorId ,
                            IL.SchoolId ,IL.SchoolInstrumentId ,IL.LendUserId ,IL.LendNum ,
                            IL.LendDay ,IL.Remark ,IL.Statuz ,IL.RevertTime ,
                            SI.Name ,SI.Code ,SI.Model ,SI.UnitName  ,SI.Floor ,SI.CupboardId ,
                            SI.StageId ,SI.CourseId ,
                            ISNULL(F.Name,'') AS FunRoom ,ISNULL(C.Name ,'') AS Cupboard ,
                            ISNULL(SI.FunRoomId ,0) AS FunRoomId ,
                            U.RealName AS LendUserName ,ISD.VarietyAttribute ,F.SafeguardUserId ,ISD.IsDangerChemical ,
                            0 AS OverRevertDay  
                    ,ur1.UnitId AS CountyId
                    FROM  eq_InstrumentLend AS IL
                    INNER JOIN  eq_SchoolInstrument AS SI ON IL.SchoolInstrumentId = SI.Id
                    INNER JOIN  eq_InstrumentStandard AS ISD ON SI.InstrumentStandardId = ISD.Id
                    INNER JOIN  SysUser AS U ON IL.LendUserId = U.Id
                    INNER JOIN up_UnitRelation AS ur1 ON ur1.BaseIsDelete = 0 AND IL.SchoolId = ur1.ExtensionObjId AND ur1.ExtensionType = 3 
                    LEFT JOIN  bn_FunRoom AS F ON SI.FunRoomId = F.Id
                    LEFT JOIN  bn_Cupboard AS C ON SI.CupboardId = C.Id
                    WHERE IL.BaseIsDelete = 0
               ) as T WHERE  1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (!param.SafeguardUserId.IsNullOrZero())
                {
                    strSql.Append(" AND SafeguardUserId = @SafeguardUserId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SafeguardUserId", param.SafeguardUserId));
                }
                if (!param.SchoolId.IsNullOrZero())
                {
                    strSql.Append(" AND SchoolId = @SchoolId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
                }
                if (param.CountyId > 0)
                {
                    strSql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
                if (param.LendUserId.HasValue && param.LendUserId > 0)
                {
                    strSql.Append(" AND LendUserId = @LendUserId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@LendUserId", param.LendUserId));
                }
                if (param.GiveUserId.HasValue && param.GiveUserId > 0)
                {
                    strSql.Append(" AND BaseCreatorId = @GiveUserId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@GiveUserId", param.GiveUserId));
                }
                if (param.StartDate.HasValue)
                {
                    strSql.Append(" AND BaseCreateTime >= @StartDate");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@StartDate", param.StartDate));
                }
                if (param.EndDate.HasValue)
                {
                    strSql.Append(" AND BaseCreateTime <= @EndDate");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@EndDate", param.EndDate.Value.AddDays(1).AddSeconds(-1)));
                }
                if (param.Statuz.HasValue && param.Statuz > -1)
                {
                    strSql.Append(" AND Statuz = @Statuz");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Statuz", param.Statuz));
                }
                if (!param.StoragePlace.IsEmpty() && !"-1".Equals(param.StoragePlace))
                {
                    if (param.StoragePlace.IndexOf(',') != -1)
                    {
                        strSql.Append(" AND CupboardId = @CupboardId");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@CupboardId", param.StoragePlace.Split(',')[1]));
                    }
                    else
                    {
                        strSql.Append(" AND FunRoomId = @FunRoomId");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@FunRoomId", param.StoragePlace));
                    }
                }
                if (!string.IsNullOrWhiteSpace(param.KeyWord))
                {
                    strSql.Append($" AND (Name LIKE '%{param.KeyWord.Trim()}%' OR Code LIKE '%{param.KeyWord.Trim()}%') ");
                }

                if (!param.InstrumentAttribute.IsNullOrZero())
                {
                    if(param.InstrumentAttribute == 1)
                    {
                        //仪器
                        strSql.Append($" AND VarietyAttribute <> '{VarietyAttributeEnum.LowValueConsumable.ParseToInt()}' AND VarietyAttribute <> '{VarietyAttributeEnum.Reagent.ParseToInt()}' ");
                    }
                    else if(param.InstrumentAttribute == 2)
                    {
                        //耗材
                        strSql.Append($" AND (VarietyAttribute = '{VarietyAttributeEnum.LowValueConsumable.ParseToInt()}' OR VarietyAttribute = '{VarietyAttributeEnum.Reagent.ParseToInt()}')");
                    }
                }
            }
            return parameter;
        }

        #endregion
    }
}
