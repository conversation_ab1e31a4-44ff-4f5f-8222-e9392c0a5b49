﻿@{ Layout = "~/Views/Shared/_Index.cshtml"; }
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@section header{
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/jquery.layout/1.4.4/jquery.layout-latest.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/jquery.layout/1.4.4/jquery.layout-latest.min.js"))
}

<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <input type="hidden" id="departmentId" col="DepartmentId">
            <div class="select-list">
                <ul>
                    <li>
                        用户状态：<span id="userStatus" col="UserStatus"></span>
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a id="btnCancle" class="btn btn-primary btn-sm" onclick="clearGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn" ></i>
                    </li>
                </ul>
            </div>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    $(function () {
        initGrid();

       $("#userStatus").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(StatusEnum).EnumToDictionaryString())) });

    });

    function initGrid() {
        var queryUrl = '@Url.Content("~/OrganizationManage/User/GetPaginationListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,           
            columns: [
                { field: 'Id', title: 'Id', visible: false },
                { field: 'DepartmentNames', title: '所属部门', sortable: false , visible: false},
                { field: 'RealName', title: '姓名', sortable: false },
                { field: 'Mobile', title: '手机号', sortable: false },
                { field: 'UserName', title: '登录账号', sortable: false },
                { field: 'RoleNames', title: '用户角色', sortable: false , visible: false},
                {
                    field: 'UserStatus', title: '用户状态', formatter: function (value, row, index) {
                        if (row.UserStatus == "@StatusEnum.Yes.ParseToInt()") {
                            return '<span class="badge badge-primary">' + "@StatusEnum.Yes.GetDescription()" + '</span>';
                        } else {
                            return '<span class="badge badge-warning">' + "@StatusEnum.No.GetDescription()" + '</span>';
                        }
                    }
                },
                {
                    field: '', title: '操作', width: '10%', align: 'left',
                    formatter: function (value, row, index) {
                        var html = $.Format('<a class="btn btn-info btn-xs" href="#" onclick="verify(this)" value="{0}"><i class="fa fa-eye"></i>验签</a> ', row.Id);
                        return html;
                    }
                }
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $("#searchDiv").getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }


    function clearGrid() {
        $('#roleId').ysComboBox('setValue', -1)
        $('#userStatus').ysComboBox('setValue', -1)
        initTree();
        $("#departmentId").val(null);
        $("#realName").val("");
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function verify(obj) {
        var id = $(obj).attr('value');
        var url = '@Url.Content("~/BusinessThirdManage/SignWx/Verify?bizType=101")' + '&id=' + id;

        ys.ajax({
                    url: url,
                    type: 'post',
                    success: function (obj) {
                        if (obj.Tag == 1) {
                            ys.msgSuccess(obj.Message);
                        }
                        else {
                            ys.msgError(obj.Message);
                        }
                    }
                });

    }
</script>
