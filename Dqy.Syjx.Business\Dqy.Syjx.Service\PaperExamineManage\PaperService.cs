﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.PaperExamineManage;
using Dqy.Syjx.Model.Param.PaperExamineManage;
using Dqy.Syjx.Enum;

namespace Dqy.Syjx.Service.PaperExamineManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2022-08-02 15:15
    /// 描 述：组卷与发布服务类
    /// </summary>
    public class PaperService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<PaperEntity>> GetList(PaperListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();

        }

        public async Task<List<PaperEntity>> GetPageList(PaperListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ListFilter(param, strSql);
            var list = await this.BaseRepository().FindList<PaperEntity>(strSql.ToString(), filter.ToArray(), pagination);
            return list.ToList();
        }

        public async Task<PaperEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<PaperEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(PaperEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(PaperEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task<int> DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
               strSql = $" update cp_Paper set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
               strSql = $"update cp_Paper set BaseIsDelete = 1 where id = {ids}";
            }
            int i = await this.BaseRepository().ExecuteBySql(strSql);
            return i;
        }

        public async Task ClonePaper(PaperEntity entity)
        {
            await entity.Create();
            await this.BaseRepository().Insert(entity);

        }

        #endregion

        #region 私有方法
        private Expression<Func<PaperEntity, bool>> ListFilter(PaperListParam param)
        {
            var expression = LinqExtensions.True<PaperEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (!string.IsNullOrEmpty(param.Name))
                {
                    expression = expression.And(t => t.Name == param.Name);
                }
            }
            return expression;
        }

        /// <summary>
        /// 试卷列表相关数据查询
        /// </summary>
        /// <param name="param"></param>
        /// <param name="strSql"></param>
        /// <returns></returns>
        private List<DbParameter> ListFilter(PaperListParam param, StringBuilder strSql)
        {
            if(param.PaperType == 1) //标准试卷
            {
                strSql.Append($@"SELECT * From (
                                SELECT p.Id AS Id ,p.BaseCreatorId AS CreatorId ,p.Code AS Code ,p.Name AS NAME ,p.PaperType AS PaperType ,p.UseObj ,ISNULL(sd1.DicName,'全部') AS UseObjName ,p.CourseId ,ISNULL(sd2.DicName,'全部') AS CourseName
                                      ,p.SchoolStagez ,ISNULL(sd3.DicName,'全部') AS SchoolStage ,p.GradeId ,ISNULL(sd4.DicName,'全部') AS GradeName ,PaperStatuz ,RadioQuestionNum ,ChedkboxQuestionNum ,IfQuestionNum ,TotalQuestionNum
                                      ,p.UnitId AS SchoolId ,u1.Name AS SchoolName ,ur.UnitId AS CountyId ,sa.AreaName AS CountyName ,ur2.UnitId AS CityId ,u3.Name AS CityName ,p.BaseCreateTime AS BaseCreateTime ,p.BaseModifyTime AS BaseModifyTime
                                      ,ISNULL(exa.ExamineStatuz,'') AS ExamineStatuz ,ISNULL(exa.AvgAccuracy,0) AS AvgAccuracy ,ISNULL(exa.Duration,0) AS Duration
                                      ,Case WHEN exa.BeginTime > '1970-01-01 00:00:00' THEN exa.BeginTime ELSE NULL END AS ExamineBeginTime
									  ,Case WHEN exa.EndTime > '1970-01-01 00:00:00' THEN exa.EndTime ELSE NULL END AS ExamineEndTime
                                      ,ISNULL(addr.ExamineCode,'') AS ExamineCode
                                 FROM  cp_Paper AS p
                                 LEFT JOIN  sys_static_dictionary AS sd1 ON p.UseObj = sd1.DictionaryId AND sd1.BaseIsDelete = 0 AND sd1.TypeCode = '2003'
                                 LEFT JOIN  sys_static_dictionary AS sd2 ON p.CourseId = sd2.DictionaryId AND sd2.BaseIsDelete = 0 AND sd2.TypeCode = '1005'
                                 LEFT JOIN  sys_static_dictionary AS sd3 ON p.SchoolStagez = sd3.DictionaryId AND sd3.BaseIsDelete = 0 AND sd3.TypeCode = '1002'
                                 LEFT JOIN  sys_static_dictionary AS sd4 ON p.GradeId = sd4.DictionaryId AND sd4.BaseIsDelete = 0 AND sd4.TypeCode = '1003'
                                 INNER JOIN  up_Unit AS u1 ON p.UnitId = u1.Id AND u1.Statuz = 1 AND u1.BaseIsDelete = 0
                                 INNER JOIN  up_UnitRelation AS ur ON ur.BaseIsDelete = 0 AND u1.Id = ur.ExtensionObjId AND ur.ExtensionType = 3
                                 INNER JOIN  up_Unit AS u2 ON UR.UnitId = u2.Id AND u2.BaseIsDelete = 0
                                 INNER JOIN  up_UnitRelation AS ur2 ON u2.Id = ur2.ExtensionObjId AND ur2.ExtensionType = 3 AND ur2.BaseIsDelete = 0
                                 INNER JOIN  up_Unit AS u3 ON ur2.UnitId = u3.Id AND u3.BaseIsDelete = 0
                                 LEFT JOIN  SysArea AS sa ON sa.AreaCode = u2.AreaId AND sa.BaseIsDelete = 0
								 LEFT JOIN  cp_Examine exa ON exa.PaperId = p.Id AND exa.BaseIsDelete = 0 AND exa.Statuz = 1
                                 LEFT JOIN  cp_ExamineAddress addr ON addr.PaperId = p.Id AND addr.ExamineId = exa.Id AND addr.BaseIsDelete = 0 AND addr.IsCurrentValid = 1
                                 WHERE p.Statuz = 1 AND p.BaseIsDelete = 0
                            )tb1 WHERE 1=1 ");
            }

            if(param.PaperType == 2) //自我练习
            {
                strSql.Append($@"SELECT * From (
                                SELECT p.Id AS Id ,p.BaseCreatorId AS CreatorId ,p.Code AS Code ,p.Name AS NAME ,p.PaperType AS PaperType ,p.UseObj ,ISNULL(sd1.DicName,'全部') AS UseObjName ,p.CourseId ,ISNULL(sd2.DicName,'全部') AS CourseName
                                      ,p.SchoolStagez ,ISNULL(sd3.DicName,'全部') AS SchoolStage ,p.GradeId ,ISNULL(sd4.DicName,'全部') AS GradeName ,PaperStatuz ,RadioQuestionNum ,ChedkboxQuestionNum ,IfQuestionNum ,TotalQuestionNum
                                      ,p.UnitId AS SchoolId ,u1.Name AS SchoolName ,ur.UnitId AS CountyId ,sa.AreaName AS CountyName ,ur2.UnitId AS CityId ,u3.Name AS CityName ,p.BaseCreateTime AS BaseCreateTime ,p.BaseModifyTime AS BaseModifyTime
                                      ,ISNULL(exa.ExamineStatuz,'') AS ExamineStatuz ,ISNULL(exa.AvgAccuracy,0) AS AvgAccuracy ,ISNULL(exa.Duration,0) AS Duration
                                      ,Case WHEN exa.BeginTime > '1970-01-01 00:00:00' THEN exa.BeginTime ELSE NULL END AS ExamineBeginTime
									  ,Case WHEN exalist.EndTime > '1970-01-01 00:00:00' THEN exalist.EndTime ELSE NULL END AS ExamineEndTime
                                      ,ISNULL(addr.ExamineCode,'') AS ExamineCode
                                      ,ISNULL(exalist.Id,0) AS ExamineListId ,exalist.SubmitStatuz
                                 FROM  cp_Paper AS p
                                 LEFT JOIN  sys_static_dictionary AS sd1 ON p.UseObj = sd1.DictionaryId AND sd1.BaseIsDelete = 0 AND sd1.TypeCode = '2003'
                                 LEFT JOIN  sys_static_dictionary AS sd2 ON p.CourseId = sd2.DictionaryId AND sd2.BaseIsDelete = 0 AND sd2.TypeCode = '1005'
                                 LEFT JOIN  sys_static_dictionary AS sd3 ON p.SchoolStagez = sd3.DictionaryId AND sd3.BaseIsDelete = 0 AND sd3.TypeCode = '1002'
                                 LEFT JOIN  sys_static_dictionary AS sd4 ON p.GradeId = sd4.DictionaryId AND sd4.BaseIsDelete = 0 AND sd4.TypeCode = '1003'
                                 INNER JOIN  up_Unit AS u1 ON p.UnitId = u1.Id AND u1.Statuz = 1 AND u1.BaseIsDelete = 0
                                 INNER JOIN  up_UnitRelation AS ur ON ur.BaseIsDelete = 0 AND u1.Id = ur.ExtensionObjId AND ur.ExtensionType = 3
                                 INNER JOIN  up_Unit AS u2 ON UR.UnitId = u2.Id AND u2.BaseIsDelete = 0
                                 INNER JOIN  up_UnitRelation AS ur2 ON u2.Id = ur2.ExtensionObjId AND ur2.ExtensionType = 3 AND ur2.BaseIsDelete = 0
                                 INNER JOIN  up_Unit AS u3 ON ur2.UnitId = u3.Id AND u3.BaseIsDelete = 0
                                 LEFT JOIN  SysArea AS sa ON sa.AreaCode = u2.AreaId AND sa.BaseIsDelete = 0
								 LEFT JOIN  cp_Examine exa ON exa.PaperId = p.Id AND exa.BaseIsDelete = 0 AND exa.Statuz = 1
                                 LEFT JOIN  cp_ExamineList exalist ON exalist.ExamineId = exa.Id AND exalist.PaperId = p.Id AND exalist.BaseIsDelete = 0
                                 LEFT JOIN  cp_ExamineAddress addr ON addr.PaperId = p.Id AND addr.ExamineId = exa.Id AND addr.BaseIsDelete = 0 AND addr.IsCurrentValid = 1
                                 WHERE p.Statuz = 1 AND p.BaseIsDelete = 0
                            )tb1 WHERE 1=1 AND TotalQuestionNum>0");
            }

            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    strSql.Append(" AND SchoolId = @SchoolId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.UnitId));

                }
                else if (param.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    strSql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                    if (param.SchoolId > 0)
                    {
                        strSql.Append(" AND SchoolId = @SchoolId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.UnitId));
                    }
                }
                else if (param.UnitType == UnitTypeEnum.City.ParseToInt() || param.UnitType == UnitTypeEnum.System.ParseToInt())
                {
                    strSql.Append(" AND CityId = @CityId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CityId", param.CityId));
                    if (param.CountyId > 0)
                    {
                        strSql.Append(" AND CountyId = @CountyId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                    }
                    if (param.SchoolId > 0)
                    {
                        strSql.Append(" AND SchoolId = @SchoolId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.UnitId));
                    }
                }
                if (param.PaperType > 0)
                {
                    strSql.Append(" AND PaperType = @PaperType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@PaperType", param.PaperType));
                }
                if (param.PaperStatuz > 0)
                {
                    strSql.Append(" AND PaperStatuz = @PaperStatuz ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@PaperStatuz", param.PaperStatuz));
                }
                if (param.UseObj > 0)
                {
                    strSql.Append(" AND UseObj = @UseObj ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UseObj", param.UseObj));
                }
                if (param.CourseId > 0)
                {
                    strSql.Append(" AND CourseId = @CourseId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (param.SchoolStagez > 0)
                {
                    strSql.Append(" AND SchoolStagez = @SchoolStagez ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStagez", param.SchoolStagez));
                }
                if (param.GradeId > 0)
                {
                    strSql.Append(" AND GradeId = @GradeId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@GradeId", param.GradeId));
                }
                if (!string.IsNullOrEmpty(param.Code))
                {
                    strSql.Append(" AND ( Name like @Code OR  Code like @Code ) ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Code", $"%{param.Code}%"));
                }
                //练习时间查询
                if (!string.IsNullOrEmpty(param.ExamineBeginTime.ParseToString()))
                {
                    strSql.Append(" AND ExamineBeginTime >= @ExamineBeginTime ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ExamineBeginTime", param.ExamineBeginTime));
                }
                if (!string.IsNullOrEmpty(param.ExamineEndTime.ParseToString()))
                {
                    param.ExamineEndTime = param.ExamineEndTime.Value.Date.Add(new TimeSpan(23, 59, 59));
                    strSql.Append(" AND ExamineBeginTime <= @ExamineEndTime ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ExamineEndTime", param.ExamineEndTime));
                }
                //自我练习创建人
                if((param.PaperType == 2) && (param.CreatorId > 0))
                {
                    strSql.Append(" AND CreatorId = @CreatorId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CreatorId", param.CreatorId));
                }
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    strSql.Append(" AND Id = @Ids ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Ids", param.Ids));
                }
                //考核状态
                if (param.ExamineStatuz > 0)
                {
                    strSql.Append(" AND ExamineStatuz = @ExamineStatuz ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ExamineStatuz", param.ExamineStatuz));
                }
            }
            return parameter;
        }
        #endregion
    }
}
