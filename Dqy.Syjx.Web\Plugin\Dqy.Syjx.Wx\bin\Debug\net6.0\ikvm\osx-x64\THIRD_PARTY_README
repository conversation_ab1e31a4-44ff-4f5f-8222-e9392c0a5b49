DO NOT TRANSLATE OR LOCALIZE.
-----------------------------

%% This notice is provided with respect to ASM Bytecode Manipulation 
Framework v5.0.3, which may be included with JRE 8, and JDK 8, and 
OpenJDK 8.

--- begin of LICENSE ---

Copyright (c) 2000-2011 France Télécom
All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions
are met:

1. Redistributions of source code must retain the above copyright
   notice, this list of conditions and the following disclaimer.

2. Redistributions in binary form must reproduce the above copyright
   notice, this list of conditions and the following disclaimer in the
   documentation and/or other materials provided with the distribution.

3. Neither the name of the copyright holders nor the names of its
   contributors may be used to endorse or promote products derived from
   this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE
LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF
THE POSSIBILITY OF SUCH DAMAGE.

--- end of LICENSE ---

--------------------------------------------------------------------------------

%% This notice is provided with respect to BSDiff v4.3, which may be 
included with JRE 8, JDK 8, and OpenJDK 8.

--- begin of LICENSE ---

Copyright 2003-2005 Colin Percival
All rights reserved

Redistribution and use in source and binary forms, with or without
modification, are permitted providing that the following conditions
are met:
1. Redistributions of source code must retain the above copyright
notice, this list of conditions and the following disclaimer.
2. Redistributions in binary form must reproduce the above copyright
notice, this list of conditions and the following disclaimer in the
documentation and/or other materials provided with the distribution.

THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR
IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
ARE DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
POSSIBILITY OF SUCH DAMAGE.

--- end of LICENSE ---

-------------------------------------------------------------------------------

%% This notice is provided with respect to CodeViewer 1.0, which may be
included with JDK 8.

--- begin of LICENSE ---

Copyright 1999 by CoolServlets.com.

Any errors or suggested improvements to this class can be reported as
instructed on CoolServlets.com. We hope you enjoy this program... your
comments will encourage further development!  This software is distributed
under the terms of the BSD License.  Redistribution and use in source and
binary forms, with or without modification, are permitted provided that the
following conditions are met:

1. Redistributions of source code must retain the above copyright notice, this
   list of conditions and the following disclaimer.

2. Redistributions in binary form must reproduce the above copyright notice,
   this list of conditions and the following disclaimer in the documentation
   and/or other materials provided with the distribution.

Neither name of CoolServlets.com nor the names of its contributors may be
used to endorse or promote products derived from this software without
specific prior written permission.

THIS SOFTWARE IS PROVIDED BY COOLSERVLETS.COM AND CONTRIBUTORS ``AS IS'' AND
ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
DISCLAIMED. IN NO EVENT SHALL THE AUTHOR OR CONTRIBUTORS BE LIABLE FOR ANY
DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
(INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON
ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE."


--- end of LICENSE ---

-------------------------------------------------------------------------------

%% This notice is provided with respect to Cryptix AES 3.2.0, which may be
included with JRE 8, JDK 8, and OpenJDK 8.

--- begin of LICENSE ---

Cryptix General License

Copyright (c) 1995-2005 The Cryptix Foundation Limited.
All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are
met:

  1. Redistributions of source code must retain the copyright notice,
     this list of conditions and the following disclaimer.

  2. Redistributions in binary form must reproduce the above copyright
     notice, this list of conditions and the following disclaimer in
     the documentation and/or other materials provided with the
     distribution.

THIS SOFTWARE IS PROVIDED BY THE CRYPTIX FOUNDATION LIMITED AND
CONTRIBUTORS ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES,
INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
IN NO EVENT SHALL THE CRYPTIX FOUNDATION LIMITED OR CONTRIBUTORS BE
LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR
BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE
OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN
IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

--- end of LICENSE ---

-------------------------------------------------------------------------------

%% This notice is provided with respect to CUP Parser Generator for 
Java 0.10k, which may be included with JRE 8, JDK 8, and OpenJDK 8.

--- begin of LICENSE ---

Copyright 1996-1999 by Scott Hudson, Frank Flannery, C. Scott Ananian

Permission to use, copy, modify, and distribute this software and its
documentation for any purpose and without fee is hereby granted, provided
that the above copyright notice appear in all copies and that both the
copyright notice and this permission notice and warranty disclaimer appear in
supporting documentation, and that the names of the authors or their
employers not be used in advertising or publicity pertaining to distribution of
the software without specific, written prior permission.

The authors and their employers disclaim all warranties with regard to
this software, including all implied warranties of merchantability and fitness.
In no event shall the authors or their employers be liable for any special,
indirect or consequential damages or any damages whatsoever resulting from
loss of use, data or profits, whether in an action of contract, negligence or
other tortious action, arising out of or in connection with the use or
performance of this software.

--- end of LICENSE ---

-------------------------------------------------------------------------------

%% This notice is provided with respect to Document Object Model (DOM) Level 2
& 3, which may be included with JRE 8, JDK 8, and OpenJDK 8.

--- begin of LICENSE ---

W3C SOFTWARE NOTICE AND LICENSE

http://www.w3.org/Consortium/Legal/2002/copyright-software-20021231

This work (and included software, documentation such as READMEs, or other
related items) is being provided by the copyright holders under the following
license. By obtaining, using and/or copying this work, you (the licensee)
agree that you have read, understood, and will comply with the following terms
and conditions.

Permission to copy, modify, and distribute this software and its
documentation, with or without modification, for any purpose and without fee
or royalty is hereby granted, provided that you include the following on ALL
copies of the software and documentation or portions thereof, including
modifications:

   1.The full text of this NOTICE in a location viewable to users of the
   redistributed or derivative work.

   2.Any pre-existing intellectual property disclaimers, notices, or terms and
   conditions. If none exist, the W3C Software Short Notice should be included
   (hypertext is preferred, text is permitted) within the body of any
   redistributed or derivative code.

   3.Notice of any changes or modifications to the files, including the date
   changes were made. (We recommend you provide URIs to the location from
   which the code is derived.)

THIS SOFTWARE AND DOCUMENTATION IS PROVIDED "AS IS," AND COPYRIGHT HOLDERS
MAKE NO REPRESENTATIONS OR WARRANTIES, EXPRESS OR IMPLIED, INCLUDING BUT NOT
LIMITED TO, WARRANTIES OF MERCHANTABILITY OR FITNESS FOR ANY PARTICULAR
PURPOSE OR THAT THE USE OF THE SOFTWARE OR DOCUMENTATION WILL NOT INFRINGE ANY
THIRD PARTY PATENTS,COPYRIGHTS, TRADEMARKS OR OTHER RIGHTS.

COPYRIGHT HOLDERS WILL NOT BE LIABLE FOR ANY DIRECT, INDIRECT, SPECIAL
OR CONSEQUENTIAL DAMAGES ARISING OUT OF ANY USE OF THE SOFTWARE OR
DOCUMENTATION.  The name and trademarks of copyright holders may NOT be used
in advertising or publicity pertaining to the software without specific,
written prior permission. Title to copyright in this software and any
associated documentation will at all times remain with copyright holders.

____________________________________

This formulation of W3C's notice and license became active on December 31
2002. This version removes the copyright ownership notice such that this
license can be used with materials other than those owned by the W3C, reflects
that ERCIM is now a host of the W3C, includes references to this specific
dated version of the license, and removes the ambiguous grant of "use".
Otherwise, this version is the same as the previous version and is written so
as to preserve the Free Software Foundation's assessment of GPL compatibility
and OSI's certification under the Open Source Definition. Please see our
Copyright FAQ for common questions about using materials from our site,
including specific terms and conditions for packages like libwww, Amaya, and
Jigsaw. Other questions about this notice can be directed to
<EMAIL>.

--- end of LICENSE ---

-------------------------------------------------------------------------------

%% This notice is provided with respect to Dynalink v0.5, which may be 
included with JRE 8, JDK 8, and OpenJDK 8.

--- begin of LICENSE ---

Copyright (c) 2009-2013, Attila Szegedi

All rights reserved.Redistribution and use in source and binary forms, with or
without modification, are permitted provided that the following conditions are
met:* Redistributions of source code must retain the above copyright notice,
this list of conditions and the following disclaimer.  * Redistributions in
binary form must reproduce the above copyright notice,   this list of
conditions and the following disclaimer in the documentation  and/or other
materials provided with the distribution.  * Neither the name of Attila
Szegedi nor the names of its contributors may be used to endorse or promote
products derived from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"AND
ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THEPOSSIBILITY OF SUCH DAMAGE.

--- end of LICENSE ---

-------------------------------------------------------------------------------

%% This notice is provided with respect to Elliptic Curve Cryptography, which 
may be included with JRE 8, JDK 8, and OpenJDK 8.

You are receiving a copy of the Elliptic Curve Cryptography library in source
form with the JDK 8 and OpenJDK 8 source distributions, and as object code in
the JRE 8 & JDK 8 runtimes.

In the case of the JRE 8 & JDK 8 runtimes, the terms of the Oracle license do
NOT apply to the Elliptic Curve Cryptography library; it is licensed under the
following license, separately from Oracle's JDK & JRE.  If you do not wish to
install the Elliptic Curve Cryptography library, you may delete the library
named libsunec.so (on Solaris and Linux systems) or sunec.dll (on Windows
systems) from the JRE bin directory reserved for native libraries.


--- begin of LICENSE ---

                  GNU LESSER GENERAL PUBLIC LICENSE
                       Version 2.1, February 1999

 Copyright (C) 1991, 1999 Free Software Foundation, Inc.
 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301  USA
 Everyone is permitted to copy and distribute verbatim copies
 of this license document, but changing it is not allowed.

[This is the first released version of the Lesser GPL.  It also counts
 as the successor of the GNU Library Public License, version 2, hence
 the version number 2.1.]

                            Preamble

  The licenses for most software are designed to take away your
freedom to share and change it.  By contrast, the GNU General Public
Licenses are intended to guarantee your freedom to share and change
free software--to make sure the software is free for all its users.

  This license, the Lesser General Public License, applies to some
specially designated software packages--typically libraries--of the
Free Software Foundation and other authors who decide to use it.  You
can use it too, but we suggest you first think carefully about whether
this license or the ordinary General Public License is the better
strategy to use in any particular case, based on the explanations below.

  When we speak of free software, we are referring to freedom of use,
not price.  Our General Public Licenses are designed to make sure that
you have the freedom to distribute copies of free software (and charge
for this service if you wish); that you receive source code or can get
it if you want it; that you can change the software and use pieces of
it in new free programs; and that you are informed that you can do
these things.

  To protect your rights, we need to make restrictions that forbid
distributors to deny you these rights or to ask you to surrender these
rights.  These restrictions translate to certain responsibilities for
you if you distribute copies of the library or if you modify it.

  For example, if you distribute copies of the library, whether gratis
or for a fee, you must give the recipients all the rights that we gave
you.  You must make sure that they, too, receive or can get the source
code.  If you link other code with the library, you must provide
complete object files to the recipients, so that they can relink them
with the library after making changes to the library and recompiling
it.  And you must show them these terms so they know their rights.

  We protect your rights with a two-step method: (1) we copyright the
library, and (2) we offer you this license, which gives you legal
permission to copy, distribute and/or modify the library.

  To protect each distributor, we want to make it very clear that
there is no warranty for the free library.  Also, if the library is
modified by someone else and passed on, the recipients should know
that what they have is not the original version, so that the original
author's reputation will not be affected by problems that might be
introduced by others.

  Finally, software patents pose a constant threat to the existence of
any free program.  We wish to make sure that a company cannot
effectively restrict the users of a free program by obtaining a
restrictive license from a patent holder.  Therefore, we insist that
any patent license obtained for a version of the library must be
consistent with the full freedom of use specified in this license.

  Most GNU software, including some libraries, is covered by the
ordinary GNU General Public License.  This license, the GNU Lesser
General Public License, applies to certain designated libraries, and
is quite different from the ordinary General Public License.  We use
this license for certain libraries in order to permit linking those
libraries into non-free programs.

  When a program is linked with a library, whether statically or using
a shared library, the combination of the two is legally speaking a
combined work, a derivative of the original library.  The ordinary
General Public License therefore permits such linking only if the
entire combination fits its criteria of freedom.  The Lesser General
Public License permits more lax criteria for linking other code with
the library.

  We call this license the "Lesser" General Public License because it
does Less to protect the user's freedom than the ordinary General
Public License.  It also provides other free software developers Less
of an advantage over competing non-free programs.  These disadvantages
are the reason we use the ordinary General Public License for many
libraries.  However, the Lesser license provides advantages in certain
special circumstances.

  For example, on rare occasions, there may be a special need to
encourage the widest possible use of a certain library, so that it becomes
a de-facto standard.  To achieve this, non-free programs must be
allowed to use the library.  A more frequent case is that a free
library does the same job as widely used non-free libraries.  In this
case, there is little to gain by limiting the free library to free
software only, so we use the Lesser General Public License.

  In other cases, permission to use a particular library in non-free
programs enables a greater number of people to use a large body of
free software.  For example, permission to use the GNU C Library in
non-free programs enables many more people to use the whole GNU
operating system, as well as its variant, the GNU/Linux operating
system.

  Although the Lesser General Public License is Less protective of the
users' freedom, it does ensure that the user of a program that is
linked with the Library has the freedom and the wherewithal to run
that program using a modified version of the Library.

  The precise terms and conditions for copying, distribution and
modification follow.  Pay close attention to the difference between a
"work based on the library" and a "work that uses the library".  The
former contains code derived from the library, whereas the latter must
be combined with the library in order to run.

                  GNU LESSER GENERAL PUBLIC LICENSE
   TERMS AND CONDITIONS FOR COPYING, DISTRIBUTION AND MODIFICATION

  0. This License Agreement applies to any software library or other
program which contains a notice placed by the copyright holder or
other authorized party saying it may be distributed under the terms of
this Lesser General Public License (also called "this License").
Each licensee is addressed as "you".

  A "library" means a collection of software functions and/or data
prepared so as to be conveniently linked with application programs
(which use some of those functions and data) to form executables.

  The "Library", below, refers to any such software library or work
which has been distributed under these terms.  A "work based on the
Library" means either the Library or any derivative work under
copyright law: that is to say, a work containing the Library or a
portion of it, either verbatim or with modifications and/or translated
straightforwardly into another language.  (Hereinafter, translation is
included without limitation in the term "modification".)

  "Source code" for a work means the preferred form of the work for
making modifications to it.  For a library, complete source code means
all the source code for all modules it contains, plus any associated
interface definition files, plus the scripts used to control compilation
and installation of the library.

  Activities other than copying, distribution and modification are not
covered by this License; they are outside its scope.  The act of
running a program using the Library is not restricted, and output from
such a program is covered only if its contents constitute a work based
on the Library (independent of the use of the Library in a tool for
writing it).  Whether that is true depends on what the Library does
and what the program that uses the Library does.

  1. You may copy and distribute verbatim copies of the Library's
complete source code as you receive it, in any medium, provided that
you conspicuously and appropriately publish on each copy an
appropriate copyright notice and disclaimer of warranty; keep intact
all the notices that refer to this License and to the absence of any
warranty; and distribute a copy of this License along with the
Library.

  You may charge a fee for the physical act of transferring a copy,
and you may at your option offer warranty protection in exchange for a
fee.

  2. You may modify your copy or copies of the Library or any portion
of it, thus forming a work based on the Library, and copy and
distribute such modifications or work under the terms of Section 1
above, provided that you also meet all of these conditions:

    a) The modified work must itself be a software library.

    b) You must cause the files modified to carry prominent notices
    stating that you changed the files and the date of any change.

    c) You must cause the whole of the work to be licensed at no
    charge to all third parties under the terms of this License.

    d) If a facility in the modified Library refers to a function or a
    table of data to be supplied by an application program that uses
    the facility, other than as an argument passed when the facility
    is invoked, then you must make a good faith effort to ensure that,
    in the event an application does not supply such function or
    table, the facility still operates, and performs whatever part of
    its purpose remains meaningful.

    (For example, a function in a library to compute square roots has
    a purpose that is entirely well-defined independent of the
    application.  Therefore, Subsection 2d requires that any
    application-supplied function or table used by this function must
    be optional: if the application does not supply it, the square
    root function must still compute square roots.)

These requirements apply to the modified work as a whole.  If
identifiable sections of that work are not derived from the Library,
and can be reasonably considered independent and separate works in
themselves, then this License, and its terms, do not apply to those
sections when you distribute them as separate works.  But when you
distribute the same sections as part of a whole which is a work based
on the Library, the distribution of the whole must be on the terms of
this License, whose permissions for other licensees extend to the
entire whole, and thus to each and every part regardless of who wrote
it.

Thus, it is not the intent of this section to claim rights or contest
your rights to work written entirely by you; rather, the intent is to
exercise the right to control the distribution of derivative or
collective works based on the Library.

In addition, mere aggregation of another work not based on the Library
with the Library (or with a work based on the Library) on a volume of
a storage or distribution medium does not bring the other work under
the scope of this License.

  3. You may opt to apply the terms of the ordinary GNU General Public
License instead of this License to a given copy of the Library.  To do
this, you must alter all the notices that refer to this License, so
that they refer to the ordinary GNU General Public License, version 2,
instead of to this License.  (If a newer version than version 2 of the
ordinary GNU General Public License has appeared, then you can specify
that version instead if you wish.)  Do not make any other change in
these notices.

  Once this change is made in a given copy, it is irreversible for
that copy, so the ordinary GNU General Public License applies to all
subsequent copies and derivative works made from that copy.

  This option is useful when you wish to copy part of the code of
the Library into a program that is not a library.

  4. You may copy and distribute the Library (or a portion or
derivative of it, under Section 2) in object code or executable form
under the terms of Sections 1 and 2 above provided that you accompany
it with the complete corresponding machine-readable source code, which
must be distributed under the terms of Sections 1 and 2 above on a
medium customarily used for software interchange.

  If distribution of object code is made by offering access to copy
from a designated place, then offering equivalent access to copy the
source code from the same place satisfies the requirement to
distribute the source code, even though third parties are not
compelled to copy the source along with the object code.

  5. A program that contains no derivative of any portion of the
Library, but is designed to work with the Library by being compiled or
linked with it, is called a "work that uses the Library".  Such a
work, in isolation, is not a derivative work of the Library, and
therefore falls outside the scope of this License.

  However, linking a "work that uses the Library" with the Library
creates an executable that is a derivative of the Library (because it
contains portions of the Library), rather than a "work that uses the
library".  The executable is therefore covered by this License.
Section 6 states terms for distribution of such executables.

  When a "work that uses the Library" uses material from a header file
that is part of the Library, the object code for the work may be a
derivative work of the Library even though the source code is not.
Whether this is true is especially significant if the work can be
linked without the Library, or if the work is itself a library.  The
threshold for this to be true is not precisely defined by law.

  If such an object file uses only numerical parameters, data
structure layouts and accessors, and small macros and small inline
functions (ten lines or less in length), then the use of the object
file is unrestricted, regardless of whether it is legally a derivative
work.  (Executables containing this object code plus portions of the
Library will still fall under Section 6.)

  Otherwise, if the work is a derivative of the Library, you may
distribute the object code for the work under the terms of Section 6.
Any executables containing that work also fall under Section 6,
whether or not they are linked directly with the Library itself.

  6. As an exception to the Sections above, you may also combine or
link a "work that uses the Library" with the Library to produce a
work containing portions of the Library, and distribute that work
under terms of your choice, provided that the terms permit
modification of the work for the customer's own use and reverse
engineering for debugging such modifications.

  You must give prominent notice with each copy of the work that the
Library is used in it and that the Library and its use are covered by
this License.  You must supply a copy of this License.  If the work
during execution displays copyright notices, you must include the
copyright notice for the Library among them, as well as a reference
directing the user to the copy of this License.  Also, you must do one
of these things:

    a) Accompany the work with the complete corresponding
    machine-readable source code for the Library including whatever
    changes were used in the work (which must be distributed under
    Sections 1 and 2 above); and, if the work is an executable linked
    with the Library, with the complete machine-readable "work that
    uses the Library", as object code and/or source code, so that the
    user can modify the Library and then relink to produce a modified
    executable containing the modified Library.  (It is understood
    that the user who changes the contents of definitions files in the
    Library will not necessarily be able to recompile the application
    to use the modified definitions.)

    b) Use a suitable shared library mechanism for linking with the
    Library.  A suitable mechanism is one that (1) uses at run time a
    copy of the library already present on the user's computer system,
    rather than copying library functions into the executable, and (2)
    will operate properly with a modified version of the library, if
    the user installs one, as long as the modified version is
    interface-compatible with the version that the work was made with.

    c) Accompany the work with a written offer, valid for at
    least three years, to give the same user the materials
    specified in Subsection 6a, above, for a charge no more
    than the cost of performing this distribution.

    d) If distribution of the work is made by offering access to copy
    from a designated place, offer equivalent access to copy the above
    specified materials from the same place.

    e) Verify that the user has already received a copy of these
    materials or that you have already sent this user a copy.

  For an executable, the required form of the "work that uses the
Library" must include any data and utility programs needed for
reproducing the executable from it.  However, as a special exception,
the materials to be distributed need not include anything that is
normally distributed (in either source or binary form) with the major
components (compiler, kernel, and so on) of the operating system on
which the executable runs, unless that component itself accompanies
the executable.

  It may happen that this requirement contradicts the license
restrictions of other proprietary libraries that do not normally
accompany the operating system.  Such a contradiction means you cannot
use both them and the Library together in an executable that you
distribute.

  7. You may place library facilities that are a work based on the
Library side-by-side in a single library together with other library
facilities not covered by this License, and distribute such a combined
library, provided that the separate distribution of the work based on
the Library and of the other library facilities is otherwise
permitted, and provided that you do these two things:

    a) Accompany the combined library with a copy of the same work
    based on the Library, uncombined with any other library
    facilities.  This must be distributed under the terms of the
    Sections above.

    b) Give prominent notice with the combined library of the fact
    that part of it is a work based on the Library, and explaining
    where to find the accompanying uncombined form of the same work.

  8. You may not copy, modify, sublicense, link with, or distribute
the Library except as expressly provided under this License.  Any
attempt otherwise to copy, modify, sublicense, link with, or
distribute the Library is void, and will automatically terminate your
rights under this License.  However, parties who have received copies,
or rights, from you under this License will not have their licenses
terminated so long as such parties remain in full compliance.

  9. You are not required to accept this License, since you have not
signed it.  However, nothing else grants you permission to modify or
distribute the Library or its derivative works.  These actions are
prohibited by law if you do not accept this License.  Therefore, by
modifying or distributing the Library (or any work based on the
Library), you indicate your acceptance of this License to do so, and
all its terms and conditions for copying, distributing or modifying
the Library or works based on it.

  10. Each time you redistribute the Library (or any work based on the
Library), the recipient automatically receives a license from the
original licensor to copy, distribute, link with or modify the Library
subject to these terms and conditions.  You may not impose any further
restrictions on the recipients' exercise of the rights granted herein.
You are not responsible for enforcing compliance by third parties with
this License.

  11. If, as a consequence of a court judgment or allegation of patent
infringement or for any other reason (not limited to patent issues),
conditions are imposed on you (whether by court order, agreement or
otherwise) that contradict the conditions of this License, they do not
excuse you from the conditions of this License.  If you cannot
distribute so as to satisfy simultaneously your obligations under this
License and any other pertinent obligations, then as a consequence you
may not distribute the Library at all.  For example, if a patent
license would not permit royalty-free redistribution of the Library by
all those who receive copies directly or indirectly through you, then
the only way you could satisfy both it and this License would be to
refrain entirely from distribution of the Library.

If any portion of this section is held invalid or unenforceable under any
particular circumstance, the balance of the section is intended to apply,
and the section as a whole is intended to apply in other circumstances.

It is not the purpose of this section to induce you to infringe any
patents or other property right claims or to contest validity of any
such claims; this section has the sole purpose of protecting the
integrity of the free software distribution system which is
implemented by public license practices.  Many people have made
generous contributions to the wide range of software distributed
through that system in reliance on consistent application of that
system; it is up to the author/donor to decide if he or she is willing
to distribute software through any other system and a licensee cannot
impose that choice.

This section is intended to make thoroughly clear what is believed to
be a consequence of the rest of this License.

  12. If the distribution and/or use of the Library is restricted in
certain countries either by patents or by copyrighted interfaces, the
original copyright holder who places the Library under this License may add
an explicit geographical distribution limitation excluding those countries,
so that distribution is permitted only in or among countries not thus
excluded.  In such case, this License incorporates the limitation as if
written in the body of this License.

  13. The Free Software Foundation may publish revised and/or new
versions of the Lesser General Public License from time to time.
Such new versions will be similar in spirit to the present version,
but may differ in detail to address new problems or concerns.

Each version is given a distinguishing version number.  If the Library
specifies a version number of this License which applies to it and
"any later version", you have the option of following the terms and
conditions either of that version or of any later version published by
the Free Software Foundation.  If the Library does not specify a
license version number, you may choose any version ever published by
the Free Software Foundation.

  14. If you wish to incorporate parts of the Library into other free
programs whose distribution conditions are incompatible with these,
write to the author to ask for permission.  For software which is
copyrighted by the Free Software Foundation, write to the Free
Software Foundation; we sometimes make exceptions for this.  Our
decision will be guided by the two goals of preserving the free status
of all derivatives of our free software and of promoting the sharing
and reuse of software generally.

                            NO WARRANTY

  15. BECAUSE THE LIBRARY IS LICENSED FREE OF CHARGE, THERE IS NO
WARRANTY FOR THE LIBRARY, TO THE EXTENT PERMITTED BY APPLICABLE LAW.
EXCEPT WHEN OTHERWISE STATED IN WRITING THE COPYRIGHT HOLDERS AND/OR
OTHER PARTIES PROVIDE THE LIBRARY "AS IS" WITHOUT WARRANTY OF ANY
KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING, BUT NOT LIMITED TO, THE
IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
PURPOSE.  THE ENTIRE RISK AS TO THE QUALITY AND PERFORMANCE OF THE
LIBRARY IS WITH YOU.  SHOULD THE LIBRARY PROVE DEFECTIVE, YOU ASSUME
THE COST OF ALL NECESSARY SERVICING, REPAIR OR CORRECTION.

  16. IN NO EVENT UNLESS REQUIRED BY APPLICABLE LAW OR AGREED TO IN
WRITING WILL ANY COPYRIGHT HOLDER, OR ANY OTHER PARTY WHO MAY MODIFY
AND/OR REDISTRIBUTE THE LIBRARY AS PERMITTED ABOVE, BE LIABLE TO YOU
FOR DAMAGES, INCLUDING ANY GENERAL, SPECIAL, INCIDENTAL OR
CONSEQUENTIAL DAMAGES ARISING OUT OF THE USE OR INABILITY TO USE THE
LIBRARY (INCLUDING BUT NOT LIMITED TO LOSS OF DATA OR DATA BEING
RENDERED INACCURATE OR LOSSES SUSTAINED BY YOU OR THIRD PARTIES OR A
FAILURE OF THE LIBRARY TO OPERATE WITH ANY OTHER SOFTWARE), EVEN IF
SUCH HOLDER OR OTHER PARTY HAS BEEN ADVISED OF THE POSSIBILITY OF SUCH
DAMAGES.

                     END OF TERMS AND CONDITIONS

           How to Apply These Terms to Your New Libraries

  If you develop a new library, and you want it to be of the greatest
possible use to the public, we recommend making it free software that
everyone can redistribute and change.  You can do so by permitting
redistribution under these terms (or, alternatively, under the terms of the
ordinary General Public License).

  To apply these terms, attach the following notices to the library.  It is
safest to attach them to the start of each source file to most effectively
convey the exclusion of warranty; and each file should have at least the
"copyright" line and a pointer to where the full notice is found.

    <one line to give the library's name and a brief idea of what it does.>
    Copyright (C) <year>  <name of author>

    This library is free software; you can redistribute it and/or
    modify it under the terms of the GNU Lesser General Public
    License as published by the Free Software Foundation; either
    version 2.1 of the License, or (at your option) any later version.

    This library is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
    Lesser General Public License for more details.

    You should have received a copy of the GNU Lesser General Public
    License along with this library; if not, write to the Free Software
    Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301  USA

Also add information on how to contact you by electronic and paper mail.

You should also get your employer (if you work as a programmer) or your
school, if any, to sign a "copyright disclaimer" for the library, if
necessary.  Here is a sample; alter the names:

  Yoyodyne, Inc., hereby disclaims all copyright interest in the
  library `Frob' (a library for tweaking knobs) written by James Random Hacker.

  <signature of Ty Coon>, 1 April 1990
  Ty Coon, President of Vice

That's all there is to it!

--- end of LICENSE ---

-------------------------------------------------------------------------------

%% This notice is provided with respect to  ECMAScript Language
Specification ECMA-262 Edition 5.1 which may be included with 
JRE 8, JDK 8, and OpenJDK 8.

--- begin of LICENSE ---

Copyright notice
Copyright © 2011 Ecma International
Ecma International
Rue du Rhone 114
CH-1204 Geneva
Tel: +41 22 849 6000
Fax: +41 22 849 6001
Web: http://www.ecma-international.org

This document and possible translations of it may be copied and furnished to
others, and derivative works that comment on or otherwise explain it or assist
in its implementation may be prepared, copied, published, and distributed, in
whole or in part, without restriction of any kind, provided that the above
copyright notice and this section are included on all such copies and derivative
works. However, this document itself may not be modified in any way, including
by removing the copyright notice or references to Ecma International, except as
needed for the purpose of developing any document or deliverable produced by
Ecma International (in which case the rules applied to copyrights must be
followed) or as required to translate it into languages other than English. The
limited permissions granted above are perpetual and will not be revoked by Ecma
International or its successors or assigns. This document and the information
contained herein is provided on an "AS IS" basis and ECMA INTERNATIONAL
DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO ANY
WARRANTY THAT THE USE OF THE INFORMATION HEREIN WILL NOT INFRINGE ANY OWNERSHIP
RIGHTS OR ANY IMPLIED WARRANTIES OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR
PURPOSE." Software License

All Software contained in this document ("Software)" is protected by copyright
and is being made available under the "BSD License", included below. This
Software may be subject to third party rights (rights from parties other than
Ecma International), including patent rights, and no licenses under such third
party rights are granted under this license even if the third party concerned is
a member of Ecma International. SEE THE ECMA CODE OF CONDUCT IN PATENT MATTERS
AVAILABLE AT http://www.ecma-international.org/memento/codeofconduct.htm FOR
INFORMATION REGARDING THE LICENSING OF PATENT CLAIMS THAT ARE REQUIRED TO
IMPLEMENT ECMA INTERNATIONAL STANDARDS*. Redistribution and use in source and
binary forms, with or without modification, are permitted provided that the
following conditions are met:

1. Redistributions of source code must retain the above copyright notice, this
list of conditions and the following disclaimer.

2. Redistributions in binary form must reproduce the above copyright notice,
this list of conditions and the following disclaimer in the documentation and/or
other materials provided with the distribution.

3. Neither the name of the authors nor Ecma International may be used to endorse
or promote products derived from this software without specific prior written
permission.

THIS SOFTWARE IS PROVIDED BY THE ECMA INTERNATIONAL "AS IS" AND ANY EXPRESS OR
IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT
SHALL ECMA INTERNATIONAL BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR
BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY
OF SUCH DAMAGE.
--- end of LICENSE ---

%% This notice is provided with respect to Dynalink library which is included
with the Nashorn technology.

--- begin of LICENSE ---
Copyright (c) 2009-2013, Attila Szegedi

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are
met:
* Redistributions of source code must retain the above copyright
  notice, this list of conditions and the following disclaimer.
* Redistributions in binary form must reproduce the above copyright
  notice, this list of conditions and the following disclaimer in the
  documentation and/or other materials provided with the distribution.
* Neither the name of the copyright holder nor the names of
  contributors may be used to endorse or promote products derived from
  this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS
IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A
PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL COPYRIGHT HOLDER
BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR
BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF
ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
--- end of LICENSE ---

%% This notice is provided with respect to Joni library which is included
with the Nashorn technology.

--- begin of LICENSE ---
Permission is hereby granted, free of charge, to any person obtaining a copy of
this software and associated documentation files (the "Software"), to deal in
the Software without restriction, including without limitation the rights to
use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies
of the Software, and to permit persons to whom the Software is furnished to do
so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

--- end of LICENSE ---

-------------------------------------------------------------------------------

%% This notice is provided with respect to FontConfig 2.5, which may be 
included with JRE 8, JDK 8, and OpenJDK 8 source distributions on
Linux and Solaris.

--- begin of LICENSE ---

Copyright © 2001,2003 Keith Packard

Permission to use, copy, modify, distribute, and sell this software and its
documentation for any purpose is hereby granted without fee, provided that the
above copyright notice appear in all copies and that both that copyright
notice and this permission notice appear in supporting documentation, and that
the name of Keith Packard not be used in advertising or publicity pertaining
to distribution of the software without specific, written prior permission.
Keith Packard makes no representations about the suitability of this software
for any purpose.  It is provided "as is" without express or implied warranty.

KEITH PACKARD DISCLAIMS ALL WARRANTIES WITH REGARD TO THIS SOFTWARE, INCLUDING
ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS, IN NO EVENT SHALL KEITH
PACKARD BE LIABLE FOR ANY SPECIAL, INDIRECT OR CONSEQUENTIAL DAMAGES OR ANY
DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN
CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.


--- end of LICENSE ---

-------------------------------------------------------------------------------

%% This notice is provided with respect to IAIK PKCS#11 Wrapper, 
which may be included with JRE 8, JDK 8, and OpenJDK 8.

--- begin of LICENSE ---

IAIK PKCS#11 Wrapper License

Copyright (c) 2002 Graz University of Technology. All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

1. Redistributions of source code must retain the above copyright notice, this
   list of conditions and the following disclaimer.

2. Redistributions in binary form must reproduce the above copyright notice,
   this list of conditions and the following disclaimer in the documentation
   and/or other materials provided with the distribution.

3. The end-user documentation included with the redistribution, if any, must
   include the following acknowledgment:

   "This product includes software developed by IAIK of Graz University of
    Technology."

   Alternately, this acknowledgment may appear in the software itself, if and
   wherever such third-party acknowledgments normally appear.

4. The names "Graz University of Technology" and "IAIK of Graz University of
   Technology" must not be used to endorse or promote products derived from this
   software without prior written permission.

5. Products derived from this software may not be called "IAIK PKCS Wrapper",
   nor may "IAIK" appear in their name, without prior written permission of
   Graz University of Technology.

THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED WARRANTIES,
INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND
FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
LICENSOR BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY,
OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
POSSIBILITY OF SUCH DAMAGE.

--- end of LICENSE ---

-------------------------------------------------------------------------------

%% This notice is provided with respect to ICU4C 4.0.1 and ICU4J 4.4, which 
may be included with JRE 8, JDK 8, and OpenJDK 8.

--- begin of LICENSE ---

Copyright (c) 1995-2010 International Business Machines Corporation and others 

All rights reserved. 

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, and/or sell copies of the
Software, and to permit persons to whom the Software is furnished to do so,
provided that the above copyright notice(s) and this permission notice appear
in all copies of the Software and that both the above copyright notice(s) and
this permission notice appear in supporting documentation.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT OF THIRD PARTY RIGHTS. IN
NO EVENT SHALL THE COPYRIGHT HOLDER OR HOLDERS INCLUDED IN THIS NOTICE BE
LIABLE FOR ANY CLAIM, OR ANY SPECIAL INDIRECT OR CONSEQUENTIAL DAMAGES, OR ANY
DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN
CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.

Except as contained in this notice, the name of a copyright holder shall not
be used in advertising or otherwise to promote the sale, use or other dealings
in this Software without prior written authorization of the copyright holder.
All trademarks and registered trademarks mentioned herein are the property of
their respective owners.

--- end of LICENSE ---

-------------------------------------------------------------------------------

%% This notice is provided with respect to IJG JPEG 6b, which may be 
included with JRE 8, JDK 8, and OpenJDK 8.

--- begin of LICENSE ---

This software is copyright (C) 1991-1998, Thomas G. Lane.
All Rights Reserved except as specified below.

Permission is hereby granted to use, copy, modify, and distribute this
software (or portions thereof) for any purpose, without fee, subject to these
conditions:
(1) If any part of the source code for this software is distributed, then this
README file must be included, with this copyright and no-warranty notice
unaltered; and any additions, deletions, or changes to the original files
must be clearly indicated in accompanying documentation.
(2) If only executable code is distributed, then the accompanying
documentation must state that "this software is based in part on the work of
the Independent JPEG Group".
(3) Permission for use of this software is granted only if the user accepts
full responsibility for any undesirable consequences; the authors accept
NO LIABILITY for damages of any kind.

These conditions apply to any software derived from or based on the IJG code,
not just to the unmodified library.  If you use our work, you ought to
acknowledge us.

Permission is NOT granted for the use of any IJG author's name or company name
in advertising or publicity relating to this software or products derived from
it.  This software may be referred to only as "the Independent JPEG Group's
software".

We specifically permit and encourage the use of this software as the basis of
commercial products, provided that all warranty or liability claims are
assumed by the product vendor.

--- end of LICENSE ---

--------------------------------------------------------------------------------

%% This notice is provided with respect to Joni v1.1.9, which may be 
included with JRE 8, JDK 8, and OpenJDK 8.

--- begin of LICENSE ---

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

--- end of LICENSE ---

-------------------------------------------------------------------------------

%% This notice is provided with respect to JOpt-Simple v3.0,  which may be 
included with JRE 8, JDK 8, and OpenJDK 8.

--- begin of LICENSE ---

 Copyright (c) 2004-2009 Paul R. Holser, Jr.

 Permission is hereby granted, free of charge, to any person obtaining
 a copy of this software and associated documentation files (the
 "Software"), to deal in the Software without restriction, including
 without limitation the rights to use, copy, modify, merge, publish,
 distribute, sublicense, and/or sell copies of the Software, and to
 permit persons to whom the Software is furnished to do so, subject to
 the following conditions:

 The above copyright notice and this permission notice shall be
 included in all copies or substantial portions of the Software.

 THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
 MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
 LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
 OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
 WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

--- end of LICENSE ---

--------------------------------------------------------------------------------

%% This notice is provided with respect to Kerberos functionality, which 
which may be included with JRE 8, JDK 8, and OpenJDK 8.

--- begin of LICENSE ---

 (C) Copyright IBM Corp. 1999 All Rights Reserved.
 Copyright 1997 The Open Group Research Institute. All rights reserved.

--- end of LICENSE ---

-------------------------------------------------------------------------------

%% This notice is provided with respect to Kerberos functionality from 
FundsXpress, INC., which may be included with JRE 8, JDK 8, and OpenJDK 8.

--- begin of LICENSE ---

 Copyright (C) 1998 by the FundsXpress, INC.

 All rights reserved.

 Export of this software from the United States of America may require
 a specific license from the United States Government.  It is the
 responsibility of any person or organization contemplating export to
 obtain such a license before exporting.

 WITHIN THAT CONSTRAINT, permission to use, copy, modify, and
 distribute this software and its documentation for any purpose and
 without fee is hereby granted, provided that the above copyright
 notice appear in all copies and that both that copyright notice and
 this permission notice appear in supporting documentation, and that
 the name of FundsXpress. not be used in advertising or publicity pertaining
 to distribution of the software without specific, written prior
 permission.  FundsXpress makes no representations about the suitability of
 this software for any purpose.  It is provided "as is" without express
 or implied warranty.

 THIS SOFTWARE IS PROVIDED ``AS IS'' AND WITHOUT ANY EXPRESS OR
 IMPLIED WARRANTIES, INCLUDING, WITHOUT LIMITATION, THE IMPLIED
 WARRANTIES OF MERCHANTIBILITY AND FITNESS FOR A PARTICULAR PURPOSE.


--- end of LICENSE ---

-------------------------------------------------------------------------------

%% This notice is provided with respect to Kronos OpenGL headers, which may be 
included with JDK 8 and OpenJDK 8 source distributions.

--- begin of LICENSE ---

 Copyright (c) 2007 The Khronos Group Inc.

 Permission is hereby granted, free of charge, to any person obtaining a copy
 of this software and/or associated documentation files (the "Materials"), to
 deal in the Materials without restriction, including without limitation the
 rights to use, copy, modify, merge, publish, distribute, sublicense, and/or
 sell copies of the Materials, and to permit persons to whom the Materials are
 furnished to do so, subject to the following conditions:

 The above copyright notice and this permission notice shall be included in all
 copies or substantial portions of the Materials.

 THE MATERIALS ARE PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL THE
 AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 OUT OF OR IN CONNECTION WITH THE MATERIALS OR THE USE OR OTHER DEALINGS IN THE
 MATERIALS.

--- end of LICENSE ---

-------------------------------------------------------------------------------

%% Portions Copyright Eastman Kodak Company 1992

-------------------------------------------------------------------------------

%% This notice is provided with respect to libpng 1.6.16, which may be 
included with JRE 8, JDK 8, and OpenJDK 8.

--- begin of LICENSE ---

This copy of the libpng notices is provided for your convenience.  In case of
any discrepancy between this copy and the notices in the file png.h that is
included in the libpng distribution, the latter shall prevail.

COPYRIGHT NOTICE, DISCLAIMER, and LICENSE:

If you modify libpng you may insert additional notices immediately following
this sentence.

This code is released under the libpng license.

libpng versions 1.2.6, August 15, 2004, through 1.6.16, December 22, 2014, are
Copyright (c) 2004, 2006-2014 Glenn Randers-Pehrson, and are
distributed according to the same disclaimer and license as libpng-1.2.5
with the following individual added to the list of Contributing Authors

   Cosmin Truta

libpng versions 1.0.7, July 1, 2000, through 1.2.5 - October 3, 2002, are
Copyright (c) 2000-2002 Glenn Randers-Pehrson, and are
distributed according to the same disclaimer and license as libpng-1.0.6
with the following individuals added to the list of Contributing Authors

   Simon-Pierre Cadieux
   Eric S. Raymond
   Gilles Vollant

and with the following additions to the disclaimer:

   There is no warranty against interference with your enjoyment of the
   library or against infringement.  There is no warranty that our
   efforts or the library will fulfill any of your particular purposes
   or needs.  This library is provided with all faults, and the entire
   risk of satisfactory quality, performance, accuracy, and effort is with
   the user.

libpng versions 0.97, January 1998, through 1.0.6, March 20, 2000, are
Copyright (c) 1998, 1999 Glenn Randers-Pehrson, and are
distributed according to the same disclaimer and license as libpng-0.96,
with the following individuals added to the list of Contributing Authors: <AUTHORS>
   Glenn Randers-Pehrson
   Willem van Schaik

libpng versions 0.89, June 1996, through 0.96, May 1997, are
Copyright (c) 1996, 1997 Andreas Dilger
Distributed according to the same disclaimer and license as libpng-0.88,
with the following individuals added to the list of Contributing Authors: <AUTHORS>
   Kevin Bracey
   Sam Bushell
   Magnus Holmgren
   Greg Roelofs
   Tom Tanner

libpng versions 0.5, May 1995, through 0.88, January 1996, are
Copyright (c) 1995, 1996 Guy Eric Schalnat, Group 42, Inc.

For the purposes of this copyright and license, "Contributing Authors"
is defined as the following set of individuals:

   Andreas Dilger
   Dave Martindale
   Guy Eric Schalnat
   Paul Schmidt
   Tim Wegner

The PNG Reference Library is supplied "AS IS".  The Contributing Authors
and Group 42, Inc. disclaim all warranties, expressed or implied,
including, without limitation, the warranties of merchantability and of
fitness for any purpose.  The Contributing Authors and Group 42, Inc.
assume no liability for direct, indirect, incidental, special, exemplary,
or consequential damages, which may result from the use of the PNG
Reference Library, even if advised of the possibility of such damage.

Permission is hereby granted to use, copy, modify, and distribute this
source code, or portions hereof, for any purpose, without fee, subject
to the following restrictions:

1. The origin of this source code must not be misrepresented.

2. Altered versions must be plainly marked as such and must not
   be misrepresented as being the original source.

3. This Copyright notice may not be removed or altered from any
   source or altered source distribution.

The Contributing Authors and Group 42, Inc. specifically permit, without
fee, and encourage the use of this source code as a component to
supporting the PNG file format in commercial products.  If you use this
source code in a product, acknowledgment is not required but would be
appreciated.


A "png_get_copyright" function is available, for convenient use in "about"
boxes and the like:

   printf("%s",png_get_copyright(NULL));

Also, the PNG logo (in PNG format, of course) is supplied in the
files "pngbar.png" and "pngbar.jpg (88x31) and "pngnow.png" (98x31).

Libpng is OSI Certified Open Source Software.  OSI Certified Open Source is a
certification mark of the Open Source Initiative.

Glenn Randers-Pehrson
glennrp at users.sourceforge.net
December 22, 2014

--- end of LICENSE ---

-------------------------------------------------------------------------------

%% This notice is provided with respect to GIFLIB 5.1.1 & libungif 4.1.3, which may be 
included with JRE 8, JDK 8, and OpenJDK 8.

--- begin of LICENSE ---

The GIFLIB distribution is Copyright (c) 1997  Eric S. Raymond

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.

--- end of LICENSE ---

-------------------------------------------------------------------------------

%% This notice is provided with respect to Little CMS 2.7, which may be 
included with JRE 8, JDK 8, and OpenJDK 8.

--- begin of LICENSE ---

Little CMS
Copyright (c) 1998-2015 Marti Maria Saguer

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

--- end of LICENSE ---

-------------------------------------------------------------------------------

%% Lucida is a registered trademark or trademark of Bigelow & Holmes in the
U.S. and other countries.

-------------------------------------------------------------------------------

%% This notice is provided with respect to Mesa 3D Graphics Library v4.1,
which may be included with JRE 8, JDK 8, and OpenJDK 8 source distributions.

--- begin of LICENSE ---

 Mesa 3-D graphics library
 Version:  4.1

 Copyright (C) 1999-2002  Brian Paul   All Rights Reserved.

 Permission is hereby granted, free of charge, to any person obtaining a
 copy of this software and associated documentation files (the "Software"),
 to deal in the Software without restriction, including without limitation
 the rights to use, copy, modify, merge, publish, distribute, sublicense,
 and/or sell copies of the Software, and to permit persons to whom the
 Software is furnished to do so, subject to the following conditions:

 The above copyright notice and this permission notice shall be included
 in all copies or substantial portions of the Software.

 THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
 OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL
 BRIAN PAUL BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN
 AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
 CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

--- end of LICENSE ---

-------------------------------------------------------------------------------

%% This notice is provided with respect to Mozilla Network Security
Services (NSS), which is supplied with the JDK test suite in the OpenJDK
source code repository. It is licensed under Mozilla Public License (MPL),
version 2.0.

The NSS libraries are supplied in executable form, built from unmodified
NSS source code labeled with the "NSS_3_16_RTM" HG tag.

The NSS source code is available in the OpenJDK source code repository at:
    jdk/test/sun/security/pkcs11/nss/src

The NSS libraries are available in the OpenJDK source code repository at:
    jdk/test/sun/security/pkcs11/nss/lib

--- begin of LICENSE ---

Mozilla Public License Version 2.0
==================================

1. Definitions
--------------

1.1. "Contributor"
    means each individual or legal entity that creates, contributes to
    the creation of, or owns Covered Software.

1.2. "Contributor Version"
    means the combination of the Contributions of others (if any) used
    by a Contributor and that particular Contributor's Contribution.

1.3. "Contribution"
    means Covered Software of a particular Contributor.

1.4. "Covered Software"
    means Source Code Form to which the initial Contributor has attached
    the notice in Exhibit A, the Executable Form of such Source Code
    Form, and Modifications of such Source Code Form, in each case
    including portions thereof.

1.5. "Incompatible With Secondary Licenses"
    means

    (a) that the initial Contributor has attached the notice described
        in Exhibit B to the Covered Software; or

    (b) that the Covered Software was made available under the terms of
        version 1.1 or earlier of the License, but not also under the
        terms of a Secondary License.

1.6. "Executable Form"
    means any form of the work other than Source Code Form.

1.7. "Larger Work"
    means a work that combines Covered Software with other material, in 
    a separate file or files, that is not Covered Software.

1.8. "License"
    means this document.

1.9. "Licensable"
    means having the right to grant, to the maximum extent possible,
    whether at the time of the initial grant or subsequently, any and
    all of the rights conveyed by this License.

1.10. "Modifications"
    means any of the following:

    (a) any file in Source Code Form that results from an addition to,
        deletion from, or modification of the contents of Covered
        Software; or

    (b) any new file in Source Code Form that contains any Covered
        Software.

1.11. "Patent Claims" of a Contributor
    means any patent claim(s), including without limitation, method,
    process, and apparatus claims, in any patent Licensable by such
    Contributor that would be infringed, but for the grant of the
    License, by the making, using, selling, offering for sale, having
    made, import, or transfer of either its Contributions or its
    Contributor Version.

1.12. "Secondary License"
    means either the GNU General Public License, Version 2.0, the GNU
    Lesser General Public License, Version 2.1, the GNU Affero General
    Public License, Version 3.0, or any later versions of those
    licenses.

1.13. "Source Code Form"
    means the form of the work preferred for making modifications.

1.14. "You" (or "Your")
    means an individual or a legal entity exercising rights under this
    License. For legal entities, "You" includes any entity that
    controls, is controlled by, or is under common control with You. For
    purposes of this definition, "control" means (a) the power, direct
    or indirect, to cause the direction or management of such entity,
    whether by contract or otherwise, or (b) ownership of more than
    fifty percent (50%) of the outstanding shares or beneficial
    ownership of such entity.

2. License Grants and Conditions
--------------------------------

2.1. Grants

Each Contributor hereby grants You a world-wide, royalty-free,
non-exclusive license:

(a) under intellectual property rights (other than patent or trademark)
    Licensable by such Contributor to use, reproduce, make available,
    modify, display, perform, distribute, and otherwise exploit its
    Contributions, either on an unmodified basis, with Modifications, or
    as part of a Larger Work; and

(b) under Patent Claims of such Contributor to make, use, sell, offer
    for sale, have made, import, and otherwise transfer either its
    Contributions or its Contributor Version.

2.2. Effective Date

The licenses granted in Section 2.1 with respect to any Contribution
become effective for each Contribution on the date the Contributor first
distributes such Contribution.

2.3. Limitations on Grant Scope

The licenses granted in this Section 2 are the only rights granted under
this License. No additional rights or licenses will be implied from the
distribution or licensing of Covered Software under this License.
Notwithstanding Section 2.1(b) above, no patent license is granted by a
Contributor:

(a) for any code that a Contributor has removed from Covered Software;
    or

(b) for infringements caused by: (i) Your and any other third party's
    modifications of Covered Software, or (ii) the combination of its
    Contributions with other software (except as part of its Contributor
    Version); or

(c) under Patent Claims infringed by Covered Software in the absence of
    its Contributions.

This License does not grant any rights in the trademarks, service marks,
or logos of any Contributor (except as may be necessary to comply with
the notice requirements in Section 3.4).

2.4. Subsequent Licenses

No Contributor makes additional grants as a result of Your choice to
distribute the Covered Software under a subsequent version of this
License (see Section 10.2) or under the terms of a Secondary License (if
permitted under the terms of Section 3.3).

2.5. Representation

Each Contributor represents that the Contributor believes its
Contributions are its original creation(s) or it has sufficient rights
to grant the rights to its Contributions conveyed by this License.

2.6. Fair Use

This License is not intended to limit any rights You have under
applicable copyright doctrines of fair use, fair dealing, or other
equivalents.

2.7. Conditions

Sections 3.1, 3.2, 3.3, and 3.4 are conditions of the licenses granted
in Section 2.1.

3. Responsibilities
-------------------

3.1. Distribution of Source Form

All distribution of Covered Software in Source Code Form, including any
Modifications that You create or to which You contribute, must be under
the terms of this License. You must inform recipients that the Source
Code Form of the Covered Software is governed by the terms of this
License, and how they can obtain a copy of this License. You may not
attempt to alter or restrict the recipients' rights in the Source Code
Form.

3.2. Distribution of Executable Form

If You distribute Covered Software in Executable Form then:

(a) such Covered Software must also be made available in Source Code
    Form, as described in Section 3.1, and You must inform recipients of
    the Executable Form how they can obtain a copy of such Source Code
    Form by reasonable means in a timely manner, at a charge no more
    than the cost of distribution to the recipient; and

(b) You may distribute such Executable Form under the terms of this
    License, or sublicense it under different terms, provided that the
    license for the Executable Form does not attempt to limit or alter
    the recipients' rights in the Source Code Form under this License.

3.3. Distribution of a Larger Work

You may create and distribute a Larger Work under terms of Your choice,
provided that You also comply with the requirements of this License for
the Covered Software. If the Larger Work is a combination of Covered
Software with a work governed by one or more Secondary Licenses, and the
Covered Software is not Incompatible With Secondary Licenses, this
License permits You to additionally distribute such Covered Software
under the terms of such Secondary License(s), so that the recipient of
the Larger Work may, at their option, further distribute the Covered
Software under the terms of either this License or such Secondary
License(s).

3.4. Notices

You may not remove or alter the substance of any license notices
(including copyright notices, patent notices, disclaimers of warranty,
or limitations of liability) contained within the Source Code Form of
the Covered Software, except that You may alter any license notices to
the extent required to remedy known factual inaccuracies.

3.5. Application of Additional Terms

You may choose to offer, and to charge a fee for, warranty, support,
indemnity or liability obligations to one or more recipients of Covered
Software. However, You may do so only on Your own behalf, and not on
behalf of any Contributor. You must make it absolutely clear that any
such warranty, support, indemnity, or liability obligation is offered by
You alone, and You hereby agree to indemnify every Contributor for any
liability incurred by such Contributor as a result of warranty, support,
indemnity or liability terms You offer. You may include additional
disclaimers of warranty and limitations of liability specific to any
jurisdiction.

4. Inability to Comply Due to Statute or Regulation
---------------------------------------------------

If it is impossible for You to comply with any of the terms of this
License with respect to some or all of the Covered Software due to
statute, judicial order, or regulation then You must: (a) comply with
the terms of this License to the maximum extent possible; and (b)
describe the limitations and the code they affect. Such description must
be placed in a text file included with all distributions of the Covered
Software under this License. Except to the extent prohibited by statute
or regulation, such description must be sufficiently detailed for a
recipient of ordinary skill to be able to understand it.

5. Termination
--------------

5.1. The rights granted under this License will terminate automatically
if You fail to comply with any of its terms. However, if You become
compliant, then the rights granted under this License from a particular
Contributor are reinstated (a) provisionally, unless and until such
Contributor explicitly and finally terminates Your grants, and (b) on an
ongoing basis, if such Contributor fails to notify You of the
non-compliance by some reasonable means prior to 60 days after You have
come back into compliance. Moreover, Your grants from a particular
Contributor are reinstated on an ongoing basis if such Contributor
notifies You of the non-compliance by some reasonable means, this is the
first time You have received notice of non-compliance with this License
from such Contributor, and You become compliant prior to 30 days after
Your receipt of the notice.

5.2. If You initiate litigation against any entity by asserting a patent
infringement claim (excluding declaratory judgment actions,
counter-claims, and cross-claims) alleging that a Contributor Version
directly or indirectly infringes any patent, then the rights granted to
You by any and all Contributors for the Covered Software under Section
2.1 of this License shall terminate.

5.3. In the event of termination under Sections 5.1 or 5.2 above, all
end user license agreements (excluding distributors and resellers) which
have been validly granted by You or Your distributors under this License
prior to termination shall survive termination.

************************************************************************
*                                                                      *
*  6. Disclaimer of Warranty                                           *
*  -------------------------                                           *
*                                                                      *
*  Covered Software is provided under this License on an "as is"       *
*  basis, without warranty of any kind, either expressed, implied, or  *
*  statutory, including, without limitation, warranties that the       *
*  Covered Software is free of defects, merchantable, fit for a        *
*  particular purpose or non-infringing. The entire risk as to the     *
*  quality and performance of the Covered Software is with You.        *
*  Should any Covered Software prove defective in any respect, You     *
*  (not any Contributor) assume the cost of any necessary servicing,   *
*  repair, or correction. This disclaimer of warranty constitutes an   *
*  essential part of this License. No use of any Covered Software is   *
*  authorized under this License except under this disclaimer.         *
*                                                                      *
************************************************************************

************************************************************************
*                                                                      *
*  7. Limitation of Liability                                          *
*  --------------------------                                          *
*                                                                      *
*  Under no circumstances and under no legal theory, whether tort      *
*  (including negligence), contract, or otherwise, shall any           *
*  Contributor, or anyone who distributes Covered Software as          *
*  permitted above, be liable to You for any direct, indirect,         *
*  special, incidental, or consequential damages of any character      *
*  including, without limitation, damages for lost profits, loss of    *
*  goodwill, work stoppage, computer failure or malfunction, or any    *
*  and all other commercial damages or losses, even if such party      *
*  shall have been informed of the possibility of such damages. This   *
*  limitation of liability shall not apply to liability for death or   *
*  personal injury resulting from such party's negligence to the       *
*  extent applicable law prohibits such limitation. Some               *
*  jurisdictions do not allow the exclusion or limitation of           *
*  incidental or consequential damages, so this exclusion and          *
*  limitation may not apply to You.                                    *
*                                                                      *
************************************************************************

8. Litigation
-------------

Any litigation relating to this License may be brought only in the
courts of a jurisdiction where the defendant maintains its principal
place of business and such litigation shall be governed by laws of that
jurisdiction, without reference to its conflict-of-law provisions.
Nothing in this Section shall prevent a party's ability to bring
cross-claims or counter-claims.

9. Miscellaneous
----------------

This License represents the complete agreement concerning the subject
matter hereof. If any provision of this License is held to be
unenforceable, such provision shall be reformed only to the extent
necessary to make it enforceable. Any law or regulation which provides
that the language of a contract shall be construed against the drafter
shall not be used to construe this License against a Contributor.

10. Versions of the License
---------------------------

10.1. New Versions

Mozilla Foundation is the license steward. Except as provided in Section
10.3, no one other than the license steward has the right to modify or
publish new versions of this License. Each version will be given a
distinguishing version number.

10.2. Effect of New Versions

You may distribute the Covered Software under the terms of the version
of the License under which You originally received the Covered Software,
or under the terms of any subsequent version published by the license
steward.

10.3. Modified Versions

If you create software not governed by this License, and you want to
create a new license for such software, you may create and use a
modified version of this License if you rename the license and remove
any references to the name of the license steward (except to note that
such modified license differs from this License).

10.4. Distributing Source Code Form that is Incompatible With Secondary
Licenses

If You choose to distribute Source Code Form that is Incompatible With
Secondary Licenses under the terms of this version of the License, the
notice described in Exhibit B of this License must be attached.

Exhibit A - Source Code Form License Notice
-------------------------------------------

  This Source Code Form is subject to the terms of the Mozilla Public
  License, v. 2.0. If a copy of the MPL was not distributed with this
  file, You can obtain one at http://mozilla.org/MPL/2.0/.

If it is not possible or desirable to put the notice in a particular
file, then You may include the notice in a location (such as a LICENSE
file in a relevant directory) where a recipient would be likely to look
for such a notice.

You may add additional accurate notices of copyright ownership.

Exhibit B - "Incompatible With Secondary Licenses" Notice
---------------------------------------------------------

  This Source Code Form is "Incompatible With Secondary Licenses", as
  defined by the Mozilla Public License, v. 2.0.

--- end of LICENSE ---

-------------------------------------------------------------------------------

%% This notice is provided with respect to PC/SC Lite for Suse Linux v.1.1.1,
which may be included with JRE 8, JDK 8, and OpenJDK 8 on Linux and Solaris.

--- begin of LICENSE ---

Copyright (c) 1999-2004 David Corcoran <<EMAIL>>
Copyright (c) 1999-2004 Ludovic Rousseau <ludovic.rousseau (at) free.fr>
All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions
are met:

1. Redistributions of source code must retain the above copyright
   notice, this list of conditions and the following disclaimer.
2. Redistributions in binary form must reproduce the above copyright
   notice, this list of conditions and the following disclaimer in the
   documentation and/or other materials provided with the distribution.
3. All advertising materials mentioning features or use of this software
   must display the following acknowledgement:
     This product includes software developed by: 
      David Corcoran <<EMAIL>>
      http://www.linuxnet.com (MUSCLE)
4. The name of the author may not be used to endorse or promote products
   derived from this software without specific prior written permission.

Changes to this license can be made only by the copyright author with 
explicit written consent.

THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR
IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT,
INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT
NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF
THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

--- end of LICENSE ---

-------------------------------------------------------------------------------

%% This notice is provided with respect to PorterStemmer v4, which may be 
included with JRE 8, JDK 8, and OpenJDK 8.

--- begin of LICENSE ---

See: http://tartarus.org/~martin/PorterStemmer

The software is completely free for any purpose, unless notes at the head of
the program text indicates otherwise (which is rare). In any case, the notes
about licensing are never more restrictive than the BSD License.

In every case where the software is not written by me (Martin Porter), this
licensing arrangement has been endorsed by the contributor, and it is
therefore unnecessary to ask the contributor again to confirm it.

I have not asked any contributors (or their employers, if they have them) for
proofs that they have the right to distribute their software in this way.

--- end of LICENSE ---

-------------------------------------------------------------------------------

%% This notice is provided with respect to Relax NG Object/Parser v.20050510,
which may be included with JRE 8, JDK 8, and OpenJDK 8.

--- begin of LICENSE ---

Copyright (c) Kohsuke Kawaguchi

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions: The above copyright
notice and this permission notice shall be included in all copies or
substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

--- end of LICENSE ---

-------------------------------------------------------------------------------

%% This notice is provided with respect to RelaxNGCC v1.12, which may be 
included with JRE 8, JDK 8, and OpenJDK 8.

--- begin of LICENSE ---

Copyright (c) 2000-2003 Daisuke Okajima and Kohsuke Kawaguchi.  
All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

1. Redistributions of source code must retain the above copyright notice, this
   list of conditions and the following disclaimer.

2. Redistributions in binary form must reproduce the above copyright notice,
   this list of conditions and the following disclaimer in the documentation
   and/or other materials provided with the distribution.

3. The end-user documentation included with the redistribution, if any, must
   include the following acknowledgment:

    "This product includes software developed by Daisuke Okajima
    and Kohsuke Kawaguchi (http://relaxngcc.sf.net/)."

Alternately, this acknowledgment may appear in the software itself, if and
wherever such third-party acknowledgments normally appear.

4. The names of the copyright holders must not be used to endorse or promote
   products derived from this software without prior written permission. For
   written permission, please contact the copyright holders.

5. Products derived from this software may not be called "RELAXNGCC", nor may
  "RELAXNGCC" appear in their name, without prior written permission of the
  copyright holders.

THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED WARRANTIES,
INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND
FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.IN NO EVENT SHALL THE APACHE
SOFTWARE FOUNDATION OR ITS CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

--- end of LICENSE ---

-------------------------------------------------------------------------------

%% This notice is provided with respect to SAX 2.0.1, which may be included 
with JRE 8, JDK 8, and OpenJDK 8.

--- begin of LICENSE ---

 SAX is free!

 In fact, it's not possible to own a license to SAX, since it's been placed in
 the public domain.

 No Warranty

 Because SAX is released to the public domain, there is no warranty for the
 design or for the software implementation, to the extent permitted by
 applicable law. Except when otherwise stated in writing the copyright holders
 and/or other parties provide SAX "as is" without warranty of any kind, either
 expressed or implied, including, but not limited to, the implied warranties
 of merchantability and fitness for a particular purpose. The entire risk as
 to the quality and performance of SAX is with you. Should SAX prove
 defective, you assume the cost of all necessary servicing, repair or
 correction.

 In no event unless required by applicable law or agreed to in writing will
 any copyright holder, or any other party who may modify and/or redistribute
 SAX, be liable to you for damages, including any general, special, incidental
 or consequential damages arising out of the use or inability to use SAX
 (including but not limited to loss of data or data being rendered inaccurate
 or losses sustained by you or third parties or a failure of the SAX to
 operate with any other programs), even if such holder or other party has been
 advised of the possibility of such damages.

 Copyright Disclaimers 

 This page includes statements to that effect by David Megginson, who would
 have been able to claim copyright for the original work.  SAX 1.0

 Version 1.0 of the Simple API for XML (SAX), created collectively by the
 membership of the XML-DEV mailing list, is hereby released into the public
 domain.

 No one owns SAX: you may use it freely in both commercial and non-commercial
 applications, bundle it with your software distribution, include it on a
 CD-ROM, list the source code in a book, mirror the documentation at your own
 web site, or use it in any other way you see fit.

 David Megginson, <EMAIL>
 1998-05-11

 SAX 2.0 

 I hereby abandon any property rights to SAX 2.0 (the Simple API for XML), and
 release all of the SAX 2.0 source code, compiled code, and documentation
 contained in this distribution into the Public Domain. SAX comes with NO
 WARRANTY or guarantee of fitness for any purpose.

 David Megginson, <EMAIL>
 2000-05-05

--- end of LICENSE ---

-------------------------------------------------------------------------------

%% This notice is provided with respect to SoftFloat version 2b, which may be 
included with JRE 8, JDK 8, and OpenJDK 8 on Linux/ARM.

--- begin of LICENSE ---

Use of any of this software is governed by the terms of the license below:

SoftFloat was written by me, John R. Hauser. This work was made possible in 
part by the International Computer Science Institute, located at Suite 600, 
1947 Center Street, Berkeley, California 94704. Funding was partially 
provided by the National Science Foundation under grant MIP-9311980. The 
original version of this code was written as part of a project to build 
a fixed-point vector processor in collaboration with the University of 
California at Berkeley, overseen by Profs. Nelson Morgan and John Wawrzynek. 

THIS SOFTWARE IS DISTRIBUTED AS IS, FOR FREE. Although reasonable effort 
has been made to avoid it, THIS SOFTWARE MAY CONTAIN FAULTS THAT WILL AT 
TIMES RESULT IN INCORRECT BEHAVIOR. USE OF THIS SOFTWARE IS RESTRICTED TO 
PERSONS AND ORGANIZATIONS WHO CAN AND WILL TAKE FULL RESPONSIBILITY FOR ALL 
LOSSES, COSTS, OR OTHER PROBLEMS THEY INCUR DUE TO THE SOFTWARE, AND WHO 
FURTHERMORE EFFECTIVELY INDEMNIFY JOHN HAUSER AND THE INTERNATIONAL COMPUTER 
SCIENCE INSTITUTE (possibly via similar legal warning) AGAINST ALL LOSSES, 
COSTS, OR OTHER PROBLEMS INCURRED BY THEIR CUSTOMERS AND CLIENTS DUE TO THE 
SOFTWARE. 

Derivative works are acceptable, even for commercial purposes, provided 
that the minimal documentation requirements stated in the source code are 
satisfied. 

--- end of LICENSE ---

-------------------------------------------------------------------------------

%% This notice is provided with respect to Sparkle 1.5,
which may be included with JRE 8 on Mac OS X.

--- begin of LICENSE ---

Copyright (c) 2012 Sparkle.org and Andy Matuschak

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

--- end of LICENSE ---

-------------------------------------------------------------------------------

%% Portions licensed from Taligent, Inc.

-------------------------------------------------------------------------------

%% This notice is provided with respect to Thai Dictionary, which may be 
included with JRE 8, JDK 8, and OpenJDK 8.

--- begin of LICENSE ---

Copyright (C) 1982 The Royal Institute, Thai Royal Government.

Copyright (C) 1998 National Electronics and Computer Technology Center,
National Science and Technology Development Agency,
Ministry of Science Technology and Environment,
Thai Royal Government.

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

--- end of LICENSE ---

-------------------------------------------------------------------------------

%% This notice is provided with respect to Unicode 6.2.0 & CLDR 21.0.1
which may be included with JRE 8, JDK 8, and OpenJDK 8.

--- begin of LICENSE ---

Unicode Terms of Use

For the general privacy policy governing access to this site, see the Unicode
Privacy Policy. For trademark usage, see the Unicode® Consortium Name and
Trademark Usage Policy.

A. Unicode Copyright.
   1. Copyright © 1991-2013 Unicode, Inc. All rights reserved.

   2. Certain documents and files on this website contain a legend indicating
      that "Modification is permitted." Any person is hereby authorized,
      without fee, to modify such documents and files to create derivative
      works conforming to the Unicode® Standard, subject to Terms and
      Conditions herein.

    3. Any person is hereby authorized, without fee, to view, use, reproduce,
       and distribute all documents and files solely for informational
       purposes in the creation of products supporting the Unicode Standard,
       subject to the Terms and Conditions herein.

    4. Further specifications of rights and restrictions pertaining to the use
       of the particular set of data files known as the "Unicode Character
       Database" can be found in Exhibit 1.

    5. Each version of the Unicode Standard has further specifications of
       rights and restrictions of use. For the book editions (Unicode 5.0 and
       earlier), these are found on the back of the title page. The online
       code charts carry specific restrictions. All other files, including
       online documentation of the core specification for Unicode 6.0 and
       later, are covered under these general Terms of Use.

    6. No license is granted to "mirror" the Unicode website where a fee is
       charged for access to the "mirror" site.

    7. Modification is not permitted with respect to this document. All copies
       of this document must be verbatim.

B. Restricted Rights Legend. Any technical data or software which is licensed
   to the United States of America, its agencies and/or instrumentalities
   under this Agreement is commercial technical data or commercial computer
   software developed exclusively at private expense as defined in FAR 2.101,
   or DFARS 252.227-7014 (June 1995), as applicable. For technical data, use,
   duplication, or disclosure by the Government is subject to restrictions as
   set forth in DFARS 202.227-7015 Technical Data, Commercial and Items (Nov
   1995) and this Agreement. For Software, in accordance with FAR 12-212 or
   DFARS 227-7202, as applicable, use, duplication or disclosure by the
   Government is subject to the restrictions set forth in this Agreement.

C. Warranties and Disclaimers.
   1. This publication and/or website may include technical or typographical
      errors or other inaccuracies . Changes are periodically added to the
      information herein; these changes will be incorporated in new editions
      of the publication and/or website. Unicode may make improvements and/or
      changes in the product(s) and/or program(s) described in this
      publication and/or website at any time.

    2. If this file has been purchased on magnetic or optical media from
       Unicode, Inc. the sole and exclusive remedy for any claim will be
       exchange of the defective media within ninety (90) days of original
       purchase.

    3. EXCEPT AS PROVIDED IN SECTION C.2, THIS PUBLICATION AND/OR SOFTWARE IS
       PROVIDED "AS IS" WITHOUT WARRANTY OF ANY KIND EITHER EXPRESS, IMPLIED,
       OR STATUTORY, INCLUDING, BUT NOT LIMITED TO, ANY WARRANTIES OF
       MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, OR NON-INFRINGEMENT.
       UNICODE AND ITS LICENSORS ASSUME NO RESPONSIBILITY FOR ERRORS OR
       OMISSIONS IN THIS PUBLICATION AND/OR SOFTWARE OR OTHER DOCUMENTS WHICH
       ARE REFERENCED BY OR LINKED TO THIS PUBLICATION OR THE UNICODE WEBSITE.

D. Waiver of Damages. In no event shall Unicode or its licensors be liable for
   any special, incidental, indirect or consequential damages of any kind, or
   any damages whatsoever, whether or not Unicode was advised of the
   possibility of the damage, including, without limitation, those resulting
   from the following: loss of use, data or profits, in connection with the
   use, modification or distribution of this information or its derivatives.

E.Trademarks & Logos.
   1. The Unicode Word Mark and the Unicode Logo are trademarks of Unicode,
      Inc.  “The Unicode Consortium” and “Unicode, Inc.” are trade names of
      Unicode, Inc.  Use of the information and materials found on this
      website indicates your acknowledgement of Unicode, Inc.’s exclusive
      worldwide rights in the Unicode Word Mark, the Unicode Logo, and the
      Unicode trade names.

   2. The Unicode Consortium Name and Trademark Usage Policy (“Trademark
      Policy”) are incorporated herein by reference and you agree to abide by
      the provisions of the Trademark Policy, which may be changed from time
      to time in the sole discretion of Unicode, Inc.

   3. All third party trademarks referenced herein are the property of their
      respective owners.

Miscellaneous.
   1. Jurisdiction and Venue. This server is operated from a location in the
      State of California, United States of America. Unicode makes no
      representation that the materials are appropriate for use in other
      locations. If you access this server from other locations, you are
      responsible for compliance with local laws. This Agreement, all use of
      this site and any claims and damages resulting from use of this site are
      governed solely by the laws of the State of California without regard to
      any principles which would apply the laws of a different jurisdiction.
      The user agrees that any disputes regarding this site shall be resolved
      solely in the courts located in Santa Clara County, California. The user
      agrees said courts have personal jurisdiction and agree to waive any
      right to transfer the dispute to any other forum.

   2. Modification by Unicode.  Unicode shall have the right to modify this
      Agreement at any time by posting it to this site. The user may not
      assign any part of this Agreement without Unicode’s prior written
      consent.

   3. Taxes. The user agrees to pay any taxes arising from access to this
      website or use of the information herein, except for those based on
      Unicode’s net income.

   4. Severability.  If any provision of this Agreement is declared invalid or
      unenforceable, the remaining provisions of this Agreement shall remain
      in effect.

   5. Entire Agreement. This Agreement constitutes the entire agreement
      between the parties.

EXHIBIT 1
UNICODE, INC. LICENSE AGREEMENT - DATA FILES AND SOFTWARE

Unicode Data Files include all data files under the directories
http://www.unicode.org/Public/, http://www.unicode.org/reports/, and
http://www.unicode.org/cldr/data/. Unicode Data Files do not include PDF
online code charts under the directory http://www.unicode.org/Public/.
Software includes any source code published in the Unicode Standard or under
the directories http://www.unicode.org/Public/,
http://www.unicode.org/reports/, and http://www.unicode.org/cldr/data/.

NOTICE TO USER: Carefully read the following legal agreement. BY DOWNLOADING,
INSTALLING, COPYING OR OTHERWISE USING UNICODE INC.'S DATA FILES ("DATA
FILES"), AND/OR SOFTWARE ("SOFTWARE"), YOU UNEQUIVOCALLY ACCEPT, AND AGREE TO
BE BOUND BY, ALL OF THE TERMS AND CONDITIONS OF THIS AGREEMENT. IF YOU DO NOT
AGREE, DO NOT DOWNLOAD, INSTALL, COPY, DISTRIBUTE OR USE THE DATA FILES OR
SOFTWARE.

COPYRIGHT AND PERMISSION NOTICE

Copyright © 1991-2012 Unicode, Inc. All rights reserved. Distributed under the
Terms of Use in http://www.unicode.org/copyright.html.

Permission is hereby granted, free of charge, to any person obtaining a copy
of the Unicode data files and any associated documentation (the "Data Files")
or Unicode software and any associated documentation (the "Software") to deal
in the Data Files or Software without restriction, including without
limitation the rights to use, copy, modify, merge, publish, distribute, and/or
sell copies of the Data Files or Software, and to permit persons to whom the
Data Files or Software are furnished to do so, provided that (a) the above
copyright notice(s) and this permission notice appear with all copies of the
Data Files or Software, (b) both the above copyright notice(s) and this
permission notice appear in associated documentation, and (c) there is clear
notice in each modified Data File or in the Software as well as in the
documentation associated with the Data File(s) or Software that the data or
software has been modified.

THE DATA FILES AND SOFTWARE ARE PROVIDED "AS IS", WITHOUT WARRANTY OF ANY
KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT OF THIRD
PARTY RIGHTS. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR HOLDERS INCLUDED IN
THIS NOTICE BE LIABLE FOR ANY CLAIM, OR ANY SPECIAL INDIRECT OR CONSEQUENTIAL
DAMAGES, OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR
PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS
ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THE
DATA FILES OR SOFTWARE.

Except as contained in this notice, the name of a copyright holder shall not
be used in advertising or otherwise to promote the sale, use or other dealings
in these Data Files or Software without prior written authorization of the
copyright holder.

Unicode and the Unicode logo are trademarks of Unicode, Inc. in the United
States and other countries. All third party trademarks referenced herein are
the property of their respective owners.

--- end of LICENSE ---

-------------------------------------------------------------------------------

%% This notice is provided with respect to UPX v3.01, which may be included 
with JRE 8 on Windows.

--- begin of LICENSE ---

Use of any of this software is governed by the terms of the license below:


                 ooooo     ooo ooooooooo.   ooooooo  ooooo
                 `888'     `8' `888   `Y88.  `8888    d8'
                  888       8   888   .d88'    Y888..8P
                  888       8   888ooo88P'      `8888'
                  888       8   888            .8PY888.
                  `88.    .8'   888           d8'  `888b
                    `YbodP'    o888o        o888o  o88888o


                    The Ultimate Packer for eXecutables
          Copyright (c) 1996-2000 Markus Oberhumer & Laszlo Molnar
               http://wildsau.idv.uni-linz.ac.at/mfx/upx.html
                          http://www.nexus.hu/upx
                            http://upx.tsx.org


PLEASE CAREFULLY READ THIS LICENSE AGREEMENT, ESPECIALLY IF YOU PLAN
TO MODIFY THE UPX SOURCE CODE OR USE A MODIFIED UPX VERSION.


ABSTRACT
========

   UPX and UCL are copyrighted software distributed under the terms
   of the GNU General Public License (hereinafter the "GPL").

   The stub which is imbedded in each UPX compressed program is part
   of UPX and UCL, and contains code that is under our copyright. The
   terms of the GNU General Public License still apply as compressing
   a program is a special form of linking with our stub.

   As a special exception we grant the free usage of UPX for all
   executables, including commercial programs.
   See below for details and restrictions.


COPYRIGHT
=========

   UPX and UCL are copyrighted software. All rights remain with the authors.

   UPX is Copyright (C) 1996-2000 Markus Franz Xaver Johannes Oberhumer
   UPX is Copyright (C) 1996-2000 Laszlo Molnar

   UCL is Copyright (C) 1996-2000 Markus Franz Xaver Johannes Oberhumer


GNU GENERAL PUBLIC LICENSE
==========================

   UPX and the UCL library are free software; you can redistribute them
   and/or modify them under the terms of the GNU General Public License as
   published by the Free Software Foundation; either version 2 of
   the License, or (at your option) any later version.

   UPX and UCL are distributed in the hope that they will be useful,
   but WITHOUT ANY WARRANTY; without even the implied warranty of
   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
   GNU General Public License for more details.

   You should have received a copy of the GNU General Public License
   along with this program; see the file COPYING.


SPECIAL EXCEPTION FOR COMPRESSED EXECUTABLES
============================================

   The stub which is imbedded in each UPX compressed program is part
   of UPX and UCL, and contains code that is under our copyright. The
   terms of the GNU General Public License still apply as compressing
   a program is a special form of linking with our stub.

   Hereby Markus F.X.J. Oberhumer and Laszlo Molnar grant you special
   permission to freely use and distribute all UPX compressed programs
   (including commercial ones), subject to the following restrictions:

   1. You must compress your program with a completely unmodified UPX
      version; either with our precompiled version, or (at your option)
      with a self compiled version of the unmodified UPX sources as
      distributed by us.
   2. This also implies that the UPX stub must be completely unmodfied, i.e.
      the stub imbedded in your compressed program must be byte-identical
      to the stub that is produced by the official unmodified UPX version.
   3. The decompressor and any other code from the stub must exclusively get
      used by the unmodified UPX stub for decompressing your program at
      program startup. No portion of the stub may get read, copied,
      called or otherwise get used or accessed by your program.


ANNOTATIONS
===========

  - You can use a modified UPX version or modified UPX stub only for
    programs that are compatible with the GNU General Public License.

  - We grant you special permission to freely use and distribute all UPX
    compressed programs. But any modification of the UPX stub (such as,
    but not limited to, removing our copyright string or making your
    program non-decompressible) will immediately revoke your right to
    use and distribute a UPX compressed program.

  - UPX is not a software protection tool; by requiring that you use
    the unmodified UPX version for your proprietary programs we
    make sure that any user can decompress your program. This protects
    both you and your users as nobody can hide malicious code -
    any program that cannot be decompressed is highly suspicious
    by definition.

  - You can integrate all or part of UPX and UCL into projects that
    are compatible with the GNU GPL, but obviously you cannot grant
    any special exceptions beyond the GPL for our code in your project.

  - We want to actively support manufacturers of virus scanners and
    similar security software. Please contact us if you would like to
    incorporate parts of UPX or UCL into such a product.



Markus F.X.J. Oberhumer                   Laszlo Molnar
<EMAIL>        <EMAIL>

Linz, Austria, 25 Feb 2000

Additional License(s)

The UPX license file is at http://upx.sourceforge.net/upx-license.html.

--- end of LICENSE ---

-------------------------------------------------------------------------------

%% This notice is provided with respect to Xfree86-VidMode Extension 1.0,
which may be included with JRE 8, JDK 8, and OpenJDK 8 on Linux and Solaris.

--- begin of LICENSE ---

Version 1.1 of XFree86 ProjectLicence.

Copyright (C) 1994-2004 The XFree86 Project, Inc.    All rights reserved.

Permission is hereby granted, free of charge, to any person obtaining a copy of
this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicence, and/or sell
copies of the Software, and to permit persons to whom the Software is furnished
to do so,subject to the following conditions:

   1. Redistributions of source code must retain the above copyright
   notice,this list of conditions, and the following disclaimer.

   2. Redistributions in binary form must reproduce the above copyright notice,
   this list of conditions and the following disclaimer in the documentation
   and/or other materials provided with the distribution, and in the same place
   and form as other copyright, license and disclaimer information.

   3. The end-user documentation included with the redistribution, if any,must
   include the following acknowledgment: "This product includes
   software developed by The XFree86 Project, Inc (http://www.xfree86.org/) and
   its contributors", in the same place and form as other third-party
   acknowledgments. Alternately, this acknowledgment may appear in the software
   itself, in the same form and location as other such third-party
   acknowledgments.

    4. Except as contained in this notice, the name of The XFree86 Project,Inc
    shall not be used in advertising or otherwise to promote the sale, use
    or other dealings in this Software without prior written authorization from
    The XFree86 Project, Inc.

    THIS SOFTWARE IS PROVIDED ``AS IS'' AND ANY EXPRESSED OR IMPLIED
    WARRANTIES,INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
    MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO
    EVENT SHALL THE XFREE86 PROJECT, INC OR ITS CONTRIBUTORS BE LIABLE FOR ANY
    DIRECT, INDIRECT, INCIDENTAL,SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
    (INCLUDING, BUT NOT LIMITED TO,PROCUREMENT OF SUBSTITUTE GOODS OR
    SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
    CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
    LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
    OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH
    DAMAGE.  

--- end of LICENSE ---

-------------------------------------------------------------------------------

%% This notice is provided with respect to X Window System 6.8.2, which may be 
included with JRE 8, JDK 8, and OpenJDK 8 on Linux and Solaris.

--- begin of LICENSE ---

          Licenses
The X.Org Foundation March 2004

1. Introduction

The X.org Foundation X Window System distribution is a compilation of code and
documentation from many sources. This document is intended primarily as a
guide to the licenses used in the distribution: you must check each file
and/or package for precise redistribution terms. None-the-less, this summary
may be useful to many users. No software incorporating the XFree86 1.1 license
has been incorporated.

This document is based on the compilation from XFree86.

2. XFree86 License

XFree86 code without an explicit copyright is covered by the following
copyright/license:

Copyright (C) 1994-2003 The XFree86 Project, Inc. All Rights Reserved.

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
XFREE86 PROJECT BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

Except as contained in this notice, the name of the XFree86 Project shall not
be used in advertising or otherwise to promote the sale, use or other dealings
in this Software without prior written authorization from the XFree86 Project.

3. Other Licenses

Portions of code are covered by the following licenses/copyrights. See
individual files for the copyright dates.

3.1. X/MIT Copyrights

3.1.1. X Consortium

Copyright (C) <date> X Consortium

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE X
CONSORTIUM BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN
ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

Except as contained in this notice, the name of the X Consortium shall not be
used in advertising or otherwise to promote the sale, use or other dealings in
this Software without prior written authorization from the X Consortium.

X Window System is a trademark of X Consortium, Inc.

3.1.2. The Open Group

Copyright <date> The Open Group

Permission to use, copy, modify, distribute, and sell this software and its
documentation for any purpose is hereby granted without fee, provided that the
above copyright notice appear in all copies and that both that copyright
notice and this permission notice appear in supporting documentation.

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
OPEN GROUP BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN
ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

Except as contained in this notice, the name of The Open Group shall not be
used in advertising or otherwise to promote the sale, use or other dealings in
this Software without prior written authorization from The Open Group.  3.2.
Berkeley-based copyrights:

o
3.2.1. General

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

   1. Redistributions of source code must retain the above copyright notice,
   this list of conditions and the following disclaimer.

   2. Redistributions in binary form must reproduce the above copyright
   notice, this list of conditions and the following disclaimer in the
   documentation and/or other materials provided with the distribution.

   3. The name of the author may not be used to endorse or promote products
   derived from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR IMPLIED
WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO
EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR
BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER
IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
POSSIBILITY OF SUCH DAMAGE.  3.2.2. UCB/LBL

Copyright (c) 1993 The Regents of the University of California. All rights
reserved.

This software was developed by the Computer Systems Engineering group at
Lawrence Berkeley Laboratory under DARPA contract BG 91-66 and contributed to
Berkeley.

All advertising materials mentioning features or use of this software must
display the following acknowledgement: This product includes software
developed by the University of California, Lawrence Berkeley Laboratory.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

   1. Redistributions of source code must retain the above copyright notice,
   this list of conditions and the following disclaimer.

   2. Redistributions in binary form must reproduce the above copyright
   notice, this list of conditions and the following disclaimer in the
   documentation and/or other materials provided with the distribution.

   3. All advertising materials mentioning features or use of this software
   must display the following acknowledgement: This product includes software
   developed by the University of California, Berkeley and its contributors.

   4. Neither the name of the University nor the names of its contributors may
   be used to endorse or promote products derived from this software without
   specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND ANY
EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
DISCLAIMED. IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE FOR ANY
DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
(INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON
ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.  3.2.3. The
NetBSD Foundation, Inc.

Copyright (c) 2003 The NetBSD Foundation, Inc. All rights reserved.

This code is derived from software contributed to The NetBSD Foundation by Ben
Collver <<EMAIL>>

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

   1. Redistributions of source code must retain the above copyright notice,
   this list of conditions and the following disclaimer.

   2. Redistributions in binary form must reproduce the above copyright
   notice, this list of conditions and the following disclaimer in the
   documentation and/or other materials provided with the distribution.

   3. All advertising materials mentioning features or use of this software
   must display the following acknowledgement: This product includes software
   developed by the NetBSD Foundation, Inc. and its contributors.

   4. Neither the name of The NetBSD Foundation nor the names of its
   contributors may be used to endorse or promote products derived from this
   software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE NETBSD FOUNDATION, INC. AND CONTRIBUTORS ``AS
IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
DISCLAIMED. IN NO EVENT SHALL THE FOUNDATION OR CONTRIBUTORS BE LIABLE FOR ANY
DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
(INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON
ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.  3.2.4. Theodore
Ts'o.

Copyright Theodore Ts'o, 1994, 1995, 1996, 1997, 1998, 1999. All rights
reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

   1. Redistributions of source code must retain the above copyright notice,
   and the entire permission notice in its entirety, including the disclaimer
   of warranties.

   2. Redistributions in binary form must reproduce the above copyright
   notice, this list of conditions and the following disclaimer in the
   documentation and/or other materials provided with the distribution.

   3. he name of the author may not be used to endorse or promote products
   derived from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES,
INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND
FITNESS FOR A PARTICULAR PURPOSE, ALL OF WHICH ARE HEREBY DISCLAIMED. IN NO
EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR
BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER
IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF NOT ADVISED OF THE
POSSIBILITY OF SUCH DAMAGE.  3.2.5. Theo de Raadt and Damien Miller

Copyright (c) 1995,1999 Theo de Raadt. All rights reserved. Copyright (c)
2001-2002 Damien Miller. All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

   1. Redistributions of source code must retain the above copyright notice,
   this list of conditions and the following disclaimer.

   2. Redistributions in binary form must reproduce the above copyright
   notice, this list of conditions and the following disclaimer in the
   documentation and/or other materials provided with the distribution.

THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR IMPLIED
WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO
EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR
BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER
IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
POSSIBILITY OF SUCH DAMAGE.  3.2.6. Todd C. Miller

Copyright (c) 1998 Todd C. Miller <<EMAIL>>

Permission to use, copy, modify, and distribute this software for any purpose
with or without fee is hereby granted, provided that the above copyright
notice and this permission notice appear in all copies.

THE SOFTWARE IS PROVIDED "AS IS" AND TODD C. MILLER DISCLAIMS ALL WARRANTIES
WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL TODD C. MILLER BE LIABLE FOR
ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION
OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN
CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.  3.2.7. Thomas
Winischhofer

Copyright (C) 2001-2004 Thomas Winischhofer

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

   1. Redistributions of source code must retain the above copyright notice,
   this list of conditions and the following disclaimer.

   2. Redistributions in binary form must reproduce the above copyright
   notice, this list of conditions and the following disclaimer in the
   documentation and/or other materials provided with the distribution.

   3. The name of the author may not be used to endorse or promote products
   derived from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESSED OR IMPLIED
WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO
EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR
BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER
IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
POSSIBILITY OF SUCH DAMAGE.  3.3. NVIDIA Corp

Copyright (c) 1996 NVIDIA, Corp. All rights reserved.

NOTICE TO USER: The source code is copyrighted under U.S. and international
laws. NVIDIA, Corp. of Sunnyvale, California owns the copyright and as design
patents pending on the design and interface of the NV chips. Users and
possessors of this source code are hereby granted a nonexclusive, royalty-free
copyright and design patent license to use this code in individual and
commercial software.

Any use of this source code must include, in the user documentation and
internal comments to the code, notices to the end user as follows:

Copyright (c) 1996 NVIDIA, Corp. NVIDIA design patents pending in the U.S. and
foreign countries.

NVIDIA, CORP. MAKES NO REPRESENTATION ABOUT THE SUITABILITY OF THIS SOURCE
CODE FOR ANY PURPOSE. IT IS PROVIDED "AS IS" WITHOUT EXPRESS OR IMPLIED
WARRANTY OF ANY KIND. NVIDIA, CORP. DISCLAIMS ALL WARRANTIES WITH REGARD TO
THIS SOURCE CODE, INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND
FITNESS FOR A PARTICULAR PURPOSE. IN NO EVENT SHALL NVIDIA, CORP. BE LIABLE
FOR ANY SPECIAL, INDIRECT, INCIDENTAL, OR CONSEQUENTIAL DAMAGES, OR ANY
DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN
CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOURCE CODE.  3.4. GLX Public
License

GLX PUBLIC LICENSE (Version 1.0 (2/11/99)) ("License")

Subject to any third party claims, Silicon Graphics, Inc. ("SGI") hereby
grants permission to Recipient (defined below), under Recipient's copyrights
in the Original Software (defined below), to use, copy, modify, merge,
publish, distribute, sublicense and/or sell copies of Subject Software
(defined below), and to permit persons to whom the Subject Software is
furnished in accordance with this License to do the same, subject to all of
the following terms and conditions, which Recipient accepts by engaging in any
such use, copying, modifying, merging, publishing, distributing, sublicensing
or selling:

1. Definitions.

    (a) "Original Software" means source code of computer software code which
    is described in Exhibit A as Original Software.

    (b) "Modifications" means any addition to or deletion from the substance
    or structure of either the Original Software or any previous
    Modifications. When Subject Software is released as a series of files, a
    Modification means (i) any addition to or deletion from the contents of a
    file containing Original Software or previous Modifications and (ii) any
    new file that contains any part of the Original Code or previous
    Modifications.

    (c) "Subject Software" means the Original Software or Modifications or the
    combination of the Original Software and Modifications, or portions of any
    of the foregoing.

    (d) "Recipient" means an individual or a legal entity exercising rights
    under, and complying with all of the terms of, this License. For legal
    entities, "Recipient" includes any entity which controls, is controlled
    by, or is under common control with Recipient. For purposes of this
    definition, "control" of an entity means (a) the power, direct or
    indirect, to direct or manage such entity, or (b) ownership of fifty
    percent (50%) or more of the outstanding shares or beneficial ownership of
    such entity.

2. Redistribution of Source Code Subject to These Terms. Redistributions of
Subject Software in source code form must retain the notice set forth in
Exhibit A, below, in every file. A copy of this License must be included in
any documentation for such Subject Software where the recipients' rights
relating to Subject Software are described. Recipient may distribute the
source code version of Subject Software under a license of Recipient's choice,
which may contain terms different from this License, provided that (i)
Recipient is in compliance with the terms of this License, and (ii) the
license terms include this Section 2 and Sections 3, 4, 7, 8, 10, 12 and 13 of
this License, which terms may not be modified or superseded by any other terms
of such license. If Recipient distributes the source code version under a
different license Recipient must make it absolutely clear that any terms which
differ from this License are offered by Recipient alone, not by SGI. Recipient
hereby agrees to indemnify SGI for any liability incurred by SGI as a result
of any such terms Recipient offers.

3. Redistribution in Executable Form. The notice set forth in Exhibit A must
be conspicuously included in any notice in an executable version of Subject
Software, related documentation or collateral in which Recipient describes the
user's rights relating to the Subject Software. Recipient may distribute the
executable version of Subject Software under a license of Recipient's choice,
which may contain terms different from this License, provided that (i)
Recipient is in compliance with the terms of this License, and (ii) the
license terms include this Section 3 and Sections 4, 7, 8, 10, 12 and 13 of
this License, which terms may not be modified or superseded by any other terms
of such license. If Recipient distributes the executable version under a
different license Recipient must make it absolutely clear that any terms which
differ from this License are offered by Recipient alone, not by SGI. Recipient
hereby agrees to indemnify SGI for any liability incurred by SGI as a result
of any such terms Recipient offers.

4. Termination. This License and the rights granted hereunder will terminate
automatically if Recipient fails to comply with terms herein and fails to cure
such breach within 30 days of the breach. Any sublicense to the Subject
Software which is properly granted shall survive any termination of this
License absent termination by the terms of such sublicense. Provisions which,
by their nature, must remain in effect beyond the termination of this License
shall survive.

5. No Trademark Rights. This License does not grant any rights to use any
trade name, trademark or service mark whatsoever. No trade name, trademark or
service mark of SGI may be used to endorse or promote products derived from
the Subject Software without prior written permission of SGI.

6. No Other Rights. This License does not grant any rights with respect to the
OpenGL API or to any software or hardware implementation thereof or to any
other software whatsoever, nor shall any other rights or licenses not
expressly granted hereunder arise by implication, estoppel or otherwise with
respect to the Subject Software. Title to and ownership of the Original
Software at all times remains with SGI. All rights in the Original Software
not expressly granted under this License are reserved.

7. Compliance with Laws; Non-Infringement. Recipient shall comply with all
applicable laws and regulations in connection with use and distribution of the
Subject Software, including but not limited to, all export and import control
laws and regulations of the U.S. government and other countries. Recipient may
not distribute Subject Software that (i) in any way infringes (directly or
contributorily) the rights (including patent, copyright, trade secret,
trademark or other intellectual property rights of any kind) of any other
person or entity or (ii) breaches any representation or warranty, express,
implied or statutory, which under any applicable law it might be deemed to
have been distributed.

8. Claims of Infringement. If Recipient at any time has knowledge of any one
or more third party claims that reproduction, modification, use, distribution,
import or sale of Subject Software (including particular functionality or code
incorporated in Subject Software) infringes the third party's intellectual
property rights, Recipient must place in a well-identified web page bearing
the title "LEGAL" a description of each such claim and a description of the
party making each such claim in sufficient detail that a user of the Subject
Software will know whom to contact regarding the claim. Also, upon gaining
such knowledge of any such claim, Recipient must conspicuously include the URL
for such web page in the Exhibit A notice required under Sections 2 and 3,
above, and in the text of any related documentation, license agreement or
collateral in which Recipient describes end user's rights relating to the
Subject Software. If Recipient obtains such knowledge after it makes Subject
Software available to any other person or entity, Recipient shall take other
steps (such as notifying appropriate mailing lists or newsgroups) reasonably
calculated to inform those who received the Subject Software that new
knowledge has been obtained.

9. DISCLAIMER OF WARRANTY. SUBJECT SOFTWARE IS PROVIDED ON AN "AS IS" BASIS,
WITHOUT WARRANTY OF ANY KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING, WITHOUT
LIMITATION, WARRANTIES THAT THE SUBJECT SOFTWARE IS FREE OF DEFECTS,
MERCHANTABLE, FIT FOR A PARTICULAR PURPOSE OR NON- INFRINGING. SGI ASSUMES NO
RISK AS TO THE QUALITY AND PERFORMANCE OF THE SOFTWARE. SHOULD ANY SOFTWARE
PROVE DEFECTIVE IN ANY RESPECT, SGI ASSUMES NO COST OR LIABILITY FOR ANY
SERVICING, REPAIR OR CORRECTION. THIS DISCLAIMER OF WARRANTY CONSTITUTES AN
ESSENTIAL PART OF THIS LICENSE. NO USE OF ANY SUBJECT SOFTWARE IS AUTHORIZED
HEREUNDER EXCEPT UNDER THIS DISCLAIMER.

10. LIMITATION OF LIABILITY. UNDER NO CIRCUMSTANCES AND UNDER NO LEGAL THEORY,
WHETHER TORT (INCLUDING, WITHOUT LIMITATION, NEGLIGENCE OR STRICT LIABILITY),
CONTRACT, OR OTHERWISE, SHALL SGI OR ANY SGI LICENSOR BE LIABLE FOR ANY
DIRECT, INDIRECT, SPECIAL, INCIDENTAL, OR CONSEQUENTIAL DAMAGES OF ANY
CHARACTER INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF GOODWILL, WORK
STOPPAGE, LOSS OF DATA, COMPUTER FAILURE OR MALFUNCTION, OR ANY AND ALL OTHER
COMMERCIAL DAMAGES OR LOSSES, EVEN IF SUCH PARTY SHALL HAVE BEEN INFORMED OF
THE POSSIBILITY OF SUCH DAMAGES. THIS LIMITATION OF LIABILITY SHALL NOT APPLY
TO LIABILITY FOR DEATH OR PERSONAL INJURY RESULTING FROM SGI's NEGLIGENCE TO
THE EXTENT APPLICABLE LAW PROHIBITS SUCH LIMITATION. SOME JURISDICTIONS DO NOT
ALLOW THE EXCLUSION OR LIMITATION OF INCIDENTAL OR CONSEQUENTIAL DAMAGES, SO
THAT EXCLUSION AND LIMITATION MAY NOT APPLY TO RECIPIENT.

11. Indemnity. Recipient shall be solely responsible for damages arising,
directly or indirectly, out of its utilization of rights under this License.
Recipient will defend, indemnify and hold harmless Silicon Graphics, Inc. from
and against any loss, liability, damages, costs or expenses (including the
payment of reasonable attorneys fees) arising out of Recipient's use,
modification, reproduction and distribution of the Subject Software or out of
any representation or warranty made by Recipient.

12. U.S. Government End Users. The Subject Software is a "commercial item"
consisting of "commercial computer software" as such terms are defined in
title 48 of the Code of Federal Regulations and all U.S. Government End Users
acquire only the rights set forth in this License and are subject to the terms
of this License.

13. Miscellaneous. This License represents the complete agreement concerning
subject matter hereof. If any provision of this License is held to be
unenforceable, such provision shall be reformed so as to achieve as nearly as
possible the same economic effect as the original provision and the remainder
of this License will remain in effect. This License shall be governed by and
construed in accordance with the laws of the United States and the State of
California as applied to agreements entered into and to be performed entirely
within California between California residents. Any litigation relating to
this License shall be subject to the exclusive jurisdiction of the Federal
Courts of the Northern District of California (or, absent subject matter
jurisdiction in such courts, the courts of the State of California), with
venue lying exclusively in Santa Clara County, California, with the losing
party responsible for costs, including without limitation, court costs and
reasonable attorneys fees and expenses. The application of the United Nations
Convention on Contracts for the International Sale of Goods is expressly
excluded. Any law or regulation which provides that the language of a contract
shall be construed against the drafter shall not apply to this License.

Exhibit A

The contents of this file are subject to Sections 2, 3, 4, 7, 8, 10, 12 and 13
of the GLX Public License Version 1.0 (the "License"). You may not use this
file except in compliance with those sections of the License. You may obtain a
copy of the License at Silicon Graphics, Inc., attn: Legal Services, 2011 N.
Shoreline Blvd., Mountain View, CA 94043 or at
http://www.sgi.com/software/opensource/glx/license.html.

Software distributed under the License is distributed on an "AS IS" basis. ALL
WARRANTIES ARE DISCLAIMED, INCLUDING, WITHOUT LIMITATION, ANY IMPLIED
WARRANTIES OF MERCHANTABILITY, OF FITNESS FOR A PARTICULAR PURPOSE OR OF NON-
INFRINGEMENT. See the License for the specific language governing rights and
limitations under the License.

The Original Software is GLX version 1.2 source code, released February, 1999.
The developer of the Original Software is Silicon Graphics, Inc. Those
portions of the Subject Software created by Silicon Graphics, Inc. are
Copyright (c) 1991-9 Silicon Graphics, Inc. All Rights Reserved.  3.5. CID
Font Code Public License

CID FONT CODE PUBLIC LICENSE (Version 1.0 (3/31/99))("License")

Subject to any applicable third party claims, Silicon Graphics, Inc. ("SGI")
hereby grants permission to Recipient (defined below), under SGI's copyrights
in the Original Software (defined below), to use, copy, modify, merge,
publish, distribute, sublicense and/or sell copies of Subject Software
(defined below) in both source code and executable form, and to permit persons
to whom the Subject Software is furnished in accordance with this License to
do the same, subject to all of the following terms and conditions, which
Recipient accepts by engaging in any such use, copying, modifying, merging,
publication, distributing, sublicensing or selling:

1. Definitions.

    a. "Original Software" means source code of computer software code that is
    described in Exhibit A as Original Software.

    b. "Modifications" means any addition to or deletion from the substance or
    structure of either the Original Software or any previous Modifications.
    When Subject Software is released as a series of files, a Modification
    means (i) any addition to or deletion from the contents of a file
    containing Original Software or previous Modifications and (ii) any new
    file that contains any part of the Original Code or previous
    Modifications.

    c. "Subject Software" means the Original Software or Modifications or the
    combination of the Original Software and Modifications, or portions of any
    of the foregoing.

    d. "Recipient" means an individual or a legal entity exercising rights
    under the terms of this License. For legal entities, "Recipient" includes
    any entity that controls, is controlled by, or is under common control
    with Recipient. For purposes of this definition, "control" of an entity
    means (i) the power, direct or indirect, to direct or manage such entity,
    or (ii) ownership of fifty percent (50%) or more of the outstanding shares
    or beneficial ownership of such entity.

    e. "Required Notice" means the notice set forth in Exhibit A to this
    License.

    f. "Accompanying Technology" means any software or other technology that
    is not a Modification and that is distributed or made publicly available
    by Recipient with the Subject Software. Separate software files that do
    not contain any Original Software or any previous Modification shall not
    be deemed a Modification, even if such software files are aggregated as
    part of a product, or in any medium of storage, with any file that does
    contain Original Software or any previous Modification.

2. License Terms. All distribution of the Subject Software must be made
subject to the terms of this License. A copy of this License and the Required
Notice must be included in any documentation for Subject Software where
Recipient's rights relating to Subject Software and/or any Accompanying
Technology are described. Distributions of Subject Software in source code
form must also include the Required Notice in every file distributed. In
addition, a ReadMe file entitled "Important Legal Notice" must be distributed
with each distribution of one or more files that incorporate Subject Software.
That file must be included with distributions made in both source code and
executable form. A copy of the License and the Required Notice must be
included in that file. Recipient may distribute Accompanying Technology under
a license of Recipient's choice, which may contain terms different from this
License, provided that (i) Recipient is in compliance with the terms of this
License, (ii) such other license terms do not modify or supersede the terms of
this License as applicable to the Subject Software, (iii) Recipient hereby
indemnifies SGI for any liability incurred by SGI as a result of the
distribution of Accompanying Technology or the use of other license terms.

3. Termination. This License and the rights granted hereunder will terminate
automatically if Recipient fails to comply with terms herein and fails to cure
such breach within 30 days of the breach. Any sublicense to the Subject
Software that is properly granted shall survive any termination of this
License absent termination by the terms of such sublicense. Provisions which,
by their nature, must remain in effect beyond the termination of this License
shall survive.

4. Trademark Rights. This License does not grant any rights to use any trade
name, trademark or service mark whatsoever. No trade name, trademark or
service mark of SGI may be used to endorse or promote products derived from or
incorporating any Subject Software without prior written permission of SGI.

5. No Other Rights. No rights or licenses not expressly granted hereunder
shall arise by implication, estoppel or otherwise. Title to and ownership of
the Original Software at all times remains with SGI. All rights in the
Original Software not expressly granted under this License are reserved.

6. Compliance with Laws; Non-Infringement. Recipient shall comply with all
applicable laws and regulations in connection with use and distribution of the
Subject Software, including but not limited to, all export and import control
laws and regulations of the U.S. government and other countries. Recipient may
not distribute Subject Software that (i) in any way infringes (directly or
contributorily) the rights (including patent, copyright, trade secret,
trademark or other intellectual property rights of any kind) of any other
person or entity, or (ii) breaches any representation or warranty, express,
implied or statutory, which under any applicable law it might be deemed to
have been distributed.

7. Claims of Infringement. If Recipient at any time has knowledge of any one
or more third party claims that reproduction, modification, use, distribution,
import or sale of Subject Software (including particular functionality or code
incorporated in Subject Software) infringes the third party's intellectual
property rights, Recipient must place in a well-identified web page bearing
the title "LEGAL" a description of each such claim and a description of the
party making each such claim in sufficient detail that a user of the Subject
Software will know whom to contact regarding the claim. Also, upon gaining
such knowledge of any such claim, Recipient must conspicuously include the URL
for such web page in the Required Notice, and in the text of any related
documentation, license agreement or collateral in which Recipient describes
end user's rights relating to the Subject Software. If Recipient obtains such
knowledge after it makes Subject Software available to any other person or
entity, Recipient shall take other steps (such as notifying appropriate
mailing lists or newsgroups) reasonably calculated to provide such knowledge
to those who received the Subject Software.

8. DISCLAIMER OF WARRANTY. SUBJECT SOFTWARE IS PROVIDED ON AN "AS IS" BASIS,
WITHOUT WARRANTY OF ANY KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING, WITHOUT
LIMITATION, WARRANTIES THAT THE SUBJECT SOFTWARE IS FREE OF DEFECTS,
MERCHANTABLE, FIT FOR A PARTICULAR PURPOSE OR NON-INFRINGING. SGI ASSUMES NO
RISK AS TO THE QUALITY AND PERFORMANCE OF THE SOFTWARE. SHOULD ANY SOFTWARE
PROVE DEFECTIVE IN ANY RESPECT, SGI ASSUMES NO COST OR LIABILITY FOR ANY
SERVICING, REPAIR OR CORRECTION. THIS DISCLAIMER OF WARRANTY CONSTITUTES AN
ESSENTIAL PART OF THIS LICENSE. NO USE OF ANY SUBJECT SOFTWARE IS AUTHORIZED
HEREUNDER EXCEPT UNDER THIS DISCLAIMER.

9. LIMITATION OF LIABILITY. UNDER NO CIRCUMSTANCES AND UNDER NO LEGAL THEORY,
WHETHER TORT (INCLUDING, WITHOUT LIMITATION, NEGLIGENCE OR STRICT LIABILITY),
CONTRACT, OR OTHERWISE, SHALL SGI OR ANY SGI LICENSOR BE LIABLE FOR ANY CLAIM,
DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR
OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SUBJECT SOFTWARE OR
THE USE OR OTHER DEALINGS IN THE SUBJECT SOFTWARE. SOME JURISDICTIONS DO NOT
ALLOW THE EXCLUSION OR LIMITATION OF CERTAIN DAMAGES, SO THIS EXCLUSION AND
LIMITATION MAY NOT APPLY TO RECIPIENT TO THE EXTENT SO DISALLOWED.

10. Indemnity. Recipient shall be solely responsible for damages arising,
directly or indirectly, out of its utilization of rights under this License.
Recipient will defend, indemnify and hold SGI and its successors and assigns
harmless from and against any loss, liability, damages, costs or expenses
(including the payment of reasonable attorneys fees) arising out of
(Recipient's use, modification, reproduction and distribution of the Subject
Software or out of any representation or warranty made by Recipient.

11. U.S. Government End Users. The Subject Software is a "commercial item"
consisting of "commercial computer software" as such terms are defined in
title 48 of the Code of Federal Regulations and all U.S. Government End Users
acquire only the rights set forth in this License and are subject to the terms
of this License.

12. Miscellaneous. This License represents the complete agreement concerning
subject matter hereof. If any provision of this License is held to be
unenforceable by any judicial or administrative authority having proper
jurisdiction with respect thereto, such provision shall be reformed so as to
achieve as nearly as possible the same economic effect as the original
provision and the remainder of this License will remain in effect. This
License shall be governed by and construed in accordance with the laws of the
United States and the State of California as applied to agreements entered
into and to be performed entirely within California between California
residents. Any litigation relating to this License shall be subject to the
exclusive jurisdiction of the Federal Courts of the Northern District of
California (or, absent subject matter jurisdiction in such courts, the courts
of the State of California), with venue lying exclusively in Santa Clara
County, California, with the losing party responsible for costs, including
without limitation, court costs and reasonable attorneys fees and expenses.
The application of the United Nations Convention on Contracts for the
International Sale of Goods is expressly excluded. Any law or regulation that
provides that the language of a contract shall be construed against the
drafter shall not apply to this License.

Exhibit A

Copyright (c) 1994-1999 Silicon Graphics, Inc.

The contents of this file are subject to the CID Font Code Public License
Version 1.0 (the "License"). You may not use this file except in compliance
with the License. You may obtain a copy of the License at Silicon Graphics,
Inc., attn: Legal Services, 2011 N. Shoreline Blvd., Mountain View, CA 94043
or at http://www.sgi.com/software/opensource/cid/license.html

Software distributed under the License is distributed on an "AS IS" basis. ALL
WARRANTIES ARE DISCLAIMED, INCLUDING, WITHOUT LIMITATION, ANY IMPLIED
WARRANTIES OF MERCHANTABILITY, OF FITNESS FOR A PARTICULAR PURPOSE OR OF
NON-INFRINGEMENT. See the License for the specific language governing rights
and limitations under the License.

The Original Software (as defined in the License) is CID font code that was
developed by Silicon Graphics, Inc. Those portions of the Subject Software (as
defined in the License) that were created by Silicon Graphics, Inc. are
Copyright (c) 1994-1999 Silicon Graphics, Inc. All Rights Reserved.

[NOTE: When using this text in connection with Subject Software delivered
solely in object code form, Recipient may replace the words "this file" with
"this software" in both the first and second sentences.] 3.6. Bitstream Vera
Fonts Copyright

The fonts have a generous copyright, allowing derivative works (as long as
"Bitstream" or "Vera" are not in the names), and full redistribution (so long
as they are not *sold* by themselves). They can be be bundled, redistributed
and sold with any software.

The fonts are distributed under the following copyright:

Copyright (c) 2003 by Bitstream, Inc. All Rights Reserved. Bitstream Vera is a
trademark of Bitstream, Inc.

Permission is hereby granted, free of charge, to any person obtaining a copy
of the fonts accompanying this license ("Fonts") and associated documentation
files (the "Font Software"), to reproduce and distribute the Font Software,
including without limitation the rights to use, copy, merge, publish,
distribute, and/or sell copies of the Font Software, and to permit persons to
whom the Font Software is furnished to do so, subject to the following
conditions:

The above copyright and trademark notices and this permission notice shall be
included in all copies of one or more of the Font Software typefaces.

The Font Software may be modified, altered, or added to, and in particular the
designs of glyphs or characters in the Fonts may be modified and additional
glyphs or characters may be added to the Fonts, only if the fonts are renamed
to names not containing either the words "Bitstream" or the word "Vera".

This License becomes null and void to the extent applicable to Fonts or Font
Software that has been modified and is distributed under the "Bitstream Vera"
names.

The Font Software may be sold as part of a larger software package but no copy
of one or more of the Font Software typefaces may be sold by itself.

THE FONT SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
OR IMPLIED, INCLUDING BUT NOT LIMITED TO ANY WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT OF COPYRIGHT, PATENT,
TRADEMARK, OR OTHER RIGHT. IN NO EVENT SHALL BITSTREAM OR THE GNOME FOUNDATION
BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, INCLUDING ANY GENERAL,
SPECIAL, INDIRECT, INCIDENTAL, OR CONSEQUENTIAL DAMAGES, WHETHER IN AN ACTION
OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF THE USE OR INABILITY TO
USE THE FONT SOFTWARE OR FROM OTHER DEALINGS IN THE FONT SOFTWARE.

Except as contained in this notice, the names of Gnome, the Gnome Foundation,
and Bitstream Inc., shall not be used in advertising or otherwise to promote
the sale, use or other dealings in this Font Software without prior written
authorization from the Gnome Foundation or Bitstream Inc., respectively. For
further information, contact: fonts at gnome dot org.  3.7. Bigelow & Holmes
Inc and URW++ GmbH Luxi font license

Luxi fonts copyright (c) 2001 by Bigelow & Holmes Inc. Luxi font instruction
code copyright (c) 2001 by URW++ GmbH. All Rights Reserved. Luxi is a
registered trademark of Bigelow & Holmes Inc.

Permission is hereby granted, free of charge, to any person obtaining a copy
of these Fonts and associated documentation files (the "Font Software"), to
deal in the Font Software, including without limitation the rights to use,
copy, merge, publish, distribute, sublicense, and/or sell copies of the Font
Software, and to permit persons to whom the Font Software is furnished to do
so, subject to the following conditions:

The above copyright and trademark notices and this permission notice shall be
included in all copies of one or more of the Font Software.

The Font Software may not be modified, altered, or added to, and in particular
the designs of glyphs or characters in the Fonts may not be modified nor may
additional glyphs or characters be added to the Fonts. This License becomes
null and void when the Fonts or Font Software have been modified.

THE FONT SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
OR IMPLIED, INCLUDING BUT NOT LIMITED TO ANY WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT OF COPYRIGHT, PATENT,
TRADEMARK, OR OTHER RIGHT. IN NO EVENT SHALL BIGELOW & HOLMES INC. OR URW++
GMBH. BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, INCLUDING ANY
GENERAL, SPECIAL, INDIRECT, INCIDENTAL, OR CONSEQUENTIAL DAMAGES, WHETHER IN
AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF THE USE OR
INABILITY TO USE THE FONT SOFTWARE OR FROM OTHER DEALINGS IN THE FONT
SOFTWARE.

Except as contained in this notice, the names of Bigelow & Holmes Inc. and
URW++ GmbH. shall not be used in advertising or otherwise to promote the sale,
use or other dealings in this Font Software without prior written
authorization from Bigelow & Holmes Inc. and URW++ GmbH.

For further information, contact:

info@urwpp.<NAME_EMAIL>


--- end of LICENSE ---

-------------------------------------------------------------------------------

%% This notice is provided with respect to zlib v1.2.8, which may be included 
with JRE 8, JDK 8, and OpenJDK 8.

--- begin of LICENSE ---

  version 1.2.8, April 28th, 2013

  Copyright (C) 1995-2013 Jean-loup Gailly and Mark Adler

  This software is provided 'as-is', without any express or implied
  warranty.  In no event will the authors be held liable for any damages
  arising from the use of this software.

  Permission is granted to anyone to use this software for any purpose,
  including commercial applications, and to alter it and redistribute it
  freely, subject to the following restrictions:

  1. The origin of this software must not be misrepresented; you must not
     claim that you wrote the original software. If you use this software
     in a product, an acknowledgment in the product documentation would be
     appreciated but is not required.
  2. Altered source versions must be plainly marked as such, and must not be
     misrepresented as being the original software.
  3. This notice may not be removed or altered from any source distribution.

  Jean-loup Gailly        Mark Adler
  <EMAIL>          <EMAIL>

--- end of LICENSE ---

-------------------------------------------------------------------------------

%% This notice is provided with respect to the following which may be 
included with JRE 8, JDK 8, and OpenJDK 8.

  Apache Commons Math 3.2
  Apache Derby 10.11.1.2
  Apache Jakarta BCEL 5.1 
  Apache Jakarta Regexp 1.4 
  Apache Santuario XML Security for Java 1.5.4
  Apache Xalan-Java 2.7.1 
  Apache Xerces Java 2.10.0 
  Apache XML Resolver 1.1 
  Dynalink 0.5


--- begin of LICENSE ---

                                 Apache License
                           Version 2.0, January 2004
                        http://www.apache.org/licenses/

   TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION

   1. Definitions.

      "License" shall mean the terms and conditions for use, reproduction,
      and distribution as defined by Sections 1 through 9 of this document.

      "Licensor" shall mean the copyright owner or entity authorized by
      the copyright owner that is granting the License.

      "Legal Entity" shall mean the union of the acting entity and all
      other entities that control, are controlled by, or are under common
      control with that entity. For the purposes of this definition,
      "control" means (i) the power, direct or indirect, to cause the
      direction or management of such entity, whether by contract or
      otherwise, or (ii) ownership of fifty percent (50%) or more of the
      outstanding shares, or (iii) beneficial ownership of such entity.

      "You" (or "Your") shall mean an individual or Legal Entity
      exercising permissions granted by this License.

      "Source" form shall mean the preferred form for making modifications,
      including but not limited to software source code, documentation
      source, and configuration files.

      "Object" form shall mean any form resulting from mechanical
      transformation or translation of a Source form, including but
      not limited to compiled object code, generated documentation,
      and conversions to other media types.

      "Work" shall mean the work of authorship, whether in Source or
      Object form, made available under the License, as indicated by a
      copyright notice that is included in or attached to the work
      (an example is provided in the Appendix below).

      "Derivative Works" shall mean any work, whether in Source or Object
      form, that is based on (or derived from) the Work and for which the
      editorial revisions, annotations, elaborations, or other modifications
      represent, as a whole, an original work of authorship. For the purposes
      of this License, Derivative Works shall not include works that remain
      separable from, or merely link (or bind by name) to the interfaces of,
      the Work and Derivative Works thereof.

      "Contribution" shall mean any work of authorship, including
      the original version of the Work and any modifications or additions
      to that Work or Derivative Works thereof, that is intentionally
      submitted to Licensor for inclusion in the Work by the copyright owner
      or by an individual or Legal Entity authorized to submit on behalf of
      the copyright owner. For the purposes of this definition, "submitted"
      means any form of electronic, verbal, or written communication sent
      to the Licensor or its representatives, including but not limited to
      communication on electronic mailing lists, source code control systems,
      and issue tracking systems that are managed by, or on behalf of, the
      Licensor for the purpose of discussing and improving the Work, but
      excluding communication that is conspicuously marked or otherwise
      designated in writing by the copyright owner as "Not a Contribution."

      "Contributor" shall mean Licensor and any individual or Legal Entity
      on behalf of whom a Contribution has been received by Licensor and
      subsequently incorporated within the Work.

   2. Grant of Copyright License. Subject to the terms and conditions of
      this License, each Contributor hereby grants to You a perpetual,
      worldwide, non-exclusive, no-charge, royalty-free, irrevocable
      copyright license to reproduce, prepare Derivative Works of,
      publicly display, publicly perform, sublicense, and distribute the
      Work and such Derivative Works in Source or Object form.

   3. Grant of Patent License. Subject to the terms and conditions of
      this License, each Contributor hereby grants to You a perpetual,
      worldwide, non-exclusive, no-charge, royalty-free, irrevocable
      (except as stated in this section) patent license to make, have made,
      use, offer to sell, sell, import, and otherwise transfer the Work,
      where such license applies only to those patent claims licensable
      by such Contributor that are necessarily infringed by their
      Contribution(s) alone or by combination of their Contribution(s)
      with the Work to which such Contribution(s) was submitted. If You
      institute patent litigation against any entity (including a
      cross-claim or counterclaim in a lawsuit) alleging that the Work
      or a Contribution incorporated within the Work constitutes direct
      or contributory patent infringement, then any patent licenses
      granted to You under this License for that Work shall terminate
      as of the date such litigation is filed.

   4. Redistribution. You may reproduce and distribute copies of the
      Work or Derivative Works thereof in any medium, with or without
      modifications, and in Source or Object form, provided that You
      meet the following conditions:

      (a) You must give any other recipients of the Work or
          Derivative Works a copy of this License; and

      (b) You must cause any modified files to carry prominent notices
          stating that You changed the files; and

      (c) You must retain, in the Source form of any Derivative Works
          that You distribute, all copyright, patent, trademark, and
          attribution notices from the Source form of the Work,
          excluding those notices that do not pertain to any part of
          the Derivative Works; and

      (d) If the Work includes a "NOTICE" text file as part of its
          distribution, then any Derivative Works that You distribute must
          include a readable copy of the attribution notices contained
          within such NOTICE file, excluding those notices that do not
          pertain to any part of the Derivative Works, in at least one
          of the following places: within a NOTICE text file distributed
          as part of the Derivative Works; within the Source form or
          documentation, if provided along with the Derivative Works; or,
          within a display generated by the Derivative Works, if and
          wherever such third-party notices normally appear. The contents
          of the NOTICE file are for informational purposes only and
          do not modify the License. You may add Your own attribution
          notices within Derivative Works that You distribute, alongside
          or as an addendum to the NOTICE text from the Work, provided
          that such additional attribution notices cannot be construed
          as modifying the License.

      You may add Your own copyright statement to Your modifications and
      may provide additional or different license terms and conditions
      for use, reproduction, or distribution of Your modifications, or
      for any such Derivative Works as a whole, provided Your use,
      reproduction, and distribution of the Work otherwise complies with
      the conditions stated in this License.

   5. Submission of Contributions. Unless You explicitly state otherwise,
      any Contribution intentionally submitted for inclusion in the Work
      by You to the Licensor shall be under the terms and conditions of
      this License, without any additional terms or conditions.
      Notwithstanding the above, nothing herein shall supersede or modify
      the terms of any separate license agreement you may have executed
      with Licensor regarding such Contributions.

   6. Trademarks. This License does not grant permission to use the trade
      names, trademarks, service marks, or product names of the Licensor,
      except as required for reasonable and customary use in describing the
      origin of the Work and reproducing the content of the NOTICE file.

   7. Disclaimer of Warranty. Unless required by applicable law or
      agreed to in writing, Licensor provides the Work (and each
      Contributor provides its Contributions) on an "AS IS" BASIS,
      WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
      implied, including, without limitation, any warranties or conditions
      of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A
      PARTICULAR PURPOSE. You are solely responsible for determining the
      appropriateness of using or redistributing the Work and assume any
      risks associated with Your exercise of permissions under this License.

   8. Limitation of Liability. In no event and under no legal theory,
      whether in tort (including negligence), contract, or otherwise,
      unless required by applicable law (such as deliberate and grossly
      negligent acts) or agreed to in writing, shall any Contributor be
      liable to You for damages, including any direct, indirect, special,
      incidental, or consequential damages of any character arising as a
      result of this License or out of the use or inability to use the
      Work (including but not limited to damages for loss of goodwill,
      work stoppage, computer failure or malfunction, or any and all
      other commercial damages or losses), even if such Contributor
      has been advised of the possibility of such damages.

   9. Accepting Warranty or Additional Liability. While redistributing
      the Work or Derivative Works thereof, You may choose to offer,
      and charge a fee for, acceptance of support, warranty, indemnity,
      or other liability obligations and/or rights consistent with this
      License. However, in accepting such obligations, You may act only
      on Your own behalf and on Your sole responsibility, not on behalf
      of any other Contributor, and only if You agree to indemnify,
      defend, and hold each Contributor harmless for any liability
      incurred by, or claims asserted against, such Contributor by reason
      of your accepting any such warranty or additional liability.

   END OF TERMS AND CONDITIONS

   APPENDIX: How to apply the Apache License to your work.

      To apply the Apache License to your work, attach the following
      boilerplate notice, with the fields enclosed by brackets "[]"
      replaced with your own identifying information. (Don't include
      the brackets!)  The text should be enclosed in the appropriate
      comment syntax for the file format. We also recommend that a
      file or class name and description of purpose be included on the
      same "printed page" as the copyright notice for easier
      identification within third-party archives.

   Copyright [yyyy] [name of copyright owner]

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.

--- end of LICENSE ---

-------------------------------------------------------------------------------
