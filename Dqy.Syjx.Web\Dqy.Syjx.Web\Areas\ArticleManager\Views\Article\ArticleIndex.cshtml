@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@using Dqy.Syjx.Entity.ArticleManager
@using Dqy.Syjx.Util.Model
@{
    Layout = "~/Views/Shared/_Index.cshtml";
}



<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <div id="Cid" col="Cid"></div>
                    </li>
                    <li>
                        资讯标题/短标题：<input id="Key" col="Key" type="text" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a id="btnCancle" class="btn btn-default btn-sm" onclick="clearGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group-sm hidden-xs" id="toolbar">
            <a id="btnEdit" class="btn btn-primary disabled" onclick="showSaveForm(false)"><i class="fa fa-edit"></i> 修改</a>
            <a id="btnDelete" class="btn btn-danger disabled" onclick="deleteForm()"><i class="fa fa-remove"></i> 删除</a>
            <a id="btnRecommend" class="btn btn-info disabled" onclick="batchRecommendIndex()"><i class="fa fa-deaf"></i> 推荐到首页</a>
            <a id="btnSuspend" class="btn btn-primary disabled" onclick="showSuspendForm()"><i class="fa fa-asterisk"></i> 暂停发布</a>
            <a id="btnResume" class="btn btn-info disabled" onclick="showResumeForm()"><i class="fa fa-asterisk"></i>恢复发布</a>
            <a id="btnCancel" class="btn btn-danger disabled" onclick="showCancelForm()"><i class="fa fa-asterisk"></i>取消推荐首页</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    $(function () {
        loadCategory(0);
        initGrid();
        
        
        $("#gridTable").on("check.bs.table uncheck.bs.table check-all.bs.table uncheck-all.bs.table", function () {
            var ids = $("#gridTable").bootstrapTable("getSelections");
            if ($('#btnRecommend')) {
                $('#btnRecommend').toggleClass('disabled', !ids.length);
            }
            if ($('#btnSuspend')) {
                $('#btnSuspend').toggleClass('disabled', !ids.length);
            }
            if ($('#btnResume')) {
                $('#btnResume').toggleClass('disabled', !ids.length);
            }
            if ($('#btnCancel')) {
                $('#btnCancel').toggleClass('disabled', !ids.length);
            }
        });
    });
     function loadCategory(pid) {
          $('#Cid').ysComboBox({ defaultName: '分类'});
        ys.ajax({
            url: '@Url.Content("~/ArticleManager/ArticleCategory/GetListJson?Pid=0")',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    $('#Cid').ysComboBox({ data: obj.Data, key: 'Id', value: 'Name', defaultName: '分类', });
                    if (pid > 0) {
                        $('#Cid').ysComboBox('setValue', pid);
                    } else {
                        $('#Cid').ysComboBox('setValue', "");
                    }
                } else {
                    $('#Cid').ysComboBox({ data: [], key: 'Id', value: 'Name', defaultName: '分类', });
                }
            }
        });
    }
    function initGrid() {
        var queryUrl = '@Url.Content("~/ArticleManager/Article/GetPageListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            columns: [
                { checkbox: true, visible: true },
                { field: 'ClassName', title: '资讯分类', width: 120, sortable: true, halign: 'center', align: 'center', },
                { field: 'ShortTitle', title: '资讯短标题', width: 120, sortable: true, halign: 'center', align: 'left', },
                { field: 'Title', title: '标题', width: 200, sortable: true, halign: 'center', align: 'left', },
                { field: 'Sort', title: '排序值', width: 60, sortable: true, halign: 'center', align: 'center', },
                {
                    field: 'Statuz', title: '状态', width: 120, sortable: true, halign: 'center', align: 'center',
                    formatter: function (value, row, index) {
                        var html = "@ArticleStatuzEnum.Save.GetDescription()";
                        if (value == "@ArticleStatuzEnum.Publish.ParseToInt()") {
                            html = "@ArticleStatuzEnum.Publish.GetDescription()";
                        }
                        return html;
                    }
                },
                {
                    field: 'IsRecommend', title: '是否推荐到首页', width: 120, sortable: true, halign: 'center', align: 'center',
                    formatter: function (value, row, index) {
                        var html = "@IsEnum.No.GetDescription()";
                        if (value) {
                            html = "@IsEnum.Yes.GetDescription()";
                        }
                        return html;
                    }
                },
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function clearGrid() {
        $('#Cid').ysComboBox('setValue', -1);
        $("#Key").val("");
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function showSaveForm(bAdd) {
        var id = 0;
        if (!bAdd) {
            var selectedRow = $('#gridTable').bootstrapTable('getSelections');
            if (!ys.checkRowEdit(selectedRow)) {
                return;
            }
            else {
                id = selectedRow[0].Id;
            }
        }
        @*ys.openDialog({
            title: id > 0 ? '编辑' : '添加',
            content: '@Url.Content("~/ArticleManager/Article/ArticleForm")' + '?id=' + id,
            width: '768px',
            height: '550px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });*@
        var url = "@Url.Content("~/ArticleManager/Article/ArticleForm")" + '?id=' + id;
        createMenuItem(url, "修改资讯");
    }

    function deleteForm() {
        var selectedRow = $('#gridTable').bootstrapTable('getSelections');
        if (ys.checkRowDelete(selectedRow)) {
            ys.confirm('确认要删除选中的' + selectedRow.length + '条数据吗？', function () {
                var ids = ys.getIds(selectedRow);
                ys.ajax({
                    url: '@Url.Content("~/ArticleManager/Article/DeleteFormJson")' + '?ids=' + ids,
                    type: 'post',
                    success: function (obj) {
                        if (obj.Tag == 1) {
                            ys.msgSuccess(obj.Message);
                            searchGrid();
                        }
                        else {
                            ys.msgError(obj.Message);
                        }
                    }
                });
            });
        }
    }

    function batchRecommendIndex() {
        var selectedRow = $('#gridTable').bootstrapTable('getSelections');
        if (ys.checkRowDelete(selectedRow)) {
            ys.confirm('确认要推荐选中的' + selectedRow.length + '条数据到首页吗？', function () {
                var ids = ys.getIds(selectedRow);
                ys.ajax({
                    url: '@Url.Content("~/ArticleManager/Article/BatchRecommendIndex")' + '?ids=' + ids,
                    type: 'post',
                    success: function (obj) {
                        if (obj.Tag == 1) {
                            ys.msgSuccess(obj.Message);
                            searchGrid();
                        }
                        else {
                            ys.msgError(obj.Message);
                        }
                    }
                });
            });
        }
    }

    function showSuspendForm() {
        var selectedRow = $('#gridTable').bootstrapTable('getSelections');
        if (ys.checkRowDelete(selectedRow)) {
            ys.confirm('确认要暂停选中的' + selectedRow.length + '条数据停止发布吗？', function () {
                var ids = ys.getIds(selectedRow);
                ys.ajax({
                    url: '@Url.Content("~/ArticleManager/Article/BatchSuspendPublish")' + '?ids=' + ids,
                    type: 'post',
                    success: function (obj) {
                        if (obj.Tag == 1) {
                            ys.msgSuccess(obj.Message);
                            searchGrid();
                        }
                        else {
                            ys.msgError(obj.Message);
                        }
                    }
                });
            });
        }
    }

    function showResumeForm() {
        var selectedRow = $('#gridTable').bootstrapTable('getSelections');
        if (ys.checkRowDelete(selectedRow)) {
            ys.confirm('确认要将选中的' + selectedRow.length + '条数据恢复发布吗？', function () {
                var ids = ys.getIds(selectedRow);
                ys.ajax({
                    url: '@Url.Content("~/ArticleManager/Article/BatchPublish")' + '?ids=' + ids,
                    type: 'post',
                    success: function (obj) {
                        if (obj.Tag == 1) {
                            ys.msgSuccess(obj.Message);
                            searchGrid();
                        }
                        else {
                            ys.msgError(obj.Message);
                        }
                    }
                });
            });
        }
    }

    function showCancelForm() {
         var selectedRow = $('#gridTable').bootstrapTable('getSelections');
        if (ys.checkRowDelete(selectedRow)) {
            ys.confirm('确认要将选中的' + selectedRow.length + '条数据取消推荐到首页吗？', function () {
                var ids = ys.getIds(selectedRow);
                ys.ajax({
                    url: '@Url.Content("~/ArticleManager/Article/BatchRecommendNoIndex")' + '?ids=' + ids,
                    type: 'post',
                    success: function (obj) {
                        if (obj.Tag == 1) {
                            ys.msgSuccess(obj.Message);
                            searchGrid();
                        }
                        else {
                            ys.msgError(obj.Message);
                        }
                    }
                });
            });
        }
    }
</script>
