﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.BusinessManage;
using Dqy.Syjx.Model.Param.BusinessManage;
using Dqy.Syjx.Enum;

namespace Dqy.Syjx.Service.BusinessManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-11-04 15:08
    /// 描 述：橱柜管理服务类
    /// </summary>
    public class CupboardService : RepositoryFactory
    {
        #region 获取数据
        public async Task<List<CupboardEntity>> GetList(CupboardListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<FunRoomEntity>> GetPageList(CupboardListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ListFilter(param, strSql);
            var list = await this.BaseRepository().FindList<FunRoomEntity>(strSql.ToString(), filter.ToArray(), pagination);
            return list.ToList();
        }

        public async Task<CupboardEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<CupboardEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(CupboardEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task DeleteForm(string ids, long userid)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update bn_Cupboard set BaseIsDelete = 1 from bn_Cupboard AS c1 INNER JOIN  bn_FunRoom AS fr2 ON c1.FunRoomId = fr2.Id AND fr2.SafeguardUserId = {userid} where c1.Id in ({ids}) ";
            }
            else
            {
                strSql = $"update bn_Cupboard set BaseIsDelete = 1 from bn_Cupboard AS c1 INNER JOIN  bn_FunRoom AS fr2 ON c1.FunRoomId = fr2.Id AND fr2.SafeguardUserId = {userid} where c1.id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<CupboardEntity, bool>> ListFilter(CupboardListParam param)
        {
            var expression = LinqExtensions.True<CupboardEntity>();
            if (param.BaseIsDelete >= 0)
            {
                expression = expression.And(t => t.BaseIsDelete.Value == param.BaseIsDelete);
            }
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (param.FunRoomId > 0)
                {
                    expression = expression.And(t => t.FunRoomId == param.FunRoomId);
                }
                if (param.UnitId > 0)
                {
                    expression = expression.And(t => t.UnitId == param.UnitId);
                }
                if (param.Name.IsNotEmptyOrNull())
                {
                    expression = expression.And(t => t.Name == param.Name);
                }
            }
            return expression;
        }

        private List<DbParameter> ListFilter(CupboardListParam param, StringBuilder strSql)
        {
            strSql.Append(@"  SELECT * From (
                 SELECT
                 fr1.BaseIsDelete ,
                 fr1.BaseCreateTime ,
                 fr1.BaseModifyTime ,
                 fr1.BaseCreatorId ,
                 fr1.BaseModifierId ,
                 fr1.BaseVersion ,
                 fr1.UnitId ,
                 fr1.DictionaryId1005 ,
                 fr1.SchoolStagez ,
                 fr1.DictionaryId1006A ,
                 fr1.DictionaryId1006B ,
                 fr1.Name ,
                 fr1.UseArea ,
                 fr1.RoomAttribute ,
                 fr1.SeatNum ,
                 fr1.IsDigitalize ,
                 fr1.SysDepartmentId ,
                 fr1.SysUserId ,
                 fr1.BuildTime ,
                 fr1.ReformTime ,
                 fr1.Address ,
                 fr1.Statuz ,
                 fr1.SafeguardUserId ,
                 fr1.UploadBriefInfoNum ,
                 fr1.UploadSystemNum ,
                 fr1.LaboratoryGroupNum ,
		         sd1.DicName AS ClassNameA ,
		         sd2.DicName AS ClassNameB ,
                 sd6.DicName AS NatureName ,
		         sd3.DicName AS SubjectName,
		         d4.DepartmentName ,
		         su5.RealName ,
                 c7.Name AS CupboardName,
                 c7.Id ,
                 c7.FunRoomId ,
                 c7.Sort
                 ,ad1.Id AS AddressId
                 , ad1.Name  AS RoomName
				 ,ad2.Name AS HouseName
		         FROM  bn_FunRoom AS fr1
		         INNER JOIN  sys_static_dictionary AS sd1 ON fr1.DictionaryId1006A = sd1.DictionaryId AND sd1.BaseIsDelete = 0
		         INNER JOIN  sys_static_dictionary AS sd2 ON fr1.DictionaryId1006B = sd2.DictionaryId AND sd2.BaseIsDelete = 0
		         INNER JOIN  sys_static_dictionary AS sd3 ON fr1.DictionaryId1005 = sd3.DictionaryId AND sd3.BaseIsDelete = 0
                 INNER JOIN  sys_static_dictionary AS sd6 ON sd2.Nature = sd6.DictionaryId AND sd6.BaseIsDelete = 0
                 LEFT JOIN  SysDepartment AS d4 ON fr1.SysDepartmentId = d4.Id AND d4.BaseIsDelete = 0
		         LEFT JOIN  SysUser AS su5 ON fr1.SafeguardUserId = su5.Id AND su5.BaseIsDelete = 0
				 INNER JOIN  bn_Cupboard AS c7 ON fr1.Id = c7.FunRoomId AND c7.BaseIsDelete = 0
			     LEFT JOIN  up_Address AS ad1 ON fr1.Address = ad1.Id
				 LEFT JOIN  up_Address AS ad2 ON ad2.Id = ad1.Pid
            ) as tb1 WHERE   1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = @BaseIsDelete ");
                parameter.Add(DbParameterExtension.CreateDbParameter("@BaseIsDelete", param.BaseIsDelete));
                strSql.Append(" AND Statuz = @Statuz ");
                parameter.Add(DbParameterExtension.CreateDbParameter("@Statuz", param.Statuz));
                strSql.Append(" AND SafeguardUserId = @SafeguardUserId ");
                parameter.Add(DbParameterExtension.CreateDbParameter("@SafeguardUserId", param.SafeguardUserId));
                if (param.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    strSql.Append(" AND UnitId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                else if (param.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    //父级Id
                }
                if (param.DictionaryId1006A > 0)
                {
                    strSql.Append(" AND DictionaryId1006A = @DictionaryId1006A ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId1006A", param.DictionaryId1006A));
                }
                if (param.DictionaryId1006B > 0)
                {
                    strSql.Append(" AND DictionaryId1006B = @DictionaryId1006B ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId1006B", param.DictionaryId1006B));
                }
                if (param.DictionaryId1005 > 0)
                {
                    strSql.Append(" AND DictionaryId1005 = @DictionaryId1005 ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId1005", param.DictionaryId1005));
                }
                if (param.RoomAttribute > 0)
                {
                    strSql.Append(" AND RoomAttribute = @RoomAttribute ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@RoomAttribute", param.RoomAttribute));
                }
                if (param.SysDepartmentId > 0)
                {
                    strSql.Append(" AND SysDepartmentId = @SysDepartmentId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SysDepartmentId", param.SysDepartmentId));
                }
                if (param.AddressId > 0)
                {
                    strSql.Append(" AND AddressId = @AddressId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@AddressId", param.AddressId));
                }
                if (param.SysUserId > 0)
                {
                    strSql.Append(" AND SysUserId = @SysUserId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SysUserId", param.SysUserId));
                }
                if (!string.IsNullOrEmpty(param.Name))
                {
                    strSql.Append(" AND ( Name like @Name OR CupboardName like @Name ) ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Name", $"%{param.Name}%"));
                }
            }
            return parameter;
        }
        #endregion
    }
}
