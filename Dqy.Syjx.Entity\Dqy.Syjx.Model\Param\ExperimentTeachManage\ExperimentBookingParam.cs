﻿using System;
using System.Collections.Generic;
using Newtonsoft.Json;
using Dqy.Syjx.Util;
using MathNet.Numerics;

namespace Dqy.Syjx.Model.Param.ExperimentTeachManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-12-14 11:28
    /// 描 述：实验预约实体查询类
    /// </summary>
    public class ExperimentBookingListParam
    {
        public ExperimentBookingListParam()
        {
            OptType = 0;
            IsEvaluate = -1;
        }
        private string _ids;
        public long? Id { get; set; }
        public string Ids
        {
            get { return StringFilter.SearchSql(_ids); }
            set { _ids = value; }
        }

        public long? SchoolId { get; set; }
        public List<long> SchoolIds { get; set; }
        public long? CountyId { get; set; }
        public int? SchoolYearStart { get; set; }
        public int? SchoolTerm { get; set; }
        public long? SchoolGradeClassId { get; set; }
        public List<string> SchoolGradeClassIds { get; set; }
        public int? GradeId { get; set; }
        public List<int> GradeIds { get; set; }
        public int? ClassId { get; set; }
        public int? CourseId { get; set; }
        public List<int> CourseIds { get; set; }
        public int? SourceType { get; set; }
        public int? ExperimentType { get; set; }
        public int? Statuz { get; set; }
        /// <summary>
        /// 大于状态，如11，则查大于11的状态
        /// </summary>
        public int? ThanStatuz { get; set; }
        public int? RecordMode { get; set; }
        /// <summary>
        /// 登记人
        /// </summary>
        public long? RecordUserId { get; set; }
        /// <summary>
        /// 预约人
        /// </summary>
        public long? BookUserId { get; set; }
        /// <summary>
        /// 功能室维护人
        /// </summary>
        public long? SafeguardUserId { get; set; }
        /// <summary>
        /// 安排人
        /// </summary>
        public long? ArrangerUserId { get; set; }

        private string _ExperimentName;
        /// <summary>
        /// 实验名称
        /// </summary>
        public string ExperimentName
        {
            get { return StringFilter.SearchSql(_ExperimentName); } //文本框查询输入条件需加过滤防sql注入
            set { _ExperimentName = value; }
        }
        private string _RecordUserName;
        /// <summary>
        /// 登记人
        /// </summary>
        public string RecordUserName
        {
            get { return StringFilter.SearchSql(_RecordUserName); } //文本框查询输入条件需加过滤防sql注入
            set { _RecordUserName = value; }
        }
        
        /// <summary>
        /// 操作类型
        /// 1：保存验证,验证同一个实验，一个班级只能预约一次
        /// 2：保存验证，验证，同一个实验室地点，相同时间节次只能预约一次。
        /// 3:验证计划是否存在，
        /// 4:验证实验计划明细是否存在。
        /// 5：验证：同一个班级同一时间，智能预约一次
        /// 
        /// 13：预约，验证当前班级是否已预约或者登记过该实验
        /// 14：验证 普通教室 ，当前时间是否可以预约。
        /// 21:状态为0：预约中  11安排退回
        /// </summary>
        public int OptType { get; set; }
        public long? PlanDetailId { get; set; }
        public long? PlanInfoId { get; set; }
        public long? TextbookVersionDetailId { get; set; }

        public DateTime? ClassTime { get; set; }
        /// <summary>
        /// 小于等于该日期
        /// </summary>
        public DateTime? ClassTimele { get; set; }
        public int SectionIndex { get; set; }
        public long? SectionId { get; set; }

        /// <summary>
        /// 是否显示当前学年
        /// </summary>
        public int? IsShowCurrentSchoolYearStart { get; set; }
        public long? FunRoomId { get; set; }

        /// <summary>
        /// 查询列表类型（ 2：预约添加班级列表 ；0：其他  10：查询已预约数据（大于10，不等于11））
        /// </summary>
        public int? ListType { get; set; }

        public int IsEvaluate { get; set; }
        /// <summary>
        /// 实验要求（1：是、2：否）
        /// </summary>
        public int? IsNeedDo { get; set; }

        /// <summary>
        /// 是否完成
        /// </summary>
        public int? IsFinish { get; set; }
        /// <summary>
        /// 市级Id
        /// </summary>
        public long? CityId { get; set; }

        /// <summary>
        /// 学段Id
        /// </summary>
        public int? SchoolStageId { get; set; }

        public List<int> SchoolStageList { get; set; }
        /// <summary>
        /// 是否主节点数据
        /// </summary>
        public int? IsMain { get; set; }

        /// <summary>
        /// 父级Id
        /// </summary>
        public long? Pid { get; set; }

        /// <summary>
        /// 父级Id集合
        /// </summary>
        public List<long> Pids { get; set; }
        /// <summary>
        /// 实验来源
        /// </summary>
        public int? SourcePath { get; set; }

        public long? SchoolExperimentId { get; set; }

        /// <summary>
        /// 校本实验id集合  验证校本实验删除用。
        /// </summary>
        public string SchoolExperimentIds { get; set; }

        /// <summary>
        /// 实验版本Id
        /// </summary>
        public long? ExperimentVersionId { get; set; }

        /// <summary>
        /// 上课开始日期
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// 上课结束时间
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// 实验Id
        /// </summary>
        public long? ExperimentId { get; set; }
        /// <summary>
        /// 实验Ids
        /// </summary>
        public List<long> ExperimentIdList { get; set; }
        /// <summary>
        /// 设置用户Id
        /// </summary>
        public long SetUserId { get; set; } = 0;
        /// <summary>
        /// 学年起始年度（统计）
        /// </summary>
        public int StaticSchoolYearStart { get; set; }
        /// <summary>
        ///学期（统计）
        /// </summary>
        public int StaticSchoolTerm { get; set; }
        /// <summary>
        ///年级Id（统计）
        /// </summary>
        public int StaticGradeId { get; set; }

        public int IsShowClassTeacher { get; set; }

        /// <summary>
        /// 按主管部门要求
        /// </summary>
        public int? IsExam { get; set; } = -1;
        /// <summary>
        /// 是否当前学年（ 2：不是） 
        /// </summary>
        public int? IsCurrentSchoolYear { get; set; }
        /// <summary>
        /// 教材类型
        /// </summary>
        public int? CompulsoryType { get; set; }

        /// <summary>
        /// 课程活动类型(1: 课内 2:课外)
        /// </summary>
        public int ActivityType { get; set; } = -10000;

        /// <summary>
        /// 实验发布Id
        /// </summary>
        /// <returns></returns>
        public long ExperimentPublishId { get; set; }
    }

    public class SerachApiBookingParam : SerachApiParam
    {
        public SerachApiBookingParam()
        {
            Statuz = -1;
            ThanStatuz = -1;
            IsEvaluate = -1;
        }
        public int? RecordMode { get; set; }
        public int? SourceType { get; set; }
        public int? SourcePath { get; set; }
        public int? SchoolYearStart { get; set; }
        public int? SchoolTerm { get; set; }
        public long? SchoolGradeClassId { get; set; }

        /// <summary>
        /// 实验版本Id
        /// </summary>
        public long? ExperimentVersionId { get; set; }
        public int? GradeId { get; set; }

        public int ListType { get; set; }

        public int Statuz { get; set; }

        public int ThanStatuz { get; set; }

        public int IsCurrentTerm { get; set; }

        /// <summary>
        /// 实验要求（1：是、2：否）
        /// </summary>
        public int? IsNeedDo { get; set; }

        public int? ExperimentType { get; set; }

        public int IsEvaluate { get; set; }
    }
}
