﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.BusinessManage;
using Dqy.Syjx.Model.Param.BusinessManage;
using Dqy.Syjx.Entity.ExperimentTeachManage;

namespace Dqy.Syjx.Service.BusinessManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-10-25 11:09
    /// 描 述：附件存放表服务类
    /// </summary>
    public class AttachmentService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<AttachmentEntity>> GetList(AttachmentListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<AttachmentEntity>> GetPageList(AttachmentListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<AttachmentEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<AttachmentEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(AttachmentEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(AttachmentEntity entity, Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }
        public async Task UpdateTransForm(AttachmentEntity entity, Repository db)
        {
                await entity.Modify();
                await db.Update(entity);
        }

        public async Task UpdateForm(AttachmentEntity entity, List<string> fields, Repository db)
        {
            if (fields != null)
            {
                await entity.Modify();
                fields.Add("BaseModifyTime");
                fields.Add("BaseModifierId");
            }
            if (db == null)
            {
                await this.BaseRepository().Update(entity, fields);
            }
            else
            {
                await db.Update(entity, fields);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
               strSql = $" update bn_Attachment set BaseIsDelete = 1,IsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
               strSql = $"update bn_Attachment set BaseIsDelete = 1,IsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }


        /// <summary>
        ///
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="fileCategory"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task DeleteFormByObjectId(string ids,int fileCategory,long userId)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql = $" UPDATE  bn_Attachment SET BaseIsDelete = 1,IsDelete = 1 WHERE ObjectId in ({ids}) AND FileCategory = {fileCategory} AND BaseCreatorId = {userId}";
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<AttachmentEntity, bool>> ListFilter(AttachmentListParam param)
        {
            var expression = LinqExtensions.True<AttachmentEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (param.FileCategory > 0)
                {
                    expression = expression.And(t => t.FileCategory == param.FileCategory);
                }

                if (param.OptType == 1)
                {
                    expression = expression.And(t => t.IsDelete == 0);
                    if (!string.IsNullOrEmpty(param.Ids) || param.ObjectId > 0)
                    {
                        long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                        expression = expression.And(t => idArr.Contains(t.Id.Value) || t.ObjectId == param.ObjectId);
                    }
                    else
                    {
                        //禁止查询到数据
                        expression = expression.And(t => t.Id < 1);
                    }
                }
                else if(param.OptType == 2)
                {
                    expression = expression.And(t => t.IsDelete == 0);
                    if (!string.IsNullOrEmpty(param.Ids) && param.ObjectIds != null && param.ObjectIds.Count > 0)
                    {
                        long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                        expression = expression.And(t => idArr.Contains(t.Id.Value) || param.ObjectIds.Contains(t.ObjectId.Value));
                    }
                    else if ((!string.IsNullOrEmpty(param.Ids)))
                    {
                        long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                        expression = expression.And(t => idArr.Contains(t.Id.Value));
                    }
                    else if (param.ObjectIds != null && param.ObjectIds.Count > 0)
                    {
                        expression = expression.And(t => param.ObjectIds.Contains(t.ObjectId.Value));
                    }
                    else
                    {
                        //禁止查询到数据
                        expression = expression.And(t => t.Id < 1);
                    }
                }
                else
                {

                    if (!string.IsNullOrEmpty(param.Ids))
                    {
                        long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                        expression = expression.And(t => idArr.Contains(t.Id.Value));
                    }
                    if (param.ObjectId != null && param.ObjectId >= 0)
                    {
                        expression = expression.And(t => t.ObjectId == param.ObjectId);
                    }

                    if (param.Id > 0)
                    {
                        expression = expression.And(t => t.Id == param.Id);
                    }
                    if (param.UserId > 0)
                    {
                        expression = expression.And(t => t.BaseCreatorId == param.UserId);
                    }

                    if (param.ListFileCategory != null && param.ListFileCategory.Count > 0)
                    {
                        if (param.ListFileCategory.Count == 2)
                        {
                            expression = expression.And(t => t.FileCategory == param.ListFileCategory[0] || t.FileCategory == param.ListFileCategory[1]);
                        }
                        else if (param.ListFileCategory.Count == 3)
                        {
                            expression = expression.And(t => t.FileCategory == param.ListFileCategory[0] || t.FileCategory == param.ListFileCategory[1] || t.FileCategory == param.ListFileCategory[2]);
                        }
                    }
                    if (param.ObjectIds != null && param.ObjectIds.Count > 0)
                    {
                        expression = expression.And(t => param.ObjectIds.Contains(t.ObjectId.Value));
                    }
                }
            }
            return expression;
        }
        #endregion
    }
}
