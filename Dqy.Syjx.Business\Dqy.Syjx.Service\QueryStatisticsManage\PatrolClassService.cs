﻿using Dqy.Syjx.Data;
using Dqy.Syjx.Data.EF;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.BusinessManage;
using Dqy.Syjx.Entity.CameraManage;
using Dqy.Syjx.Entity.OrganizationManage;
using Dqy.Syjx.Entity.QueryStatisticsManage;
using Dqy.Syjx.Entity.SystemManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Model.Param.BusinessManage;
using Dqy.Syjx.Model.Param.OrganizationManage;
using Dqy.Syjx.Model.Param.QueryStatisticsManage;
using Dqy.Syjx.Model.Param.SystemManage;
using Dqy.Syjx.Service.OrganizationManage;
using Dqy.Syjx.Service.SystemManage;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Util.Tools;
using NetTopologySuite.Index.HPRtree;
using NPOI.SS.Formula.Functions;
using System;
using System.Collections.Generic;
using System.Data.Common;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Dqy.Syjx.Service.QueryStatisticsManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2022-05-19 14:08
    /// 描 述：服务类
    /// </summary>
    public class PatrolClassService : RepositoryFactory
    {
        private UserSubjectSetService userSubjectSetService = new UserSubjectSetService();
        private UnitScheduleService scheduleService = new UnitScheduleService();
        private ConfigSetService configSetService = new ConfigSetService();

        #region 获取数据
        public async Task<List<PatrolClassEntity>> GetList(PatrolClassListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<PatrolClassEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<PatrolClassEntity>(id);
        }

        /// <summary>
        /// 获取远程在线巡查数据
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<List<FunRoomEntity>> GetRoomVideo(FunRoomListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            string videoStatuzField = ""; //摄像机在线状态取值字段
            string associationCamera = ""; //关联摄像机表
            List<ConfigSetEntity> configObj = await configSetService.GetList(new ConfigSetListParam { TypeCode = "5003_SXJPP", UnitId = 100000000000000001 }); //获取平台配置的摄像机品牌
            if (configObj.Count > 0)
            {
                if (configObj.LastOrDefault().ConfigValue.Equals("1")) //海康
                {
                    videoStatuzField = " hk.Status";
                    associationCamera = " LEFT JOIN  HaiKangCamera AS hk ON hk.CameraName = scm.SrcName ";
                }
                else if(configObj.LastOrDefault().ConfigValue.Equals("2")) //大华
                {
                    videoStatuzField = " dh.isOnline";
                    associationCamera = " LEFT JOIN  DahuaDevice AS dh ON dh.deviceName = scm.SrcName ";
                }
                else
                {
                    videoStatuzField = " hk.Status";
                    associationCamera = " LEFT JOIN  HaiKangCamera AS hk ON hk.CameraName = scm.SrcName ";
                }
            }
            else
            {
                videoStatuzField = " scm.Statuz";
            }
            List<DbParameter> filter = GetFilterRoomVideoList(param, strSql, videoStatuzField, associationCamera);
            var list = await this.BaseRepository().FindList<FunRoomEntity>(strSql.ToString(), filter.ToArray(), pagination);
            return list.ToList();
        }

        /// <summary>
        /// 正在上课获取json数据
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<List<FunRoomEntity>> GetFunroomVideo(FunRoomListParam param, Pagination pagination)
        {
            string videoStatuzField = ""; //摄像机在线状态取值字段
            string associationCamera = ""; //关联摄像机表
            List<ConfigSetEntity> configObj = await configSetService.GetList(new ConfigSetListParam { TypeCode = "5003_SXJPP", UnitId = 100000000000000001 }); //获取平台配置的摄像机品牌
            if (configObj.Count > 0)
            {
                if (configObj.LastOrDefault().ConfigValue.Equals("1")) //海康
                {
                    videoStatuzField = " hk.Status";
                    associationCamera = " LEFT JOIN  HaiKangCamera AS hk ON hk.CameraName = scm.SrcName ";
                }
                else if (configObj.LastOrDefault().ConfigValue.Equals("2")) //大华
                {
                    videoStatuzField = " dh.isOnline";
                    associationCamera = " LEFT JOIN  DahuaDevice AS dh ON dh.deviceName = scm.SrcName ";
                }
                else
                {
                    videoStatuzField = " hk.Status";
                    associationCamera = " LEFT JOIN  HaiKangCamera AS hk ON hk.CameraName = scm.SrcName ";
                }
            }
            else
            {
                videoStatuzField = " scm.Statuz";
            }
            StringBuilder strSql = new StringBuilder();
            strSql.Append($@"SELECT * From (
                                 SELECT fr.Id ,fr.SchoolStagez ,fr.BaseIsDelete
			                       ,fr.UnitId ,u.Name AS SchoolName ,ur.UnitId AS CountyId ,sa.AreaName AS CountyName ,ur2.UnitId AS CityId ,u3.Name AS CityName ,fr.Name
								   ,fr.DictionaryId1006A ,sd1.DicName AS ClassNameA ,fr.DictionaryId1006B ,sd2.DicName AS ClassNameB ,fr.RoomAttribute
			                       ,sd4.DicName AS NatureName ,fr.DictionaryId1005 ,sd3.DicName AS SubjectName ,scm.SrcName AS SrcName ,fr.Statuz
                                   ,fr.Address AS AddressId ,{videoStatuzField} AS VideoStatuz ,ISNULL(ad1.Name,'') AS RoomName ,IsNULL(ad2.Name,'') AS HouseName
                                ,booking.ExperimentName ,booking.ClassTime ,booking.SectionIndex , booking.ClassBeginTime ,booking.ClassEndTime		                        
                                FROM  bn_FunRoom fr
                                INNER JOIN ex_ExperimentBooking AS booking ON booking.BaseIsDelete = 0 AND booking.IsMain = 0 AND booking.Statuz > 0 AND booking.Statuz <> 11 AND fr.Id = booking.FunRoomId
                                INNER JOIN  bn_SchoolCamera AS scm ON scm.UnitId = fr.UnitId AND scm.FunRoomId = fr.Id AND scm.BaseIsDelete = 0 AND scm.Statuz = 1
	                            INNER JOIN  up_Unit AS u ON fr.UnitId = u.Id AND u.BaseIsDelete = 0
		                        INNER JOIN  up_UnitRelation AS ur ON u.Id = ur.ExtensionObjId AND ur.ExtensionType = 3 AND ur.BaseIsDelete = 0
                                INNER JOIN  up_Unit AS u2 ON ur.UnitId = u2.Id AND u2.BaseIsDelete = 0
								INNER JOIN  up_UnitRelation AS ur2 ON u2.Id = ur2.ExtensionObjId AND ur2.ExtensionType = 3 AND ur2.BaseIsDelete = 0
                                INNER JOIN  up_Unit AS u3 ON ur2.UnitId = u3.Id AND u3.BaseIsDelete = 0
                                LEFT JOIN  SysArea AS sa ON sa.AreaCode = u2.AreaId AND sa.BaseIsDelete = 0
		                        INNER JOIN  sys_static_dictionary AS sd1 ON fr.DictionaryId1006A = sd1.DictionaryId AND sd1.BaseIsDelete = 0
		                        INNER JOIN  sys_static_dictionary AS sd2 ON fr.DictionaryId1006B = sd2.DictionaryId AND sd2.BaseIsDelete = 0
		                        INNER JOIN  sys_static_dictionary AS sd3 ON fr.DictionaryId1005 = sd3.DictionaryId AND sd3.BaseIsDelete = 0
		                        INNER JOIN  sys_static_dictionary AS sd4 ON fr.RoomAttribute = sd4.DictionaryId AND sd4.BaseIsDelete = 0
                                {associationCamera}
	                            LEFT JOIN  up_Address AS ad1 ON fr.Address = ad1.Id
	                            LEFT JOIN  up_Address AS ad2 ON ad2.Id = ad1.Pid
		                        WHERE fr.BaseIsDelete = 0 AND fr.Statuz = 1 AND fr.RoomAttribute != 1009003 AND scm.SrcName IS NOT NULL AND scm.SrcName <> ''
                            ) as tb1 WHERE  1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    strSql.Append(" AND UnitId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                else if (param.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    strSql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                    if (param.UnitId > 0)
                    {
                        strSql.Append(" AND UnitId = @UnitId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                    }
                }
                else if (param.UnitType == UnitTypeEnum.City.ParseToInt())
                {
                    strSql.Append(" AND CityId = @CityId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CityId", param.CityId));
                    if (param.CountyId > 0)
                    {
                        strSql.Append(" AND CountyId = @CountyId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                    }
                    if (param.UnitId > 0)
                    {
                        strSql.Append(" AND UnitId = @UnitId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                    }
                }

                if (param.DictionaryId1006A > 0)
                {
                    strSql.Append(" AND DictionaryId1006A = @DictionaryId1006A ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId1006A", param.DictionaryId1006A));
                }
                if (param.DictionaryId1006B > 0)
                {
                    strSql.Append(" AND DictionaryId1006B = @DictionaryId1006B ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId1006B", param.DictionaryId1006B));
                }
                if (param.DictionaryId1005 > 0)
                {
                    strSql.Append(" AND DictionaryId1005 = @DictionaryId1005 ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId1005", param.DictionaryId1005));
                }
                if (param.RoomAttribute > 0)
                {
                    strSql.Append(" AND RoomAttribute = @RoomAttribute ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@RoomAttribute", param.RoomAttribute));
                }
                if (!string.IsNullOrEmpty(param.Name))
                {
                    strSql.Append($" AND Name like '%{param.Name}%'");
                }
                if (param.VideoStatuz >= 0)
                {
                    strSql.Append(" AND VideoStatuz = @VideoStatuz ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@VideoStatuz", param.VideoStatuz));
                }

                if (param.SetUserId > 0)
                {
                    //判断用户是否设置了学段、学科
                    var listStage = userSubjectSetService.GetUserSetStageList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listStage.Count > 0)
                    {
                        string where = "";
                        int stageLength = listStage.Count;
                        int index = 1;
                        foreach (var stage in listStage)
                        {
                            where += $" SchoolStagez LIKE '{stage.DictionaryId}' ";
                            if (index != stageLength)
                            {
                                where += " OR ";
                            }
                            index++;
                        }
                        strSql.Append($" AND ({where})");
                    }
                    var listCourse = userSubjectSetService.GetUserSetCourseList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listCourse.Count > 0)
                    {
                        string courseId = string.Join(",", listCourse.Select(f => f.DictionaryId));
                        strSql.Append($" AND DictionaryId1005 IN ({courseId})");
                    }
                }

                //正在上课的。
                strSql.Append(" AND ClassBeginTime <= @ClassTime AND @ClassTime <= ClassEndTime ");
                parameter.Add(DbParameterExtension.CreateDbParameter("@ClassTime", DateTime.Now));

            }
            var list = await this.BaseRepository().FindList<FunRoomEntity>(strSql.ToString(), parameter.ToArray(), pagination);
            return list.ToList();
        }

        /// <summary>
        /// 获取在线巡课统计数据
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<List<PatrolClassEntity>> GetStatistics(PatrolClassListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = GetFilterStatistics(param, strSql);
            var list = await this.BaseRepository().FindList<PatrolClassEntity>(strSql.ToString(), filter.ToArray(), pagination);
            return list.ToList();
        }

        /// <summary>
        /// 在线巡课基础数据查询
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<List<PatrolClassEntity>> GetDetailPageList(PatrolClassListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = GetFilterList(param, strSql, "");
            var list = await this.BaseRepository().FindList<PatrolClassEntity>(strSql.ToString(), filter.ToArray(), pagination);
            return list.ToList();
        }

        /// <summary>
        /// 初始化【在线巡课】数据
        /// </summary>
        public async Task<TData> InitPatrolClass(string usedate, int unitType, long UnitId)
        {
            TData obj = new TData();
            //判断单位作息时间
            var unitScheduleList = await scheduleService.GetList(new UnitScheduleListParam() { UnitId = UnitId});
            if(unitScheduleList.Count == 0)
            {
                unitScheduleList = await scheduleService.GetList(new UnitScheduleListParam() { UnitId = 0 });
            }

            if (unitScheduleList.Count > 0)
            {
                UnitScheduleEntity scheduleEntity = unitScheduleList.LastOrDefault();
                string currentDate = DateTime.Now.ToString("MM-dd"); //当前系统日期
                string strWhere = "";
                int schedule = 0; //作息(1、夏,2、东)
                if (DateTime.Parse(currentDate) >= DateTime.Parse(scheduleEntity.BeginDate) && DateTime.Parse(currentDate) <= DateTime.Parse(scheduleEntity.EndDate)) //夏季作息时间
                {
                    schedule = 1;
                    strWhere += $" AND c.BeginTime <> '' AND c.EndTime <> ''";
                }
                else if (DateTime.Parse(currentDate) >= DateTime.Parse(scheduleEntity.DjBeginTime) && DateTime.Parse(currentDate) <= DateTime.Parse(scheduleEntity.DjEndTime)) //冬季作息时间
                {
                    schedule = 2;
                    strWhere += $" AND c.DjBeginTime <> '' AND c.DjEndTime <> ''";
                }

                if (unitType == UnitTypeEnum.School.ParseToInt()) //学校
                {
                    strWhere += $" AND c.UnitId = {UnitId}";
                }
                if (unitType == UnitTypeEnum.County.ParseToInt()) //区县
                {
                    strWhere += $" AND ur.UnitId = {UnitId}";
                }
                if (unitType == UnitTypeEnum.City.ParseToInt()) //市级
                {
                    strWhere += $" AND ur2.UnitId = {UnitId}";
                }
                var list = await GetPatrolClassList(strWhere); //查询需要巡课的课程节次数据

                StringBuilder insertSql = new StringBuilder();
                StringBuilder deleteSql = new StringBuilder();
                if (list.Count > 0)
                {
                    deleteSql.Append($@"Delete from bn_PatrolClass where UseDate = '{usedate}' AND UnitId = {UnitId}; ");
                    ExecDataBySql(deleteSql.ToString(), $"删除【{usedate}】“在线巡课”数据操作"); //删除重复数据
                    foreach (var item in list)
                    {
                        if (schedule == 1)
                        {
                            item.BeginTime = item.BeginTime;
                            item.EndTime = item.EndTime;
                        }
                        else
                        {
                            item.BeginTime = item.DjBeginTime;
                            item.EndTime = item.DjEndTime;
                        }

                         insertSql.Append(@$"INSERT INTO  bn_PatrolClass
                                                        ( Id ,BaseIsDelete ,BaseCreateTime ,BaseModifyTime ,BaseCreatorId ,BaseModifierId ,BaseVersion ,UnitId ,FunRoomId ,DictionaryId1006A ,DictionaryId1006B ,
                                                          SchoolTermName ,RoomAttribute ,SchoolStage ,DictionaryId1005 ,CourseSectionId ,BeginTime ,EndTime ,UseDate ,IsClass ,CameraId ,SrcName ,CameraIndexCode ,Statuz
                                                        )
                                                    values({Dqy.Syjx.IdGenerator.IdGeneratorHelper.Instance.GetId()},0,'{DateTime.Now}','{DateTime.Now}',0,0,0,{item.UnitId},{item.FunRoomId},{item.DictionaryId1006A},{item.DictionaryId1006B},
                                                        '{item.SchoolTermName}',{item.RoomAttribute},'{item.SchoolStage}',{item.DictionaryId1005},{item.CourseSectionId},'{item.BeginTime}','{item.EndTime}','{usedate}',0,'','{item.SrcName}','{item.CameraIndexCode}',1); ");
                    }
                    ExecDataBySql(insertSql.ToString(), $"新增【{usedate}】“在线巡课”数据操作"); //插入bn_PatrolClass表数据操作
                    obj.Tag = 1;
                }
            }
            else
            {
                obj.Tag = 0;
               // obj.Message = "请先设置单位作息时间";
            }
            return obj;
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(PatrolClassEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(PatrolClassEntity entity, Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update bn_PatrolClass set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
                strSql = $"update bn_PatrolClass set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }

        /// <summary>
        /// 修改字段是否正常上课IsClass（0：否，1：是）
        /// </summary>
        /// <param name="isclass"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<int> Update(int isclass, long id)
        {
            string strSql = $"UPDATE  bn_PatrolClass SET IsClass = {isclass} WHERE Id = {id}";
            return await this.BaseRepository().ExecuteBySql(strSql);
        }

        /// <summary>
        /// 设置状态Statuz（0：禁用，1：启用）
        /// </summary>
        /// <param name="statuz"></param>
        /// <param name="usedate"></param>
        /// <returns></returns>
        public async Task<int> SetStatuz(int statuz, string usedate)
        {
            string strSql = $"update bn_PatrolClass set Statuz = {statuz} where UseDate = '{usedate}'";
            return await this.BaseRepository().ExecuteBySql(strSql);
        }

     

        /// <summary>
        /// 查询每个课程节次内拍摄到最大人数图片
        /// </summary>
        /// <param name="srcName">摄像头编号</param>
        /// <param name="bgnTime">课程节次开始时间</param>
        /// <param name="endTime">课程节次结束时间</param>
        /// <param name="usedate">采集时间</param>
        /// <returns></returns>
        public Task<IEnumerable<HaiKangEventNotifyEntity>> GetMaxPeople(string srcName, string bgnTime, string endTime, string usedate)
        {

            string strSql = $@"select Id ,PeopleNum ,ImageUrl ,SrcName ,SendTime ,statuz ,PicPath from (
                                select Id ,PeopleNum ,ImageUrl ,SrcName ,SendTime ,statuz ,PicPath,ROW_NUMBER() OVER(partition by CONVERT(nvarchar(10),SendTime,121),SrcName order by PeopleNum desc)as sortId  from HaiKangEventNotify
                                where (CONVERT(varchar(100),SendTime,24) BETWEEN '{bgnTime}' AND '{endTime}') and SrcName='{srcName}' AND CONVERT(varchar(10),SendTime,120) = '{usedate}'
                                )t where sortId = 1";
            return new SqlServerDatabase(GlobalContext.SystemConfig.DBConnectionString2).FindList<HaiKangEventNotifyEntity>(strSql.ToString());
        }

        /// <summary>
        /// 获取摄像头抓拍图片
        /// </summary>
        /// <param name="srcName"></param>
        /// <param name="bgnTime"></param>
        /// <param name="endTime"></param>
        /// <param name="usedate"></param>
        /// <returns></returns>
        public Task<IEnumerable<HaiKangEventNotifyEntity>> GetCapturesPic(string srcName, string bgnTime, string endTime, string usedate)
        {

            string strSql = $@"select Id ,PeopleNum ,ImageUrl ,SrcName ,SendTime ,statuz ,PicPath from (
                                select Id ,PeopleNum ,ImageUrl ,SrcName ,SendTime ,statuz ,PicPath,ROW_NUMBER() OVER(partition by CONVERT(nvarchar(10),SendTime,121),SrcName order by PeopleNum desc)as sortId  from HaiKangEventNotify
                                where (CONVERT(varchar(100),SendTime,24) BETWEEN '{bgnTime}' AND '{endTime}') and SrcName='{srcName}' AND CONVERT(varchar(10),SendTime,120) = '{usedate}'
                                )t where sortId = 1";
            return new SqlServerDatabase(GlobalContext.SystemConfig.DBConnectionString2).FindList<HaiKangEventNotifyEntity>(strSql.ToString());
        }
        #endregion

        #region 私有方法
        /// <summary>
        /// 验证是否在学期内
        /// </summary>
        /// <returns></returns>
        public bool IsInSchoolTerm(string datetime)
        {
            bool isinTerm = false;
            string strSql = $"SELECT Id FROM  bn_SchoolTerm WHERE BaseIsDelete = 0 AND TermStart <= '{datetime}' AND TermEnd >='{datetime}'";
            Task<IEnumerable<SchoolTermEntity>> list = new SqlServerDatabase(GlobalContext.SystemConfig.DBConnectionString).FindList<SchoolTermEntity>(strSql);
            if (list.Result.Count() > 0)
            {
                isinTerm = true;
            }
            return isinTerm;
        }

        /// <summary>
        /// 验证学校是否有摄像头
        /// </summary>
        /// <param name="unitId"></param>
        /// <returns></returns>
        public bool IsHasCamera(long unitId)
        {
            bool ishasCamera = false;
            string strSql = $"SELECT Id FROM  bn_SchoolCamera WHERE BaseIsDelete = 0 AND Statuz = 1 AND UnitId = {unitId}";
            Task<IEnumerable<SchoolCameraEntity>> list = new SqlServerDatabase(GlobalContext.SystemConfig.DBConnectionString).FindList<SchoolCameraEntity>(strSql);
            if (list.Result.Count() > 0)
            {
                ishasCamera = true;
            }
            return ishasCamera;
        }

        /// <summary>
        /// 验证是否存在使用日期的在线巡课数据
        /// </summary>
        /// <param name="usedate"></param>
        /// <returns></returns>
        public bool IsHasPatrolClassData(string usedate, long unitId)
        {
            bool ishasPatrolclassData = false;
            string strSql = $"SELECT Id FROM  bn_PatrolClass WHERE BaseIsDelete = 0 AND UseDate = '{usedate}' AND UnitId = {unitId}";
            Task<IEnumerable<SchoolCameraEntity>> list = new SqlServerDatabase(GlobalContext.SystemConfig.DBConnectionString).FindList<SchoolCameraEntity>(strSql);
            if (list.Result.Count() > 0)
            {
                ishasPatrolclassData = true;
            }
            return ishasPatrolclassData;
        }

        /// <summary>
        /// 条件过滤查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        private Expression<Func<PatrolClassEntity, bool>> ListFilter(PatrolClassListParam param)
        {
            var expression = LinqExtensions.True<PatrolClassEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
            }
            return expression;
        }

        /// <summary>
        /// 查询需要巡课的课程节次数据
        /// </summary>
        /// <param name="strWhere"></param>
        /// <param name="bgntimeField"></param>
        /// <param name="endtimeField"></param>
        /// <returns></returns>
        public async Task<List<PatrolClassEntity>> GetPatrolClassList(string strWhere)
        {
            int weekid = DateTime.Now.DayOfWeek.ParseToInt(); //星期
            string strSql = @$"SELECT u.Name AS UnitName ,f.UnitId ,f.Id AS FunRoomId ,f.Name ,f.DictionaryId1006A ,f.DictionaryId1006B 
                                    ,st1.SchoolYearStart,st1.SchoolTerm,'' AS SchoolTermName ,
					                 f.RoomAttribute ,f.SchoolStagez AS SchoolStage ,f.DictionaryId1005 ,c.Id AS CourseSectionId
									 ,c.GradeId ,sd3.DicName AS GradeName ,c.WeekId ,sd1.DicName AS WeekName ,c.BeginTime AS BeginTime ,c.EndTime AS EndTime ,c.DjBeginTime ,c.DjEndTime
                                     ,GETDATE() AS UseDate ,sc.SrcName ,ur2.UnitId AS CityId ,u3.Name AS CityName ,ur.UnitId AS CountyId ,u2.Name AS CountyName
			                  FROM    bn_FunRoom f
				                     CROSS JOIN up_CourseSection c
				                     INNER JOIN  sys_static_dictionary AS sd1 ON c.WeekId = sd1.DictionaryId AND sd1.BaseIsDelete = 0 
									 INNER JOIN  sys_static_dictionary AS sd3 ON c.GradeId = sd3.DictionaryId AND sd3.BaseIsDelete = 0 
				                     INNER JOIN  bn_SchoolTerm AS st1 ON st1.BaseIsDelete = 0 AND st1.TermStart <= GETDATE() AND GETDATE()  <= st1.TermEnd
				                     INNER JOIN  up_Unit u ON u.Id = f.UnitId AND u.BaseIsDelete = 0
                                     INNER JOIN  up_UnitRelation AS ur ON u.Id = ur.ExtensionObjId AND ur.ExtensionType = 3 AND ur.BaseIsDelete = 0 AND u.BaseIsDelete = 0
		                             INNER JOIN  up_Unit AS u2 ON ur.UnitId = u2.Id AND u2.BaseIsDelete = 0
		                             INNER JOIN  up_UnitRelation AS ur2 ON u2.Id = ur2.ExtensionObjId AND ur2.ExtensionType = 3 AND ur2.BaseIsDelete = 0
		                             INNER JOIN  up_Unit AS u3 ON u3.Id = ur2.UnitId AND u3.BaseIsDelete = 0
                                     INNER JOIN  bn_SchoolCamera sc ON sc.FunRoomId = f.Id
			                  WHERE  f.UnitId = c.UnitId
				                     AND f.BaseIsDelete = 0 AND f.Statuz = 1 AND f.RoomAttribute != 1009003
				                     AND c.BaseIsDelete = 0 AND c.Statuz = 1
                                     AND sd1.Memo = '{weekid}'";  //星期查询
            if (!string.IsNullOrEmpty(strWhere))
            {
                strSql += strWhere;
            }

            var list = await this.BaseRepository().FindList<PatrolClassEntity>(strSql);
            if (list!=null)
            {
                list = list.DistinctBy(m => m.FunRoomId).ToList();
                list.ForEach(m => {
                    m.SchoolTermName = GetSchoolTermName(m.SchoolYearStart, m.SchoolYearStart + 1, m.SchoolTerm);
                });
            }
            return list.ToList();
        }

        /// <summary>
        /// 远程在线巡查条件查询
        /// </summary>
        /// <param name="param"></param>
        /// <param name="strSql"></param>
        /// <param name="videoStatuzField">摄像机在线状态取值字段</param>
        /// <param name="associationCamera">关联摄像机表</param>
        /// <returns></returns>
        private List<DbParameter> GetFilterRoomVideoList(FunRoomListParam param, StringBuilder strSql,string videoStatuzField,string associationCamera)
        {
            strSql.Append($@"SELECT * From (
                                 SELECT fr.Id ,fr.SchoolStagez ,fr.BaseIsDelete ,fr.BaseCreateTime ,fr.BaseModifyTime ,fr.BaseCreatorId ,fr.BaseModifierId ,fr.BaseVersion
			                       ,fr.UnitId ,u.Name AS SchoolName ,ur.UnitId AS CountyId ,sa.AreaName AS CountyName ,ur2.UnitId AS CityId ,u3.Name AS CityName ,fr.Name
								   ,fr.DictionaryId1006A ,sd1.DicName AS ClassNameA ,fr.DictionaryId1006B ,sd2.DicName AS ClassNameB ,fr.RoomAttribute
			                       ,sd4.DicName AS NatureName ,fr.DictionaryId1005 ,sd3.DicName AS SubjectName ,scm.SrcName AS SrcName ,fr.Statuz
                                   ,fr.Address AS AddressId ,{videoStatuzField} AS VideoStatuz ,ISNULL(ad1.Name,'') AS RoomName ,IsNULL(ad2.Name,'') AS HouseName
		                        FROM  bn_FunRoom fr
	                            INNER JOIN  up_Unit AS u ON fr.UnitId = u.Id AND u.BaseIsDelete = 0
		                        INNER JOIN  up_UnitRelation AS ur ON u.Id = ur.ExtensionObjId AND ur.ExtensionType = 3 AND ur.BaseIsDelete = 0
                                INNER JOIN  up_Unit AS u2 ON ur.UnitId = u2.Id AND u2.BaseIsDelete = 0
								INNER JOIN  up_UnitRelation AS ur2 ON u2.Id = ur2.ExtensionObjId AND ur2.ExtensionType = 3 AND ur2.BaseIsDelete = 0
                                INNER JOIN  up_Unit AS u3 ON ur2.UnitId = u3.Id AND u3.BaseIsDelete = 0
                                LEFT JOIN  SysArea AS sa ON sa.AreaCode = u2.AreaId AND sa.BaseIsDelete = 0
		                        INNER JOIN  sys_static_dictionary AS sd1 ON fr.DictionaryId1006A = sd1.DictionaryId AND sd1.BaseIsDelete = 0
		                        INNER JOIN  sys_static_dictionary AS sd2 ON fr.DictionaryId1006B = sd2.DictionaryId AND sd2.BaseIsDelete = 0
		                        INNER JOIN  sys_static_dictionary AS sd3 ON fr.DictionaryId1005 = sd3.DictionaryId AND sd3.BaseIsDelete = 0
		                        INNER JOIN  sys_static_dictionary AS sd4 ON fr.RoomAttribute = sd4.DictionaryId AND sd4.BaseIsDelete = 0
                                LEFT JOIN  bn_SchoolCamera AS scm ON scm.UnitId = fr.UnitId AND scm.FunRoomId = fr.Id AND scm.BaseIsDelete = 0 AND scm.Statuz = 1
                                {associationCamera}
	                            LEFT JOIN  up_Address AS ad1 ON fr.Address = ad1.Id
	                            LEFT JOIN  up_Address AS ad2 ON ad2.Id = ad1.Pid
		                        WHERE fr.BaseIsDelete = 0 AND fr.Statuz = 1 AND fr.RoomAttribute != 1009003 AND scm.SrcName IS NOT NULL AND scm.SrcName <> ''
                            ) as tb1 WHERE  1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    strSql.Append(" AND UnitId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                else if (param.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    strSql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                    if (param.UnitId > 0)
                    {
                        strSql.Append(" AND UnitId = @UnitId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                    }
                }
                else if(param.UnitType == UnitTypeEnum.City.ParseToInt())
                {
                    strSql.Append(" AND CityId = @CityId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CityId", param.CityId));
                    if (param.CountyId > 0)
                    {
                        strSql.Append(" AND CountyId = @CountyId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                    }
                    if (param.UnitId > 0)
                    {
                        strSql.Append(" AND UnitId = @UnitId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                    }
                }

                if (param.DictionaryId1006A > 0)
                {
                    strSql.Append(" AND DictionaryId1006A = @DictionaryId1006A ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId1006A", param.DictionaryId1006A));
                }
                if (param.DictionaryId1006B > 0)
                {
                    strSql.Append(" AND DictionaryId1006B = @DictionaryId1006B ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId1006B", param.DictionaryId1006B));
                }
                if (param.DictionaryId1005 > 0)
                {
                    strSql.Append(" AND DictionaryId1005 = @DictionaryId1005 ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId1005", param.DictionaryId1005));
                }
                if (param.RoomAttribute > 0)
                {
                    strSql.Append(" AND RoomAttribute = @RoomAttribute ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@RoomAttribute", param.RoomAttribute));
                }
                if (!string.IsNullOrEmpty(param.Name))
                {
                    strSql.Append($" AND Name like '%{param.Name}%'");
                }
                if(param.VideoStatuz >= 0)
                {
                    strSql.Append(" AND VideoStatuz = @VideoStatuz ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@VideoStatuz", param.VideoStatuz));
                }

                if (param.SetUserId > 0)
                {
                    //判断用户是否设置了学段、学科
                    var listStage = userSubjectSetService.GetUserSetStageList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listStage.Count > 0)
                    {
                        string where = "";
                        int stageLength = listStage.Count;
                        int index = 1;
                        foreach (var stage in listStage)
                        {
                            where += $" SchoolStagez LIKE '{stage.DictionaryId}' ";
                            if (index != stageLength)
                            {
                                where += " OR ";
                            }
                            index++;
                        }
                        strSql.Append($" AND ({where})");
                    }
                    var listCourse = userSubjectSetService.GetUserSetCourseList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listCourse.Count > 0)
                    {
                        string courseId = string.Join(",", listCourse.Select(f => f.DictionaryId));
                        strSql.Append($" AND DictionaryId1005 IN ({courseId})");
                    }
                }
            }

            return parameter;
        }

        /// <summary>
        /// 在线巡课查询统计
        /// </summary>
        /// <param name="param"></param>
        /// <param name="strSql"></param>
        /// <returns></returns>
        private List<DbParameter> GetFilterStatistics(PatrolClassListParam param, StringBuilder strSql)
        {
            StringBuilder whereStr = new StringBuilder();
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    whereStr.Append(" AND UnitId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                else if (param.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    whereStr.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                    if (param.SchoolId > 0)
                    {
                        whereStr.Append(" AND UnitId = @UnitId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.SchoolId));
                    }
                }
                else if (param.UnitType == UnitTypeEnum.City.ParseToInt())
                {
                    whereStr.Append(" AND CityId = @CityId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CityId", param.CityId));
                    if (param.CountyId > 0)
                    {
                        whereStr.Append(" AND CountyId = @CountyId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                    }
                    if (param.SchoolId > 0)
                    {
                        whereStr.Append(" AND UnitId = @UnitId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.SchoolId));
                    }
                }

                if (param.DictionaryId1006A > 0)
                {
                    whereStr.Append(" AND DictionaryId1006A = @DictionaryId1006A ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId1006A", param.DictionaryId1006A));
                }
                if (param.DictionaryId1006B > 0)
                {
                    whereStr.Append(" AND DictionaryId1006B = @DictionaryId1006B ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId1006B", param.DictionaryId1006B));
                }
                if (param.DictionaryId1005 > 0)
                {
                    whereStr.Append(" AND DictionaryId1005 = @DictionaryId1005 ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId1005", param.DictionaryId1005));
                }
                if (param.RoomAttribute > 0)
                {
                    whereStr.Append(" AND RoomAttribute = @RoomAttribute ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@RoomAttribute", param.RoomAttribute));
                }
                if (param.SchoolYearStart > 0)
                {
                    whereStr.Append(" AND SchoolYearStart = @SchoolYearStart ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolYearStart", param.SchoolYearStart));
                }
                if (!string.IsNullOrEmpty(param.SchoolTerm) && param.SchoolTerm != "-1")
                {
                    string schoolTermName = ((SchoolTermEnum)Convert.ToInt32(param.SchoolTerm)).GetDescription();
                    whereStr.Append($" AND SchoolTermName like  '%{schoolTermName}%'");
                }
                if (!string.IsNullOrEmpty(param.FunRoomName))
                {
                    whereStr.Append($" AND FunRoomName like  '%{param.FunRoomName}%'");
                }

                if (param.SetUserId > 0)
                {
                    var listCourse = userSubjectSetService.GetUserSetCourseList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listCourse.Count > 0)
                    {
                        string courseId = string.Join(",", listCourse.Select(f => f.DictionaryId));
                        whereStr.Append($" AND DictionaryId1005 IN ({courseId})");
                    }
                }
            }
            strSql.Append($@"SELECT SrcName ,SchoolYearStart ,SchoolTermName ,UnitId ,UnitName ,CountyId ,CountyName ,CityId ,CityName ,FunRoomId ,FunRoomName ,DictionaryId1006A ,ClassNameA
                                    ,DictionaryId1006B ,ClassNameB ,RoomAttribute ,RoomAttributeName ,DictionaryId1005 ,SubjectName ,NormalClass
                             From (
                                   SELECT  pc.SchoolStage,pc.SchoolTermName ,pc.UnitId ,u.Name AS UnitName ,ur.UnitId AS CountyId ,sa.AreaName AS CountyName ,ur2.UnitId AS CityId ,u3.Name AS CityName ,pc.FunRoomId ,fr.Name AS FunRoomName
			                              ,pc.DictionaryId1006A ,sd1.DicName AS ClassNameA ,pc.DictionaryId1006B ,sd2.DicName AS ClassNameB ,pc.RoomAttribute
                                          ,sd4.DicName AS RoomAttributeName ,pc.DictionaryId1005 ,sd3.DicName AS SubjectName ,st1.SchoolYearStart AS SchoolYearStart ,scm.SrcName AS SrcName
                                          ,( SELECT COUNT(1) FROM bn_PatrolClass pcs
                                             
											 WHERE pc.BaseIsDelete = 0 AND pc.Statuz = 1 AND scm.SrcName IS NOT NULL AND scm.SrcName <> '' AND fr.RoomAttribute !='1009003' AND IsClass = 1
											 AND pcs.SchoolTermName = pc.SchoolTermName AND pcs.UnitId = pc.UnitId AND pcs.DictionaryId1006A = pc.DictionaryId1006A AND pcs.DictionaryId1006B = pc.DictionaryId1006B  AND pcs.DictionaryId1005 = pc.DictionaryId1005
											 AND pcs.SchoolStage = pc.SchoolStage AND pcs.RoomAttribute = pc.RoomAttribute AND pcs.FunRoomId = pc.FunRoomId
											) AS NormalClass
                                   FROM  bn_PatrolClass pc
                                   INNER JOIN  up_Unit AS u ON pc.UnitId = u.Id AND u.BaseIsDelete = 0
                                   INNER JOIN  up_UnitRelation AS ur ON u.Id = ur.ExtensionObjId AND ur.ExtensionType = 3 AND ur.BaseIsDelete = 0
                                   INNER JOIN  up_Unit AS u2 ON ur.UnitId = u2.Id AND u2.BaseIsDelete = 0
                                   INNER JOIN  up_UnitRelation AS ur2 ON u2.Id = ur2.ExtensionObjId AND ur2.ExtensionType = 3 AND ur2.BaseIsDelete = 0
                                   INNER JOIN  up_Unit AS u3 ON ur2.UnitId = u3.Id AND u3.BaseIsDelete = 0
                                   LEFT JOIN  SysArea AS sa ON sa.AreaCode = u2.AreaId AND sa.BaseIsDelete = 0
                                   INNER JOIN  bn_FunRoom AS fr ON fr.Id = pc.FunRoomId AND ur.BaseIsDelete = 0
                                   INNER JOIN  sys_static_dictionary AS sd1 ON pc.DictionaryId1006A = sd1.DictionaryId AND sd1.BaseIsDelete = 0
                                   INNER JOIN  sys_static_dictionary AS sd2 ON pc.DictionaryId1006B = sd2.DictionaryId AND sd2.BaseIsDelete = 0
                                   INNER JOIN  sys_static_dictionary AS sd3 ON pc.DictionaryId1005 = sd3.DictionaryId AND sd3.BaseIsDelete = 0
                                   INNER JOIN  sys_static_dictionary AS sd4 ON pc.RoomAttribute = sd4.DictionaryId AND sd4.BaseIsDelete = 0
                                   LEFT JOIN  bn_SchoolTerm AS st1 ON st1.TermStart <= pc.UseDate AND pc.UseDate  <= st1.TermEnd AND st1.BaseIsDelete = 0
                                   LEFT JOIN  bn_SchoolCamera AS scm ON scm.UnitId = pc.UnitId AND scm.FunRoomId = pc.FunRoomId AND scm.BaseIsDelete = 0 AND scm.Statuz = 1
                                   
                                   WHERE pc.BaseIsDelete = 0 AND pc.Statuz = 1 AND scm.SrcName IS NOT NULL AND scm.SrcName <> '' AND fr.RoomAttribute !='1009003' AND pc.MaxPeopleNumber IS NOT NULL
                                ) as tb1 WHERE  1 = 1 {whereStr}
	                         GROUP BY SchoolTermName ,UnitId ,UnitName ,CountyId ,CountyName ,CityId ,CityName ,FunRoomId ,FunRoomName ,DictionaryId1006A ,ClassNameA
                            ,DictionaryId1006B ,ClassNameB ,RoomAttribute ,RoomAttributeName ,DictionaryId1005 ,SubjectName ,SchoolYearStart ,SrcName ,NormalClass");

            return parameter;
        }

        /// <summary>
        /// 在线巡课条件查询
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        private List<DbParameter> GetFilterList(PatrolClassListParam param, StringBuilder strSql, string sortSql)
        {
                strSql.Append($@"SELECT * From (
                                SELECT  pc.Id ,pc.BaseIsDelete ,pc.BaseCreateTime ,pc.BaseModifyTime ,pc.BaseCreatorId ,pc.BaseModifierId ,pc.BaseVersion
			                       ,pc.UnitId ,u.Name AS UnitName ,ur.UnitId AS CountyId ,sa.AreaName AS CountyName ,ur2.UnitId AS CityId ,u3.Name AS CityName ,pc.FunRoomId ,fr.Name AS FunRoomName ,pc.DictionaryId1006A ,sd1.DicName AS ClassNameA ,pc.DictionaryId1006B ,sd2.DicName AS ClassNameB
			                       ,pc.SchoolTermName ,pc.RoomAttribute ,sd4.DicName AS RoomAttributeName ,pc.SchoolStage ,pc.DictionaryId1005 ,sd3.DicName AS SubjectName ,st1.SchoolYearStart
			                       ,pc.CourseSectionId
								  
                                   ,pc.BeginTime ,pc.EndTime ,pc.UseDate ,pc.IsClass ,pc.CameraId ,scm.SrcName AS SrcName ,pc.CameraIndexCode ,pc.Statuz ,pc.MaxPeopleNumber
                                FROM  bn_PatrolClass pc
	                            INNER JOIN  up_Unit AS u ON pc.UnitId = u.Id AND u.BaseIsDelete = 0
		                        INNER JOIN  up_UnitRelation AS ur ON u.Id = ur.ExtensionObjId AND ur.ExtensionType = 3 AND ur.BaseIsDelete = 0
                                INNER JOIN  up_Unit AS u2 ON ur.UnitId = u2.Id AND u2.BaseIsDelete = 0
                                INNER JOIN  up_UnitRelation AS ur2 ON u2.Id = ur2.ExtensionObjId AND ur2.ExtensionType = 3 AND ur2.BaseIsDelete = 0
                                INNER JOIN  up_Unit AS u3 ON ur2.UnitId = u3.Id AND u3.BaseIsDelete = 0
                                LEFT JOIN  SysArea AS sa ON sa.AreaCode = u2.AreaId AND sa.BaseIsDelete = 0
		                        INNER JOIN  bn_FunRoom AS fr ON fr.Id = pc.FunRoomId AND ur.BaseIsDelete = 0
		                        INNER JOIN  sys_static_dictionary AS sd1 ON pc.DictionaryId1006A = sd1.DictionaryId AND sd1.BaseIsDelete = 0
		                        INNER JOIN  sys_static_dictionary AS sd2 ON pc.DictionaryId1006B = sd2.DictionaryId AND sd2.BaseIsDelete = 0
		                        INNER JOIN  sys_static_dictionary AS sd3 ON pc.DictionaryId1005 = sd3.DictionaryId AND sd3.BaseIsDelete = 0
		                        INNER JOIN  sys_static_dictionary AS sd4 ON pc.RoomAttribute = sd4.DictionaryId AND sd4.BaseIsDelete = 0
		                        
                                LEFT JOIN  bn_SchoolTerm AS st1 ON st1.TermStart <= pc.UseDate AND pc.UseDate  <= st1.TermEnd AND st1.BaseIsDelete = 0
                                LEFT JOIN  bn_SchoolCamera AS scm ON scm.UnitId = pc.UnitId AND scm.FunRoomId = pc.FunRoomId AND scm.BaseIsDelete = 0 AND scm.Statuz = 1
		                        WHERE pc.BaseIsDelete = 0 AND pc.Statuz = 1  AND pc.IsClass = {param.IsClass} AND scm.SrcName IS NOT NULL AND scm.SrcName <> '' AND pc.MaxPeopleNumber IS NOT NULL
                            ) as tb1 WHERE  1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    strSql.Append(" AND UnitId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                else if (param.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    if(param.CountyId > 0)
                    {
                        strSql.Append(" AND CountyId = @CountyId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                        if (param.SchoolId > 0)
                        {
                            strSql.Append(" AND UnitId = @UnitId ");
                            parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.SchoolId));
                        }
                    }
                }
                else if (param.UnitType == UnitTypeEnum.City.ParseToInt())
                {
                    if(param.CityId > 0)
                    {
                        strSql.Append(" AND CityId = @CityId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@CityId", param.CityId));
                    }
                    if (param.CountyId > 0)
                    {
                        strSql.Append(" AND CountyId = @CountyId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                    }
                }

                if (param.DictionaryId1006A > 0)
                {
                    strSql.Append(" AND DictionaryId1006A = @DictionaryId1006A ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId1006A", param.DictionaryId1006A));
                }
                if (param.DictionaryId1006B > 0)
                {
                    strSql.Append(" AND DictionaryId1006B = @DictionaryId1006B ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId1006B", param.DictionaryId1006B));
                }
                if (param.DictionaryId1005 > 0)
                {
                    strSql.Append(" AND DictionaryId1005 = @DictionaryId1005 ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId1005", param.DictionaryId1005));
                }
                if (param.RoomAttribute > 0)
                {
                    strSql.Append(" AND RoomAttribute = @RoomAttribute ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@RoomAttribute", param.RoomAttribute));
                }
                if (param.SchoolYearStart > 0)
                {
                    strSql.Append(" AND SchoolYearStart = @SchoolYearStart ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolYearStart", param.SchoolYearStart));
                }
                if (!string.IsNullOrEmpty(param.SchoolTerm) && param.SchoolTerm != "-1")
                {
                    string schoolTermName = ((SchoolTermEnum)Convert.ToInt32(param.SchoolTerm)).GetDescription();
                    strSql.Append($" AND SchoolTermName like  '%{schoolTermName}%'");
                }
                if (!string.IsNullOrEmpty(param.BeginTime) && !string.IsNullOrEmpty(param.EndTime))
                {
                    strSql.Append($" AND ( (((EndTime <= '{param.BeginTime}') OR (BeginTime <= '{param.EndTime}')) AND UseDate = '{DateTime.Now.ToString("yyyy-MM-dd")}') "); //当天数据只显示当前时间节点之前的课程节次，未到时间的课程节次不显示
                    strSql.Append($" OR (BeginTime >= '00:00' AND EndTime <= '23:23' AND UseDate != '{DateTime.Now.ToString("yyyy-MM-dd")}') ) "); //历史数据显示所有时间节点的课程节次
                }
                if (!string.IsNullOrEmpty(param.UseDate.ToString("yyyy-MM-dd")) && param.UseDate.ToString("yyyy-MM-dd") != "0001-01-01")
                {
                    strSql.Append($" AND UseDate = '{param.UseDate.ToString("yyyy-MM-dd")}'");
                }
                if (param.CourseSectionNo > 0)
                {
                    strSql.Append($" AND CourseSectionNo = {param.CourseSectionNo}");
                }
                if(!string.IsNullOrEmpty(param.StartDate) && !string.IsNullOrEmpty(param.EndDate))
                {
                    strSql.Append($" AND UseDate between '{param.StartDate}' and '{param.EndDate}' ");

                }else if(!string.IsNullOrEmpty(param.StartDate) && string.IsNullOrEmpty(param.EndDate))
                {
                    strSql.Append($" AND UseDate >= '{param.StartDate}'");

                }else if(string.IsNullOrEmpty(param.StartDate) && !string.IsNullOrEmpty(param.EndDate))
                {
                    strSql.Append($" AND UseDate <= '{param.EndDate}'");
                }
                if (!string.IsNullOrEmpty(param.SrcName))
                {
                    strSql.Append($" AND SrcName = '{param.SrcName}'");
                }
                if (param.FunRoomId > 0)
                {
                    strSql.Append($" AND FunRoomId = {param.FunRoomId}");
                }
                if (!string.IsNullOrEmpty(param.FunRoomName) && param.FunRoomName.Length > 0)
                {
                    strSql.Append($" AND FunRoomName like  '%{param.FunRoomName}%'");
                }
                //if(param.IsCurrenSection > 0)
                //{
                //    strSql.Append($" AND IsCurrenSection = {param.IsCurrenSection}");
                //}
            }

            if (!string.IsNullOrEmpty(sortSql.ToString())) //分组排序查询
            {
                strSql.Append(sortSql);
            }
            return parameter;
        }

        /// <summary>
        /// 执行数据库操作
        /// </summary>
        /// <param name="execSql">需要执行的sql语句</param>
        /// <param name="operate">操作提示文字</param>
        /// <returns></returns>
        private int ExecDataBySql(string execSql, string operate)
        {
            try
            {
                if (!string.IsNullOrEmpty(execSql))
                {
                    var execResult = new SqlServerDatabase(GlobalContext.SystemConfig.DBConnectionString).ExecuteBySql(execSql);
                    return execResult.Result;
                }
                else
                {
                    return -1;
                }
            }
            catch (Exception ex)
            {
                throw new Exception("程序出现异常，异常信息为：" + ex.Message);
            }
        }

        /// <summary>
        /// 获取学期字符名称
        /// </summary>
        /// <param name="schoolYearStart">开始学年</param>
        /// <param name="schoolYearEnd">结束学年</param>
        /// <param name="schoolTerm">学期</param>
        /// <returns></returns>
        private string GetSchoolTermName(int schoolYearStart, int schoolYearEnd, int schoolTerm)
        {
            string name = "--";
            if (schoolYearStart > 1000 && schoolYearEnd > 1000)
            {
                name = string.Format("{0}~{1}", schoolYearStart % 100, schoolYearEnd % 100);
                if (schoolTerm == 1)
                {
                    name += "上学期";
                }
                else
                {
                    name += "下学期";
                }
            }
            return name;
        }
        #endregion
    }
}
