﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.OrganizationManage;
using Dqy.Syjx.Model.Param.OrganizationManage;
using Dqy.Syjx.Enum;

namespace Dqy.Syjx.Service.OrganizationManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-09-25 15:35
    /// 描 述：服务类
    /// </summary>
    public class UserExtensionService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<UserExtensionEntity>> GetList(UserExtensionListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<UserExtensionEntity>> GetPageList(UserExtensionListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<UserExtensionEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<UserExtensionEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(UserExtensionEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTranForm(UserExtensionEntity entity, Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            long[] idArr = TextHelper.SplitToArray<long>(ids, ',');
            await this.BaseRepository().Delete<UserExtensionEntity>(idArr);
        }

        public async Task UpdateExperimenterTrans(string ids, Repository db)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update up_UserExtension set IsExperimenter = 0 where BaseIsDelete = 0 AND SysUserId in ({ids}) ";
            }
            else
            {
                strSql = $" update up_UserExtension set IsExperimenter = 0 where BaseIsDelete = 0 AND SysUserId = {ids}";
            }
            await db.ExecuteBySql(strSql);
        }

        #endregion

        #region 私有方法
        private Expression<Func<UserExtensionEntity, bool>> ListFilter(UserExtensionListParam param)
        {
            var expression = LinqExtensions.True<UserExtensionEntity>();
            expression = expression.And(a => a.BaseIsDelete == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.SysUserId.Value));
                }
                if (param.UserId > 0)
                {
                    expression = expression.And(t => t.SysUserId == param.UserId);
                }
                if (param.UnitId > 0)
                {
                    expression = expression.And(t => t.UnitId == param.UnitId);
                }
            }
            return expression;
        }

        /// <summary>
        /// 统计实验员数量
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<List<UserExtensionEntity>> GetExperimenterNum(UserExtensionListParam param)
        {
            string sqlColumn = "";
            string sqlTable = "";
            if (param.SchoolStageId > 0)
            {
                sqlColumn = " ,dr6.DictionaryToId AS SchoolStageId ";
                sqlTable = @" LEFT JOIN  sys_dictionary_relation AS dr6 ON se7.SchoolProp = dr6.DictionaryId AND dr6.RelationType = 1 ";
            }
            StringBuilder strSql = new StringBuilder();
            strSql.Append(@$"  SELECT ExperimenterNature , Count(Id) as Num From (
                 SELECT s1.Id
                 ,s1.UserStatus
				 ,ue2.IsExperimenter
				 ,ue2.ExperimenterNature
				 ,ue2.UnitId
				 ,UR.UnitId AS CountyId
                 ,ur2.UnitId AS CityId
                 ,se7.SchoolProp
                 {sqlColumn}
				 FROM  SysUser AS s1
				 INNER JOIN  up_UserExtension AS ue2 ON s1.Id = ue2.SysUserId AND ue2.BaseIsDelete = 0 AND ue2.IsCurrentUnit = 1
				 INNER JOIN  up_UnitRelation AS UR ON ue2.UnitId = UR.ExtensionObjId AND UR.ExtensionType = 3 AND UR.BaseIsDelete = 0
				 INNER JOIN  up_UnitRelation AS ur2 ON UR.UnitId = ur2.ExtensionObjId AND ur2.ExtensionType = 3 AND ur2.BaseIsDelete = 0
                 LEFT JOIN  up_SchoolExtension AS se7 ON ue2.UnitId  = se7.UnitId AND se7.BaseIsDelete = 0
                 {sqlTable}
				 WHERE s1.BaseIsDelete = 0
                ) as tb1 WHERE 1 = 1 AND UserStatus = 1  ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    strSql.Append(" AND UnitId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                else if (param.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    strSql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.UnitId));
                }
                else if (param.UnitType == UnitTypeEnum.City.ParseToInt())
                {
                    strSql.Append(" AND CityId = @CityId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CityId", param.UnitId));
                }
                if (param.SchoolStageId > 0)
                {
                    strSql.Append(" AND SchoolStageId = @SchoolStageId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStageId", param.SchoolStageId));
                }
                if (param.IsExperimenter > -1)
                {
                    strSql.Append(" AND IsExperimenter = @IsExperimenter ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@IsExperimenter", param.IsExperimenter));
                }
                if (param.SchoolProple != -10000)
                {
                    strSql.Append(" AND SchoolProp <> @SchoolProple ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolProple", param.SchoolProple));
                }
            }
            strSql.Append(" GROUP BY CityId ,ExperimenterNature ");
            var list = await this.BaseRepository().FindList<UserExtensionEntity>(strSql.ToString(), parameter.ToArray());
            return list.ToList();
        }
        #endregion
    }
}
