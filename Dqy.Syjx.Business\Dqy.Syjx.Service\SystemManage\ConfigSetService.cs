﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.SystemManage;
using Dqy.Syjx.Model.Param.SystemManage;
using Dqy.Syjx.Enum;
using Castle.DynamicProxy.Generators.Emitters.SimpleAST;

namespace Dqy.Syjx.Service.SystemManage
{
    /// <summary>
    /// 创 建：jp
    /// 日 期：2021-09-24 16:35
    /// 描 述：服务类
    /// </summary>
    public class ConfigSetService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<ConfigSetEntity>> GetList(ConfigSetListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }
        /// <sum
        }

        public async Task<List<ConfigSetEntity>> GetList(ConfigSetListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }
        /// <summary>
        /// 获取系统默认配置列表
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<List<ConfigSetEntity>> GetPageList(ConfigSetListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            if (expression!=null)
            {
                expression = expression.And(t => t.ConfigType == 0);//读取初始化数据
                expression = expression.And(t => t.UnitId == 0);//读取初始化数据
            }

            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        /// <summary>
        /// 根据编码获取对应的配置信息(必填验证方法)。
        /// UnitId必须填写，单位如果为学校CityId和CountyId必须填写， 为区县CityId必须填写
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<List<ConfigSetEntity>> GetListByCode(ConfigSetListParam param)
        {
            List<ConfigSetEntity> data = new List<ConfigSetEntity>();
            var expression = LinqExtensions.True<ConfigSetEntity>();
            expression = expression.And(t => t.BaseIsDelete == 0);
            expression = expression.And(t => t.Statuz == StatusEnum.Yes.ParseToInt());
            expression = expression.And(t => t.ModuleType == param.ModuleType);
            expression = expression.And(t => t.ConfigType == 0);//读取初始化数据
            expression = expression.And(t => t.UnitId == 0);//读取初始化数据
            expression = expression.And(t => t.TypeCode.Contains(param.TypeCode));
            var list = await this.BaseRepository().FindList(expression);
            if (list != null && list.Count() > 0)
            {
                expression = LinqExtensions.True<ConfigSetEntity>();
                expression = expression.And(t => t.BaseIsDelete == 0);
                expression = expression.And(t => t.Statuz == StatusEnum.Yes.ParseToInt());
                expression = expression.And(t => t.ModuleType == param.ModuleType);
                //expression = expression.And(t => t.ConfigType == 0);//读取初始化数据
                //expression = expression.And(t => t.UnitType == UnitTypeEnum.City.ParseToInt() );//读取初始化数据
                //expression = expression.And(t => t.ConfigValue == "1");//读取初始化数据
                expression = expression.And(t => t.TypeCode.Contains(param.TypeCode));
                expression = expression.And(t => t.UnitId == param.UnitId || t.UnitId == param.CountyId || t.UnitId == param.CityId);//读取初始化数据
                var listAllData = await this.BaseRepository().FindList(expression);

                foreach (var item in list)
                {
                    if (param.UnitType == UnitTypeEnum.City && item.UnitType == UnitTypeEnum.City.ParseToInt())
                    {
                        if (listAllData != null && listAllData.Count() > 0)
                        {
                            var listTemp = listAllData.Where(m => m.UnitId == param.UnitId && m.TypeCode == item.TypeCode);
                            if (listTemp != null && listTemp.Count() > 0)
                            {
                                item.ConfigValue = listTemp.FirstOrDefault().ConfigValue;
                            }
                        }
                        data.Add(item);
                    }
                    else if (param.UnitType == UnitTypeEnum.County && (item.UnitType == UnitTypeEnum.County.ParseToInt() || item.UnitType == UnitTypeEnum.City.ParseToInt()))
                    {
                        if (listAllData != null && listAllData.Count() > 0)
                        {
                            var listTemp = listAllData.Where(m => m.UnitId == param.CityId && m.TypeCode == item.TypeCode && m.ConfigValue == "1");
                            if (listTemp != null && listTemp.Count() > 0)
                            {
                                item.ConfigValue = listTemp.FirstOrDefault().ConfigValue;
                            }
                            else
                            {
                                var listCounty = listAllData.Where(m => m.UnitId == param.UnitId && m.TypeCode == item.TypeCode);
                                if (listCounty != null && listCounty.Count() > 0)
                                {
                                    item.ConfigValue = listCounty.FirstOrDefault().ConfigValue;
                                }
                            }
                        }
                        data.Add(item);
                    }
                    else if (param.UnitType == UnitTypeEnum.School)
                    {
                        if (listAllData != null && listAllData.Count() > 0)
                        {
                            var listTemp = listAllData.Where(m => m.UnitId == param.CityId && m.TypeCode == item.TypeCode && m.ConfigValue == "1");
                            if (listTemp != null && listTemp.Count() > 0)
                            {
                                item.ConfigValue = listTemp.FirstOrDefault().ConfigValue;
                            }
                            else
                            {
                                var listCounty = listAllData.Where(m => m.UnitId == param.CountyId && m.TypeCode == item.TypeCode && m.ConfigValue == "1");
                                if (listCounty != null && listCounty.Count() > 0)
                                {
                                    item.ConfigValue = listCounty.FirstOrDefault().ConfigValue;
                                }
                                else
                                {
                                    var listSchool = listAllData.Where(m => m.UnitId == param.UnitId && m.TypeCode == item.TypeCode);
                                    if (listSchool != null && listSchool.Count() > 0)
                                    {
                                        item.ConfigValue = listSchool.FirstOrDefault().ConfigValue;
                                    }
                                }
                            }
                        }
                        data.Add(item);
                    }
                }
            }
            return data;
        }

        public async Task<ConfigSetEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<ConfigSetEntity>(id);
        }

        public async Task<List<ConfigSetEntity>> GetListByTypeCode(ConfigSetListParam param)
        {
            var expression = ListFilterByTypeCode(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        /// <summary>
        /// 判断是否存在验证配置数据
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<List<ConfigSetEntity>> IsExistPageValidData(ConfigSetListParam param)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = PageValidDataListFilter(param, strSql);
            var list = await this.BaseRepository().FindList<ConfigSetEntity>(strSql.ToString(), filter.ToArray());
            return list.ToList();
        }

        /// <summary>
        /// 获取填报验证配置数据
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<List<ConfigSetEntity>> GetPageValidDataList(ConfigSetListParam param)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = new List<DbParameter>();

            if(param.UnitType.ParseToInt() == UnitTypeEnum.School.ParseToInt()) //学校
            {
                filter = SchoolPageValidDataListFilter(param, strSql);
                var allList = await this.BaseRepository().FindList<ConfigSetEntity>(strSql.ToString(), filter.ToArray());

                // 在C#中处理分组和排序逻辑，替代原来的ROW_NUMBER()
                var groupedList = allList.GroupBy(x => x.TypeCode)
                    .Select(g => {
                        var orderedGroup = g.OrderBy(x => x.UnitType == 2 && x.ConfigValue == "1" ? 0 : 1) // FieldIsModify 逻辑
                                          .ThenByDescending(x => x.UnitType) // UnitType DESC
                                          .ThenBy(x => x.TypeCode); // TypeCode ASC
                        var selectedItem = orderedGroup.First(); // 取每组第一个

                        // 设置 FieldIsModify 属性
                        selectedItem.FieldIsModify = (selectedItem.UnitType == 2 && selectedItem.ConfigValue == "1") ? 0 : 1;

                        return selectedItem;
                    })
                    .OrderBy(x => x.TypeCode)
                    .ToList();

                return groupedList;
            }

            if(param.UnitType.ParseToInt() == UnitTypeEnum.County.ParseToInt()) //区县
            {
                filter = PageValidDataListFilter(param, strSql);
                var list = await this.BaseRepository().FindList<ConfigSetEntity>(strSql.ToString(), filter.ToArray());

                // 为区县数据设置 FieldIsModify 属性
                foreach (var item in list)
                {
                    item.FieldIsModify = 1; // 区县数据默认为1
                }

                return list.ToList();
            }

            return new List<ConfigSetEntity>();
        }

        public async Task<List<ConfigSetEntity>> GetListByTypeCode(string typeCode)
        {
            var list = await this.BaseRepository().FindList<ConfigSetEntity>(f => f.TypeCode == typeCode);
            return list.ToList();
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(ConfigSetEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveAllByCodeForm(ConfigSetEntity entity,string newtypecode,string typecode)
        {
            List<DbParameter> filter = new List<DbParameter>();
            filter.Add(DbParameterExtension.CreateDbParameter("@0", entity.TypeCode));
            filter.Add(DbParameterExtension.CreateDbParameter("@1", entity.TypeName));
            filter.Add(DbParameterExtension.CreateDbParameter("@2", entity.ModuleName));
            filter.Add(DbParameterExtension.CreateDbParameter("@3", entity.MenuName));
            filter.Add(DbParameterExtension.CreateDbParameter("@4", entity.VerifyHint));
            filter.Add(DbParameterExtension.CreateDbParameter("@5", typecode));
            await this.BaseRepository().ExecuteBySql(" UPDATE sys_ConfigSet SET TypeCode = @0 ,TypeName = @1 ,ModuleName = @2 , MenuName = @3,VerifyHint = @4 WHERE ModuleType = 2 AND TypeCode = @5 ", filter.ToArray());
        }

        public async Task DeleteForm(string ids)
        {
            long[] idArr = TextHelper.SplitToArray<long>(ids, ',');
            await this.BaseRepository().Delete<ConfigSetEntity>(idArr);
        }
        #endregion

        #region 私有方法
        private Expression<Func<ConfigSetEntity, bool>> ListFilter(ConfigSetListParam param)
        {
            var expression = LinqExtensions.True<ConfigSetEntity>();
            expression = expression.And(t => t.BaseIsDelete == 0);
            expression = expression.And(t => t.Statuz == StatusEnum.Yes.ParseToInt());
            if (param != null)
            {
                if (param.UnitId >= 0)
                {
                    expression = expression.And(t => t.UnitId == param.UnitId);
                }
                if (param.UnitType.HasValue)
                {
                    expression = expression.And(t => t.UnitType == param.UnitType.ParseToInt());
                }
                if (!string.IsNullOrEmpty(param.ModuleName))
                {
                    expression = expression.And(t => t.ModuleName.Contains(param.ModuleName));
                }
                if (!string.IsNullOrEmpty(param.ModuleCode))
                {
                    expression = expression.And(t => t.ModuleCode.Equals(param.ModuleCode));
                }
                if (!string.IsNullOrEmpty(param.MenuName))
                {
                    expression = expression.And(t => t.MenuName.Contains(param.MenuName));
                }
                if (!string.IsNullOrEmpty(param.TypeName))
                {
                    expression = expression.And(t => t.TypeName.Contains(param.TypeName));
                }
                if (!string.IsNullOrEmpty(param.TypeCode))
                {
                    expression = expression.And(t => t.TypeCode.Contains(param.TypeCode));
                }
            }
            return expression;
        }

        private Expression<Func<ConfigSetEntity, bool>> ListFilterByTypeCode(ConfigSetListParam param)
        {
            var expression = LinqExtensions.True<ConfigSetEntity>();
            expression = expression.And(t => t.BaseIsDelete == 0);
            expression = expression.And(t => t.Statuz == StatusEnum.Yes.ParseToInt());

            if (param != null)
            {
                if (param.UnitId >= 0)
                    expression = expression.And(t => t.UnitId == param.UnitId);

                if (param.UnitType.HasValue)
                    expression = expression.And(t => t.UnitType == param.UnitType.ParseToInt());

                if (param.ConfigType >= 0)
                    expression = expression.And(t => t.ConfigType == param.ConfigType);
                if (!string.IsNullOrEmpty(param.TypeCode))
                    expression = expression.And(t => t.TypeCode == param.TypeCode);
                if (!string.IsNullOrEmpty(param.ModuleCode))
                    expression = expression.And(t => t.ModuleCode == param.ModuleCode);
            }
            return expression;
        }
        /// <summary>
        /// 获取学校配置的页面填报验证字段
        /// </summary>
        /// <param name="param"></param>
        /// <param name="strSql"></param>
        /// <returns></returns>
        private List<DbParameter> SchoolPageValidDataListFilter(ConfigSetListParam param, StringBuilder strSql)
        {
            // 使用完全通用的SQL语句，移除所有数据库特定语法，包含所有必要字段
            strSql.Append(@"SELECT cs.Id, cs.BaseIsDelete, cs.BaseCreateTime, cs.BaseModifyTime,
                                   cs.BaseCreatorId, cs.BaseModifierId, cs.BaseVersion, cs.UnitId,
                                   cs.UnitType, cs.ModuleType, cs.ModuleCode, cs.ModuleName,
                                   cs.MenuName, cs.TypeCode, cs.TypeName, cs.ConfigValue,
                                   cs.ConfigType, cs.Statuz, cs.VerifyHint, cs.Remark,
                                   cs.ValueType, cs.ComboValues, cs.UserId, cs.RegDate
                            FROM sys_ConfigSet cs
                            WHERE cs.Statuz = 1 AND cs.BaseIsDelete = 0");

            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.ModuleType > 0)
                {
                    strSql.Append(" AND cs.ModuleType = @ModuleType");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ModuleType", param.ModuleType));
                }
                if (!string.IsNullOrEmpty(param.ModuleCode))
                {
                    strSql.Append(" AND cs.ModuleCode = @ModuleCode");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ModuleCode", param.ModuleCode));
                }
                if (param.UnitId > -1 && param.CountyId > -1)
                {
                    strSql.Append(" AND (cs.UnitId = @UnitId OR cs.UnitId = @CountyId)");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
            }

            // 移除ORDER BY，让C#代码处理排序
            return parameter;
        }

        /// <summary>
        /// 获取区县配置的页面填报验证字段
        /// </summary>
        /// <param name="param"></param>
        /// <param name="strSql"></param>
        /// <returns></returns>
        private List<DbParameter> PageValidDataListFilter(ConfigSetListParam param, StringBuilder strSql)
        {
            // 使用通用SQL语句，移除计算字段，在C#中处理，包含所有必要字段
            strSql.Append(@"SELECT cs.Id, cs.BaseIsDelete, cs.BaseCreateTime, cs.BaseModifyTime,
                                   cs.BaseCreatorId, cs.BaseModifierId, cs.BaseVersion, cs.UnitId,
                                   cs.UnitType, cs.ModuleType, cs.ModuleCode, cs.ModuleName,
                                   cs.MenuName, cs.TypeCode, cs.TypeName, cs.ConfigValue,
                                   cs.ConfigType, cs.Statuz, cs.VerifyHint, cs.Remark,
                                   cs.ValueType, cs.ComboValues, cs.UserId, cs.RegDate
                            FROM sys_ConfigSet cs
                            WHERE cs.BaseIsDelete = 0 AND cs.Statuz = 1");

            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if(param.ModuleType > 0)
                {
                    strSql.Append(" AND cs.ModuleType = @ModuleType");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ModuleType", param.ModuleType));
                }
                if (!string.IsNullOrEmpty(param.ModuleCode))
                {
                    strSql.Append(" AND cs.ModuleCode = @ModuleCode");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ModuleCode", param.ModuleCode));
                }
                if (param.UnitId > -1)
                {
                    strSql.Append(" AND cs.UnitId = @UnitId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                if (param.UnitType.HasValue)
                {
                    strSql.Append(" AND cs.UnitType = @UnitType");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitType", param.UnitType.ParseToInt()));
                }
            }

            return parameter;
        }

        //private Expression<Func<ConfigSetEntity, bool>> GetSystemNoExistsCustomDataListFilter(ConfigSetListParam param)
        //{
        //    var expression = LinqExtensions.True<ConfigSetEntity>();
        //    expression = expression.And(t => t.Statuz == StatusEnum.Yes.ParseToInt())
        //        .And(t => t.ConfigType == 0)
        //        .And(t => t.UnitType == param.UnitType.ParseToInt())
        //        .And(t=>t.TypeCode.Contains());

        //    return expression;
        //}
        #endregion
    }
}

