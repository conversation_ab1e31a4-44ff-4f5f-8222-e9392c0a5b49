using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.IO;
using System.Threading.Tasks;
using System.Linq.Expressions;
using System.Data;
using System.Data.Common;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.OrganizationManage;
using Dqy.Syjx.Enum.OrganizationManage;
using Dqy.Syjx.Model.Param.OrganizationManage;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Entity;
using Dqy.Syjx.Data.EF;
using Dqy.Syjx.Service.SystemManage;
using Dqy.Syjx.Data;
using static Dqy.Syjx.Util.ThirdOAuth.TztxSoftAcore2;
using System.Reflection;
using NPOI.OpenXmlFormats.Wordprocessing;
using Dqy.Syjx.Model.Param.SystemManage;
using Dqy.Syjx.Entity.SystemManage;

namespace Dqy.Syjx.Service.OrganizationManage
{
    public class UserService : RepositoryFactory
    {

        private StaticDictionaryService staticDictionaryService = new StaticDictionaryService();

        private UnitRelationService relationService = new UnitRelationService();

        #region 获取数据
        public async Task<List<UserEntity>> GetList(UserListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<UserEntity>> GetPageList(UserListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        /// <summary>
        /// 获取所有未删除信息
        /// </summary>
        /// <returns></returns>
        public async Task<List<UserEntity>> GetListAll()
        {
            var expression = LinqExtensions.True<UserEntity>();
            expression = expression.And(t => t.BaseIsDelete == 0);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<UserEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<UserEntity>(id);
        }

        public async Task<UserEntity> GetEntity(string userName)
        {
            return await this.BaseRepository().FindEntity<UserEntity>(p => p.UserName == userName);
        }

        /// <summary>
        /// 根据手机号码获取用户
        /// </summary>
        /// <param name="mobile"></param>
        /// <returns></returns>
        public async Task<UserEntity> GetEntityByMobile(string mobile)
        {
            return await this.BaseRepository().FindEntity<UserEntity>(p => p.Mobile == mobile && p.BaseIsDelete == 0);
        }

        /// <summary>
        /// 修改用户时验证手机号码是否存在
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="mobile"></param>
        /// <returns></returns>
        public async Task<UserEntity> GetEntityByMobile(long userId,string mobile)
        {
            return await this.BaseRepository().FindEntity<UserEntity>(p => p.Mobile == mobile && p.Id != userId && p.BaseIsDelete == 0);
        }

        public async Task<UserEntity> CheckLogin(string userName)
        {
            var expression = LinqExtensions.True<UserEntity>();
            expression = expression.And(t => t.BaseIsDelete == 0);
            expression = expression.And(t => t.UserName == userName || t.Mobile == userName);
            //expression = expression.And(t => t.UserName == userName);
            return await this.BaseRepository().FindEntity(expression);
        }

        public bool ExistUserName(UserEntity entity)
        {
            var expression = LinqExtensions.True<UserEntity>();
            expression = expression.And(t => t.BaseIsDelete == 0);
            if (entity.Id.IsNullOrZero())
            {
                expression = expression.And(t => t.UserName == entity.UserName);
            }
            else
            {
                expression = expression.And(t => t.UserName == entity.UserName && t.Id != entity.Id);
            }
            return this.BaseRepository().IQueryable(expression).Count() > 0 ? true : false;
        }


        /// <summary>
        /// 根据第三方id，获取用户信息
        /// </summary>
        /// <param name="thirdUnitId"></param>
        /// <returns></returns>
        public async Task<List<UserEntity>> GetThirdUser(string thirdUserId)
        {
            return (await GetList(new UserListParam { ThirdUserId = thirdUserId }));

            //return await this.BaseRepository().FindEntity<UserEntity>(a => a.ThirdUserId == thirdUserId);
        }


        public async Task<List<UserEntity>> GetUserList(UserListParam param)
        {
            /*by wanglei 2024-04-12。
            必须使用INNER JOIN  SysUserBelong，否则验证是否为两个管理员时，无效。
            如果有的功能：为了保证匿名用户能在用户列表中出现，可以向SysUserBelong插入一条BelongId = -99的数据，这样可以为匿名用户授权
            */
            StringBuilder strSql = new StringBuilder();
            var parameter = new List<DbParameter>();
            strSql.Append(@"SELECT * FROM
                            (SELECT SU.BaseIsDelete,SU.Id,SU.UserName,SU.RealName,SU.CreateUnitId,UE.UnitId
                             FROM  SysUser AS SU
                             INNER JOIN  up_UnitRelation AS UE ON SU.Id = UE.ExtensionObjId AND UE.ExtensionType = 1
                             INNER JOIN  SysUserBelong AS SB ON SU.Id = SB.UserId  AND SB.BelongType = 2 AND SB.BelongId = " + param.RoleId + " ) A");
            if (param != null)
            {
                strSql.Append(" WHERE UnitId = @UnitId AND BaseIsDelete = 0 ");
                parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));

                strSql.Append(" AND CreateUnitId = @CreateUnitId");
                parameter.Add(DbParameterExtension.CreateDbParameter("@CreateUnitId", param.CreateUnitId));

                if (param.Id > 0)
                {
                    strSql.Append(" AND Id <> @Id");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Id", param.Id));
                }
            }
            var list = await this.BaseRepository().FindList<UserEntity>(strSql.ToString(), parameter.ToArray());
            return list.DistinctBy(m => m.Id).ToList();
        }

        /// <summary>
        /// 获取用户信息列表
        /// </summary>
        /// <param name="param">查询参数</param>
        /// <param name="pagination">分页参数</param>
        /// <returns></returns>
        public async Task<List<UserEntity>> GetUserList(UserListParam param, Pagination pagination)
        {
            StringBuilder strList = new StringBuilder();
            string where = $" UnitId = {param.UnitId} AND BaseIsDelete = 0 ";
            if (param != null)
            {
                if (param.RoleId != null && param.RoleId != -1)
                {
                    where += $" AND RoleId = {param.RoleId}";
                }
                if (param.UserStatus != null && param.UserStatus != -1)
                {
                    where += $" AND UserStatus = {param.UserStatus}";
                }
                if (!string.IsNullOrEmpty(param.RealName))
                {
                    where += $" AND (RealName like '%{param.RealName}%' OR UserName LIKE '%{param.RealName}%' OR Mobile LIKE '%{param.RealName}%')";
                }
                if (param.UserIds.Length > 0)
                {
                    where += $" AND Id = {param.UserIds}";
                }
                if (param.ChildrenDepartmentIdList != null && param.ChildrenDepartmentIdList.Count > 0)
                {
                    int index = 0;
                    foreach (long id in param.ChildrenDepartmentIdList)
                    {
                        if (index == 0)
                        {
                            where += $" AND ( DepartmentId = {id} ";
                        }
                        else
                        {
                            where += $" OR DepartmentId = {id} ";
                        }
                        index++;
                    }
                    where += " )";
                }
            }

            string strBody = @$" SELECT * FROM
	                            (
		                            SELECT SU.Id ,SU.RealName ,SU.UserName,SU.Mobile,SU.UserStatus,SU.Email ,SU.Birthday ,SU.Gender,UE.UnitId,SU.BaseIsDelete ,
			                               UC.SubjectIdz,R.RoleName,R.Id AS RoleId,D.Id AS DepartmentId,D.DepartmentName
		                            FROM  SysUser AS SU
		                            LEFT JOIN SysUserBelong AS SUB ON SU.Id = SUB.UserId
		                            LEFT JOIN SysRole AS R ON SUB.BelongId = R.Id AND SUB.BelongType = 2
		                            LEFT JOIN  up_UnitRelation AS UE ON SU.Id = UE.ExtensionObjId AND UE.ExtensionType = 1
		                            LEFT JOIN  up_DepeartmentRelation AS DR ON DR.ExtensionObjId = SU.Id AND DR.ExtensionType = 1 AND DR.UnitId = UE.UnitId
		                            LEFT JOIN SysDepartment AS D ON DR.SysDepartmentId = D.Id AND D.Statuz = 1
		                            LEFT JOIN  up_UserClassInfo AS UC ON UC.UserId = SU.Id AND UC.BaseIsDelete = 0

	                            ) AS A
	                            WHERE {where}";

            var parameter = new List<DbParameter>();
            strList.Append(@$"SELECT Id,RealName ,UserName,Mobile,UserStatus,Email ,Birthday ,Gender,UnitId,BaseIsDelete,SubjectIdz
                            FROM
                            (
	                           {strBody}
                            ) AS B
                            GROUP BY Id,RealName ,UserName,Mobile,UserStatus,Email ,Birthday ,Gender,UnitId,BaseIsDelete,SubjectIdz ");

            string strRole = @$"SELECT Id,RoleId,RoleName
                               FROM
                               (
                                  {strBody}
                               ) AS B
                               GROUP BY Id,RoleId ,RoleName";

            string strDepartment = @$"SELECT Id,DepartmentId,DepartmentName
                               FROM
                               (
                                  {strBody}
                               ) AS B
                               GROUP BY Id,DepartmentId ,DepartmentName";

            var listUser = await this.BaseRepository().FindList<UserEntity>(strList.ToString(), parameter.ToArray(), pagination);

            var roleList = await this.BaseRepository().FindList<UserExtendModel>(strRole);
            var userRole = roleList.GroupBy(x => x.Id)
                  .Select(g => new
                  {
                      Id = g.Key,
                      RoleIds = String.Join(",", g.Where(f => f.RoleId > 0).Select(x => x.RoleId)),
                      RoleNames = String.Join(",", g.Where(f => f.RoleId > 0).Select(x => x.RoleName))
                  })
                  .ToList();

            var departmentList = await this.BaseRepository().FindList<UserExtendModel>(strDepartment);
            var userDepartment = departmentList.GroupBy(x => x.Id)
                 .Select(g => new
                 {
                     Id = g.Key,
                     DepartmentIds = String.Join(",", g.Where(f => f.DepartmentId > 0).Select(x => x.DepartmentId)),
                     DepartmentNames = String.Join(",", g.Where(f => f.DepartmentId > 0).Select(x => x.DepartmentName))
                 })
                 .ToList();

            var userList = listUser.Join(userRole, p1 => p1.Id, p2 => p2.Id, (p1, p2) =>
           new UserEntity()
           {
               Id = p1.Id,
               UserName = p1.UserName,
               RoleIds = p2.RoleIds,
               RoleNames = p2.RoleNames,
               RealName = p1.RealName,
               Mobile = p1.Mobile,
               UserStatus = p1.UserStatus,
               Email = p1.Email,
               Birthday = p1.Birthday,
               Gender = p1.Gender,
               UnitId = p1.UnitId,
               UnitName = p1.UnitName,
               BaseIsDelete = p1.BaseIsDelete,
               CreateUnitId = p1.CreateUnitId,
               SubjectIdz = p1.SubjectIdz,
               UnitType = p1.UnitType
           }).ToList();
            List<StaticDictionaryEntity> listDic = await staticDictionaryService.GetList(new StaticDictionaryListParam() { TypeCode = "1005", Statuz = 1 });

            userList = userList.Join(userDepartment, p1 => p1.Id, p2 => p2.Id, (p1, p2) =>
            new UserEntity()
            {
                Id = p1.Id,
                UserName = p1.UserName,
                RoleIds = p1.RoleIds,
                RoleNames = p1.RoleNames,
                RealName = p1.RealName,
                Mobile = p1.Mobile,
                UserStatus = p1.UserStatus,
                Email = p1.Email,
                Birthday = p1.Birthday,
                Gender = p1.Gender,
                UnitId = p1.UnitId,
                UnitName = p1.UnitName,
                BaseIsDelete = p1.BaseIsDelete,
                CreateUnitId = p1.CreateUnitId,
                UnitType = p1.UnitType,
                DepartmentIds = p2.DepartmentIds,
                DepartmentNames = p2.DepartmentNames,
                SubjectIdz = p1.SubjectIdz,
                SubjectNamez = string.Join(",", listDic.Where(f =>p1.SubjectIdz != null && p1.SubjectIdz.Contains(f.DictionaryId.ToString())).Select(n=>n.DicName))
            }).ToList();

            return userList;
        }


        /// <summary>
        /// 获取区县用户列表，增加管理学段、管理学科
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<List<UserEntity>> GetCountyUserList(UserListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            var parameter = new List<DbParameter>();
            string where = $" UnitId = {param.UnitId} AND BaseIsDelete = 0 ";
            string whereRole = where;
            string whereDepartment = where;
            string whereTemp = $" UnitId = {param.UnitId} AND BaseIsDelete = 0 ";
            if (param != null)
            {
                if (param.RoleId != null && param.RoleId != -1)
                {
                    where += $" AND RoleId = {param.RoleId}";
                    whereRole += $" AND RoleId = {param.RoleId}";
                }
                if (param.UserStatus != null && param.UserStatus != -1)
                {
                    where += $" AND UserStatus = {param.UserStatus}";
                    whereRole += $" AND UserStatus = {param.UserStatus}";
                    whereDepartment += $" AND UserStatus = {param.UserStatus}";
                    whereTemp += $" AND UserStatus = {param.UserStatus}";
                }
                if (param.UserIds.Length > 0)
                {
                    where += $" AND Id = {param.UserIds}";
                    whereRole += $" AND Id = {param.UserIds}";
                    whereDepartment += $" AND Id = {param.UserIds}";
                    whereTemp += $" AND Id = {param.UserIds}";
                }
                if (!string.IsNullOrEmpty(param.RealName))
                {
                    where += $" AND (RealName like '%{param.RealName}%' OR UserName LIKE '%{param.RealName}%' OR Mobile LIKE '%{param.RealName}%')";
                    whereRole += $" AND (RealName like '%{param.RealName}%' OR UserName LIKE '%{param.RealName}%' OR Mobile LIKE '%{param.RealName}%')";
                    whereDepartment += $" AND (RealName like '%{param.RealName}%' OR UserName LIKE '%{param.RealName}%' OR Mobile LIKE '%{param.RealName}%')";
                    whereTemp += $" AND (RealName like '%{param.RealName}%' OR UserName LIKE '%{param.RealName}%' OR Mobile LIKE '%{param.RealName}%')";
                }

                if (param.ChildrenDepartmentIdList != null && param.ChildrenDepartmentIdList.Count > 0)
                {
                    int index = 0;
                    foreach (long id in param.ChildrenDepartmentIdList)
                    {
                        if (index == 0)
                        {
                            where += $" AND (DepartmentId = {id}";
                            whereDepartment += $" AND (DepartmentId = {id}";
                        }
                        else
                        {
                            where += $" OR DepartmentId = {id}";
                            whereDepartment += $" OR DepartmentId = {id}";
                        }
                        index++;
                    }
                    where += " ) ";
                    whereDepartment += " ) ";
                }
            }

            string list = @$"SELECT Id ,RealName ,UserName,Mobile,UserStatus,Email ,Birthday ,Gender,UnitId,CreateUnitId,BaseIsDelete,BaseModifyTime
                        FROM
                        (
	                        SELECT * FROM
	                        (
		                        SELECT SU.Id ,SU.RealName ,SU.UserName,SU.Mobile,SU.UserStatus,SU.IsSystem,
				                        SU.Email ,SU.Birthday ,SU.Gender,UE.UnitId,SU.CreateUnitId,
				                        SU.BaseIsDelete,SU.BaseModifyTime,R.RoleName,R.Id AS RoleId,D.Id AS DepartmentId,D.DepartmentName
                                FROM  SysUser AS SU
		                        LEFT JOIN SysUserBelong AS SUB ON SU.Id = SUB.UserId
		                        LEFT JOIN SysRole AS R ON SUB.BelongId = R.Id AND SUB.BelongType = 2
                                LEFT JOIN  up_UnitRelation AS UE ON SU.Id = UE.ExtensionObjId AND UE.ExtensionType = 1
		                        LEFT JOIN  up_DepeartmentRelation AS DR ON DR.ExtensionObjId = SU.Id AND DR.ExtensionType = 1 AND DR.UnitId = UE.UnitId
		                        LEFT JOIN  SysDepartment AS D ON DR.SysDepartmentId = D.Id AND D.Statuz = 1
	                        ) AS A
	                        WHERE {where}
                        ) AS B
                        GROUP BY Id ,RealName ,UserName,Mobile,UserStatus,Email ,Birthday ,Gender,UnitId,CreateUnitId,BaseIsDelete,BaseModifyTime";

            string sqlRole = @$"SELECT * FROM
                        (
	                        SELECT SU.Id ,SU.RealName ,SU.UserName,SU.Mobile,SU.UserStatus,SU.IsSystem,
			                        SU.Email ,SU.Birthday ,SU.Gender,UE.UnitId,SU.CreateUnitId,
			                        SU.BaseIsDelete,SU.BaseModifyTime,R.RoleName,R.Id AS RoleId
                            FROM  SysUser AS SU
	                        LEFT JOIN SysUserBelong AS SUB ON SU.Id = SUB.UserId
	                        LEFT JOIN SysRole AS R ON SUB.BelongId = R.Id AND SUB.BelongType = 2
                            LEFT JOIN  up_UnitRelation AS UE ON SU.Id = UE.ExtensionObjId AND UE.ExtensionType = 1
                        ) AS A
                        WHERE  {whereRole}";

            string strDepartment = @$"SELECT * FROM
                (
	                SELECT SU.Id ,SU.RealName ,SU.UserName,SU.Mobile,SU.UserStatus,SU.IsSystem,
			                SU.Email ,SU.Birthday ,SU.Gender,UE.UnitId,SU.CreateUnitId,
			                SU.BaseIsDelete,SU.BaseModifyTime,D.Id AS DepartmentId,D.DepartmentName
                    FROM  SysUser AS SU
                    LEFT JOIN  up_UnitRelation AS UE ON SU.Id = UE.ExtensionObjId AND UE.ExtensionType = 1
	                LEFT JOIN  up_DepeartmentRelation AS DR ON DR.ExtensionObjId = SU.Id AND DR.ExtensionType = 1 AND DR.UnitId = UE.UnitId
	                LEFT JOIN SysDepartment AS D ON DR.SysDepartmentId = D.Id AND D.Statuz = 1
                ) AS A
                WHERE {whereDepartment}  ";

            string strStage = @$"SELECT * FROM
                    (
                        SELECT SU.Id ,SU.RealName ,SU.UserName,SU.Mobile,SU.UserStatus,SU.IsSystem,
		                        SU.Email ,SU.Birthday ,SU.Gender,UE.UnitId,SU.CreateUnitId,
		                        SU.BaseIsDelete,SU.BaseModifyTime
                        ,SD.DictionaryId AS StageId,SD.DicName AS StageName
                        FROM  SysUser AS SU
                        LEFT JOIN  up_UnitRelation AS UE ON SU.Id = UE.ExtensionObjId AND UE.ExtensionType = 1
                        LEFT JOIN  up_UserSubjectSet AS D ON SU.Id = D.UserId
                        LEFT JOIN  sys_static_dictionary AS SD ON D.SubjectValue = SD.DictionaryId AND D.SubjectTypeId = 1 AND SD.TypeCode = '1002'
                    ) AS A
                WHERE {whereTemp} ";

            string strCourse = @$"SELECT * FROM
                    (
                        SELECT SU.Id ,SU.RealName ,SU.UserName,SU.Mobile,SU.UserStatus,SU.IsSystem,
		                        SU.Email ,SU.Birthday ,SU.Gender,UE.UnitId,SU.CreateUnitId,
		                        SU.BaseIsDelete,SU.BaseModifyTime,SD.DictionaryId AS CourseId,SD.DicName AS CourseName
                        FROM  SysUser AS SU
                        LEFT JOIN  up_UnitRelation AS UE ON SU.Id = UE.ExtensionObjId AND UE.ExtensionType = 1
                        LEFT JOIN  up_UserSubjectSet AS D ON SU.Id = D.UserId
                        LEFT JOIN  sys_static_dictionary AS SD ON D.SubjectValue = SD.DictionaryId AND D.SubjectTypeId = 2 AND SD.TypeCode = '1005'
                    ) AS A
                WHERE {whereTemp} ";

            var listUser = await this.BaseRepository().FindList<UserEntity>(list, parameter.ToArray(), pagination);

            var roleList = await this.BaseRepository().FindList<UserExtendModel>(sqlRole);
            var userRole = roleList.GroupBy(x => x.Id)
                  .Select(g => new
                  {
                      Id = g.Key,
                      RoleIds = String.Join(",", g.Where(f => f.RoleId > 0).Select(x => x.RoleId)),
                      RoleNames = String.Join(",", g.Where(f => f.RoleId > 0).Select(x => x.RoleName))
                  })
                  .ToList();

            var departmentList = await this.BaseRepository().FindList<UserExtendModel>(strDepartment);
            var userDepartment = departmentList.GroupBy(x => x.Id)
                 .Select(g => new
                 {
                     Id = g.Key,
                     DepartmentIds = String.Join(",", g.Where(f => f.DepartmentId > 0).Select(x => x.DepartmentId)),
                     DepartmentNames = String.Join(",", g.Where(f => f.DepartmentId > 0).Select(x => x.DepartmentName))
                 })
                 .ToList();

            var stageList = await this.BaseRepository().FindList<UserExtendModel>(strStage);
            var userStage = stageList.GroupBy(x => x.Id)
                .Select(g => new
                {
                    Id = g.Key,
                    StageNames = String.Join(",", g.Where(f => f.StageId > 0).Select(x => x.StageName))
                })
                .ToList();

            var courseList = await this.BaseRepository().FindList<UserExtendModel>(strCourse);
            var userCourse = courseList.GroupBy(x => x.Id)
               .Select(g => new
               {
                   Id = g.Key,
                   CourseNames = String.Join(",", g.Where(f => f.CourseId > 0).Select(x => x.CourseName))
               })
               .ToList();


            var userList = listUser.Join(userRole, p1 => p1.Id, p2 => p2.Id, (p1, p2) =>
           new UserEntity()
           {
               Id = p1.Id,
               UserName = p1.UserName,
               RoleIds = p2.RoleIds,
               RoleNames = p2.RoleNames,
               RealName = p1.RealName,
               Mobile = p1.Mobile,
               UserStatus = p1.UserStatus,
               Email = p1.Email,
               Birthday = p1.Birthday,
               Gender = p1.Gender,
               UnitId = p1.UnitId,
               BaseModifyTime = p1.BaseModifyTime,
               UnitType = p1.UnitType
           }).ToList();

            userList = userList.Join(userDepartment, p1 => p1.Id, p2 => p2.Id, (p1, p2) =>
            new UserEntity()
            {
                Id = p1.Id,
                UserName = p1.UserName,
                RoleIds = p1.RoleIds,
                RoleNames = p1.RoleNames,
                RealName = p1.RealName,
                DepartmentIds = p2.DepartmentIds,
                DepartmentNames = p2.DepartmentNames,
                Mobile = p1.Mobile,
                UserStatus = p1.UserStatus,
                Email = p1.Email,
                Birthday = p1.Birthday,
                Gender = p1.Gender,
                UnitId = p1.UnitId,
                BaseModifyTime = p1.BaseModifyTime,
                UnitType = p1.UnitType

            }).ToList();

            userList = userList.Join(userStage, p1 => p1.Id, p2 => p2.Id, (p1, p2) =>
           new UserEntity()
           {
               Id = p1.Id,
               UserName = p1.UserName,
               RoleIds = p1.RoleIds,
               RoleNames = p1.RoleNames,
               RealName = p1.RealName,
               DepartmentIds = p1.DepartmentIds,
               DepartmentNames = p1.DepartmentNames,
               Mobile = p1.Mobile,
               UserStatus = p1.UserStatus,
               Email = p1.Email,
               Birthday = p1.Birthday,
               Gender = p1.Gender,
               UnitId = p1.UnitId,
               BaseModifyTime = p1.BaseModifyTime,
               UnitType = p1.UnitType,
               StageNames = p2.StageNames

           }).ToList();

            userList = userList.Join(userCourse, p1 => p1.Id, p2 => p2.Id, (p1, p2) =>
           new UserEntity()
           {
               Id = p1.Id,
               UserName = p1.UserName,
               RoleIds = p1.RoleIds,
               RoleNames = p1.RoleNames,
               RealName = p1.RealName,
               DepartmentIds = p1.DepartmentIds,
               DepartmentNames = p1.DepartmentNames,
               Mobile = p1.Mobile,
               UserStatus = p1.UserStatus,
               Email = p1.Email,
               Birthday = p1.Birthday,
               Gender = p1.Gender,
               UnitId = p1.UnitId,
               BaseModifyTime = p1.BaseModifyTime,
               UnitType = p1.UnitType,
               StageNames = p1.StageNames,
               CourseNames = p2.CourseNames,

           }).ToList();

            return userList.ToList();
        }

        /// <summary>
        /// 查询单个用户
        /// </summary>
        /// <param name="userId">用户Id</param>
        /// <returns></returns>
        public async Task<UserEntity> GetUserSingle(long userId)
        {
            var parameter = new List<DbParameter>();
            string sql = @$"SELECT * FROM
                        (
	                        SELECT SU.Id ,SU.RealName ,SU.UserName,SU.Mobile,SU.UserStatus,
			                        SU.Email ,SU.Birthday ,SU.Gender,UE.UnitId,UE.Id AS UserExtensionId,SU.BaseIsDelete,SU.QQ,U.UnitType,
			                        SU.BaseCreateTime,SU.Portrait,SU.ThirdUserId,SU.UserValidate
	                        FROM  SysUser AS SU
	                        LEFT JOIN  up_UnitRelation AS UE ON SU.Id = UE.ExtensionObjId AND UE.ExtensionType = 1
	                        LEFT JOIN  up_Unit AS U ON UE.UnitId = U.ID
	                        GROUP BY SU.Id ,RealName ,UserName,SU.Mobile,UserStatus,Email ,Birthday ,Gender,UnitId,UE.Id,SU.BaseIsDelete,SU.QQ,U.UnitType,SU.BaseCreateTime,SU.Portrait,SU.ThirdUserId,SU.UserValidate
                        ) A
                        WHERE Id = {userId} AND BaseIsDelete = 0";

            string sqlRole = @$"SELECT * FROM
                        (
	                        SELECT SU.Id ,SU.RealName ,SU.UserName,SU.Mobile,SU.UserStatus,SU.IsSystem,
			                        SU.Email ,SU.Birthday ,SU.Gender,UE.UnitId,SU.CreateUnitId,
			                        SU.BaseIsDelete,SU.BaseModifyTime,R.RoleName,R.Id AS RoleId
                            FROM  SysUser AS SU
	                        LEFT JOIN SysUserBelong AS SUB ON SU.Id = SUB.UserId
	                        LEFT JOIN SysRole AS R ON SUB.BelongId = R.Id AND SUB.BelongType = 2
                            LEFT JOIN  up_UnitRelation AS UE ON SU.Id = UE.ExtensionObjId AND UE.ExtensionType = 1
                        ) AS A
                        WHERE Id = {userId}";

            string strDepartment = @$"SELECT * FROM
                (
	                SELECT SU.Id ,SU.RealName ,SU.UserName,SU.Mobile,SU.UserStatus,SU.IsSystem,
			                SU.Email ,SU.Birthday ,SU.Gender,UE.UnitId,SU.CreateUnitId,
			                SU.BaseIsDelete,SU.BaseModifyTime,D.Id AS DepartmentId,D.DepartmentName
                    FROM  SysUser AS SU
                    LEFT JOIN  up_UnitRelation AS UE ON SU.Id = UE.ExtensionObjId AND UE.ExtensionType = 1
	                LEFT JOIN  up_DepeartmentRelation AS DR ON DR.ExtensionObjId = SU.Id AND DR.ExtensionType = 1 AND DR.UnitId = UE.UnitId
	                LEFT JOIN SysDepartment AS D ON DR.SysDepartmentId = D.Id AND D.Statuz = 1
                ) AS A
                WHERE Id = {userId}";
            var listUser = await this.BaseRepository().FindList<UserEntity>(sql);

            var roleList = await this.BaseRepository().FindList<UserExtendModel>(sqlRole);
            var userRole = roleList.GroupBy(x => x.Id)
                  .Select(g => new
                  {
                      Id = g.Key,
                      RoleIds = String.Join(",", g.Where(f => f.RoleId > 0).Select(x => x.RoleId)),
                      RoleNames = String.Join(",", g.Where(f => f.RoleId > 0).Select(x => x.RoleName))
                  })
                  .ToList();

            var departmentList = await this.BaseRepository().FindList<UserExtendModel>(strDepartment);
            var userDepartment = departmentList.GroupBy(x => x.Id)
                 .Select(g => new
                 {
                     Id = g.Key,
                     DepartmentIds = String.Join(",", g.Where(f => f.DepartmentId > 0).Select(x => x.DepartmentId)),
                     DepartmentNames = String.Join(",", g.Where(f => f.DepartmentId > 0).Select(x => x.DepartmentName))
                 })
                 .ToList();

            var userList = listUser.Join(userRole, p1 => p1.Id, p2 => p2.Id, (p1, p2) =>
           new UserEntity()
           {
               Id = p1.Id,
               UserName = p1.UserName,
               RoleIds = p2.RoleIds,
               RoleNames = p2.RoleNames,
               RealName = p1.RealName,
               Mobile = p1.Mobile,
               UserStatus = p1.UserStatus,
               Email = p1.Email,
               Birthday = p1.Birthday,
               Gender = p1.Gender,
               UnitId = p1.UnitId,
               UserExtensionId = p1.UserExtensionId,
               BaseIsDelete = p1.BaseIsDelete,
               QQ = p1.QQ,
               BaseModifyTime = p1.BaseModifyTime,
               BaseCreateTime = p1.BaseCreateTime,
               Portrait = p1.Portrait,
               ThirdUserId = p1.ThirdUserId,
               UnitType = p1.UnitType,
               UserValidate = p1.UserValidate
           }).ToList();

            userList = userList.Join(userDepartment, p1 => p1.Id, p2 => p2.Id, (p1, p2) =>
            new UserEntity()
            {
                Id = p1.Id,
                UserName = p1.UserName,
                RoleIds = p1.RoleIds,
                RoleNames = p1.RoleNames,
                RealName = p1.RealName,
                Mobile = p1.Mobile,
                UserStatus = p1.UserStatus,
                Email = p1.Email,
                Birthday = p1.Birthday,
                Gender = p1.Gender,
                UnitId = p1.UnitId,
                UserExtensionId = p1.UserExtensionId,
                BaseIsDelete = p1.BaseIsDelete,
                QQ = p1.QQ,
                BaseModifyTime = p1.BaseModifyTime,
                BaseCreateTime = p1.BaseCreateTime,
                Portrait = p1.Portrait,
                ThirdUserId = p1.ThirdUserId,
                UnitType = p1.UnitType,
                DepartmentIds = p2.DepartmentIds,
                DepartmentNames = p2.DepartmentNames,
                UserValidate = p1.UserValidate,
            }).ToList();

            return userList.FirstOrDefault();
        }



        /// <summary>
        /// 获取下属单位超管信息
        /// </summary>
        /// <param name="param">查询参数</param>
        /// <param name="pagination">分页参数</param>
        /// <returns></returns>
        public async Task<List<UserEntity>> GetSuperUserList(UserListParam param, Pagination pagination)
        {
            var parameter = new List<DbParameter>();
            string where = " 1 = 1";
            string whereRole = " 1 = 1";
            string whereDepartment = " 1 = 1";
            if (param != null)
            {
                where = $"ParentUnitId = {param.UnitId} AND BaseIsDelete = 0";
                whereRole = where;
                whereDepartment = where;

                if (param.SearchUnitId > -1)
                {
                    where += $" AND UnitId = {param.SearchUnitId}";
                }
                if (param.RoleId != null && param.RoleId != -1)
                {
                    where += $" AND RoleId = {param.RoleId}";
                    whereRole += $" AND RoleId = {param.RoleId}";
                }
                if (param.UserStatus != null && param.UserStatus != -1)
                {
                    where += $" AND UserStatus = {param.UserStatus}";
                    whereRole += $" AND UserStatus = {param.UserStatus}";
                    whereDepartment += $" AND UserStatus = {param.UserStatus}";
                }
                if (!string.IsNullOrEmpty(param.RealName))
                {
                    where += $" AND (RealName like '%{param.RealName}%' OR UserName LIKE '%{param.RealName}%' OR Mobile LIKE '%{param.RealName}%')";
                    whereRole += $" AND (RealName like '%{param.RealName}%' OR UserName LIKE '%{param.RealName}%' OR Mobile LIKE '%{param.RealName}%')";
                    whereDepartment += $" AND (RealName like '%{param.RealName}%' OR UserName LIKE '%{param.RealName}%' OR Mobile LIKE '%{param.RealName}%')";
                }
            }

            string sqlList = @$"SELECT Id ,RealName ,UserName,Mobile,UserStatus,
	                               Email ,Birthday ,Gender,UnitId,UnitName,ParentUnitId,CreateUnitId,BaseIsDelete
                             FROM
                            (
	                            SELECT * FROM
	                            (
		                            SELECT SU.Id ,SU.RealName ,SU.UserName,SU.Mobile,SU.UserStatus,
			                               SU.Email ,SU.Birthday ,SU.Gender,UE.UnitId,U.Name AS UnitName,UR.UnitId AS ParentUnitId,SU.CreateUnitId,SU.BaseIsDelete,
			                               R.RoleName,R.Id AS RoleId,D.Id AS DepartmentId,D.DepartmentName
		                            FROM  SysUser AS SU
		                            LEFT JOIN SysUserBelong AS SUB ON SU.Id = SUB.UserId
		                            LEFT JOIN SysRole AS R ON SUB.BelongId = R.Id AND SUB.BelongType = 2
		                            LEFT JOIN  up_UnitRelation AS UE ON SU.Id = UE.ExtensionObjId AND UE.ExtensionType = 1
		                            LEFT JOIN  up_UnitRelation AS UR ON UE.UnitId = UR.ExtensionObjId AND UR.ExtensionType = 3
		                            LEFT JOIN  up_Unit AS U ON UR.ExtensionObjId = U.Id
		                            LEFT JOIN  up_DepeartmentRelation AS DR ON DR.ExtensionObjId = SU.Id AND DR.ExtensionType = 1 AND DR.UnitId = UE.UnitId
		                            LEFT JOIN  SysDepartment AS D ON DR.SysDepartmentId = D.Id AND D.Statuz = 1
	                            ) AS A
	                            WHERE {where}
                            ) AS B
                            GROUP BY Id ,RealName ,UserName,Mobile,UserStatus,Email ,Birthday ,Gender,UnitId,UnitName, ParentUnitId,CreateUnitId,BaseIsDelete";

            var listUser = await this.BaseRepository().FindList<UserEntity>(sqlList, parameter.ToArray(), pagination);

            string sqlRole = @$"SELECT * FROM
                        (
	                        SELECT SU.Id ,SU.RealName ,SU.UserName,SU.Mobile,SU.UserStatus,SU.IsSystem,
			                        SU.Email ,SU.Birthday ,SU.Gender,UE.UnitId,SU.CreateUnitId,UR.UnitId AS ParentUnitId,
			                        SU.BaseIsDelete,SU.BaseModifyTime,R.RoleName,R.Id AS RoleId
                            FROM  SysUser AS SU
                            LEFT JOIN  up_UnitRelation AS UE ON SU.Id = UE.ExtensionObjId AND UE.ExtensionType = 1
		                    LEFT JOIN  up_UnitRelation AS UR ON UE.UnitId = UR.ExtensionObjId AND UR.ExtensionType = 3
	                        LEFT JOIN SysUserBelong AS SUB ON SU.Id = SUB.UserId
	                        LEFT JOIN SysRole AS R ON SUB.BelongId = R.Id AND SUB.BelongType = 2
                        ) AS A
                        WHERE  {whereRole}";

            string strDepartment = @$"SELECT * FROM
                (
	                SELECT SU.Id ,SU.RealName ,SU.UserName,SU.Mobile,SU.UserStatus,SU.IsSystem,
			                SU.Email ,SU.Birthday ,SU.Gender,UE.UnitId,SU.CreateUnitId,UR.UnitId AS ParentUnitId,
			                SU.BaseIsDelete,SU.BaseModifyTime,D.Id AS DepartmentId,D.DepartmentName
                    FROM  SysUser AS SU
                    LEFT JOIN  up_UnitRelation AS UE ON SU.Id = UE.ExtensionObjId AND UE.ExtensionType = 1
		            LEFT JOIN  up_UnitRelation AS UR ON UE.UnitId = UR.ExtensionObjId AND UR.ExtensionType = 3
	                LEFT JOIN  up_DepeartmentRelation AS DR ON DR.ExtensionObjId = SU.Id AND DR.ExtensionType = 1 AND DR.UnitId = UE.UnitId
	                LEFT JOIN SysDepartment AS D ON DR.SysDepartmentId = D.Id AND D.Statuz = 1
                ) AS A
                WHERE {whereDepartment}  ";

            var roleList = await this.BaseRepository().FindList<UserExtendModel>(sqlRole);
            var userRole = roleList.GroupBy(x => x.Id)
                  .Select(g => new
                  {
                      Id = g.Key,
                      RoleIds = String.Join(",", g.Where(f => f.RoleId > 0).Select(x => x.RoleId)),
                      RoleNames = String.Join(",", g.Where(f => f.RoleId > 0).Select(x => x.RoleName))
                  })
                  .ToList();

            var departmentList = await this.BaseRepository().FindList<UserExtendModel>(strDepartment);
            var userDepartment = departmentList.GroupBy(x => x.Id)
                 .Select(g => new
                 {
                     Id = g.Key,
                     DepartmentIds = String.Join(",", g.Where(f => f.DepartmentId > 0).Select(x => x.DepartmentId)),
                     DepartmentNames = String.Join(",", g.Where(f => f.DepartmentId > 0).Select(x => x.DepartmentName))
                 })
                 .ToList();

            var userList = listUser.Join(userRole, p1 => p1.Id, p2 => p2.Id, (p1, p2) =>
          new UserEntity()
          {
              Id = p1.Id,
              UserName = p1.UserName,
              UnitName = p1.UnitName,
              RoleIds = p2.RoleIds,
              RoleNames = p2.RoleNames,
              RealName = p1.RealName,
              Mobile = p1.Mobile,
              UserStatus = p1.UserStatus,
              Email = p1.Email,
              Birthday = p1.Birthday,
              Gender = p1.Gender,
              UnitId = p1.UnitId,
              ParentUnitId = p1.ParentUnitId,
              CreateUnitId = p1.CreateUnitId,
              UnitType = p1.UnitType
          }).ToList();

            userList = userList.Join(userDepartment, p1 => p1.Id, p2 => p2.Id, (p1, p2) =>
            new UserEntity()
            {
                Id = p1.Id,
                UserName = p1.UserName,
                UnitName = p1.UnitName,
                RoleIds = p1.RoleIds,
                RoleNames = p1.RoleNames,
                RealName = p1.RealName,
                DepartmentIds = p2.DepartmentIds,
                DepartmentNames = p2.DepartmentNames,
                Mobile = p1.Mobile,
                UserStatus = p1.UserStatus,
                Email = p1.Email,
                Birthday = p1.Birthday,
                Gender = p1.Gender,
                UnitId = p1.UnitId,
                ParentUnitId = p1.ParentUnitId,
                CreateUnitId = p1.CreateUnitId,
                UnitType = p1.UnitType

            }).ToList();

            return userList.ToList();
        }

        /// <summary>
        /// 查询下属单位单个用户
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task<UserEntity> GetSuperUserSingle(long userId)
        {

            var parameter = new List<DbParameter>();
            string sql = @$"SELECT * FROM
                        (
	                        SELECT SU.Id ,SU.RealName ,SU.UserName,SU.Mobile,SU.UserStatus,
		                            SU.Email ,SU.Birthday ,SU.Gender,UE.UnitId,U.Name AS UnitName,UR.UnitId AS ParentUnitId,SU.CreateUnitId,SU.BaseIsDelete
	                        FROM  SysUser AS SU
	                        LEFT JOIN  up_UnitRelation AS UE ON SU.Id = UE.ExtensionObjId AND UE.ExtensionType = 1
	                        LEFT JOIN  up_UnitRelation AS UR ON UE.UnitId = UR.ExtensionObjId AND UR.ExtensionType = 3
	                        LEFT JOIN  up_Unit AS U ON UR.ExtensionObjId = U.Id
	                        GROUP BY SU.Id ,RealName ,UserName,SU.Mobile,UserStatus,Email ,Birthday ,Gender,UE.UnitId,U.Name,UR.UnitId,SU.CreateUnitId,SU.BaseIsDelete
                        ) A
                        WHERE Id = {userId} AND BaseIsDelete = 0";

            string sqlRole = @$"SELECT * FROM
                        (
	                        SELECT SU.Id ,SU.RealName ,SU.UserName,SU.Mobile,SU.UserStatus,SU.IsSystem,
			                        SU.Email ,SU.Birthday ,SU.Gender,UE.UnitId,SU.CreateUnitId,
			                        SU.BaseIsDelete,SU.BaseModifyTime,R.RoleName,R.Id AS RoleId
                            FROM  SysUser AS SU
	                        LEFT JOIN SysUserBelong AS SUB ON SU.Id = SUB.UserId
	                        LEFT JOIN SysRole AS R ON SUB.BelongId = R.Id AND SUB.BelongType = 2
                            LEFT JOIN  up_UnitRelation AS UE ON SU.Id = UE.ExtensionObjId AND UE.ExtensionType = 1
                        ) AS A
                        WHERE Id = {userId}";

            string strDepartment = @$"SELECT * FROM
                (
	                SELECT SU.Id ,SU.RealName ,SU.UserName,SU.Mobile,SU.UserStatus,SU.IsSystem,
			                SU.Email ,SU.Birthday ,SU.Gender,UE.UnitId,SU.CreateUnitId,
			                SU.BaseIsDelete,SU.BaseModifyTime,D.Id AS DepartmentId,D.DepartmentName
                    FROM  SysUser AS SU
                    LEFT JOIN  up_UnitRelation AS UE ON SU.Id = UE.ExtensionObjId AND UE.ExtensionType = 1
	                LEFT JOIN  up_DepeartmentRelation AS DR ON DR.ExtensionObjId = SU.Id AND DR.ExtensionType = 1 AND DR.UnitId = UE.UnitId
	                LEFT JOIN SysDepartment AS D ON DR.SysDepartmentId = D.Id AND D.Statuz = 1
                ) AS A
                WHERE Id = {userId}";
            var listUser = await this.BaseRepository().FindList<UserEntity>(sql);

            var roleList = await this.BaseRepository().FindList<UserExtendModel>(sqlRole);
            var userRole = roleList.GroupBy(x => x.Id)
                  .Select(g => new
                  {
                      Id = g.Key,
                      RoleIds = String.Join(",", g.Where(f => f.RoleId > 0).Select(x => x.RoleId)),
                      RoleNames = String.Join(",", g.Where(f => f.RoleId > 0).Select(x => x.RoleName))
                  })
                  .ToList();

            var departmentList = await this.BaseRepository().FindList<UserExtendModel>(strDepartment);
            var userDepartment = departmentList.GroupBy(x => x.Id)
                 .Select(g => new
                 {
                     Id = g.Key,
                     DepartmentIds = String.Join(",", g.Where(f => f.DepartmentId > 0).Select(x => x.DepartmentId)),
                     DepartmentNames = String.Join(",", g.Where(f => f.DepartmentId > 0).Select(x => x.DepartmentName))
                 })
                 .ToList();
            var userList = listUser.Join(userRole, p1 => p1.Id, p2 => p2.Id, (p1, p2) =>
           new UserEntity()
           {
               Id = p1.Id,
               UserName = p1.UserName,
               RoleIds = p2.RoleIds,
               RoleNames = p2.RoleNames,
               RealName = p1.RealName,
               Mobile = p1.Mobile,
               UserStatus = p1.UserStatus,
               Email = p1.Email,
               Birthday = p1.Birthday,
               Gender = p1.Gender,
               UnitId = p1.UnitId,
               UnitName = p1.UnitName,
               BaseIsDelete = p1.BaseIsDelete,
               CreateUnitId = p1.CreateUnitId,
               UnitType = p1.UnitType,
               ParentUnitId = p1.ParentUnitId,
           }).ToList();

            userList = userList.Join(userDepartment, p1 => p1.Id, p2 => p2.Id, (p1, p2) =>
            new UserEntity()
            {
                Id = p1.Id,
                UserName = p1.UserName,
                RoleIds = p1.RoleIds,
                RoleNames = p1.RoleNames,
                RealName = p1.RealName,
                Mobile = p1.Mobile,
                UserStatus = p1.UserStatus,
                Email = p1.Email,
                Birthday = p1.Birthday,
                Gender = p1.Gender,
                UnitId = p1.UnitId,
                UnitName = p1.UnitName,
                BaseIsDelete = p1.BaseIsDelete,
                CreateUnitId = p1.CreateUnitId,
                UnitType = p1.UnitType,
                ParentUnitId = p1.ParentUnitId,
                DepartmentIds = p2.DepartmentIds,
                DepartmentNames = p2.DepartmentNames,
            }).ToList();


            return userList.FirstOrDefault();
        }


        /// <summary>
        /// 查询所有用户信息
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<List<UserEntity>> GetUserAllList(UserListParam param, Pagination pagination)
        {
            #region 测试存储过程调用
            //var list=new List<UserEntity>();
            //string sql = @"DECLARE @f INT=1,@m NVARCHAR(127)=''
            //            EXEC USP_SysUser_Test @SchoolId = 0,@flag = @f OUTPUT,@msg = @m OUTPUT
            //            SELECT @f AS Gender,@m AS Email ";

            //var ff = await this.BaseRepository().FindList<UserEntity>(sql);
            //return list;
            #endregion

            var parameter = new List<DbParameter>();
            string where = " BaseIsDelete = 0 AND UnitId <> 100000000000000001";
            string whereRole = where;
            string whereDepartment = where;
            if (param != null)
            {
                if (param.UnitId > -1)
                {
                    where += $" AND UnitId = {param.UnitId}";
                    whereRole += $" AND UnitId = {param.UnitId}";
                    whereDepartment += $" AND UnitId = {param.UnitId}";
                }
                if (param.Id > 0)
                {
                    where += $"  AND Id = {param.Id}";
                    whereRole += $"  AND Id = {param.Id}";
                    whereDepartment += $"  AND Id = {param.Id}";
                }
                if (param.IsSystem == 1)
                {
                    where += "  AND IsSystem = 0";
                    whereRole += "  AND IsSystem = 0";
                    whereDepartment += "  AND IsSystem = 0";
                }
                if (param.RoleId != null && param.RoleId != -1)
                {
                    where += $" AND RoleId = {param.RoleId}";
                    whereRole += $" AND RoleId = {param.RoleId}";
                }
                if (param.UserStatus != null && param.UserStatus != -1)
                {
                    where += $" AND UserStatus = {param.UserStatus}";
                    whereRole += $" AND UserStatus = {param.UserStatus}";
                    whereDepartment += $" AND UserStatus = {param.UserStatus}";
                }
                if (param.SearchUnitId > -1)
                {
                    where += $" AND UnitId = {param.SearchUnitId}";
                    whereRole += $" AND UnitId = {param.SearchUnitId}";
                    whereDepartment += $" AND UnitId = {param.SearchUnitId}";
                }
                if (!string.IsNullOrEmpty(param.RealName))
                {
                    where += $" AND (RealName like '%{param.RealName}%' OR UserName LIKE '%{param.RealName}%' OR Mobile LIKE '%{param.RealName}%')";
                    whereRole += $" AND (RealName like '%{param.RealName}%' OR UserName LIKE '%{param.RealName}%' OR Mobile LIKE '%{param.RealName}%')";
                    whereDepartment += $" AND (RealName like '%{param.RealName}%' OR UserName LIKE '%{param.RealName}%' OR Mobile LIKE '%{param.RealName}%')";
                }

                if (!string.IsNullOrEmpty(param.StartTime.ParseToString()))
                {
                    where += $" AND BaseModifyTime >= '{param.StartTime}'";
                    whereRole += $" AND BaseModifyTime >= '{param.StartTime}'";
                    whereDepartment += $" AND BaseModifyTime >= '{param.StartTime}'";
                }
                if (!string.IsNullOrEmpty(param.EndTime.ParseToString()))
                {
                    where += $" AND BaseModifyTime <= '{param.EndTime}'";
                    whereRole += $" AND BaseModifyTime <= '{param.EndTime}'";
                    whereDepartment += $" AND BaseModifyTime <= '{param.EndTime}'";
                }
                if (param.ChildrenDepartmentIdList != null && param.ChildrenDepartmentIdList.Count > 0)
                {
                    int index = 0;
                    foreach (long id in param.ChildrenDepartmentIdList)
                    {
                        if (index == 0)
                        {
                            where += $" AND (DepartmentId = {id}";
                            whereDepartment += $" AND (DepartmentId = {id}";
                        }
                        else
                        {
                            where += $" OR DepartmentId = {id}";
                            whereDepartment += $" OR DepartmentId = {id}";
                        }
                        index++;
                    }
                    where += " ) ";
                    whereDepartment += " ) ";
                }
            }

            string list = @$"SELECT IsSystem,Id ,RealName ,UserName,Mobile,UserStatus,Email ,Birthday ,Gender,UnitId,UnitName,CreateUnitId,BaseIsDelete,BaseModifyTime,UnitType
                            FROM
                            (
                                SELECT * FROM
	                            (
		                            SELECT SU.Id ,SU.RealName ,SU.UserName,SU.Mobile,SU.UserStatus,SU.IsSystem,
				                            SU.Email ,SU.Birthday ,SU.Gender,UE.UnitId,U.Name AS UnitName,SU.CreateUnitId,
				                            SU.BaseIsDelete,SU.BaseModifyTime,U.UnitType,R.RoleName,R.Id AS RoleId,D.Id AS DepartmentId,D.DepartmentName
                                    FROM  SysUser AS SU
		                            LEFT JOIN SysUserBelong AS SUB ON SU.Id = SUB.UserId
		                            LEFT JOIN SysRole AS R ON SUB.BelongId = R.Id AND SUB.BelongType = 2
                                    LEFT JOIN  up_UnitRelation AS UE ON SU.Id = UE.ExtensionObjId AND UE.ExtensionType = 1
		                            LEFT JOIN  up_DepeartmentRelation AS DR ON DR.ExtensionObjId = SU.Id AND DR.ExtensionType = 1 AND DR.UnitId = UE.UnitId
		                            LEFT JOIN SysDepartment AS D ON DR.SysDepartmentId = D.Id AND D.Statuz = 1
	                                LEFT JOIN  up_Unit AS U ON UE.UnitId = U.Id
	                            ) AS A
	                            WHERE {where}
                            ) AS B
                            GROUP BY IsSystem,Id ,RealName ,UserName,Mobile,UserStatus,Email ,Birthday ,Gender,UnitId,UnitName,CreateUnitId,BaseIsDelete,BaseModifyTime,UnitType";





            string sqlRole = @$"SELECT * FROM
                        (
	                        SELECT SU.Id ,SU.RealName ,SU.UserName,SU.Mobile,SU.UserStatus,SU.IsSystem,
			                        SU.Email ,SU.Birthday ,SU.Gender,UE.UnitId,SU.CreateUnitId,
			                        SU.BaseIsDelete,SU.BaseModifyTime,R.RoleName,R.Id AS RoleId
                            FROM  SysUser AS SU
	                        LEFT JOIN SysUserBelong AS SUB ON SU.Id = SUB.UserId
	                        LEFT JOIN SysRole AS R ON SUB.BelongId = R.Id AND SUB.BelongType = 2
                            LEFT JOIN  up_UnitRelation AS UE ON SU.Id = UE.ExtensionObjId AND UE.ExtensionType = 1
                        ) AS A
                        WHERE  {whereRole}";

            string strDepartment = @$"SELECT * FROM
                (
	                SELECT SU.Id ,SU.RealName ,SU.UserName,SU.Mobile,SU.UserStatus,SU.IsSystem,
			                SU.Email ,SU.Birthday ,SU.Gender,UE.UnitId,SU.CreateUnitId,
			                SU.BaseIsDelete,SU.BaseModifyTime,D.Id AS DepartmentId,D.DepartmentName
                    FROM  SysUser AS SU
                    LEFT JOIN  up_UnitRelation AS UE ON SU.Id = UE.ExtensionObjId AND UE.ExtensionType = 1
	                LEFT JOIN  up_DepeartmentRelation AS DR ON DR.ExtensionObjId = SU.Id AND DR.ExtensionType = 1 AND DR.UnitId = UE.UnitId
	                LEFT JOIN SysDepartment AS D ON DR.SysDepartmentId = D.Id AND D.Statuz = 1
                ) AS A
                WHERE {whereDepartment}  ";


            var listUser = await this.BaseRepository().FindList<UserEntity>(list, parameter.ToArray(), pagination);

            var roleList = await this.BaseRepository().FindList<UserExtendModel>(sqlRole);
            var userRole = roleList.GroupBy(x => x.Id)
                  .Select(g => new
                  {
                      Id = g.Key,
                      RoleIds = String.Join(",", g.Where(f => f.RoleId > 0).Select(x => x.RoleId)),
                      RoleNames = String.Join(",", g.Where(f => f.RoleId > 0).Select(x => x.RoleName))
                  })
                  .ToList();

            var departmentList = await this.BaseRepository().FindList<UserExtendModel>(strDepartment);
            var userDepartment = departmentList.GroupBy(x => x.Id)
                  .Select(g => new
                  {
                      Id = g.Key,
                      DepartmentIds = String.Join(",", g.Where(f => f.DepartmentId > 0).Select(x => x.DepartmentId)),
                      DepartmentNames = String.Join(",", g.Where(f => f.DepartmentId > 0).Select(x => x.DepartmentName))
                  })
                  .ToList();

            var userList = listUser.Join(userRole, p1 => p1.Id, p2 => p2.Id, (p1, p2) =>
            new UserEntity()
            {
                IsSystem = p1.IsSystem,
                Id = p1.Id,
                UserName = p1.UserName,
                RoleIds = p2.RoleIds,
                RoleNames = p2.RoleNames,
                RealName = p1.RealName,
                Mobile = p1.Mobile,
                UserStatus = p1.UserStatus,
                Email = p1.Email,
                Birthday = p1.Birthday,
                Gender = p1.Gender,
                UnitId = p1.UnitId,
                UnitName = p1.UnitName,
                BaseModifyTime = p1.BaseModifyTime,
                UnitType = p1.UnitType
            }).ToList();

            userList = userList.Join(userDepartment, p1 => p1.Id, p2 => p2.Id, (p1, p2) =>
            new UserEntity()
            {
                IsSystem = p1.IsSystem,
                Id = p1.Id,
                UserName = p1.UserName,
                RoleIds = p1.RoleIds,
                RoleNames = p1.RoleNames,
                RealName = p1.RealName,
                DepartmentIds = p2.DepartmentIds,
                DepartmentNames = p2.DepartmentNames,
                Mobile = p1.Mobile,
                UserStatus = p1.UserStatus,
                Email = p1.Email,
                Birthday = p1.Birthday,
                Gender = p1.Gender,
                UnitId = p1.UnitId,
                UnitName = p1.UnitName,
                BaseModifyTime = p1.BaseModifyTime,
                UnitType = p1.UnitType

            }).ToList();
            return userList;
        }

        /// <summary>
        /// 获取用户列表信息(角色)
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<List<UserExtendModel>> GetUserRoleList(UserListParam param)
        {
            string where = " BaseIsDelete = 0 AND UnitId <> 100000000000000001";
            if (param != null)
            {
                if (param.UnitId > 0)
                {
                    if (param.UnitType == UnitTypeEnum.City.ParseToInt())
                    {
                        where += $" AND CityId = {param.UnitId}";
                    }
                    else if (param.UnitType == UnitTypeEnum.County.ParseToInt())
                    {
                        where += $" AND CountyId = {param.UnitId}";
                    }
                    else if (param.UnitType == UnitTypeEnum.System.ParseToInt())
                    {
                        //系统管理员
                    }
                    else
                    {
                        where += $" AND UnitId = {param.UnitId}";
                    }
                }
                if (param.IsSystem == 1)
                {
                    where += "  AND IsSystem = 0";
                }
                if (param.RoleId != null && param.RoleId != -1)
                {
                    where += $" AND RoleId = {param.RoleId}";
                }
                if (param.UserStatus != null && param.UserStatus != -1)
                {
                    where += $" AND UserStatus = {param.UserStatus}";
                }
                if (param.SearchUnitId > -1)
                {
                    where += $" AND UnitId = {param.SearchUnitId}";
                }
                if (!string.IsNullOrEmpty(param.RealName))
                {
                    where += $" AND (RealName like '%{param.RealName}%' OR UserName LIKE '%{param.RealName}%' OR Mobile LIKE '%{param.RealName}%')";
                }

                if (!string.IsNullOrEmpty(param.StartTime.ParseToString()))
                {
                    where += $" AND BaseModifyTime >= '{param.StartTime}'";
                }
                if (!string.IsNullOrEmpty(param.EndTime.ParseToString()))
                {
                    where += $" AND BaseModifyTime <= '{param.EndTime}'";
                }
                if (param.SchoolProple!=-10000)
                {
                    where += $" AND SchoolProp <> {param.SchoolProple}";
                }
            }
            string sql = @$"SELECT * FROM
                        (
	                        SELECT SU.Id ,SU.RealName ,SU.UserName,SU.Mobile,SU.UserStatus,SU.IsSystem,
			                        SU.Email ,SU.Birthday ,SU.Gender,UE.UnitId,SU.CreateUnitId,
			                        SU.BaseIsDelete,SU.BaseModifyTime,R.RoleName, R.RoleId
									,ur3.UnitId AS CountyId
									,ur4.UnitId AS CityId
                                    ,se7.SchoolProp
                            FROM  SysUser AS SU
	                        LEFT JOIN SysUserBelong AS SUB ON SU.Id = SUB.UserId
	                        LEFT JOIN SysRole AS R ON R.BaseIsDelete= 0 AND SUB.BelongId = R.Id AND SUB.BelongType = 2
                            LEFT JOIN up_UnitRelation AS UE ON SU.Id = UE.ExtensionObjId AND UE.ExtensionType = 1
		                    LEFT JOIN up_UnitRelation AS ur3 ON ur3.BaseIsDelete = 0 AND UE.UnitId = ur3.ExtensionObjId AND ur3.ExtensionType = 3
							LEFT JOIN up_UnitRelation AS ur4 ON ur4.BaseIsDelete = 0 AND ur3.UnitId = ur4.ExtensionObjId AND ur3.ExtensionType = 3
                            LEFT JOIN  up_SchoolExtension AS se7 ON UE.UnitId = se7.UnitId AND se7.BaseIsDelete = 0
                        ) AS A
                        WHERE  {where}";
            var listRole = await this.BaseRepository().FindList<UserExtendModel>(sql);
            return listRole.ToList();
        }

        /// <summary>
        /// 获取用户信息列表根据部门Id
        /// </summary>
        /// <param name="param">查询参数</param>
        /// <param name="pagination">分页参数</param>
        /// <returns></returns>
        public async Task<List<UserEntity>> GetUserByDepartmentList(UserListParam param)
        {
            StringBuilder strSql = new StringBuilder();
            var parameter = new List<DbParameter>();
            if (param.IsNeedDepartment)
            {
                strSql.Append(@"SELECT * FROM   (
                SELECT
                SU.Id ,
                SU.RealName ,
                SU.UserName,
                SU.Mobile,
                SU.UserStatus,
                SU.Email ,
                SU.Birthday ,
                SU.Gender ,
                UE.UnitId ,
                SU.BaseIsDelete ,
                dr2.SysDepartmentId
                FROM  SysUser AS SU
                LEFT JOIN  up_UnitRelation AS UE ON SU.Id = UE.ExtensionObjId AND UE.ExtensionType = 1
                LEFT JOIN  up_DepeartmentRelation AS dr2 ON dr2.ExtensionType = 1 AND SU.Id = dr2.ExtensionObjId
                ) A");
            }
            else
            {
                strSql.Append(@"SELECT * FROM   (
                SELECT
                SU.Id ,
                SU.RealName ,
                SU.UserName,
                SU.Mobile,
                SU.UserStatus,
                SU.Email ,
                SU.Birthday ,
                SU.Gender ,
                UE.UnitId ,
                SU.BaseIsDelete
                FROM  SysUser AS SU
                LEFT JOIN  up_UnitRelation AS UE ON SU.Id = UE.ExtensionObjId AND UE.ExtensionType = 1
                ) A");
            }

            if (param != null)
            {
                strSql.Append($" WHERE BaseIsDelete =  {IsEnum.No.ParseToInt()} AND UserStatus = {StatusEnum.Yes.ParseToInt()}");
                strSql.Append(" AND UnitId = @UnitId ");
                parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));

                if (param.ChildrenDepartmentIdList != null && param.ChildrenDepartmentIdList.Count > 0)
                {
                    string strIds = "";
                    foreach (long id in param.ChildrenDepartmentIdList)
                    {
                        strIds += "'" + id.ToString() + "',";
                    }
                    strIds = strIds.TrimEnd(',');
                    strSql.Append($" AND SysDepartmentId IN ({strIds})");
                }
            }
            var list = await this.BaseRepository().FindList<UserEntity>(strSql.ToString(), parameter.ToArray());
            return list.ToList();
        }


        public async Task<List<UserEntity>> GetComboxUserByRoleId(long unitId, long roleId)
        {
            var parameter = new List<DbParameter>();
            string sql = @$"SELECT Id ,RealName ,UserName,Mobile,UserStatus,IsSystem,Email ,Birthday ,Gender,UnitId,UnitName,CreateUnitId,BaseIsDelete,BaseModifyTime,UnitType,
	                               RoleName,RoleId
                            FROM
                            (
	                            SELECT * FROM
	                            (
		                            SELECT SU.Id ,SU.RealName ,SU.UserName,SU.Mobile,SU.UserStatus,SU.IsSystem,
			                               SU.Email ,SU.Birthday ,SU.Gender,UE.UnitId,U.Name AS UnitName,SU.CreateUnitId,SU.BaseIsDelete,SU.BaseModifyTime,U.UnitType,
			                               R.RoleName,R.Id AS RoleId
		                            FROM  SysUser AS SU
		                            LEFT JOIN SysUserBelong AS SUB ON SU.Id = SUB.UserId
		                            LEFT JOIN SysRole AS R ON SUB.BelongId = R.Id AND SUB.BelongType = 2
		                            LEFT JOIN  up_UnitRelation AS UE ON SU.Id = UE.ExtensionObjId AND UE.ExtensionType = 1
		                            LEFT JOIN  up_Unit AS U ON UE.UnitId = U.Id
	                            ) AS A
	                            WHERE BaseIsDelete = 0 AND UnitId = {unitId} AND RoleId = {roleId}
                            ) AS B
                            GROUP BY Id ,RealName ,UserName,Mobile,UserStatus,IsSystem,Email ,Birthday ,Gender,UnitId,UnitName,CreateUnitId,BaseIsDelete,BaseModifyTime,UnitType,
	                               RoleName,RoleId
                            ORDER BY RealName ASC";
            var list = await this.BaseRepository().FindList<UserEntity>(sql);
            return list.ToList();
        }
        #endregion

        #region 提交数据
        public async Task UpdateUser(UserEntity entity)
        {
            await this.BaseRepository().Update(entity);
        }
        public async Task UpdateTransUser(UserEntity entity, Repository db)
        {
            await db.Update(entity);
        }
        public async Task SaveForm(UserEntity entity)
        {
            var db = await this.BaseRepository().BeginTrans();
            try
            {
                if (entity.Id.IsNullOrZero())
                {
                    await entity.Create();
                    await db.Insert(entity);
                }
                else
                {
                    await db.Delete<UserBelongEntity>(t => t.UserId == entity.Id);

                    // 密码不进行更新，有单独的方法更新密码
                    entity.Password = null;
                    await entity.Modify();
                    await db.Update(entity);
                }
                // 职位
                if (!string.IsNullOrEmpty(entity.PositionIds))
                {
                    foreach (long positionId in TextHelper.SplitToArray<long>(entity.PositionIds, ','))
                    {
                        UserBelongEntity positionBelongEntity = new UserBelongEntity();
                        positionBelongEntity.UserId = entity.Id;
                        positionBelongEntity.BelongId = positionId;
                        positionBelongEntity.BelongType = UserBelongTypeEnum.Position.ParseToInt();
                        await positionBelongEntity.Create();
                        await db.Insert(positionBelongEntity);
                    }
                }
                // 角色
                if (!string.IsNullOrEmpty(entity.RoleIds))
                {
                    foreach (long roleId in TextHelper.SplitToArray<long>(entity.RoleIds, ','))
                    {
                        UserBelongEntity departmentBelongEntity = new UserBelongEntity();
                        departmentBelongEntity.UserId = entity.Id;
                        departmentBelongEntity.BelongId = roleId;
                        departmentBelongEntity.BelongType = UserBelongTypeEnum.Role.ParseToInt();
                        await departmentBelongEntity.Create();
                        await db.Insert(departmentBelongEntity);
                    }
                }
                await db.CommitTrans();
            }
            catch(Exception ex)
            {
                await db.RollbackTrans();
                throw new Exception("程序出现异常，异常信息为：" + ex.Message);
            }
        }

        public async Task SaveUserEntity(UserEntity entity)
        {
            var db = await this.BaseRepository().BeginTrans();
            try
            {
                if (entity.Id.IsNullOrZero())
                {
                    await entity.Create();
                    await db.Insert(entity);
                }
                else
                {
                    // 密码不进行更新
                    entity.Password = null;
                    await entity.Modify();
                    await db.Update(entity);
                }
                await db.CommitTrans();
            }
            catch(Exception ex)
            {
                await db.RollbackTrans();
                throw new Exception("程序出现异常，异常信息为：" + ex.Message);
            }
        }


        public async Task DeleteForm(string ids)
        {
            var db = await this.BaseRepository().BeginTrans();
            try
            {
                long[] idArr = TextHelper.SplitToArray<long>(ids, ',');
                await db.Delete<UserEntity>(idArr);
                await db.Delete<UserBelongEntity>(t => idArr.Contains(t.UserId.Value));
                await db.CommitTrans();
            }
            catch(Exception ex)
            {
                await db.RollbackTrans();
                throw new Exception("程序出现异常，异常信息为：" + ex.Message);
            }
        }

        public async Task ResetPassword(UserEntity entity)
        {
            await entity.Modify();
            await this.BaseRepository().Update(entity);
        }

        public async Task ChangeUser(UserEntity entity)
        {
            await entity.Modify();
            await this.BaseRepository().Update(entity);
        }

        /// <summary>
        /// 保存用户信息(后加)
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public async Task SaveUserForm(UserEntity entity)
        {
            var db = await this.BaseRepository().BeginTrans();
            try
            {
                if (entity.Id.IsNullOrZero())
                {
                    await entity.Create();
                    await db.Insert(entity);

                    ////插入用户扩展up_UserExtension信息
                    //UserExtensionEntity extension = new UserExtensionEntity();
                    //extension.UnitId = entity.UnitId;
                    //extension.CreateUnitId = entity.CreateUnitId;
                    //extension.SysUserId = entity.Id.Value;
                    //await extension.Create();
                    //await db.Insert(extension);

                    //插入单位关系表up_UnitRelation信息
                    UnitRelationEntity relation = new UnitRelationEntity();
                    relation.UnitId = entity.UnitId;
                    relation.ExtensionType = UnitRelationTypeEnum.User.ParseToInt();
                    relation.ExtensionObjId = entity.Id.Value;
                    await relation.Create();
                    await db.Insert(relation);
                }
                else
                {
                    if (entity.IsChangeRole)
                    {
                        //1.先删除角色
                        await db.Delete<UserBelongEntity>(t => t.UserId == entity.Id);
                    }
                    if (entity.IsChangeDepartment)
                    {
                        //2.删除组织架构
                        await db.Delete<DepeartmentRelationEntity>(t => t.ExtensionObjId == entity.Id && t.ExtensionType == DepartmentRelationTypeEnum.DepartUser.ParseToInt());
                    }
                    if (entity.IsChangeUnitId)
                    {
                        //3.更新用户单位信息
                        UnitRelationEntity unitRelation = await db.FindEntity<UnitRelationEntity>(a => a.ExtensionObjId == entity.Id && a.ExtensionType == UnitRelationTypeEnum.User.ParseToInt());
                        unitRelation.UnitId = entity.UnitId;
                        await unitRelation.Modify();
                        await db.Update(unitRelation);
                    }

                    await entity.Modify();
                    await db.Update(entity);
                }

                // 角色
                if (entity.IsChangeRole)
                {
                    if (!string.IsNullOrEmpty(entity.RoleIds))
                    {
                        foreach (long roleId in TextHelper.SplitToArray<long>(entity.RoleIds, ','))
                        {
                            UserBelongEntity departmentBelongEntity = new UserBelongEntity();
                            departmentBelongEntity.UserId = entity.Id;
                            departmentBelongEntity.BelongId = roleId;
                            departmentBelongEntity.BelongType = UserBelongTypeEnum.Role.ParseToInt();
                            await departmentBelongEntity.Create();
                            await db.Insert(departmentBelongEntity);
                        }
                    }
                }

                //组织架构
                if (entity.IsChangeDepartment)
                {
                    if (!string.IsNullOrEmpty(entity.DepartmentIds))
                    {
                        foreach (long departmentId in TextHelper.SplitToArray<long>(entity.DepartmentIds, ','))
                        {
                            DepeartmentRelationEntity departmentEntity = new DepeartmentRelationEntity();
                            departmentEntity.UnitId = entity.UnitId;
                            departmentEntity.ExtensionType = DepartmentRelationTypeEnum.DepartUser.ParseToInt();
                            departmentEntity.SysDepartmentId = departmentId;
                            departmentEntity.ExtensionObjId = entity.Id;
                            await departmentEntity.Create();
                            await db.Insert(departmentEntity);
                        }
                    }
                }


                await db.CommitTrans();
            }
            catch(Exception ex)
            {
                await db.RollbackTrans();
                throw new Exception("程序出现异常，异常信息为：" + ex.Message);
            }
        }

        /// <summary>
        /// 单条用户信息(后加)
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task DeleteUserFormById(long id)
        {
            var db = await this.BaseRepository().BeginTrans();
            try
            {
                await db.Delete<UserEntity>(id);
                await db.Delete<UserBelongEntity>(t => t.UserId == id);
                await db.Delete<UserExtensionEntity>(t => t.SysUserId == id);
                await db.Delete<DepeartmentRelationEntity>(t => t.ExtensionObjId == id && t.ExtensionType == DepartmentRelationTypeEnum.DepartUser.ParseToInt());
                await db.Delete<UnitRelationEntity>(t => t.ExtensionObjId == id && t.ExtensionType == UnitRelationTypeEnum.User.ParseToInt());
                await db.CommitTrans();
            }
            catch(Exception ex)
            {
                await db.RollbackTrans();
                throw new Exception("程序出现异常，异常信息为：" + ex.Message);
            }
        }

        /// <summary>
        /// 批量删除用户信息(后加)
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task DeleteUserForm(string ids)
        {
            var db = await this.BaseRepository().BeginTrans();
            try
            {
                long[] idArr = TextHelper.SplitToArray<long>(ids, ',');
                await db.Delete<UserEntity>(idArr);
                await db.Delete<UserBelongEntity>(t => idArr.Contains(t.UserId.Value));
                await db.Delete<UserExtensionEntity>(t => idArr.Contains(t.SysUserId.Value));
                await db.Delete<DepeartmentRelationEntity>(t => idArr.Contains(t.ExtensionObjId.Value) && t.ExtensionType == DepartmentRelationTypeEnum.DepartUser.ParseToInt());
                await db.Delete<UnitRelationEntity>(t => idArr.Contains(t.ExtensionObjId.Value) && t.ExtensionType == UnitRelationTypeEnum.User.ParseToInt());
                await db.CommitTrans();
            }
            catch(Exception ex)
            {
                await db.RollbackTrans();
                throw new Exception("程序出现异常，异常信息为：" + ex.Message);
            }
        }


        /// <summary>
        /// 保存下属单位用户信息
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public async Task SaveChildUserForm(UserEntity entity)
        {
            var db = await this.BaseRepository().BeginTrans();
            try
            {
                if (entity.Id.IsNullOrZero())
                {
                    await entity.Create();
                    await db.Insert(entity);

                    ////插入用户扩展up_UserExtension信息
                    //UserExtensionEntity extension = new UserExtensionEntity();
                    //extension.UnitId = entity.UnitId;
                    //extension.CreateUnitId = entity.CreateUnitId;
                    //extension.SysUserId = entity.Id.Value;
                    //await extension.Create();
                    //await db.Insert(extension);

                    //插入单位关系表up_UnitRelation信息
                    UnitRelationEntity relation = new UnitRelationEntity();
                    relation.UnitId = entity.UnitId;
                    relation.ExtensionType = UnitRelationTypeEnum.User.ParseToInt();
                    relation.ExtensionObjId = entity.Id.Value;
                    await relation.Create();
                    await db.Insert(relation);
                }
                else
                {
                    await entity.Modify();
                    await db.Update(entity);

                    //用户单位关系表
                    UnitRelationEntity relation = await relationService.GetEntity(entity.Id.Value, entity.UnitId.Value);
                    if (relation != null)
                    {
                        relation.UnitId = entity.UnitId;
                        await relationService.SaveForm(relation);
                    }
                }

                // 角色
                if (entity.IsChangeRole)
                {
                    //1.先删除角色
                    await db.Delete<UserBelongEntity>(t => t.UserId == entity.Id);

                    if (!string.IsNullOrEmpty(entity.RoleIds))
                    {
                        foreach (long roleId in TextHelper.SplitToArray<long>(entity.RoleIds, ','))
                        {
                            UserBelongEntity departmentBelongEntity = new UserBelongEntity();
                            departmentBelongEntity.UserId = entity.Id;
                            departmentBelongEntity.BelongId = roleId;
                            departmentBelongEntity.BelongType = UserBelongTypeEnum.Role.ParseToInt();
                            await departmentBelongEntity.Create();
                            await db.Insert(departmentBelongEntity);
                        }
                    }
                }
                await db.CommitTrans();
            }
            catch(Exception ex)
            {
                await db.RollbackTrans();
                throw new Exception("程序出现异常，异常信息为：" + ex.Message);
            }
        }

        /// <summary>
        /// 假删除当前单位用户信息
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task DeleteSchoolForm(string ids)
        {
            var db = await this.BaseRepository().BeginTrans();
            try
            {
                ids = StringFilter.ValidateAndCleanIds(ids);
                if(ids == null)
                    throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
                string strSql;
                if (ids.IndexOf(',') != -1)
                {
                    strSql = $" update SysUser set BaseIsDelete = 1 where Id in ({ids}) ";
                }
                else
                {
                    strSql = $"update SysUser set BaseIsDelete = 1 where id = {ids}";
                }
                await this.BaseRepository().ExecuteBySql(strSql);


                //strSql = $" UPDATE  up_UserExtension SET BaseIsDelete = 1 FROM  up_UserExtension AS UE  INNER JOIN f_split('{ids}',',') AS F ON UE.SysUserId = F.col";
                //await this.BaseRepository().ExecuteBySql(strSql);

                strSql = $" UPDATE  up_DepeartmentRelation SET BaseIsDelete = 1 WHERE ExtensionObjId in ({ids}) AND ExtensionType = 1";
                await this.BaseRepository().ExecuteBySql(strSql);

                await db.CommitTrans();
            }
            catch (Exception ex)
            {
                await db.RollbackTrans();
                throw new Exception("程序出现异常，异常信息为：" + ex.Message);
            }

        }

        /// <summary>
        /// 修改默认登录用户账号，主要针对统一身份认证一个账号对应实验教学平台两个账号的情况
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task TurnDefaultUser(UserEntity user)
        {
            if (user == null)
                return;
            if (string.IsNullOrEmpty(user.ThirdUserId))
                return;
            var userList = await GetThirdUser(user.ThirdUserId);
            var oldDefault = userList.Where(m => m.IsThirdUserDefault == 1).FirstOrDefault();
            if(oldDefault != null)
            {
                oldDefault.IsThirdUserDefault = 0;
                await this.BaseRepository().Update(oldDefault);
            }
            user.IsThirdUserDefault = 1;
            await this.BaseRepository().Update(user);

        }
            #endregion

            #region 私有方法
            private Expression<Func<UserEntity, bool>> ListFilter(UserListParam param)
        {
            var expression = LinqExtensions.True<UserEntity>();
            if (param != null)
            {
                if (param.CreateUnitId > 0)
                {
                    expression = expression.And(a => a.CreateUnitId == param.CreateUnitId);
                }
                if (param.Id > 0)
                {
                    expression = expression.And(a => a.Id != param.Id);
                }
                if (!string.IsNullOrEmpty(param.UserName))
                {
                    expression = expression.And(t => t.UserName.Contains(param.UserName));
                }
                if (!string.IsNullOrEmpty(param.UserIds))
                {
                    long[] userIdList = TextHelper.SplitToArray<long>(param.UserIds, ',');
                    expression = expression.And(t => userIdList.Contains(t.Id.Value));
                }
                if (!string.IsNullOrEmpty(param.Mobile))
                {
                    expression = expression.And(t => t.Mobile.Contains(param.Mobile));
                }
                if (param.UserStatus > -1)
                {
                    expression = expression.And(t => t.UserStatus == param.UserStatus);
                }
                if (!string.IsNullOrEmpty(param.StartTime.ParseToString()))
                {
                    expression = expression.And(t => t.BaseModifyTime >= param.StartTime);
                }
                if (!string.IsNullOrEmpty(param.EndTime.ParseToString()))
                {
                    param.EndTime = param.EndTime.Value.Date.Add(new TimeSpan(23, 59, 59));
                    expression = expression.And(t => t.BaseModifyTime <= param.EndTime);
                }
                if (param.ChildrenDepartmentIdList != null && param.ChildrenDepartmentIdList.Count > 0)
                {
                    expression = expression.And(t => param.ChildrenDepartmentIdList.Contains(t.DepartmentId.Value));
                }

                if (param.ThirdUserId.Length > 0)
                {
                    expression = expression.And(t => t.ThirdUserId == param.ThirdUserId);
                }
            }
            return expression;
        }
        #endregion
    }
}
