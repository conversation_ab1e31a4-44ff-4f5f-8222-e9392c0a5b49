﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.InstrumentManage;
using Dqy.Syjx.Model.Param.InstrumentManage;
using System.Data;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Enum.InstrumentManage;
using Dqy.Syjx.Entity.OrganizationManage;
using Dqy.Syjx.Service.OrganizationManage;
using Dqy.Syjx.Entity.SystemManage;

namespace Dqy.Syjx.Service.InstrumentManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-09-27 17:37
    /// 描 述：服务类
    /// </summary>
    public class PurchaseDeclarationService :  RepositoryFactory
    {

        private UserSubjectSetService userSubjectSetService = new UserSubjectSetService();

        #region 获取数据
        public async Task<List<PurchaseDeclarationEntity>> GetList(PurchaseDeclarationListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<PurchaseDeclarationEntity>> GetPageList(PurchaseDeclarationListParam param, Pagination pagination)
        {
            StringBuilder sql = new StringBuilder();
            var expression = ListFilter(param, sql);
            var list = await this.BaseRepository().FindList<PurchaseDeclarationEntity>(sql.ToString(), expression.ToArray(), pagination);
            return list.ToList();
        }

        public async Task<PurchaseDeclarationEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<PurchaseDeclarationEntity>(id);
        }

        /// <summary>
        /// 获取总计金额
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<decimal> GetTotalSum(PurchaseDeclarationListParam param)
        {
            StringBuilder sql = new StringBuilder();
            var expression = ListFilter(param, sql, " ISNULL(SUM(AmountSum) ,0) AS AmountSum ");
            DataTable dt = await this.BaseRepository().FindTable(sql.ToString(), expression.ToArray());
            decimal total = 0;
            if (dt != null && dt.Rows.Count > 0)
            {
                total = dt.Rows[0]["AmountSum"].ParseToDecimal();
            }
            return total;
        }

        /// <summary>
        /// 获取导出数据
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<List<PurchaseDeclarationEntity>> GetExportPageList(PurchaseDeclarationListParam param, Pagination pagination)
        {
            StringBuilder sql = new StringBuilder();
            sql.Append(@$" SELECT * From (
                               SELECT  EL.AllocateType,PD.Id ,
                                       PD.BaseIsDelete ,PD.BaseCreateTime ,PD.BaseModifyTime ,PD.BaseCreatorId ,PD.BaseModifierId ,PD.BaseVersion ,
                                       PD.PurchaseYear ,PD.InstrumentStandardId ,PD.Name ,PD.ModelStandardId ,PD.Model ,PD.UnitName ,
                                       PD.Num ,PD.Price ,(PD.Num * PD.Price) AS AmountSum ,
                                       PD.Stage ,PD.StageId ,PD.Course ,PD.CourseId ,
                                       PD.Reason ,PD.Statuz ,PD.IsGoBack ,PD.SchoolId ,
                                       (CASE SD.Depth WHEN 3 THEN SD3.Id WHEN 2 THEN SD2.Id WHEN 1 THEN SD.Id END) AS InstrumentClassId ,
                                       (CASE SD.Depth WHEN 3 THEN SD3.Name WHEN 2 THEN SD2.Name WHEN 1 THEN SD.Name END) AS InstrumentClassName ,
                                       U.RealName AS UserName ,
                                       PD.BaseModifyTime AS ApprovalDate,
                                       UN.Name AS SchoolName ,UR.UnitId AS CountyId ,UN.Sort ,
                                       ISNULL(SD4.Code ,SD.Code) AS Code ,PD.PurchaseType,PD.OriginalCode
                               FROM  eq_PurchaseDeclaration AS PD
                               INNER JOIN  SysUser AS U ON PD.BaseCreatorId = U.Id
                               INNER JOIN  up_Unit AS UN ON PD.SchoolId = UN.Id
                               INNER JOIN  up_UnitRelation AS UR ON PD.SchoolId = UR.ExtensionObjId AND UR.ExtensionType = {UnitRelationTypeEnum.Unit.ParseToInt()}
                               INNER JOIN  eq_InstrumentStandard AS SD ON PD.InstrumentStandardId = SD.Id AND SD.ClassType = 1 AND IsLast = 1
                               INNER JOIN  eq_InstrumentStandard AS SD2 ON SD.Pid = SD2.Id
                               LEFT  JOIN  eq_InstrumentStandard AS SD3 ON SD2.Pid = SD3.Id
                               LEFT  JOIN  eq_InstrumentStandard AS SD4 ON PD.ModelStandardId = SD4.Id AND SD4.ClassType = 2
                               LEFT  JOIN  eq_InstrumentEvaluateStandard AS ES ON PD.StageId = ES.SchoolStage AND PD.CourseId = ES.DictionaryId1005 AND ES.Statuz = 1  AND ES.BaseIsDelete = 0
			                   LEFT  JOIN  eq_InstrumentEvaluateList AS EL ON EL.EvaluateStandardId = ES.Id AND EL.Statuz = 1 AND EL.InstrumentName = PD.Name AND EL.Code = SD.Code  AND EL.BaseIsDelete = 0
                               WHERE PD.BaseIsDelete = 0
                          ) as T WHERE  1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.SchoolId.HasValue && param.SchoolId > 0)
                {
                    sql.Append(" AND SchoolId = @SchoolId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
                }
                if (!param.CountyId.IsNullOrZero())
                {
                    sql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
                if (param.StartDate.HasValue)
                {
                    sql.Append(" AND BaseCreateTime >= @StartDate");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@StartDate", param.StartDate));
                }
                if (param.EndDate.HasValue)
                {
                    sql.Append(" AND BaseCreateTime <= @EndDate");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@EndDate", param.EndDate.Value.AddDays(1).AddSeconds(-1)));
                }
                if (!param.PurchaseYear.IsNullOrZero() && param.PurchaseYear > 0)
                {
                    sql.Append(" AND PurchaseYear = @PurchaseYear ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@PurchaseYear", param.PurchaseYear));
                }
                if (!param.InstrumentClassId.IsNullOrZero() && param.InstrumentClassId > 0)
                {
                    sql.Append(" AND InstrumentClassId = @InstrumentClassId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@InstrumentClassId", param.InstrumentClassId));
                }
                if (!param.CourseId.IsNullOrZero() && param.CourseId > 0)
                {
                    sql.Append(" AND CourseId = @CourseId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (param.Statuz.HasValue && param.Statuz > -1)
                {
                    sql.Append(" AND Statuz = @Statuz");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Statuz", param.Statuz));
                }
                if (!param.UserId.IsNullOrZero())
                {
                    sql.Append(" AND BaseCreatorId = @UserId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UserId", param.UserId));
                }
                if (!param.UserName.IsEmpty())
                {
                    sql.Append($" AND UserName LIKE '%{param.UserName.Trim()}%'");
                }
                if (param.AuditStartDate.HasValue)
                {
                    sql.Append(" AND ApprovalDate >= @AuditStartDate");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@AuditStartDate", param.AuditStartDate));
                }
                if (param.AuditEndDate.HasValue)
                {
                    sql.Append(" AND ApprovalDate <= @AuditEndDate");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@AuditEndDate", param.AuditEndDate.Value.AddDays(1).AddSeconds(-1)));
                }
                if (!param.PurchaseType.IsNullOrZero())
                {
                    sql.Append(" AND PurchaseType = @PurchaseType");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@PurchaseType", param.PurchaseType));
                }
                if (!string.IsNullOrWhiteSpace(param.KeyWord))
                {
                    sql.Append($" AND Name LIKE '%{param.KeyWord.Trim()}%' ");
                }

                if (param.AllocateType > 0)
                {
                    sql.Append($" AND AllocateType = {param.AllocateType}");
                }

                if (param.SetUserId > 0)
                {
                    //判断用户是否设置了学段、学科
                    var listStage = userSubjectSetService.GetUserSetStageList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listStage.Count > 0)
                    {
                        string stageId = string.Join(",", listStage.Select(f => f.DictionaryId));
                        sql.Append($" AND StageId IN ({stageId})");
                    }

                    var listCourse = userSubjectSetService.GetUserSetCourseList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listCourse.Count > 0)
                    {
                        string courseId = string.Join(",", listCourse.Select(f => f.DictionaryId));
                        sql.Append($" AND CourseId IN ({courseId})");
                    }

                }
            }
            var list = await this.BaseRepository().FindList<PurchaseDeclarationEntity>(sql.ToString(), parameter.ToArray(), pagination);
            return list.ToList();
        }

        /// <summary>
        /// 获取按汇总导出数据
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<List<PurchaseDeclarationEntity>> GetExportSummaryList(PurchaseDeclarationListParam param)
        {
            StringBuilder sql = new StringBuilder();
            sql.Append(@$" SELECT PurchaseYear ,InstrumentClassId ,InstrumentClassName ,InstrumentStandardId,Name ,ModelStandardId ,Model ,UnitName ,StageId ,Stage ,CourseId ,Course  ,SUM(Num) AS Num ,Code
                           From (
                               SELECT  PD.Id ,
                                       PD.BaseIsDelete ,PD.BaseCreateTime ,PD.BaseModifyTime ,PD.BaseCreatorId ,PD.BaseModifierId ,PD.BaseVersion ,
                                       PD.PurchaseYear ,PD.InstrumentStandardId ,PD.Name ,PD.ModelStandardId ,PD.Model ,PD.UnitName ,
                                       PD.Num ,PD.Price ,(PD.Num * PD.Price) AS AmountSum ,
                                       PD.Stage ,PD.StageId ,PD.Course ,PD.CourseId ,
                                       PD.Reason ,PD.Statuz ,PD.IsGoBack ,PD.SchoolId ,
                                       (CASE SD.Depth WHEN 3 THEN SD3.Id WHEN 2 THEN SD2.Id WHEN 1 THEN SD.Id END) AS InstrumentClassId ,
                                       (CASE SD.Depth WHEN 3 THEN SD3.Name WHEN 2 THEN SD2.Name WHEN 1 THEN SD.Name END) AS InstrumentClassName ,
                                       U.RealName AS UserName ,
                                       UN.Name AS SchoolName ,UR.UnitId AS CountyId ,UN.Sort ,
                                       ISNULL(SD4.Code ,SD.Code) AS Code ,PD.PurchaseType
                               FROM  eq_PurchaseDeclaration AS PD
                               INNER JOIN  SysUser AS U ON PD.BaseCreatorId = U.Id
                               INNER JOIN  up_Unit AS UN ON PD.SchoolId = UN.Id
                               INNER JOIN  up_UnitRelation AS UR ON PD.SchoolId = UR.ExtensionObjId AND UR.ExtensionType = {UnitRelationTypeEnum.Unit.ParseToInt()}
                               INNER JOIN  eq_InstrumentStandard AS SD ON PD.InstrumentStandardId = SD.Id AND SD.ClassType = 1 AND IsLast = 1
                               INNER JOIN  eq_InstrumentStandard AS SD2 ON SD.Pid = SD2.Id
                               LEFT  JOIN  eq_InstrumentStandard AS SD3 ON SD2.Pid = SD3.Id
                               LEFT  JOIN  eq_InstrumentStandard AS SD4 ON PD.ModelStandardId = SD4.Id AND SD4.ClassType = 2
                               WHERE PD.BaseIsDelete = 0
                          ) as T WHERE  1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.SchoolId.HasValue && param.SchoolId > 0)
                {
                    sql.Append(" AND SchoolId = @SchoolId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
                }
                if (!param.CountyId.IsNullOrZero())
                {
                    sql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
                if (param.StartDate.HasValue)
                {
                    sql.Append(" AND BaseCreateTime >= @StartDate");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@StartDate", param.StartDate));
                }
                if (param.EndDate.HasValue)
                {
                    sql.Append(" AND BaseCreateTime <= @EndDate");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@EndDate", param.EndDate.Value.AddDays(1).AddSeconds(-1)));
                }
                if (!param.PurchaseYear.IsNullOrZero() && param.PurchaseYear > 0)
                {
                    sql.Append(" AND PurchaseYear = @PurchaseYear ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@PurchaseYear", param.PurchaseYear));
                }
                if (!param.InstrumentClassId.IsNullOrZero() && param.InstrumentClassId > 0)
                {
                    sql.Append(" AND InstrumentClassId = @InstrumentClassId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@InstrumentClassId", param.InstrumentClassId));
                }
                if (!param.CourseId.IsNullOrZero() && param.CourseId > 0)
                {
                    sql.Append(" AND CourseId = @CourseId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (param.Statuz.HasValue && param.Statuz > -1)
                {
                    sql.Append(" AND Statuz = @Statuz");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Statuz", param.Statuz));
                }
                if (!param.UserId.IsNullOrZero())
                {
                    sql.Append(" AND BaseCreatorId = @UserId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UserId", param.UserId));
                }
                if (!param.UserName.IsEmpty())
                {
                    sql.Append($" AND UserName LIKE '%{param.UserName.Trim()}%'");
                }
            }
            sql.Append(" GROUP BY PurchaseYear ,InstrumentClassId ,InstrumentClassName ,InstrumentStandardId,Name ,ModelStandardId ,Model ,UnitName ,StageId ,Stage ,CourseId ,Course ,Code ");
            var list = await this.BaseRepository().FindList<PurchaseDeclarationEntity>(sql.ToString(), parameter.ToArray());
            return list.ToList();
        }

        /// <summary>
        /// 获取市级年度采购计划汇总数据
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<(List<PurchaseDeclarationEntity> list, decimal totalAmount)> GetPurchaseCityGroupList(PurchaseDeclarationListParam param, Pagination pagination)
        {
            string sql = $@"
	                        SELECT	UR2.UnitId AS CityId ,
			                        UR.UnitId AS CountyId ,AR.AreaName ,U2.Sort AS CountySort ,
			                        PD.PurchaseYear ,PD.StageId ,PD.CourseId ,
			                        D.DicName AS Stage ,D2.DicName AS Course ,SUM(PD.Num * PD.Price) AS AmountSum
	                        FROM  eq_PurchaseDeclaration AS PD
	                        INNER JOIN  sys_static_dictionary AS D ON PD.StageId = D.DictionaryId AND D.TypeCode = '{DicTypeCodeEnum.SchoolStage.ParseToInt()}'
	                        INNER JOIN  sys_static_dictionary AS D2 ON PD.CourseId = D2.DictionaryId AND D2.TypeCode = '{DicTypeCodeEnum.Course.ParseToInt()}'
	                        INNER JOIN  up_Unit AS U ON PD.SchoolId = U.Id AND U.BaseIsDelete = 0
	                        INNER JOIN  up_UnitRelation AS UR ON PD.SchoolId = UR.ExtensionObjId AND UR.BaseIsDelete = 0 AND UR.ExtensionType = {UnitRelationTypeEnum.Unit.ParseToInt()}
	                        INNER JOIN  up_UnitRelation AS UR2 ON UR.UnitId = UR2.ExtensionObjId AND UR2.BaseIsDelete = 0 AND UR2.ExtensionType = {UnitRelationTypeEnum.Unit.ParseToInt()}
	                        INNER JOIN  up_Unit AS U2 ON UR.UnitId = U2.Id AND U2.BaseIsDelete = 0
	                        INNER JOIN  SysArea AS AR ON U2.AreaId = AR.AreaCode
	                        WHERE PD.BaseIsDelete = 0 AND PD.Statuz = {InstrumentAuditStatuzEnum.End.ParseToInt()}
	                        GROUP BY UR2.UnitId ,U2.Sort ,UR.UnitId ,PD.PurchaseYear ,PD.StageId ,PD.CourseId ,D.DicName ,D2.DicName ,AR.AreaName
            ";
            StringBuilder whereSql = new StringBuilder();
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (!param.CityId.IsNullOrZero())
                {
                    whereSql.Append(" AND CityId = @CityId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CityId", param.CityId));
                }
                if (!param.CountyId.IsNullOrZero())
                {
                    whereSql.Append(" AND CountyId = @CountyId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
                if (!param.PurchaseYear.IsNullOrZero())
                {
                    whereSql.Append(" AND PurchaseYear = @PurchaseYear");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@PurchaseYear", param.PurchaseYear));
                }
                if (!param.StageId.IsNullOrZero())
                {
                    whereSql.Append(" AND StageId = @StageId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@StageId", param.StageId));
                }
                if (!param.CourseId.IsNullOrZero())
                {
                    whereSql.Append(" AND CourseId = @CourseId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
            }

            StringBuilder listSql = new StringBuilder();
            listSql.Append(" SELECT * FROM ( ");
            listSql.Append(sql.ToString());
            listSql.Append(" )T WHERE 1 = 1 ");
            listSql.Append(whereSql.ToString());
            var listR = await this.BaseRepository().FindList<PurchaseDeclarationEntity>(listSql.ToString(), parameter.ToArray(), pagination);


            StringBuilder amountSql = new StringBuilder();
            amountSql.Append(" SELECT ISNULL(SUM(AmountSum) ,0) AS AmountSum FROM ( ");
            amountSql.Append(sql.ToString());
            amountSql.Append(" )T  WHERE 1 = 1 ");
            amountSql.Append(whereSql.ToString());
            var amountR = await this.BaseRepository().FindTable(amountSql.ToString(), parameter.ToArray());
            decimal amountTotal = 0;
            if (amountR != null && amountR.Rows.Count > 0)
            {
                amountTotal = amountR.Rows[0]["AmountSum"].ParseToDecimal();
            }

            return (listR.ToList(), amountTotal);
        }


        /// <summary>
        /// 获取年度采购计划汇总数据
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<(List<PurchaseDeclarationEntity> list, decimal totalAmount)> GetPurchaseGroupList(PurchaseDeclarationListParam param, Pagination pagination)
        {
            string sql = $@"
	                        SELECT	UR2.UnitId AS CityId ,
			                        UR.UnitId AS CountyId ,AR.AreaName ,U2.Sort AS CountySort ,
			                        PD.SchoolId ,U.Name AS SchoolName ,U.Sort AS SchoolSort ,
			                        PD.PurchaseYear ,PD.StageId ,PD.CourseId ,
			                        D.DicName AS Stage ,D2.DicName AS Course ,SUM(PD.Num * PD.Price) AS AmountSum
	                        FROM  eq_PurchaseDeclaration AS PD
	                        INNER JOIN  sys_static_dictionary AS D ON PD.StageId = D.DictionaryId AND D.TypeCode = '{DicTypeCodeEnum.SchoolStage.ParseToInt()}'
	                        INNER JOIN  sys_static_dictionary AS D2 ON PD.CourseId = D2.DictionaryId AND D2.TypeCode = '{DicTypeCodeEnum.Course.ParseToInt()}'
	                        INNER JOIN  up_Unit AS U ON PD.SchoolId = U.Id AND U.BaseIsDelete = 0
	                        INNER JOIN  up_UnitRelation AS UR ON PD.SchoolId = UR.ExtensionObjId AND UR.BaseIsDelete = 0 AND UR.ExtensionType = {UnitRelationTypeEnum.Unit.ParseToInt()}
	                        INNER JOIN  up_UnitRelation AS UR2 ON UR.UnitId = UR2.ExtensionObjId AND UR2.BaseIsDelete = 0 AND UR2.ExtensionType = {UnitRelationTypeEnum.Unit.ParseToInt()}
	                        INNER JOIN  up_Unit AS U2 ON UR.UnitId = U2.Id AND U2.BaseIsDelete = 0
	                        INNER JOIN  SysArea AS AR ON U2.AreaId = AR.AreaCode
	                        WHERE PD.BaseIsDelete = 0 AND PD.Statuz = {InstrumentAuditStatuzEnum.End.ParseToInt()}
	                        GROUP BY UR2.UnitId ,U2.Sort ,U.Sort ,UR.UnitId ,PD.SchoolId ,PD.PurchaseYear ,PD.StageId ,PD.CourseId ,D.DicName ,D2.DicName ,U.Name ,AR.AreaName
            ";
            StringBuilder whereSql = new StringBuilder();
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (!param.CityId.IsNullOrZero())
                {
                    whereSql.Append(" AND CityId = @CityId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CityId", param.CityId));
                }
                if (!param.CountyId.IsNullOrZero())
                {
                    whereSql.Append(" AND CountyId = @CountyId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
                if (!param.SchoolId.IsNullOrZero())
                {
                    whereSql.Append(" AND SchoolId = @SchoolId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
                }
                if (!param.PurchaseYear.IsNullOrZero())
                {
                    whereSql.Append(" AND PurchaseYear = @PurchaseYear");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@PurchaseYear", param.PurchaseYear));
                }
                if (!param.StageId.IsNullOrZero())
                {
                    whereSql.Append(" AND StageId = @StageId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@StageId", param.StageId));
                }
                if (!param.CourseId.IsNullOrZero())
                {
                    whereSql.Append(" AND CourseId = @CourseId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }

                if (param.SetUserId > 0)
                {
                    //判断用户是否设置了学段、学科
                    var listStage = userSubjectSetService.GetUserSetStageList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listStage.Count > 0)
                    {
                        string stageId = string.Join(",", listStage.Select(f => f.DictionaryId));
                        whereSql.Append($" AND StageId IN ({stageId})");
                    }
                    var listCourse = userSubjectSetService.GetUserSetCourseList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listCourse.Count > 0)
                    {
                        string courseId = string.Join(",", listCourse.Select(f => f.DictionaryId));
                        whereSql.Append($" AND CourseId IN ({courseId})");
                    }
                }
            }

            StringBuilder listSql = new StringBuilder();
            listSql.Append(" SELECT * FROM ( ");
            listSql.Append(sql.ToString());
            listSql.Append(" )T WHERE 1 = 1 ");
            listSql.Append(whereSql.ToString());
            var listR = await this.BaseRepository().FindList<PurchaseDeclarationEntity>(listSql.ToString(), parameter.ToArray(), pagination);


            StringBuilder amountSql = new StringBuilder();
            amountSql.Append(" SELECT ISNULL(SUM(AmountSum) ,0) AS AmountSum FROM ( ");
            amountSql.Append(sql.ToString());
            amountSql.Append(" )T  WHERE 1 = 1 ");
            amountSql.Append(whereSql.ToString());
            var amountR = await this.BaseRepository().FindTable(amountSql.ToString(), parameter.ToArray());
            decimal amountTotal = 0;
            if (amountR != null && amountR.Rows.Count > 0)
            {
                amountTotal = amountR.Rows[0]["AmountSum"].ParseToDecimal();
            }

            return (listR.ToList(), amountTotal);
        }


        /// <summary>
        /// 获取按学校采购计划汇总数据
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<(List<PurchaseDeclarationEntity> list, decimal totalAmount)> GetPurchaseSchoolGroupList(PurchaseDeclarationListParam param, Pagination pagination)
        {
            StringBuilder sql = new StringBuilder();
            //sql.Append($@"
            //             SELECT	UR2.UnitId AS CityId ,
            //               UR.UnitId AS CountyId ,AR.AreaName ,U2.Sort AS CountySort ,
            //               PD.SchoolId ,U.Name AS SchoolName ,U.Sort AS SchoolSort ,
            //               PD.PurchaseYear ,SUM(PD.Num * PD.Price) AS AmountSum
            //             FROM  eq_PurchaseDeclaration AS PD
            //             INNER JOIN  up_Unit AS U ON PD.SchoolId = U.Id AND U.BaseIsDelete = 0
            //             INNER JOIN  up_UnitRelation AS UR ON PD.SchoolId = UR.ExtensionObjId AND UR.BaseIsDelete = 0 AND UR.ExtensionType = {UnitRelationTypeEnum.Unit.ParseToInt()}
            //             INNER JOIN  up_UnitRelation AS UR2 ON UR.UnitId = UR2.ExtensionObjId AND UR2.BaseIsDelete = 0 AND UR2.ExtensionType = {UnitRelationTypeEnum.Unit.ParseToInt()}
            //             INNER JOIN  up_Unit AS U2 ON UR.UnitId = U2.Id AND U2.BaseIsDelete = 0
            //             INNER JOIN  SysArea AS AR ON U2.AreaId = AR.AreaCode
            //             WHERE PD.BaseIsDelete = 0 AND PD.Statuz = {InstrumentAuditStatuzEnum.End.ParseToInt()} "
            //);

            sql.Append($@"SELECT UR2.UnitId AS CityId ,
		                        UR.UnitId AS CountyId ,AR.AreaName ,U2.Sort AS CountySort ,
		                        U.Id AS SchoolId ,U.Name AS SchoolName ,U.Sort AS SchoolSort ,
		                        PD.PurchaseYear ,SUM(PD.Num * PD.Price) AS AmountSum
                        FROM up_Unit AS U
                        LEFT JOIN  up_UnitRelation AS UR ON U.Id = UR.ExtensionObjId AND UR.BaseIsDelete = 0 AND UR.ExtensionType = 3
                        LEFT JOIN  up_UnitRelation AS UR2 ON UR.UnitId = UR2.ExtensionObjId AND UR2.BaseIsDelete = 0 AND UR2.ExtensionType = 3
                        LEFT JOIN  up_Unit AS U2 ON UR.UnitId = U2.Id AND U2.BaseIsDelete = 0
                        LEFT JOIN  SysArea AS AR ON U2.AreaId = AR.AreaCode
                        LEFT JOIN eq_PurchaseDeclaration AS PD ON PD.SchoolId = U.Id AND U.BaseIsDelete = 0 AND PD.BaseIsDelete = 0 AND PD.Statuz = {InstrumentAuditStatuzEnum.End.ParseToInt()}");

            var parameter = new List<DbParameter>();

            if (param != null)
            {
                if (!param.PurchaseYear.IsNullOrZero())
                {
                    sql.Append(" AND PD.PurchaseYear = @PurchaseYear");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@PurchaseYear", param.PurchaseYear));
                }
                if (!param.StageId.IsNullOrZero())
                {
                    sql.Append(" AND PD.StageId = @StageId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@StageId", param.StageId));
                }
                if (!param.CourseId.IsNullOrZero())
                {
                    sql.Append(" AND PD.CourseId = @CourseId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (!param.PurchaseType.IsNullOrZero())
                {
                    sql.Append(" AND PD.PurchaseType = @PurchaseType");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@PurchaseType", param.PurchaseType));
                }

                if (param.SetUserId > 0)
                {
                    //判断用户是否设置了学段、学科
                    var listStage = userSubjectSetService.GetUserSetStageList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listStage.Count > 0)
                    {
                        string stageId = string.Join(",", listStage.Select(f => f.DictionaryId));
                        sql.Append($" AND StageId IN ({stageId})");
                    }

                    var listCourse = userSubjectSetService.GetUserSetCourseList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listCourse.Count > 0)
                    {
                        string courseId = string.Join(",", listCourse.Select(f => f.DictionaryId));
                        sql.Append($" AND CourseId IN ({courseId})");
                    }

                }

                sql.Append(" WHERE 1 = 1 ");

                if (!param.CityId.IsNullOrZero())
                {
                    sql.Append(" AND UR2.UnitId = @CityId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CityId", param.CityId));
                }
                if (!param.CountyId.IsNullOrZero())
                {
                    sql.Append(" AND UR.UnitId = @CountyId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
                if (!param.SchoolId.IsNullOrZero())
                {
                    sql.Append(" AND U.Id = @SchoolId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
                }

            }

            sql.Append(" GROUP BY UR2.UnitId ,U2.Sort ,U.Sort ,UR.UnitId ,U.Id ,PD.PurchaseYear ,U.Name ,AR.AreaName");

            StringBuilder listSql = new StringBuilder();
            listSql.Append(" SELECT * FROM ( ");
            listSql.Append(sql.ToString());
            listSql.Append(" )T");
            var listR = await this.BaseRepository().FindList<PurchaseDeclarationEntity>(listSql.ToString(), parameter.ToArray(), pagination);


            StringBuilder amountSql = new StringBuilder();
            amountSql.Append(" SELECT ISNULL(SUM(AmountSum) ,0) AS AmountSum FROM ( ");
            amountSql.Append(sql.ToString());
            amountSql.Append(" )T  WHERE 1 = 1 ");
            //amountSql.Append(sql.ToString());
            var amountR = await this.BaseRepository().FindTable(amountSql.ToString(), parameter.ToArray());
            decimal amountTotal = 0;
            if (amountR != null && amountR.Rows.Count > 0)
            {
                amountTotal = amountR.Rows[0]["AmountSum"].ParseToDecimal();
            }

            return (listR.ToList(), amountTotal);
        }

        /// <summary>
        /// 按区县汇总采购计划分析列表
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<List<PurchaseDeclarationEntity>> GetCityPurchasePlanAnalyseList(PurchaseDeclarationListParam param, Pagination pagination)
        {
            StringBuilder sql = new StringBuilder();
            sql.AppendFormat(@"
SELECT * FROM (
	SELECT CityId ,CountyId ,AreaName ,CountySort ,PurchaseYear ,StageId ,Stage ,AmountSum ,CONVERT(DECIMAL(18 ,2) ,(AmountSum / SchoolStudentNum)) AS StudentAvgAmount ,(CONVERT(DECIMAL(18 ,2) ,(AmountSum / SchoolStudentNum)) - StandardAvgAmount) AS StudentDiffMount FROM (
		SELECT	U.Id AS CityId ,0 AS CountyId ,'全市' AS AreaName  ,0 AS CountySort ,ISNULL(PD.PurchaseYear ,{0}) AS PurchaseYear ,D.DictionaryId AS StageId ,D.DicName AS Stage ,
				ISNULL(SUM(PD.Price * PD.Num) ,0) AS AmountSum ,
				SchoolStudentNum = ISNULL((SELECT SUM(StudentNum) FROM  up_SchoolGradeClass AS SGC INNER JOIN  up_UnitRelation AS URR ON SGC.UnitId = URR.ExtensionObjId AND URR.BaseIsDelete = 0 AND URR.ExtensionType = 3 INNER JOIN  up_UnitRelation AS URR2 ON URR.UnitId = URR2.ExtensionObjId AND URR2.BaseIsDelete = 0 AND URR2.ExtensionType = 3 WHERE SGC.BaseIsDelete = 0 AND SGC.IsGraduate = 0 AND SGC.SchoolStage = D.DictionaryId AND URR2.UnitId = U.Id ) ,-1) ,
				StandardAvgAmount = (SELECT ConfigValue FROM  sys_ConfigSet WHERE BaseIsDelete = 0 AND ConfigType = 0 AND UnitType = 0 AND TypeCode = (CASE WHEN D.DictionaryId = 1002001 THEN '1001_XXSJNJF' WHEN D.DictionaryId = 1002002 THEN '1001_CZSJNJF' ELSE '1001_GZSJNJF' END))
		FROM  up_Unit AS U
		INNER JOIN  sys_static_dictionary AS D ON D.TypeCode = '1002' AND D.BaseIsDelete = 0
		LEFT JOIN ( SELECT  ur2.UnitId AS CityId ,pd.StageId ,pd.Price ,pd.Num ,pd.PurchaseYear
					FROM     eq_PurchaseDeclaration AS pd
					INNER JOIN  up_UnitRelation AS ur ON pd.SchoolId = ur.ExtensionObjId AND ur.ExtensionType = 3 AND ur.BaseIsDelete = 0
					INNER JOIN  up_UnitRelation AS ur2 ON ur.UnitId = ur2.ExtensionObjId AND ur2.ExtensionType = 3 AND ur2.BaseIsDelete = 0
					WHERE   pd.BaseIsDelete = 0 AND pd.Statuz = {1} AND pd.PurchaseYear = {0}
				  ) AS PD ON U.Id = PD.CityId AND D.DictionaryId = PD.StageId
		WHERE U.BaseIsDelete = 0
		GROUP BY U.Id ,PD.PurchaseYear ,D.DictionaryId ,D.DicName
	)t1

	UNION ALL

	SELECT  CityId ,CountyId ,AreaName ,CountySort ,PurchaseYear ,StageId ,Stage ,AmountSum  ,CONVERT(DECIMAL(18 ,2) ,(AmountSum / SchoolStudentNum)) AS StudentAvgAmount ,(CONVERT(DECIMAL(18 ,2) ,(AmountSum / SchoolStudentNum)) - StandardAvgAmount) AS StudentDiffMount FROM    (
			  SELECT UR.UnitId AS CityId ,U.Id AS CountyId ,AR.AreaName ,U.Sort AS CountySort ,D.DictionaryId AS StageId ,D.DicName AS Stage,
					 ISNULL(SUM(PD.Price * PD.Num) ,0) AS AmountSum ,ISNULL(PD.PurchaseYear ,{0}) AS PurchaseYear ,
					 SchoolStudentNum = ISNULL((SELECT SUM(StudentNum) FROM  up_SchoolGradeClass AS SGC INNER JOIN  up_UnitRelation AS URR ON SGC.UnitId = URR.ExtensionObjId AND URR.BaseIsDelete = 0 AND URR.ExtensionType = 3 WHERE SGC.BaseIsDelete = 0 AND SGC.IsGraduate = 0 AND SGC.SchoolStage = D.DictionaryId AND URR.UnitId = U.Id ) ,-1) ,
					 StandardAvgAmount = (SELECT ConfigValue FROM  sys_ConfigSet WHERE BaseIsDelete = 0 AND ConfigType = 0 AND UnitType = 0 AND TypeCode = (CASE WHEN D.DictionaryId = 1002001 THEN '1001_XXSJNJF' WHEN D.DictionaryId = 1002002 THEN '1001_CZSJNJF' ELSE '1001_GZSJNJF' END))
			  FROM    up_UnitRelation AS UR
					 INNER JOIN  up_Unit AS U ON UR.ExtensionObjId = U.Id
					 INNER JOIN  SysArea AS AR ON U.AreaId = AR.AreaCode
					 INNER JOIN  sys_static_dictionary AS D ON D.TypeCode = '1002' AND D.BaseIsDelete = 0
					 LEFT JOIN ( SELECT  ur.UnitId AS CountyId ,pd.StageId ,pd.Price ,pd.Num ,pd.PurchaseYear
								 FROM     eq_PurchaseDeclaration AS pd
										 INNER JOIN  up_UnitRelation AS ur ON pd.SchoolId = ur.ExtensionObjId AND ur.ExtensionType = 3 AND ur.BaseIsDelete = 0
								 WHERE   pd.BaseIsDelete = 0 AND pd.Statuz = {1} AND pd.PurchaseYear = {0}
							   ) AS PD ON U.Id = PD.CountyId AND D.DictionaryId = PD.StageId
			  WHERE  UR.BaseIsDelete = 0 AND UR.ExtensionType = 3
			  GROUP BY UR.UnitId ,U.Id ,AR.AreaName ,U.Sort ,D.DictionaryId ,D.DicName ,PD.PurchaseYear
	) t2
)T WHERE CityId = {2}
            ", param.PurchaseYear, InstrumentAuditStatuzEnum.End.ParseToInt(), param.CityId);
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (!param.CountyId.IsNullOrZero())
                {
                    sql.Append(" AND CountyId = @CountyId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
                if (!param.StageId.IsNullOrZero())
                {
                    sql.Append(" AND StageId = @StageId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@StageId", param.StageId));
                }
            }
            return (await this.BaseRepository().FindList<PurchaseDeclarationEntity>(sql.ToString(), parameter.ToArray(), pagination)).ToList();
        }

        /// <summary>
        /// 采购计划分析-区县按学校查看
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<List<PurchaseDeclarationEntity>> GetSchoolPurchasePlanAnalyseByCounty(PurchaseDeclarationListParam param, Pagination pagination)
        {
            string where = " 1 = 1";
            if (param.CourseId > 0)
            {
                where += $" AND PD.CourseId = {param.CourseId}";
            }
            else
            {
                if (param.SetUserId > 0)
                {
                    var listCourse = userSubjectSetService.GetUserSetCourseList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listCourse.Count > 0)
                    {
                        string courseId = string.Join(",", listCourse.Select(f => f.DictionaryId));
                        where += $" AND PD.CourseId IN ({courseId})";
                    }
                }
            }
            StringBuilder sql = new StringBuilder();
            sql.AppendFormat(@"
        SELECT * FROM (
	        SELECT CountyId ,0 AS SchoolId ,'全区（县）' AS SchoolName, 0 AS SchoolSort ,PurchaseYear ,StageId ,Stage ,AmountSum
	        ,CONVERT(DECIMAL(18 ,2) ,(AmountSum / SchoolStudentNum)) AS StudentAvgAmount
	        ,(CONVERT(DECIMAL(18 ,2) ,(AmountSum / SchoolStudentNum)) - StandardAvgAmount) AS StudentDiffMount
	        FROM
	        (
		        SELECT SUM(CONVERT(DECIMAL(18,2),CS.ConfigValue)) AS StandardAvgAmount,CountyId,
			           StageId,Stage,AmountSum,PurchaseYear,SchoolStudentNum
		         FROM
		    (
			    SELECT  U.Id AS CountyId ,D.DictionaryId AS StageId ,D.DicName AS Stage ,
					    ISNULL(SUM(PD.Price * PD.Num), 0) AS AmountSum ,ISNULL(PD.PurchaseYear, {0}) AS PurchaseYear,
					    SchoolStudentNum
					    ,(CASE WHEN D.DictionaryId = 1002001 THEN '1001_XXSJNJF' WHEN D.DictionaryId = 1002002 THEN '1001_CZSJNJF' ELSE '1001_GZSJNJF' END) AS Code
			    FROM       up_Unit AS U
			    INNER JOIN  sys_static_dictionary AS D ON D.TypeCode = '1002' AND D.BaseIsDelete = 0
			    LEFT JOIN ( SELECT  ur.UnitId AS CountyId ,pd.StageId ,pd.Price ,pd.Num ,pd.PurchaseYear,pd.CourseId
						    FROM  eq_PurchaseDeclaration AS pd
						    INNER JOIN  up_UnitRelation AS ur ON pd.SchoolId = ur.ExtensionObjId AND ur.ExtensionType = 3 AND ur.BaseIsDelete = 0
						    WHERE  {3} AND pd.BaseIsDelete = 0 AND pd.Statuz = {1} AND pd.PurchaseYear = {0} )
						    AS PD ON U.Id = PD.CountyId AND D.DictionaryId = PD.StageId
			    LEFT JOIN (
				    SELECT SUM(StudentNum) AS SchoolStudentNum,SGC.SchoolStage,URR.UnitId
				    FROM  up_SchoolGradeClass AS SGC
				    INNER JOIN  up_UnitRelation AS URR ON SGC.UnitId = URR.ExtensionObjId AND URR.BaseIsDelete = 0 AND URR.ExtensionType = 3
				    WHERE SGC.BaseIsDelete = 0 AND SGC.IsGraduate = 0
				    GROUP BY SGC.SchoolStage,URR.UnitId
			    ) AS A ON A.SchoolStage = D.DictionaryId AND A.UnitId = U.Id

			    GROUP BY  U.Id ,U.Sort ,D.DictionaryId ,D.DicName ,PD.PurchaseYear,A.SchoolStudentNum
		    ) AS B
		    LEFT JOIN sys_ConfigSet AS CS ON CS.TypeCode = B.Code
		    GROUP BY CountyId,B.PurchaseYear,B.StageId,B.Stage,AmountSum,B.SchoolStudentNum
	    )AS T1

	UNION ALL

    SELECT CountyId ,SchoolId ,SchoolName ,SchoolSort ,PurchaseYear ,StageId ,Stage ,AmountSum
		  ,CONVERT(DECIMAL(18 ,2) ,(AmountSum / SchoolStudentNum)) AS StudentAvgAmount
		  ,(CONVERT(DECIMAL(18 ,2) ,(AmountSum / SchoolStudentNum)) - StandardAvgAmount) AS StudentDiffMount

	FROM
	(
	SELECT SUM(CONVERT(DECIMAL(18,2),CS.ConfigValue)) AS StandardAvgAmount,CountyId,SchoolId,SchoolName,SchoolSort,
			   StageId,Stage,AmountSum,PurchaseYear,SchoolStudentNum
		 FROM
		(
			SELECT UR.UnitId AS CountyId ,U.Id AS SchoolId ,U.Name AS SchoolName ,U.Sort AS SchoolSort ,
					D.DictionaryId AS StageId ,D.DicName AS Stage ,ISNULL(SUM(PD.Price * PD.Num) ,0) AS AmountSum ,ISNULL(PD.PurchaseYear ,{0}) AS PurchaseYear ,
					ISNULL(F.SchoolStudentNum,-1) AS SchoolStudentNum
					,(CASE WHEN D.DictionaryId = 1002001 THEN '1001_XXSJNJF' WHEN D.DictionaryId = 1002002 THEN '1001_CZSJNJF' ELSE '1001_GZSJNJF' END) AS Code
			FROM  up_UnitRelation AS UR
			INNER JOIN  up_Unit AS U ON UR.ExtensionObjId = U.Id
			INNER JOIN  up_Unit AS U2 ON UR.UnitId = U2.Id
			INNER JOIN  up_SchoolExtension AS SE ON U.Id = SE.UnitId
			INNER JOIN  sys_dictionary_relation AS DR ON SE.SchoolProp = DR.DictionaryId
			INNER JOIN  sys_static_dictionary AS D ON DR.DictionaryToId = D.DictionaryId
			LEFT JOIN  eq_PurchaseDeclaration AS PD ON U.Id = PD.SchoolId AND PD.BaseIsDelete = 0 AND PD.Statuz = {1}
						AND PD.PurchaseYear = {0} AND PD.StageId = D.DictionaryId
			LEFT JOIN(
				SELECT SUM(StudentNum) AS SchoolStudentNum,SchoolStage,UnitId FROM  up_SchoolGradeClass WHERE BaseIsDelete = 0 AND IsGraduate = 0 GROUP BY SchoolStage,UnitId
			) AS F ON F.SchoolStage = D.DictionaryId AND F.UnitId = U.Id
			WHERE   {3} AND UR.BaseIsDelete = 0 AND UR.ExtensionType = 3
			GROUP BY F.SchoolStudentNum, UR.UnitId ,U.Id ,U.Name ,U.Sort ,D.DictionaryId ,D.DicName ,PD.PurchaseYear ,U2.Sort
		) AS B
		LEFT JOIN sys_ConfigSet AS CS ON CS.TypeCode = B.Code
		GROUP BY CountyId,B.SchoolId,SchoolName,SchoolSort,B.PurchaseYear,B.StageId,B.Stage,AmountSum,B.SchoolStudentNum
	)AS T2
) AS T   WHERE CountyId = {2}", param.PurchaseYear, InstrumentAuditStatuzEnum.End.ParseToInt(), param.CountyId, where);
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (!param.SchoolId.IsNullOrZero())
                {
                    sql.Append(" AND SchoolId = @SchoolId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
                }
                if (!param.StageId.IsNullOrZero())
                {
                    sql.Append(" AND StageId = @StageId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@StageId", param.StageId));
                }

                if (param.SetUserId > 0)
                {
                    //判断用户是否设置了学段、学科
                    var listStage = userSubjectSetService.GetUserSetStageList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listStage.Count > 0)
                    {
                        string stageId = string.Join(",", listStage.Select(f => f.DictionaryId));
                        sql.Append($" AND StageId IN ({stageId})");
                    }

                }
            }


            return (await this.BaseRepository().FindList<PurchaseDeclarationEntity>(sql.ToString(), parameter.ToArray(), pagination)).ToList();
        }

        /// <summary>
        /// 采购计划分析总计金额
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<decimal> GetPurchasePlanAnalyseTotal(PurchaseDeclarationListParam param)
        {
            StringBuilder sql = new StringBuilder();
            sql.AppendFormat(@"
                    SELECT ISNULL(SUM(PD.Price * PD.Num) ,0) AS AmountSum
                    FROM  eq_PurchaseDeclaration AS PD
                    INNER JOIN  up_UnitRelation AS UR ON PD.SchoolId = UR.ExtensionObjId AND UR.ExtensionType = 3
                    INNER JOIN  up_UnitRelation AS UR2 ON UR.UnitId = UR2.ExtensionObjId AND UR2.ExtensionType = 3
                    WHERE PD.BaseIsDelete = 0 AND PD.Statuz = {0} AND PD.PurchaseYear = {1}
            ", InstrumentAuditStatuzEnum.End.ParseToInt(), param.PurchaseYear);
            if (param != null)
            {
                if (!param.SchoolId.IsNullOrZero())
                {
                    sql.AppendFormat(" AND SchoolId = {0}", param.SchoolId);
                }
                if (!param.CountyId.IsNullOrZero())
                {
                    sql.AppendFormat(" AND UR.UnitId = {0}", param.CountyId);
                }
                if (!param.CityId.IsNullOrZero())
                {
                    sql.AppendFormat(" AND UR2.UnitId = {0}", param.CityId);
                }
                if (!param.StageId.IsNullOrZero())
                {
                    sql.AppendFormat(" AND StageId = {0}", param.StageId);
                }

                if (param.SetUserId > 0)
                {
                    //判断用户是否设置了学段、学科
                    var listStage = userSubjectSetService.GetUserSetStageList(param.UserId.Value).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listStage.Count > 0)
                    {
                        string stageId = string.Join(",", listStage.Select(f => f.DictionaryId));
                        sql.Append($" AND StageId IN ({stageId})");
                    }
                }

                if (param.CourseId > 0)
                {
                    sql.AppendFormat($" AND PD.CourseId = {param.CourseId}");
                }
                else
                {
                    if (param.SetUserId > 0)
                    {
                        var listCourse = userSubjectSetService.GetUserSetCourseList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                        if (listCourse.Count > 0)
                        {
                            string courseId = string.Join(",", listCourse.Select(f => f.DictionaryId));
                            sql.AppendFormat($" AND PD.CourseId IN ({courseId})");
                        }
                    }
                }

            }



            return (await this.BaseRepository().FindObject(sql.ToString())).ParseToDecimal();
        }

        /// <summary>
        /// 采购计划分析-市级按学校查看
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<List<PurchaseDeclarationEntity>> GetSchoolPurchasePlanAnalyseByCity(PurchaseDeclarationListParam param, Pagination pagination)
        {
            StringBuilder sql = new StringBuilder();
            sql.AppendFormat(@"
                SELECT * FROM (
SELECT  CityId ,CountyId, AreaName ,CountySort ,0 AS SchoolId ,'全区（县）' AS SchoolName ,0 AS SchoolSort ,PurchaseYear ,StageId ,Stage ,AmountSum ,CONVERT(DECIMAL(18 ,2) ,(AmountSum / SchoolStudentNum)) AS StudentAvgAmount ,(CONVERT(DECIMAL(18 ,2) ,(AmountSum / SchoolStudentNum)) - StandardAvgAmount) AS StudentDiffMount
                            FROM    ( SELECT UR.UnitId AS CityId ,U.Id AS CountyId ,AR.AreaName ,U.Sort AS CountySort ,D.DictionaryId AS StageId ,D.DicName AS Stage,
				                             ISNULL(SUM(PD.Price * PD.Num) ,0) AS AmountSum ,ISNULL(PD.PurchaseYear ,{0}) AS PurchaseYear ,
				                             SchoolStudentNum = ISNULL((SELECT SUM(StudentNum) FROM  up_SchoolGradeClass AS SGC INNER JOIN  up_UnitRelation AS URR ON SGC.UnitId = URR.ExtensionObjId AND URR.BaseIsDelete = 0 AND URR.ExtensionType = 3 WHERE SGC.BaseIsDelete = 0 AND SGC.IsGraduate = 0 AND SGC.SchoolStage = D.DictionaryId AND URR.UnitId = U.Id ) ,-1) ,
				                             StandardAvgAmount = (SELECT ConfigValue FROM  sys_ConfigSet WHERE BaseIsDelete = 0 AND ConfigType = 0 AND UnitType = 0 AND TypeCode = (CASE WHEN D.DictionaryId = 1002001 THEN '1001_XXSJNJF' WHEN D.DictionaryId = 1002002 THEN '1001_CZSJNJF' ELSE '1001_GZSJNJF' END))
                                      FROM    up_UnitRelation AS UR
                                             INNER JOIN  up_Unit AS U ON UR.ExtensionObjId = U.Id
                                             INNER JOIN  SysArea AS AR ON U.AreaId = AR.AreaCode
                                             INNER JOIN  sys_static_dictionary AS D ON D.TypeCode = '1002' AND D.BaseIsDelete = 0
                                             LEFT JOIN ( SELECT  ur.UnitId AS CountyId ,pd.StageId ,pd.Price ,pd.Num ,pd.PurchaseYear
                                                         FROM     eq_PurchaseDeclaration AS pd
                                                                 INNER JOIN  up_UnitRelation AS ur ON pd.SchoolId = ur.ExtensionObjId AND ur.ExtensionType = 3 AND ur.BaseIsDelete = 0
                                                         WHERE   pd.BaseIsDelete = 0 AND pd.Statuz = {1} AND pd.PurchaseYear = {0}
                                                       ) AS PD ON U.Id = PD.CountyId AND D.DictionaryId = PD.StageId
                                      WHERE  UR.BaseIsDelete = 0 AND UR.ExtensionType = 3
		                              GROUP BY UR.UnitId ,U.Id ,AR.AreaName ,U.Sort ,D.DictionaryId ,D.DicName ,PD.PurchaseYear
                                    ) T
union all
SELECT CityId ,CountyId, AreaName ,CountySort ,SchoolId ,SchoolName ,SchoolSort ,PurchaseYear ,StageId ,Stage ,AmountSum ,CONVERT(DECIMAL(18 ,2) ,(AmountSum / SchoolStudentNum)) AS StudentAvgAmount ,(CONVERT(DECIMAL(18 ,2) ,(AmountSum / SchoolStudentNum)) - StandardAvgAmount) AS StudentDiffMount FROM (
                        SELECT UR2.UnitId AS CityId ,UR.UnitId AS CountyId ,AR.AreaName ,U.Id AS SchoolId ,U.Name AS SchoolName ,U.Sort AS SchoolSort ,U2.Sort AS CountySort ,
                                D.DictionaryId AS StageId ,D.DicName AS Stage ,ISNULL(SUM(PD.Price * PD.Num) ,0) AS AmountSum ,ISNULL(PD.PurchaseYear ,{0}) AS PurchaseYear ,
                                SchoolStudentNum = ISNULL((SELECT SUM(StudentNum) FROM  up_SchoolGradeClass WHERE BaseIsDelete = 0 AND IsGraduate = 0 AND SchoolStage = D.DictionaryId AND UnitId = U.Id ) ,-1) ,
                                StandardAvgAmount = (SELECT ConfigValue FROM  sys_ConfigSet WHERE BaseIsDelete = 0 AND ConfigType = 0 AND UnitType = 0 AND TypeCode = (CASE WHEN D.DictionaryId = 1002001 THEN '1001_XXSJNJF' WHEN D.DictionaryId = 1002002 THEN '1001_CZSJNJF' ELSE '1001_GZSJNJF' END))
                        FROM  up_UnitRelation AS UR
	                    INNER JOIN  up_UnitRelation AS UR2 ON UR.UnitId = UR2.ExtensionObjId AND UR2.BaseIsDelete = 0 AND UR2.ExtensionType = 3
                        INNER JOIN  up_Unit AS U ON UR.ExtensionObjId = U.Id
	                    INNER JOIN  up_Unit AS U2 ON UR.UnitId = U2.Id
	                    INNER JOIN  SysArea AS AR ON U2.AreaId = AR.AreaCode
                        INNER JOIN  up_SchoolExtension AS SE ON U.Id = SE.UnitId
                        INNER JOIN  sys_dictionary_relation AS DR ON SE.SchoolProp = DR.DictionaryId
                        INNER JOIN  sys_static_dictionary AS D ON DR.DictionaryToId = D.DictionaryId
                        LEFT JOIN  eq_PurchaseDeclaration AS PD ON U.Id = PD.SchoolId AND PD.BaseIsDelete = 0 AND PD.Statuz = {1} AND PD.PurchaseYear = {0} AND PD.StageId = D.DictionaryId
	                    WHERE UR.BaseIsDelete = 0 AND UR.ExtensionType = 3
                        GROUP BY UR2.UnitId ,UR.UnitId ,U.Id ,U.Name ,AR.AreaName ,U.Sort ,D.DictionaryId ,D.DicName ,PD.PurchaseYear ,U2.Sort
                    )T
)T WHERE CityId = {2}
            ", param.PurchaseYear, InstrumentAuditStatuzEnum.End.ParseToInt(), param.CityId);
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (!param.CountyId.IsNullOrZero())
                {
                    sql.Append(" AND CountyId = @CountyId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
                if (!param.SchoolId.IsNullOrZero())
                {
                    sql.Append(" AND SchoolId = @SchoolId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
                }
                if (!param.StageId.IsNullOrZero())
                {
                    sql.Append(" AND StageId = @StageId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@StageId", param.StageId));
                }
            }
            return (await this.BaseRepository().FindList<PurchaseDeclarationEntity>(sql.ToString(), parameter.ToArray(), pagination)).ToList();
        }

        #endregion

        #region 提交数据
        public async Task SaveForm(PurchaseDeclarationEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(PurchaseDeclarationEntity entity, Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update  eq_PurchaseDeclaration set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
                strSql = $" update  eq_PurchaseDeclaration set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }

        public async Task UpdatePurchaseType(long schoolId, string ids, int purchaseType)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update  eq_PurchaseDeclaration set PurchaseType = {purchaseType} where Id in ({ids}) and SchoolId = {schoolId} ";
            }
            else
            {
                strSql = $" update  eq_PurchaseDeclaration set PurchaseType = {purchaseType} where id = {ids} and SchoolId = {schoolId}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<PurchaseDeclarationEntity, bool>> ListFilter(PurchaseDeclarationListParam param)
        {
            var expression = LinqExtensions.True<PurchaseDeclarationEntity>();
            if (param != null)
            {
                expression = expression.And(t => t.BaseIsDelete == 0);
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (param.Statuz.HasValue)
                {
                    if (param.Statuz.Value.Equals(-100))
                    {
                        expression = expression.And(t => t.Statuz == 0 || t.Statuz == 11);
                    }
                    else
                    {
                        expression = expression.And(t => t.Statuz == param.Statuz);
                    }
                }
                if (param.UserId > 0)
                {
                    expression = expression.And(t => t.BaseCreatorId == param.UserId);
                }
                if (!param.SchoolId.IsNullOrZero())
                {
                    expression = expression.And(t => t.SchoolId == param.SchoolId);
                }
                if (!param.PurchaseYear.IsNullOrZero())
                {
                    expression = expression.And(t => t.PurchaseYear == param.PurchaseYear);
                }
            }
            return expression;
        }

        private List<DbParameter> ListFilter(PurchaseDeclarationListParam param, StringBuilder strSql ,string funcSql = "*")
        {
            if(param.IsAssociation == 0)
            {
                strSql.Append(@$" SELECT  {funcSql} From (
                               SELECT EL.AllocateType,PD.Id ,
                                    PD.BaseIsDelete ,PD.BaseCreateTime ,PD.BaseModifyTime ,PD.BaseCreatorId ,PD.BaseModifierId ,PD.BaseVersion ,
                                    PD.PurchaseYear ,PD.InstrumentStandardId ,PD.Name ,PD.ModelStandardId ,PD.Model ,PD.UnitName ,
                                    PD.Num ,PD.Price ,(PD.Num * PD.Price) AS AmountSum ,
                                    PD.Stage ,PD.StageId ,PD.Course ,PD.CourseId ,PD.Reason ,
                                    PD.Statuz ,PD.IsGoBack ,PD.SchoolId ,
                                    (CASE SD.Depth WHEN 3 THEN SD3.Id WHEN 2 THEN SD2.Id WHEN 1 THEN SD.Id END) AS InstrumentClassId ,
                                    (CASE SD.Depth WHEN 3 THEN SD3.Name WHEN 2 THEN SD2.Name WHEN 1 THEN SD.Name END) AS InstrumentClassName ,
                                    U.RealName AS UserName ,
                                    PD.BaseModifyTime AS ApprovalDate,
                                    SD.Code ,UN.Name AS SchoolName ,UR.UnitId AS CountyId ,UN.Sort ,SD.IsDangerChemical ,
		                            UR2.UnitId AS CityId ,PD.PurchaseType ,
                                    ISNULL(SDM.Code ,SD.Code) AS LastCode ,EL.AllocateType
                            FROM  eq_PurchaseDeclaration AS PD
                            INNER JOIN  SysUser AS U ON PD.BaseCreatorId = U.Id
                            INNER JOIN  up_Unit AS UN ON PD.SchoolId = UN.Id
                            INNER JOIN  up_UnitRelation AS UR ON PD.SchoolId = UR.ExtensionObjId AND UR.ExtensionType = {UnitRelationTypeEnum.Unit.ParseToInt()}
                            INNER JOIN  up_UnitRelation AS UR2 ON UR.UnitId = UR2.ExtensionObjId AND UR2.ExtensionType = {UnitRelationTypeEnum.Unit.ParseToInt()}
                            INNER JOIN  eq_InstrumentStandard AS SD ON PD.InstrumentStandardId = SD.Id AND SD.ClassType = 1 AND IsLast = 1
                            INNER JOIN  eq_InstrumentStandard AS SD2 ON SD.Pid = SD2.Id
                            LEFT JOIN  eq_InstrumentStandard AS SD3 ON SD2.Pid = SD3.Id
                            LEFT JOIN  eq_InstrumentStandard AS SDM ON PD.ModelStandardId = SDM.Id AND SDM.ClassType = 2
                            LEFT JOIN  eq_InstrumentEvaluateStandard AS ES ON PD.StageId = ES.SchoolStage AND PD.CourseId = ES.DictionaryId1005 AND ES.Statuz = 1 AND ES.BaseIsDelete = 0
			                LEFT JOIN  eq_InstrumentEvaluateList AS EL ON EL.EvaluateStandardId = ES.Id AND EL.Statuz = 1 AND EL.InstrumentName = PD.Name AND EL.Code = SD.Code AND EL.BaseIsDelete = 0
                            WHERE PD.BaseIsDelete = 0
                          ) as T WHERE  1 = 1 ");
            }
            else
            {
                strSql.Append(@$" SELECT  {funcSql} From (
                               SELECT PD.Id ,
                                    PD.BaseIsDelete ,PD.BaseCreateTime ,PD.BaseModifyTime ,PD.BaseCreatorId ,PD.BaseModifierId ,PD.BaseVersion ,
                                    PD.PurchaseYear ,PD.InstrumentStandardId ,PD.Name ,PD.ModelStandardId ,PD.Model ,PD.UnitName ,
                                    PD.Num ,PD.Price ,(PD.Num * PD.Price) AS AmountSum ,
                                    PD.Stage ,PD.StageId ,PD.Course ,PD.CourseId ,PD.Reason ,
                                    PD.Statuz ,PD.IsGoBack ,PD.SchoolId ,
                                    (CASE SD.Depth WHEN 3 THEN SD3.Id WHEN 2 THEN SD2.Id WHEN 1 THEN SD.Id END) AS InstrumentClassId ,
                                    (CASE SD.Depth WHEN 3 THEN SD3.Name WHEN 2 THEN SD2.Name WHEN 1 THEN SD.Name END) AS InstrumentClassName ,
                                    U.RealName AS UserName ,
                                    PD.BaseModifyTime AS ApprovalDate,
                                    SD.Code ,UN.Name AS SchoolName ,UR.UnitId AS CountyId ,UN.Sort ,SD.IsDangerChemical ,
		                            UR2.UnitId AS CityId ,PD.PurchaseType ,
                                    ISNULL(SDM.Code ,SD.Code) AS LastCode
                            FROM  eq_PurchaseDeclaration AS PD
                            INNER JOIN  SysUser AS U ON PD.BaseCreatorId = U.Id
                            INNER JOIN  up_Unit AS UN ON PD.SchoolId = UN.Id
                            INNER JOIN  up_UnitRelation AS UR ON PD.SchoolId = UR.ExtensionObjId AND UR.ExtensionType = {UnitRelationTypeEnum.Unit.ParseToInt()}
                            INNER JOIN  up_UnitRelation AS UR2 ON UR.UnitId = UR2.ExtensionObjId AND UR2.ExtensionType = {UnitRelationTypeEnum.Unit.ParseToInt()}
                            INNER JOIN  eq_InstrumentStandard AS SD ON PD.InstrumentStandardId = SD.Id AND SD.ClassType = 1 AND IsLast = 1
                            INNER JOIN  eq_InstrumentStandard AS SD2 ON SD.Pid = SD2.Id
                            LEFT JOIN  eq_InstrumentStandard AS SD3 ON SD2.Pid = SD3.Id
                            LEFT JOIN  eq_InstrumentStandard AS SDM ON PD.ModelStandardId = SDM.Id AND SDM.ClassType = 2
                            WHERE PD.BaseIsDelete = 0
                          ) as T WHERE  1 = 1 ");
            }
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.SchoolId.HasValue && param.SchoolId > 0)
                {
                    strSql.Append(" AND SchoolId = @SchoolId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
                }
                if (!param.CountyId.IsNullOrZero())
                {
                    strSql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
                if (!param.CityId.IsNullOrZero())
                {
                    strSql.Append(" AND CityId = @CityId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CityId", param.CityId));
                }
                if (param.StartDate.HasValue)
                {
                    strSql.Append(" AND BaseCreateTime >= @StartDate");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@StartDate", param.StartDate));
                }
                if (param.EndDate.HasValue)
                {
                    strSql.Append(" AND BaseCreateTime <= @EndDate");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@EndDate", param.EndDate.Value.AddDays(1).AddSeconds(-1)));
                }
                if (!param.PurchaseYear.IsNullOrZero() && param.PurchaseYear > 0)
                {
                    strSql.Append(" AND PurchaseYear = @PurchaseYear ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@PurchaseYear", param.PurchaseYear));
                }
                if (!param.InstrumentClassId.IsNullOrZero() && param.InstrumentClassId > 0)
                {
                    strSql.Append(" AND InstrumentClassId = @InstrumentClassId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@InstrumentClassId", param.InstrumentClassId));
                }
                if (!param.StageId.IsNullOrZero())
                {
                    strSql.Append(" AND StageId = @StageId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@StageId", param.StageId));
                }
                if(!param.CourseId.IsNullOrZero() && param.CourseId > 0)
                {
                    strSql.Append(" AND CourseId = @CourseId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (param.Statuz.HasValue && param.Statuz > -1)
                {
                    strSql.Append(" AND Statuz = @Statuz");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Statuz", param.Statuz));
                }
                if (!param.UserId.IsNullOrZero())
                {
                    strSql.Append(" AND BaseCreatorId = @UserId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UserId", param.UserId));
                }
                if (!param.UserName.IsEmpty())
                {
                    strSql.Append($" AND UserName LIKE '%{param.UserName.Trim()}%'");
                }
                if (param.AuditStartDate.HasValue)
                {
                    strSql.Append(" AND ApprovalDate >= @AuditStartDate");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@AuditStartDate", param.AuditStartDate));
                }
                if (param.AuditEndDate.HasValue)
                {
                    strSql.Append(" AND ApprovalDate <= @AuditEndDate");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@AuditEndDate", param.AuditEndDate.Value.AddDays(1).AddSeconds(-1)));
                }
                if (!param.PurchaseType.IsNullOrZero())
                {
                    strSql.Append(" AND PurchaseType = @PurchaseType");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@PurchaseType", param.PurchaseType));
                }
                if (!string.IsNullOrWhiteSpace(param.KeyWord))
                {
                    strSql.Append($" AND Name LIKE '%{param.KeyWord.Trim()}%'");
                }
                if(param.IsReport == 1)
                {
                    strSql.Append($" AND Statuz > {InstrumentAuditStatuzEnum.DeclareIng.ParseToInt()}");
                }
                if(param.IsAssociation == 0 && param.AllocateType > 0)
                {
                    strSql.Append($" AND AllocateType = {param.AllocateType}");
                }

                if (param.SetUserId > 0)
                {
                    //判断用户是否设置了学段、学科
                    var listStage = userSubjectSetService.GetUserSetStageList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listStage.Count > 0)
                    {
                        string stageId = string.Join(",", listStage.Select(f => f.DictionaryId));
                        strSql.Append($" AND StageId IN ({stageId})");
                    }

                    var listCourse = userSubjectSetService.GetUserSetCourseList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listCourse.Count > 0)
                    {
                        string courseId = string.Join(",", listCourse.Select(f => f.DictionaryId));
                        strSql.Append($" AND CourseId IN ({courseId})");
                    }

                }
            }
            return parameter;
        }

        #endregion
    }
}
