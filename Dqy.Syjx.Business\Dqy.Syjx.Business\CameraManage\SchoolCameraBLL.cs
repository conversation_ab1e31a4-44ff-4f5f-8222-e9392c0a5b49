﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Model.Input.CameraManage;
using Dqy.Syjx.Entity.CameraManage;
using Dqy.Syjx.Model.Param.CameraManage;
using Dqy.Syjx.Service.CameraManage;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Model.Param;
using Dqy.Syjx.Web.Code;
using Dqy.Syjx.Service.OrganizationManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Entity.OrganizationManage;
using Dqy.Syjx.Entity.BusinessManage;
using Dqy.Syjx.Service.BusinessManage;
using Dqy.Syjx.Model.Param.BusinessManage;
using System.Data.Common;
using System.Text;

namespace Dqy.Syjx.Business.CameraManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2022-01-10 10:02
    /// 描 述：摄像头管理业务类
    /// </summary>
    public class SchoolCameraBLL
    {
        /// <summary>
        /// ApiToken值（仅WebApi端传入）
        /// </summary>
        public string ApiToken = string.Empty;

        private SchoolCameraService schoolCameraService = new SchoolCameraService();
        private UnitService unitService = new UnitService();
        private FunRoomService funroomService = new FunRoomService();
        private HaiKangCameraService ******************** = new HaiKangCameraService();
        #region 获取数据
        public async Task<TData<List<SchoolCameraEntity>>> GetList(SchoolCameraListParam param)
        {
            TData<List<SchoolCameraEntity>> obj = new TData<List<SchoolCameraEntity>>();
            obj.Data = await schoolCameraService.GetList(param);
            obj.Total = obj.Data.Count;
            obj.Tag = 1;
            return obj;
        }

        public async Task<TData<List<SchoolCameraEntity>>> GetPageList(SchoolCameraListParam param, Pagination pagination)
        {
            TData<List<SchoolCameraEntity>> obj = new TData<List<SchoolCameraEntity>>();
            OperatorInfo operatorInfo = await Operator.Instance.Current();
            param.UnitType = operatorInfo.UnitType;
            if (operatorInfo.UnitType == UnitTypeEnum.County.ParseToInt()) 
            {
                param.CountyId = operatorInfo.UnitId.Value; //区县
            }

            obj.Data = await schoolCameraService.GetPageList(param, pagination);
            obj.Total = pagination.TotalCount;
            //if (obj.Data != null && obj.Data.Count > 0)
            //{
            //    var listCounty = await unitService.GetList(new Model.Param.OrganizationManage.UnitListParam() { UnitType = UnitTypeEnum.County.ParseToInt() });
            //    obj.Data.ForEach(m => m.CountyName = GetCountyName(m.CountyId, listCounty));
            //}
            obj.Tag = 1;
            return obj;
        }

        private string GetCountyName(long id ,List<UnitEntity> list)
        {
            string name = "";
            if (list!=null && list.Count > 0)
            {
                var entityTemp = list.Where(m => m.Id == id).FirstOrDefault();
                if (entityTemp!=null)
                {
                    name = entityTemp.Name;
                }
            }
            return name;
        }

        public async Task<TData<SchoolCameraEntity>> GetEntity(long id)
        {
            TData<SchoolCameraEntity> obj = new TData<SchoolCameraEntity>();
            obj.Data = await schoolCameraService.GetEntity(id);
            if (obj.Data != null)
            {
                obj.Tag = 1;
            }
            return obj;
        }

        /// <summary>
        /// 获取学校实验室/专用室列表
        /// </summary>
        /// <returns></returns>
        public async Task<List<FunRoomEntity>> GetSchoolFunRoomList()
        {
            StringBuilder strSql = new StringBuilder();
            Pagination pagin = new Pagination();
            pagin.PageSize = int.MaxValue;
            var list = await funroomService.GetPageList(new FunRoomListParam() { BaseIsDelete = 0}, pagin);
            return list.OrderBy(t => t.UnitId).ThenBy(t => t.SubjectName).ThenBy(t => t.Name).ToList();
        }


        public async Task<TData<List<UnitEntity>>> GetSchoolUnitList()
        {
            TData<List<UnitEntity>> obj = new TData<List<UnitEntity>>();
            obj.Data = await unitService.GetList(new Model.Param.OrganizationManage.UnitListParam() { UnitType = UnitTypeEnum.School.ParseToInt(), Statuz = StatusEnum.Yes.ParseToInt() });
            if (obj.Data != null)
            {
                obj.Tag = 1;
            }
            return obj;
        }

        public async Task<TData<List<FunRoomEntity>>> GetFunRoomList(long id)
        {
            TData<List<FunRoomEntity>> obj = new TData<List<FunRoomEntity>>();
            OperatorInfo operatorInfo = await Operator.Instance.Current();

            var entity = await schoolCameraService.GetEntity(id);
            if (entity == null)
            {
                obj.Tag = 0;
                obj.Message = "该摄像头已不存在，无法关联。";
                return obj;
            }
            if (entity.Statuz != StatusEnum.Yes.ParseToInt())
            {
                obj.Tag = 0;
                obj.Message = "该摄像头已禁用，无法关联。";
                return obj;
            }
            SchoolCameraListParam param = new SchoolCameraListParam();
            param.UnitType = operatorInfo.UnitType;
            param.UnitId = entity.UnitId;
            obj.Data = await funroomService.GetListByCamera(param);
            obj.Total = obj.Data.Count;
            obj.Tag = 1;
            return obj;
        }

        /// <summary>
        /// 验证当前功能室，是否已关联摄像头
        /// </summary>
        /// <param name="funroomid">实验室id</param>
        /// <returns></returns>
        public async Task<TData<int>> GetVerifyForm(long funroomid)
        {
            TData<int> obj = new TData<int>();
            OperatorInfo operatorInfo = await Operator.Instance.Current(ApiToken);
            if (operatorInfo == null)
            {
                obj.Tag = -1;
                obj.Message = "登录信息失效！";
                return obj;
            }
            obj.Data = 0;
            SchoolCameraListParam param = new SchoolCameraListParam();
            param.UnitId = operatorInfo.UnitId.Value;
            param.FunRoomId = funroomid;
            param.Statuz = StatusEnum.Yes.ParseToInt();
            var list = await schoolCameraService.GetList(param);
            if (list != null && list.Count > 0)
            {
                obj.Data = 1;
            }
            obj.Tag = 1;
            return obj;
        }
        #endregion

        #region 提交数据
        public async Task<TData<string>> SetStatuzForm(long id, int statuz)
        {
            TData<string> obj = new TData<string>();
            if (statuz != StatusEnum.Yes.ParseToInt() && statuz != StatusEnum.No.ParseToInt())
            {
                obj.Tag = 0;
                obj.Message = "非法操作。";
                return obj;
            }
            SchoolCameraEntity entity = await schoolCameraService.GetEntity(id);
            if (entity == null)
            {
                obj.Tag = 0;
                obj.Message = "该摄像头信息已不存在。";
                return obj;
            }
            if (statuz != entity.Statuz)
            {
                entity.Statuz = statuz;
                await schoolCameraService.SaveForm(entity);
                obj.Message = "摄像头启用成功";
                if (statuz == StatusEnum.No.ParseToInt())
                {
                    obj.Message = "摄像头禁用成功";
                }
                obj.Tag = 1;
            }
            else
            {
                obj.Tag = 0;
                obj.Message = $"该摄像头已{((StatusEnum)statuz).GetDescription()}";
            }
            return obj;
        }

        public async Task<TData> DeleteForm(string ids)
        {
            TData obj = new TData();
            if (ids.IsEmpty()) { return obj; }
            SchoolCameraListParam param = new SchoolCameraListParam { Ids = ids };
            var list = await schoolCameraService.GetList(param);
            ids = "";
            foreach (var m in list)
            {
                //此处需增加校验是否满足删除条件

                ids += ", " + m.Id.Value;
            }
            obj.Tag = 1;
            if (ids.Length > 1)
                await schoolCameraService.DeleteForm(ids);
            else
                obj.Tag = 0;
            return obj;
        }

        /// <summary>
        /// 导入摄像机信息
        /// </summary>
        /// <param name="param"></param>
        /// <param name="list"></param>
        /// <returns></returns>
        public async Task<TData> ImportJson(ImportParam param, List<SchoolCameraInputModel> list)
        {
            TData obj = new TData();
            try
            {
                OperatorInfo operatorInfo = await Operator.Instance.Current(ApiToken);
                var listed = await schoolCameraService.GetList(new SchoolCameraListParam() { });
                var unitlist = await unitService.GetList(new Model.Param.OrganizationManage.UnitListParam() { UnitType = UnitTypeEnum.School.ParseToInt() });
                if (list != null && list.Count > 0 && list.Any())
                {
                    string errorMsg = "";
                    var unitEntity = new UnitEntity();
                    var schoolName = "";
                    int index = 1;

                    //先判断list数据是否有重复的项
                    var listGroup = list.GroupBy(f => new
                    {
                        f.SrcName
                    })
                    .Select(group => new SchoolCameraGroupModel
                    {
                        SrcName = group.Key.SrcName,
                        TotalCount = group.Count()
                    }).ToList();

                    if (listGroup.Exists(f => f.TotalCount > 1))
                    {
                        var listTip = listGroup.Where(f => f.TotalCount > 1).Select(f => "【摄像机名称：" + f.SrcName + "】").ToList();
                        errorMsg = string.Join("、", listTip);
                        obj.Tag = 0;
                        obj.Message = "导入失败：" + errorMsg+"存在重复。";
                        return obj;
                    }


                    foreach (var item in list)
                    {
                        var rowName = $"第{index}条数据";
                        if (schoolName == "" || schoolName != item.SchoolName)
                        {
                            schoolName = item.SchoolName;
                            unitEntity = unitlist.Where(m => m.Name == item.SchoolName).FirstOrDefault();
                        }
                        if (unitEntity == null || unitEntity.Id <= 0)
                        {
                            errorMsg += $"导入失败，{rowName}单位名称{item.SchoolName}不存在。";
                        }
                        else if(unitEntity != null)
                        {
                            item.UnitId = unitEntity.Id.Value;
                        }
                        if (string.IsNullOrEmpty(item.SrcName.Trim()))
                        {
                            errorMsg += $"导入失败，{rowName}摄像机名称必须填写。";
                        }
                        else
                        {
                          
                            //验证导入的数据摄像机名称是否存在重复。
                            if (listed.Exists(m => m.SrcName == item.SrcName))
                            {
                                errorMsg += $"导入失败，{rowName}摄像机名称{item.SrcName}已存在。<br/>";
                            }
                        }
                        if (string.IsNullOrEmpty(item.Address.Trim()))
                        {
                            errorMsg += $"导入失败:{rowName}摄像机所在实验室地点必须填写。<br/>";
                        }
                        index++;
                    }
                    if (errorMsg != "")
                    {
                        obj.Tag = 0;
                        obj.Message = "导入失败：<br/>"+ errorMsg;
                        return obj;
                    }
                   
                    foreach (SchoolCameraInputModel entity in list)
                    {
                        await ********************.SaveForm(new CameraPointEntity()
                        {
                            CameraIndexCode = entity.CameraIndexCode,
                            CameraName = entity.SrcName,
                            CreateTime = DateTime.Now,
                            Status = 1
                        });

                        await schoolCameraService.SaveForm(new SchoolCameraEntity()
                        {
                            SchoolName = entity.SchoolName,
                            UnitId = entity.UnitId,
                            Address = entity.Address,
                            SrcName = entity.SrcName,
                            SrcIndex = entity.CameraIndexCode,
                            Statuz = StatusEnum.Yes.ParseToInt()
                        });
                    }
                    obj.Tag = 1;
                    
                }
                else
                {
                    obj.Message = " 未找到导入的数据";
                   
                }
            }
            catch (Exception ex)
            {
                obj.Tag = 0;
                obj.Message = "保存异常，请联系客服协助处理。" + ex.Message;
                throw new Exception("程序出现异常，异常信息为：" + ex.Message);
            }
            return obj;
        }

        public async Task<TData<string>> SetRelationForm(SchoolCameraInputModel model)
        {
            TData<string> obj = new TData<string>();
            OperatorInfo operatorInfo = await Operator.Instance.Current();
            SchoolCameraEntity entity = await schoolCameraService.GetEntity(model.Id);
            if (entity == null)
            {
                obj.Tag = 0;
                obj.Message = "该摄像头信息已不存在。";
                return obj;
            }
            var funroomEntity = await funroomService.GetEntity(model.FunRoomId.Value);
            if (funroomEntity != null && funroomEntity.UnitId == entity.UnitId)
            {
                if (entity.FunRoomId > 0)
                {
                    obj.Tag = 0;
                    obj.Message = $"该摄像头已关联功能室，不可重复关联。";
                    return obj;
                }
                //这个这个功能室没有关联其他摄像头。
                var list = await schoolCameraService.GetList(new SchoolCameraListParam() { FunRoomId = model.FunRoomId.Value });
                if (list != null && list.Count > 0)
                {
                    obj.Tag = 0;
                    obj.Message = $"该功能室已关联其他摄像头，禁止再次关联其他摄像头。";
                    return obj;
                }
                entity.FunRoomId = model.FunRoomId;
                entity.OptUserId = operatorInfo.UserId.Value;
                await schoolCameraService.SaveForm(entity);
                obj.Tag = 1;
            }
            else
            {
                obj.Tag = 0;
                obj.Message = $"该功能室不存在,请重新选择。";
            }
            return obj;
        }

        public async Task<TData> CancelRelationForm(string ids)
        {
            TData obj = new TData();
            if (ids.IsEmpty()) { return obj; }
            SchoolCameraListParam param = new SchoolCameraListParam { Ids = ids };
            var list = await schoolCameraService.GetList(param);
            ids = "";
            foreach (var m in list)
            {
                //此处需增加校验是否满足条件
                ids += ", " + m.Id.Value;
            }
            obj.Tag = 1;
            if (ids.Length > 1)
                await schoolCameraService.CancelRelationForm(ids);
            else
                obj.Tag = 0;
            return obj; 
        }
        #endregion

        #region 私有方法
        #endregion
    }
    public class SchoolCameraModel
    {
        public int id { get; set; }
        public int peopleNum { get; set; }
        public string imageUrl { get; set; }
        public string srcName { get; set; }
        public DateTime sendTime { get; set; }
        public int statuz { get; set; }
        public string picPath { get; set; }
    }

    public class SchoolCameraResultModel
    {
        public int total { get; set; }
        public SchoolCameraModel data { get; set; }
        public int tag { get; set; }
        public string message { get; set; }
        public string description { get; set; }
        public int errorCode { get; set; }
    }
}
