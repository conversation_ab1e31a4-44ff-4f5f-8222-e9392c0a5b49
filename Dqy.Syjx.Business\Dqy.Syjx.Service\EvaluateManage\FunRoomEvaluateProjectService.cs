﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.EvaluateManage;
using Dqy.Syjx.Model.Param.EvaluateManage;

namespace Dqy.Syjx.Service.EvaluateManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-11-25 15:16
    /// 描 述：评估项目服务类
    /// </summary>
    public class FunRoomEvaluateProjectService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<FunRoomEvaluateProjectEntity>> GetList(FunRoomEvaluateProjectListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<FunRoomEvaluateProjectEntity>> GetPageList(FunRoomEvaluateProjectListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<FunRoomEvaluateProjectEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<FunRoomEvaluateProjectEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(FunRoomEvaluateProjectEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(FunRoomEvaluateProjectEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
               strSql = $" update bn_FunRoomEvaluateProject set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
               strSql = $"update bn_FunRoomEvaluateProject set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<FunRoomEvaluateProjectEntity, bool>> ListFilter(FunRoomEvaluateProjectListParam param)
        {
            var expression = LinqExtensions.True<FunRoomEvaluateProjectEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (param.IsDefaultEv.HasValue)
                {
                    expression = expression.And(t => t.IsDefaultEv == param.IsDefaultEv);
                }
            }
            return expression;
        }
        #endregion
    }
}
