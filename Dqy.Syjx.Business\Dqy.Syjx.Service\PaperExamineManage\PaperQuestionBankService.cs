﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.PaperExamineManage;
using Dqy.Syjx.Model.Param.PaperExamineManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Enum.BusinessManage;
using Dqy.Syjx.Entity.BusinessManage;

namespace Dqy.Syjx.Service.PaperExamineManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2022-08-04 11:57
    /// 描 述：试卷题库服务类
    /// </summary>
    public class PaperQuestionBankService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<PaperQuestionBankEntity>> GetList(PaperQuestionBankListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<PaperQuestionBankEntity>> GetPageList(PaperQuestionBankListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ListFilter(param, strSql);
            var list = await this.BaseRepository().FindList<PaperQuestionBankEntity>(strSql.ToString(), filter.ToArray(), pagination);
            return list.ToList();
        }

        public async Task<PaperQuestionBankEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<PaperQuestionBankEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(PaperQuestionBankEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(PaperQuestionBankEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task<int> DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
               strSql = $" update cp_PaperQuestionBank set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
               strSql = $"update cp_PaperQuestionBank set BaseIsDelete = 1 where id = {ids}";
            }
            int i = await this.BaseRepository().ExecuteBySql(strSql);
            return i;
        }

        /// <summary>
        /// 根据试卷Id删除试卷题目
        /// </summary>
        /// <param name="paperid"></param>
        /// <returns></returns>
        public async Task<int> DeleteByPaperId(long paperid)
        {
            string strSql = $"update cp_PaperQuestionBank set BaseIsDelete = 1 where PaperId = {paperid}";
            int i = await this.BaseRepository().ExecuteBySql(strSql);
            return i;
        }
        #endregion

        #region 私有方法
        private Expression<Func<PaperQuestionBankEntity, bool>> ListFilter(PaperQuestionBankListParam param)
        {
            var expression = LinqExtensions.True<PaperQuestionBankEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (param.Statuz > 0)
                {
                    expression = expression.And(t => t.Statuz == param.Statuz);
                }
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (param.QuestionBankId > 0)
                {
                    expression = expression.And(t => t.QuestionBankId == param.QuestionBankId);
                }
                if (param.PaperId > 0)
                {
                    expression = expression.And(t => t.PaperId == param.PaperId);
                }
            }
            return expression;
        }

        private List<DbParameter> ListFilter(PaperQuestionBankListParam param, StringBuilder strSql)
        {
            strSql.Append($@"SELECT * From (
                                 SELECT p.Id AS Id ,q.Code AS Code ,q.Title AS Title ,q.QuestionType AS QuestionType ,sd5.DicName AS QuestionTypeName ,q.Classify,sd6.DicName AS ClassifyName
				                      ,q.UseObj ,ISNULL(sd1.DicName,'全部') AS UseObjName ,q.CourseId ,ISNULL(sd2.DicName,'全部') AS CourseName
                                       ,q.SchoolStage ,ISNULL(sd3.DicName,'全部') AS SchoolStageName ,q.GradeId ,ISNULL(sd4.DicName,'全部') AS GradeName ,p.PaperId AS PaperId
                                       ,pe.UnitId AS SchoolId ,u1.Name AS SchoolName ,ur.UnitId AS CountyId ,sa.AreaName AS CountyName ,ur2.UnitId AS CityId ,u3.Name AS CityName
                                  FROM  cp_PaperQuestionBank AS p
			                      INNER JOIN  cp_Paper AS pe ON p.PaperId = pe.Id AND pe.BaseIsDelete = 0 AND pe.Statuz = 1
			                      INNER JOIN cp_QuestionBank AS q ON p.QuestionBankId = q.Id AND q.BaseIsDelete = 0 AND q.Statuz = 1
                                  LEFT JOIN  sys_static_dictionary AS sd1 ON q.UseObj = sd1.DictionaryId AND sd1.BaseIsDelete = 0 AND sd1.TypeCode = '2003'
                                  LEFT JOIN  sys_static_dictionary AS sd2 ON q.CourseId = sd2.DictionaryId AND sd2.BaseIsDelete = 0 AND sd2.TypeCode = '1005'
                                  LEFT JOIN  sys_static_dictionary AS sd3 ON q.SchoolStage = sd3.DictionaryId AND sd3.BaseIsDelete = 0 AND sd3.TypeCode = '1002'
                                  LEFT JOIN  sys_static_dictionary AS sd4 ON q.GradeId = sd4.DictionaryId AND sd4.BaseIsDelete = 0 AND sd4.TypeCode = '1003'
			                      INNER JOIN  sys_static_dictionary AS sd5 ON q.QuestionType = sd5.DictionaryId AND sd5.BaseIsDelete = 0 AND sd5.TypeCode = '2002'
                                  INNER JOIN  sys_static_dictionary AS sd6 ON q.Classify = sd6.DictionaryId AND sd6.BaseIsDelete = 0 AND sd6.TypeCode = '2001'
			                      INNER JOIN  up_Unit AS u1 ON pe.UnitId = u1.Id AND u1.Statuz = 1 AND u1.BaseIsDelete = 0
                                  INNER JOIN  up_UnitRelation AS ur ON ur.BaseIsDelete = 0 AND u1.Id = ur.ExtensionObjId AND ur.ExtensionType = 3
                                  INNER JOIN  up_Unit AS u2 ON UR.UnitId = u2.Id AND u2.BaseIsDelete = 0
                                  INNER JOIN  up_UnitRelation AS ur2 ON u2.Id = ur2.ExtensionObjId AND ur2.ExtensionType = 3 AND ur2.BaseIsDelete = 0
                                  INNER JOIN  up_Unit AS u3 ON ur2.UnitId = u3.Id AND u3.BaseIsDelete = 0
                                  INNER JOIN  SysArea AS sa ON sa.AreaCode = u2.AreaId AND sa.BaseIsDelete = 0
                                  WHERE p.Statuz = 1 AND p.BaseIsDelete = 0
                            )tb1 WHERE 1=1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    strSql.Append(" AND SchoolId = @SchoolId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));

                }
                else if (param.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    strSql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                    if (param.SchoolId > 0)
                    {
                        strSql.Append(" AND SchoolId = @SchoolId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
                    }
                }
                else if (param.UnitType == UnitTypeEnum.City.ParseToInt() || param.UnitType == UnitTypeEnum.System.ParseToInt())
                {
                    strSql.Append(" AND CityId = @CityId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CityId", param.CityId));
                    if (param.CountyId > 0)
                    {
                        strSql.Append(" AND CountyId = @CountyId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                    }
                    if (param.SchoolId > 0)
                    {
                        strSql.Append(" AND SchoolId = @SchoolId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
                    }
                }

                if(param.PaperId > 0)
                {
                    strSql.Append(" AND PaperId = @PaperId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@PaperId", param.PaperId));
                }
                if (param.QuestionType > 0)
                {
                    strSql.Append(" AND QuestionType = @QuestionType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@QuestionType", param.QuestionType));
                }
                if(param.Classify > 0)
                {
                    strSql.Append(" AND Classify = @Classify ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Classify", param.Classify));
                }
                if (param.UseObj > -1)
                {
                    strSql.Append(" AND UseObj = @UseObj ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UseObj", param.UseObj));
                }
                if (param.CourseId > -1)
                {
                    strSql.Append(" AND CourseId = @CourseId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (param.SchoolStage > -1)
                {
                    strSql.Append(" AND SchoolStage = @SchoolStage ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStage", param.SchoolStage));
                }
                if (param.GradeId > -1)
                {
                    strSql.Append(" AND GradeId = @GradeId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@GradeId", param.GradeId));
                }
                if (!string.IsNullOrEmpty(param.Keyword))
                {
                    strSql.Append(" AND ( Title like @Keyword OR  Code like @Keyword ) ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Keyword", $"%{param.Keyword}%"));
                }
            }
            return parameter;
        }

        public async Task<List<AttachmentEntity>> GetTitlePictureList(PaperQuestionBankListParam param)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append($@"SELECT * From (
                                SELECT
								  a3.Id ,a3.BaseIsDelete ,a3.BaseCreateTime ,a3.BaseModifyTime ,a3.BaseCreatorId ,a3.BaseModifierId ,a3.BaseVersion
								  ,a3.ObjectId ,a3.FileCategory ,a3.Title ,a3.Path ,a3.Width ,a3.Height ,a3.Ext
								  ,a3.IsDefault ,a3.Remark ,a3.IsShow ,a3.IsDelete ,a3.IsAudit ,a3.Approver ,a3.AuditDate ,a3.AuditResult
                                  ,pq1.PaperId
								  FROM  cp_PaperQuestionBank AS pq1
								  INNER JOIN  cp_Paper AS p2 ON pq1.PaperId = p2.Id AND p2.BaseIsDelete= 0
								  INNER JOIN  bn_Attachment AS a3 ON pq1.Id = a3.ObjectId AND a3.BaseIsDelete = 0 AND a3.IsDelete = 0
								  WHERE pq1.BaseIsDelete = 0 AND pq1.Statuz = 1
                            ) tb1 WHERE 1=1 ");
            strSql.Append($" AND FileCategory = {FileCategoryEnum.QuestionBankTitle.ParseToInt()} ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.PaperId > 0)
                {
                    strSql.Append(" AND PaperId = @PaperId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@PaperId", param.PaperId));
                }
                if (param.Id > 0)
                {
                    strSql.Append(" AND ObjectId = @ObjectId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ObjectId", param.Id));
                }
            }
            var list = await this.BaseRepository().FindList<AttachmentEntity>(strSql.ToString(), parameter.ToArray());
            return list.ToList();
        }
        #endregion
    }
}
