﻿using Dqy.Syjx.Data;
using Dqy.Syjx.Entity;
using Dqy.Syjx.Entity.BusinessManage;
using Dqy.Syjx.Entity.BusinessThirdManage;
using Dqy.Syjx.Entity.ExperimentTeachManage;
using Dqy.Syjx.Entity.InstrumentManage;
using Dqy.Syjx.Entity.OrganizationManage;
using Dqy.Syjx.Entity.SystemManage;
using Dqy.Syjx.Model.Input.BusinessThirdManage;
using Dqy.Syjx.Model.Param.BusinessThirdManage;
using Dqy.Syjx.Service.BusinessThirdManage;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Newtonsoft.Json;
using Org.BouncyCastle.Crypto;
using System;
using System.Collections.Generic;
using System.Data.Common;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dqy.Syjx.Business.BusinessThirdManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2025-05-22 10:26
    /// 描 述：无锡市签名验签业务类
    /// </summary>
    public class SignWxBLL
    {
        private SignWxService signWxService = new SignWxService();

        #region 获取数据

        public async Task<TData<List<SignWxEntity>>> GetList(SignWxListParam param)
        {
            TData<List<SignWxEntity>> obj = new TData<List<SignWxEntity>>();
            obj.Data = await signWxService.GetList(param);
            obj.Total = obj.Data.Count;
            obj.Tag = 1;
            return obj;
        }

        public async Task<TData<List<SignWxEntity>>> GetPageList(SignWxListParam param, Pagination pagination)
        {
            TData<List<SignWxEntity>> obj = new TData<List<SignWxEntity>>();
            obj.Data = await signWxService.GetPageList(param, pagination);
            obj.Total = pagination.TotalCount;
            obj.Tag = 1;
            return obj;
        }

        public async Task<TData<SignWxEntity>> GetEntity(long id)
        {
            TData<SignWxEntity> obj = new TData<SignWxEntity>();
            obj.Data = await signWxService.GetEntity(id);
            if (obj.Data != null)
            {
                obj.Tag = 1;
            }
            return obj;
        }

        #endregion 获取数据

        #region 提交数据

        public async Task<TData<string>> SaveForm(SignWxInputModel model)
        {
            var entity = new SignWxEntity();
            if (model.Id > 0)
            {
                entity = await signWxService.GetEntity(model.Id);
            }
            entity.Id = model.Id;
            entity.BizType = model.BizType;
            entity.ObjId = model.ObjId;
            entity.InData = model.InData;
            entity.SignValue = model.SignValue;
            TData<string> obj = new TData<string>();
            await signWxService.SaveForm(entity);
            obj.Data = entity.Id.ParseToString();
            obj.Tag = 1;
            return obj;
        }

        public async Task<TData> DeleteForm(string ids)
        {
            TData obj = new TData();
            if (ids.IsEmpty()) { return obj; }
            SignWxListParam param = new SignWxListParam { Ids = ids };
            var list = await signWxService.GetList(param);
            ids = "";
            foreach (var m in list)
            {
                //此处需增加校验是否满足删除条件

                ids += ", " + m.Id.Value;
            }
            obj.Tag = 1;
            if (ids.Length > 1)
                await signWxService.DeleteForm(ids);
            else
                obj.Tag = 0;
            return obj;
        }

        #endregion 提交数据

        public async Task<IEnumerable<LogLoginEntity>> GetUnsignedLoginLog()
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(@"SELECT *
                FROM SysLogLogin
                WHERE NOT EXISTS (
                    SELECT 1
                    FROM Third_SignWx
                    WHERE Third_SignWx.ObjId = SysLogLogin.Id
                ) ;");

            var parameter = new List<DbParameter>();
            var unsignedLoginLogs = await signWxService.BaseRepository().FindList<LogLoginEntity>(strSql.ToString(), parameter.ToArray());

            return unsignedLoginLogs;
        }

        public async Task<IEnumerable<LogOperateEntity>> GetUnsignedOperationLog()
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(@"SELECT *
                FROM SysLogOperate
                WHERE NOT EXISTS (
                    SELECT 1
                    FROM Third_SignWx
                    WHERE Third_SignWx.ObjId = SysLogOperate.Id
                ) ;");

            var parameter = new List<DbParameter>();
            var unsignedOperationLogList = await signWxService.BaseRepository().FindList<LogOperateEntity>(strSql.ToString(), parameter.ToArray());

            return unsignedOperationLogList;
        }

        public async Task<IEnumerable<PurchaseDeclarationEntity>> GetUnsignedInstrumentPurchaseStatistics()
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append($@"SELECT *
                FROM eq_PurchaseDeclaration
                WHERE NOT EXISTS (
                    SELECT 1
                    FROM Third_SignWx
                    WHERE Third_SignWx.ObjId = eq_PurchaseDeclaration.Id
                ) or BaseModifyTime > '{DateTime.Now.ToString("yyyy-MM-dd")}';");

            var parameter = new List<DbParameter>();
            var unsignedPurchaseDeclarationList = await signWxService.BaseRepository().FindList<PurchaseDeclarationEntity>(strSql.ToString(), parameter.ToArray());

            return unsignedPurchaseDeclarationList;
        }

        public async Task<IEnumerable<FunRoomUseEntity>> GetUnsignedQueryStatisticsFunRoomUse()
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append($@"SELECT *
                FROM bn_FunRoomUse
                WHERE NOT EXISTS (
                    SELECT 1
                    FROM Third_SignWx
                    WHERE Third_SignWx.ObjId = bn_FunRoomUse.Id
                ) or BaseModifyTime > '{DateTime.Now.ToString("yyyy-MM-dd")}';");

            var parameter = new List<DbParameter>();
            var unsignedFunRoomUseList = await signWxService.BaseRepository().FindList<FunRoomUseEntity>(strSql.ToString(), parameter.ToArray());

            return unsignedFunRoomUseList;
        }

        public async Task<IEnumerable<UserEntity>> GetUnsignedUserInfo()
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append($@"SELECT *
                FROM SysUser
                WHERE NOT EXISTS (
                    SELECT 1
                    FROM Third_SignWx
                    WHERE Third_SignWx.ObjId = SysUser.Id
                ) or BaseModifyTime > '{DateTime.Now.ToString("yyyy-MM-dd")}';");

            var parameter = new List<DbParameter>();
            var unsignedUserList = await signWxService.BaseRepository().FindList<UserEntity>(strSql.ToString(), parameter.ToArray());

            return unsignedUserList;
        }

        public async Task<IEnumerable<MenuEntity>> GetUnsignedMenu()
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(@$"SELECT *
                FROM SysMenu
                WHERE NOT EXISTS (
                    SELECT 1
                    FROM Third_SignWx
                    WHERE Third_SignWx.ObjId = SysMenu.Id
                ) or BaseModifyTime > '{DateTime.Now.ToString("yyyy-MM-dd")}';");

            var parameter = new List<DbParameter>();
            var unsignedMenuList = await signWxService.BaseRepository().FindList<MenuEntity>(strSql.ToString(), parameter.ToArray());

            return unsignedMenuList;
        }

        public async Task<IEnumerable<ExperimentBookingEntity>> GetUnsignedQueryStatisticsExperimentBooking()
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append($@"SELECT *
                FROM ex_ExperimentBooking
                WHERE NOT EXISTS (
                    SELECT 1
                    FROM Third_SignWx
                    WHERE Third_SignWx.ObjId = ex_ExperimentBooking.Id
                ) or BaseModifyTime > '{DateTime.Now.ToString("yyyy-MM-dd")}';");

            var parameter = new List<DbParameter>();
            var unsignedExperimentBookingList = await signWxService.BaseRepository().FindList<ExperimentBookingEntity>(strSql.ToString(), parameter.ToArray());

            return unsignedExperimentBookingList;
        }

        public async Task<SignWxEntity> GetEntityByObjId(long objId, long bizType)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(@"SELECT *
                FROM Third_SignWx
                WHERE 1 = 1");

            var parameter = new List<DbParameter>();

            strSql.Append(" AND ObjId = @ObjId ");
            parameter.Add(DbParameterExtension.CreateDbParameter("@ObjId", objId));
            strSql.Append(" AND BizType = @BizType ");
            parameter.Add(DbParameterExtension.CreateDbParameter("@BizType", bizType));
            var signWxList = await signWxService.BaseRepository().FindList<SignWxEntity>(strSql.ToString(), parameter.ToArray());

            return signWxList.FirstOrDefault();
        }
    }
}