﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.ExperimentTeachManage;
using Dqy.Syjx.Model.Param.ExperimentTeachManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Enum.ExperimentTeachManage;

namespace Dqy.Syjx.Service.ExperimentTeachManage
{
    /// <summary>
    /// 创 建：陶瑞
    /// 日 期：2024-03-07 17:14
    /// 描 述：设置考核实验服务类
    /// </summary>
    public class PlanExamParameterService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<PlanExamParameterEntity>> GetList(PlanExamListParameterListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<PlanExamParameterEntity>> GetPageList(PlanExamListParameterListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<PlanExamParameterEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<PlanExamParameterEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(PlanExamParameterEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(PlanExamParameterEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update ex_PlanExamParameter set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
                strSql = $"update ex_PlanExamParameter set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }

        public async Task UpdateNum(long id, long unitid)
        {
            string strSql = $@" UPDATE  ex_PlanExamParameter	
                    SET Num = (SELECT COUNT(pd1.Id) FROM  ex_PlanExamExperiment AS pd1 WHERE pd1.PlanParameterSetId = {id} AND  pd1.BaseIsDelete = 0) ,
		   	    NeedShowNum = (SELECT COUNT(pd1.Id) FROM  ex_PlanExamExperiment AS pd1 WHERE pd1.PlanParameterSetId = {id} AND pd1.IsNeedDo = {IsStatusEnum.Yes.ParseToInt()} AND pd1.ExperimentType = {ExperimentTypeEnum.Demo.ParseToInt()}  AND pd1.BaseIsDelete = 0) ,
               NeedGroupNum = (SELECT COUNT(pd1.Id) FROM  ex_PlanExamExperiment AS pd1 WHERE pd1.PlanParameterSetId = {id} AND pd1.IsNeedDo = {IsStatusEnum.Yes.ParseToInt()} AND pd1.ExperimentType = {ExperimentTypeEnum.Group.ParseToInt()} AND pd1.BaseIsDelete = 0) ,
            OptionalShowNum = (SELECT COUNT(pd1.Id) FROM  ex_PlanExamExperiment AS pd1 WHERE pd1.PlanParameterSetId = {id} AND pd1.IsNeedDo = {IsStatusEnum.No.ParseToInt()}  AND pd1.ExperimentType = {ExperimentTypeEnum.Demo.ParseToInt()}  AND pd1.BaseIsDelete = 0) ,
           OptionalGroupNum = (SELECT COUNT(pd1.Id) FROM  ex_PlanExamExperiment AS pd1 WHERE pd1.PlanParameterSetId = {id} AND pd1.IsNeedDo = {IsStatusEnum.No.ParseToInt()}  AND pd1.ExperimentType = {ExperimentTypeEnum.Group.ParseToInt()} AND pd1.BaseIsDelete = 0)
            WHERE Id = {id} AND UnitId = {unitid} ";
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<PlanExamParameterEntity, bool>> ListFilter(PlanExamListParameterListParam param)
        {
            var expression = LinqExtensions.True<PlanExamParameterEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (param.UnitId > 0)
                {
                    expression = expression.And(t => t.UnitId == param.UnitId);
                }
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (param.SchoolYearStart > 0)
                {
                    expression = expression.And(t => t.SchoolYearStart == param.SchoolYearStart);
                }
                if (param.SchoolTerm > 0)
                {
                    expression = expression.And(t => t.SchoolTerm == param.SchoolTerm);
                }
                if (param.SchoolStage > 0)
                {
                    expression = expression.And(t => t.SchoolStage == param.SchoolStage);
                }
                if (param.GradeId > 0)
                {
                    expression = expression.And(t => t.GradeId == param.GradeId);
                }
                if (param.CourseId > 0)
                {
                    expression = expression.And(t => t.CourseId == param.CourseId);
                }
                if (param.CompulsoryType > 0)
                {
                    expression = expression.And(t => t.CompulsoryType == param.CompulsoryType);
                }
                if (param.SchoolStageList != null && param.SchoolStageList.Count > 0)
                {
                    expression = expression.And(t => param.SchoolStageList.Contains(t.SchoolStage));
                }
                if (param.CourseIdList != null && param.CourseIdList.Count > 0)
                {
                    expression = expression.And(t => param.CourseIdList.Contains(t.CourseId));
                }
            }
            return expression;
        }


        #endregion

        #region sql查询方法

        public async Task<List<TextbookVersionDetailEntity>> GetAddExperimentPageList(PlanExamExperimentListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append($@"  SELECT * From (
                SELECT 
				vd2.Id ,
				vd2.BaseIsDelete ,
				vd2.BaseCreateTime ,
				vd2.BaseModifyTime ,
				vd2.BaseCreatorId ,
				vd2.BaseModifierId ,
				vd2.BaseVersion ,
				vd2.TextbookVersionBaseId ,
				vd2.Chapter ,
				vd2.ExperimentCode ,
				vd2.ExperimentName ,
				vd2.ExperimentType ,
				vd2.IsNeedDo ,
				vd2.IsEvaluate ,
				vd2.IsBase ,
				vd2.EquipmentNeed ,
				vd2.MaterialNeed ,
				vd2.Remark ,
				vd2.Statuz ,
                vd2.ChapterSort,
				vb1.VersionName ,
                CASE WHEN ISNULL(vb1.CompulsoryType ,0) = {TextbookCompulsoryTypeEnum.Must.ParseToInt()} Then {TextbookCompulsoryTypeEnum.Must.ParseToInt()} Else {TextbookCompulsoryTypeEnum.NonSelectMust.ParseToInt()} end CompulsoryType,
				vc3.SchoolStage ,
				vc3.GradeId ,
				vc3.CourseId ,
				vc3.SchoolTerm ,
                vc3.Id AS TextbookVersionCurrentId ,
				CASE WHEN ISNULL(pd4.PlanParameterSetId,0) > 0 THEN 1 ELSE 0 END AS IsSelected  
		        FROM  ex_TextbookVersionDetail AS vd2
		        INNER JOIN ex_TextbookVersionBase AS vb1 ON vd2.TextbookVersionBaseId = vb1.Id AND vb1.BaseIsDelete = 0 AND vb1.Statuz = {StatusEnum.Yes.ParseToInt()}
				INNER JOIN ex_TextbookVersionCurrent AS vc3 ON vb1.Id = vc3.TextbookVersionBaseId AND vc3.BaseIsDelete = 0 AND vc3.Statuz = {StatusEnum.Yes.ParseToInt()}
				LEFT JOIN ex_PlanExamExperiment AS pd4 ON pd4.PlanParameterSetId = {param.PlanParameterSetId} AND pd4.BaseIsDelete = 0 AND pd4.ExperimentId = vd2.Id
                WHERE vd2.Statuz = {StatusEnum.Yes.ParseToInt()} AND vc3.CountyIdz like '%{param.UnitId}%' 
            ) as tb1 WHERE BaseIsDelete = 0 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.IsNeedDo > 0)
                {
                    strSql.Append(" AND IsNeedDo = @IsNeedDo ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@IsNeedDo", param.IsNeedDo));
                }
                if (param.IsEvaluate == IsStatusEnum.Yes.ParseToInt() || param.IsEvaluate == IsStatusEnum.No.ParseToInt())
                {
                    if (param.IsEvaluate == IsStatusEnum.Yes.ParseToInt())
                    {
                        strSql.Append($" AND IsEvaluate = {IsStatusEnum.Yes.ParseToInt()} ");
                    }
                    else
                    {
                        strSql.Append($" AND IsEvaluate <> {IsStatusEnum.Yes.ParseToInt()} ");
                    }
                }
                if (param.ExperimentType > 0)
                {
                    strSql.Append(" AND ExperimentType = @ExperimentType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ExperimentType", param.ExperimentType));
                }
                if (!string.IsNullOrEmpty(param.Name))
                {
                    strSql.Append(" AND (ExperimentName like @Name OR Chapter like @Name ) ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Name", $"%{param.Name}%"));
                }
                if (param.Ids != null && param.Ids.Length > 0)
                {
                    strSql.Append($" AND Id IN ({param.Ids})");
                }
                /*下面是必要条件。*/
                if (param.SchoolStage > 0)
                {
                    strSql.Append(" AND SchoolStage = @SchoolStage ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStage", param.SchoolStage));
                }
                if (param.GradeId > 0)
                {
                    strSql.Append(" AND GradeId = @GradeId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@GradeId", param.GradeId));
                }
                if (param.CourseId > 0)
                {
                    strSql.Append(" AND CourseId = @CourseId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (param.SchoolTerm > 0)
                {
                    strSql.Append(" AND SchoolTerm = @SchoolTerm ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolTerm", param.SchoolTerm));
                }
                if (param.CompulsoryType > 0)
                {
                    strSql.Append(" AND CompulsoryType = @CompulsoryType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CompulsoryType", param.CompulsoryType));
                }
                if (param.VersionId > 0)
                {
                    strSql.Append(" AND TextbookVersionCurrentId = @TextbookVersionCurrentId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@TextbookVersionCurrentId", param.VersionId));
                }
            }

            var list = await this.BaseRepository().FindList<TextbookVersionDetailEntity>(strSql.ToString(), parameter.ToArray(), pagination);
            return list.ToList();
        }


        /// <summary>
        /// 获取考核列表
        /// </summary>
        /// <param name="param">查询参数</param>
        /// <param name="pagination">分页参数</param>
        /// <returns></returns>
        public async Task<IEnumerable<PlanExamParameterEntity>> GetPlanExamRatePageList(PlanExamListParameterListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            var parameter = new List<DbParameter>();
            strSql.Append($@" SELECT * FROM (
                SELECT pp1.* 
                ,schoolstage.DicName AS SchoolStageName ,course.DicName AS CourseName ,grade.DicName AS GradeName
                ,u3.Id AS SchoolId , u3.Name AS SchoolName ,u3.Sort 
                ,se11.SchoolProp
                FROM ex_PlanExamParameter AS pp1
                INNER JOIN up_UnitRelation AS ur2 ON ur2.BaseIsDelete = 0 AND pp1.UnitId = ur2.UnitId AND ur2.ExtensionType = 3
                INNER JOIN up_Unit AS u3 ON u3.BaseIsDelete = 0 AND u3.Id = ur2.ExtensionObjId
                INNER JOIN up_SchoolExtension AS se11 ON se11.BaseIsDelete = 0 AND se11.UnitId = u3.Id
				INNER JOIN sys_dictionary_relation AS dicr12 ON dicr12.BaseIsDelete = 0 
                                                        AND dicr12.DictionaryId = se11.SchoolProp 
                                                        AND dicr12.DictionaryToId = pp1.SchoolStage 
                                                        AND dicr12.RelationType = 1                
                LEFT JOIN  sys_static_dictionary AS schoolstage ON schoolstage.BaseIsDelete = 0 AND pp1.SchoolStage = schoolstage.DictionaryId
                LEFT JOIN  sys_static_dictionary AS course ON course.BaseIsDelete = 0 AND pp1.CourseId = course.DictionaryId
                LEFT JOIN  sys_static_dictionary AS grade ON grade.BaseIsDelete = 0 AND pp1.GradeId = grade.DictionaryId 
            ) AS tab01 Where BaseIsDelete = 0 ");
            if (param != null)
            {
                if (param.Id > 0)
                {
                    strSql.Append(" AND Id = @Id ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Id", param.Id));
                }
                if (param.UnitId > 0)
                {
                    strSql.Append(" AND UnitId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                if (param.SchoolId > 0)
                {
                    strSql.Append(" AND SchoolId = @SchoolId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
                }
                if (param.SchoolStageList != null && param.SchoolStageList.Count > 0)
                {
                    strSql.AppendFormat(" AND ({0}) ", string.Join(" OR ", param.SchoolStageList.Select(m => string.Format("  SchoolStage = {0} ", m))));
                }
                if (param.SchoolPropList != null && param.SchoolPropList.Count > 0)
                {
                    strSql.AppendFormat(" AND ({0}) ", string.Join(" OR ", param.SchoolPropList.Select(m => string.Format("  SchoolProp = {0} ", m))));
                }
                if (param.SchoolYearStart > 0)
                {
                    strSql.Append(" AND SchoolYearStart = @SchoolYearStart ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolYearStart", param.SchoolYearStart));
                }
                if (param.SchoolTerm > 0)
                {
                    strSql.Append(" AND SchoolTerm = @SchoolTerm ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolTerm", param.SchoolTerm));
                }
                if (param.SchoolStage > 0)
                {
                    strSql.Append(" AND SchoolStage = @SchoolStage ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStage", param.SchoolStage));
                }
                if (param.GradeId > 0)
                {
                    strSql.Append(" AND GradeId = @GradeId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@GradeId", param.GradeId));
                }
                if (param.CourseId > 0)
                {
                    strSql.Append(" AND CourseId = @CourseId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (param.CourseIdList != null && param.CourseIdList.Count > 0)
                {
                    strSql.AppendFormat(" AND ({0}) ", string.Join(" OR ", param.CourseIdList.Select(m => string.Format(" CourseId = {0} ", m))));
                }
                if (param.CompulsoryType > 0)
                {
                    strSql.Append(" AND CompulsoryType = @CompulsoryType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CompulsoryType", param.CompulsoryType));
                }
            }
            if (pagination == null)
            {
                return await this.BaseRepository().FindList<PlanExamParameterEntity>(strSql.ToString(), parameter.ToArray());
            }
            else
            {
                return await this.BaseRepository().FindList<PlanExamParameterEntity>(strSql.ToString(), parameter.ToArray(), pagination);
            }

        }

        /// <summary>
        /// 编制的考核实验列表
        /// </summary>
        /// <param name="param">查询参数</param>
        /// <param name="pagination">分页参数</param>
        /// <returns>考核列表</returns>
        public async Task<IEnumerable<PlanExamParameterEntity>> GetPlanExamDetailList(PlanExamListParameterListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            var parameter = new List<DbParameter>();
            strSql.Append($@" SELECT * FROM (
                SELECT pp1.* ,u3.Id AS SchoolId , u3.Name AS SchoolName ,u3.Sort
                ,pe2.ExperimentId 
                FROM ex_PlanExamParameter AS pp1
                INNER JOIN up_UnitRelation AS ur2 ON ur2.BaseIsDelete = 0 AND pp1.UnitId = ur2.UnitId AND ur2.ExtensionType = 3
                INNER JOIN up_Unit AS u3 ON u3.BaseIsDelete = 0 AND u3.Id = ur2.ExtensionObjId
                INNER JOIN up_SchoolExtension AS se11 ON se11.BaseIsDelete = 0 AND se11.UnitId = u3.Id
				INNER JOIN sys_dictionary_relation AS dicr12 ON dicr12.BaseIsDelete = 0 
                                                        AND dicr12.DictionaryId = se11.SchoolProp 
                                                        AND dicr12.DictionaryToId = pp1.SchoolStage 
                                                        AND dicr12.RelationType = 1 
                INNER JOIN ex_PlanExamExperiment AS pe2 ON pe2.BaseIsDelete = 0 AND pp1.Id = pe2.PlanParameterSetId
                INNER JOIN ex_PlanDetail AS pd3 ON pd3.BaseIsDelete = 0 AND pd3.WeekNum > 0 AND pe2.ExperimentId = pd3.ExperimentId
                INNER JOIN ex_PlanInfo AS pi4 ON pi4.BaseIsDelete = 0 AND pd3.PlanInfoId = pi4.Id  
                AND  u3.Id = pi4.SchoolId
                AND pi4.SchoolYearStart = pp1.SchoolYearStart AND pi4.SchoolTerm = pp1.SchoolTerm
                AND pi4.CourseId = pp1.CourseId
                AND pi4.GradeId = pp1.GradeId --编制考核室9年级，编制计划8年级问题      
                AND ISNULL(pi4.CompulsoryType,0) = ISNULL( pp1.CompulsoryType,0)
             ) AS tab01 Where BaseIsDelete = 0 ");
            if (param != null)
            {
                if (param.UnitId > 0)
                {
                    strSql.Append(" AND UnitId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                if (param.SchoolId > 0)
                {
                    strSql.Append(" AND SchoolId = @SchoolId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
                }
                if (param.SchoolYearStart > 0)
                {
                    strSql.Append(" AND SchoolYearStart = @SchoolYearStart ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolYearStart", param.SchoolYearStart));
                }
                if (param.SchoolTerm > 0)
                {
                    strSql.Append(" AND SchoolTerm = @SchoolTerm ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolTerm", param.SchoolTerm));
                }
                if (param.GradeId > 0)
                {
                    strSql.Append(" AND GradeId = @GradeId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@GradeId", param.GradeId));
                }
                if (param.CourseId > 0)
                {
                    strSql.Append(" AND CourseId = @CourseId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (param.CourseIdList != null && param.CourseIdList.Count > 0)
                {
                    strSql.AppendFormat(" AND ({0}) ", string.Join(" OR ", param.CourseIdList.Select(m => string.Format(" CourseId = {0} ", m))));
                }
                if (param.SchoolStageList != null && param.SchoolStageList.Count > 0)
                {
                    strSql.AppendFormat(" AND ({0}) ", string.Join(" OR ", param.SchoolStageList.Select(m => string.Format("  SchoolStage = {0} ", m))));
                }
                if (param.CompulsoryType > 0)
                {
                    strSql.Append(" AND CompulsoryType = @CompulsoryType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CompulsoryType", param.CompulsoryType));
                }
            }
            if (pagination == null)
            {
                return await this.BaseRepository().FindList<PlanExamParameterEntity>(strSql.ToString(), parameter.ToArray());
            }
            else
            {
                return await this.BaseRepository().FindList<PlanExamParameterEntity>(strSql.ToString(), parameter.ToArray(), pagination);
            }
 
        }



        /// <summary>
        /// 考核实验已编制计划的列表
        /// </summary>
        /// <param name="param">查询参数</param>
        /// <param name="pagination">分页参数</param>
        /// <returns>编制计划实验列表</returns>
        public async Task<IEnumerable<PlanDetailEntity>> GetPlanExamedList(PlanExamExperimentListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            var parameter = new List<DbParameter>();
            strSql.Append($@" SELECT * FROM (
                SELECT pd3.* 
                ,pe2.Chapter ,pe2.Remark
                ,vb5.VersionName AS VersionBaseName
                ,u3.Id AS SchoolId , u3.Name AS SchoolName ,u3.Sort
                ,pp1.UnitId ,pp1.Id AS PlanParameterSetId 
                FROM ex_PlanExamParameter AS pp1
                INNER JOIN up_UnitRelation AS ur2 ON ur2.BaseIsDelete = 0 AND pp1.UnitId = ur2.UnitId AND ur2.ExtensionType = 3
                INNER JOIN up_Unit AS u3 ON u3.BaseIsDelete = 0 AND u3.Id = ur2.ExtensionObjId
                INNER JOIN up_SchoolExtension AS se11 ON se11.BaseIsDelete = 0 AND se11.UnitId = u3.Id
				INNER JOIN sys_dictionary_relation AS dicr12 ON dicr12.BaseIsDelete = 0 
                                                        AND dicr12.DictionaryId = se11.SchoolProp 
                                                        AND dicr12.DictionaryToId = pp1.SchoolStage 
                                                        AND dicr12.RelationType = 1                
                INNER JOIN ex_PlanExamExperiment AS pe2 ON pe2.BaseIsDelete = 0 AND pp1.Id = pe2.PlanParameterSetId
                INNER JOIN ex_PlanDetail AS pd3 ON pd3.BaseIsDelete = 0  AND pd3.WeekNum > 0 AND pe2.ExperimentId = pd3.ExperimentId
                INNER JOIN ex_PlanInfo AS pi4 ON pi4.BaseIsDelete = 0 AND pd3.PlanInfoId = pi4.Id  
                AND  u3.Id = pi4.SchoolId
                AND pi4.SchoolYearStart = pp1.SchoolYearStart AND pi4.SchoolTerm = pp1.SchoolTerm
                AND pi4.CourseId = pp1.CourseId
                AND pi4.GradeId = pp1.GradeId --编制考核室9年级，编制计划8年级问题
                LEFT JOIN ex_TextbookVersionBase AS vb5 ON vb5.BaseIsDelete = 0 AND pd3.TextbookVersionBaseId = vb5.Id   
             ) AS tab01 Where BaseIsDelete = 0 ");
            if (param != null)
            {
                if (param.UnitId > 0)
                {
                    strSql.Append(" AND UnitId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                if (param.SchoolId > 0)
                {
                    strSql.Append(" AND SchoolId = @SchoolId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
                }
                if (param.PlanParameterSetId > 0)
                {
                    strSql.Append(" AND PlanParameterSetId = @PlanParameterSetId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@PlanParameterSetId", param.PlanParameterSetId));
                }
                if (param.VersionId > 0)
                {
                    strSql.Append(" AND TextbookVersionBaseId = @TextbookVersionBaseId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@TextbookVersionBaseId", param.VersionId));
                }
                if (param.ExperimentType > 0)
                {
                    strSql.Append(" AND ExperimentType = @ExperimentType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ExperimentType", param.ExperimentType));
                }
                if (param.IsNeedDo > 0)
                {
                    strSql.Append(" AND IsNeedDo = @IsNeedDo ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@IsNeedDo", param.IsNeedDo));
                }
                if (!string.IsNullOrEmpty(param.Name))
                {
                    strSql.Append(" AND (ExperimentName like @Name OR Chapter like @Name ) ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Name", $"%{param.Name}%"));
                }
            }
            return await this.BaseRepository().FindList<PlanDetailEntity>(strSql.ToString(), parameter.ToArray(), pagination);
        }

        /// <summary>
        /// 已登记的编制考核实验列表（关联登记表）
        /// </summary>
        /// <param name="param">查询参数</param>
        /// <param name="pagination">分页参数</param>
        /// <returns>考核列表</returns>
        public async Task<IEnumerable<PlanExamParameterEntity>> GetPlanExamExpRecordList(PlanExamListParameterListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            var parameter = new List<DbParameter>();
            strSql.Append($@" SELECT * FROM (
                SELECT pp1.* ,u3.Id AS SchoolId , u3.Name AS SchoolName ,u3.Sort
                ,pe2.ExperimentType ,pe2.IsNeedDo ,pe2.IsEvaluate
                ,pe2.ExperimentId 
                ,booking.SchoolGradeClassId
                ,su.Id AS TeacherId
                ,su.RealName
                FROM ex_PlanExamParameter AS pp1
                INNER JOIN up_UnitRelation AS ur2 ON ur2.BaseIsDelete = 0 AND pp1.UnitId = ur2.UnitId AND ur2.ExtensionType = 3
                INNER JOIN up_Unit AS u3 ON u3.BaseIsDelete = 0 AND u3.Id = ur2.ExtensionObjId
                INNER JOIN up_SchoolExtension AS se11 ON se11.BaseIsDelete = 0 AND se11.UnitId = u3.Id
				INNER JOIN sys_dictionary_relation AS dicr12 ON dicr12.BaseIsDelete = 0 
                                                        AND dicr12.DictionaryId = se11.SchoolProp 
                                                        AND dicr12.DictionaryToId = pp1.SchoolStage 
                                                        AND dicr12.RelationType = 1
                INNER JOIN ex_PlanExamExperiment AS pe2 ON pe2.BaseIsDelete = 0 AND pp1.Id = pe2.PlanParameterSetId
				INNER JOIN ex_ExperimentBooking AS booking ON booking.BaseIsDelete = 0 
				AND booking.Statuz = 100 AND booking.IsMain = 0
				AND booking.ExperimentId = pe2.ExperimentId
				AND booking.SchoolId =  u3.Id
				AND booking.CourseId = pp1.CourseId
				AND booking.SchoolYearStart = pp1.SchoolYearStart
				AND booking.SchoolTerm = pp1.SchoolTerm 
				AND booking.GradeId = pp1.GradeId --编制考核室9年级，编制计划8年级问题 
                LEFT JOIN SysUser AS su ON booking.BaseCreatorId = su.Id
             ) AS tab01 Where BaseIsDelete = 0 ");
            if (param != null)
            {
                if (param.Id > 0)
                {
                    strSql.Append(" AND Id = @Id ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Id", param.Id));
                }
                if (param.UnitId > 0)
                {
                    strSql.Append(" AND UnitId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                if (param.SchoolId > 0)
                {
                    strSql.Append(" AND SchoolId = @SchoolId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
                }
                if (param.SchoolYearStart > 0)
                {
                    strSql.Append(" AND SchoolYearStart = @SchoolYearStart ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolYearStart", param.SchoolYearStart));
                }
                if (param.SchoolTerm > 0)
                {
                    strSql.Append(" AND SchoolTerm = @SchoolTerm ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolTerm", param.SchoolTerm));
                }
                if (param.GradeId > 0)
                {
                    strSql.Append(" AND GradeId = @GradeId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@GradeId", param.GradeId));
                }
                if (param.CourseId > 0)
                {
                    strSql.Append(" AND CourseId = @CourseId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }

                if (param.CompulsoryType > 0)
                {
                    strSql.Append(" AND CompulsoryType = @CompulsoryType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CompulsoryType", param.CompulsoryType));
                }
                if (param.SchoolStageList != null && param.SchoolStageList.Count > 0)
                {
                    strSql.Append($" AND ({string.Join(" OR ", param.SchoolStageList.Select(f => string.Format(" SchoolStage = {0}", f)))}) ");
                }
                if (param.CourseIdList!=null && param.CourseIdList.Count > 0)
                {
                    strSql.Append($" AND ({string.Join(" OR ", param.CourseIdList.Select(f => string.Format(" CourseId = {0}", f)))}) ");
                }
                if (!string.IsNullOrEmpty(param.TeacherName))
                {
                    strSql.Append($" AND RealName like '%{param.TeacherName}%' "); //任课老师
                }
            }
            if (pagination==null)
            {
                return await this.BaseRepository().FindList<PlanExamParameterEntity>(strSql.ToString(), parameter.ToArray());
            }
            else
            {
                return await this.BaseRepository().FindList<PlanExamParameterEntity>(strSql.ToString(), parameter.ToArray(), pagination);
            }
    
        }

        #region 考核实验 (拆分方法，预约方法)

        /// <summary>
        /// 已登记的编制考核实验列表（关联登记表）
        /// </summary>
        /// <param name="param">查询参数</param>
        /// <param name="pagination">分页参数</param>
        /// <returns>考核列表</returns>
        public async Task<IEnumerable<PlanExamParameterEntity>> GetPlanExamExpList(PlanExamListParameterListParam param)
        {
            StringBuilder strSql = new StringBuilder();
            var parameter = new List<DbParameter>();
            strSql.Append($@" SELECT pp1.Id ,
                pp1.UnitId ,
                pp1.SchoolStage ,
                pp1.GradeId ,
                pp1.CourseId ,
                pp1.SchoolYearStart ,
                pp1.SchoolYearEnd ,
                pp1.SchoolTerm ,
                pp1.Num ,
                pp1.NeedShowNum ,
                pp1.NeedGroupNum ,
                pp1.OptionalShowNum ,
                pp1.OptionalGroupNum ,
                pp1.CompulsoryType ,u3.Id AS SchoolId , u3.Name AS SchoolName ,u3.Sort
                ,pe2.ExperimentType ,pe2.IsNeedDo ,pe2.IsEvaluate
                ,pe2.ExperimentId 
                ,booking.SchoolGradeClassId
                FROM ex_PlanExamParameter AS pp1
                INNER JOIN up_UnitRelation AS ur2 ON ur2.BaseIsDelete = 0 AND pp1.UnitId = ur2.UnitId AND ur2.ExtensionType = 3
                INNER JOIN up_Unit AS u3 ON u3.BaseIsDelete = 0 AND u3.Id = ur2.ExtensionObjId
                INNER JOIN up_SchoolExtension AS se11 ON se11.BaseIsDelete = 0 AND se11.UnitId = u3.Id
                INNER JOIN ex_PlanExamExperiment AS pe2 ON pe2.BaseIsDelete = 0 AND pp1.Id = pe2.PlanParameterSetId
				INNER JOIN ex_ExperimentBooking AS booking ON booking.BaseIsDelete = 0 
				AND booking.Statuz = 100 AND booking.IsMain = 0
				AND booking.ExperimentId = pe2.ExperimentId
				AND booking.SchoolId =  u3.Id
				AND booking.CourseId = pp1.CourseId
				AND booking.SchoolYearStart = pp1.SchoolYearStart
				AND booking.SchoolTerm = pp1.SchoolTerm 
				AND booking.GradeId = pp1.GradeId --编制考核室9年级，编制计划8年级问题 
              Where pp1.BaseIsDelete = 0 ");
            if (param != null)
            {
                if (param.Id > 0)
                {
                    strSql.Append(" AND pp1.Id = @Id ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Id", param.Id));
                }
                if (param.UnitId > 0)
                {
                    strSql.Append(" AND pp1.UnitId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                if (param.SchoolId > 0)
                {
                    strSql.Append(" AND u3.Id = @SchoolId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
                }
                if (param.SchoolYearStart > 0)
                {
                    strSql.Append(" AND pp1.SchoolYearStart = @SchoolYearStart ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolYearStart", param.SchoolYearStart));
                }
                if (param.SchoolTerm > 0)
                {
                    strSql.Append(" AND pp1.SchoolTerm = @SchoolTerm ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolTerm", param.SchoolTerm));
                }
                if (param.GradeId > 0)
                {
                    strSql.Append(" AND pp1.GradeId = @GradeId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@GradeId", param.GradeId));
                }
                if (param.CourseId > 0)
                {
                    strSql.Append(" AND pp1.CourseId = @CourseId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (param.SchoolStage > 0)
                {
                    strSql.Append(" AND pp1.SchoolStage = @SchoolStage ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStage", param.SchoolStage));
                }
                if (param.CompulsoryType > 0)
                {
                    strSql.Append(" AND pp1.CompulsoryType = @CompulsoryType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CompulsoryType", param.CompulsoryType));
                }
                if (param.SchoolStageList != null && param.SchoolStageList.Count > 0)
                {
                    strSql.Append($" AND ({string.Join(" OR ", param.SchoolStageList.Select(f => string.Format(" pp1.SchoolStage = {0}", f)))}) ");
                }
                if (param.CourseIdList != null && param.CourseIdList.Count > 0)
                {
                    strSql.Append($" AND ({string.Join(" OR ", param.CourseIdList.Select(f => string.Format(" pp1.CourseId = {0}", f)))}) ");
                }
            }
            return await this.BaseRepository().FindList<PlanExamParameterEntity>(strSql.ToString(), parameter.ToArray());
        }

        #endregion

        /// <summary>
        /// 获取考核班级列表(学校)
        /// </summary>
        /// <param name="param">查询参数</param>
        /// <param name="pagination">分页参数</param>
        /// <returns></returns>
        public async Task<IEnumerable<PlanExamParameterEntity>> GetPlanExamClassRatePageList(PlanExamListParameterListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            var parameter = new List<DbParameter>();
            string selectClassSql = " ,gradeclass.ClassDesc  ,gradeclass.Id AS SchoolGradeClassId ,gradeclass.SelectSubject ";
            string tableClassSql = $"  INNER JOIN up_SchoolGradeClass AS gradeclass ON gradeclass.BaseIsDelete = 0 AND u3.Id = gradeclass.UnitId AND gradeclass.GradeId = pp1.GradeId AND gradeclass.IsGraduate =  0 AND gradeclass.Statuz =  {StatusEnum.Yes.ParseToInt()}  ";
            if (param.IsCurrentSchoolYear==2)
            {
                selectClassSql = " ,gradeclass.ClassDesc  ,gradeclass.SchoolGradeClassId ,gradeclass.SelectSubject ";
                tableClassSql = $"  INNER JOIN up_SchoolGradeClassHistory AS gradeclass ON gradeclass.BaseIsDelete = 0 AND u3.Id = gradeclass.UnitId AND pp1.SchoolYearStart = gradeclass.SchoolYearStart  AND gradeclass.GradeId = pp1.GradeId AND gradeclass.IsGraduate =  0 AND gradeclass.Statuz =  {StatusEnum.Yes.ParseToInt()}  ";
            }
            strSql.Append($@" SELECT * FROM (
                SELECT pp1.* 
                ,schoolstage.DicName AS SchoolStageName ,course.DicName AS CourseName ,grade.DicName AS GradeName
                ,u3.Id AS SchoolId , u3.Name AS SchoolName ,u3.Sort 
                {selectClassSql}
                FROM ex_PlanExamParameter AS pp1
                INNER JOIN up_UnitRelation AS ur2 ON ur2.BaseIsDelete = 0 AND pp1.UnitId = ur2.UnitId AND ur2.ExtensionType = 3
                INNER JOIN up_Unit AS u3 ON u3.BaseIsDelete = 0 AND u3.Id = ur2.ExtensionObjId
                INNER JOIN up_SchoolExtension AS se11 ON se11.BaseIsDelete = 0 AND se11.UnitId = u3.Id
				INNER JOIN sys_dictionary_relation AS dicr12 ON dicr12.BaseIsDelete = 0 
                                                        AND dicr12.DictionaryId = se11.SchoolProp 
                                                        AND dicr12.DictionaryToId = pp1.SchoolStage 
                                                        AND dicr12.RelationType = 1  
                {tableClassSql}                
                LEFT JOIN  sys_static_dictionary AS schoolstage ON schoolstage.BaseIsDelete = 0 AND pp1.SchoolStage = schoolstage.DictionaryId
                LEFT JOIN  sys_static_dictionary AS course ON course.BaseIsDelete = 0 AND pp1.CourseId = course.DictionaryId
                LEFT JOIN  sys_static_dictionary AS grade ON grade.BaseIsDelete = 0 AND pp1.GradeId = grade.DictionaryId 
            ) AS tab01 Where BaseIsDelete = 0 ");
            if (param != null)
            {
                if (param.Id > 0)
                {
                    strSql.Append(" AND Id = @Id ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Id", param.Id));
                }
                if (param.UnitId > 0)
                {
                    strSql.Append(" AND UnitId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                if (param.SchoolId > 0)
                {
                    strSql.Append(" AND SchoolId = @SchoolId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
                }
                if (param.SchoolStageList != null && param.SchoolStageList.Count > 0)
                {
                    strSql.AppendFormat(" AND ({0}) ", string.Join(" OR ", param.SchoolStageList.Select(m => string.Format("  SchoolStage = {0} ", m))));
                }
                if (param.SchoolYearStart > 0)
                {
                    strSql.Append(" AND SchoolYearStart = @SchoolYearStart ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolYearStart", param.SchoolYearStart));
                }
                if (param.SchoolTerm > 0)
                {
                    strSql.Append(" AND SchoolTerm = @SchoolTerm ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolTerm", param.SchoolTerm));
                }
                if (param.GradeId > 0)
                {
                    strSql.Append(" AND GradeId = @GradeId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@GradeId", param.GradeId));
                }
                if (param.CourseId > 0)
                {
                    strSql.Append(" AND CourseId = @CourseId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (param.GradeId >= GradeEnum.GaoYi.ParseToInt())
                {
                    if (param.CompulsoryType == ClassCompulsoryTypeEnum.Select.ParseToInt())
                    {
                        if (param.CourseId > 0)
                        {
                            strSql.Append($" AND SelectSubject like '%{param.CourseId}%' "); //年级班级Id
                        }
                    }
                    else
                    {
                        if (param.CourseId > 0)
                        {
                            strSql.Append($" AND (ISNULL(SelectSubject,'') = '' OR  SelectSubject NOT like '%{param.CourseId}%' ) "); //年级班级Id
                        }
                    }
                }
                if (param.CompulsoryType > 0)
                {
                    strSql.Append(" AND CompulsoryType = @CompulsoryType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CompulsoryType", param.CompulsoryType));
                }
                if (!string.IsNullOrEmpty(param.Name))
                {
                    strSql.Append(" AND ClassDesc like @ClassDesc ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ClassDesc", $"%{param.Name}%"));
                }
            }
            return await this.BaseRepository().FindList<PlanExamParameterEntity>(strSql.ToString(), parameter.ToArray(), pagination);
        }

        /// <summary>
        /// 获取考核班级列表(学校)
        /// </summary>
        /// <param name="param">查询参数</param>
        /// <param name="pagination">分页参数</param>
        /// <returns></returns>
        public async Task<IEnumerable<PlanExamParameterEntity>> GetPlanExamClassTeacherRatePageList(PlanExamListParameterListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            var parameter = new List<DbParameter>();
            string selectClassSql = " ,gradeclass.ClassDesc  ,gradeclass.Id AS SchoolGradeClassId ,gradeclass.SelectSubject ";
            string tableClassSql = $"  INNER JOIN up_SchoolGradeClass AS gradeclass ON gradeclass.BaseIsDelete = 0 AND u3.Id = gradeclass.UnitId AND gradeclass.GradeId = pp1.GradeId   AND gradeclass.IsGraduate =  0 AND gradeclass.Statuz =  {StatusEnum.Yes.ParseToInt()} ";
            string whereClassSql = " AND booking.SchoolGradeClassId = gradeclass.Id ";
            if (param.IsCurrentSchoolYear == 2)
            {
                selectClassSql = " ,gradeclass.ClassDesc  ,gradeclass.SchoolGradeClassId ,gradeclass.SelectSubject,gradeclass.ClassId ";
                tableClassSql = $"  INNER JOIN up_SchoolGradeClassHistory AS gradeclass ON gradeclass.BaseIsDelete = 0 AND u3.Id = gradeclass.UnitId AND pp1.SchoolYearStart = gradeclass.SchoolYearStart  AND gradeclass.GradeId = pp1.GradeId  AND gradeclass.IsGraduate =  0  AND gradeclass.Statuz =  {StatusEnum.Yes.ParseToInt()} ";
                whereClassSql = " AND booking.SchoolGradeClassId = gradeclass.SchoolGradeClassId ";
            }
            strSql.Append($@" SELECT * FROM (
                SELECT pp1.* 
                ,schoolstage.DicName AS SchoolStageName ,course.DicName AS CourseName ,grade.DicName AS GradeName
                ,u3.Id AS SchoolId , u3.Name AS SchoolName ,u3.Sort 
                {selectClassSql}
                ,su.Id AS TeacherId
		        ,su.RealName AS TeacherName
                FROM ex_PlanExamParameter AS pp1
                INNER JOIN up_UnitRelation AS ur2 ON ur2.BaseIsDelete = 0 AND pp1.UnitId = ur2.UnitId AND ur2.ExtensionType = 3
                INNER JOIN up_Unit AS u3 ON u3.BaseIsDelete = 0 AND u3.Id = ur2.ExtensionObjId
                INNER JOIN up_SchoolExtension AS se11 ON se11.BaseIsDelete = 0 AND se11.UnitId = u3.Id
				INNER JOIN sys_dictionary_relation AS dicr12 ON dicr12.BaseIsDelete = 0 
                                                        AND dicr12.DictionaryId = se11.SchoolProp 
                                                        AND dicr12.DictionaryToId = pp1.SchoolStage 
                                                        AND dicr12.RelationType = 1  
                {tableClassSql}            
                INNER JOIN (
					SELECT pee.PlanParameterSetId
					,bookt.SchoolYearStart
					,bookt.SchoolTerm
					,bookt.GradeId  
					, bookt.BaseCreatorId 
					, bookt.SchoolGradeClassId  
					,bookt.Statuz
					,bookt.IsMain
					FROM ex_ExperimentBooking AS bookt 
					INNER JOIN ex_PlanExamExperiment AS pee ON pee.BaseIsDelete = 0 AND bookt.ExperimentId = pee.ExperimentId
					GROUP BY pee.PlanParameterSetId
					,bookt.SchoolYearStart
					,bookt.SchoolTerm
					,bookt.GradeId  
					, bookt.BaseCreatorId 
					, bookt.SchoolGradeClassId 
					,bookt.Statuz
					,bookt.IsMain
			   ) AS booking     ON  booking.Statuz = 100 AND booking.IsMain = 0 {whereClassSql} AND pp1.Id = booking.PlanParameterSetId 
							   AND pp1.SchoolYearStart = booking.SchoolYearStart
							   AND pp1.SchoolTerm = booking.SchoolTerm  
                INNER JOIN SysUser AS su ON booking.BaseCreatorId  = su.Id
                LEFT JOIN  sys_static_dictionary AS schoolstage ON schoolstage.BaseIsDelete = 0 AND pp1.SchoolStage = schoolstage.DictionaryId
                LEFT JOIN  sys_static_dictionary AS course ON course.BaseIsDelete = 0 AND pp1.CourseId = course.DictionaryId
                LEFT JOIN  sys_static_dictionary AS grade ON grade.BaseIsDelete = 0 AND pp1.GradeId = grade.DictionaryId 
            ) AS tab01 Where BaseIsDelete = 0 ");
            if (param != null)
            {
                if (param.Id > 0)
                {
                    strSql.Append(" AND Id = @Id ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Id", param.Id));
                }
                if (param.UnitId > 0)
                {
                    strSql.Append(" AND UnitId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                if (param.SchoolId > 0)
                {
                    strSql.Append(" AND SchoolId = @SchoolId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
                }
                if (param.SchoolStageList != null && param.SchoolStageList.Count > 0)
                {
                    strSql.AppendFormat(" AND ({0}) ", string.Join(" OR ", param.SchoolStageList.Select(m => string.Format("  SchoolStage = {0} ", m))));
                }
                if (param.SchoolYearStart > 0)
                {
                    strSql.Append(" AND SchoolYearStart = @SchoolYearStart ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolYearStart", param.SchoolYearStart));
                }
                if (param.SchoolTerm > 0)
                {
                    strSql.Append(" AND SchoolTerm = @SchoolTerm ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolTerm", param.SchoolTerm));
                }
                if (param.GradeId > 0)
                {
                    strSql.Append(" AND GradeId = @GradeId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@GradeId", param.GradeId));
                }
                if (param.CourseId > 0)
                {
                    strSql.Append(" AND CourseId = @CourseId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (param.GradeId >= GradeEnum.GaoYi.ParseToInt())
                {
                    if (param.CompulsoryType == ClassCompulsoryTypeEnum.Select.ParseToInt())
                    {
                        if (param.CourseId > 0)
                        {
                            strSql.Append($" AND SelectSubject like '%{param.CourseId}%' "); //年级班级Id
                        }
                    }
                    else
                    {
                        if (param.CourseId > 0)
                        {
                            strSql.Append($" AND (ISNULL(SelectSubject,'') = '' OR  SelectSubject NOT like '%{param.CourseId}%' ) "); //年级班级Id
                        }
                    }
                }
                if (param.CompulsoryType > 0)
                {
                    strSql.Append(" AND CompulsoryType = @CompulsoryType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CompulsoryType", param.CompulsoryType));
                }
                if (!string.IsNullOrEmpty(param.Name))
                {
                    strSql.Append(" AND ClassDesc like @ClassDesc ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ClassDesc", $"%{param.Name}%"));
                }
                if (!string.IsNullOrEmpty(param.TeacherName))
                {
                    strSql.Append($" AND TeacherName like '%{param.TeacherName}%' "); //实验来源
                }
            }
            return await this.BaseRepository().FindList<PlanExamParameterEntity>(strSql.ToString(), parameter.ToArray(), pagination);
        }
         
        #endregion
    }
}
