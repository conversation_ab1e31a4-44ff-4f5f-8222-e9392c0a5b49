﻿@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@using Dqy.Syjx.Entity.ArticleManager
@using Dqy.Syjx.Util.Model
@{
    Layout = "~/Views/Shared/_Index.cshtml";
}

<style>
     
  
    .section_right {
        width: 950px;
        margin:30px;
        background-color: #fff;
    }

    .articleContent {
       
    }

    .articleContent ul{
        padding-inline-start: 0px;
    }
   
        .articleContent  li  { 
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px 30px;
            border-bottom: 1px dashed #e6e6e6;
            font-size: 16px;
            color: #333;
        }

            .articleContent li:hover {
                cursor: pointer;
                color: #398ee5;
                text-decoration: underline;
             }


            .articleContent li .liTitle {
                
                 max-width: 80%;
                 overflow: hidden;
                 text-overflow: ellipsis;
                 white-space: nowrap;
             }
    .activeName{
        color: #409eff !important;
        border-right: 5px solid #409eff !important;
    }
</style>

<div>
   <div class="article_contact"> 
        <div class="section_right">
            <div class="articleContent">
                <ul id="article_item">
                </ul>
            </div>
        </div>
   </div>
</div>
<script>
    $(function () {
    var code = ys.request("code");
    console.log("code",code)

    if(code=='homegg'){
        $('.menu-item1').addClass("activeName");
    }else if(code=='homefg'){
        $('.menu-item2').addClass("activeName");
    }else if(code=='homewd'){
        $('.menu-item3').addClass("activeName");
    }else{
        $('.menu-item1').addClass("activeName");
    }


    $(".el-menu-item").click(function () {
        // 将当前点击的按钮背景颜色设置为蓝色
        $(this).addClass("activeName");
        // 将其他按钮的背景颜色设置为灰色
        $(this).siblings(".el-menu-item").removeClass("activeName");
      });
    
    GetArticlePageJson(code)
    
    })
    function getAricel(code) {
        GetArticlePageJson(code)
    }
    //咨询列表
    function GetArticlePageJson(code) {
            ys.ajax({
                url: '@Url.Content("~/Home/GetArticlePageJson")',
                type: 'post',
                data: {pageSize: 10000 ,pageIndex: 1,ConfigCode:code},
                success: function (obj) {
                    if (obj.Tag == 1) {
                        let articleData=obj.Data||[]
                        var html = ''
                        for (var i = 0; i < articleData.length; i++) {
                            let url = '@Url.Content("/ArticleManager/Article/ArticleDetail/")' + articleData[i].Id
                            html += `<a href=${url} target='_blank' title=${articleData[i].ShortTitle}>`;
                            html += "<li>";
                            html += "<span class='liTitle'>" + articleData[i].ShortTitle + "</span>";
                            html += "<span>" +  articleData[i].BaseModifyTime.substring(0, 10) + "</span>";
                            html += "</li>";
                            html += "</a>";
                           
                        }
                        $("#article_item").html(html); 
                    }else {
                        ys.msgError(obj.Message);
                    }
                }
            });
    }
</script>