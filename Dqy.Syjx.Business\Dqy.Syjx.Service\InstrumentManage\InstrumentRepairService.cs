﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.InstrumentManage;
using Dqy.Syjx.Model.Param.InstrumentManage;
using Dqy.Syjx.Enum.InstrumentManage;

namespace Dqy.Syjx.Service.InstrumentManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-11-24 14:05
    /// 描 述：服务类
    /// </summary>
    public class InstrumentRepairService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<InstrumentRepairEntity>> GetList(InstrumentRepairListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<InstrumentRepairEntity>> GetPageList(InstrumentRepairListParam param, Pagination pagination)
        {
            StringBuilder sql = new StringBuilder();
            var expression = ListFilter(param, sql);
            var list = await this.BaseRepository().FindList<InstrumentRepairEntity>(sql.ToString(), expression.ToArray(), pagination);
            return list.ToList();
        }

        public async Task<InstrumentRepairEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<InstrumentRepairEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(InstrumentRepairEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(InstrumentRepairEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
               strSql = $" update eq_InstrumentRepair set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
               strSql = $"update eq_InstrumentRepair set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<InstrumentRepairEntity, bool>> ListFilter(InstrumentRepairListParam param)
        {
            var expression = LinqExtensions.True<InstrumentRepairEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (!param.SchoolInstrumentId.IsNullOrZero())
                {
                    expression = expression.And(t => t.SchoolInstrumentId == param.SchoolInstrumentId);
                }
                if (param.IsShow.HasValue)
                {
                    expression = expression.And(t => t.IsShow == param.IsShow);
                }
            }
            return expression;
        }

        private List<DbParameter> ListFilter(InstrumentRepairListParam param, StringBuilder strSql)
        {
            strSql.Append(@" SELECT * From (
                               SELECT  A.Id ,A.BaseCreateTime ,A.BaseCreatorId ,
                                       A.SchoolId ,A.SchoolInstrumentId ,A.RepairNum ,A.RepairTime ,A.RepairContent ,A.IsShow ,
                                       B.Code ,B.Name ,B.Model ,B.CupboardId ,B.UnitName ,B.CourseId ,B.Floor ,B.StageId ,
                                       U.RealName AS RepairUserName ,ISD.VarietyAttribute ,
                                       ISNULL(D.Name,'') AS FunRoomName ,ISNULL(C.Name,'') AS CupboardName ,B.FunRoomId ,
                                       D.SafeguardUserId
                               FROM  eq_InstrumentRepair AS A
                               INNER JOIN  eq_SchoolInstrument AS B ON A.SchoolInstrumentId = B.Id
                               INNER JOIN  eq_InstrumentStandard AS ISD ON B.InstrumentStandardId = ISD.Id
                               INNER JOIN  SysUser AS U ON A.BaseCreatorId = U.Id
                               LEFT JOIN  bn_FunRoom AS D ON B.FunRoomId = D.Id
                               LEFT JOIN  bn_Cupboard AS C ON B.CupboardId = C.Id
                               WHERE A.BaseIsDelete = 0
                          ) as T WHERE  1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                //strSql.Append(new SchoolInstrumentService().GetUserSchoolStageSubjectSql(param.SchoolStageIdz, param.SubjectIdz));
                if (!param.SafeguardUserId.IsNullOrZero())
                {
                    strSql.Append(" AND SafeguardUserId = @SafeguardUserId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SafeguardUserId", param.SafeguardUserId));
                }
                if (param.IsShow.HasValue)
                {
                    strSql.Append(" AND IsShow = @IsShow ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@IsShow", param.IsShow));
                }
                if (!param.SchoolId.IsNullOrZero())
                {
                    strSql.Append(" AND SchoolId = @SchoolId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
                }
                if (!param.CourseId.IsNullOrZero() && param.CourseId > 0)
                {
                    strSql.Append($" AND CourseId = @CourseId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (!param.StoragePlace.IsEmpty() && !"-1".Equals(param.StoragePlace))
                {
                    if (param.StoragePlace.IndexOf(',') != -1)
                    {
                        strSql.Append(" AND CupboardId = @CupboardId");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@CupboardId", param.StoragePlace.Split(',')[1]));
                    }
                    else
                    {
                        strSql.Append(" AND FunRoomId = @FunRoomId");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@FunRoomId", param.StoragePlace));
                    }
                }
                if (param.RepairUserId.HasValue && param.RepairUserId > 0)
                {
                    strSql.Append(" AND BaseCreatorId = @RepairUserId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@RepairUserId", param.RepairUserId));
                }
                if (!string.IsNullOrWhiteSpace(param.KeyWord))
                {
                    strSql.Append($" AND (Name LIKE '%{param.KeyWord.Trim()}%' OR Code LIKE '%{param.KeyWord.Trim()}%') ");
                }

                if (!param.InstrumentAttribute.IsNullOrZero())
                {
                    if (param.InstrumentAttribute == 1)
                    {
                        //仪器
                        strSql.Append($" AND VarietyAttribute <> '{VarietyAttributeEnum.LowValueConsumable.ParseToInt()}' AND VarietyAttribute <> '{VarietyAttributeEnum.Reagent.ParseToInt()}' ");
                    }
                    else if (param.InstrumentAttribute == 2)
                    {
                        //耗材
                        strSql.Append($" AND (VarietyAttribute = '{VarietyAttributeEnum.LowValueConsumable.ParseToInt()}' OR VarietyAttribute = '{VarietyAttributeEnum.Reagent.ParseToInt()}')");
                    }
                }
            }
            return parameter;
        }
        #endregion
    }
}
