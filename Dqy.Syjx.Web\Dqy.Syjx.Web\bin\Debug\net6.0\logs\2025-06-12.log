2025-06-12 09:07:25.2218||DEBUG||程序启动 |ContentRootPath:D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web |WebRootPath:D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\wwwroot |IsDevelopment:True |url: |action: 
2025-06-12 13:26:16.6378||DEBUG||程序启动 |ContentRootPath:D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web |WebRootPath:D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\wwwroot |IsDevelopment:True |url: |action: 
2025-06-12 13:29:49.4261||INFO||URL1:/InstrumentManage/SchoolInstrument/EditInfoIndex |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-12 13:38:57.2505||ERROR||Value cannot be null. (Parameter 'source')
   at System.Linq.ThrowHelper.ThrowArgumentNullException(ExceptionArgument argument)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Dqy.Syjx.Service.OrganizationManage.UnitService.GetSchoolGradeCourseList(UnitListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Service\OrganizationManage\UnitService.cs:line 795
   at Dqy.Syjx.Business.QueryStatisticsManage.WorkBLL.GetUnitSchoolOverDueList(UnitListParam param) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Business\QueryStatisticsManage\WorkBLL.cs:line 794
   at Dqy.Syjx.Web.Controllers.HomeController.GetOverDue() in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\Controllers\HomeController.cs:line 1204
   at lambda_method819(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
 |url: http://localhost/Home/GetOverDue|action: GetOverDue
2025-06-12 13:41:57.7765||INFO||URL1:/QueryStatisticsManage/FunRoom/CountyInstitution |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-12 13:42:01.3717||INFO||URL1:/QueryStatisticsManage/FunRoom/CountySafetyCheck |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-12 14:08:47.5417||DEBUG||程序启动 |ContentRootPath:D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web |WebRootPath:D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\wwwroot |IsDevelopment:True |url: |action: 
2025-06-12 14:09:02.6247||ERROR||Value cannot be null. (Parameter 'source')
   at System.Linq.ThrowHelper.ThrowArgumentNullException(ExceptionArgument argument)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Dqy.Syjx.Service.OrganizationManage.UnitService.GetSchoolGradeCourseList(UnitListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Service\OrganizationManage\UnitService.cs:line 795
   at Dqy.Syjx.Business.QueryStatisticsManage.WorkBLL.GetUnitSchoolOverDueList(UnitListParam param) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Business\QueryStatisticsManage\WorkBLL.cs:line 794
   at Dqy.Syjx.Web.Controllers.HomeController.GetOverDue() in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\Controllers\HomeController.cs:line 1204
   at lambda_method746(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
 |url: http://localhost/Home/GetOverDue|action: GetOverDue
2025-06-12 14:09:07.3520||INFO||URL1:/QueryStatisticsManage/Instrument/CountyInstrumentStatistic |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-12 16:31:13.8650||ERROR||Value cannot be null. (Parameter 'source')
   at System.Linq.ThrowHelper.ThrowArgumentNullException(ExceptionArgument argument)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Dqy.Syjx.Service.OrganizationManage.UnitService.GetSchoolGradeCourseList(UnitListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Service\OrganizationManage\UnitService.cs:line 795
   at Dqy.Syjx.Business.QueryStatisticsManage.WorkBLL.GetUnitSchoolOverDueList(UnitListParam param) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Business\QueryStatisticsManage\WorkBLL.cs:line 794
   at Dqy.Syjx.Web.Controllers.HomeController.GetOverDue() in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\Controllers\HomeController.cs:line 1204
   at lambda_method2012(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
 |url: http://localhost/Home/GetOverDue|action: GetOverDue
2025-06-12 16:31:18.0789||INFO||URL1:/CameraManage/SchoolCamera/CameraList |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-12 16:31:18.5650||ERROR||Value cannot be null. (Parameter 'source')
   at System.Linq.ThrowHelper.ThrowArgumentNullException(ExceptionArgument argument)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Dqy.Syjx.Service.CameraManage.SchoolCameraService.GetPageList(SchoolCameraListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Service\CameraManage\SchoolCameraService.cs:line 39
   at Dqy.Syjx.Business.CameraManage.SchoolCameraBLL.GetPageList(SchoolCameraListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Business\CameraManage\SchoolCameraBLL.cs:line 62
   at Dqy.Syjx.Web.Areas.CameraManage.Controllers.SchoolCameraController.GetPageListJson(SchoolCameraListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\Areas\CameraManage\Controllers\SchoolCameraController.cs:line 89
   at lambda_method2142(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
 |url: http://localhost/CameraManage/SchoolCamera/GetPageListJson|action: GetPageListJson
2025-06-12 16:31:24.9175||WARN||耗时的Sql： SELECT  IFNULL(SUM(StockNum) ,0) AS StockNum  From (
                            SELECT  A.Id ,
                                    A.BaseIsDelete ,A.BaseCreateTime ,A.BaseModifyTime ,A.BaseCreatorId ,A.BaseModifierId ,A.BaseVersion ,
                                    A.InstrumentStandardId ,A.Name ,A.Code ,A.ModelStandardId ,IFNULL(A.Model ,'') AS Model ,A.UnitName ,A.InputNum ,A.Num ,A.Price ,
                                    A.StockNum ,A.LendNum ,A.ScrapNum ,A.OutNum ,
                                    A.Stage ,A.StageId ,A.Course ,A.PurchaseDate ,A.WarrantyMonth ,A.SupplierName ,A.Brand ,
                                    A.Statuz ,A.SchoolId ,A.InputType ,A.CupboardId ,A.Floor ,A.SourceSchoolInstrumentId ,A.IsSelfMade ,
                                    A.CourseId ,IFNULL(B.Name ,'') AS Cupboard , IFNULL(B.Sort,0) AS Sort ,
                                    IFNULL(A.FunRoomId ,0) AS FunRoomId ,IFNULL(C.Name,'') AS FunRoom ,
                                    S.VarietyAttribute ,C.SafeguardUserId ,D.DicName AS VarietyAttributeName ,S.IsDangerChemical ,
                                    U.Name AS SchoolName,A.ModelCode,A.QRCode,SU.UserName,SU.RealName,U2.Id AS CountyId
                                    FROM  eq_SchoolInstrument AS A
                                    INNER JOIN  up_Unit AS U ON A.SchoolId = U.Id
                                    INNER JOIN  up_UnitRelation AS UR ON U.Id = UR.ExtensionObjId AND UR.ExtensionType = 3
                                    INNER JOIN  up_UnitRelation AS UR2 ON UR.UnitId = UR2.ExtensionObjId AND UR2.ExtensionType = 3
                                    INNER JOIN  up_Unit AS U2 ON UR.UnitId = U2.Id
                                    INNER JOIN  eq_InstrumentStandard AS S ON A.InstrumentStandardId = S.Id
                                    INNER JOIN  sys_static_dictionary AS D ON S.VarietyAttribute = D.DictionaryId AND D.BaseIsDelete = 0 AND D.TypeCode = '1060'
                                    INNER JOIN SysUser AS SU ON A.BaseCreatorId = SU.Id
                                    LEFT JOIN  bn_FunRoom AS C ON A.FunRoomId = C.Id
                                    LEFT JOIN  bn_Cupboard AS B ON A.CupboardId = B.Id
                                    WHERE A.BaseIsDelete = 0
                            ) as T WHERE StockNum > 0   |url: http://localhost/Home/BaseInfoNum|action: BaseInfoNum
2025-06-12 16:31:28.6063||INFO||URL1:/CameraManage/SchoolCamera/CameraRelationList |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-12 16:31:29.0035||ERROR||Value cannot be null. (Parameter 'source')
   at System.Linq.ThrowHelper.ThrowArgumentNullException(ExceptionArgument argument)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Dqy.Syjx.Service.CameraManage.SchoolCameraService.GetPageList(SchoolCameraListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Service\CameraManage\SchoolCameraService.cs:line 39
   at Dqy.Syjx.Business.CameraManage.SchoolCameraBLL.GetPageList(SchoolCameraListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Business\CameraManage\SchoolCameraBLL.cs:line 62
   at Dqy.Syjx.Web.Areas.CameraManage.Controllers.SchoolCameraController.GetRelationPageListJson(SchoolCameraListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\Areas\CameraManage\Controllers\SchoolCameraController.cs:line 192
   at lambda_method2292(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
 |url: http://localhost/CameraManage/SchoolCamera/GetRelationPageListJson|action: GetRelationPageListJson
2025-06-12 16:31:37.0599||ERROR||Value cannot be null. (Parameter 'source')
   at System.Linq.ThrowHelper.ThrowArgumentNullException(ExceptionArgument argument)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Dqy.Syjx.Service.CameraManage.SchoolCameraService.GetPageList(SchoolCameraListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Service\CameraManage\SchoolCameraService.cs:line 39
   at Dqy.Syjx.Business.CameraManage.SchoolCameraBLL.GetPageList(SchoolCameraListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Business\CameraManage\SchoolCameraBLL.cs:line 62
   at Dqy.Syjx.Web.Areas.CameraManage.Controllers.SchoolCameraController.GetPageListJson(SchoolCameraListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\Areas\CameraManage\Controllers\SchoolCameraController.cs:line 89
   at lambda_method2142(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
 |url: http://localhost/CameraManage/SchoolCamera/GetPageListJson|action: GetPageListJson
2025-06-12 16:31:45.0112||ERROR||Value cannot be null. (Parameter 'source')
   at System.Linq.ThrowHelper.ThrowArgumentNullException(ExceptionArgument argument)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Dqy.Syjx.Service.CameraManage.SchoolCameraService.GetPageList(SchoolCameraListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Service\CameraManage\SchoolCameraService.cs:line 39
   at Dqy.Syjx.Business.CameraManage.SchoolCameraBLL.GetPageList(SchoolCameraListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Business\CameraManage\SchoolCameraBLL.cs:line 62
   at Dqy.Syjx.Web.Areas.CameraManage.Controllers.SchoolCameraController.GetPageListJson(SchoolCameraListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\Areas\CameraManage\Controllers\SchoolCameraController.cs:line 89
   at lambda_method2142(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
 |url: http://localhost/CameraManage/SchoolCamera/GetPageListJson|action: GetPageListJson
