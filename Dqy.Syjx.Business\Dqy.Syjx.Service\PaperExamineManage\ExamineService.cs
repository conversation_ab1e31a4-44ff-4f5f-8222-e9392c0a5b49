﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.PaperExamineManage;
using Dqy.Syjx.Model.Param.PaperExamineManage;
using Dqy.Syjx.Enum.PaperExamineManage;

namespace Dqy.Syjx.Service.PaperExamineManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2022-08-10 16:23
    /// 描 述：考核表服务类
    /// </summary>
    public class ExamineService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<ExamineEntity>> GetList(ExamineParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<ExamineEntity>> GetPageList(ExamineParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ListFilter(param, strSql);
            var list = await this.BaseRepository().FindList<ExamineEntity>(strSql.ToString(), filter.ToArray(), pagination);
            return list.ToList();
        }

        public async Task<List<ExamineListEntity>> GetUserPageList(ExamineListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = UserListFilter(param, strSql);
            var list = await this.BaseRepository().FindList<ExamineListEntity>(strSql.ToString(), filter.ToArray(), pagination);
            return list.ToList();
        }

        /// <summary>
        /// 查询自我练习数据
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<List<ExamineEntity>> GetExerciseListFilter(ExamineParam param)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ExerciseListFilter(param, strSql);
            Pagination pagination = new Pagination();
            pagination.PageIndex = 1;
            pagination.PageSize = int.MaxValue;
            var list = await this.BaseRepository().FindList<ExamineEntity>(strSql.ToString(), filter.ToArray(), pagination);
            return list.ToList();
        }

        /// <summary>
        /// 人员测评-已参加测评查询
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<List<ExamineListEntity>> GetExamineList(ExamineListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ExamineListFilter(param, strSql);
            var list = await this.BaseRepository().FindList<ExamineListEntity>(strSql.ToString(), filter.ToArray(), pagination);
            return list.ToList();
        }

        public async Task<ExamineEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<ExamineEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(ExamineEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(ExamineEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
               strSql = $" update cp_Examine set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
               strSql = $"update cp_Examine set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }

        /// <summary>
        /// 根据试卷Id删除考核主表数据
        /// </summary>
        /// <param name="paperid"></param>
        /// <returns></returns>
        public async Task DeleteByPaperId(long paperid)
        {
            string strSql = $"update cp_Examine set BaseIsDelete = 1 where PaperId = {paperid}";
            await this.BaseRepository().ExecuteBySql(strSql);
        }

        public async Task UpdateAccuracy(long id)
        {
            string strSql = $@" UPDATE  cp_Examine SET  AvgAccuracy =(CASE WHEN e2.ExamineNum > 0 THEN  CAST(e2.AccuracyTotal*1./e2.ExamineNum as decimal(18,4)) ELSE 0 END)
            ,ExamineStatuz = (CASE WHEN  e1.ExamineStatuz =  {ExerciseStatuz.NotStarted.ParseToInt()} THEN {ExerciseStatuz.InExercise.ParseToInt()} ELSE e1.ExamineStatuz END)
            FROM  cp_Examine AS e1
            INNER JOIN (
            SELECT el1.ExamineId , SUM(Accuracy) AS AccuracyTotal ,COUNT(Id) AS ExamineNum FROM  cp_ExamineList	AS el1 WHERE el1.ExamineId = {id} AND el1.BaseIsDelete = 0  GROUP BY el1.ExamineId  ) AS e2 ON e1.Id = e2.ExamineId
            WHERE e1.BaseIsDelete = 0 AND e1.Id = {id} ";
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<ExamineEntity, bool>> ListFilter(ExamineParam param)
        {
            var expression = LinqExtensions.True<ExamineEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
            }
            return expression;
        }


        private List<DbParameter> ListFilter(ExamineParam param, StringBuilder strSql)
        {
            StringBuilder strBody = new StringBuilder();
            strBody.Append($@" SELECT e.Id ,e.BaseIsDelete ,e.BaseCreateTime ,e.BaseModifyTime ,e.BaseCreatorId ,e.BaseModifierId ,e.BaseVersion
                ,e.AvgAccuracy ,e.Duration ,e.BeginTime ,e.EndTime ,e.ExamineStatuz ,e.Statuz ,e.UnitId ,e.Remark   ,e. PaperId
                ,p.Code ,p.Name ,p.PaperType ,p.UseObj ,p.CourseId ,p.SchoolStagez ,p.GradeId ,p.PaperStatuz
                ,p.RadioQuestionNum ,p.ChedkboxQuestionNum ,p.IfQuestionNum ,p.TotalQuestionNum
                ,Case WHEN p.ExamineTime > '1970-01-01 00:00:00' THEN p.ExamineTime ELSE NULL END AS ExamineTime
                ,dic3.Nature ,dic3.DicName AS UseObjName
                FROM  cp_Examine AS e
                INNER JOIN  cp_Paper AS p ON p.Id = e.PaperId AND p.BaseIsDelete = 0
                LEFT JOIN  sys_static_dictionary AS dic3 ON dic3.BaseIsDelete = 0 AND dic3.TypeCode = '2003' AND dic3.DictionaryId = p.UseObj --适用对象
                WHERE p.Statuz = 1 AND p.PaperStatuz = 3  ");


            strSql.Append(" Select * From ( ");
            strSql.Append(strBody.ToString());
            strSql.Append(" ) AS tb_1 Where 1 = 1 ");

            var parameter = new List<DbParameter>();
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = @BaseIsDelete ");
                parameter.Add(DbParameterExtension.CreateDbParameter("@BaseIsDelete", param.BaseIsDelete));
                strSql.Append(" AND UnitId = @UnitId ");
                parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                if (param.Nature > 0)
                {
                    strSql.Append(" AND Nature = @Nature ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Nature", param.Nature));
                }
                if (param.UseObj > 0)
                {
                    strSql.Append(" AND UseObj = @UseObj ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UseObj", param.UseObj));
                }
                if (param.BeginExamineTime != null)
                {
                    strSql.Append(" AND ExamineTime > @BeginExamineTime ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@BeginExamineTime", param.BeginExamineTime));
                }
                if (param.EndExamineTime != null)
                {
                    strSql.Append(" AND ExamineTime < @EndExamineTime ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@EndExamineTime", param.EndExamineTime.Value.AddDays(1).AddSeconds(-1)));
                }
                if (!string.IsNullOrEmpty(param.Keyword))
                {
                    strSql.Append(" AND ( Name like @Keyword OR Code like @Keyword ) ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Keyword", $"%{param.Keyword}%"));
                }
            }
            return parameter;
        }

        private List<DbParameter> UserListFilter(ExamineListParam param, StringBuilder strSql)
        {
            StringBuilder strBody = new StringBuilder();
            strBody.Append($@" SELECT
            el.Id ,el.BaseIsDelete ,el.BaseCreateTime ,el.BaseModifyTime ,el.BaseCreatorId ,el.BaseModifierId ,el.BaseVersion ,
            el.ExamineId ,el.PaperId ,el.RealName ,el.Mobile ,el.SysUserId ,el.GradeClassName ,el.CountyId ,el.Accuracy ,
            el.Duration ,el.BeginTime ,el.EndTime ,el.Statuz ,el.SubmitStatuz ,el.UnitId ,el.Remark ,el.ExamineUserCode
            ,dic3.Nature ,dic3.DicName AS UseObjName
            FROM  cp_ExamineList	 AS el
            INNER JOIN  cp_Examine AS e ON el.ExamineId = e.Id AND e.BaseIsDelete = 0
			 INNER JOIN  cp_Paper AS p ON p.Id = e.PaperId AND p.BaseIsDelete = 0
            LEFT JOIN  sys_static_dictionary AS dic3 ON dic3.BaseIsDelete = 0 AND dic3.TypeCode = '2003' AND dic3.DictionaryId = p.UseObj --适用对象
            Where e.Statuz = 1 ");

            strSql.Append(" Select * From ( ");
            strSql.Append(strBody.ToString());
            strSql.Append(" ) AS tb_1 Where 1 = 1 ");

            var parameter = new List<DbParameter>();
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = @BaseIsDelete ");
                parameter.Add(DbParameterExtension.CreateDbParameter("@BaseIsDelete", param.BaseIsDelete));

                if (param.ExamineId > 0)
                {
                    strSql.Append(" AND ExamineId = @ExamineId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ExamineId", param.ExamineId));
                }
                if (param.SubmitStatuz > 0)
                {
                    strSql.Append(" AND SubmitStatuz = @SubmitStatuz ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SubmitStatuz", param.SubmitStatuz));
                }
                if (!string.IsNullOrEmpty(param.Keyword))
                {
                    strSql.Append(" AND ( RealName like @Keyword ) ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Keyword", $"%{param.Keyword}%"));
                }
            }
            return parameter;
        }

        /// <summary>
        /// 查询自我练习数据
        /// </summary>
        /// <param name="param"></param>
        /// <param name="strSql"></param>
        /// <returns></returns>
        private List<DbParameter> ExerciseListFilter(ExamineParam param, StringBuilder strSql)
        {
            strSql.Append($@"SELECT * From (
                                SELECT exa.Id ,exa.BaseCreatorId ,exa.ExamineStatuz ,cp.PaperType FROM  cp_Examine exa
                                INNER JOIN  cp_Paper cp ON cp.Id = exa.PaperId AND cp.BaseIsDelete = 0
                            )tb1 WHERE 1=1 AND PaperType = 2 AND ExamineStatuz < 3 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.CreatorId > 0)
                {
                    strSql.Append(" AND BaseCreatorId = @BaseCreatorId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@BaseCreatorId",param.CreatorId));
                }

            }
            return parameter;
        }

        /// <summary>
        /// 人员测评-已参加测评查询
        /// </summary>
        /// <param name="param"></param>
        /// <param name="strSql"></param>
        /// <returns></returns>
        private List<DbParameter>  ExamineListFilter(ExamineListParam param, StringBuilder strSql)
        {
            strSql.Append($@"SELECT * FROM (
	                            SELECT exl.Id ,p.Id AS PaperId ,p.PaperType AS PaperType ,ex.Id AS ExamineId ,p.Name AS PaperName ,p.TotalQuestionNum ,exl.Accuracy ,exl.BeginTime ,exl.EndTime ,SubmitStatuz AS SubmitStatuz ,exl.SysUserId
	                            FROM  cp_ExamineList AS exl
	                            INNER JOIN  cp_Examine ex ON ex.Id = exl.ExamineId AND ex.BaseIsDelete = 0 AND ex.Statuz = 1
	                            INNER JOIN  cp_Paper p ON p.Id = exl.PaperId AND p.BaseIsDelete = 0 AND p.Statuz = 1
	                            WHERE exl.Statuz = 1 AND exl.BaseIsDelete = 0
                            ) t WHERE 1=1 AND PaperType=1");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.SysUserId > 0)
                {
                    strSql.Append(" AND SysUserId = @SysUserId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SysUserId", param.SysUserId));
                }
                if(param.SubmitStatuz > 0)
                {
                    strSql.Append(" AND SubmitStatuz = @SubmitStatuz ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SubmitStatuz", param.SubmitStatuz));
                }
                if (!string.IsNullOrEmpty(param.PaperName))
                {
                    strSql.Append($" AND PaperName like '%{param.PaperName}%'");
                }

                //考核时间查询
                if (!string.IsNullOrEmpty(param.BeginTime.ParseToString()))
                {
                    strSql.Append(" AND BeginTime >= @BeginTime ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@BeginTime", param.BeginTime));
                }
                if (!string.IsNullOrEmpty(param.EndTime.ParseToString()))
                {
                    param.EndTime = param.EndTime.Value.Date.Add(new TimeSpan(23, 59, 59));
                    strSql.Append(" AND BeginTime <= @EndTime ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@EndTime", param.EndTime));
                }
            }
            return parameter;
        }
        #endregion
    }
}
