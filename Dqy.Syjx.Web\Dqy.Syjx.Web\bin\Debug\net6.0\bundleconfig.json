// Configure bundling and minification for the project.
// More info at https://go.microsoft.com/fwlink/?LinkId=808241
[
  {
    "outputFileName": "wwwroot/yisha/css/style.min.css",
    "inputFiles": [
      "wwwroot/yisha/css/animate.css",
      "wwwroot/yisha/css/style.css",
      "wwwroot/yisha/css/skins.css",
      "wwwroot/lib/imageview/css/boxImg.css",
      "wwwroot/junfei/css/custom.css"
    ],
    "minify": {
      "enabled": false,
      "renameLocals": true
    }
  },
  {
    "outputFileName": "wwwroot/yisha/js/yisha.min.js",
    "inputFiles": [
      "wwwroot/yisha/js/yisha.js",
      "wwwroot/yisha/js/yisha-plugin.js",
      "wwwroot/lib/imageview/js/boxImg.js"
    ],
    "minify": {
      "enabled": true,
      "renameLocals": true
    }
  },

  {
    "outputFileName": "wwwroot/yisha/css/yisha.min.css",
    "inputFiles": [
      "wwwroot/yisha/css/yisha.css"
    ],
    "minify": {
      "enabled": true,
      "renameLocals": true
    }
  },
  {
    "outputFileName": "wwwroot/yisha/js/yisha-init.min.js",
    "inputFiles": [
      "wwwroot/yisha/js/yisha-init.js"
    ],
    "minify": {
      "enabled": true,
      "renameLocals": true
    }
  },
  {
    "outputFileName": "wwwroot/yisha/js/yisha-index.min.js",
    "inputFiles": [
      "wwwroot/yisha/js/yisha-index.js"
    ],
    "minify": {
      "enabled": true,
      "renameLocals": true
    }
  },
  {
    "outputFileName": "wwwroot/yisha/js/yisha-data.min.js",
    "inputFiles": [
      "wwwroot/yisha/js/yisha-data.js"
    ],
    "minify": {
      "enabled": true,
      "renameLocals": true
    }
  },
  {
    "outputFileName": "wwwroot/lib/jquery/3.7.1/jquery.min.js",
    "inputFiles": [
      "wwwroot/lib/jquery/3.7.1/jquery.js",
      "wwwroot/lib/jquery/3.7.1/jquery-migrate-1.2.1.js",
      "wwwroot/lib/jquery.blockui/2.7/jquery.blockUI.js",
      "wwwroot/lib/jquery.cookie/1.4.1/jquery.cookie.js",
      "wwwroot/lib/jquery.fullscreen/1.2/jquery.fullscreen.js"
    ],
    "minify": {
      "enabled": true,
      "renameLocals": true
    }
  },

  {
    "outputFileName": "wwwroot/lib/bootstrap/3.3.7/css/bootstrap.min.css",
    "inputFiles": [
      "wwwroot/lib/bootstrap/3.3.7/css/bootstrap.css"
    ],
    "minify": {
      "enabled": true,
      "renameLocals": true
    }
  },
  {
    "outputFileName": "wwwroot/lib/bootstrap/3.3.7/js/bootstrap.min.js",
    "inputFiles": [
      "wwwroot/lib/bootstrap/3.3.7/js/bootstrap.js",
      "wwwroot/lib/bootstrap/3.3.7/js/bootstrap.dropdown.js"
    ],
    "minify": {
      "enabled": true,
      "renameLocals": true
    }
  },

  {
    "outputFileName": "wwwroot/lib/fontawesome/4.7.0/css/fontawesome.min.css",
    "inputFiles": [
      "wwwroot/lib/fontawesome/4.7.0/css/font-awesome.css"
    ],
    "minify": {
      "enabled": true,
      "renameLocals": true
    }
  },

  {
    "outputFileName": "wwwroot/lib/layer/3.1.1/layer.min.js",
    "inputFiles": [
      "wwwroot/lib/layer/3.1.1/layer.js"
    ],
    "minify": {
      "enabled": true,
      "renameLocals": true
    }
  },

  {
    "outputFileName": "wwwroot/lib/laydate/5.0.9/laydate.min.js",
    "inputFiles": [
      "wwwroot/lib/laydate/5.0.9/laydate.js"
    ],
    "minify": {
      "enabled": true,
      "renameLocals": true
    }
  },

  {
    "outputFileName": "wwwroot/lib/jquery.validation/1.14.0/jquery.validate.min.js",
    "inputFiles": [
      "wwwroot/lib/jquery.validation/1.14.0/jquery.validate.js",
      "wwwroot/lib/jquery.validation/1.14.0/jquery.validate.extend.js",
      "wwwroot/lib/jquery.validation/1.14.0/localization/messages_zh.js"
    ],
    "minify": {
      "enabled": true,
      "renameLocals": true
    }
  },

  {
    "outputFileName": "wwwroot/lib/zTree/v3/css/metroStyle/metroStyle.min.css",
    "inputFiles": [
      "wwwroot/lib/zTree/v3/css/metroStyle/metroStyle.css"
    ],
    "minify": {
      "enabled": true,
      "renameLocals": true
    }
  },
  {
    "outputFileName": "wwwroot/lib/zTree/v3/js/ztree.min.js",
    "inputFiles": [
      "wwwroot/lib/zTree/v3/js/jquery.ztree.all-3.5.js",
      "wwwroot/yisha/js/yisha-jquery-ztree-plugin.js"
    ],
    "minify": {
      "enabled": true,
      "renameLocals": true
    }
  },

  {
    "outputFileName": "wwwroot/lib/select2/4.0.6/css/select2.min.css",
    "inputFiles": [
      "wwwroot/lib/select2/4.0.6/css/select2.css"
    ],
    "minify": {
      "enabled": true,
      "renameLocals": true
    }
  },
  {
    "outputFileName": "wwwroot/lib/select2/4.0.6/js/select2.min.js",
    "inputFiles": [
      "wwwroot/lib/select2/4.0.6/js/select2.js",
      "wwwroot/lib/select2/4.0.6/js/i18n/zh-CN.js"
    ],
    "minify": {
      "enabled": true,
      "renameLocals": true
    }
  },

  {
    "outputFileName": "wwwroot/lib/jquery.layout/1.4.4/jquery.layout-latest.min.css",
    "inputFiles": [
      "wwwroot/lib/jquery.layout/1.4.4/jquery.layout-latest.css"
    ],
    "minify": {
      "enabled": true,
      "renameLocals": true
    }
  },
  {
    "outputFileName": "wwwroot/lib/jquery.layout/1.4.4/jquery.layout-latest.min.js",
    "inputFiles": [
      "wwwroot/lib/jquery.layout/1.4.4/jquery.layout-latest.js"
    ],
    "minify": {
      "enabled": true,
      "renameLocals": true
    }
  },

  {
    "outputFileName": "wwwroot/lib/icheck/1.0.2/skins/custom.min.css",
    "inputFiles": [
      "wwwroot/lib/icheck/1.0.2/skins/custom.css"
    ],
    "minify": {
      "enabled": true,
      "renameLocals": true
    }
  },
  {
    "outputFileName": "wwwroot/lib/icheck/1.0.2/icheck.min.js",
    "inputFiles": [
      "wwwroot/lib/icheck/1.0.2/icheck.js"
    ],
    "minify": {
      "enabled": true,
      "renameLocals": true
    }
  },

  {
    "outputFileName": "wwwroot/lib/bootstrap.table/1.12.0/bootstrap-table.min.css",
    "inputFiles": [
      "wwwroot/lib/bootstrap.table/1.12.0/bootstrap-table.css"
    ]
  },
  {
    "outputFileName": "wwwroot/lib/bootstrap.table/1.12.0/bootstrap-table.min.js",
    "inputFiles": [
      "wwwroot/lib/bootstrap.table/1.12.0/bootstrap-table.js",
      "wwwroot/lib/bootstrap.table/1.12.0/extensions/mobile/bootstrap-table-mobile.js",
      "wwwroot/lib/bootstrap.table/1.12.0/locale/bootstrap-table-zh-CN.js",
      "wwwroot/yisha/js/yisha-jquery-bootstrap-table-plugin.js"
    ],
    "minify": {
      "enabled": true,
      "renameLocals": true
    }
  },

  {
    "outputFileName": "wwwroot/lib/bootstrap.treetable/1.0/bootstrap-treetable.min.css",
    "inputFiles": [
      "wwwroot/lib/bootstrap.treetable/1.0/bootstrap-treetable.css"
    ],
    "minify": {
      "enabled": true,
      "renameLocals": true
    }
  },
  {
    "outputFileName": "wwwroot/lib/bootstrap.treetable/1.0/bootstrap-treetable.min.js",
    "inputFiles": [
      "wwwroot/lib/bootstrap.treetable/1.0/bootstrap-treetable.js",
      "wwwroot/yisha/js/yisha-jquery-bootstrap-treetable-plugin.js"
    ],
    "minify": {
      "enabled": true,
      "renameLocals": true
    }
  },

  {
    "outputFileName": "wwwroot/lib/jquery.smartwizard/4.0.1/css/smart_wizard.min.css",
    "inputFiles": [
      "wwwroot/lib/bootstrap/3.3.7/css/bootstrap-theme.css",
      "wwwroot/lib/jquery.smartwizard/4.0.1/css/smart_wizard.css",
      "wwwroot/lib/jquery.smartwizard/4.0.1/css/smart_wizard_theme_arrows.css"
    ],
    "minify": {
      "enabled": true,
      "renameLocals": true
    }
  },
  {
    "outputFileName": "wwwroot/lib/jquery.smartwizard/4.0.1/css/smart_wizard.min.js",
    "inputFiles": [
      "wwwroot/lib/jquery.smartwizard/4.0.1/js/jquery.smartWizard.js"
    ],
    "minify": {
      "enabled": true,
      "renameLocals": true
    }
  },

  {
    "outputFileName": "wwwroot/lib/jquery.ui/1.12.1/jquery-ui.min.css",
    "inputFiles": [
      "wwwroot/lib/jquery.ui/1.12.1/jquery-ui.css"
    ],
    "minify": {
      "enabled": true,
      "renameLocals": true
    }
  },
  {
    "outputFileName": "wwwroot/lib/jquery.ui/1.12.1/jquery-ui.min.js",
    "inputFiles": [
      "wwwroot/lib/jquery.ui/1.12.1/jquery-ui.js"
    ],
    "minify": {
      "enabled": true,
      "renameLocals": true
    }
  },

  {
    "outputFileName": "wwwroot/lib/highlight/9.13.1/css/vs.min.css",
    "inputFiles": [
      "wwwroot/lib/highlight/9.13.1/css/vs.css"
    ],
    "minify": {
      "enabled": true,
      "renameLocals": true
    }
  },
  {
    "outputFileName": "wwwroot/lib/jquery.ui/1.12.1/highlight.pack.min.js",
    "inputFiles": [
      "wwwroot/lib/highlight/9.13.1/highlight.pack.js"
    ],
    "minify": {
      "enabled": true,
      "renameLocals": true
    }
  },

  {
    "outputFileName": "wwwroot/lib/cropbox/1.0/cropbox.min.css",
    "inputFiles": [
      "wwwroot/lib/cropbox/1.0/cropbox.css"
    ],
    "minify": {
      "enabled": true,
      "renameLocals": true
    }
  },
  {
    "outputFileName": "wwwroot/lib/cropbox/1.0/cropbox.min.js",
    "inputFiles": [
      "wwwroot/lib/cropbox/1.0/cropbox.js"
    ],
    "minify": {
      "enabled": true,
      "renameLocals": true
    }
  },

  {
    "outputFileName": "wwwroot/lib/imageupload/1.0/css/imgup.min.css",
    "inputFiles": [
      "wwwroot/lib/imageupload/1.0/css/imgup.css"
    ],
    "minify": {
      "enabled": true,
      "renameLocals": true
    }
  },
  {
    "outputFileName": "wwwroot/lib/imageupload/1.0/js/imgup.min.js",
    "inputFiles": [
      "wwwroot/lib/imageupload/1.0/js/imgup.js",
      "wwwroot/lib/imageupload/1.0/js/preview.js"
    ],
    "minify": {
      "enabled": true,
      "renameLocals": true
    }
  },
  {
    "outputFileName": "wwwroot/lib/fileinput/5.0.3/js/fileinput.min.js",
    "inputFiles": [
      "wwwroot/lib/fileinput/5.0.3/js/fileinput.js",
      "wwwroot/lib/fileinput/5.0.3/js/locales/zh.js"
    ],
    "minify": {
      "enabled": true,
      "renameLocals": true
    }
  },
  {
    "outputFileName": "wwwroot/lib/fileinput/5.0.3/css/fileinput.min.css",
    "inputFiles": [
      "wwwroot/lib/fileinput/5.0.3/css/fileinput.css"
    ],
    "minify": {
      "enabled": true,
      "renameLocals": true
    }
  },
  {
    "outputFileName": "wwwroot/lib/report/echarts/echarts.min.js",
    "inputFiles": [
      "wwwroot/lib/report/echarts/echarts.js",
      "wwwroot/lib/report/echarts/china.js"
    ],
    "minify": {
      "enabled": true,
      "renameLocals": true
    }
  },
  {
    "outputFileName": "wwwroot/lib/report/peity/jquery.peity.min.js",
    "inputFiles": [
      "wwwroot/lib/report/peity/jquery.peity.js"
    ],
    "minify": {
      "enabled": true,
      "renameLocals": true
    }
  },

  {
    "outputFileName": "wwwroot/lib/bootstrap.tagsinput/0.8.0/bootstrap-tagsinput.min.css",
    "inputFiles": [
      "wwwroot/lib/bootstrap.tagsinput/0.8.0/bootstrap-tagsinput.css",
      "wwwroot/lib/bootstrap.tagsinput/0.8.0/bootstrap-tagsinput-typeahead.css"
    ],
    "minify": {
      "enabled": true,
      "renameLocals": true
    }
  },
  {
    "outputFileName": "wwwroot/lib/bootstrap.tagsinput/0.8.0/bootstrap-tagsinput.min.js",
    "inputFiles": [
      "wwwroot/lib/bootstrap.tagsinput/0.8.0/bootstrap-tagsinput.js"
    ],
    "minify": {
      "enabled": true,
      "renameLocals": true
    }
  },
  {
    "outputFileName": "wwwroot/lib/jquery-timepicker/css/timePicker.min.css",
    "inputFiles": [
      "wwwroot/lib/jquery-timepicker/css/timePicker.css"
    ],
    "minify": {
      "enabled": true,
      "renameLocals": true
    }
  },
  {
    "outputFileName": "wwwroot/lib/jquery-timepicker/js/timepicker.min.js",
    "inputFiles": [
      "wwwroot/lib/jquery-timepicker/js/jquery-timepicker.js"
    ],
    "minify": {
      "enabled": true,
      "renameLocals": true
    }
  },
  {
    "outputFileName": "wwwroot/lib/jquery.string.format/jquery.string.format.min.js",
    "inputFiles": [
      "wwwroot/lib/jquery.string.format/jquery.string.format.js"
    ],
    "minify": {
      "enabled": true,
      "renameLocals": true
    }
  },
  {
    "outputFileName": "wwwroot/lib/md5/js/md5.min.js",
    "inputFiles": [
      "wwwroot/lib/md5/md5.js"
    ],
    "minify": {
      "enabled": true,
      "renameLocals": true
    }
  },
  {
    "outputFileName": "wwwroot/lib/uploadifive/File.min.css",
    "inputFiles": [
      "wwwroot/lib/uploadifive/ShowFile.css",
      "wwwroot/lib/uploadifive/uploadifive.css"
    ],
    "minify": {
      "enabled": true,
      "renameLocals": true
    }
  },
  {
    "outputFileName": "wwwroot/lib/uploadifive/File.min.js",
    "inputFiles": [
      "wwwroot/lib/uploadifive/jquery.uploadifive.js"
    ],
    "minify": {
      "enabled": true,
      "renameLocals": true
    }
  },
  {
    "outputFileName": "wwwroot/junfei/js/CommonBox.min.js",
    "inputFiles": [
      "wwwroot/junfei/js/CommonBox.js",
      "wwwroot/junfei/js/Common.js"
    ],
    "minify": {
      "enabled": true,
      "renameLocals": true
    }
  }
]
