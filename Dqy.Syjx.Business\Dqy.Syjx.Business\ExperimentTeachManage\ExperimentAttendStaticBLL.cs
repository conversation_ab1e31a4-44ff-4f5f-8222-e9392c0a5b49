﻿using Dqy.Syjx.Business.OrganizationManage;
using Dqy.Syjx.Entity.ExperimentTeachManage;
using Dqy.Syjx.Entity.OrganizationManage;
using Dqy.Syjx.Entity.SystemManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Enum.ExperimentTeachManage;
using Dqy.Syjx.Model.Input.ExperimentTeachManage;
using Dqy.Syjx.Model.Param.ExperimentTeachManage;
using Dqy.Syjx.Model.Param.OrganizationManage;
using Dqy.Syjx.Model.Param.SystemManage;
using Dqy.Syjx.Model.Result.ExperimentTeachManage;
using Dqy.Syjx.Service.ExperimentTeachManage;
using Dqy.Syjx.Service.OrganizationManage;
using Dqy.Syjx.Service.SystemManage;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Util.Tools;
using Dqy.Syjx.Web.Code;
using Dynamitey.DynamicObjects;
using NPOI.SS.Formula.Functions;
using Senparc.Weixin.MP;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using static Dqy.Syjx.Util.ThirdOAuth.TztxSoftAcore;

namespace Dqy.Syjx.Business.ExperimentTeachManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-12-21 14:18
    /// 描 述：业务类
    /// </summary>
    public class ExperimentAttendStaticBLL
    {
        /// <summary>
        /// ApiToken值（仅WebApi端传入）
        /// </summary>
        public string ApiToken = string.Empty;

        private ExperimentAttendStaticService experimentAttendStaticService = new ExperimentAttendStaticService();
        private TextbookVersionParameterService textbookVersionParameterService = new TextbookVersionParameterService();
        private TextbookVersionParameterHistoryService textbookVersionParameterHistoryService = new TextbookVersionParameterHistoryService();
        private UnitRelationService unitRelationService = new UnitRelationService();
        private StaticDictionaryService staticDictionaryService = new StaticDictionaryService();
        private SchoolExtensionService schoolExtensionService = new SchoolExtensionService();
        private SchoolTermBLL schoolTermBLL = new SchoolTermBLL();
        private ExperimentBookingService experimentBookingService = new ExperimentBookingService();
        private PlanDetailService planDetailService = new PlanDetailService();
        private PlanInfoService planInfoService = new PlanInfoService();
        private SchoolGradeClassService schoolGradeClassService = new SchoolGradeClassService();
        private UnitService unitService = new UnitService();
        private SchoolExperimentService schoolExperimentService = new SchoolExperimentService();
        #region 获取数据
        /// <summary>
        /// 实验开出统计-学校端
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<TData<List<ExperimentAttendStaticEntity>>> GetPageList(ExperimentAttendStaticListParam param, Pagination pagination)
        {
            TData<List<ExperimentAttendStaticEntity>> obj = new TData<List<ExperimentAttendStaticEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            if (operatorinfo != null)
            {
                if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    param.SchoolId = operatorinfo.UnitId;
                }
                else if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    param.CountyId = operatorinfo.UnitId;
                    param.SetUserId = operatorinfo.UserId.Value;
                }
                else if (operatorinfo.UnitType == UnitTypeEnum.City.ParseToInt())
                {
                    param.CityId = operatorinfo.UnitId;
                }
                obj.Data = await experimentAttendStaticService.GetPageList(param, pagination);
                obj.Total = pagination.TotalCount;
                obj.Tag = 1;
            }
            return obj;
        }

        /// <summary>
        /// 实验开出统计-区县端
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<TData<List<ExperimentAttendStaticEntity>>> GetCountyPageList(ExperimentAttendStaticListParam param, Pagination pagination)
        {
            TData<List<ExperimentAttendStaticEntity>> obj = new TData<List<ExperimentAttendStaticEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            if (operatorinfo != null)
            {
                if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    param.CountyId = operatorinfo.UnitId;
                }
                else if (operatorinfo.UnitType == UnitTypeEnum.City.ParseToInt())
                {
                    param.CityId = operatorinfo.UnitId;
                }
                param.SetUserId = operatorinfo.UserId.Value;
                obj.Data = await experimentAttendStaticService.GetCountyPageList(param, pagination);
                obj.Total = pagination.TotalCount;
                obj.Tag = 1;
            }
            return obj;
        }

        /// <summary>
        /// 实验开出统计-市级端
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<TData<List<ExperimentAttendStaticEntity>>> GetCityPageList(ExperimentAttendStaticListParam param, Pagination pagination)
        {
            TData<List<ExperimentAttendStaticEntity>> obj = new TData<List<ExperimentAttendStaticEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            if (operatorinfo != null)
            {
                if (operatorinfo.UnitType == UnitTypeEnum.City.ParseToInt())
                {
                    param.CityId = operatorinfo.UnitId;
                }
                obj.Data = await experimentAttendStaticService.GetCityPageList(param, pagination);
                obj.Total = pagination.TotalCount;
                obj.Tag = 1;
            }
            return obj;
        }

        public async Task<TData<List<ExperimentBookingEntity>>> GetExperimentAttendStaticDetail(ExperimentBookingListParam param, Pagination pagination)
        {
            TData<List<ExperimentBookingEntity>> obj = new TData<List<ExperimentBookingEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            if (operatorinfo != null)
            {
                string courseName = "";
                var entityCourse = await staticDictionaryService.GetEntityByDictionaryId(param.CourseId ?? 0, DicTypeCodeEnum.Course.ParseToInt().ToString());
                if (entityCourse != null)
                {
                    courseName = entityCourse.DicName;
                }

                List<ExperimentBookingEntity> listBooking = new List<ExperimentBookingEntity>();
                List<ExperimentBookingStaticModel> listBookingAll = new List<ExperimentBookingStaticModel>();
                int IsShowClassTeacher = 0;
                if (param.GradeId < GradeEnum.GaoYi.ParseToInt())
                {
                    var paramBooking = new ExperimentBookingListParam();
                    paramBooking.SchoolId = operatorinfo.UnitId;
                    paramBooking.SchoolStageId = param.SchoolStageId;
                    paramBooking.CourseId = param.CourseId;
                    paramBooking.GradeId = param.GradeId ?? 0;
                    paramBooking.SchoolYearStart = param.SchoolYearStart ?? 0;
                    paramBooking.SchoolTerm = param.SchoolTerm ?? 0;
                    IsShowClassTeacher = param.IsShowClassTeacher;
                    paramBooking.IsShowClassTeacher = param.IsShowClassTeacher;
                    paramBooking.IsEvaluate = 1;
                    if (param.SchoolYearStart != operatorinfo.SchoolTermStartYear)
                    {
                        paramBooking.IsCurrentSchoolYear = 2;
                    }
                    listBooking = await experimentBookingService.GetStaticClassPage(paramBooking, pagination);
                 
                    //获取达标考核参数。
                    var paramParameter = new TextbookVersionListParameterListParam();
                    //区县
                    if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
                    {
                        paramParameter.CountyId = await unitService.GetParentUnitId(operatorinfo.UnitId ?? 0);
                    }
                    else
                    {
                        paramParameter.CountyId = 1;//禁止其他单位查询数据，该方法不支持
                    }
                    paramParameter.CourseId = param.CourseId;
                    paramParameter.GradeId = param.GradeId;
                    paramParameter.SchoolTerm = param.SchoolTerm;
                    TextbookVersionParameterEntity entityParameter = null; 
                    if (param.SchoolYearStart != operatorinfo.SchoolTermStartYear)
                    {
                      var  listParameterHistory = await textbookVersionParameterHistoryService.GetList(paramParameter);
                        if (listParameterHistory!=null && listParameterHistory.Count > 0)
                        {
                            entityParameter = new TextbookVersionParameterEntity();
                            entityParameter.Id = listParameterHistory[0].TextbookVersionParameterId;
                            entityParameter.NeedShowNum = listParameterHistory[0].NeedShowNum;
                            entityParameter.NeedGroupNum = listParameterHistory[0].NeedGroupNum;
                            entityParameter.OptionalShowNum = listParameterHistory[0].OptionalShowNum;
                            entityParameter.OptionalGroupNum = listParameterHistory[0].OptionalGroupNum;
                        }
                    }
                    else
                    {
                        var listParameter = await textbookVersionParameterService.GetList(paramParameter);
                        entityParameter = listParameter.FirstOrDefault();
                    }
                    if (entityParameter != null)
                    {
                        //正常应该是一条
                        if (listBooking != null && listBooking.Count > 0)
                        {
                            //获取统计数据
                            paramBooking = new ExperimentBookingListParam();
                            paramBooking.SchoolId = operatorinfo.UnitId;
                            paramBooking.SchoolStageId = param.SchoolStageId;
                            paramBooking.CourseId = param.CourseId;
                            paramBooking.GradeId = param.GradeId ?? 0;
                            paramBooking.SchoolYearStart = param.SchoolYearStart ?? 0;
                            paramBooking.SchoolTerm = param.SchoolTerm ?? 0;
                            paramBooking.IsEvaluate = 1;
                            if (param.SourceType == SourceTypeEnum.ExperimentPlan.ParseToInt())
                            {
                                listBookingAll = await experimentBookingService.GetPlanAll(paramBooking);
                            }
                            else
                            {
                                listBookingAll = await experimentBookingService.GetAll(paramBooking);
                            }

                            listBooking.ForEach(m =>
                            {
                                m.ClassDesc = m.ClassName;
                                m.CourseName = courseName;
                                m.NeedShowNum = entityParameter.NeedShowNum;
                                m.NeedShowNumed = NeedShowNumed(listBookingAll, m, IsShowClassTeacher);
                                m.NeedGroupNum = entityParameter.NeedGroupNum;
                                m.NeedGroupNumed = NeedGroupNumed(listBookingAll, m, IsShowClassTeacher);
                                m.OptionalShowNum = entityParameter.OptionalShowNum;
                                m.OptionalShowNumed = OptionalShowNumed(listBookingAll, m, IsShowClassTeacher);
                                m.OptionalGroupNum = entityParameter.OptionalGroupNum;
                                m.OptionalGroupNumed = OptionalGroupNumed(listBookingAll, m, IsShowClassTeacher);
                            });

                            foreach (var item in listBooking)
                            {
                                item.SourceType = param.SourceType ?? 0;
                                item.NeedShowNumRatio = GetComputeRatio(item.NeedShowNumed, item.NeedShowNum);
                                item.NeedGroupNumRatio = GetComputeRatio(item.NeedGroupNumed, item.NeedGroupNum);
                                item.OptionalShowNumRatio = GetComputeRatio(item.OptionalShowNumed, item.OptionalShowNum);
                                item.OptionalGroupNumRatio = GetComputeRatio(item.OptionalGroupNumed, item.OptionalGroupNum);
                                item.AllNum = (item.NeedShowNum + item.NeedGroupNum + item.OptionalShowNum + item.OptionalGroupNum);
                                int alltotal = (item.NeedShowNumed + item.NeedGroupNumed + item.OptionalShowNumed + item.OptionalGroupNumed);
                                item.AllNumed = alltotal;
                                item.TotalRatio = GetComputeRatio(alltotal.ParseToDecimal(), item.AllNum);
                            }
                            obj.Data = listBooking;
                            obj.Total = pagination.TotalCount;
                        }
                    }
                }
                else
                {
                    /**** 高中
                     * 1：查询需要统计的班级
                     * 2：统计查询标准（考核数）。
                     * 3：获取数据
                     * ****/
                    var paramPlan = new PlanInfoListParam();
                    paramPlan.CourseId = param.CourseId;
                    paramPlan.GradeId = param.GradeId;
                    paramPlan.SchoolTerm = param.SchoolTerm;
                    paramPlan.SchoolYearStart = param.SchoolYearStart;
                    paramPlan.SchoolId = operatorinfo.UnitId;
                    paramPlan.CompulsoryType = param.CompulsoryType;
                    var listPlan = await planInfoService.GetList(paramPlan);
                    if (listPlan != null && listPlan.Count > 0)
                    {
                        // var listPlanStatic = listPlan.GroupBy(m => new { m.SchoolId, m.CourseId, m.GradeId, m.SchoolTerm, m.SchoolYearStart });
                        ExperimentBookingListParam paramBooking = new ExperimentBookingListParam();
                        paramBooking.CourseId = param.CourseId;
                        paramBooking.SchoolId = operatorinfo.UnitId;
                        paramBooking.StaticGradeId = param.GradeId ?? 0;
                        paramBooking.StaticSchoolTerm = param.SchoolTerm ?? 0;//根据实际计划来
                        paramBooking.StaticSchoolYearStart = param.SchoolYearStart ?? 0;  
                        paramBooking.IsShowClassTeacher = param.IsShowClassTeacher;
                        IsShowClassTeacher = param.IsShowClassTeacher;
                        paramBooking.Statuz = 100;
                        paramBooking.IsMain = 0;
                        paramBooking.IsEvaluate = 1;
                        paramBooking.CompulsoryType = param.CompulsoryType;
                        listBooking = await experimentBookingService.GetStaticClassGaoZhongPage(paramBooking, pagination);
                        if (listBooking != null && listBooking.Count > 0)
                        {
                            //获取统计数据
                            paramBooking = new ExperimentBookingListParam();
                            paramBooking.SchoolId = operatorinfo.UnitId;
                            paramBooking.SchoolStageId = param.SchoolStageId;
                            paramBooking.CourseId = param.CourseId;
                            paramBooking.GradeId = param.GradeId ?? 0;
                            paramBooking.StaticSchoolYearStart = param.SchoolYearStart ?? 0;
                            paramBooking.StaticSchoolTerm = param.SchoolTerm ?? 0;
                            paramBooking.IsEvaluate = 1;
                            paramBooking.CompulsoryType = param.CompulsoryType;
                            listBookingAll = await experimentBookingService.GetPlanAll(paramBooking);

                            listBooking.ForEach(m =>
                            {
                                m.ClassDesc = m.ClassName;
                                m.CourseName = courseName;
                                m.NeedShowNum = listPlan.Where(n => (n.ClassIdz ?? "").Contains(m.SchoolGradeClassId.ToString())).Sum(j => j.NeedShowNum);
                                m.NeedShowNumed = NeedShowNumed(listBookingAll, m, IsShowClassTeacher);
                                m.NeedGroupNum = listPlan.Where(n => (n.ClassIdz ?? "").Contains(m.SchoolGradeClassId.ToString())).Sum(j => j.NeedGroupNum);
                                m.NeedGroupNumed = NeedGroupNumed(listBookingAll, m, IsShowClassTeacher);
                                m.OptionalShowNum = listPlan.Where(n => (n.ClassIdz ?? "").Contains(m.SchoolGradeClassId.ToString())).Sum(j => j.OptionalShowNum);
                                m.OptionalShowNumed = OptionalShowNumed(listBookingAll, m, IsShowClassTeacher); 
                                m.OptionalGroupNum = listPlan.Where(n => (n.ClassIdz ?? "").Contains(m.SchoolGradeClassId.ToString())).Sum(j => j.OptionalGroupNum);
                                m.OptionalGroupNumed = OptionalGroupNumed(listBookingAll, m, IsShowClassTeacher);
                            });
                            foreach (var item in listBooking)
                            {
                                item.SourceType = param.SourceType ?? 0;
                                item.NeedShowNumRatio = GetComputeRatio(item.NeedShowNumed, item.NeedShowNum);
                                item.NeedGroupNumRatio = GetComputeRatio(item.NeedGroupNumed, item.NeedGroupNum);
                                item.OptionalShowNumRatio = GetComputeRatio(item.OptionalShowNumed, item.OptionalShowNum);
                                item.OptionalGroupNumRatio = GetComputeRatio(item.OptionalGroupNumed, item.OptionalGroupNum);
                                item.AllNum = (item.NeedShowNum + item.NeedGroupNum + item.OptionalShowNum + item.OptionalGroupNum);
                                int alltotal = (item.NeedShowNumed + item.NeedGroupNumed + item.OptionalShowNumed + item.OptionalGroupNumed);
                                item.AllNumed = alltotal;
                                item.TotalRatio = GetComputeRatio(alltotal.ParseToDecimal(), item.AllNum);
                            }
                            obj.Data = listBooking;
                            obj.Total = pagination.TotalCount;
                        }
                    }
                }
            }
            if (obj.Data==null)
            {
                obj.Data = new List<ExperimentBookingEntity>();
                obj.Total = 0;
            }
            obj.Tag = 1;
            return obj;
        }

        private int NeedShowNumed(List<ExperimentBookingStaticModel> list, ExperimentBookingEntity m, int isshowclassteacher)
        {
            int num = 0;
            if (list != null && m != null)
            {
                if (isshowclassteacher == 1)
                {
                    num = list.Where(n => m.SchoolGradeClassId == n.SchoolGradeClassId && n.IsNeedDo == 1 && n.ExperimentType == 1 && n.BaseModifierId == m.BaseModifierId).Count();
                }
                else
                {
                    num = list.Where(n => m.SchoolGradeClassId == n.SchoolGradeClassId && n.IsNeedDo == 1 && n.ExperimentType == 1).Count();
                }
            }
            return num;
        }
        private int NeedGroupNumed(List<ExperimentBookingStaticModel> list, ExperimentBookingEntity m, int isshowclassteacher)
        {
            int num = 0;
            if (list != null && m != null)
            {
                if (isshowclassteacher == 1)
                {
                    num = list.Where(n => m.SchoolGradeClassId == n.SchoolGradeClassId && n.IsNeedDo == 1 && n.ExperimentType == 2 && n.BaseModifierId == m.BaseModifierId).Count();
                }
                else
                {
                    num = list.Where(n => m.SchoolGradeClassId == n.SchoolGradeClassId && n.IsNeedDo == 1 && n.ExperimentType == 2).Count(); ;
                }
            }
            return num;
        }
        private int OptionalShowNumed(List<ExperimentBookingStaticModel> list, ExperimentBookingEntity m, int isshowclassteacher)
        {
            int num = 0;
            if (list != null && m != null)
            {
                if (isshowclassteacher == 1)
                {
                    num = list.Where(n => m.SchoolGradeClassId == n.SchoolGradeClassId && n.IsNeedDo == 2 && n.ExperimentType == 1 && n.BaseModifierId == m.BaseModifierId).Count();
                }
                else
                {
                    num = list.Where(n => m.SchoolGradeClassId == n.SchoolGradeClassId && n.IsNeedDo == 2 && n.ExperimentType == 1).Count();
                }
            }
            return num;
        }

        private int OptionalGroupNumed(List<ExperimentBookingStaticModel> list, ExperimentBookingEntity m, int isshowclassteacher)
        {
            int num = 0;
            if (list != null && m != null)
            {
                if (isshowclassteacher == 1)
                {
                    num = list.Where(n => m.SchoolGradeClassId == n.SchoolGradeClassId && n.IsNeedDo == 2 && n.ExperimentType == 2 && n.BaseModifierId == m.BaseModifierId).Count();
                }
                else
                {
                    num = list.Where(n => m.SchoolGradeClassId == n.SchoolGradeClassId && n.IsNeedDo == 2 && n.ExperimentType == 2).Count();
                }
            }
            return num;
        }
        private decimal GetComputeRatio(decimal divider, int dividend)
        {
            decimal ratio = 0;
            if (dividend > 0)
            {
                ratio = (divider / dividend) * 100;
            }
            else if (divider > 0)
            {
                ratio = 100;
            }
            return ratio;
        }

        public async Task<TData<ExperimentAttendStaticEntity>> GetEntity(long id)
        {
            TData<ExperimentAttendStaticEntity> obj = new TData<ExperimentAttendStaticEntity>();
            obj.Data = await experimentAttendStaticService.GetEntity(id);
            if (obj.Data != null)
            {
                obj.Tag = 1;
            }
            return obj;
        }

        /// <summary>
        /// 实验开出分析-区县端
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<TData<List<ExperimentAttendAnalysis>>> GetCountyExperimentAttendAnalysisList(ExperimentAttendStaticListParam param)
        {
            TData<List<ExperimentAttendAnalysis>> obj = new TData<List<ExperimentAttendAnalysis>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            if (operatorinfo == null)
            {
                obj.Tag = -1;
                obj.Message = "登录信息失效！";
                return obj;
            }
            param.CountyId = operatorinfo.UnitId;
            param.SetUserId = operatorinfo.UserId.Value;
            Pagination pagination = new Pagination();
            pagination.PageIndex = 1;
            pagination.PageSize = int.MaxValue;
            var attendList = await experimentAttendStaticService.GetPageList(param, pagination);
            var schoolList = attendList.OrderBy(a => a.GradeId).ToList().GroupBy(f => new { f.GradeId, f.GradeName, f.CourseId, f.CourseName });

            List<ExperimentAttendAnalysis> list = new List<ExperimentAttendAnalysis>();
            foreach (var item in schoolList)
            {
                list.Add(new ExperimentAttendAnalysis
                {
                    GradeId = item.Key.GradeId,
                    GradeName = item.Key.GradeName,
                    CourseId = item.Key.CourseId,
                    CourseName = item.Key.CourseName,
                    ExperimentAttendStaticList = attendList.Where(f => f.GradeId == item.Key.GradeId && f.GradeName == item.Key.GradeName && f.CourseId == item.Key.CourseId && f.CourseName == item.Key.CourseName).ToList()
                });
            }
            obj.Data = list;
            obj.Tag = 1;
            obj.Message = "查询成功";
            return obj;
        }

        /// <summary>
        /// 实验开出分析-市级端
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<TData<List<ExperimentAttendAnalysis>>> GetCityExperimentAttendAnalysisList(ExperimentAttendStaticListParam param)
        {
            TData<List<ExperimentAttendAnalysis>> obj = new TData<List<ExperimentAttendAnalysis>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            if (operatorinfo == null)
            {
                obj.Tag = -1;
                obj.Message = "登录信息失效！";
                return obj;
            }
            param.CityId = operatorinfo.UnitId;
            Pagination pagination = new Pagination();
            pagination.PageIndex = 1;
            pagination.PageSize = int.MaxValue;
            pagination.Sort = "SchoolYearStart";
            pagination.SortType = "ASC";
            var attendList = await experimentAttendStaticService.GetCountyPageList(param, pagination);
            var countyList = attendList.OrderBy(a => a.GradeId).ToList().GroupBy(f => new { f.GradeId, f.GradeName, f.CourseId, f.CourseName });

            List<ExperimentAttendAnalysis> list = new List<ExperimentAttendAnalysis>();
            foreach (var item in countyList)
            {
                list.Add(new ExperimentAttendAnalysis
                {
                    GradeId = item.Key.GradeId,
                    GradeName = item.Key.GradeName,
                    CourseId = item.Key.CourseId,
                    CourseName = item.Key.CourseName,
                    ExperimentAttendStaticList = attendList.Where(f => f.GradeId == item.Key.GradeId && f.GradeName == item.Key.GradeName && f.CourseId == item.Key.CourseId && f.CourseName == item.Key.CourseName).ToList()
                });
            }
            obj.Data = list;
            obj.Tag = 1;
            obj.Message = "查询成功";
            return obj;
        }

        /// <summary>
        /// 实验开出分析按照区县查询-市级端
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<TData<List<ExperimentAttendAnalysis>>> GetCityExperimentAttendAnalysisByCountyJson(ExperimentAttendStaticListParam param)
        {
            TData<List<ExperimentAttendAnalysis>> obj = new TData<List<ExperimentAttendAnalysis>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            if (operatorinfo == null)
            {
                obj.Tag = -1;
                obj.Message = "登录信息失效！";
                return obj;
            }

            Pagination pagination = new Pagination();
            pagination.PageIndex = 1;
            pagination.PageSize = int.MaxValue;
            pagination.Sort = "SchoolYearStart";
            pagination.SortType = "ASC";
            var attendList = await experimentAttendStaticService.GetPageList(param, pagination);
            var countyList = attendList.OrderBy(a => a.GradeId).ToList().GroupBy(f => new { f.GradeId, f.GradeName, f.CourseId, f.CourseName });

            List<ExperimentAttendAnalysis> list = new List<ExperimentAttendAnalysis>();
            foreach (var item in countyList)
            {
                list.Add(new ExperimentAttendAnalysis
                {
                    GradeId = item.Key.GradeId,
                    GradeName = item.Key.GradeName,
                    CourseId = item.Key.CourseId,
                    CourseName = item.Key.CourseName,
                    ExperimentAttendStaticList = attendList.Where(f => f.GradeId == item.Key.GradeId && f.GradeName == item.Key.GradeName && f.CourseId == item.Key.CourseId && f.CourseName == item.Key.CourseName).ToList()
                });
            }
            obj.Data = list;
            obj.Tag = 1;
            obj.Message = "查询成功";
            return obj;
        }

        /// <summary>
        /// 获取区县实验教学达标看板数据
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<TData<ExperimentAttendKanban>> GetCountyExperimentQualifyList(ExperimentAttendStaticListParam param)
        {
            TData<ExperimentAttendKanban> obj = new TData<ExperimentAttendKanban>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            if (operatorinfo == null)
            {
                obj.Tag = -1;
                obj.Message = "登录信息失效！";
                return obj;
            }
            //根据学段获取对应学科
            var courseList = await staticDictionaryService.GetChildToList(new StaticDictionaryListParam { Pid = param.SchoolStageId ?? 0, TypeCode = DicTypeCodeEnum.Course.ParseToInt().ParseToString(), Nature = 1 });
            //根据学段获取对应年级
            var gradeList = await staticDictionaryService.GetChildList(new StaticDictionaryListParam { Pid = param.SchoolStageId ?? 0, TypeCode = DicTypeCodeEnum.Grade.ParseToInt().ParseToString() });

            List<StaticDictionaryEntity> gradeDataList = new List<StaticDictionaryEntity>();
            foreach (var course in courseList)
            {
                //根据学科获取对应年级
                var gradeCourseList = await staticDictionaryService.GetChildList(new StaticDictionaryListParam { Pid = course.DictionaryId ?? 0, TypeCode = DicTypeCodeEnum.Grade.ParseToInt().ParseToString() });
                var sxz = from p in gradeList join u in gradeCourseList on p.DictionaryId equals u.DictionaryId select p;
                foreach (var item in sxz)
                {
                    gradeDataList.Add(item);
                }
            }
            gradeDataList = gradeDataList.DistinctBy(m=>m.DictionaryId).OrderBy(t => t.DictionaryId).ToList();
            courseList = courseList.OrderBy(t => t.DictionaryId).ToList();

            //获取实验达标数据
            param.CountyId = operatorinfo.UnitId;
            var list = await experimentAttendStaticService.GetCountyPageList(param, new Pagination { PageSize = int.MaxValue, Sort = " GradeId ASC ,CourseId ASC " });

            //组装看板格式数据
            obj.Data = new ExperimentAttendKanban();
            obj.Data.GradeNameList = gradeDataList.Select(t => t.DicName).ToList();
            obj.Data.CourseNameList = courseList.Select(t => t.DicName).ToList();
            obj.Data.ExperimentCourseRatioKanbanList = new List<ExperimentCourseRatioKanban>();
            foreach (var courseName in obj.Data.CourseNameList)
            {
                ExperimentCourseRatioKanban experimentCourseRatioKanban = new ExperimentCourseRatioKanban();
                experimentCourseRatioKanban.CourseName = courseName;

                List<decimal> ratioList = new List<decimal>();
                foreach (var gradeName in obj.Data.GradeNameList)
                {
                    var ratio = list.Find(t => t.GradeName.Equals(gradeName) && t.CourseName.Equals(courseName));
                    ratioList.Add(ratio != null ? Math.Round(ratio.TotalRatio, 2, MidpointRounding.AwayFromZero) : -1);
                }
                experimentCourseRatioKanban.Ratio = ratioList;
                obj.Data.ExperimentCourseRatioKanbanList.Add(experimentCourseRatioKanban);
            }
            obj.Tag = 1;
            obj.Message = "查询成功";
            return obj;
        }
        #endregion

        #region 实验开出率（看板“区县”，“市级”）
 
        /// <summary>
        /// 实验开出排行 （看板）
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        #endregion

        #region 提交数据
        public async Task<TData> InitDataForm(ExperimentAttendStaticInputModel model)
        {
            TData obj = new TData();

            var schoolTermObj = await schoolTermBLL.GetDefaultSchoolTerm();
            if (schoolTermObj.Tag == 1)
            {
                //判断不能更新覆盖之前学年学期的历史数据
                if (model.SchoolYearStart < schoolTermObj.Data.SchoolYearStart
                    || (model.SchoolYearStart == schoolTermObj.Data.SchoolYearStart && model.SchoolTerm < schoolTermObj.Data.SchoolTerm))
                {
                    obj.Tag = 0;
                    obj.Message = "历史数据不可覆盖更新！";
                    return obj;
                }
            }

            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            //if (operatorinfo != null)
            //{
            //    if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
            //    {
            //        model.SchoolId = operatorinfo.UnitId ?? 0;
            //        //await experimentAttendStaticService.InitDataForm(model);
            //        await ExperimentAttendStatic_Init(model);
            //    }
            //    else if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
            //    {
            //        var schoolList = await unitRelationService.GetList(new UnitRelationListParam
            //        {
            //            ExtensionType = 3,
            //            UnitId = operatorinfo.UnitId ?? 0
            //        });
            //        foreach (var school in schoolList)
            //        {
            //            model.SchoolId = school.ExtensionObjId ?? 0;
            //            //await experimentAttendStaticService.InitDataForm(model);
            //            await ExperimentAttendStatic_Init(model);
            //        }
            //    }
            //}
            obj.Tag = 1;
            return obj;
        }
        #endregion

        #region 私有方法
        #endregion

        #region 实验统计达标初始化汇总数据

        public async Task<TData> ExperimentAttendStatic_Delete(ExperimentAttendStaticInputModel model)
        {
            TData r = new TData();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);

            //学校轨数、学校属性、
            SchoolExtensionListParam paramSchoolExt = new SchoolExtensionListParam();
            paramSchoolExt.UnitId = model.SchoolId;
            var listSchoolExt = await schoolExtensionService.GetList(paramSchoolExt);
            if (!(listSchoolExt != null && listSchoolExt.Count > 0))
            {
                r.Tag = 0;
                r.Message = "学校扩展信息属性不存在，请先完善学校信息。";
                return r;
            }

            var listStage = UnitBLL.GetSchoolStageList(listSchoolExt[0].SchoolProp ?? 0);
            if (!(listStage != null && listStage.Count > 0))
            {
                r.Tag = 0;
                r.Message = "学校扩展信息属性不存在，请先完善学校信息。";
                return r;
            }
            var countyList = await unitRelationService.GetList(new UnitRelationListParam
            {
                ExtensionType = 3,
                ExtensionObjId = model.SchoolId
            });
            if (!(countyList != null && countyList.Count > 0))
            {
                r.Tag = 0;
                r.Message = "学校信息异常，没有配置对应所属区县。";
                return r;
            }
            //删除学校信息。	DELETE FROM bn_FunRoomAttendStatic WHERE SchoolId = @SchoolId 
            await experimentAttendStaticService.Deletes(model);
             
            return r;
        }
        /// <summary>
        /// 处理产生配置，删除考核结果
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public async Task<TData> DeleteStaticResult(TextbookVersionParameterEntity entity)
        {
            TData obj = new TData();
            if (entity != null)
            {
                ExperimentAttendStaticListParam param = new ExperimentAttendStaticListParam();
                param.CourseId = entity.CourseId;
                param.GradeId = entity.GradeId;
                param.SchoolTerm = entity.SchoolTerm;
                param.CountyId = entity.CountyId;
                var list = await experimentAttendStaticService.GetPageList(param, new Pagination() { PageSize = int.MaxValue });
                if (list != null && list.Count > 0)
                {
                    await experimentAttendStaticService.DeleteForm(string.Join(",", list.Select(m => m.Id)));
                }
            }
            return obj;
        }

        /// <summary>
        /// 高中删除统计达标结果（存在其他班级，则禁止调用）
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public async Task<TData> DeleteGaoZhongPlanResult(PlanInfoEntity entity)
        {
            TData obj = new TData();
            if (entity != null)
            {
                ExperimentAttendStaticListParam param = new ExperimentAttendStaticListParam();
                param.CourseId = entity.CourseId;
                param.GradeId = entity.GradeId;
                param.SchoolTerm = entity.SchoolTerm;
                param.CountyId = entity.CountyId;
                param.SchoolId = entity.SchoolId;
                var list = await experimentAttendStaticService.GetPageList(param, new Pagination() { PageSize = int.MaxValue });
                if (list != null && list.Count > 0)
                {
                    await experimentAttendStaticService.DeleteForm(string.Join(",", list.Select(m => m.Id)));
                }
            }
            return obj;
        }

        /// <summary>
        /// 更新达标结果。
        /// </summary>
        /// <param name="entity"></param>
        /// <param name="ids"></param>
        /// <param name="sourcepath">
        /// 1:达标参数设置页面点击更新  
        /// 2：实验版本修改  （没有学校Id）
        /// 3:清单修改更新基础版本数量   （没有学校Id）
        /// 4:编制计划的时候调用  
        /// 5：班级修改的时候调用(更新学校轨数的时候调用，实体只有学校Id) 
        /// 6 登记、撤销的时候调用 
        /// 7:校本实验修改</param>
        /// <remarks>
        /// 1：更新实验的时候更新达标结果，（更新达标数量的时候更新，是否考核）
        /// 2：更新班级，添加班级、删除班级，轨数会变，结果要重新计算。（先更新指标数）
        /// 3：登记实验的时候，如果实验是需要评估的则计算更新。
        /// 4：版本参数修改、修改清单，更新编制计划、修改
        /// 
        /// 
        /// 调用来源
        /// 1：【达标参数设置页面】，点击【更新达标结果】，根据Ids，如果没有，则为全部
        /// 
        /// 
        /// 1：参数配置，选择更新（更新的是考核的值）
        /// 2：更新实验清单，添加、修改，是否考核数量的变动（更新的是达标结果的值）
        /// 3：更新计划实验数量、。（更新的是高中考核数据的值。）
        /// 4：登记实验，撤销登记（更新的是达标结果的值。）
        /// 5：删除添加班级，更新考核数量。
        /// 
        /// 
        /// 5：删除、创建编辑，学校轨数会变更，小学、初中会修改对应的年级。高中处理相关的计划
        /// 
        /// 注：4、5、6调用的时候，考虑校本实验的问题。
        /// </remarks>
        /// <returns></returns>
        public async Task<TData> SaveStaticResult(ExperimentBookingEntity entity, string ids, int optsource = 1)
        {
            TData r = new TData();
            /***
             * 两种更新方式，1：管理点击更新，根据选择或者更新所有； 2：登记和删除实验（撤销登记）
             * 1：根据登记实验实体Entiy，判断，
             * 2：根据传递来的选择参数集合
             */

            long schoolid = 0;
            int gradeid = 0;
            int courseid = 0;
            int schoolterm = 0;
            int schoolYearStart = DateTime.Now.Year;

            //处理学校列表
            #region 处理学校查询条件
            if (entity != null && (optsource == 4 || optsource == 5 || optsource == 6 || optsource == 7))
            {
                schoolid = entity.SchoolId;
            }
            //获取学校集合
            var paramSchool = new UnitListParam();
            paramSchool.UnitType = UnitTypeEnum.School.ParseToInt();
            paramSchool.Id = schoolid;
            List<UnitEntity> schoolList = await unitService.GetSchoolList(paramSchool);
            if (schoolList == null || schoolList.Count == 0)
            {
                r.Tag = 0;
                r.Message = "没查询到需要更新的单位信息。";
                return r;
            }
            #endregion

            #region 处理学科、年级查询条件
            if (entity != null && (optsource == 2 || optsource == 3 || optsource == 4 || optsource == 6 || optsource == 7))
            {
                //获取学校
                gradeid = entity.GradeId;
                courseid = entity.CourseId;
                schoolterm = entity.SchoolTerm;
            }
            #endregion

            #region 处理学期条件

            //获取学年、学期
            if (entity == null || entity.SchoolYearStart == 0)
            {
                var entitySchoolTerm = await schoolTermBLL.GetDefaultSchoolTerm();
                if (entitySchoolTerm != null)
                {
                    schoolYearStart = entitySchoolTerm.Data.SchoolYearStart;
                    schoolterm = entitySchoolTerm.Data.SchoolTerm;
                    if (entity != null && entity.SchoolTerm > 0)
                    {
                        schoolterm = entity.SchoolTerm;
                    }
                }
            }
            else
            {
                schoolYearStart = entity.SchoolYearStart;
                schoolterm = entity.SchoolTerm;
            }

            #endregion

            ExperimentAttendStaticListParam paramStatic = new ExperimentAttendStaticListParam();
            paramStatic.GradeId = gradeid;
            paramStatic.CourseId = courseid;
            paramStatic.SchoolId = schoolid;
            paramStatic.SchoolYearStart = schoolYearStart;
            paramStatic.SchoolTerm = schoolterm;
            var listStatic = await experimentAttendStaticService.GetList(paramStatic);
            if (optsource != 7)
            {
                if (gradeid < GradeEnum.GaoYi.ParseToInt())
                {
                    //获取选择或者所有的达标参数
                    TextbookVersionListParameterListParam paramVersion = new TextbookVersionListParameterListParam();
                    paramVersion.GradeId = gradeid;
                    paramVersion.CourseId = courseid;
                    paramVersion.SchoolTerm = schoolterm;
                    paramVersion.Ids = ids;
                    var listVersion = await textbookVersionParameterService.GetList(paramVersion);
                    if (listVersion == null || listVersion.Count == 0)
                    {
                        r.Tag = 0;
                        r.Message = "没查询到需要更新的参数配置信息。";
                        return r;
                    }
                    ExperimentBookingListParam paramBooking = new ExperimentBookingListParam();
                    //目录
                    foreach (var school in schoolList)
                    {
                        //学段
                        List<int> listStage = UnitBLL.GetSchoolStageList(school.SchoolProp ?? 0);
                        //获取汇总数据(按个学校)
                        if (courseid > 0)
                        {
                            paramBooking.CourseId = courseid;
                        }
                        paramBooking.SchoolId = school.Id;
                        paramBooking.Statuz = 100;
                        paramBooking.IsMain = 0;
                        paramBooking.IsEvaluate = 1;
                        //var listBooking = await experimentBookingService.GetPlanAll(paramBooking);
                        var listCatalogBooking = await experimentBookingService.GetAll(paramBooking);
                        //获取年级数据
                        SchoolGradeClassListParam paramGradeClass = new SchoolGradeClassListParam();
                        paramGradeClass.UnitId = school.Id;
                        paramGradeClass.IsGraduate = 0;
                        paramGradeClass.Statuz = StatusEnum.Yes.ParseToInt();
                        var listGradeClass = await schoolGradeClassService.GetList(paramGradeClass);
                        if (listGradeClass == null || listGradeClass.Count == 0)
                        {
                            continue;
                        }
                        foreach (var version in listVersion)
                        {

                            if (school.CountyId == version.CountyId)
                            {
                                //过滤高中，高中必须要做计划。
                                if (listStage.Contains(version.SchoolStage))
                                {
                                    int classnum = 0;
                                    var listTempClass = listGradeClass.Where(m => m.GradeId == version.GradeId);
                                    if (listTempClass != null && listTempClass.Count() > 0)
                                    {
                                        classnum = listTempClass.Count();
                                    }
                                    if (version.GradeId < GradeEnum.GaoYi.ParseToInt())
                                    {
                                        //目录
                                        if (listCatalogBooking != null && listCatalogBooking.Count > 0)
                                        {
                                            //根据配置参数统计
                                            listCatalogBooking.ForEach(m => m.StaticSchoolTerm = m.SchoolTerm);
                                        }
                                        var entityCatalogAttendStatic = SetEntity(listStatic, listCatalogBooking, version, school.Id ?? 0, schoolYearStart, classnum, 2);
                                        experimentAttendStaticService.SaveForm(entityCatalogAttendStatic);

                                        ////计划
                                        //if (listBooking!=null && listBooking.Count > 0)
                                        //{
                                        //    //根据配置参数统计
                                        //    listBooking.ForEach(m => m.StaticSchoolTerm = m.SchoolTerm);
                                        //}
                                        //var entityAttendStatic = SetEntity(listStatic, listBooking, version, school.Id ?? 0, schoolYearStart, classnum, 1);
                                        //experimentAttendStaticService.SaveForm(entityAttendStatic);
                                    }
                                }
                            }
                        }
                    }
                }
                else
                {
                    /***更新高中的达标结果（高中实验，只能通过编制计划做实验）
                     * 1：登记为高中的时候只更新高中。
                     * 2：编制计划的时候，为高中的时候更新。
                     * 3: 班级添加、修改、删除的时候更新
                     * 3：修改实验清单的时候，存在编制计划中更新。
                     */

                    //获取选择或者所有的达标参数
                    var paramPlan = new PlanInfoListParam();
                    paramPlan.CourseId = courseid;
                    paramPlan.GradeId = gradeid;
                    paramPlan.SchoolTerm = schoolterm;
                    paramPlan.SchoolYearStart = schoolYearStart;
                    paramPlan.SchoolId = schoolid;
                    if (entity != null && entity.PlanInfoId > 0)
                    {
                        paramPlan.Id = entity.PlanInfoId;
                    }
                    var listPlan = await planInfoService.GetList(paramPlan);
                    if (listPlan != null && listPlan.Count > 0)
                    {
                        var listPlanStatic = listPlan.GroupBy(m => new { m.SchoolId, m.CourseId, m.GradeId, m.SchoolTerm, m.SchoolYearStart,m.CompulsoryType });
                        ExperimentBookingListParam paramBooking = new ExperimentBookingListParam();
                        foreach (var school in schoolList)
                        {
                            //获取汇总数据(按个学校)
                            if (courseid > 0)
                            {
                                paramBooking.CourseId = courseid;
                            }
                            paramBooking.SchoolId = school.Id;
                            paramBooking.Statuz = 100;
                            paramBooking.IsMain = 0;
                            paramBooking.IsEvaluate = 1;
                            var listBooking = await experimentBookingService.GetPlanAll(paramBooking);

                            foreach (var plan in listPlanStatic)
                            {
                                //高中计划
                                var entityAttendStatic = SetEntityGaoZhong(listStatic, listBooking, plan.ToList(), school.Id ?? 0, schoolYearStart);
                                experimentAttendStaticService.SaveForm(entityAttendStatic);
                            }
                        }
                    }
                }
            }
            /****
             *   SourcePath = 2 登记实验。
             *   7：修改、添加、删除校本实验的时候添加
             */
            if (optsource == 7 || (entity != null && entity.SourcePath == 2))
            {
                //校本实验统计
                var paramSchoolExperiment = new SchoolExperimentListParam();
                paramSchoolExperiment.SchoolId = schoolid;
                paramSchoolExperiment.GradeId = gradeid;
                paramSchoolExperiment.CourseId = courseid;
                paramSchoolExperiment.SchoolTerm = schoolterm;
                paramSchoolExperiment.Statuz = 1;
                var listParamer = await schoolExperimentService.GetList(paramSchoolExperiment);
                if (listParamer == null)
                {
                    r.Tag = 0;
                    r.Message = "更新失败，查询校本实验信息失败。";
                    return r;
                }
                ExperimentBookingListParam paramBooking = new ExperimentBookingListParam();
                paramBooking.SchoolId = schoolid;
                paramBooking.GradeId = gradeid;
                paramBooking.CourseId = courseid;
                paramBooking.SchoolTerm = schoolterm;
                paramBooking.SchoolYearStart = schoolYearStart;
                var listBooking = await experimentBookingService.GetXiaoBenAll(paramBooking);
                if (listBooking == null || listBooking.Count == 0)
                {
                    r.Tag = 0;
                    r.Message = "更新失败，班级信息不存在。";
                    return r;
                }
                var listSchoolExperiment = listParamer.GroupBy(m => new { m.CourseId, m.GradeId, m.SchoolTerm, m.SchoolYearStart });

                //校本实验
                //获取年级数据
                SchoolGradeClassListParam paramGradeClass = new SchoolGradeClassListParam();
                paramGradeClass.UnitId = schoolid;
                paramGradeClass.IsGraduate = 0;
                paramGradeClass.GradeId = gradeid;
                paramGradeClass.Statuz = StatusEnum.Yes.ParseToInt();
                var listGradeClass = await schoolGradeClassService.GetList(paramGradeClass);
                if (listGradeClass == null || listGradeClass.Count == 0)
                {
                    r.Tag = 0;
                    r.Message = "更新失败，班级信息不存在。";
                    return r;
                }
                foreach (var item in listSchoolExperiment)
                {
                    var listSchoolExperimentTemp = item.ToList();
                    int classnum = listGradeClass.Where(m => m.GradeId == listSchoolExperimentTemp.FirstOrDefault().GradeId).Count();
                    var entityAttendStatic = SetEntityXiaoBen(listStatic, listBooking, listSchoolExperimentTemp, schoolid, schoolYearStart, classnum);
                    experimentAttendStaticService.SaveForm(entityAttendStatic);
                }
            }

            r.Tag = 1;
            r.Message = "更新成功。";
            return r;
        }

        /// <summary>
        /// 获取更新后的统计数据
        /// </summary>
        /// <param name="listStatic">统计数据集合</param>
        /// <param name="listBooking">预约登记数据集合</param>
        /// <param name="version">版本参数实体信息</param>
        /// <param name="schoolid">学校Id</param>
        /// <param name="schoolyearstart">学年</param>
        /// <param name="classnum">班级数量</param>
        /// <param name="sourcetype">项目来源类型SourceTypeEnum.ExperimentPlan 1:计划   2：目录</param>
        /// <returns></returns>
        private ExperimentAttendStaticEntity SetEntity(List<ExperimentAttendStaticEntity> listStatic, List<ExperimentBookingStaticModel> listBooking, TextbookVersionParameterEntity version, long schoolid, int schoolyearstart, int classnum, int sourcetype)
        {
            var entityAttendStatic = new ExperimentAttendStaticEntity();
            var listTemp = listStatic.Where(m => m.SourceType == sourcetype && m.DataStorageType == 1 &&
            m.SchoolId == schoolid &&
            m.CourseId == version.CourseId &&
            m.GradeId == version.GradeId &&
            m.SchoolYearStart == schoolyearstart &&
            m.SchoolTerm == version.SchoolTerm);
            if (listTemp != null && listTemp.Count() > 0)
            {
                entityAttendStatic = listTemp.FirstOrDefault();
            }
            else
            {
                entityAttendStatic.DataStorageType = 1;
                entityAttendStatic.SchoolId = schoolid;
                entityAttendStatic.CourseId = version.CourseId;
                entityAttendStatic.GradeId = version.GradeId;
                entityAttendStatic.SchoolYearStart = schoolyearstart;
                entityAttendStatic.SchoolYearEnd = schoolyearstart + 1;
                entityAttendStatic.SchoolTerm = version.SchoolTerm;
                entityAttendStatic.SourceType = sourcetype;
            }
            entityAttendStatic.CompulsoryType = 0;
            entityAttendStatic.NeedShowNum = version.StandardNeedShowNum * classnum;
            entityAttendStatic.NeedGroupNum = version.StandardNeedGroupNum * classnum;
            entityAttendStatic.OptionalShowNum = version.StandardOptionalShowNum * classnum;
            entityAttendStatic.OptionalGroupNum = version.StandardOptionalGroupNum * classnum;

            entityAttendStatic.NeedShowNumed = GetNeedShowNumedNum(listBooking, schoolyearstart, version);
            entityAttendStatic.NeedGroupNumed = GetNeedGroupNumedNum(listBooking, schoolyearstart, version);
            entityAttendStatic.OptionalShowNumed = GetOptionalShowNumedNum(listBooking, schoolyearstart, version);
            entityAttendStatic.OptionalGroupNumed = GetOptionalGroupNumedNum(listBooking, schoolyearstart, version);
            return entityAttendStatic;
        }


        /// <summary>
        /// 获取更新后的统计数据（高中）
        /// </summary>
        /// <param name="listStatic">统计数据集合</param>
        /// <param name="listBooking">预约登记数据集合必须不等于null</param>
        /// <param name="planinfos">版本参数实体信息,必须大于1条</param>
        /// <param name="schoolid">学校Id</param>
        /// <param name="schoolyearstart">学年</param>
        /// <remarks>SourceType:1:选修科目班级   2:非选修科目班级</remarks>
        /// <returns></returns>
        private ExperimentAttendStaticEntity SetEntityGaoZhong(List<ExperimentAttendStaticEntity> listStatic, List<ExperimentBookingStaticModel> listBooking, List<PlanInfoEntity> planinfos, long schoolid, int schoolyearstart)
        {
            if (planinfos == null || planinfos.Count < 1)
            {
                return null;
            }
            TextbookVersionParameterEntity version = new TextbookVersionParameterEntity();
            version.CourseId = planinfos[0].CourseId;
            version.GradeId = planinfos[0].GradeId;
            version.SchoolTerm = planinfos[0].SchoolTerm;
            int compulsoryType = planinfos[0].CompulsoryType ?? ClassCompulsoryTypeEnum.NonSelect.ParseToInt();//如果为空为null则为非选修班级
            //这里借用SourceType 存储  compulsoryType 的值
            var entityAttendStatic = new ExperimentAttendStaticEntity();
            var listTemp = listStatic.Where(m => m.CompulsoryType == compulsoryType && m.DataStorageType == 2 &&
            m.SchoolId == schoolid &&
            m.CourseId == planinfos[0].CourseId &&
            m.GradeId == planinfos[0].GradeId &&
            m.SchoolYearStart == schoolyearstart &&
            m.SchoolTerm == planinfos[0].SchoolTerm);
            if (listTemp != null && listTemp.Count() > 0)
            {
                entityAttendStatic = listTemp.FirstOrDefault();
            }
            else
            {
                entityAttendStatic.DataStorageType = 2;
                entityAttendStatic.SchoolId = schoolid;
                entityAttendStatic.CourseId = version.CourseId;
                entityAttendStatic.GradeId = version.GradeId;
                entityAttendStatic.SchoolYearStart = schoolyearstart;
                entityAttendStatic.SchoolYearEnd = schoolyearstart + 1;
                entityAttendStatic.SchoolTerm = version.SchoolTerm;
                entityAttendStatic.SourceType = 1;//都是计划来源
                entityAttendStatic.CompulsoryType = compulsoryType;
            }
          
            entityAttendStatic.NeedShowNum = planinfos.Sum(m => m.NeedShowNum * m.ClassIdz.Split(",").Count());
            entityAttendStatic.NeedGroupNum = planinfos.Sum(m => m.NeedGroupNum * m.ClassIdz.Split(",").Count());
            entityAttendStatic.OptionalShowNum = planinfos.Sum(m => m.OptionalShowNum * m.ClassIdz.Split(",").Count());
            entityAttendStatic.OptionalGroupNum = planinfos.Sum(m => m.OptionalGroupNum * m.ClassIdz.Split(",").Count());
            string classids = string.Join(",", planinfos.Select(m => m.ClassIdz ?? ""));
            entityAttendStatic.NeedShowNumed = GetNeedShowNumedNum(listBooking, schoolyearstart, version, compulsoryType, classids);
            entityAttendStatic.NeedGroupNumed = GetNeedGroupNumedNum(listBooking, schoolyearstart, version, compulsoryType, classids);
            entityAttendStatic.OptionalShowNumed = GetOptionalShowNumedNum(listBooking, schoolyearstart, version, compulsoryType, classids);
            entityAttendStatic.OptionalGroupNumed = GetOptionalGroupNumedNum(listBooking, schoolyearstart, version, compulsoryType, classids);
            return entityAttendStatic;
        }
        /// <summary>
        /// 获取更新后的统计数据(校本)
        /// </summary>
        /// <param name="listStatic">统计数据集合</param>
        /// <param name="listBooking">预约登记数据集合必须不等于null</param>
        /// <param name="planinfos">版本参数实体信息,必须大于1条</param>
        /// <param name="schoolid">学校Id</param>
        /// <param name="schoolyearstart">学年</param>
        /// <param name="classnum">班级数量</param>
        /// <param name="sourcetype">项目来源类型SourceTypeEnum.ExperimentPlan 1:计划   2：目录</param>
        /// <returns></returns>
        private ExperimentAttendStaticEntity SetEntityXiaoBen(List<ExperimentAttendStaticEntity> listStatic, List<ExperimentBookingStaticModel> listBooking, List<SchoolExperimentEntity> experiments, long schoolid, int schoolyearstart, int classnum)
        {
            TextbookVersionParameterEntity version = new TextbookVersionParameterEntity();
            version.CourseId = experiments[0].CourseId;
            version.GradeId = experiments[0].GradeId;
            version.SchoolTerm = experiments[0].SchoolTerm;

            var entityAttendStatic = new ExperimentAttendStaticEntity();
            var listTemp = listStatic.Where(m => m.SourceType == 3 && m.DataStorageType == 2 &&
            m.SchoolId == schoolid &&
            m.CourseId == experiments[0].CourseId &&
            m.GradeId == experiments[0].GradeId &&
            m.SchoolYearStart == schoolyearstart &&
            m.SchoolTerm == experiments[0].SchoolTerm);
            if (listTemp != null && listTemp.Count() > 0)
            {
                entityAttendStatic = listTemp.FirstOrDefault();
            }
            else
            {
                entityAttendStatic.DataStorageType = 2;
                entityAttendStatic.SchoolId = schoolid;
                entityAttendStatic.CourseId = version.CourseId;
                entityAttendStatic.GradeId = version.GradeId;
                entityAttendStatic.SchoolYearStart = schoolyearstart;
                entityAttendStatic.SchoolYearEnd = schoolyearstart + 1;
                entityAttendStatic.SchoolTerm = version.SchoolTerm;
                entityAttendStatic.SourceType = 3;
            }
            entityAttendStatic.CompulsoryType = 0;
            entityAttendStatic.NeedShowNum = experiments.Where(m => m.IsNeedDo == 1 && m.ExperimentType == 1).Count() * classnum;
            entityAttendStatic.NeedGroupNum = experiments.Where(m => m.IsNeedDo == 1 && m.ExperimentType == 2).Count() * classnum;
            entityAttendStatic.OptionalShowNum = experiments.Where(m => m.IsNeedDo == 2 && m.ExperimentType == 1).Count() * classnum;
            entityAttendStatic.OptionalGroupNum = experiments.Where(m => m.IsNeedDo == 2 && m.ExperimentType == 2).Count() * classnum;

            entityAttendStatic.NeedShowNumed = GetNeedShowNumedNum(listBooking, schoolyearstart, version);
            entityAttendStatic.NeedGroupNumed = GetNeedGroupNumedNum(listBooking, schoolyearstart, version);
            entityAttendStatic.OptionalShowNumed = GetOptionalShowNumedNum(listBooking, schoolyearstart, version);
            entityAttendStatic.OptionalGroupNumed = GetOptionalGroupNumedNum(listBooking, schoolyearstart, version);
            return entityAttendStatic;
        }

        /// <summary>
        /// 已做必做演示实验数量
        /// </summary>
        /// <param name="listBooking">实验登记列表</param>
        /// <param name="schoolyearstart">统计年度</param>
        /// <param name="version">统计参数版本信息</param>
        /// <param name="compulsorytype">1： 必修 2：选修</param>
        /// <param name="classids">班级id集合</param>
        /// <returns></returns>
        public int GetNeedShowNumedNum(List<ExperimentBookingStaticModel> listBooking, int schoolyearstart, TextbookVersionParameterEntity version, int compulsorytype = 0, string classids = "")
        {
            int num = 0;
            if (listBooking != null && listBooking.Count > 0)
            {
                var listBookingTemp = listBooking.Where(m => m.IsEvaluate == 1 &&
                m.IsNeedDo == 1 &&
                m.ExperimentType == 1 &&
                m.StaticSchoolYearStart == schoolyearstart &&
                m.StaticSchoolTerm == version.SchoolTerm &&
                m.StaticGradeId == version.GradeId &&
                m.CourseId == version.CourseId
                );
                if (compulsorytype > 0 && listBookingTemp != null)
                {
                    listBookingTemp = listBookingTemp.Where(m => m.CompulsoryType == compulsorytype && classids.Contains(m.SchoolGradeClassId.ToString()));
                }
                if (listBookingTemp != null && listBookingTemp.Count() > 0)
                {
                    num = listBookingTemp.Count();
                }
            }
            return num;
        }

        /// <summary>
        /// 已做必做分组实验数量
        /// </summary>
        /// <param name="listBooking">实验登记列表</param>
        /// <param name="schoolyearstart">统计年度</param>
        /// <param name="version">统计参数版本信息</param>
        /// <param name="compulsorytype">1： 必修 2：选修</param>
        /// <param name="classids">班级id集合</param>
        /// <returns></returns>
        public int GetNeedGroupNumedNum(List<ExperimentBookingStaticModel> listBooking, int schoolyearstart, TextbookVersionParameterEntity version, int compulsorytype = 0, string classids = "")
        {
            int num = 0;
            if (listBooking != null && listBooking.Count > 0)
            {
                var listBookingTemp = listBooking.Where(m => m.IsEvaluate == 1 &&
                m.IsNeedDo == 1 &&
                m.ExperimentType == 2 &&
                m.StaticSchoolYearStart == schoolyearstart &&
                m.StaticSchoolTerm == version.SchoolTerm &&
                m.StaticGradeId == version.GradeId &&
                m.CourseId == version.CourseId
                );
                if (compulsorytype > 0 && listBookingTemp != null)
                {
                    listBookingTemp = listBookingTemp.Where(m => m.CompulsoryType == compulsorytype && classids.Contains(m.SchoolGradeClassId.ToString()));
                }
                if (listBookingTemp != null && listBookingTemp.Count() > 0)
                {
                    num = listBookingTemp.Count();
                }
            }
            return num;
        }

        /// <summary>
        /// 已做选做演示实验数量
        /// </summary>
        /// <param name="listBooking">实验登记列表</param>
        /// <param name="schoolyearstart">统计年度</param>
        /// <param name="version">统计参数版本信息</param>
        /// <param name="compulsorytype">1： 必修 2：选修</param>
        /// <param name="classids">班级id集合</param>
        /// <returns></returns>
        public int GetOptionalShowNumedNum(List<ExperimentBookingStaticModel> listBooking, int schoolyearstart, TextbookVersionParameterEntity version, int compulsorytype = 0, string classids = "")
        {
            int num = 0;
            if (listBooking != null && listBooking.Count > 0)
            {
                var listBookingTemp = listBooking.Where(m => m.IsEvaluate == 1 &&
                m.IsNeedDo == 2 &&
                m.ExperimentType == 1 &&
                m.StaticSchoolYearStart == schoolyearstart &&
                m.StaticSchoolTerm == version.SchoolTerm &&
                m.StaticGradeId == version.GradeId &&
                m.CourseId == version.CourseId
                );
                if (compulsorytype > 0 && listBookingTemp != null)
                {
                    listBookingTemp = listBookingTemp.Where(m => m.CompulsoryType == compulsorytype && classids.Contains(m.SchoolGradeClassId.ToString()));
                }
                if (listBookingTemp != null && listBookingTemp.Count() > 0)
                {
                    num = listBookingTemp.Count();
                }
            }
            return num;
        }
        /// <summary>
        /// 已做选做分组实验数量
        /// </summary>
        /// <param name="listBooking">实验登记列表</param>
        /// <param name="schoolyearstart">统计年度</param>
        /// <param name="version">统计参数版本信息</param>
        /// <param name="compulsorytype">1： 必修 2：选修</param>
        /// <param name="classids">班级id集合</param>
        /// <returns></returns>
        public int GetOptionalGroupNumedNum(List<ExperimentBookingStaticModel> listBooking, int schoolyearstart, TextbookVersionParameterEntity version, int compulsorytype = 0, string classids = "")
        {
            int num = 0;
            if (listBooking != null && listBooking.Count > 0)
            {
                var listBookingTemp = listBooking.Where(m => m.IsEvaluate == 1 &&
                m.IsNeedDo == 2 &&
                m.ExperimentType == 2 &&
                m.StaticSchoolYearStart == schoolyearstart &&
                m.StaticSchoolTerm == version.SchoolTerm &&
                m.StaticGradeId == version.GradeId &&
                m.CourseId == version.CourseId
                );
                if (compulsorytype > 0 && listBookingTemp != null)
                {
                    listBookingTemp = listBookingTemp.Where(m => m.CompulsoryType == compulsorytype && classids.Contains(m.SchoolGradeClassId.ToString()));
                }
                if (listBookingTemp != null && listBookingTemp.Count() > 0)
                {
                    num = listBookingTemp.Count();
                }
            }
            return num;
        }
        #endregion
    }
}
