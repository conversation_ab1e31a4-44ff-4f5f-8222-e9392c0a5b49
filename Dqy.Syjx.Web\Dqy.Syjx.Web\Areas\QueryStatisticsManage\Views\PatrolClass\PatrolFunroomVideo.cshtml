﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
    int UnitType = (int)ViewBag.UnitType;
    int IsSystem = (int)ViewBag.IsSystem;
}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .select-list li input {
        width: 160px !important;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li id="liCountyId" style="display:none;">
                        <span id="CountyId" col="CountyId" style="display: inline-block;width:160px;"></span>
                    </li>
                    <li id="liSchoolId" style="display:none;">
                        <div id="UnitId" col="UnitId" value="0" style="display: inline-block;width:180px;"></div>
                    </li>
                    <li>
                        <div id="DictionaryId1006A" col="DictionaryId1006A" style="display: inline-block;width:140px;"></div>
                    </li>
                    <li>
                        <div id="DictionaryId1006B" col="DictionaryId1006B" style="display: inline-block;width:140px;"></div>
                    </li>
                    <li>
                        <div id="RoomAttribute" col="RoomAttribute" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <div id="DictionaryId1005" col="DictionaryId1005" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <input id="Name" col="Name" type="text" placeholder="实验（专用）室名称" style="display: inline-block;" />
                    </li>
                    <li>
                        <div id="VideoStatuz" col="VideoStatuz" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>&nbsp;&nbsp;
                        <a id="btnRefresh" class="btn btn-default btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>&nbsp;&nbsp;
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>
<script type="text/javascript">
    $(function () {
        if (@IsSystem == 1) {
            $("#UnitId").ysComboBox({
                data: [{ "Id": 0, "Name": "系统管理员" }],
                key: 'Id',
                value: 'Name',
                defaultName: '单位名称',
            });
        }

        if (@UnitType == @UnitTypeEnum.City.ParseToInt()) {
            $('#liCountyId').show();
        }

        if (@UnitType <= @UnitTypeEnum.County.ParseToInt()) {
            $('#liSchoolId').show();
        }


        $("#CountyId").ysComboBox({
            url: '@Url.Content("~/OrganizationManage/Unit/GetCountyBoxByCityIdJson")',
            key: "Id",
            value: "Name",
            defaultName: '区县名称',
            onChange: function () {
                var countyId = $("#CountyId").ysComboBox("getValue");
                loadSchool(countyId);
            }
        });

        function loadSchool(countyId) {
            if (parseInt(countyId) == -1) {
                $("#UnitId").ysComboBox({
                    url: '@Url.Content("~/OrganizationManage/Unit/GetChildrenPageList?PageSize=10000")',
                    key: 'Id',
                    value: 'Name',
                    defaultName: '单位名称',
                });
                $(".select2-container").width("100%");
            } else if (parseInt(countyId) == 0) {
                $("#UnitId").ysComboBox({
                    data: [],
                    key: 'Id',
                    value: 'Name',
                    defaultName: '单位名称',
                });
                $(".select2-container").width("100%");
            } else if (parseInt(countyId) > 0)  {
                 $("#UnitId").ysComboBox({
                    url: '@Url.Content("~/OrganizationManage/Unit/GetUnitList?")' + 'Pid=' + countyId,
                    key: 'Id',
                    value: 'Name',
                    defaultName: '单位名称',
                });
                $(".select2-container").width("100%");
            }
        }

        if (@UnitType == @UnitTypeEnum.City.ParseToInt()) {
            loadSchool(0);
        }
        if (@UnitType == @UnitTypeEnum.County.ParseToInt()) {
            loadSchool(-1);
        }
        if (@UnitType == @UnitTypeEnum.School.ParseToInt()) {
            loadSchool(-1);
        }

        $("#DictionaryId1006A").ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetList2Json")' + '?TypeCode=1006&OptType=5',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName:'一级分类',
            onChange: function () {
                var dicA = $("#DictionaryId1006A").ysComboBox("getValue");
                loadDictionaryId1006B(dicA);
            }
        });
        loadDictionaryId1006B(0);
        loadNature();
        loadSubject();
        loadVideoStatuz();
        //加载数据
        initGrid();
    });
     function loadDictionaryId1006B(dicA) {
        if (parseInt(dicA) > 0) {
            $("#DictionaryId1006B").ysComboBox({
                url: '@Url.Content("~/SystemManage/StaticDictionary/GetList2Json")' + '?TypeCode=1006&OptType=5' + '&Pid=' + dicA,
                key: 'DictionaryId',
                value: 'DicName',
                defaultName: '二级分类',
            });
            $(".select2-container").width("100%");
        } else {
            $("#DictionaryId1006B").ysComboBox({
                data: [],
                key: 'DictionaryId',
                value: 'DicName',
                defaultName: '二级分类',
            });
            $(".select2-container").width("100%");
        }
    }

    function loadNature() {
        $("#RoomAttribute").ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetList2Json")' + '?TypeCode=1009&Ids=1009001,1009002',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '属性',
        });
    }

    function loadSubject() {
        $("#DictionaryId1005").ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetCourseByUserId")',
            defaultName: '适用学科',
            key: 'DictionaryId',
            value: 'DicName'
        });
    }

    function loadVideoStatuz(){
        $("#VideoStatuz").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(VideoStatuzEnum).EnumToDictionaryString())), defaultName: '状态' });
    }

    function initGrid() {
        var queryUrl = '@Url.Content("~/QueryStatisticsManage/PatrolClass/GetFunroomVideoJson")';
        var columnsArr=[];
        columnsArr = [
                {
                    title: '操作', halign: 'center', valign: 'middle', align: 'center', width: 110,
                    formatter: function (value, row, index) {
                        var actions = [];
                        if (row.VideoStatuz == "@VideoStatuzEnum.Online.ParseToInt()") {
                            actions.push('<a class="btn btn-info btn-xs" href="#" onclick="preview(\'' + row.SrcName + '\');"><i class="fa fa-video-camera"></i></a>');
                        } else {
                            actions.push('&nbsp;-&nbsp;');
                        }
                        return actions.join('');
                    }
                },
                { field: 'Id', title: 'Id', visible: false },
                { field: 'index', title: '序号', width: 50, halign: 'center', valign: 'middle', align: 'center', formatter: function (value, row, index) { return index + 1; } },
                { field: 'CountyName', title: '区县名称', sortable: true, halign: 'center', valign: 'middle', visible: @UnitType == @UnitTypeEnum.City.ParseToInt()? true : false },
                { field: 'SchoolName', title: '单位名称', sortable: true, halign: 'center', valign: 'middle', visible: @UnitType <= @UnitTypeEnum.County.ParseToInt()? true : false },
                { field: 'ClassNameA', title: '一级分类', sortable: true, width: 200, halign: 'center', valign: 'middle' },
                { field: 'ClassNameB', title: '二级分类', sortable: true, width: 200, halign: 'center', valign: 'middle' },
                { field: 'Name', title: '实验（专用）室名称', sortable: true, width: 260, halign: 'center', valign: 'middle' },
                { field: 'SrcName', title: '摄像机名称', sortable: true, width: 200, halign: 'center', valign: 'middle' },
                {
                    field: 'AddressId', title: '地点', sortable: true, width: 260, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        var html = '--';
                        if (row.HouseName != undefined && row.HouseName.length > 0) {
                            html = (row.HouseName + "(" + row.RoomName + ")");
                        } else if (row.RoomName != undefined && row.RoomName.length > 0) {
                            html = row.RoomName;
                        }
                        return html;
                    }
                },
                { field: 'NatureName', title: '属性', sortable: true, width: 100, halign: 'center', valign: 'middle', align: 'center' },
                { field: 'SubjectName', title: '适用学科', sortable: true, width: 150, halign: 'center', valign: 'middle', align: 'center' },
                {
                    field: 'VideoStatuz', title: '状态', sortable: true, width: 100, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        if (value == "@VideoStatuzEnum.Online.ParseToInt()") {
                            html = "@VideoStatuzEnum.Online.GetDescription()";
                        } else {
                            html = "@VideoStatuzEnum.NotOnline.GetDescription()";
                        }
                        return html;
                    }
                }
            ];
        $('#gridTable').ysTable({
            url: queryUrl,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            sortName: 'UnitId ASC',
            columns: columnsArr,
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function resetGrid() {
        $('#CountyId').ysComboBox('setValue', -1);
        $('#UnitId').ysComboBox('setValue', -1);
        $("#DictionaryId1006A").ysComboBox('setValue', -1);
        loadDictionaryId1006B(0);
        $("#RoomAttribute").ysComboBox('setValue', -1);
        $("#DictionaryId1005").ysComboBox('setValue', -1);
        $("#VideoStatuz").ysComboBox('setValue', -1);
        $("#Name").val("");
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    //预览视频
    function preview(srcName) {
         ys.ajax({
            url: '@Url.Content("~/CameraManage/SchoolCamera/GetVideoPlayResources")' + '?cameraName=' + srcName,
                    type: 'post',
                    success: function (obj) {
                        if (obj.Tag == 1) {
                            console.log('播放资源', obj.Data);
                            var url = "";
                            if(obj.Data.Brand == "1"){  //海康
                                url = '@Url.Content("~/QueryStatisticsManage/PatrolClass/Preview")' + '?t=' + obj.Data.Brand + '&index=' + obj.Data.PlayResources;
                            }else if (obj.Data.Brand == "2") {  //大华
                                url = '@Url.Content("~/QueryStatisticsManage/PatrolClass/DhPreview")' + '?t=' + obj.Data.Brand + '&index=' + obj.Data.PlayResources;
                            }else{

                            }

                            if(url!=""){
                                window.open(url);
                            }else{
                                ys.msgError("未查询到播放资源");
                            }
                        }
                        else {
                            ys.msgError(obj.Message);
                        }
                    }
                });
    }

</script>
