﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.ExperimentTeachManage;
using Dqy.Syjx.Model.Param.ExperimentTeachManage;
using Dqy.Syjx.Enum;

namespace Dqy.Syjx.Service.ExperimentTeachManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2024-10-18 09:44
    /// 描 述：实验发布清单服务类
    /// </summary>
    public class PublishDetailService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<PublishDetailEntity>> GetList(PublishDetailListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<PublishDetailEntity>> GetPageList(PublishDetailListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<PublishDetailEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<PublishDetailEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(PublishDetailEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }
        public async Task UpdateForm(PublishDetailEntity entity, List<string> fields)
        {
            if (fields != null)
            {
                await entity.Modify();
                fields.Add("BaseModifyTime");
                fields.Add("BaseModifierId");
            }
            await this.BaseRepository().Update(entity, fields);
        }
        public async Task SaveTransForm(PublishDetailEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update ex_PublishDetail set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
                strSql = $"update ex_PublishDetail set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }

        /// <summary>
        /// 更新出勤人数。
        /// </summary>
        /// <param name="id">实验课程发布表</param>
        /// <param name="experimentid">实验id</param>
        /// <param name="db"></param>
        /// <returns></returns>
        public async Task<int> UpdateAttendanceNumForm(long id, long experimentid, Repository db)
        {
            string strSql = @$" Update ex_PublishDetail 
            SET AttendanceNum = (SELECT COUNT(1) FROM (SELECT MIN(Id) AS Id FROM ex_ExperimentCriticize WHERE ExperimentPublishId =  {id} AND ExperimentId = {experimentid} AND BaseIsDelete = 0  AND AttendanceStatuz = 1 GROUP BY  ParentStudentId, StudentId) AS tb1 )
            Where BaseIsDelete = 0 AND ExperimentPublishId = {id} AND ExperimentId = {experimentid} ";
            if (db == null)
            {
                return await this.BaseRepository().ExecuteBySql(strSql);
            }
            else
            {
                return await db.ExecuteBySql(strSql);
            }

        }
        #endregion

        #region 私有方法
        private Expression<Func<PublishDetailEntity, bool>> ListFilter(PublishDetailListParam param)
        {
            var expression = LinqExtensions.True<PublishDetailEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (param.ExperimentPublishId> 0)
                {
                    expression = expression.And(t => t.ExperimentPublishId == param.ExperimentPublishId);
                }
                if (param.ExperimentId > 0)
                {
                    expression = expression.And(t => t.ExperimentId == param.ExperimentId);
                }
            }
            return expression;
        }

        /// <summary>
        /// 添加实验课程-课程清单
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<List<PublishDetailEntity>> GetOutPageList(PublishDetailListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append($@"  SELECT * From (
                SELECT pd1.* 
                ,dic3.DicName AS CourseName 
                ,s5.RealName AS GuidanceTeacherName
                ,sdic.DictValue AS ExperimentTypeName
                FROM  ex_PublishDetail AS pd1 
                INNER JOIN  sys_static_dictionary AS dic3 ON pd1.CourseId = dic3.DictionaryId AND dic3.TypeCode = '{DicTypeCodeEnum.Course.ParseToInt().ToString()}'  
                INNER JOIN  SysUser AS s5 ON pd1.GuidanceTeacher = s5.Id
                LEFT JOIN SysDataDictDetail AS sdic ON sdic.BaseIsDelete = 0 AND  sdic.TypeCode = 'OET101001' AND pd1.SchoolId = sdic.UnitId AND pd1.ExperimentType = sdic.DictKey
            ) as tb1 WHERE   1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = @BaseIsDelete ");
                parameter.Add(DbParameterExtension.CreateDbParameter("@BaseIsDelete", param.BaseIsDelete));
                if (param.ExperimentPublishId > 0)
                {
                    strSql.Append(" AND ExperimentPublishId = @ExperimentPublishId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ExperimentPublishId", param.ExperimentPublishId));
                }
                if (param.SchoolId > 0)
                {
                    strSql.Append(" AND SchoolId = @SchoolId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
                }
                if (param.ExperimentType > 0)
                {
                    strSql.Append(" AND ExperimentType = @ExperimentType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ExperimentType", param.ExperimentType));
                }
                if (!string.IsNullOrEmpty(param.Name))
                {
                    //  OR EquipmentNeed like @Name OR MaterialNeed like @Name OR Remark like @Name
                    strSql.Append(" AND ( ExperimentName like @Name ) ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Name", $"%{param.Name}%"));
                }
                if (param.GradeId > 0)
                {
                    strSql.Append(" AND ApplicableGrade like @ApplicableGrade ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ApplicableGrade", $"%{param.GradeId}%"));
                }
                if (param.CourseId > 0)
                {
                    strSql.Append(" AND CourseId = @CourseId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (param.ActivityType != -10000)
                {
                    strSql.Append($" AND ActivityType = {param.ActivityType} "); //课程类型
                }
            }
            var list = await this.BaseRepository().FindList<PublishDetailEntity>(strSql.ToString(), parameter.ToArray(), pagination);
            return list.ToList();
        }
        /// <summary>
        /// 添加实验课程，添加课程列表
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<List<PublishDetailEntity>> GetOutAddPageDetailList(PublishDetailListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append($@"  SELECT * From (
                SELECT se1.* 
                ,dic3.DicName AS CourseName    
                ,sdic.DictValue AS ExperimentTypeName
				,ISNULL(pd1.Id ,0) AS ExperimentPublishId
				,s5.RealName AS GuidanceTeacherName
				,pd1.GuidanceTeacher 
                FROM ex_SchoolExperiment AS se1 
                INNER JOIN  sys_static_dictionary AS dic3 ON se1.CourseId = dic3.DictionaryId AND dic3.TypeCode = '{DicTypeCodeEnum.Course.ParseToInt().ToString()}'  
             
                LEFT JOIN SysDataDictDetail AS sdic ON sdic.BaseIsDelete = 0 AND sdic.TypeCode = 'OET101001' ANd se1.SchoolId = sdic.UnitId AND se1.ExperimentType = sdic.DictKey
				LEFT JOIN ex_PublishDetail AS pd1 ON   pd1.BaseIsDelete = 0 AND se1.Id = pd1.ExperimentId AND pd1.ExperimentPublishId = {param.ExperimentPublishId}
				LEFT JOIN  SysUser AS s5 ON pd1.GuidanceTeacher = s5.Id
            ) as tb1 WHERE   tb1.ActivityType = {ActivityTypeEnum.OutOfClass.ParseToInt()} ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = @BaseIsDelete ");
                parameter.Add(DbParameterExtension.CreateDbParameter("@BaseIsDelete", param.BaseIsDelete));
                
                if (param.SchoolId > 0)
                {
                    strSql.Append(" AND SchoolId = @SchoolId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
                }
                if (param.ExperimentType > 0)
                {
                    strSql.Append(" AND ExperimentType = @ExperimentType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ExperimentType", param.ExperimentType));
                }
                if (!string.IsNullOrEmpty(param.Name))
                {
                    strSql.Append(" AND (EquipmentNeed like @Name OR ExperimentName like @Name) ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Name", $"%{param.Name}%"));
                }
                if (param.GradeId > 0)
                {
                    strSql.Append(" AND ApplicableGrade like @ApplicableGrade ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ApplicableGrade", $"%{param.GradeId}%"));
                }
                if (param.CourseId > 0)
                {
                    strSql.Append(" AND CourseId = @CourseId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (param.ActivityType != -10000)
                {
                    strSql.Append($" AND ActivityType = {param.ActivityType} "); //课程类型
                }
                if (param.Statuz != -10000)
                {
                    strSql.Append($" AND Statuz = {param.Statuz} "); //课程类型
                }
            }
            var list = await this.BaseRepository().FindList<PublishDetailEntity>(strSql.ToString(), parameter.ToArray(), pagination);
            return list.ToList();
        }

        #endregion
    }
}
