﻿using Dqy.Syjx.Business.BusinessManage;
using Dqy.Syjx.Business.ExperimentTeachManage;
using Dqy.Syjx.Business.OrganizationManage;
using Dqy.Syjx.Business.SystemManage;
using Dqy.Syjx.Entity.BusinessManage;
using Dqy.Syjx.Entity.ExperimentTeachManage;
using Dqy.Syjx.Entity.InstrumentManage;
using Dqy.Syjx.Entity.OrganizationManage;
using Dqy.Syjx.Entity.SystemManage;
using Dqy.Syjx.Enum.ExperimentTeachManage;
using Dqy.Syjx.Model.Input.ExperimentTeachManage;
using Dqy.Syjx.Model.Param;
using Dqy.Syjx.Model.Param.ExperimentTeachManage;
using Dqy.Syjx.Model.Param.OrganizationManage;
using Dqy.Syjx.Model.Param.SystemManage;
using Dqy.Syjx.Model.Result.ExperimentTeachManage;
using Dqy.Syjx.Service.HttpService;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Web.Code;
using Dynamitey.DynamicObjects;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NPOI.SS.Formula.Functions;
using Senparc.Weixin.WxOpen.AdvancedAPIs.WxApp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace Dqy.Syjx.WebApi.Controllers
{
    /// <summary>
    /// 实验教学
    /// </summary>
    [Route("api/experimentteach")]
    [ApiController]
    [AuthorizeFilter]
    public class ExperimentTeachController : ControllerBase
    {
        private ExperimentBookingBLL experimentBookingBLL = new ExperimentBookingBLL();
        private PlanDetailBLL planDetailBLL = new PlanDetailBLL();
        //private AttachmentBLL attachmentBLL = new AttachmentBLL();
        private SchoolGradeClassBLL schoolGradeClassBLL = new SchoolGradeClassBLL();
        private SchoolTermBLL schoolTermBLL = new SchoolTermBLL();
        private ExperiInstruLogBLL experiInstruLogBLL = new ExperiInstruLogBLL();
        private StaticDictionaryBLL dicBLL = new StaticDictionaryBLL();
        private UserBLL userBll = new UserBLL();
        private UnitBLL unitBll = new UnitBLL();
        private AppManageBLL appManagerBLL = new AppManageBLL();
        private readonly IHttpContextAccessor _httpContextAccessor;

        public ExperimentTeachController(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        #region 获取数据

        /// <summary>
        /// 获取当前学年学期数据
        /// </summary>
        /// <returns></returns>
        [Route("getcurrentschoolterm")]
        [HttpGet]
        public async Task<TData<SchoolTermEntity>> GetCurrentSchoolTerm()
        {
            TData<SchoolTermEntity> obj = new TData<SchoolTermEntity>();
            SchoolTermEntity entity = new SchoolTermEntity();
            //获取当前学年学期 
            DateTime now = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day);
            var schoolTermList = (await schoolTermBLL.GetList(new SchoolTermListParam
            {
                OptType = 2,
                ClassTime = now
            })).Data;

            int schoolYearStart = -1;
            int schoolTerm = -1;
            if (schoolTermList.Count > 0)
            {
                schoolYearStart = schoolTermList.FirstOrDefault().SchoolYearStart;
                schoolTerm = schoolTermList.FirstOrDefault().SchoolTerm;
            }
            entity.SchoolYearStart = schoolYearStart;
            entity.SchoolTerm = schoolTerm;
            obj.Tag = 1;
            obj.Data = entity;
            return obj;
        }

        /// <summary>
        /// 获取安排人
        /// </summary>
        /// <param name="schoolStageId">学段</param>
        /// <param name="courseId">学科</param>
        /// <param name="sessionId">sessionId</param>
        /// <returns></returns>
        [Route("getarrangeuser")]
        [HttpGet]
        public async Task<TData<List<UserSchoolStageSubjectEntity>>> GetArrangeUserList(int schoolStageId, int courseId, string sessionId)
        {
            experimentBookingBLL.ApiToken = StringFilter.SearchSql(sessionId);
            TData<List<UserSchoolStageSubjectEntity>> obj = await experimentBookingBLL.GetArrangerList(schoolStageId, courseId);
            return obj;
        }

        /// <summary>
        /// 获取实验计划列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [Route("getplanlist")]
        [HttpPost]
        public async Task<TData<List<PlanDetailEntity>>> GetPlanExperimentList(PlanDetailListApiParam param)
        {
            if (param == null) { return null; }
            planDetailBLL.ApiToken = param.SessionId;
            TData<List<PlanDetailEntity>> obj = await planDetailBLL.GetPageBookingList(new PlanDetailListParam
            {
                BaseIsDelete = 0,
                //SchoolGradeClassId = param.SchoolGradeClassId,
                CourseId = param.CourseId,
                ClassTime = param.ClassTime,
                SchoolTerm = param.SchoolTerm ?? 0,
                SchoolGradeClassIdz = param.SchoolGradeClassIdz,
                Name = param.keyWord
            }, new Pagination
            {
                Sort = "IsSelected asc, Chapter asc, Id asc",
                SortType = "asc",
                PageIndex = param.PageIndex,
                PageSize = param.PageSize
            });
            return obj;
        }

        /// <summary>
        /// 获取实验目录列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [Route("getcataloglist")]
        [HttpPost]
        public async Task<TData<List<TextbookVersionDetailEntity>>> GetCatalogExperimentList(PlanDetailListApiParam param)
        {
            if (param == null) { return null; }
            experimentBookingBLL.ApiToken = param.SessionId;
            TData<List<TextbookVersionDetailEntity>> obj = await experimentBookingBLL.GetSelectVersionDetailPageList(new TextbookVersionDetailListParam
            {
                BaseIsDelete = 0,
                // SchoolGradeClassId = param.SchoolGradeClassId,
                CourseId = param.CourseId,
                ClassTime = param.ClassTime,
                SchoolTerm = param.SchoolTerm ?? 0,
                SchoolGradeClassIdz = param.SchoolGradeClassIdz,
                SchoolStage = param.SchoolStage ?? 0,
                Name = param.keyWord
            }, new Pagination
            {
                Sort = "IsSelected ASC, SchoolTerm Desc",
                SortType = "asc",
                PageIndex = param.PageIndex,
                PageSize = param.PageSize
            });
            return obj;
        }

        /// <summary>
        /// 已预约列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [Route("getreservelist")]
        [HttpPost]
        public async Task<TData<List<ExperimentBookingEntity>>> GetReserveList(SerachApiParam param)
        {
            if (param == null) { return null; }
            experimentBookingBLL.ApiToken = param.SessionId;
            TData<List<ExperimentBookingEntity>> obj = new TData<List<ExperimentBookingEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(param.SessionId);
            if (operatorinfo == null)
            {
                obj.Tag = -1;
                obj.Message = "登录信息失效！";
                return obj;
            }
            obj = await experimentBookingBLL.GetExperimentBookingList(new ExperimentBookingListParam
            {
                ExperimentName = param.KeyWord,
                BookUserId = operatorinfo.UserId,
                RecordMode = ExperimentRecordModeEnum.BookIng.ParseToInt(),
                IsMain = 1
            }, new Pagination
            {
                Sort = "Id",
                SortType = "desc",
                PageIndex = param.PageIndex,
                PageSize = param.PageSize
            });
            return obj;
        }

        /// <summary>
        /// 待安排列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [Route("getarrangelist")]
        [HttpPost]
        public async Task<TData<List<ExperimentBookingEntity>>> GetWaitArrangeList(SerachApiParam param)
        {
            if (param == null) { return null; }
            experimentBookingBLL.ApiToken = param.SessionId;
            TData<List<ExperimentBookingEntity>> obj = new TData<List<ExperimentBookingEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(param.SessionId);
            if (operatorinfo == null)
            {
                obj.Tag = -1;
                obj.Message = "登录信息失效！";
                return obj;
            }
            obj = await experimentBookingBLL.GetExperimentBookingList(new ExperimentBookingListParam
            {
                ExperimentName = param.KeyWord,
                ListType = 1,
                SafeguardUserId = operatorinfo.UserId,
                RecordMode = ExperimentRecordModeEnum.BookIng.ParseToInt(),
                Statuz = ExperimentBookStatuzEnum.WaitArrange.ParseToInt(),
                IsMain = 1
            }, new Pagination
            {
                Sort = "Id",
                SortType = "desc",
                PageIndex = param.PageIndex,
                PageSize = param.PageSize
            }); ;
            return obj;
        }

        /// <summary>
        /// 已安排列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [Route("getarrangedlist")]
        [HttpPost]
        public async Task<TData<List<ExperimentBookingEntity>>> GetAlreadyArrangeList(SerachApiParam param)
        {
            if (param == null) { return null; }
            experimentBookingBLL.ApiToken = param.SessionId;
            TData<List<ExperimentBookingEntity>> obj = new TData<List<ExperimentBookingEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(param.SessionId);
            if (operatorinfo == null)
            {
                obj.Tag = -1;
                obj.Message = "登录信息失效！";
                return obj;
            }
            obj = await experimentBookingBLL.GetExperimentBookingList(new ExperimentBookingListParam
            {
                ExperimentName = param.KeyWord,
                ListType = 1,
                SafeguardUserId = operatorinfo.UserId,
                RecordMode = ExperimentRecordModeEnum.BookIng.ParseToInt(),
                ThanStatuz = ExperimentBookStatuzEnum.WaitArrange.ParseToInt(),
                IsMain = 1 //主数据
            }, new Pagination
            {
                Sort = "Id",
                SortType = "desc",
                PageIndex = param.PageIndex,
                PageSize = param.PageSize
            });
            return obj;
        }

        /// <summary>
        /// 待登记列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [Route("getregisterlist")]
        [HttpPost]
        public async Task<TData<List<ExperimentBookingEntity>>> GetWaitRecordList(SerachApiParam param)
        {
            if (param == null) { return null; }
            experimentBookingBLL.ApiToken = param.SessionId;
            TData<List<ExperimentBookingEntity>> obj = new TData<List<ExperimentBookingEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(param.SessionId);
            if (operatorinfo == null)
            {
                obj.Tag = -1;
                obj.Message = "登录信息失效！";
                return obj;
            }
            obj = await experimentBookingBLL.GetExperimentBookingList(new ExperimentBookingListParam
            {
                ExperimentName = param.KeyWord,
                BookUserId = operatorinfo.UserId,
                RecordMode = ExperimentRecordModeEnum.BookIng.ParseToInt(),
                Statuz = ExperimentBookStatuzEnum.WaitRecord.ParseToInt(),
                IsMain = 1
            }, new Pagination
            {
                Sort = "Id",
                SortType = "desc",
                PageIndex = param.PageIndex,
                PageSize = param.PageSize
            });
            return obj;
        }

        /// <summary>
        /// 已登记列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [Route("getregisteredlist")]
        [HttpPost]
        public async Task<TData<List<ExperimentBookingEntity>>> GetAlreadyRecordList(SerachApiParam param)
        {
            if (param == null) { return null; }
            experimentBookingBLL.ApiToken = param.SessionId;
            TData<List<ExperimentBookingEntity>> obj = new TData<List<ExperimentBookingEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(param.SessionId);
            if (operatorinfo == null)
            {
                obj.Tag = -1;
                obj.Message = "登录信息失效！";
                return obj;
            }
            obj = await experimentBookingBLL.GetExperimentBookingList(new ExperimentBookingListParam
            {
                ExperimentName = param.KeyWord,
                BookUserId = operatorinfo.UserId,
                RecordMode = ExperimentRecordModeEnum.BookIng.ParseToInt(),
                Statuz = ExperimentBookStatuzEnum.AlreadyRecord.ParseToInt(),
                IsMain = 1
            }, new Pagination
            {
                Sort = "Id",
                SortType = "desc",
                PageIndex = param.PageIndex,
                PageSize = param.PageSize
            });
            return obj;
        }

        /// <summary>
        /// 简易登记列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [Route("geteasyregisteredlist")]
        [HttpPost]
        public async Task<TData<List<ExperimentBookingEntity>>> GetEasyAlreadyRecordList(SerachApiParam param)
        {
            if (param == null) { return null; }
            experimentBookingBLL.ApiToken = param.SessionId;
            TData<List<ExperimentBookingEntity>> obj = new TData<List<ExperimentBookingEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(param.SessionId);
            if (operatorinfo == null)
            {
                obj.Tag = -1;
                obj.Message = "登录信息失效！";
                return obj;
            }
            obj = await experimentBookingBLL.GetExperimentBookingList(new ExperimentBookingListParam
            {
                ExperimentName = param.KeyWord,
                BookUserId = operatorinfo.UserId,
                RecordMode = ExperimentRecordModeEnum.Easy.ParseToInt(),
                Statuz = ExperimentBookStatuzEnum.AlreadyRecord.ParseToInt(),
                IsMain = 1
            }, new Pagination
            {
                Sort = "Id",
                SortType = "desc",
                PageIndex = param.PageIndex,
                PageSize = param.PageSize
            });
            return obj;
        }

        /// <summary>
        /// 获取实验预约详情
        /// </summary>
        /// <param name="id"></param>
        /// <param name="sessionId"></param>
        /// <returns></returns>
        [Route("getdetail")]
        [HttpGet]
        public async Task<TData<ExperimentBookingEntity>> GetDetail(long id, string sessionId)
        {
            TData<ExperimentBookingEntity> obj = new TData<ExperimentBookingEntity>();

            experimentBookingBLL.ApiToken = StringFilter.SearchSql(sessionId);
            obj = await experimentBookingBLL.GetDetailById(id);
            if (obj.Tag == 0)
            {
                //if (obj.Data.Statuz == ExperimentBookStatuzEnum.AlreadyRecord.ParseToInt())
                //{
                //    obj.Data.AttachmentList = (await attachmentBLL.GetList(new Model.Param.BusinessManage.AttachmentListParam
                //    {
                //        FileCategory = FileCategoryEnum.ExperimentSceneImg.ParseToInt(),
                //        ObjectId = obj.Data.ExperimentRecordId
                //    })).Data;
                //}
                obj.Message = "未找到数据！";
            }

            return obj;
        }

        /// <summary>
        /// 获取实验安排时自动带出的所需仪器和实验材料值
        /// </summary>
        /// <param name="id"></param>
        /// <param name="sessionId"></param>
        /// <returns></returns>
        [Route("getneedval")]
        [HttpGet]
        public async Task<TData<object>> GetArrangerNeedValue(long id, string sessionId)
        {
            sessionId = StringFilter.SearchSql(sessionId);
            experimentBookingBLL.ApiToken = sessionId;
            TData<object> obj = new TData<object>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(sessionId);
            if (operatorinfo == null)
            {
                obj.Tag = -1;
                obj.Message = "登录信息失效！";
                return obj;
            }
            var (equipmentNeed, materialNeed) = await experimentBookingBLL.GetArrangerNeedValue(id);
            obj.Data = new
            {
                ArrangerEquipmentNeed = equipmentNeed,
                ArrangerMaterialNeed = materialNeed
            };
            obj.Tag = 1;
            return obj;
        }

        /// <summary>
        /// 验证是否跨年级选择班级
        /// </summary>
        /// <returns></returns>
        [Route("verifygradeclass")]
        [HttpPost]
        public async Task<TData<string>> VerifyGradeClass(ExperimentBookingInputModel model)
        {
            TData<string> obj = new TData<string>();
            if (model == null) { return obj; }
            SchoolGradeClassListParam classParam = new SchoolGradeClassListParam();
            classParam.Ids = model.SchoolGradeClassIdz;
            schoolGradeClassBLL.ApiToken = model.SessionId;
            var classListobj = await schoolGradeClassBLL.GetList(classParam);
            if (classListobj != null && classListobj.Data.Count > 0)
            {
                var classList = classListobj.Data.ToList();
                //验证是否跨年级了。
                if (classList.GroupBy(m => m.GradeId).Count() > 1)
                {
                    obj.Tag = 0;
                    obj.Message = "你选择的上课班级禁止跨年级选择。";
                }
                else
                {
                    obj.Tag = 1;
                }
            }
            return obj;
        }
        #endregion

        #region 提交数据

        /// <summary>
        /// 提交实验预约
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [Route("savereserve")]
        [HttpPost]
        public async Task<TData<string>> SaveExperimentReserve(ExperimentBookingInputModel model)
        {
            TData<string> obj = new TData<string>();
            if (model != null)
            {
                experimentBookingBLL.ApiToken = model.SessionId;
                //加跨年级验证
                SchoolGradeClassListParam classParam = new SchoolGradeClassListParam();
                classParam.Ids = model.SchoolGradeClassIdz;
                schoolGradeClassBLL.ApiToken = model.SessionId;
                var classListobj = await schoolGradeClassBLL.GetList(classParam);
                if (classListobj != null && classListobj.Data.Count > 0)
                {
                    var classList = classListobj.Data.ToList();
                    //验证是否跨年级了。
                    if (classList.GroupBy(m => m.GradeId).Count() > 1)
                    {
                        obj.Tag = 0;
                        obj.Message = "你选择的上课班级禁止跨年级选择。";
                    }
                    else
                    {
                        obj = await experimentBookingBLL.SaveForm(model);
                    }
                }
            }
            return obj;
        }

        /// <summary>
        /// 实验安排登记
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [Route("arrangerecord")]
        [HttpPost]
        public async Task<TData> ArrangeRecord(ExperimentBookArrangeRecordInputModel model)
        {
            TData obj = new TData();
            if (model == null)
            {
                obj.Tag = 0;
                obj.Message = "参数错误。";
                return obj;
            }
            experimentBookingBLL.ApiToken = model.SessionId;
            obj = await experimentBookingBLL.ArrangeRecordForm(model);
            return obj;
        }

        /// <summary>
        /// 简易登记
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [Route("easyrecord")]
        [HttpPost]
        public async Task<TData<string>> SaveEasyRecord(ExperimentBookingInputModel model)
        {
            TData<string> obj = new TData<string>();
            if (model == null)
            {
                obj.Tag = 0;
                obj.Message = "参数错误。";
                return obj;
            }
            experimentBookingBLL.ApiToken = model.SessionId;
            //加跨年级验证
            SchoolGradeClassListParam classParam = new SchoolGradeClassListParam();
            classParam.Ids = model.SchoolGradeClassIdz;
            schoolGradeClassBLL.ApiToken = model.SessionId;
            var classListobj = await schoolGradeClassBLL.GetList(classParam);
            if (classListobj != null && classListobj.Data.Count > 0)
            {
                var classList = classListobj.Data.ToList();
                //验证是否跨年级了。
                if (classList.GroupBy(m => m.GradeId).Count() > 1)
                {
                    obj.Tag = 0;
                    obj.Message = "你选择的上课班级禁止跨年级选择。";
                }
                else
                {
                    obj = await experimentBookingBLL.SaveEasyForm(model);
                }
            }

            return obj;
        }

        /// <summary>
        /// 实验安排、登记、预约撤回
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [Route("withdrawarrangerecord")]
        [HttpPost]
        public async Task<TData<string>> WithdrawArrangeRecord(ArrageRecodeWithdrawInputModel model)
        {
            TData<string> obj = new TData<string>();
            if (model == null)
            {
                obj.Tag = 0;
                obj.Message = "参数错误。";
                return obj;
            }
            experimentBookingBLL.ApiToken = model.SessionId;
            TData result = await experimentBookingBLL.ArrangeRecordWithdrawForm(model);
            obj.Tag = result.Tag;
            if (result.Tag == 1)
            {
                obj.Message = "撤回成功";
            }
            else
            {
                obj.Message = result.Message;
            }
            return obj;
        }

        /// <summary>
        /// 实验预约删除
        /// </summary>
        /// <returns></returns>
        [Route("deletereserve")]
        [HttpPost]
        public async Task<TData<string>> DeleteReserve(ExperimentBookingInputModel model)
        {
            TData<string> obj = new TData<string>();
            if (model == null)
            {
                obj.Tag = 0;
                obj.Message = "参数错误。";
                return obj;
            }
            experimentBookingBLL.ApiToken = model.SessionId;
            TData result = await experimentBookingBLL.DeleteForm(model.Id);
            obj.Tag = result.Tag;
            if (result.Tag == 1)
            {
                obj.Message = "删除成功";
            }
            else
            {
                obj.Message = result.Message;
            }
            return obj;
        }

        #endregion

        #region 改版获取预约实验信息接口

        /// <summary>
        /// 获取预约实体信息
        /// </summary>
        /// <param name="id">预约Id</param>
        /// <param name="sessionId"></param>
        /// <returns></returns>
        [Route("geteditformjson")]
        [HttpGet]
        public async Task<TData<object>> GetEditForm(long id, string sessionId)
        {
            var obj = new TData<object>();
            experimentBookingBLL.ApiToken = StringFilter.SearchSql(sessionId);
            var booking = await experimentBookingBLL.GetEditEntity(id); //ExperimentBookingEntity
            if (booking != null && booking.Tag == 1 && booking.Data != null)
            {
                string ExperimentName = "";
                int ExperimentType = 0;
                string MaterialNeed = "";
                int Groupz = 0;
                long Id = 0;
                ExperimentBookingEntity experimentObjEntity = new ExperimentBookingEntity();
                List<ExperimentInstrumentEntity> InstrumentList = null;
                if (booking.Data.ChildList != null && booking.Data.ChildList.Count > 0)
                {
                    experimentObjEntity = booking.Data.ChildList.FirstOrDefault();
                    Id = experimentObjEntity.Id ?? 0;
                    ExperimentName = experimentObjEntity.ExperimentName;
                    ExperimentType = experimentObjEntity.ExperimentType ?? 0;
                    MaterialNeed = experimentObjEntity.MaterialNeed;
                    InstrumentList = experimentObjEntity.InstrumentList;
                    Groupz = experimentObjEntity.Groupz ?? 0;
                }

                string classname = "";
                long funroomid = 0;
                string FunRoomName = "";
                int SectionIndex = 1;
                string SectionId = "";
                string SectionBgnTime = "";
                string SectionEndTime = "";
                DateTime? ClassTime = null;
                string ArrangerName = "";
                string ArrangerId = "";
                if (booking.Data.ClassList != null && booking.Data.ClassList.Count > 0)
                {
                    var entityClass = booking.Data.ClassList.FirstOrDefault();
                    classname = entityClass.ClassName;
                    funroomid = entityClass.FunRoomId ?? 0;
                    ClassTime = entityClass.ClassTime;
                    FunRoomName = entityClass.FunRoomName;
                    SectionIndex = entityClass.SectionIndex ?? 0;
                    SectionId = entityClass.SectionId.ToString();
                    SectionBgnTime = entityClass.SectionBgnTime;
                    SectionEndTime = entityClass.SectionEndTime;
                    ArrangerId = entityClass.ArrangerId.ToString();
                    ArrangerName = entityClass.ArrangerName;
                }
                obj.Tag = 1;
                var bookingObj = new
                {
                    Id = Id.ToString(),
                    SchoolStage = booking.Data.SchoolStage,
                    CourseId = booking.Data.CourseId,
                    SourceType = booking.Data.SourceType,
                    ExperimentName = ExperimentName,
                    ExperimentType = ExperimentType,
                    ClassName = classname,
                    FunRoomId = funroomid.ToString(),
                    FunRoomName = FunRoomName,
                    ClassTime = ClassTime,
                    MaterialNeed = MaterialNeed,
                    InstrumentList = InstrumentList,
                    ExperimentUserList = booking.Data.ExperimentUserList,
                    IsImageValidation = experimentObjEntity.IsImageValidation,
                    IsSummaryValidation = experimentObjEntity.IsSummaryValidation,
                    ExperimentSummary = experimentObjEntity.ExperimentSummary,
                    RunStatuz = experimentObjEntity.RunStatuz,
                    ProblemDesc = experimentObjEntity.ProblemDesc,
                    IsRelationCamera = experimentObjEntity.IsRelationCamera,
                    AttachmentList = experimentObjEntity.AttachmentList,
                    Remark = booking.Data.Remark,
                    SectionId = SectionId,
                    SectionIndex = SectionIndex,
                    SectionBgnTime = SectionBgnTime,
                    SectionEndTime = SectionEndTime,
                    ArrangerId = ArrangerId,
                    ArrangerName = ArrangerName,
                    Groupz = Groupz
                };
                obj.Data = bookingObj;
            }
            return obj;
        }

        /// <summary>
        /// 获取预约实体信息
        /// </summary>
        /// <param name="id"></param>
        /// <param name="funroomid"></param>
        /// <param name="classtime"></param>
        /// <param name="sessionId"></param>
        /// <returns></returns>
        [Route("getsectionjson")]
        [HttpGet]
        public async Task<TData<ExperimentBookingSectionModel>> GetSectionForm(long id, long funroomid, DateTime classtime, string sessionId)
        {
            experimentBookingBLL.ApiToken = StringFilter.SearchSql(sessionId);
            return await experimentBookingBLL.GetSectionByDate(id, funroomid, classtime);
        }


        /// <summary>
        /// 已预约列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [Route("getbookinglisted")]
        [HttpPost]
        public async Task<TData<List<ExperimentBookingEntity>>> GetBookingListed(SerachApiParam param)
        {
            if (param == null) { return null; }
            experimentBookingBLL.ApiToken = param.SessionId;
            TData<List<ExperimentBookingEntity>> obj = new TData<List<ExperimentBookingEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(param.SessionId);
            if (operatorinfo == null)
            {
                obj.Tag = -1;
                obj.Message = "登录信息失效！";
                return obj;
            }

            var paramBooking = new ExperimentBookingListParam();
            paramBooking.ExperimentName = param.KeyWord;
            paramBooking.BookUserId = operatorinfo.UserId;
            paramBooking.RecordMode = ExperimentRecordModeEnum.BookIng.ParseToInt();
            paramBooking.ThanStatuz = ExperimentBookStatuzEnum.WaitArrange.ParseToInt();
            paramBooking.IsMain = 1;
            var pagination = new Pagination();
            pagination.Sort = "Id";
            pagination.SortType = "desc";
            pagination.PageIndex = param.PageIndex;
            pagination.PageSize = param.PageSize;
            obj = await experimentBookingBLL.GetBookingedList(paramBooking, pagination);
            return obj;
        }


        /// <summary>
        /// 获取预约详情
        /// </summary>
        /// <param name="id"></param>
        /// <param name="pid"></param>
        /// <param name="statuz"></param>
        /// <param name="sessionId"></param>
        /// <returns></returns>
        [Route("getdetaillist")]
        [HttpGet]
        public async Task<TData<List<ExperimentBookingEntity>>> GetDetailList(long id, long pid, int statuz, string sessionId)
        {
            TData<List<ExperimentBookingEntity>> obj = new TData<List<ExperimentBookingEntity>>();
            if (id <= 0 && pid <= 0)
            {
                obj.Tag = -1; obj.Message = "非法操作，请从页面点击操作";
                return obj;
            }
            experimentBookingBLL.ApiToken = sessionId;
            OperatorInfo operatorinfo = await Operator.Instance.Current(sessionId);
            if (operatorinfo == null)
            {
                obj.Tag = -1;
                obj.Message = "登录信息失效！";
                return obj;
            }
            obj = await experimentBookingBLL.GetDetailForm(id, pid, statuz);
            return obj;
        }
        #endregion

        #region 改版 预约保存
        /// <summary>
        /// 实验选择班级预约保存
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [Route("saveaddplan")]
        [HttpPost]
        public async Task<TData<string>> SaveAddPlan(ExperimentBookingInputModel model)
        {
            TData<string> obj = new TData<string>();
            if (model != null)
            {
                experimentBookingBLL.ApiToken = model.SessionId;
                OperatorInfo operatorinfo = await Operator.Instance.Current(model.SessionId);
                if (operatorinfo == null)
                {
                    obj.Tag = -1;
                    obj.Message = "登录信息失效！";
                    return obj;
                }
                var isUpgrade = await schoolGradeClassBLL.GetIsUpgrade(operatorinfo, 0);
                if (isUpgrade)
                {
                    string optmsg = "预约";
                    if (model.RecordMode == ExperimentRecordModeEnum.Easy.ParseToInt())
                    {
                        optmsg = "登记";
                    }
                    obj.Tag = 0;
                    obj.Message = "学校还未进行年级升级，禁止" + optmsg + "；请联系系统管理员进行年级升级。";
                    return obj;
                }
            }
            return await experimentBookingBLL.AddPlanForm(model);
        }

        /// <summary>
        /// 保存预约信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [Route("savebooking")]
        [HttpPost]
        public async Task<TData<string>> SaveBooking(ExperimentBookingInputModel model)
        {
            TData<string> obj = new TData<string>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(model.SessionId);
            string wxs_token = GetSession("wxs_third_sysendmsg_token");
            if (model != null)
            {
                experimentBookingBLL.ApiToken = model.SessionId;
            }
            obj = await experimentBookingBLL.SaveAppBooking(model, wxs_token);
            if (obj.Tag ==1 && !string.IsNullOrEmpty(wxs_token))
            {
                var userid = (long)obj.ExtendData;
                if (userid > 0)
                {
                    SendExperimentUser(userid, wxs_token, operatorinfo);
                }
            }
            return obj;
        }


        private async Task SendExperimentUser(long userid, string token, OperatorInfo operatorinfo)
        {
            var requestTokenUri = $"/contact/task/create";
            try
            {
                TData<List<AppManageEntity>> obj = await appManagerBLL.GetList(new AppManageListParam() { AppCode = "NTJidUZpdzBaMzZzSHlFdw", AppType = 1, IsMain = 2 });
                if (obj.Total == 0)
                {
                    throw new BusinessException("请联系运维人员在【第三方应用管理】->【第三方应用列表】中配置相关内容。");
                }
                AppManageEntity objEntity = obj.Data.FirstOrDefault();
                var userEntity = await userBll.GetEntity(operatorinfo.UserId ?? 0);
                string uuid = "";
                if (userEntity != null && userEntity.Data != null)
                {
                    uuid = userEntity.Data.ThirdUserId;
                }
                var unitEntity = await unitBll.GetEntity(operatorinfo.UnitId ?? 0);
                string code = "";
                if (unitEntity != null && unitEntity.Data != null)
                {
                    code = unitEntity.Data.Code;
                }
                var resultUser = await userBll.GetEntity(userid);
                if (resultUser != null && resultUser.Data != null)
                {
                    var tokenQueryParams = new Dictionary<string, string>
                            {
                                { "code", code },
                                { "uuid", uuid },
                                { "type", "0" },
                                { "content", "你有一个实验待安排。" },
                                { "rcv_uuuid", resultUser.Data.ThirdUserId },
                                { "parameter",  Guid.NewGuid().ToString() },
                                { "uri", objEntity.AuthHost },
                                { "wxuri", objEntity.CallBackUrl },
                            };

                    var content = new FormUrlEncodedContent(tokenQueryParams);

                    using var httpClient = new HttpClient(new HttpClientBusinessHandler("提交推送消息失败。"));

                    httpClient.BaseAddress = new Uri(objEntity.AuthHost);
                    var credentials = $"{objEntity.ClientId}:{objEntity.ClientSecret}";
                    var base64Credentials = Convert.ToBase64String(Encoding.ASCII.GetBytes(credentials));
                    httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", base64Credentials);
                    httpClient.DefaultRequestHeaders.Add("token", token);
                    var response = await httpClient.PostAsync(requestTokenUri, content);
                    var tokenResponseJson = await response.Content.ReadAsStringAsync();
                    LogHelper.Error($"---->从【无锡市智慧教育】推送任务，给实验员推送消息，返回值：{tokenResponseJson}", null);
                    var tokenResponse = JsonConvert.DeserializeObject<WxResult>(tokenResponseJson);
                    if (tokenResponse.code == "0")
                    {
                        LogHelper.Error($"---->从【无锡市智慧教育】推送任务，给实验员推送消息，推送成功。", null);
                    }
                }
                else
                {
                    LogHelper.Error($"---->从【无锡市智慧教育】推送任务，给实验员推送消息，未查询到需要推送数据的实验员，失败请求为:{requestTokenUri}", null);
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"---->从【无锡市智慧教育】推送任务，给实验员推送消息，失败请求为:{requestTokenUri}，{ex.StackTrace}", null);
                throw new BusinessException($"从【无锡市智慧教育】推送任务。" + ex.Message);
            }
        }

        /// <summary>
        /// 获取Session
        /// </summary>
        /// <param name="key">键</param>
        /// <returns>返回对应的值</returns>
        protected string GetSession(string key)
        {
            var value = HttpContext.Session.GetString(key);
            if (string.IsNullOrEmpty(value))
                value = string.Empty;
            return value;
        }
        #endregion

        #region 改版获取安排信息、保存安排信息

        /// <summary>
        /// 待安排列表，已安排列表
        /// </summary>
        /// <param name="param">待安排列表{ListType:1,Statuz:10} 已安排列表{ListType:0(或者不传递),Statuz:0或者不传递,ThanStatuz:10}</param>
        /// <returns></returns>
        [Route("getbookingarrangelist")]
        [HttpPost]
        public async Task<TData<List<ExperimentBookingEntity>>> GetBookingArrangeList(SerachApiBookingParam param)
        {
            if (param == null) { return null; }
            experimentBookingBLL.ApiToken = param.SessionId;
            TData<List<ExperimentBookingEntity>> obj = new TData<List<ExperimentBookingEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(param.SessionId);
            if (operatorinfo == null)
            {
                obj.Tag = -1;
                obj.Message = "登录信息失效！";
                return obj;
            }

            var paramBooking = new ExperimentBookingListParam();
            paramBooking.ExperimentName = param.KeyWord;
            paramBooking.SafeguardUserId = operatorinfo.UserId;
            paramBooking.RecordMode = ExperimentRecordModeEnum.BookIng.ParseToInt();
            paramBooking.Statuz = param.Statuz;
            paramBooking.ThanStatuz = param.ThanStatuz;
            paramBooking.IsMain = 1;
            paramBooking.ListType = param.ListType;
            //
            var pagination = new Pagination();
            pagination.Sort = "Id";
            pagination.SortType = "desc";
            pagination.PageIndex = param.PageIndex;
            pagination.PageSize = param.PageSize;
            obj = await experimentBookingBLL.GetArrangeList(paramBooking, pagination);
            return obj;
        }


        /// <summary>
        /// 获取安排实体信息
        /// </summary>
        /// <param name="id">安排Id</param>
        /// <param name="sessionId"></param>
        /// <returns></returns>
        [Route("getarragejson")]
        [HttpGet]
        public async Task<TData<ExperimentBookingEntity>> GetArrageJson(long id, string sessionId)
        {
            experimentBookingBLL.ApiToken = StringFilter.SearchSql(sessionId);
            return await experimentBookingBLL.GetEditArrangeForm(id);
        }

        /// <summary>
        /// 保存安排信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [Route("savearrange")]
        [HttpPost]
        public async Task<TData<string>> SaveArrange(ExperimentBookingInputModel model)
        {
            TData<string> obj = new TData<string>();
            if (model != null)
            {
                experimentBookingBLL.ApiToken = model.SessionId;
            }
            return await experimentBookingBLL.SaveAppArrange(model);
        }
        #endregion

        #region 改版 获取登记信息、保存登记信息
        /// <summary>
        /// 待安排列表，已安排列表
        /// </summary>
        /// <param name="param">待登记列表{Statuz:20} 已登记列表{ThanStatuz:20}</param>
        /// <returns></returns>
        [Route("getbookingrecordlist")]
        [HttpPost]
        public async Task<TData<List<ExperimentBookingEntity>>> GetBookingRecordList(SerachApiBookingParam param)
        {

            TData<List<ExperimentBookingEntity>> obj = new TData<List<ExperimentBookingEntity>>();
            if (param == null)
            {
                obj.Tag = -1; obj.Message = "非法操作，请从页面点击操作";
                return obj;
            }
            experimentBookingBLL.ApiToken = param.SessionId;
            OperatorInfo operatorinfo = await Operator.Instance.Current(param.SessionId);
            if (operatorinfo == null)
            {
                obj.Tag = -1;
                obj.Message = "登录信息失效！";
                return obj;
            }

            var paramBooking = new ExperimentBookingListParam();
            paramBooking.ExperimentName = param.KeyWord;
            paramBooking.BookUserId = operatorinfo.UserId;
            paramBooking.RecordMode = ExperimentRecordModeEnum.BookIng.ParseToInt();
            paramBooking.Statuz = param.Statuz;
            paramBooking.ThanStatuz = param.ThanStatuz;
            paramBooking.IsMain = 0;

            var pagination = new Pagination();
            pagination.Sort = "Id";
            pagination.SortType = "desc";
            pagination.PageIndex = param.PageIndex;
            pagination.PageSize = param.PageSize;

            obj = await experimentBookingBLL.GetRecordList(paramBooking, pagination);
            return obj;
        }

        /// <summary>
        /// 获取登记实体信息
        /// </summary>
        /// <param name="id">安排Id</param>
        /// <param name="sessionId"></param>
        /// <returns></returns>
        [Route("getrecordjson")]
        [HttpGet]
        public async Task<TData<ExperimentBookingEntity>> GetRecordJson(long id, string sessionId)
        {
            experimentBookingBLL.ApiToken = StringFilter.SearchSql(sessionId);
            return await experimentBookingBLL.GetRecordForm(id);
        }

        /// <summary>
        /// 保存登记信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [Route("saverecord")]
        [HttpPost]
        public async Task<TData<string>> SaveRecord(ExperimentBookArrangeRecordInputModel model)
        {
            TData<string> obj = new TData<string>();
            if (model != null)
            {
                experimentBookingBLL.ApiToken = model.SessionId;
            }
            return await experimentBookingBLL.SaveAppRecord(model);
        }
        #endregion

        #region 获取预约、登记实验列表方法

        /// <summary>
        /// 获取当前学年学期数据
        /// </summary>
        /// <returns></returns>
        [Route("getbookinglist")]
        [HttpPost]
        public async Task<TData<List<ExperimentPlanViewModel>>> GetBookingExperimentList(SerachApiBookingParam param)
        {
            TData<List<ExperimentPlanViewModel>> obj = new TData<List<ExperimentPlanViewModel>>();
            if (param != null)
            {
                experimentBookingBLL.ApiToken = param.SessionId;
                OperatorInfo operatorInfo = await Operator.Instance.Current(StringFilter.SearchSql(param.SessionId));
                string sortName = " AllNum DESC, WeekNum ASC ";
                if (param.SourceType == SourceTypeEnum.ExperimentList.ParseToInt())
                {
                    sortName = " AllNum DESC, Chapter ASC ";
                }
                obj = await experimentBookingBLL.GetPlanList(new ExperimentBookingListParam
                {
                    ExperimentName = param.KeyWord,
                    RecordMode = param.RecordMode,
                    SourceType = param.SourceType,
                    SchoolYearStart = param.SchoolYearStart,
                    ExperimentType = param.ExperimentType,
                    IsNeedDo = param.IsNeedDo,
                    SchoolTerm = param.SchoolTerm,
                    GradeId = param.GradeId,
                    ExperimentVersionId = param.ExperimentVersionId,
                }, new Pagination
                {
                    Sort = sortName,
                    SortType = "desc",
                    PageIndex = 1,
                    PageSize = 10000
                });
                if (obj.Data != null && obj.Data.Count > 0)
                {
                    param.PageIndex = param.PageIndex <= 1 ? 1 : param.PageIndex;
                    param.PageSize = param.PageSize <= 1 ? 10 : param.PageSize;
                    int takeNum = param.PageSize;
                    int skipnum = param.PageSize * (param.PageIndex - 1);
                    obj.Data = obj.Data.Skip(skipnum).Take(takeNum).ToList();
                }
            }
            return obj;
        }

        /// <summary>
        /// 获取仪器列表（本单位）
        /// </summary>
        /// <returns></returns>
        [Route("getinstrumentList")]
        [HttpPost]
        public async Task<TData<List<SchoolInstrumentEntity>>> GetInstrumentList(SerachApiBookingParam param)
        {
            TData<List<SchoolInstrumentEntity>> obj = new TData<List<SchoolInstrumentEntity>>();
            if (param != null)
            {
                experimentBookingBLL.ApiToken = param.SessionId;


                obj = await experimentBookingBLL.GetInstrumentList(new Model.Param.InstrumentManage.SchoolInstrumentListParam
                {
                    KeyWord = param.KeyWord,
                }, new Pagination
                {
                    Sort = "Id",
                    SortType = "desc",
                    PageIndex = param.PageIndex,
                    PageSize = param.PageSize
                });
                if (obj != null && obj.Data != null && obj.Data.Count > 0)
                {
                    obj.Data.ForEach(m => m.Model = m.Model ?? "");
                }
            }
            return obj;
        }
        #endregion
        #region 改版 简易登记,列表查询方法、页面信息获取方法、整体保存方法。

        /// <summary>
        /// 已预约列表简易登记
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [Route("geteasylisted")]
        [HttpPost]
        public async Task<TData<List<ExperimentBookingEntity>>> GetEasyListed(SerachApiParam param)
        {
            if (param == null) { return null; }
            experimentBookingBLL.ApiToken = param.SessionId;
            TData<List<ExperimentBookingEntity>> obj = new TData<List<ExperimentBookingEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(param.SessionId);
            if (operatorinfo == null)
            {
                obj.Tag = -1;
                obj.Message = "登录信息失效！";
                return obj;
            }

            var paramBooking = new ExperimentBookingListParam();
            paramBooking.ExperimentName = param.KeyWord;
            paramBooking.BookUserId = operatorinfo.UserId;
            paramBooking.RecordMode = ExperimentRecordModeEnum.Easy.ParseToInt();
            paramBooking.ThanStatuz = ExperimentBookStatuzEnum.BookIng.ParseToInt();
            paramBooking.IsMain = 1;
            var pagination = new Pagination();
            pagination.Sort = "Id";
            pagination.SortType = "desc";
            pagination.PageIndex = param.PageIndex;
            pagination.PageSize = param.PageSize;
            obj = await experimentBookingBLL.GetBookingedList(paramBooking, pagination);
            return obj;
        }

        /// <summary>
        /// 保存登记信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [Route("saverecordeasy")]
        [HttpPost]
        public async Task<TData<string>> SaveRecordEasy(ExperimentBookingInputModel model)
        {
            TData<string> obj = new TData<string>();
            if (model != null)
            {
                experimentBookingBLL.ApiToken = model.SessionId;
            }
            return await experimentBookingBLL.SaveAppRecordEasy(model);
        }
        #endregion

        #region 保存修改已登记信息的，登记信息
        [Route("saverecordedit")]
        [HttpPost]
        public async Task<TData<string>> SaveRecordEdit(ExperimentBookingInputModel model)
        {
            TData<string> obj = new TData<string>();
            if (model != null)
            {
                experimentBookingBLL.ApiToken = model.SessionId;
            }
            return await experimentBookingBLL.SaveEditRecord(model);
        }
        #endregion

        #region 仪器借还接口。
        /// <summary>
        /// 实验仪器借还列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [Route("getloanlist")]
        [HttpPost]
        public async Task<TData<List<ExperiInstruStatuzEntity>>> LoanList(ExperiInstruApiParam param)
        {
            if (param == null) { return null; }
            experiInstruLogBLL.ApiToken = param.SessionId;
            TData<List<ExperiInstruStatuzEntity>> obj = new TData<List<ExperiInstruStatuzEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(param.SessionId);
            if (operatorinfo == null)
            {
                obj.Tag = -1;
                obj.Message = "登录信息失效！";
                return obj;
            }

            var paramExperiInstru = new ExperiInstruStatuzListParam();
            paramExperiInstru.ExperimentName = param.KeyWord;
            paramExperiInstru.IsShowAll = param.IsShowAll;
            paramExperiInstru.SchoolYearStart = param.SchoolYearStart;
            paramExperiInstru.SchoolTerm = param.SchoolTerm;
            paramExperiInstru.LoanSysUserId = param.LoanSysUserId;
            paramExperiInstru.Statuz = param.Statuz ?? -1;
            paramExperiInstru.ExperimentStatuz = param.ExperimentStatuz;
            //其他条件小程序暂不支持，如支持加条件赋值
            var pagination = new Pagination();
            pagination.Sort = "Id";
            pagination.SortType = "desc";
            pagination.PageIndex = param.PageIndex;
            pagination.PageSize = param.PageSize;
            obj = await experiInstruLogBLL.GetStatuzPage(paramExperiInstru, pagination);
            return obj;
        }

        /// <summary>
        /// 获取实验借出详情。
        /// </summary>
        /// <param name="id"></param>
        /// <param name="sessionId"></param>
        /// <returns></returns>
        [Route("getloanjson")]
        [HttpGet]
        public async Task<TData<ExperiInstruStatuzEntity>> GetLoanFormJson(long id, string sessionId)
        {
            experiInstruLogBLL.ApiToken = sessionId;
            return await experiInstruLogBLL.GetEntity(id);
        }

        /// <summary>
        /// 确认保存借出
        /// </summary>
        /// <param name="modelz"></param>
        /// <returns></returns>
        [Route("saveloanjson")]
        [HttpPost]
        public async Task<TData<string>> SaveLoanFormJson(ExperiInstruLogInputModel modelz)
        {
            if (modelz != null)
            {
                experiInstruLogBLL.ApiToken = modelz.SessionId;
            }
            return await experiInstruLogBLL.SaveLoanForm(modelz);
        }

        /// <summary>
        /// 确认添加仪器
        /// </summary>
        /// <param name="modelz"></param>
        /// <returns></returns>
        [Route("saveloanaddInstrment")]
        [HttpPost]
        public async Task<TData> SaveLoanAddInstrmentJson(ExperiInstruLogInputModel modelz)
        {
            if (modelz != null)
            {
                experiInstruLogBLL.ApiToken = modelz.SessionId;
            }
            return await experiInstruLogBLL.SaveAddInstrment(modelz);
        }

        /// <summary>
        /// 修改仪器型号
        /// </summary>
        /// <param name="modelz"></param>
        /// <returns></returns>
        [Route("saveloanInstrmentmodel")]
        [HttpPost]
        public async Task<TData> SaveLoanInstrmentModelJson(ExperiInstruLogInputModel modelz)
        {
            if (modelz != null)
            {
                experiInstruLogBLL.ApiToken = modelz.SessionId;
            }
            return await experiInstruLogBLL.SaveInstrmentModel(modelz);
        }

        /// <summary>
        /// 修改仪器单位名称
        /// </summary>
        /// <param name="modelz"></param>
        /// <returns></returns>
        [Route("saveloanInstrmentunit")]
        [HttpPost]
        public async Task<TData> SaveLoanInstrmentunitJson(ExperiInstruLogInputModel modelz)
        {
            if (modelz != null)
            {
                experiInstruLogBLL.ApiToken = modelz.SessionId;
            }
            return await experiInstruLogBLL.SaveInstrmentUnitName(modelz);
        }

        /// <summary>
        /// 修改仪器数量
        /// </summary>
        /// <param name="modelz"></param>
        /// <returns></returns>
        [Route("saveloanInstrmentnum")]
        [HttpPost]
        public async Task<TData> SaveLoanInstrmentnumJson(ExperiInstruLogInputModel modelz)
        {
            if (modelz != null)
            {
                experiInstruLogBLL.ApiToken = modelz.SessionId;
            }
            return await experiInstruLogBLL.SaveInstrmentNum(modelz);
        }

        /// <summary>
        /// 确认删除仪器
        /// </summary>
        /// <param name="modelz"></param>
        /// <returns></returns>
        [Route("saveloandelInstrment")]
        [HttpPost]
        public async Task<TData> SaveLoanDelInstrmentJson(ExperiInstruLogInputModel modelz)
        {
            if (modelz != null)
            {
                experiInstruLogBLL.ApiToken = modelz.SessionId;
            }
            return await experiInstruLogBLL.SaveDelInstrment(modelz);
        }




        /// <summary>
        /// 确认保存归还
        /// </summary>
        /// <param name="modelz">实体信息</param>
        /// <returns></returns>
        [Route("savebackjson")]
        [HttpPost]
        public async Task<TData> SaveBackFormJson(ExperiInstruLogInputModel modelz)
        {
            if (modelz == null)
            {
                return new TData() { Tag = 0, Message = "你无权访问" };
            }
            experiInstruLogBLL.ApiToken = modelz.SessionId;
            return await experiInstruLogBLL.BackForm(modelz.Id);
        }
        #endregion


        /// <summary>
        /// 获取教材版本下拉集合
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [Route("getversionlist")]
        [HttpPost]
        public async Task<TData<List<TextbookVersionCurrentEntity>>> GetVersionList(TextbookVersionApiParam param)
        {
            if (param == null) { return null; }
            experimentBookingBLL.ApiToken = param.SessionId;
            TData<List<TextbookVersionCurrentEntity>> obj = new TData<List<TextbookVersionCurrentEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(param.SessionId);
            if (operatorinfo == null)
            {
                obj.Tag = -1;
                obj.Message = "登录信息失效！";
                return obj;
            }
            TextbookVersionCurrentListParam paramVersion=new TextbookVersionCurrentListParam();
            paramVersion.SchoolTerm = param.SchoolTerm;
            paramVersion.GradeId = param.GradeId;
            paramVersion.CourseId = param.CourseId;
            obj = await experimentBookingBLL.GetVersionList(paramVersion);
            return obj;
        }

        /// <summary>
        /// 获取预约年级下拉集合
        /// </summary>
        /// <param name="dictionaryId"></param>
        /// <param name="sessionId"></param>
        /// <returns></returns>
        [Route("getbookinggradelist")]
        [HttpGet]
        public async Task<TData<List<StaticDictionaryEntity>>> GetBookingGradeList(int dictionaryId, string sessionId)
        {
            dicBLL.ApiToken = sessionId;
            TData<List<StaticDictionaryEntity>> obj = new TData<List<StaticDictionaryEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(sessionId);
            if (operatorinfo == null)
            {
                obj.Tag = -1;
                obj.Message = "登录信息失效！";
                return obj;
            }
            StaticDictionaryListParam param = new StaticDictionaryListParam();
            param.DictionaryId = dictionaryId;
            obj = await dicBLL.GetSchoolGradeList(param);
            return obj;
        }
    }
    /// <summary>
    /// 无锡 推送消息，调用推送消息实验员
    /// </summary>
    public class WxResult
    {
        public string code { get; set; }

        public string msg { get; set; }

        public string request_id { get; set; }
    }
}
