﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.PaperExamineManage;
using Dqy.Syjx.Model.Param.PaperExamineManage;
using Dqy.Syjx.Enum.BusinessManage;

namespace Dqy.Syjx.Service.PaperExamineManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2022-08-04 11:36
    /// 描 述：试卷题库选项服务类
    /// </summary>
    public class PaperQuestionOptionsService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<PaperQuestionOptionsEntity>> GetList(PaperQuestionOptionsListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<PaperQuestionOptionsEntity>> GetPageList(PaperQuestionOptionsListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<PaperQuestionOptionsEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<PaperQuestionOptionsEntity>(id);
        }

        public async Task<List<PaperQuestionOptionsEntity>> GetAllList(PaperQuestionOptionsListParam param)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append($@"SELECT * From (
                SELECT
                pq4.Id ,pq4.BaseIsDelete ,pq4.BaseCreateTime ,pq4.BaseModifyTime ,pq4.BaseCreatorId ,pq4.BaseModifierId ,pq4.BaseVersion
                ,pq4.QuestionBankId ,pq4.QuestionBankOptionId ,pq4.PaperId ,pq4.PaperQuestionBankId
                ,pq4.Name ,pq4.NameDesc ,pq4.Sort ,pq4.IsTrue ,pq4.Statuz ,pq4.Remark
                ,ISNULL((SELECT TOP (1) a3.Path FROM  bn_Attachment AS a3 WHERE  pq4.Id = a3.ObjectId AND a3.BaseIsDelete = 0 AND a3.IsDelete = 0 AND FileCategory = {FileCategoryEnum.QuestionBankOption.ParseToInt()}),'') AS PicturePath
                FROM  cp_PaperQuestionOptions AS pq4
                INNER JOIN   cp_PaperQuestionBank AS pq1 ON  pq1.BaseIsDelete = 0 AND pq1.Statuz = 1 AND pq4.PaperQuestionBankId = pq1.Id
                INNER JOIN  cp_Paper AS p2 ON pq1.PaperId = p2.Id AND p2.BaseIsDelete= 0
                WHERE pq4.BaseIsDelete = 0 AND pq4.Statuz = 1
            )tb1 WHERE 1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.PaperId > 0)
                {
                    strSql.Append(" AND PaperId = @PaperId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@PaperId", param.PaperId));
                }
            }
            var list = await this.BaseRepository().FindList<PaperQuestionOptionsEntity>(strSql.ToString(), parameter.ToArray());
            return list.ToList();
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(PaperQuestionOptionsEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(PaperQuestionOptionsEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
               strSql = $" update cp_PaperQuestionOptions set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
               strSql = $"update cp_PaperQuestionOptions set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }

        /// <summary>
        /// 根据试卷Id删除试卷题目选项
        /// </summary>
        /// <returns></returns>
        public async Task DeleteByPaperId(long paperid)
        {
            string strSql = $"update cp_PaperQuestionOptions set BaseIsDelete = 1 where PaperId = {paperid}";
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<PaperQuestionOptionsEntity, bool>> ListFilter(PaperQuestionOptionsListParam param)
        {
            var expression = LinqExtensions.True<PaperQuestionOptionsEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (param.QuestionBankId > 0)
                {
                    expression = expression.And(t => t.QuestionBankId == param.QuestionBankId);
                }
                if (param.PaperQuestionBankId > 0)
                {
                    expression = expression.And(t => t.PaperQuestionBankId == param.PaperQuestionBankId);
                }
                if (param.PaperId > 0)
                {
                    expression = expression.And(t => t.PaperId == param.PaperId);
                }
            }
            return expression;
        }

        #endregion
    }
}
