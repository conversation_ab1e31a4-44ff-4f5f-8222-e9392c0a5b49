﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.EvaluateManage;
using Dqy.Syjx.Model.Param.EvaluateManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Service.OrganizationManage;
using Dqy.Syjx.Enum.InstrumentManage;
using Dynamitey.DynamicObjects;
using static Dqy.Syjx.Util.ThirdOAuth.TztxSoftAcore;

namespace Dqy.Syjx.Service.EvaluateManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2022-01-05 13:07
    /// 描 述：服务类
    /// </summary>
    public class InstrumentAttendStaticService :  RepositoryFactory
    {

        private UserSubjectSetService userSubjectSetService = new UserSubjectSetService();

        #region 获取数据

        public async Task<List<InstrumentAttendStaticEntity>> GetList(InstrumentAttendStaticListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        /// <summary>
        /// 根据
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<InstrumentAttendStaticEntity> GetEntityByCondition(InstrumentAttendStaticListParam param)
        {
            var expression = ListFilter(param);
            InstrumentAttendStaticEntity obj = null;
            var list = await this.BaseRepository().FindList(expression);
            if (list.ToList().Count > 0)
            {
                obj = list.FirstOrDefault();
            }
            return obj;
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<InstrumentAttendStaticEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<InstrumentAttendStaticEntity>(id);
        }

        public async Task<List<InstrumentAttendStaticEntity>> GetPageList(InstrumentAttendStaticListParam param, Pagination pagination)
        {
            StringBuilder sql = new StringBuilder();
            sql.Append(@" SELECT * From (
                            SELECT  S.Id ,ES.Id AS InstrumentStandardId,
                            S.BaseIsDelete ,S.BaseCreateTime ,S.BaseModifyTime ,S.BaseCreatorId ,S.BaseModifierId ,S.BaseVersion ,
                            S.InstrumentEvaluateProjectId ,S.SchoolId ,S.StageId ,S.CourseId ,
                            IEL.ContrastCode AS InstrumentCode ,S.Name ,S.Model ,S.StandardNum ,S.StockNum ,
                            S.UnitName ,S.AllocateType ,S.InstrumentCode AS InstrumentStandardCode ,
		                    U.Name AS SchoolName ,SD.DicName AS StageName ,SD2.DicName AS CourseName ,
                            (S.StockNum - S.StandardNum) AS DifferenceNum ,UR.UnitId AS CountyId ,U.Sort ,UR2.UnitId AS CityId ,IEL.IsEvaluate     ");
            if (param.IsSearchInuptNum == 1)
            {
                sql.Append(",ISNULL(A.InputNum,0) AS InputNum");
            }
            sql.Append(@"   FROM  eq_InstrumentAttendStatic AS S
                               INNER JOIN  up_Unit AS U ON S.SchoolId = U.Id
                               INNER JOIN  sys_static_dictionary AS SD ON S.StageId = SD.DictionaryId
                                           AND SD.TypeCode = '1002' AND SD.BaseIsDelete = 0 
                               INNER JOIN  sys_static_dictionary AS SD2 ON S.CourseId = SD2.DictionaryId
                                           AND SD2.TypeCode = '1005' AND SD2.BaseIsDelete = 0	
                               INNER JOIN  up_UnitRelation AS UR ON UR.ExtensionObjId = S.SchoolId AND UR.ExtensionType = 3 AND UR.BaseIsDelete = 0
                               INNER JOIN  up_UnitRelation AS UR2 ON UR.UnitId = UR2.ExtensionObjId AND UR2.ExtensionType = 3 AND UR2.BaseIsDelete = 0

                              INNER JOIN  eq_InstrumentEvaluateProjectVersion AS EV ON EV.EvaluateProjectId = S.InstrumentEvaluateProjectId AND EV.SchoolStage = S.StageId AND EV.DictionaryId1005 = S.CourseId AND EV.BaseIsDelete = 0
							  INNER JOIN  eq_InstrumentEvaluateStandard AS ES ON EV.EvaluateStandardId = ES.Id AND ES.BaseIsDelete = 0 		
							  INNER JOIN eq_InstrumentEvaluateList AS IEL ON S.EvaluateListId = IEL.Id AND IEL.BaseIsDelete = 0 AND IEL.Statuz = 1
                             ");
            if (param.IsSearchInuptNum == 1)
            {
                sql.Append(@$"LEFT JOIN(

                        SELECT SD.Code,PD.Name,ISNULL(SUM(PD.Num),0) AS InputNum
                        FROM eq_PurchaseDeclaration AS PD
                        INNER JOIN  eq_InstrumentStandard AS SD ON PD.InstrumentStandardId = SD.Id
                        WHERE PD.BaseIsDelete = 0 AND  PD.SchoolId = {param.SchoolId} AND PD.Statuz > -1 AND YEAR(PD.BaseCreateTime) = YEAR(GETDATE())
                        GROUP BY SD.Code,PD.Name
                        ) AS A ON S.InstrumentCode = A.Code AND S.Name = A.Name");
            }

            sql.Append(" ) as T WHERE  IsEvaluate = 1 ");
            var parameter = new List<DbParameter>();
            if (parameter != null)
            {
                if (!param.SchoolId.IsNullOrZero())
                {
                    sql.Append(" AND SchoolId = @SchoolId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
                }
                if (!param.CountyId.IsNullOrZero())
                {
                    sql.Append(" AND CountyId = @CountyId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
                if (!param.CityId.IsNullOrZero())
                {
                    sql.Append(" AND CityId = @CityId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CityId", param.CityId));
                }
                if (!param.InstrumentEvaluateProjectId.IsNullOrZero())
                {
                    sql.Append(" AND InstrumentEvaluateProjectId = @InstrumentEvaluateProjectId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@InstrumentEvaluateProjectId", param.InstrumentEvaluateProjectId));
                }
                if (!param.SchoolStageId.IsNullOrZero())
                {
                    sql.Append(" AND StageId = @StageId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@StageId", param.SchoolStageId));
                }
                if (!param.CourseId.IsNullOrZero())
                {
                    sql.Append(" AND CourseId = @CourseId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (!param.KeyWord.IsEmpty())
                {
                    sql.Append($" AND (Name LIKE '%{param.KeyWord}%' OR InstrumentCode LIKE '%{param.KeyWord}%') ");
                }
                if (param.IsOnlyShowNoStandard == 1)
                {
                    sql.Append(" AND (StockNum - StandardNum) < 0 ");
                }
                if (!param.AllocateType.IsNullOrZero())
                {
                    sql.Append(" AND AllocateType = @AllocateType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@AllocateType", param.AllocateType.Value));
                }
                if (!param.SchoolStageIdz.IsEmpty())
                {
                    sql.AppendFormat(" AND StageId IN ({0})", param.SchoolStageIdz);
                }
                if (!param.CourseIdz.IsEmpty())
                {
                    sql.AppendFormat(" AND CourseId IN ({0})", param.CourseIdz);
                }
            }
            var list = await this.BaseRepository().FindList<InstrumentAttendStaticEntity>(sql.ToString(), parameter.ToArray(), pagination);
            return list.ToList();
        }

        /// <summary>
        /// 获取市级仪器达标汇总统计数据
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<List<InstrumentAttendStaticEntity>> GetCityGroupPageList(InstrumentAttendStaticListParam param, Pagination pagination)
        {
            int beginNumber = (pagination.PageIndex - 1) * pagination.PageSize;
            int endNumber = pagination.PageIndex * pagination.PageSize;
            if (pagination.Sort.ToUpper().IndexOf("ASC") > -1 || pagination.Sort.ToUpper().IndexOf("DESC") > -1)
            {
                pagination.SortType = "";
            }

            StringBuilder sql = new StringBuilder();

            if (GlobalContext.SystemConfig.DBProvider.Equals("Dm") || GlobalContext.SystemConfig.DBProvider.Equals("MySql"))
            {
                sql.Append(@" SELECT ");
            }
            else
            {
                sql.AppendFormat(" SELECT    ROW_NUMBER() OVER ( ORDER BY {0} {1}) AS ROWNUM ,", pagination.Sort, pagination.SortType);
            }

            sql.AppendFormat(@"      * ,Convert(decimal(18,2),( StandardCount * 1.00 /  NeedStandardCount * 100)) AS StandardRate
                            FROM      ( SELECT    S.InstrumentEvaluateProjectId ,
					                              UR2.UnitId AS CityId ,
					                              UR.UnitId AS CountyId ,U2.Name AS CountyName ,U2.Sort AS CountySort ,AR.AreaName ,
                                                  S.StageId ,SD.DicName AS StageName ,
                                                  S.CourseId ,SD2.DicName AS CourseName ,
                                                  S.AllocateType ,COUNT(1) AS NeedStandardCount ,
                                                  ( SELECT    COUNT(1)
                                                    FROM       eq_InstrumentAttendStatic AS IAS
                                                    INNER JOIN eq_InstrumentEvaluateList AS EL ON IAS.EvaluateListId = EL.Id AND EL.Statuz = 1 AND EL.BaseIsDelete = 0
													INNER	  JOIN up_UnitRelation as UUR ON IAS.SchoolId = UUR.ExtensionObjId AND UUR.ExtensionType = 3
                                                    WHERE     IAS.BaseIsDelete = 0 AND UUR.UnitId = UR.UnitId AND IAS.InstrumentEvaluateProjectId = S.InstrumentEvaluateProjectId
                                                              AND IAS.StageId = S.StageId AND IAS.CourseId = S.CourseId AND IAS.AllocateType = S.AllocateType AND IAS.StockNum >= IAS.StandardNum
                                                              AND EL.IsEvaluate = 1) AS StandardCount
                                        FROM       eq_InstrumentAttendStatic AS S
                                                  INNER JOIN eq_InstrumentEvaluateList AS L ON S.EvaluateListId = L.Id AND L.Statuz = 1 AND L.BaseIsDelete = 0
                                                  INNER JOIN  sys_static_dictionary AS SD ON S.StageId = SD.DictionaryId AND SD.TypeCode = '1002' AND SD.BaseIsDelete = 0
                                                  INNER JOIN  sys_static_dictionary AS SD2 ON S.CourseId = SD2.DictionaryId AND SD2.TypeCode = '1005' AND SD2.BaseIsDelete = 0
                                                  INNER JOIN  up_Unit AS U ON S.SchoolId = U.Id
                                                  INNER JOIN  up_UnitRelation AS UR ON UR.ExtensionObjId = S.SchoolId AND UR.ExtensionType = 3 AND UR.BaseIsDelete = 0
					                              INNER JOIN  up_UnitRelation AS UR2 ON UR2.ExtensionObjId = UR.UnitId AND UR2.ExtensionType = 3 AND UR2.BaseIsDelete = 0
					                              INNER JOIN  up_Unit AS U2 ON UR.UnitId = U2.Id
                                                  INNER JOIN  SysArea AS AR ON U2.AreaId = AR.AreaCode
                                        WHERE     S.BaseIsDelete = 0 AND L.IsEvaluate = 1
                                        GROUP BY  S.InstrumentEvaluateProjectId ,S.StageId ,S.CourseId ,S.AllocateType ,
					                           U2.Sort ,UR.UnitId ,UR2.UnitId ,U2.Name ,AR.AreaName ,
                                                  SD.DicName ,SD2.DicName
                            ) T WHERE 1 = 1
            ", DicTypeCodeEnum.SchoolStage.ParseToInt(), DicTypeCodeEnum.Course.ParseToInt());
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (!param.CityId.IsNullOrZero())
                {
                    sql.Append(" AND CityId = @CityId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CityId", param.CityId));
                }
                if (!param.CountyId.IsNullOrZero())
                {
                    sql.Append(" AND CountyId = @CountyId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
                if (!param.InstrumentEvaluateProjectId.IsNullOrZero())
                {
                    sql.Append(" AND InstrumentEvaluateProjectId = @InstrumentEvaluateProjectId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@InstrumentEvaluateProjectId", param.InstrumentEvaluateProjectId));
                }
                if (!param.AllocateType.IsNullOrZero())
                {
                    sql.Append(" AND AllocateType = @AllocateType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@AllocateType", param.AllocateType.Value));
                }
                if (!param.SchoolStageId.IsNullOrZero())
                {
                    sql.Append(" AND StageId = @StageId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@StageId", param.SchoolStageId.Value));
                }
                if (!param.CourseId.IsNullOrZero())
                {
                    sql.Append(" AND CourseId = @CourseId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId.Value));
                }
            }

            StringBuilder searchSql = new StringBuilder();
            searchSql.Append("SELECT * FROM (");
            searchSql.Append(sql.ToString());

            if (GlobalContext.SystemConfig.DBProvider.Equals("Dm") || GlobalContext.SystemConfig.DBProvider.Equals("MySql"))
            {
                searchSql.AppendFormat(" ) N limit {0},{1}", beginNumber, pagination.PageSize);
            }
            else
            {
                searchSql.AppendFormat(" ) N WHERE ROWNUM > {0} AND ROWNUM <= {1}", beginNumber, endNumber);
            }
            var list = await this.BaseRepository().FindList<InstrumentAttendStaticEntity>(searchSql.ToString(), parameter.ToArray());

            var totalList = await this.BaseRepository().FindList<InstrumentAttendStaticEntity>(sql.ToString(), parameter.ToArray());
            pagination.TotalCount = totalList.Count();

            return list.ToList();
        }

        /// <summary>
        /// 获取仪器达标汇总统计数据
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<List<InstrumentAttendStaticEntity>> GetGroupPageList(InstrumentAttendStaticListParam param, Pagination pagination)
        {
            // 构建基础查询SQL，使用通用语法
            StringBuilder sql = new StringBuilder();
            sql.Append(@"
                SELECT S.InstrumentEvaluateProjectId,
                       UR2.UnitId AS CityId,
                       UR.UnitId AS CountyId,
                       U2.Name AS CountyName,
                       U2.Sort AS CountySort,
                       AR.AreaName,
                       S.SchoolId,
                       U.Name AS SchoolName,
                       U.Sort AS SchoolSort,
                       S.StageId,
                       SD.DicName AS StageName,
                       S.CourseId,
                       SD2.DicName AS CourseName,
                       S.AllocateType,
                       COUNT(1) AS NeedStandardCount,
                       (SELECT COUNT(1)
                        FROM eq_InstrumentAttendStatic AS IAS
                        INNER JOIN eq_InstrumentEvaluateList AS IEL ON IAS.EvaluateListId = IEL.Id
                                   AND IEL.Statuz = 1 AND IEL.BaseIsDelete = 0
                        WHERE IAS.BaseIsDelete = 0
                              AND IAS.InstrumentEvaluateProjectId = S.InstrumentEvaluateProjectId
                              AND IAS.SchoolId = S.SchoolId
                              AND IAS.StageId = S.StageId
                              AND IAS.CourseId = S.CourseId
                              AND IAS.AllocateType = S.AllocateType
                              AND IAS.StockNum >= IAS.StandardNum
                              AND IEL.IsEvaluate = 1) AS StandardCount
                FROM eq_InstrumentAttendStatic AS S
                INNER JOIN eq_InstrumentEvaluateList AS EL ON S.EvaluateListId = EL.Id
                           AND EL.BaseIsDelete = 0 AND EL.Statuz = 1
                INNER JOIN sys_static_dictionary AS SD ON S.StageId = SD.DictionaryId
                           AND SD.TypeCode = @SchoolStageTypeCode AND SD.BaseIsDelete = 0
                INNER JOIN sys_static_dictionary AS SD2 ON S.CourseId = SD2.DictionaryId
                           AND SD2.TypeCode = @CourseTypeCode AND SD2.BaseIsDelete = 0
                INNER JOIN up_Unit AS U ON S.SchoolId = U.Id
                INNER JOIN up_UnitRelation AS UR ON UR.ExtensionObjId = S.SchoolId
                           AND UR.ExtensionType = 3 AND UR.BaseIsDelete = 0
                INNER JOIN up_UnitRelation AS UR2 ON UR2.ExtensionObjId = UR.UnitId
                           AND UR2.ExtensionType = 3 AND UR2.BaseIsDelete = 0
                INNER JOIN up_Unit AS U2 ON UR.UnitId = U2.Id
                INNER JOIN SysArea AS AR ON U2.AreaId = AR.AreaCode
                WHERE S.BaseIsDelete = 0 AND EL.IsEvaluate = 1");

            // 添加基础参数
            var parameter = new List<DbParameter>
            {
                DbParameterExtension.CreateDbParameter("@SchoolStageTypeCode", DicTypeCodeEnum.SchoolStage.ParseToInt().ToString()),
                DbParameterExtension.CreateDbParameter("@CourseTypeCode", DicTypeCodeEnum.Course.ParseToInt().ToString())
            };

            // 添加查询条件到WHERE子句
            if (param != null)
            {
                if (!param.CityId.IsNullOrZero())
                {
                    sql.Append(" AND UR2.UnitId = @CityId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CityId", param.CityId));
                }
                if (!param.CountyId.IsNullOrZero())
                {
                    sql.Append(" AND UR.UnitId = @CountyId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
                if (!param.SchoolId.IsNullOrZero())
                {
                    sql.Append(" AND S.SchoolId = @SchoolId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
                }
                if (!param.InstrumentEvaluateProjectId.IsNullOrZero())
                {
                    sql.Append(" AND S.InstrumentEvaluateProjectId = @InstrumentEvaluateProjectId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@InstrumentEvaluateProjectId", param.InstrumentEvaluateProjectId));
                }
                if (!param.AllocateType.IsNullOrZero())
                {
                    sql.Append(" AND S.AllocateType = @AllocateType");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@AllocateType", param.AllocateType.Value));
                }
                if (!param.SchoolStageId.IsNullOrZero())
                {
                    sql.Append(" AND S.StageId = @StageId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@StageId", param.SchoolStageId.Value));
                }
                if (!param.CourseId.IsNullOrZero())
                {
                    sql.Append(" AND S.CourseId = @CourseId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId.Value));
                }

                // 处理用户权限过滤
                if (param.SetUserId > 0)
                {
                    var listStage = await userSubjectSetService.GetUserSetStageList(param.SetUserId);
                    var validStages = listStage.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (validStages.Count > 0)
                    {
                        var stageIds = string.Join(",", validStages.Select(f => f.DictionaryId));
                        var cleanStageIds = StringFilter.ValidateAndCleanIds(stageIds);
                        if (cleanStageIds != null)
                        {
                            sql.Append($" AND S.StageId IN ({cleanStageIds})");
                        }
                    }

                    var listCourse = await userSubjectSetService.GetUserSetCourseList(param.SetUserId);
                    var validCourses = listCourse.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (validCourses.Count > 0)
                    {
                        var courseIds = string.Join(",", validCourses.Select(f => f.DictionaryId));
                        var cleanCourseIds = StringFilter.ValidateAndCleanIds(courseIds);
                        if (cleanCourseIds != null)
                        {
                            sql.Append($" AND S.CourseId IN ({cleanCourseIds})");
                        }
                    }
                }
            }

            // 添加GROUP BY子句
            sql.Append(@"
                GROUP BY S.InstrumentEvaluateProjectId, S.SchoolId, S.StageId, S.CourseId, S.AllocateType,
                         U.Sort, U2.Sort, UR.UnitId, UR2.UnitId, U.Name, U2.Name, AR.AreaName,
                         SD.DicName, SD2.DicName");

            // 使用通用分页方式，在C#中处理分页和计算StandardRate
            var allData = await this.BaseRepository().FindList<InstrumentAttendStaticEntity>(sql.ToString(), parameter.ToArray());

            // 在C#中计算StandardRate，避免使用数据库特定的CONVERT函数
            var processedData = allData.Select(item => {
                if (item.NeedStandardCount > 0)
                {
                    item.StandardRate = Math.Round((decimal)item.StandardCount / (decimal)item.NeedStandardCount * 100, 2);
                }
                else
                {
                    item.StandardRate = 0;
                }
                return item;
            }).ToList();

            // 设置总数
            pagination.TotalCount = processedData.Count;

            // 在C#中处理排序和分页
            var sortedData = ApplySorting(processedData, pagination.Sort, pagination.SortType);
            var pagedData = sortedData.Skip((pagination.PageIndex - 1) * pagination.PageSize)
                                    .Take(pagination.PageSize)
                                    .ToList();

            return pagedData;
        }

        /// <summary>
        /// 应用排序逻辑
        /// </summary>
        private List<InstrumentAttendStaticEntity> ApplySorting(List<InstrumentAttendStaticEntity> data, string sortField, string sortType)
        {
            if (string.IsNullOrEmpty(sortField))
            {
                return data.OrderBy(x => x.SchoolSort).ThenBy(x => x.CountySort).ToList();
            }

            var isDescending = sortType?.ToUpper() == "DESC";

            return sortField.ToLower() switch
            {
                "schoolname" => isDescending ? data.OrderByDescending(x => x.SchoolName).ToList() : data.OrderBy(x => x.SchoolName).ToList(),
                "countyname" => isDescending ? data.OrderByDescending(x => x.CountyName).ToList() : data.OrderBy(x => x.CountyName).ToList(),
                "stagename" => isDescending ? data.OrderByDescending(x => x.StageName).ToList() : data.OrderBy(x => x.StageName).ToList(),
                "coursename" => isDescending ? data.OrderByDescending(x => x.CourseName).ToList() : data.OrderBy(x => x.CourseName).ToList(),
                "standardrate" => isDescending ? data.OrderByDescending(x => x.StandardRate).ToList() : data.OrderBy(x => x.StandardRate).ToList(),
                "needstandardcount" => isDescending ? data.OrderByDescending(x => x.NeedStandardCount).ToList() : data.OrderBy(x => x.NeedStandardCount).ToList(),
                "standardcount" => isDescending ? data.OrderByDescending(x => x.StandardCount).ToList() : data.OrderBy(x => x.StandardCount).ToList(),
                _ => data.OrderBy(x => x.SchoolSort).ThenBy(x => x.CountySort).ToList()
            };
        }

        /// <summary>
        /// 获取实体信息
        /// </summary>
        /// <param name="id"></param>
        /// <param name="unitId"></param>
        /// <returns></returns>
        public async Task<InstrumentAttendStaticEntity> GetEntity(long id, long unitId)
        {
            StringBuilder sql = new StringBuilder();
            sql.Append($@"
                    SELECT  IA.Name ,SD.Id AS InstrumentStandardId ,
		                    IA.StageId ,D1.DicName AS StageName ,
		                    IA.CourseId ,D2.DicName AS CourseName ,SD.UnitName
                    FROM  eq_InstrumentAttendStatic AS IA
                    INNER JOIN  eq_InstrumentStandard AS SD ON IA.InstrumentCode = SD.Code
                    INNER JOIN  sys_static_dictionary AS D1 ON IA.StageId = D1.DictionaryId AND D1.TypeCode = '{DicTypeCodeEnum.SchoolStage.ParseToInt()}'
                    INNER JOIN  sys_static_dictionary AS D2 ON IA.CourseId = D2.DictionaryId AND D2.TypeCode = '{DicTypeCodeEnum.Course.ParseToInt()}'
                    WHERE IA.Id = {id} AND IA.SchoolId = {unitId}  ");
            var list = await this.BaseRepository().FindList<InstrumentAttendStaticEntity>(sql.ToString());
            if (list != null && list.Count() > 0)
            {
                return list.First();
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 获取区县仪器达标结果分析数据
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<List<InstrumentAttendStaticEntity>> GetCountyStandardResultAnalyseList(InstrumentAttendStaticListParam param, string orderBy = "")
        {
            if (orderBy.IsEmpty()) orderBy = " StageId ASC ,CourseId ASC ";

            string where = " L.IsEvaluate = 1";
            if (param.SetUserId > 0)
            {
                //判断用户是否设置了学段、学科
                var listStage = userSubjectSetService.GetUserSetStageList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                if (listStage.Count > 0)
                {
                    string stageId = string.Join(",", listStage.Select(f => f.DictionaryId));
                    where += $" AND S.StageId IN ({stageId})";
                }

                var listCourse = userSubjectSetService.GetUserSetCourseList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                if (listCourse.Count > 0)
                {
                    string courseId = string.Join(",", listCourse.Select(f => f.DictionaryId));
                    where += $" AND S.CourseId IN ({courseId})";
                }
            }
            StringBuilder sql = new StringBuilder();
            sql.AppendFormat(@"
                SELECT * ,Convert(decimal(18,2),(StandardCount * 1.00 / NeedStandardCount * 100)) AS StandardRate FROM (
	                SELECT	UR.UnitId AS CountyId ,S.StageId ,S.CourseId ,D.DicName AS StageName ,D2.DicName AS CourseName ,
			                COUNT(1) AS NeedStandardCount ,
			                ( SELECT    COUNT(1)
			                  FROM       eq_InstrumentAttendStatic AS IAS
                              INNER JOIN eq_InstrumentEvaluateList AS EL ON IAS.EvaluateListId = EL.Id AND EL.BaseIsDelete = 0 AND EL.Statuz = 1
			                  INNER	  JOIN up_UnitRelation as UUR ON IAS.SchoolId = UUR.ExtensionObjId AND UUR.ExtensionType = 3 AND UUR.BaseIsDelete = 0
			                  WHERE   IAS.BaseIsDelete = 0 AND UUR.UnitId = UR.UnitId AND IAS.InstrumentEvaluateProjectId = S.InstrumentEvaluateProjectId
				                AND IAS.StageId = S.StageId AND IAS.CourseId = S.CourseId AND IAS.StockNum >= IAS.StandardNum {2}
                                AND EL.IsEvaluate = 1 ) AS StandardCount
	                FROM  eq_InstrumentAttendStatic AS S
                    INNER JOIN eq_InstrumentEvaluateList AS L ON S.EvaluateListId = L.Id AND L.BaseIsDelete = 0 AND L.Statuz = 1
	                INNER JOIN  up_UnitRelation AS UR ON S.SchoolId = UR.ExtensionObjId AND UR.ExtensionType = 3 AND UR.BaseIsDelete = 0
	                INNER JOIN  sys_static_dictionary AS D ON S.StageId = D.DictionaryId AND D.TypeCode = '1002'
	                INNER JOIN  sys_static_dictionary AS D2 ON S.CourseId = D2.DictionaryId AND D2.TypeCode = '1005'
	                WHERE {6} AND S.InstrumentEvaluateProjectId = {0} AND L.IsEvaluate = 1 AND UR.UnitId = {1} {3} {4}
	                GROUP BY S.InstrumentEvaluateProjectId ,UR.UnitId ,S.StageId ,S.CourseId ,D.DicName ,D2.DicName
                )T ORDER BY {5}
            ", param.InstrumentEvaluateProjectId
                , param.CountyId
                , !param.AllocateType.IsNullOrZero() ? $" AND IAS.AllocateType = {param.AllocateType}" : ""
                , !param.AllocateType.IsNullOrZero() ? $" AND S.AllocateType = {param.AllocateType}" : ""
                , !param.SchoolStageId.IsNullOrZero() ? $" AND S.StageId = {param.SchoolStageId}" : ""
                , orderBy
                , where);

            return (await this.BaseRepository().FindList<InstrumentAttendStaticEntity>(sql.ToString())).ToList();
        }

        /// <summary>
        /// 获取市级仪器达标结果分析数据
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<List<InstrumentAttendStaticEntity>> GetCityStandardResultAnalyseList(InstrumentAttendStaticListParam param)
        {
            StringBuilder sql = new StringBuilder();
            sql.AppendFormat(@"
                    SELECT * ,Convert(decimal(18,2),(StandardCount * 1.00 / NeedStandardCount * 100)) AS StandardRate FROM (
                        SELECT	UR2.UnitId AS CityId ,S.StageId ,S.CourseId ,D.DicName AS StageName ,D2.DicName AS CourseName ,
                                COUNT(1) AS NeedStandardCount ,
                                ( SELECT    COUNT(1)
                                  FROM       eq_InstrumentAttendStatic AS IAS
                                  INNER JOIN eq_InstrumentEvaluateList AS EL ON IAS.EvaluateListId = EL.Id AND EL.Statuz = 1 AND EL.BaseIsDelete = 0
                                  INNER	  JOIN up_UnitRelation as UUR ON IAS.SchoolId = UUR.ExtensionObjId AND UUR.ExtensionType = 3 AND UUR.BaseIsDelete = 0
			                      INNER   JOIN  up_UnitRelation AS UUR2 ON UUR.UnitId = UUR2.ExtensionObjId AND UUR2.ExtensionType = 3 AND UUR2.BaseIsDelete = 0
                                  WHERE   IAS.BaseIsDelete = 0 AND UUR2.UnitId = UR2.UnitId AND IAS.InstrumentEvaluateProjectId = S.InstrumentEvaluateProjectId
                                    AND IAS.StageId = S.StageId AND IAS.CourseId = S.CourseId AND EL.IsEvaluate = 1 AND IAS.StockNum >= IAS.StandardNum {2} {3} ) AS StandardCount
                        FROM  eq_InstrumentAttendStatic AS S
                        INNER JOIN eq_InstrumentEvaluateList AS L ON S.EvaluateListId = L.Id AND L.Statuz = 1 AND L.BaseIsDelete = 0
                        INNER JOIN  up_UnitRelation AS UR ON S.SchoolId = UR.ExtensionObjId AND UR.ExtensionType = 3 AND UR.BaseIsDelete = 0
	                    INNER JOIN  up_UnitRelation AS UR2 ON UR.UnitId = UR2.ExtensionObjId AND UR2.ExtensionType = 3 AND UR2.BaseIsDelete = 0
                        INNER JOIN  sys_static_dictionary AS D ON S.StageId = D.DictionaryId AND D.TypeCode = '1002'
                        INNER JOIN  sys_static_dictionary AS D2 ON S.CourseId = D2.DictionaryId AND D2.TypeCode = '1005'
                        WHERE S.InstrumentEvaluateProjectId = {0} AND L.IsEvaluate = 1 AND UR2.UnitId = {1} {4} {5}
                        GROUP BY S.InstrumentEvaluateProjectId ,UR2.UnitId ,S.StageId ,S.CourseId ,D.DicName ,D2.DicName
                    )T ORDER BY StageId ASC ,CourseId ASC
            ", param.InstrumentEvaluateProjectId
                , param.CityId
                , !param.CountyId.IsNullOrZero() ? $" AND UUR.UnitId = {param.CountyId}" : ""
                , !param.AllocateType.IsNullOrZero() ? $" AND IAS.AllocateType = {param.AllocateType}" : ""
                , !param.CountyId.IsNullOrZero() ? $" AND UR.UnitId = {param.CountyId}" : ""
                , !param.AllocateType.IsNullOrZero() ? $" AND S.AllocateType = {param.AllocateType}" : "");
            return (await this.BaseRepository().FindList<InstrumentAttendStaticEntity>(sql.ToString())).ToList();
        }

        /// <summary>
        /// 根据学校Id删除数据
        /// </summary>
        /// <param name="schoolId"></param>
        /// <returns></returns>
        public async Task DeleteBySchoolId(long schoolId)
        {
            string strSql = $"DELETE FROM eq_InstrumentAttendStatic WHERE SchoolId = {schoolId}";
            await this.BaseRepository().ExecuteBySql(strSql);
        }

        /// <summary>
        /// 根据仪器配置标准明细表Id删除数据
        /// </summary>
        /// <param name="evaluateListId"></param>
        /// <returns></returns>
        public async Task DeleteByEvaluateListId(long evaluateListId)
        {
            string strSql = $"DELETE FROM eq_InstrumentAttendStatic WHERE EvaluateListId = {evaluateListId}";
            await this.BaseRepository().ExecuteBySql(strSql);
        }

        /// <summary>
        /// 清空仪器表
        /// </summary>
        /// <returns></returns>
        public async Task ClearAllAttendStatic()
        {
            string strSql = $"TRUNCATE TABLE eq_InstrumentAttendStatic";
            await this.BaseRepository().ExecuteBySql(strSql);
        }

        /// <summary>
        /// 根据仪器配置标准主表Id删除数据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task DeleteAttendStaticById(long id)
        {
            string strSql = $@"DELETE FROM eq_InstrumentAttendStatic
                                FROM eq_InstrumentAttendStatic AS IAS
                                INNER JOIN eq_InstrumentEvaluateList AS IEL ON IAS.EvaluateListId = IEL.Id
                                INNER JOIN eq_InstrumentEvaluateStandard AS IES ON IEL.EvaluateStandardId = IES.Id
                                WHERE IES.Id = {id}";
            await this.BaseRepository().ExecuteBySql(strSql);
        }

        /// <summary>
        /// 根据学校Id查询已生成仪器达标数据
        /// </summary>
        /// <param name="schoolId">学校Id</param>
        /// <returns></returns>
        public async Task<List<InstrumentAttendStaticEntity>> GetAttendStaticListBySchoolId(long schoolId)
        {
            string sql = @$"SELECT IAS.Id ,EL.Id AS EvaluateListId ,IAS.StageId ,IAS.CourseId ,
	                               EL.EvaluateStandardId ,EL.RegulationRatio ,EL.NeglectSmallNum ,EL.Num
                            FROM eq_InstrumentAttendStatic AS IAS
                            INNER JOIN eq_InstrumentEvaluateList AS EL ON IAS.EvaluateListId = EL.Id AND EL.BaseIsDelete = 0 AND EL.Statuz = 1
                            WHERE IAS.SchoolId = {schoolId}";
            var list = await this.BaseRepository().FindList<InstrumentAttendStaticEntity>(sql);
            return list.ToList();
        }

        /// <summary>
        /// 根据选中的仪器达标加权值设置表Id获取需要
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task<List<InstrumentAttendStaticEntity>> GetAttendStaticListByWeightedId(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");

            string sql = @$"SELECT IAS.Id ,IAS.SchoolId , EL.Id AS EvaluateListId ,IAS.StageId ,IAS.CourseId ,
	                                EL.EvaluateStandardId ,EL.RegulationRatio ,EL.NeglectSmallNum ,EL.Num,
                                    IAS.InstrumentEvaluateProjectId ,IAS.InstrumentCode ,IAS.Name ,IAS.Model ,
                                    IAS.StockNum ,IAS.UnitName ,IAS.AllocateType
                            FROM eq_InstrumentEvaluateWeighted AS EW
                            INNER JOIN eq_InstrumentEvaluateStandard AS ES ON EW.EvaluateStandardId = ES.Id AND ES.Statuz = 1 AND ES.BaseIsDelete = 0
                            INNER JOIN eq_InstrumentEvaluateList AS EL ON ES.Id = EL.EvaluateStandardId AND EL.Statuz = 1 AND EL.BaseIsDelete = 0
                            INNER JOIN eq_InstrumentAttendStatic AS IAS ON EL.Id = IAS.EvaluateListId AND IAS.BaseIsDelete = 0
                            WHERE EW.Id IN ({ids}) AND EL.IsEvaluate = 1";
            var list = await this.BaseRepository().FindList<InstrumentAttendStaticEntity>(sql);
            return list.ToList();
        }


        /// <summary>
        /// 先查询所有
        /// </summary>
        /// <returns></returns>
        public async Task<List<InstrumentAttendStaticEntity>> GetAttendStaticList(int isEvaluate=0)
        {

            string sql = @$"SELECT DISTINCT EL.Id AS EvaluateListId ,EV.EvaluateProjectId AS InstrumentEvaluateProjectId ,ES.SchoolStage AS StageId ,ES.DictionaryId1005 AS CourseId ,
	                                       EL.Code AS InstrumentCode ,EL.InstrumentName AS Name,EL.ParamDemand AS Model ,EL.UnitName ,
	                                       EL.AllocateType ,ES.Id AS EvaluateStandardId ,EL.RegulationRatio ,EL.NeglectSmallNum ,EL.Num ,ER.ActualCode
                                    FROM eq_InstrumentEvaluateList AS EL
                                    INNER JOIN eq_InstrumentEvaluateRelation AS ER ON EL.Id = ER.EvaluateListId AND EL.BaseIsDelete = 0 AND EL.Statuz = 1
                                    INNER JOIN eq_InstrumentEvaluateStandard AS ES ON EL.EvaluateStandardId = ES.Id AND ES.BaseIsDelete = 0 AND ES.Statuz = 1
                                    INNER JOIN eq_InstrumentEvaluateProjectVersion AS EV ON ES.Id = EV.EvaluateStandardId AND EV.BaseIsDelete = 0
                                    ";
            if(isEvaluate == 1)
            {
                sql += " WHERE EL.IsEvaluate = 1 ";
            }
            var list = await this.BaseRepository().FindList<InstrumentAttendStaticEntity>(sql);
            return list.ToList();
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="InstrumentEvaluateStandardId"></param>
        /// <returns></returns>
        public async Task<List<InstrumentAttendStaticEntity>> GetAttendStaticListById(long InstrumentEvaluateStandardId)
        {

            string sql = @$"SELECT DISTINCT EL.Id AS EvaluateListId ,EV.EvaluateProjectId AS InstrumentEvaluateProjectId ,ES.SchoolStage AS StageId ,ES.DictionaryId1005 AS CourseId ,
	                                       EL.Code AS InstrumentCode ,EL.InstrumentName AS Name,EL.ParamDemand AS Model ,EL.UnitName ,
	                                       EL.AllocateType ,ES.Id AS EvaluateStandardId ,EL.RegulationRatio ,EL.NeglectSmallNum ,EL.Num ,ER.ActualCode
                                    FROM eq_InstrumentEvaluateList AS EL
                                    INNER JOIN eq_InstrumentEvaluateRelation AS ER ON EL.Id = ER.EvaluateListId AND EL.BaseIsDelete = 0 AND EL.Statuz = 1
                                    INNER JOIN eq_InstrumentEvaluateStandard AS ES ON EL.EvaluateStandardId = ES.Id AND ES.BaseIsDelete = 0 AND ES.Statuz = 1
                                    INNER JOIN eq_InstrumentEvaluateProjectVersion AS EV ON ES.Id = EV.EvaluateStandardId AND EV.BaseIsDelete = 0
                                    WHERE EL.IsEvaluate = 1 AND ES.Id = {InstrumentEvaluateStandardId}";
            var list = await this.BaseRepository().FindList<InstrumentAttendStaticEntity>(sql);
            return list.ToList();
        }

        /// <summary>
        /// 根据编码、学段、学科获取评估数据信息(根据编码、学段、课程查询)
        /// </summary>
        /// <param name="code">编码</param>
        /// <param name="stageId">学段Id</param>
        /// <param name="courseId">学科Id</param>
        /// <returns></returns>
        public async Task<List<InstrumentAttendStaticEntity>> GetAttendStaticList(string code, int stageId, int courseId)
        {
            string sql = @$"SELECT DISTINCT EL.Id AS EvaluateListId ,EV.EvaluateProjectId AS InstrumentEvaluateProjectId ,ES.SchoolStage AS StageId ,ES.DictionaryId1005 AS CourseId ,
                                        EL.Code AS InstrumentCode ,EL.InstrumentName AS Name,EL.ParamDemand AS Model ,EL.UnitName ,
                                        EL.AllocateType ,ES.Id AS EvaluateStandardId ,EL.RegulationRatio ,EL.NeglectSmallNum ,EL.Num
                                    FROM eq_InstrumentEvaluateList AS EL
                                    INNER JOIN eq_InstrumentEvaluateRelation AS ER ON EL.Id = ER.EvaluateListId AND EL.BaseIsDelete = 0 AND EL.Statuz = 1
                                    INNER JOIN eq_InstrumentEvaluateStandard AS ES ON EL.EvaluateStandardId = ES.Id AND ES.BaseIsDelete = 0 AND ES.Statuz = 1
                                    INNER JOIN eq_InstrumentEvaluateProjectVersion AS EV ON ES.Id = EV.EvaluateStandardId AND EV.BaseIsDelete = 0
                                    WHERE ER.ActualCode = '{code}' AND ES.SchoolStage = {stageId} AND ES.DictionaryId1005 = {courseId}";
            var list = await this.BaseRepository().FindList<InstrumentAttendStaticEntity>(sql);
            return list.ToList();
        }

        /// <summary>
        /// 获取所有学校已入库仪器清单
        /// </summary>
        /// <param name="evaluateProjectId">仪器达标达标参数设置主表Id</param>
        /// <returns></returns>
        public async Task<List<SchoolInstrumentModel>> GetEvaluateAttendList(long evaluateProjectId)
        {
            string sql = @$"SELECT DISTINCT SI.SchoolId,SI.Code,SI.StageId,SI.CourseId
                            FROM eq_InstrumentEvaluateList AS EL
                            INNER JOIN eq_InstrumentEvaluateRelation AS ER ON EL.Id = ER.EvaluateListId
                            INNER JOIN eq_InstrumentEvaluateStandard AS ES ON EL.EvaluateStandardId = ES.Id AND ES.BaseIsDelete = 0 AND ES.Statuz = 1
                            INNER JOIN eq_InstrumentEvaluateProjectVersion AS EV ON ES.Id = EV.EvaluateStandardId AND EV.BaseIsDelete = 0
                            INNER JOIN eq_SchoolInstrument AS SI ON ER.ActualCode = SI.Code AND SI.BaseIsDelete = 0 AND SI.Statuz = {InstrumentInputStatuzEnum.AlreadyInputStorage.ParseToInt()}
                            WHERE EL.Statuz = 1 AND EL.IsEvaluate = 1 AND EV.EvaluateProjectId = {evaluateProjectId}";
            var list = await this.BaseRepository().FindList<SchoolInstrumentModel>(sql);
            return list.ToList();
        }

        /// <summary>
        /// 根据"仪器达标标准库加权值设置表"id集合查询学校入库仪器清单数据
        /// </summary>
        /// <param name="ids">仪器达标标准库加权值设置表id集合</param>
        /// <returns></returns>
        public async Task<List<SchoolInstrumentModel>> GetWeightedAttendList(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");

            string sql = @$"
                            SELECT DISTINCT SI.SchoolId,SI.Code,SI.StageId,SI.CourseId
                            FROM eq_InstrumentEvaluateWeighted AS EW
                            INNER JOIN eq_InstrumentEvaluateStandard AS ES ON EW.EvaluateStandardId = ES.Id AND ES.Statuz = 1 AND ES.BaseIsDelete = 0
                            INNER JOIN eq_InstrumentEvaluateList AS EL ON ES.Id = EL.EvaluateStandardId AND EL.Statuz = 1 AND EL.BaseIsDelete = 0
                            INNER JOIN eq_InstrumentEvaluateRelation AS ER ON EL.Id = ER.EvaluateListId AND ER.BaseIsDelete = 0
                            INNER JOIN eq_SchoolInstrument AS SI ON ER.ActualCode = SI.Code AND SI.BaseIsDelete = 0 AND SI.Statuz = {InstrumentInputStatuzEnum.AlreadyInputStorage.ParseToInt()}
                            WHERE EW.Id IN ({ids})";
            var list = await this.BaseRepository().FindList<SchoolInstrumentModel>(sql);
            return list.ToList();
        }

        ///// <summary>
        ///// 获取学校库存数量
        ///// </summary>
        ///// <param name="schoolId">学校Id</param>
        ///// <param name="evaluateListId">仪器配置标准明细表Id</param>
        ///// <param name="stageId">学段Id</param>
        ///// <param name="courseId">学科Id</param>
        ///// <returns></returns>
        public async Task<SchoolStock> GetSchoolStockNum(long schoolId, long evaluateListId, int stageId, int courseId)
        {
            string sql = @$"SELECT ISNULL(SUM(StockNum),0) AS StockNum,ISNULL(SUM(LendNum),0) AS LendNum
                            FROM eq_SchoolInstrument
                            WHERE BaseIsDelete = 0 AND Statuz = {InstrumentInputStatuzEnum.AlreadyInputStorage.ParseToInt()} AND SchoolId = {schoolId}
                            AND StageId Like '%{stageId}%' AND CourseId = {courseId}
                            AND Code IN(SELECT ActualCode FROM eq_InstrumentEvaluateRelation WHERE EvaluateListId = {evaluateListId})";
            var list = await this.BaseRepository().FindList<SchoolStock>(sql);
            return list.ToList().FirstOrDefault();
        }

        /// <summary>
        /// 根据学校Id获取学校已入库仪器数据
        /// </summary>
        /// <param name="schoolId">学校Id</param>
        /// <returns></returns>
        public async Task<List<SchoolAttendStatic>> GetSchoolAttendStaticList(long schoolId)
        {
            string sql = @$"SELECT Code,StageId,CourseId,StockNum,LendNum
                            FROM eq_SchoolInstrument
                            WHERE BaseIsDelete = 0 AND SchoolId = {schoolId} AND Statuz = {InstrumentInputStatuzEnum.AlreadyInputStorage.ParseToInt()}";
            var list = await this.BaseRepository().FindList<SchoolAttendStatic>(sql);
            return list.ToList();
        }

        /// <summary>
        /// 获取所有学校已入库仪器数据
        /// </summary>
        /// <returns></returns>
        public async Task<List<SchoolAllAttendStatic>> GetSchoolAllList()
        {
            string sql = @$"SELECT SchoolId,Code,StageId,CourseId,StockNum,LendNum
                            FROM eq_SchoolInstrument
                            WHERE BaseIsDelete = 0 AND Statuz = {InstrumentInputStatuzEnum.AlreadyInputStorage.ParseToInt()}";
            var list = await this.BaseRepository().FindList<SchoolAllAttendStatic>(sql);
            return list.ToList();
        }

        /// <summary>
        /// 获取所有学校已入库仪器清单，并根据分类编码、学科、学科分组
        /// 用于遍历计算所有仪器达标数据
        /// </summary>
        /// <returns></returns>
        public async Task<List<SchoolInstrumentModel>> GetSchoolInstrumentAllList()
        {
            string sql = @$"SELECT SchoolId,Code,StageId,CourseId FROM eq_SchoolInstrument
                            WHERE BaseIsDelete = 0 AND Statuz = {InstrumentInputStatuzEnum.AlreadyInputStorage.ParseToInt()}
                            GROUP BY SchoolId,Code,StageId,CourseId";
            var list = await this.BaseRepository().FindList<SchoolInstrumentModel>(sql);
            return list.ToList();
        }

        /// <summary>
        /// 根据仪器配置标准明细表Id获取
        /// </summary>
        /// <param name="evaluateListId">标准明细表Id</param>
        /// <param name="courseId">学科Id</param>
        /// <param name="stageId">学段Id</param>
        /// <returns></returns>
        public async Task<List<SchoolInstrumentModel>> GetSchoolInstrumentList(long evaluateListId,int stageId,int courseId)
        {
            string sql = @$"SELECT SchoolId,Code,StageId,CourseId FROM eq_SchoolInstrument
                            WHERE BaseIsDelete = 0 AND Statuz = {InstrumentInputStatuzEnum.AlreadyInputStorage.ParseToInt()}
                            AND Code IN (SELECT ActualCode FROM eq_InstrumentEvaluateRelation WHERE EvaluateListId = {evaluateListId})
                            AND StageId LIKE '%{stageId}%' AND CourseId = {courseId}
                            GROUP BY SchoolId,Code,StageId,CourseId";
            var list = await this.BaseRepository().FindList<SchoolInstrumentModel>(sql);
            return list.ToList();
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(InstrumentAttendStaticEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update eq_InstrumentAttendStatic set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
                strSql = $"update eq_InstrumentAttendStatic set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion


        #region 私有方法
        /// <summary>
        ///
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        private Expression<Func<InstrumentAttendStaticEntity, bool>> ListFilter(InstrumentAttendStaticListParam param)
        {
            var expression = LinqExtensions.True<InstrumentAttendStaticEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (param.SchoolId > 0)
                {
                    expression = expression.And(f => f.SchoolId == param.SchoolId);
                }
                if (param.InstrumentEvaluateProjectId > 0)
                {
                    expression = expression.And(f => f.InstrumentEvaluateProjectId == param.InstrumentEvaluateProjectId);
                }
                if(param.SchoolStageId > 0)
                {
                    expression = expression.And(f => f.StageId == param.SchoolStageId);
                }
                if(param.CourseId > 0)
                {
                    expression = expression.And(f=>f.CourseId == param.CourseId);
                }
                if (!string.IsNullOrEmpty(param.InstrumentCode))
                {
                    expression = expression.And(f=>f.InstrumentCode == param.InstrumentCode);
                }
                if(param.EvaluateListId > 0)
                {
                    expression = expression.And(f => f.EvaluateListId == param.EvaluateListId);
                }
            }
            return expression;
        }

        #endregion
    }
}
