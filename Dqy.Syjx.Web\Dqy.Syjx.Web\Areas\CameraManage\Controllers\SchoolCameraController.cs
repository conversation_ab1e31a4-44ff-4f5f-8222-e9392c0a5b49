﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using System.Web;
using Microsoft.AspNetCore.Mvc;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Entity;
using Dqy.Syjx.Model;
using Dqy.Syjx.Web.Controllers;
using Dqy.Syjx.Entity.CameraManage;
using Dqy.Syjx.Model.Input.CameraManage;
using Dqy.Syjx.Business.CameraManage;
using Dqy.Syjx.Model.Param.CameraManage;
using Dqy.Syjx.Model.Param;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Entity.BusinessManage;
using Dqy.Syjx.Model.Param.BusinessManage;
using Dqy.Syjx.Business.SystemManage;
using Dqy.Syjx.Entity.SystemManage;
using Dqy.Syjx.Enum.SystemManage;
using Dqy.Syjx.Model.Param.SystemManage;
using Dqy.Syjx.Web.Code;
using NPOI.SS.Formula.Functions;
using Dqy.Syjx.Model.Result;
using Dqy.Syjx.Web.CommonLib;
using System.Text.RegularExpressions;
using System.Data.Common;
using Senparc.Weixin.WxOpen.AdvancedAPIs.WxApp.WxAppJson;
using Dqy.Syjx.Business.EvaluateManage;
using Dqy.Syjx.Model.Param.EvaluateManage;

namespace Dqy.Syjx.Web.Areas.CameraManage.Controllers
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2022-01-10 10:02
    /// 描 述：摄像头管理控制器类
    /// </summary>
    [Area("CameraManage")]
    public class SchoolCameraController :  BaseController
    {
        private SchoolCameraBLL schoolCameraBLL = new SchoolCameraBLL();
        private ConfigSetBLL configSetBLL = new ConfigSetBLL();
        private CameraPointBLL cameraPointBLL =new CameraPointBLL();
        private DahuaDeviceBLL deviceBLL = new DahuaDeviceBLL();
        private DahuaChannelBLL channelBLL = new DahuaChannelBLL();

        #region 视图功能
        [AuthorizeFilter("camera:schoolcamera:view")]
        public ActionResult CameraList()
        {
            return View();
        }

        public ActionResult Import()
        {
            return View();
        }

        /// <summary>
        /// 视频预览页面
        /// </summary>
        /// <returns></returns>
        public ActionResult DhVideoPreview()
        {
            return View();
        }

        public ActionResult ShoolCameraForm()
        {
            return View();
        }

        #endregion

        #region 获取数据
        [HttpGet]
        [AuthorizeFilter("camera:schoolcamera:search")]
        public async Task<ActionResult> GetListJson(SchoolCameraListParam param)
        {
            TData<List<SchoolCameraEntity>> obj = await schoolCameraBLL.GetList(param);
            return Json(obj);
        }

        [HttpGet]
        [AuthorizeFilter("camera:schoolcamera:search")]
        public async Task<ActionResult> GetPageListJson(SchoolCameraListParam param, Pagination pagination)
        {
            TData<List<SchoolCameraEntity>> obj = await schoolCameraBLL.GetPageList(param, pagination);
            return Json(obj);
        }

        /// <summary>
        /// 导出摄像机管理信息
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> ExportSchoolcamera(SchoolCameraListParam param, Pagination pagination)
        {
            TData<string> obj = new TData<string>();
            pagination.PageSize = int.MaxValue;
            TData<List<SchoolCameraEntity>> resultObj = await schoolCameraBLL.GetPageList(param, pagination);
            if (resultObj.Tag == 1)
            {
                resultObj.Data.ForEach(t =>
                {
                    t.StrStatuz = t.Statuz == 1 ? "启用" : "禁用";
                });
            
                string file = new ExcelHelper<SchoolCameraEntity>().ExportToExcel("摄像机管理.xls",
                                                                                         "摄像机管理",
                                                                                         resultObj.Data,
                                                                                         new string[] { "CountyName", "SchoolName", "SrcName", "SrcIndex", "Address", "Remark", "StrStatuz" });
                obj.Data = file;
                obj.Tag = 1;
            }
            return Json(obj);
        }


        [HttpGet]
        public async Task<ActionResult> GetFormJson(long id)
        {
            TData<SchoolCameraEntity> obj = await schoolCameraBLL.GetEntity(id);
            return Json(obj);
        }

        [HttpPost]
        public async Task<IActionResult> ExportSchoolListJson(long schoolId)
        {
            TData<string> obj = new TData<string>();
            var funroomlist = await schoolCameraBLL.GetSchoolFunRoomList();
            if (funroomlist.Count > 0)
            {
                var funList = funroomlist.Where(f => f.RoomAttribute != 1009003).ToList();
                if (schoolId != -1)
                {
                    funList = funList.Where(f => f.UnitId == schoolId).ToList();
                }
                var resultData = (from item in funList
                                  select new FunRoomEntity
                                  {
                                      CountyName = item.CountyName,
                                      SchoolName = item.SchoolName, //学校名称
                                      Name = item.Name,             //实验室、专用室名称
                                      RoomName = item.RoomName,     //实验室地点
                                      SrcName = item.SrcName,       //摄像机名称
                                      CameraIndexCode = item.CameraIndexCode, //摄像机编号
                                      Remark = item.Remark          //备注
                                  }).ToList();
                string file = new ExcelHelper<FunRoomEntity>().ExportToExcel("导入摄像头信息模板.xls",
                                                                                         "摄像头信息列表",
                                                                                         resultData,
                                                                                         new string[] { "CountyName", "SchoolName", "Name", "RoomName", "SrcName", "CameraIndexCode", "Remark" }, "说明：1.如实施的实验室不在此表中，请联系学校管理员在实验教学管理平台添加实验室信息。2.摄像机名称对应海康综合安防平台的监控点名称、摄像机编号对应监控点编号。");
                obj.Data = file;
                obj.Tag = 1;
            }
           
            return Json(obj);
        }

        [HttpGet]
        public async Task<ActionResult> GetVerifyJson(long funroomid)
        {
            TData<int> obj = await schoolCameraBLL.GetVerifyForm(funroomid);
            return Json(obj);
        }
        #endregion

        #region 提交数据
        /// <summary>
        /// 启用禁用
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("camera:schoolcamera:add,camera:schoolcamera:edit")]
        public async Task<ActionResult> SetStatuzJson(long id, int statuz)
        {
            TData<string> obj = await schoolCameraBLL.SetStatuzForm(id, statuz);
            return Json(obj);
        }

        //[HttpPost]
        //[AuthorizeFilter("camera:schoolcamera:delete")]
        //public async Task<ActionResult> DeleteFormJson(string ids)
        //{
        //    TData obj = await schoolCameraBLL.DeleteForm(ids);
        //    return Json(obj);
        //}

        [HttpPost]
        public async Task<IActionResult> ImportJson(ImportParam param)
        {
            List<SchoolCameraInputModel> list = new ExcelHelper<SchoolCameraInputModel>().ImportFromExcel(param.FilePath,2);
            TData obj = await schoolCameraBLL.ImportJson(param, list);
            return Json(obj);
        }
        #endregion

        #region 摄像头关联视图
        [AuthorizeFilter("camera:schoolcamera:view")]
        public ActionResult CameraRelationList()
        {
            return View();
        }

        public ActionResult Relation()
        {
            return View();
        }
        #endregion

        #region 摄像头关联-获取数据
        [HttpGet]
        [AuthorizeFilter("camera:schoolcamera:search")]
        public async Task<ActionResult> GetRelationPageListJson(SchoolCameraListParam param, Pagination pagination)
        {
            param.Statuz = StatusEnum.Yes.ParseToInt();
            TData<List<SchoolCameraEntity>> obj = await schoolCameraBLL.GetPageList(param, pagination);
            return Json(obj);
        }

        [HttpGet]
        public async Task<ActionResult> GetFunRoomListJson(long id)
        {
            TData<List<FunRoomEntity>> obj = await schoolCameraBLL.GetFunRoomList(id);
            return Json(obj);
        }
        #endregion

        #region 摄像头关联-提交数据
        /// <summary>
        /// 设置关联
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("camera:schoolcamera:edit")]
        public async Task<ActionResult> SetRelationForm(SchoolCameraInputModel mdel)
        {
            TData<string> obj = await schoolCameraBLL.SetRelationForm(mdel);
            return Json(obj);
        }
        /// <summary>
        /// 取消关联
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("camera:schoolcamera:edit")]
        public async Task<ActionResult> CancelRelationJson(string ids)
        {
            TData obj = await schoolCameraBLL.CancelRelationForm(ids);
            return Json(obj);
        }
        #endregion

        #region 根据平台配置的摄像机品牌获取视频播放资源
        /// <summary>
        /// 海康 获取监控点，网页播放需使用海康官方提供的视频插件
        //  大华 获取视频播放通道编号调用接口获取播放流，网页播放无需任何插件
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<TData<VideoPlayResources>> GetVideoPlayResources(string cameraName)
        {
            TData<VideoPlayResources> obj = new TData<VideoPlayResources>();
            TData<List<ConfigSetEntity>> configObj = await configSetBLL.GetDefaultList(new ConfigSetListParam { TypeCode = "5003_SXJPP", UnitId = 100000000000000001 }); //获取平台配置的摄像机品牌
            if (configObj.Tag == 1 && configObj.Data != null && configObj.Data.Count > 0)
            {
                if (configObj.Data.LastOrDefault().ConfigValue.Equals("1")) //海康-获取监控点
                {
                    TData<CameraPointEntity> pointobj = await cameraPointBLL.GetCameraPoint(cameraName);
                    if (pointobj.Data != null)
                    {
                        obj.Tag = 1;
                        obj.Data = new VideoPlayResources() { Brand = 1, PlayResources = pointobj.Data.CameraIndexCode };
                    }
                    else
                    {
                        obj.Tag = 0;
                        obj.Data = null;
                    }
                }
                else if(configObj.Data.LastOrDefault().ConfigValue.Equals("2")) //大华-获取视频播放通道
                {
                    var devicelist = await deviceBLL.GetList(new DahuaDeviceListParam() { deviceName = cameraName });
                    if (devicelist.Data.Count > 0)
                    {
                        var channelobj = await channelBLL.GetList(new DahuaChannelListParam() { deviceCode = devicelist.Data.LastOrDefault().deviceCode });
                        if (channelobj.Data != null)
                        {
                            string channelId = channelobj.Data.LastOrDefault().channelCode; //通道编号
                            //根据通道编号调用大华平台接口获取视频播放流
                            var videoStreamObj =  await GetVideoStream(channelId);
                            if (videoStreamObj.Tag == 1 && !string.IsNullOrEmpty(videoStreamObj.Data))
                            {
                                obj.Tag = 1;
                                obj.Data = new VideoPlayResources() { Brand = 2, PlayResources = videoStreamObj.Data };
                            }
                        }
                    }
                }
            }
            return obj;
        }
        #endregion

        #region 获取播放流(hls,flv)
        private async Task<TData<string>> GetVideoStream(string channelCode)
        {
            TData<string> obj = new TData<string>();
            var authorizationResult = await DahuaApi.GetAuthorization(); //获取请求头authorization
            if (authorizationResult.Tag == 1 && authorizationResult.Data != null)
            {
                string data = "{\"data\": {\"channelId\": \"" + channelCode + "\",\"streamType\": \"1\",\"type\": \"flv\"}}";
                var videoObj = await SendHelper.SendPostAsync(GlobalContext.SystemConfig.DahuaApi + "/evo-apigw/admin/API/video/stream/realtime", data, authorizationResult.Data.Authorization);
                if (videoObj.Tag == 1 && !string.IsNullOrEmpty(videoObj.Data))
                {
                    VideoStream videoStream = Util.Tools.JsonExtention.ToObject<VideoStream>(videoObj.Data);
                    string videoUrl = videoStream.data.url + "?token=" + authorizationResult.Data.Token;
                    if (HttpHelper.IsUrl(videoUrl) && GlobalContext.SystemConfig.DahuaVideoIP != "")
                    {
                        videoUrl = Regex.Replace(videoUrl, @"\d+\.\d+\.\d+\.\d+", GlobalContext.SystemConfig.DahuaVideoIP, RegexOptions.IgnoreCase); //替换播放URL中的IP地址
                    }
                    obj.Data = videoUrl;
                    obj.Tag = 1;
                    LogHelper.Info($"【调用大华平台接口获取视频播放流】=> 播放流地址：{videoUrl}", null);
                }
                else
                {
                    obj.Tag = 0;
                    LogHelper.Info("【调用大华平台接口获取视频播放流】=> 请求头为空", null);
                }
            }
            return obj;
        }
        #endregion
    }
}
