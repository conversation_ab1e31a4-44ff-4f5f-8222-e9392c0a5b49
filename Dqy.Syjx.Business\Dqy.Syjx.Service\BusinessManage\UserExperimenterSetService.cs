﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.BusinessManage;
using Dqy.Syjx.Model.Param.BusinessManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Enum.OrganizationManage;
using Dqy.Syjx.Entity.SystemManage;
using System.Collections;
using Dqy.Syjx.Util.Tools;
using Dqy.Syjx.Entity.OrganizationManage;
using Dqy.Syjx.Model.Param.OrganizationManage;

namespace Dqy.Syjx.Service.BusinessManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-12-13 15:30
    /// 描 述：实验员设置服务类
    /// </summary>
    public class UserExperimenterSetService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<UserExperimenterSetEntity>> GetList(UserExperimenterSetListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<UserExperimenterSetEntity>> GetPageList(UserExperimenterSetListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ListFilter(param, strSql);
            IEnumerable<UserExperimenterSetEntity> list = null;
            if (pagination!=null)
            {
                list = await this.BaseRepository().FindList<UserExperimenterSetEntity>(strSql.ToString(), filter.ToArray(), pagination);
            }
            else
            {
                list = await this.BaseRepository().FindList<UserExperimenterSetEntity>(strSql.ToString(), filter.ToArray());
            }
            if (pagination != null && list != null && list.Count() > 0)
            {
                //SubjectIds,SubjectNames
                IEnumerable<StaticDictionaryEntity> courseList = await GetCousreListByUser(list.Select(m => m.SysUserId));
                if (courseList != null && courseList.Count() > 0)
                {
                    list.ForEach(m =>
                    {
                        m.SubjectIds = string.Join(",", courseList.Where(n => n.Id == m.SysUserId).Select(j => j.DictionaryId));
                        m.SubjectNames = string.Join(",", courseList.Where(n => n.Id == m.SysUserId).Select(j => j.DicName));
                    });
                }
            }
            return list.ToList();
        }

        public async Task<List<UserExperimenterSetEntity>> GetZbReportAllList(UserExperimenterSetListParam param)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ListZbReportAllFilter(param, strSql);
            var list = await this.BaseRepository().FindList<UserExperimenterSetEntity>(strSql.ToString(), filter.ToArray());
            return list.ToList();
        }

        public async Task<UserExperimenterSetEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<UserExperimenterSetEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(UserExperimenterSetEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(UserExperimenterSetEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteTransForm(string ids, Repository db)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
               strSql = $" update bn_UserExperimenterSet set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
               strSql = $"update bn_UserExperimenterSet set BaseIsDelete = 1 where id = {ids}";
            }
            await db.ExecuteBySql(strSql);
        }
        /// <summary>
        ///
        /// </summary>
        /// <param name="ids">用户Id</param>
        /// <param name="unitid">单位Id</param>
        /// <param name="db"></param>
        /// <returns></returns>
        public async Task DeleteTransByUserIdForm(string ids,long unitid, Repository db)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update bn_UserExperimenterSet set BaseIsDelete = 1 where SysUserId in ({ids}) AND IsCurrentUnit =  {IsEnum.Yes.ParseToInt()}  AND BaseIsDelete =0 AND UnitId = {unitid} ";
            }
            else
            {
                strSql = $"update bn_UserExperimenterSet set BaseIsDelete = 1 where IsCurrentUnit = {IsEnum.Yes.ParseToInt()} AND BaseIsDelete =0 AND SysUserId = {ids} AND UnitId = {unitid} ";
            }
            await db.ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<UserExperimenterSetEntity, bool>> ListFilter(UserExperimenterSetListParam param)
        {
            var expression = LinqExtensions.True<UserExperimenterSetEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (param.IsCurrentUnit > -1)
                {
                    expression = expression.And(t => t.IsCurrentUnit == param.IsCurrentUnit);
                }
                if (param.UnitId > 0)
                {
                    expression = expression.And(t => t.UnitId == param.UnitId);
                }
                if (param.SysUserId > 0)
                {
                    expression = expression.And(t => t.SysUserId == param.SysUserId);
                }

            }
            return expression;
        }

        private List<DbParameter> ListFilter(UserExperimenterSetListParam param, StringBuilder strSql)
        {
            strSql.Append($@" SELECT * From (
                SELECT
                ur1.Id,
                ur1.UnitId ,
                ur1.SysUserId ,
                ur1.IsExperimenter ,
                ur1.ExperimenterNature ,
                ur1.Statuz ,
	            ue2.JobTitle ,
	            ue2.TotalSchoolHour , 
	            su3.RealName , 
	            su3.UserName , 
	            su3.Gender ,	
	            su3.Birthday ,
                ue2.EducationLevel ,
	            ur6.UnitId AS CountyId ,u21.Name AS CountyName,ur22.UnitId AS CityId,u7.Name AS UnitName ,
                se21.SchoolProp
            FROM  bn_UserExperimenterSet	 AS ur1
            INNER JOIN  up_UserExtension AS ue2 ON ue2.IsCurrentUnit = 1 AND ur1.SysUserId = ue2.SysUserId AND ue2.BaseIsDelete = 0 AND ur1.BaseIsDelete = 0  AND ur1.IsCurrentUnit =  1
            INNER JOIN  SysUser AS su3 ON ue2.SysUserId = su3.Id AND su3.BaseIsDelete = 0
            INNER JOIN  up_UnitRelation AS ur6 ON ur1.UnitId = ur6.ExtensionObjId AND ur6.ExtensionType = 3  AND ur6.BaseIsDelete = 0
            INNER JOIN  up_Unit AS u7 on ur1.UnitId = u7.Id  AND u7.BaseIsDelete = 0
            LEFT JOIN up_SchoolExtension as se21 on u7.Id = se21.UnitId AND se21.BaseIsDelete = 0
            INNER JOIN  up_Unit AS u21 ON ur6.UnitId = u21.Id AND u21.Statuz = 1 AND u21.BaseIsDelete = 0
            INNER JOIN  up_UnitRelation AS ur22 ON u21.Id = ur22.ExtensionObjId AND ur22.ExtensionType = 3 AND ur22.BaseIsDelete = 0
            LEFT JOIN  sys_static_dictionary AS dic4 ON ue2.JobTitle = dic4.DictionaryId  AND dic4.BaseIsDelete = 0
            LEFT JOIN bn_UserSchoolStageSubject AS subject ON subject.IsCurrentUnit = 1 AND subject.BaseIsDelete = 0 AND subject.UserId = ur1.SysUserId ");
            if (param.SubjectId>0)
            {
                if (param.SubjectId > 0)
                {
                    strSql.Append($" where subject.SubjectIdz = {param.SubjectId} ");
                }
            }
            else
            {
                if (param.SubjectList != null && param.SubjectList.Count > 0)
                {
                    param.SubjectList = param.SubjectList.Distinct().ToList();
                    strSql.Append($" where ({string.Join(" OR ", param.SubjectList.Select(m => string.Format(" subject.SubjectIdz = {0} ", m)))}) ");
                }
            }
            strSql.Append($@" GROUP BY  ur1.Id,ur1.UnitId,ur1.IsExperimenter ,ur1.SysUserId,ur1.Statuz,ur1.ExperimenterNature,u7.Name,ue2.JobTitle,ue2.TotalSchoolHour,su3.RealName,su3.UserName,su3.Birthday,ur6.UnitId, su3.Gender,ue2.EducationLevel,u21.Name,ur22.UnitId,se21.SchoolProp) A WHERE 1 = 1 ");

            var parameter = new List<DbParameter>();
            if (param != null)
            {
                //strSql.Append(" AND BaseIsDelete = @BaseIsDelete ");
                //parameter.Add(DbParameterExtension.CreateDbParameter("@BaseIsDelete", param.BaseIsDelete));
                if (param.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    strSql.Append(" AND UnitId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                else if (param.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    strSql.Append(" AND CountyId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                else if (param.UnitType == UnitTypeEnum.City.ParseToInt())
                {
                    strSql.Append(" AND CityId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                if(param.SchoolId >0)
                {
                    strSql.Append(" AND UnitId = @SchoolId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
                }
                if (param.CountyId > 0)
                {
                    strSql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
                if (param.Gender >= 0)
                {
                    strSql.Append(" AND Gender = @Gender ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Gender", param.Gender));
                }
                if (param.JobTitle > 0)
                {
                    strSql.Append(" AND JobTitle = @JobTitle ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@JobTitle", param.JobTitle));
                }
                if (param.EducationLevel > 0)
                {
                    strSql.Append(" AND EducationLevel = @EducationLevel ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@EducationLevel", param.EducationLevel));
                }
                if (param.ExperimenterNature == ExperimenterNatureEnum.FullTime.ParseToInt() || param.ExperimenterNature == ExperimenterNatureEnum.PartTime.ParseToInt())
                {
                    strSql.Append(" AND ExperimenterNature = @ExperimenterNature ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ExperimenterNature", param.ExperimenterNature));
                }
                if (!string.IsNullOrEmpty(param.RealName))
                {
                    strSql.Append(" AND RealName like @RealName ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@RealName", $"%{ param.RealName.Trim()}%"));
                }
                //if (param.IsCurrentUnit >= 0)
                //{
                //    strSql.Append(" AND IsCurrentUnit = @IsCurrentUnit ");
                //    parameter.Add(DbParameterExtension.CreateDbParameter("@IsCurrentUnit", param.IsCurrentUnit));
                //}
                //if (param.Statuz> 0)
                //{
                //    strSql.Append(" AND Statuz = @Statuz ");
                //    parameter.Add(DbParameterExtension.CreateDbParameter("@Statuz", param.Statuz));
                //}
                if (param.IsExperimenter >= 0)
                {
                    strSql.Append(" AND IsExperimenter = @IsExperimenter ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@IsExperimenter", param.IsExperimenter));
                }
                if (param.SchoolPropList != null && param.SchoolPropList.Count > 0)
                {
                    param.SchoolPropList = param.SchoolPropList.Distinct().ToList();
                    strSql.Append($" AND ({string.Join(" OR ", param.SchoolPropList.Select(m => string.Format(" SchoolProp = {0} ", m)))}) ");
                }
            }
            return parameter;
        }


        private List<DbParameter> ListZbReportAllFilter(UserExperimenterSetListParam param, StringBuilder strSql)
        {
            strSql.Append($@" SELECT * From (
                SELECT
                ur1.Id,
                ur1.UnitId ,
                ur1.SysUserId ,
                ur1.IsExperimenter ,
                ur1.ExperimenterNature ,
                ur1.Statuz ,
	            ue2.JobTitle ,
	            ue2.TotalSchoolHour ,
	            su3.RealName ,
	            su3.UserName ,
	            su3.Gender ,
	            su3.Birthday ,
                ue2.EducationLevel ,
	            ur6.UnitId AS CountyId ,u21.Name AS CountyName,ur22.UnitId AS CityId,u7.Name AS UnitName ,
                se21.SchoolProp
                ,subject.SubjectIdz
            FROM  bn_UserExperimenterSet	 AS ur1
            INNER JOIN  up_UserExtension AS ue2 ON ue2.IsCurrentUnit = 1 AND ur1.SysUserId = ue2.SysUserId AND ue2.BaseIsDelete = 0 AND ur1.BaseIsDelete = 0  AND ur1.IsCurrentUnit =  1
            INNER JOIN  SysUser AS su3 ON ue2.SysUserId = su3.Id AND su3.BaseIsDelete = 0
            INNER JOIN  up_UnitRelation AS ur6 ON ur1.UnitId = ur6.ExtensionObjId AND ur6.ExtensionType = 3  AND ur6.BaseIsDelete = 0
            INNER JOIN  up_Unit AS u7 on ur1.UnitId = u7.Id  AND u7.BaseIsDelete = 0
            LEFT JOIN up_SchoolExtension as se21 on u7.Id = se21.UnitId AND se21.BaseIsDelete = 0
            INNER JOIN  up_Unit AS u21 ON ur6.UnitId = u21.Id AND u21.Statuz = 1 AND u21.BaseIsDelete = 0
            INNER JOIN  up_UnitRelation AS ur22 ON u21.Id = ur22.ExtensionObjId AND ur22.ExtensionType = 3 AND ur22.BaseIsDelete = 0
            LEFT JOIN  sys_static_dictionary AS dic4 ON ue2.JobTitle = dic4.DictionaryId  AND dic4.BaseIsDelete = 0
            LEFT JOIN bn_UserSchoolStageSubject AS subject ON subject.IsCurrentUnit = 1 AND subject.BaseIsDelete = 0 AND subject.UserId = ur1.SysUserId
            INNER JOIN zb_Report AS rp ON rp.BaseIsDelete = 0 AND rp.IsReport = 1 AND rp.IsCurrent = 1 AND u7.Id = rp.SchoolId
            INNER JOIN zb_ReportDetail AS rd ON rd.BaseIsDelete = 0 AND rp.Id = rd.ReportId
            ");

            strSql.Append($@" ) A WHERE 1 = 1 ");

            var parameter = new List<DbParameter>();
            if (param != null)
            {
                //strSql.Append(" AND BaseIsDelete = @BaseIsDelete ");
                //parameter.Add(DbParameterExtension.CreateDbParameter("@BaseIsDelete", param.BaseIsDelete));
                if (param.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    strSql.Append(" AND UnitId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                else if (param.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    strSql.Append(" AND CountyId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                else if (param.UnitType == UnitTypeEnum.City.ParseToInt())
                {
                    strSql.Append(" AND CityId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                if (param.SchoolId > 0)
                {
                    strSql.Append(" AND UnitId = @SchoolId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
                }
                if (param.CountyId > 0)
                {
                    strSql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
                if (param.Gender >= 0)
                {
                    strSql.Append(" AND Gender = @Gender ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Gender", param.Gender));
                }
                if (param.JobTitle > 0)
                {
                    strSql.Append(" AND JobTitle = @JobTitle ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@JobTitle", param.JobTitle));
                }
                if (param.EducationLevel > 0)
                {
                    strSql.Append(" AND EducationLevel = @EducationLevel ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@EducationLevel", param.EducationLevel));
                }
                if (param.ExperimenterNature == ExperimenterNatureEnum.FullTime.ParseToInt() || param.ExperimenterNature == ExperimenterNatureEnum.PartTime.ParseToInt())
                {
                    strSql.Append(" AND ExperimenterNature = @ExperimenterNature ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ExperimenterNature", param.ExperimenterNature));
                }
                if (!string.IsNullOrEmpty(param.RealName))
                {
                    strSql.Append(" AND RealName like @RealName ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@RealName", $"%{param.RealName.Trim()}%"));
                }
                if (param.IsExperimenter >= 0)
                {
                    strSql.Append(" AND IsExperimenter = @IsExperimenter ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@IsExperimenter", param.IsExperimenter));
                }
            }
            return parameter;
        }
        /// <summary>
        /// 获取学科集合，（根据用户Id）
        /// </summary>
        /// <param name="userlist"></param>
        /// <returns></returns>
        private async Task<IEnumerable<StaticDictionaryEntity>> GetCousreListByUser(IEnumerable<long> userlist)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append($@"  SELECT * From (
                SELECT  R.UserId AS Id ,D.DictionaryId, D.DicName
                FROM  (SELECT UserId, IsCurrentUnit, BaseIsDelete, SubjectIdz FROM bn_UserSchoolStageSubject ) AS R
				INNER JOIN  sys_static_dictionary AS D ON R.SubjectIdz = D.DictionaryId AND D.TypeCode = '1005'
                WHERE R.IsCurrentUnit = 1 AND R.BaseIsDelete = 0 ) AS tb1 ");
            strSql.Append($" Where ({string.Join(" OR ", userlist.Select(m => $" Id = {m} "))} )");
            return await this.BaseRepository().FindList<StaticDictionaryEntity>(strSql.ToString());

            //// 简化查询，先获取所有用户的学科数据，然后在C#中处理字符串匹配
            //var userIds = string.Join(",", userlist);
            //StringBuilder strSql = new StringBuilder();
            //strSql.Append($@"
            //    SELECT DISTINCT uss.UserId AS Id, uss.SubjectIdz, D.DictionaryId, D.DicName
            //    FROM bn_UserSchoolStageSubject uss
            //    INNER JOIN sys_static_dictionary AS D ON D.TypeCode = '1005' AND D.BaseIsDelete = 0
            //    WHERE uss.IsCurrentUnit = 1 AND uss.BaseIsDelete = 0
            //      AND uss.UserId IN ({userIds})");

            //var allData = await this.BaseRepository().FindList<dynamic>(strSql.ToString());

            //// 在C#中处理字符串匹配逻辑
            //var result = new List<StaticDictionaryEntity>();
            //foreach (var item in allData)
            //{
            //    var subjectIdz = item.SubjectIdz?.ToString() ?? "";
            //    var dictionaryId = item.DictionaryId.ToString();

            //    // 检查是否匹配
            //    if (IsSubjectMatch(subjectIdz, dictionaryId))
            //    {
            //        result.Add(new StaticDictionaryEntity
            //        {
            //            Id = item.Id,
            //            DictionaryId = item.DictionaryId,
            //            DicName = item.DicName
            //        });
            //    }
            //}

            //return result.Distinct();
        }

        /// <summary>
        /// 检查学科ID是否在学科字符串中
        /// </summary>
        private bool IsSubjectMatch(string subjectIdz, string dictionaryId)
        {
            if (string.IsNullOrEmpty(subjectIdz) || string.IsNullOrEmpty(dictionaryId))
                return false;

            var subjects = subjectIdz.Split(',', StringSplitOptions.RemoveEmptyEntries);
            return subjects.Contains(dictionaryId);
        }
        #endregion

        #region 查询统计


        /// <summary>
        /// 统计实验员学段学科统计
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<List<UserExtensionEntity>> GetSchoolStageCourseList(UserExtensionListParam param)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(@$"  SELECT * From (
                 SELECT s1.Id
				 ,s1.RealName
				 ,s1.UserName
				 ,s1.Mobile
                 ,s1.UserStatus
				 ,ue2.IsExperimenter
				 ,ue2.ExperimenterNature
				 ,ue2.UnitId
				 ,UR.UnitId AS CountyId
                 ,ur2.UnitId AS CityId
                 ,se7.SchoolProp
                 ,dr6.DictionaryToId AS SchoolStageId
				 ,usss.SchoolStageIdz
				 ,usss.SubjectIdz
				 FROM  bn_UserExperimenterSet AS ues
				 INNER JOIN SysUser AS s1 ON s1.BaseIsDelete = 0 AND ues.SysUserId = s1.Id
				 INNER JOIN  up_UserExtension AS ue2 ON s1.Id = ue2.SysUserId AND ue2.BaseIsDelete = 0 AND ue2.IsCurrentUnit = 1
				 INNER JOIN  up_UnitRelation AS UR ON ue2.UnitId = UR.ExtensionObjId AND UR.ExtensionType = 3 AND UR.BaseIsDelete = 0
				 INNER JOIN  up_UnitRelation AS ur2 ON UR.UnitId = ur2.ExtensionObjId AND ur2.ExtensionType = 3 AND ur2.BaseIsDelete = 0
                 LEFT JOIN  up_SchoolExtension AS se7 ON ue2.UnitId  = se7.UnitId AND se7.BaseIsDelete = 0
                 LEFT JOIN  sys_dictionary_relation AS dr6 ON se7.SchoolProp = dr6.DictionaryId AND dr6.RelationType = 1
				 LEFT JOIN bn_UserSchoolStageSubject AS usss ON usss.BaseIsDelete = 0 AND se7.UnitId = usss.UnitId AND ues.SysUserId = usss.UserId
				 WHERE s1.BaseIsDelete = 0 AND s1.UserStatus = 1
                ) as tb1     WHERE 1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    strSql.Append(" AND UnitId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                else if (param.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    strSql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.UnitId));
                }
                else if (param.UnitType == UnitTypeEnum.City.ParseToInt())
                {
                    strSql.Append(" AND CityId = @CityId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CityId", param.UnitId));
                }
                if (param.SchoolStageId > 0)
                {
                    strSql.Append(" AND SchoolStageId = @SchoolStageId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStageId", param.SchoolStageId));
                }
                if (param.IsExperimenter > -1)
                {
                    strSql.Append(" AND IsExperimenter = @IsExperimenter ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@IsExperimenter", param.IsExperimenter));
                }
                if (param.SchoolProple != -10000)
                {
                    strSql.Append(" AND SchoolProp <> @SchoolProple ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolProple", param.SchoolProple));
                }
            }
 
            var list = await this.BaseRepository().FindList<UserExtensionEntity>(strSql.ToString(), parameter.ToArray());
            return list.ToList();
        }

        #endregion
    }
}
