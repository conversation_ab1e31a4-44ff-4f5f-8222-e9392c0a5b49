﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using System.Web;
using Microsoft.AspNetCore.Mvc;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Entity;
using Dqy.Syjx.Model;
using Dqy.Syjx.Web.Controllers;
using Dqy.Syjx.Entity.InstrumentManage;
using Dqy.Syjx.Model.Input.InstrumentManage;
using Dqy.Syjx.Business.InstrumentManage;
using Dqy.Syjx.Model.Param.InstrumentManage;
using Dqy.Syjx.Web.Code;
using Dqy.Syjx.Enum.InstrumentManage;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Model.Result.InstrumentManage;
using Dqy.Syjx.Business.EvaluateManage;
using Dqy.Syjx.Model.Param.EvaluateManage;
using Dqy.Syjx.Entity.EvaluateManage;
using Dqy.Syjx.Entity.BusinessManage;
using Dqy.Syjx.Business.BusinessManage;
using Dqy.Syjx.Model.Param.BusinessManage;
using Dqy.Syjx.Business.SystemManage;
using Dqy.Syjx.Model.Param.SystemManage;
using Dqy.Syjx.Enum.SystemManage;
using Dqy.Syjx.Entity.SystemManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Model.Param.OrganizationManage;
using Dqy.Syjx.Service.OrganizationManage;
using Dqy.Syjx.Business.OrganizationManage;
using Dqy.Syjx.Service.SystemManage;
using NetTopologySuite.Index.HPRtree;
using Microsoft.EntityFrameworkCore.Infrastructure;
using NPOI.HPSF;

namespace Dqy.Syjx.Web.Areas.InstrumentManage.Controllers
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-09-27 17:37
    /// 描 述：控制器类
    /// </summary>

    [RequestFormLimits(ValueCountLimit = 500000)]
    [Area("InstrumentManage")]
    public class PurchaseDeclarationController : BaseController
    {
        private PurchaseDeclarationBLL purchaseDeclarationBLL = new PurchaseDeclarationBLL();
        private InstrumentStandardBLL instrumentStandardBLL = new InstrumentStandardBLL();
        private InstrumentAttendStaticBLL instrumentAttendStaticBLL = new InstrumentAttendStaticBLL();
        private UserSchoolStageSubjectBLL userSchoolStageSubjectBLL = new UserSchoolStageSubjectBLL();
        private ConfigSetBLL configSetBLL = new ConfigSetBLL();
        private UnitRelationBLL unitRelationBLL = new UnitRelationBLL();
        private SchoolInstrumentBLL schoolInstrumentBLL = new SchoolInstrumentBLL();

        #region 视图功能
        [AuthorizeFilter("instrument:purchasedeclaration:view")]
        public ActionResult PurchaseDeclarationIndex()
        {
            return View();
        }

        public ActionResult PurchaseFilling()
        {
            return View();
        }

        public async Task<ActionResult> PurchaseDeclarationForm()
        {
            int isAllowEditModel = 1; //是否允许学校自定义规格属性
            OperatorInfo operatorinfo = await Operator.Instance.Current();
            if(operatorinfo != null)
            {
                TData<ConfigSetEntity> configObj = await configSetBLL.GetEntityByCode(new ConfigSetListParam
                {
                    UnitId = operatorinfo.UnitId.Value,
                    UnitType = (UnitTypeEnum)operatorinfo.UnitType,
                    ConfigUnitType = UnitTypeEnum.County,
                    TypeCode = "1002_SFXXZDYGG"
                });

                if (configObj.Tag == 1 && configObj.Data != null)
                {
                    int.TryParse(configObj.Data.ConfigValue, out isAllowEditModel);
                }
            }
            
            ViewBag.IsAllowEditModel = isAllowEditModel;

            return View();
        }

        public ActionResult StandardChoose()
        {
            return View();
        }

        public ActionResult MallProductChoose()
        {
            return View();
        }

        public ActionResult SelectStandard()
        {
            return View();
        }

        public ActionResult ConcatenationTab()
        {
            return View();
        }

        public ActionResult SelectMallProduct()
        {
            return View();
        }

        [AuthorizeFilter("instrument:purchasedeclaration:view")]
        public ActionResult InstrumentStandardIndex()
        {
            return View();
        }

        /// <summary>
        /// 按达标填报页面
        /// </summary>
        /// <returns></returns>
        [AuthorizeFilter("instrument:purchasedeclaration:view")]
        public async Task<ActionResult> StandardDeclarationForm()
        {
            int isAllowEditModel = 1; //是否允许学校自定义规格属性
            OperatorInfo operatorinfo = await Operator.Instance.Current();
            if (operatorinfo != null)
            {
                TData<ConfigSetEntity> configObj = await configSetBLL.GetEntityByCode(new ConfigSetListParam
                {
                    UnitId = operatorinfo.UnitId.Value,
                    UnitType = (UnitTypeEnum)operatorinfo.UnitType,
                    ConfigUnitType = UnitTypeEnum.County,
                    TypeCode = "1002_SFXXZDYGG"
                });

                if (configObj.Tag == 1 && configObj.Data != null)
                {
                    int.TryParse(configObj.Data.ConfigValue, out isAllowEditModel);
                }
            }

            ViewBag.IsAllowEditModel = isAllowEditModel;

            return View();
        }
        #endregion

        #region 获取数据
        /// <summary>
        /// 
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpGet]
        [AuthorizeFilter("instrument:purchasedeclaration:search")]
        public async Task<ActionResult> GetListJson(PurchaseDeclarationListParam param)
        {
            TData<List<PurchaseDeclarationEntity>> obj = await purchaseDeclarationBLL.GetList(param);
            return Json(obj);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpGet]
        [AuthorizeFilter("instrument:purchasedeclaration:search")]
        public async Task<ActionResult> GetSubmitJson(PurchaseDeclarationListParam param)
        {
            TData<List<PurchaseDeclarationEntity>> obj = new TData<List<PurchaseDeclarationEntity>>();
            OperatorInfo operatorInfo = await Operator.Instance.Current();
            if (operatorInfo != null)
            {
                param.SchoolId = operatorInfo.UnitId;
                param.UserId = operatorInfo.UserId;
                param.Statuz = -100;
                obj = await purchaseDeclarationBLL.GetList(param);

                obj.Data.FindAll(a => a.Num == 0).ForEach(a => a.Num = null);
                obj.Data.FindAll(a => a.Price == 0).ForEach(a => a.Price = null);

                //查询区县设置
                long parentId = 0;
                var listUnitRelation = await unitRelationBLL.GetList(new UnitRelationListParam { ExtensionType = 3, ExtensionObjId = operatorInfo.UnitId.Value });
                if (listUnitRelation.Data.Count > 0)
                {
                    parentId = listUnitRelation.Data[0].UnitId.Value;
                }
                var listPriceSet = await configSetBLL.GetDefaultList(new ConfigSetListParam { TypeCode = "1002_SFXXZDYGG", UnitId = parentId });
                if (listPriceSet.Data.Count > 0 && listPriceSet.Data[0].ConfigValue.Equals("0"))
                {
                    obj.Total = -1;
                }
            }
            return Json(obj);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AuthorizeFilter("instrument:purchasedeclaration:search")]
        public async Task<ActionResult> GetNeedValidateJson()
        {
            TData<string> obj = new TData<string>();
            ConfigSetListParam param = new ConfigSetListParam();
            param.TypeCode = "1SYYQ-1CGJH-1CGJH-Reason";
            param.ModuleType = ConfigSetModuleTypeEnum.FormRequired.ParseToInt();
            TData<List<ConfigSetEntity>> configObj = await configSetBLL.GetListByCode(param);
            if (configObj.Tag == 1 && configObj.Data.Count > 0)
            {
                obj.Tag = 1;
                obj.Data = configObj.Data[0].ConfigValue;
            }
            return Json(obj);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        [HttpGet]
        [AuthorizeFilter("instrument:purchasedeclaration:search")]
        public async Task<ActionResult> GetPageListJson(PurchaseDeclarationListParam param, Pagination pagination)
        {
            TData<List<PurchaseDeclarationEntity>> obj = new TData<List<PurchaseDeclarationEntity>>();
            OperatorInfo operatorInfo = await Operator.Instance.Current();
            if (operatorInfo != null)
            {
                param.IsAssociation = 1;
                param.SchoolId = operatorInfo.UnitId;
                param.UserId = operatorInfo.UserId;
                obj = await purchaseDeclarationBLL.GetPageList(param, pagination);
            }
            return Json(obj);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> GetFormJson(long id)
        {
            TData<PurchaseDeclarationEntity> obj = await purchaseDeclarationBLL.GetEntity(id);
            return Json(obj);
        }

        /// <summary>
        /// 获取仪器商城产品
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> GetMallProductListJson(long instrumentStandardId, Pagination pagination,string instrumentStandarCode= "")
        {
            TData<List<ProductSearchSimpleYun>> obj = new TData<List<ProductSearchSimpleYun>>();
            string standarCode = "";
            if (!string.IsNullOrEmpty(instrumentStandarCode) && instrumentStandarCode!= "null")
            {
                var standardEntity = await instrumentStandardBLL.GetEntityByCode(instrumentStandarCode);
                if(standardEntity.Data.ClassType == 2)
                {
                    var pidEntity = await instrumentStandardBLL.GetEntity(standardEntity.Data.Pid.Value);
                    instrumentStandarCode = pidEntity.Data.Code;
                }
                standarCode = instrumentStandarCode;
            }
            else
            {
                var standardEntity = await instrumentStandardBLL.GetEntity(instrumentStandardId);
                if (standardEntity.Tag == 1)
                {
                    standarCode = standardEntity.Data.Code;
                }
            }
            obj = await purchaseDeclarationBLL.GetInstrumentApiList(new SearchInstrumentApiInputModel
            {
                code = standarCode,
                CurrPage = pagination.PageIndex,
                Limit = pagination.PageSize
            });
            return Json(obj);
        }

        /// <summary>
        /// 获取仪器达标数据
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        [HttpGet]
        [AuthorizeFilter("instrument:purchasedeclaration:search")]
        public async Task<ActionResult> GetInstrumentStandardListJson(InstrumentAttendStaticListParam param, Pagination pagination)
        {
            TData<List<InstrumentAttendStaticEntity>> obj = new TData<List<InstrumentAttendStaticEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current();
            if(operatorinfo != null)
            {
                List<UserSchoolStageSubjectEntity> powerList = (await userSchoolStageSubjectBLL.GetList(new UserSchoolStageSubjectListParam { UserId = operatorinfo.UserId ?? 0 })).Data;
                if (powerList.Count > 0)
                {
                    //param.SchoolStageIdz = powerList[0].SchoolStageIdz;
                    //param.CourseIdz = powerList[0].SubjectIdz;
                    param.SchoolStageIdz = string.Join(",", powerList.ToList().Select(f => f.SchoolStageIdz));
                    param.CourseIdz = string.Join(",", powerList.ToList().Select(f => f.SubjectIdz));
                    param.IsOnlyShowNoStandard = 1;
                    param.IsSearchInuptNum = 1;
                    obj = await instrumentAttendStaticBLL.GetPageList(param, pagination);
                }
            }
            return Json(obj);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> GetDeclarationByAttendId(long id)
        {
            var obj = await instrumentAttendStaticBLL.GetEntity(id);
            return Json(obj);
        }

        #endregion

        #region 提交数据
        [HttpPost]
        [AuthorizeFilter("instrument:purchasedeclaration:add,instrument:purchasedeclaration:edit")]
        public async Task<ActionResult> SaveFormJson(PurchaseDeclarationInputModel entity)
        {
            OperatorInfo operatorInfo = await Operator.Instance.Current();
            TData<string> obj = new TData<string>();
            if (operatorInfo != null)
            {
                ConfigSetListParam param = new ConfigSetListParam();
                param.TypeCode = "1SYYQ-1CGJH-1CGJH";
                param.ModuleType = ConfigSetModuleTypeEnum.FormRequired.ParseToInt();
                TData<List<ConfigSetEntity>> configObj = await configSetBLL.GetListByCode(param);
                if (configObj.Tag == 1 && configObj.Data.Count > 0)
                {
                    string errorMsg = "";
                    foreach (var item in configObj.Data)
                    {
                        if (item.ConfigValue == "1")
                        {
                            string typecode = item.TypeCode.Replace("1SYYQ-1CGJH-1CGJH-", "");
                            if (typecode == "Reason")
                            {
                                if (string.IsNullOrEmpty(entity.Reason))
                                {
                                    errorMsg += (item.VerifyHint + "<br/>");
                                }
                            }
                        }
                    }
                    if (errorMsg != "")
                    {
                        obj.Tag = 0;
                        obj.Message = errorMsg;
                        return Json(obj);
                    }
                }

                entity.SchoolId = operatorInfo.UnitId;
                obj = await purchaseDeclarationBLL.SaveForm(entity);
            }
            return Json(obj);
        }

        [HttpPost]
        [AuthorizeFilter("instrument:purchasedeclaration:delete")]
        public async Task<ActionResult> DeleteFormJson(long id)
        {
            TData obj = await purchaseDeclarationBLL.DeleteForm(id);
            return Json(obj);
        }

        [HttpPost]
        [AuthorizeFilter("instrument:purchasedeclaration:edit")]
        public async Task<ActionResult> GoNext(long id)
        {
            TData obj = await purchaseDeclarationBLL.GoNext(id);
            return Json(obj);
        }

        [HttpPost]
        [AuthorizeFilter("instrument:purchasedeclaration:edit")]
        public async Task<ActionResult> Withdraw(long id)
        {
            TData obj = await purchaseDeclarationBLL.Withdraw(id);
            return Json(obj);
        }

        [HttpPost]
        [AuthorizeFilter("instrument:purchasedeclaration:batchsubmit")]
        public async Task<ActionResult> BatchGoNext(string ids)
        {
            TData obj = new TData();
            ids = StringFilter.SearchSql(ids);
            var idList = ids.Split(',');
            int total = 0;
            int success = 0;
            foreach (var idstr in idList)
            {
                long id = 0;
                if (long.TryParse(idstr, out id))
                {
                    total++;
                    var r = await purchaseDeclarationBLL.GoNext(id);
                    if (r.Tag == 1)
                        success++;
                }
            }

            obj.Tag = 1;
            if (total == success)
                obj.Message = "批量提交成功！";
            else
                obj.Message = $"共批量提交{total}条数据，其中成功{success}条，失败{total - success}条。失败可能原因：状态不可操作，或您无权限操作。";

            return Json(obj);
        }


        /// <summary>
        /// 批量提交
        /// </summary>
        /// <param name="list">仪器采购计划申报表集合数据</param>
        /// <param name="operateType">操作类型（0：保存；1：提交）</param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("instrument:purchasedeclaration:add,instrument:purchasedeclaration:edit")]
        public async Task<ActionResult> SaveBatchFormJson(List<PurchaseDeclarationInputModel> list,int operateType)
        {
            TData<string> obj = new TData<string>();
            string tipMsg = "";
            //判断“采购理由”是否必填
            ConfigSetListParam param = new ConfigSetListParam();
            param.TypeCode = "1SYYQ-1CGJH-1CGJH-Reason";
            param.ModuleType = ConfigSetModuleTypeEnum.FormRequired.ParseToInt();
            TData<List<ConfigSetEntity>> configObj = await configSetBLL.GetListByCode(param);
            if (configObj.Tag == 1 && configObj.Data.Count > 0)
            {
                if (configObj.Data[0].ConfigValue.Equals("1"))
                {
                    var listReson = list.Where(a => a.Reason == "").ToList();
                    if(listReson.Count > 0)
                    {
                        obj.Tag = 0;
                        obj.Message = "配置了“采购理由”必填，提交的数据必须填写采购理由！";
                        return Json(obj);
                    }
                }
            }

            //验证学科、学科是否有不存在的
            var reportStage = list.GroupBy(f => new { f.Stage }).ToList();
            var reportCourse = list.GroupBy(f => new { f.Course }).ToList();
            var listStage = await userSchoolStageSubjectBLL.GetSchoolStageByUser(0);
            var listCourse = await userSchoolStageSubjectBLL.GetSubjectByUser(0);
            if(listStage.Data.Count() == 0 || listCourse.Data.Count() == 0)
            {
                obj.Tag = 0;
                obj.Message = "适用学段或适用学科未获得授权，不能填报！";
                return Json(obj);
            }
            else
            {
                foreach(var item in reportStage)
                {
                    if (!listStage.Data.ToList().Exists(f => f.DicName.Equals(item.Key.Stage)))
                    {
                        tipMsg += item.Key.Stage + "、";
                    }
                }
                if (!string.IsNullOrEmpty(tipMsg))
                {
                    tipMsg = tipMsg.TrimEnd('、');
                    obj.Tag = 0;
                    obj.Message = $"您未被授权管理学段({tipMsg})，所以不能录入关于这些学段的仪器信息。";
                    return Json(obj);
                }
                var courseList = listCourse.Data.ToList();
                foreach (var item in reportCourse)
                {
                    if (!courseList.Exists(f => f.DicName.Equals(item.Key.Course)))
                    {
                        tipMsg += item.Key.Course + "、";
                    }
                }
                if (!string.IsNullOrEmpty(tipMsg))
                {
                    tipMsg = tipMsg.TrimEnd('、');
                    obj.Tag = 0;
                    obj.Message = $"您未被授权管理学科({tipMsg})，所以不能录入关于这些学科的仪器信息。";
                    return Json(obj);
                }
            }

            //判断学段、学科是否对应
            string strMessg = "";
            foreach (PurchaseDeclarationInputModel p in list)
            {
                string rowMsg = await schoolInstrumentBLL.CheckStageSubjectMessage(p.StageId, p.CourseId, p.Stage, p.Course);
                if (!string.IsNullOrEmpty(rowMsg))
                {
                    rowMsg = $"第{p.RowIndex}行," + rowMsg;
                    strMessg += rowMsg+"<br/>";
                }
            }
           
            //判断输入的分类代码是否存在
            List<InstrumentModel> listCompare = await instrumentStandardBLL.GetCheckCodeList();
            foreach (PurchaseDeclarationInputModel input in list)
            {
                if(input.OriginalCode.Length == 11)
                {
                    if (!listCompare.Exists(f => f.Code.Substring(0, 9) == input.OriginalCode.Substring(0, 9)))
                    {
                        strMessg += $"第{input.RowIndex}行,分类代码“{input.OriginalCode}”在基础库中不存在！<br/>";
                    }
                    else
                    {
                        var o = listCompare.Where(f => f.Code == input.OriginalCode).FirstOrDefault();
                        if (o != null)
                        {
                            input.Code = input.OriginalCode;
                        }
                        else
                        {
                            input.Code = input.OriginalCode.Substring(0, 9) + "00";
                        }
                    }
                }
                else
                {
                    strMessg += $"第{input.RowIndex}行,分类代码“{input.OriginalCode}”必须为11位！<br/>";
                }
            }
            if (!string.IsNullOrEmpty(strMessg))
            {
                obj.Tag = 0;
                obj.Message = strMessg;
                return Json(obj);
            }

            //处理手动录入，只填写编码未选择，可以填写末级分类，也能填写规格属性的分类
            List<PurchaseDeclarationInputModel> listSupose = list.Where(a=>a.InstrumentStandardId == 0).ToList();
            if (listSupose.Count > 0)
            {
                foreach(PurchaseDeclarationInputModel pd in listSupose)
                {
                    var o = listCompare.Where(a=>a.Code == pd.Code).FirstOrDefault();
                    if(o.ClassType == 2 || o.IsLast == 0)
                    {
                        pd.InstrumentStandardId = o.Pid;
                        pd.ModelStandardId = o.Id;
                    }
                    else
                    {
                        pd.InstrumentStandardId = o.Id;
                    }
                }
            }
            //处理从达标库选择
            List<PurchaseDeclarationInputModel> listCompliance = list.Where(a =>a.EntryType == 3).ToList();
            if (listCompliance.Count > 0)
            {
                List<string> listComplianceCode = listCompliance.Select(a => a.ModelCode).ToList();
                string strComplianceCode = string.Join(",", listComplianceCode);
                List<InstrumentModel> listResult = await instrumentStandardBLL.GetCheckCodeList(strComplianceCode);
                foreach (PurchaseDeclarationInputModel pd in listCompliance)
                {
                    var o = listResult.Where(a => a.Code == pd.ModelCode).FirstOrDefault();
                    if (o != null)
                    {
                        pd.ModelStandardId = o.Id;
                    }
                }
            }

            obj = await purchaseDeclarationBLL.SaveBatchFormJson(list, operateType);
            return Json(obj);
        }

        /// <summary>
        /// 临时保存
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("instrument:purchasedeclaration:add,instrument:purchasedeclaration:edit")]
        public async Task<ActionResult> SaveTempFormJson(List<PurchaseTempDeclarationInputModel> list)
        {
            TData<string> obj = new TData<string>();
            obj = await purchaseDeclarationBLL.SaveTempFormJson(list);
            return Json(obj);
        }

        /// <summary>
        /// 导出仪器-已报计划
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<IActionResult> ExportPurchaseDeclaration(PurchaseDeclarationListParam param)
        {
            TData<string> obj = new TData<string>();
            OperatorInfo operatorInfo = await Operator.Instance.Current();
            if (operatorInfo != null)
            {
                param.SchoolId = operatorInfo.UnitId;
                //param.Statuz = InstrumentAuditStatuzEnum.End.ParseToInt();
                param.UserId = operatorInfo.UserId;
                var resultObj = await purchaseDeclarationBLL.GetExportPageList(param, new Pagination { PageSize = int.MaxValue });
                if (resultObj.Tag == 1)
                {
                    string file = new ExcelHelper<PurchaseDeclarationEntity>().ExportToExcel("已报计划.xls",
                                                                                             "已报计划",
                                                                                             resultObj.Data,
                                                                                             new string[] { "PurchaseYear", "OriginalCode", "Name", "Model", "Num", "UnitName", "Price", "AmountSum", "Stage", "Course", "UserName" });
                    obj.Data = file;
                    obj.Tag = 1;
                }
            }
            return Json(obj);
        }
        #endregion
    }
}
