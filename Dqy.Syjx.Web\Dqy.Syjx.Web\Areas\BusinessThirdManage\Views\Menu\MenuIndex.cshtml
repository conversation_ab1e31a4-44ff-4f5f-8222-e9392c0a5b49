﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}

@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@section header{
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/bootstrap.treetable/1.0/bootstrap-treetable.min.css"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/bootstrap.treetable/1.0/bootstrap-treetable.min.js"))
}

<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <form>
                <div class="select-list">
                    <ul>
                        <li>
                            角色授权名称：<input id="menuName" col="MenuName" type="text" />
                        </li>
                        <li>
                            角色授权状态：<span id="menuStatus" col="MenuStatus"></span>
                        </li>
                        <li><a class="btn btn-primary btn-sm" onclick="searchTreeGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a></li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>

<script type="text/javascript">
    var parentId = 0;
    var id = 0;
    $(function () {
        initTreeGrid();

        $("#menuStatus").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(StatusEnum).EnumToDictionaryString())) });
    });

    function initTreeGrid() {
        var options = {
            code: "Id",
            parentCode: "ParentId",
            expandAll: false,
            expandFirst: false,
            toolbar: '#toolbar',
            expandColumn: '1',
            url: '@Url.Content("~/SystemManage/Menu/GetListJson")',
            modalName: "角色授权",
            columns: [
                {
                    field: 'MenuName', title: '角色授权名称', width: '20%', formatter: function (value, row, index) {
                        if (ys.isNullOrEmpty(row.MenuIcon)) {
                            return row.MenuName;
                        } else {
                            return '<i class="' + row.MenuIcon + '"></i> <span class="nav-label">' + row.MenuName + '</span>';
                        }
                    }
                },
                {
                    field: 'MenuType', title: '类型', width: '10%', align: "left",
                    formatter: function (value, item, index) {
                        if (item.MenuType == "@MenuTypeEnum.Directory.ParseToInt()") {
                            return '<span class="label label-success">@MenuTypeEnum.Directory.GetDescription()</span>';
                        }
                        else if (item.MenuType == "@MenuTypeEnum.Menu.ParseToInt()") {
                            return '<span class="label label-primary">@MenuTypeEnum.Menu.GetDescription()</span>';
                        }
                        else if (item.MenuType == "@MenuTypeEnum.Button.ParseToInt()") {
                            return '<span class="label label-warning">@MenuTypeEnum.Button.GetDescription()</span>';
                        }
                    }
                },
                { field: 'MenuSort', title: '显示顺序', width: '5%', align: "left" },
                { field: 'MenuUrl', title: '请求地址', width: '35%', align: "left" },
                { field: 'Authorize', title: '权限标识', width: '15%', align: "left" },
                {
                    field: 'MenuStatus', title: '状态', width: '5%', align: "left",
                    formatter: function (value, row, index) {
                        if (row.MenuStatus == "@StatusEnum.Yes.ParseToInt()") {
                            return '<span class="badge badge-primary">' + "@StatusEnum.Yes.GetDescription()" + '</span>';
                        } else {
                            return '<span class="badge badge-warning">' + "@StatusEnum.No.GetDescription()" + '</span>';
                        }
                    }
                },
                { field: 'Remark', title: '备注', width: '10%', align: "left" },
                {
                    field: '', title: '操作', width: '10%', align: 'left',
                    formatter: function (value, row, index) {
                        var html = $.Format('<a class="btn btn-info btn-xs" href="#" onclick="verify(this)" value="{0}"><i class="fa fa-eye"></i>验签</a> ', row.Id);
                        return html;
                    }
                }
            ],
            onLoadSuccess: function () {
                if (id != 0) {
                    $('#gridTable').ysTreeTable('expandRowById', id);
                }
            }
        };
        $('#gridTable').ysTreeTable(options);
    }

    function searchTreeGrid(callBackId) {
        var param = $("#searchDiv").getWebControls();
        $('#gridTable').ysTreeTable('search', param);
        if (callBackId) {
            id = callBackId;
        }
    }
    function verify(obj) {
        var id = $(obj).attr('value');
        var url = '@Url.Content("~/BusinessThirdManage/SignWx/Verify?bizType=102")' + '&id=' + id;

        ys.ajax({
                    url: url,
                    type: 'post',
                    success: function (obj) {
                        if (obj.Tag == 1) {
                            ys.msgSuccess(obj.Message);
                        }
                        else {
                            ys.msgError(obj.Message);
                        }
                    }
                });

    }
</script>