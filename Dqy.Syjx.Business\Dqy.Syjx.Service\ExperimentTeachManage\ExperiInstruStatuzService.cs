﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.ExperimentTeachManage;
using Dqy.Syjx.Model.Param.ExperimentTeachManage;

namespace Dqy.Syjx.Service.ExperimentTeachManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2023-08-01 15:20
    /// 描 述：实验仪器状态服务类
    /// </summary>
    public class ExperiInstruStatuzService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<ExperiInstruStatuzEntity>> GetList(ExperiInstruStatuzListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<ExperiInstruStatuzEntity>> GetPageList(ExperiInstruStatuzListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<ExperiInstruStatuzEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<ExperiInstruStatuzEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(ExperiInstruStatuzEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(ExperiInstruStatuzEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
               strSql = $" update ex_ExperiInstruStatuz set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
               strSql = $"update ex_ExperiInstruStatuz set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }

        public async Task DeleteByExperiInstruStatuzId(long experiinstrustatuzid)
        {
            string strSql = $"update ex_ExperiInstruLog set BaseIsDelete = 1 where ExperiInstruStatuzId = {experiinstrustatuzid}";
            await this.BaseRepository().ExecuteBySql(strSql);
        }

        #endregion

        #region 私有方法
        private Expression<Func<ExperiInstruStatuzEntity, bool>> ListFilter(ExperiInstruStatuzListParam param)
        {
            var expression = LinqExtensions.True<ExperiInstruStatuzEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (param.ExperimentBookingIds != null && param.ExperimentBookingIds.Count > 0)
                {
                    expression = expression.And(t => param.ExperimentBookingIds.Contains(t.ExperimentBookingId));
                }
            }
            return expression;
        }
        #endregion
    }
}
