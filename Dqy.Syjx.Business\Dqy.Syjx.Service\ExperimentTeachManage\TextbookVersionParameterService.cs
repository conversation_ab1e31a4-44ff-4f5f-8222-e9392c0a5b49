﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.ExperimentTeachManage;
using Dqy.Syjx.Model.Param.ExperimentTeachManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Model.Result.ExperimentTeachManage;
using System.Data;

namespace Dqy.Syjx.Service.ExperimentTeachManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-12-14 14:27
    /// 描 述：服务类
    /// </summary>
    public class TextbookVersionParameterService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<TextbookVersionParameterEntity>> GetList(TextbookVersionListParameterListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseReposusing System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.ExperimentTeachManage;
using Dqy.Syjx.Model.Param.ExperimentTeachManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Model.Result.ExperimentTeachManage;
using System.Data;

namespace Dqy.Syjx.Service.ExperimentTeachManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-12-14 14:27
    /// 描 述：服务类
    /// </summary>
    public class TextbookVersionParameterService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<TextbookVersionParameterEntity>> GetList(TextbookVersionListParameterListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<TextbookVersionParameterEntity>> GetPageList(TextbookVersionListParameterListParam param, Pagination pagination)
        {
            StringBuilder sql = new StringBuilder();
            var expression = ListFilter(param, sql);
            var list = await this.BaseRepository().FindList<TextbookVersionParameterEntity>(sql.ToString(), expression.ToArray(), pagination);
            return list.ToList();
        }

        public async Task<TextbookVersionParameterEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<TextbookVersionParameterEntity>(id);
        }

        /// <summary>
        /// 获取版本数量总计
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<DataTable> GetTextbookVersionBaseNumTotal(TextbookVersionListParameterListParam param)
        {
            StringBuilder sql = new StringBuilder();
            var expression = ListFilterTotalNum(param, sql);
            return await this.BaseRepository().FindTable(sql.ToString(), expression.ToArray());
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(TextbookVersionParameterEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(TextbookVersionParameterEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update ex_TextbookVersionParameter set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
                strSql = $"update ex_TextbookVersionParameter set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<TextbookVersionParameterEntity, bool>> ListFilter(TextbookVersionListParameterListParam param)
        {
            var expression = LinqExtensions.True<TextbookVersionParameterEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (!param.SchoolStage.IsNullOrZero())
                {
                    expression = expression.And(t => t.SchoolStage == param.SchoolStage);
                }
                if (!param.GradeId.IsNullOrZero())
                {
                    expression = expression.And(t => t.GradeId == param.GradeId);
                }
                if (!param.CourseId.IsNullOrZero())
                {
                    expression = expression.And(t => t.CourseId == param.CourseId);
                }
                if (!param.SchoolTerm.IsNullOrZero())
                {
                    expression = expression.And(t => t.SchoolTerm == param.SchoolTerm);
                }
            }
            return expression;
        }

        private List<DbParameter> ListFilter(TextbookVersionListParameterListParam param, StringBuilder strSql)
        {
            strSql.Append(@$" SELECT * From (
                               SELECT  VP.Id ,
                                       VP.BaseCreateTime ,VP.BaseModifyTime ,VP.BaseCreatorId ,VP.BaseModifierId ,
                                       VP.SchoolStage ,VP.GradeId ,VP.CourseId ,VP.SchoolTerm ,VP.Statuz ,
                                       VP.NeedShowNum ,VP.NeedGroupNum ,VP.OptionalShowNum ,VP.OptionalGroupNum ,
                                       VP.StandardNeedShowNum ,VP.StandardNeedGroupNum ,VP.StandardOptionalShowNum ,VP.StandardOptionalGroupNum ,
                                       D1.DicName AS SchoolStageName ,D2.DicName AS GradeName ,D3.DicName AS CourseName ,
                                      (VP.NeedShowNum + VP.NeedGroupNum + VP.OptionalShowNum + VP.OptionalGroupNum) AS AllTotal,
                                      (VP.StandardNeedShowNum + VP.StandardNeedGroupNum + VP.StandardOptionalShowNum + VP.StandardOptionalGroupNum) AS CheckTotal ,
                                      VP.CountyId ,AR.AreaName
                               FROM  ex_TextbookVersionParameter AS VP
                               INNER JOIN  sys_static_dictionary AS D1 ON VP.SchoolStage = D1.DictionaryId AND D1.BaseIsDelete = 0
                                                                    AND D1.TypeCode = '{DicTypeCodeEnum.SchoolStage.ParseToInt()}'
                               INNER JOIN  sys_static_dictionary AS D2 ON VP.GradeId = D2.DictionaryId AND D2.BaseIsDelete = 0 
                                                                    AND D2.TypeCode = '{DicTypeCodeEnum.Grade.ParseToInt()}'
                               INNER JOIN  sys_static_dictionary AS D3 ON VP.CourseId = D3.DictionaryId AND D3.BaseIsDelete = 0 
                                                                    AND D3.TypeCode = '{DicTypeCodeEnum.Course.ParseToInt()}'
                               INNER JOIN  up_Unit AS U ON VP.CountyId = U.Id 
							   LEFT JOIN  SysArea AS AR ON U.AreaId = AR.AreaCode 
                               WHERE VP.BaseIsDelete = 0                               
                          ) as T WHERE  1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (!param.SchoolStage.IsNullOrZero())
                {
                    strSql.Append(" AND SchoolStage = @SchoolStage ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStage", param.SchoolStage));
                }
                if (!param.GradeId.IsNullOrZero())
                {
                    strSql.Append(" AND GradeId = @GradeId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@GradeId", param.GradeId));
                }
                if (!param.CourseId.IsNullOrZero())
                {
                    strSql.Append(" AND CourseId = @CourseId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (!param.SchoolTerm.IsNullOrZero())
                {
                    strSql.Append(" AND SchoolTerm = @SchoolTerm ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolTerm", param.SchoolTerm));
                }
                if (!param.CountyId.IsNullOrZero())
                {
                    strSql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
            }
            return parameter;
        }

        private List<DbParameter> ListFilterTotalNum(TextbookVersionListParameterListParam param , StringBuilder strSql)
        {
            strSql.Append(@$" SELECT ISNULL(SUM(NeedShowNum) ,0) AS NeedShowNum ,ISNULL(SUM(NeedGroupNum) ,0) AS NeedGroupNum ,
                                     ISNULL(SUM(OptionalShowNum) ,0) AS OptionalShowNum ,ISNULL(SUM(OptionalGroupNum) ,0) AS OptionalGroupNum 
                              From (
                                   SELECT VB.NeedShowNum ,VB.NeedGroupNum ,VB.OptionalShowNum ,VB.OptionalGroupNum ,
                                          VC.SchoolStage ,VC.GradeId ,VC.CourseId ,VC.SchoolTerm ,VC.CountyIdz                                     
                                   FROM  ex_TextbookVersionCurrent AS VC
                                   INNER JOIN  ex_TextbookVersionBase AS VB ON VC.TextbookVersionBaseId = VB.Id AND VB.BaseIsDelete = 0                               
                                   WHERE VC.BaseIsDelete = 0                               
                          ) as T WHERE  1 = 1 ");
            var parameter = new List<DbParameter>();
            if (!param.SchoolStage.IsNullOrZero())
            {
                strSql.Append(" AND SchoolStage = @SchoolStage ");
                parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStage", param.SchoolStage));
            }
            if (!param.GradeId.IsNullOrZero())
            {
                strSql.Append(" AND GradeId = @GradeId");
                parameter.Add(DbParameterExtension.CreateDbParameter("@GradeId", param.GradeId));
            }
            if (!param.CourseId.IsNullOrZero())
            {
                strSql.Append(" AND CourseId = @CourseId");
                parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
            }
            if (!param.SchoolTerm.IsNullOrZero())
            {
                strSql.Append(" AND SchoolTerm = @SchoolTerm ");
                parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolTerm", param.SchoolTerm));
            }
            if (!param.CountyId.IsNullOrZero())
            {
                strSql.AppendFormat(" AND CountyIdz LIKE '%{0}%' ", param.CountyId);
            }
            return parameter;
        }

        /// <summary>
        /// 获取年级学科必做实验数量
        /// </summary>
        /// <param name="param"></param>
        /// <param name="strSql"></param>
        /// <returns></returns>
        public async Task<List<TextbookVersionParameterEntity>> GetGradeCourseNumList(TextbookVersionListParameterListParam param)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(@$" SELECT GradeId,CourseId,GradeName , CourseName 
                            ,Sum(StandardNeedShowNum) AS StandardNeedShowNum
                            ,Sum(StandardNeedGroupNum) AS StandardNeedGroupNum
                            ,Sum(AllTotal) AS AllTotal
                             From (
                              SELECT  VP.Id  
									   ,VP.GradeId ,VP.CourseId
                                       ,VP.SchoolStage
									   ,D2.DicName AS GradeName ,D3.DicName AS CourseName 
									   ,VP.Statuz 
									   ,VP.CountyId 
                                       ,VP.NeedShowNum ,VP.NeedGroupNum  
                                       ,VP.StandardNeedShowNum ,VP.StandardNeedGroupNum  
									    ,(VP.StandardNeedShowNum + VP.StandardNeedGroupNum ) AS AllTotal 
                               FROM  ex_TextbookVersionParameter AS VP
                               INNER JOIN  sys_static_dictionary AS D2 ON VP.GradeId = D2.DictionaryId AND D2.BaseIsDelete = 0  AND D2.TypeCode =  '{DicTypeCodeEnum.Grade.ParseToInt()}'
                               INNER JOIN  sys_static_dictionary AS D3 ON VP.CourseId = D3.DictionaryId AND D3.BaseIsDelete = 0  AND D3.TypeCode = '{DicTypeCodeEnum.Course.ParseToInt()}'
                               WHERE VP.BaseIsDelete = 0                            
                          ) as T WHERE  1 = 1 AND Statuz = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.SchoolStage > 0)
                {
                    strSql.AppendFormat(" AND SchoolStage = @SchoolStage ", param.SchoolStage);
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStage", param.SchoolStage));
                }
                if (!param.CountyId.IsNullOrZero())
                {
                    strSql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
            }
            strSql.Append(" group by GradeId,CourseId,GradeName , CourseName ");
            var list = await this.BaseRepository().FindList<TextbookVersionParameterEntity>(strSql.ToString(), parameter.ToArray());
            return list.ToList();
        }
        #endregion
    }
}
