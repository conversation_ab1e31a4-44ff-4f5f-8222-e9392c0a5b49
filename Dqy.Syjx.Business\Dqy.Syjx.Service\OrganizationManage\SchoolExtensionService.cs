﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.OrganizationManage;
using Dqy.Syjx.Model.Param.OrganizationManage;

namespace Dqy.Syjx.Service.OrganizationManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-09-16 13:07
    /// 描 述：服务类
    /// </summary>
    public class SchoolExtensionService : RepositoryFactory
    {
        #region 获取数据
        public async Task<List<SchoolExtensionEntity>> GetList(SchoolExtensionListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<SchoolExtensionEntity>> GetPageList(SchoolExtensionListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<SchoolExtensionEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<SchoolExtensionEntity>(id);
        }

        public async Task<SchoolExtensionEntity> GetEntityBySchoolId(long schoolId)
        {
            return await this.BaseRepository().FindEntity<SchoolExtensionEntity>(f => f.UnitId == schoolId);
        }
        #endregion

        #region 提交数据
        /// <summary>
        /// 更新指定的字段信息。
        /// </summary>
        /// <param name="entity">数据实体</param>
        /// <param name="fields">需要更新的字段</param>
        /// <returns></returns>
        public async Task UpdateForm(SchoolExtensionEntity entity, List<string> fields)
        {
            if (fields != null)
            {
                await entity.Modify();
                fields.Add("BaseModifyTime");
                fields.Add("BaseModifierId");
            }
            await this.BaseRepository().Update(entity, fields);
        }

        public async Task SaveForm(SchoolExtensionEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }
        public async Task SaveTransForm(SchoolExtensionEntity entity, Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }
        public async Task DeleteForm(string ids)
        {
            long[] idArr = TextHelper.SplitToArray<long>(ids, ',');
            await this.BaseRepository().Delete<SchoolExtensionEntity>(idArr);
        }
        #endregion

        #region 私有方法
        private Expression<Func<SchoolExtensionEntity, bool>> ListFilter(SchoolExtensionListParam param)
        {
            var expression = LinqExtensions.True<SchoolExtensionEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                expression = expression.And(m => m.UnitId == param.UnitId);
            }
            return expression;
        }

        public async Task<int> UpdateStudentNum(long unitid)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append($@"   UPDATE	  up_SchoolExtension 
                                SET StudentNum = ISNULL((SELECT SUM(a.StudentNum) FROM up_SchoolGradeClass AS a WHERE a.UnitId = {unitid} AND a.BaseIsDelete = 0  AND a.IsGraduate = 0 ),0)
                                , ClassNum = ISNULL((SELECT COUNT(a.Id) FROM up_SchoolGradeClass AS a WHERE a.UnitId = {unitid} AND a.BaseIsDelete = 0 AND a.IsGraduate = 0 ),0)
                                WHERE  up_SchoolExtension.UnitId = {unitid} ");
            return await this.BaseRepository().ExecuteBySql(strSql.ToString());
        }

        public async Task<int> UpdateClassNum(long unitid, Repository db)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.AppendFormat(@"   UPDATE	  up_SchoolExtension 
                                SET ClassNum = ISNULL((SELECT COUNT(a.Id) FROM up_SchoolGradeClass AS a WHERE a.UnitId = {0} AND a.BaseIsDelete = 0 AND a.IsGraduate = 0 ),0)
                                    ,StudentNum = ISNULL((SELECT SUM(a.StudentNum) FROM up_SchoolGradeClass AS a WHERE a.UnitId = {0} AND a.BaseIsDelete = 0 AND a.IsGraduate = 0) ,0)
                                WHERE  up_SchoolExtension.UnitId = {0} AND  up_SchoolExtension.BaseIsDelete = 0
                            ", unitid);
            return await db.ExecuteBySql(strSql.ToString());
        }
        #endregion
    }
}
