2025-06-17 10:29:35.4694||DEBUG||程序启动 |ContentRootPath:D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web |WebRootPath:D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\wwwroot |IsDevelopment:True |url: |action: 
2025-06-17 13:09:55.9982||DEBUG||程序启动 |ContentRootPath:D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web |WebRootPath:D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\wwwroot |IsDevelopment:True |url: |action: 
2025-06-17 13:10:25.3762||ERROR||Value cannot be null. (Parameter 'source')
   at System.Linq.ThrowHelper.ThrowArgumentNullException(ExceptionArgument argument)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Dqy.Syjx.Service.BusinessManage.UserSchoolStageSubjectService.GetUserSchoolStageSubject(Int64 unitId, Int64 userId, String unitStageIdz) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Service\BusinessManage\UserSchoolStageSubjectService.cs:line 207
   at Dqy.Syjx.Business.BusinessManage.UserSchoolStageSubjectBLL.GetUserSchoolStageSubject() in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Business\BusinessManage\UserSchoolStageSubjectBLL.cs:line 716
   at Dqy.Syjx.Web.Areas.BusinessManage.Controllers.UserSchoolStageSubjectController.GetUserSchoolStageSubject() in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\Areas\BusinessManage\Controllers\UserSchoolStageSubjectController.cs:line 141
   at lambda_method726(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
 |url: http://localhost/BusinessManage/UserSchoolStageSubject/GetUserSchoolStageSubject|action: GetUserSchoolStageSubject
2025-06-17 13:10:48.3183||DEBUG||程序启动 |ContentRootPath:D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web |WebRootPath:D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\wwwroot |IsDevelopment:True |url: |action: 
2025-06-17 17:57:16.8196||DEBUG||程序启动 |ContentRootPath:D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web |WebRootPath:D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\wwwroot |IsDevelopment:True |url: |action: 
2025-06-17 17:57:53.6695||ERROR||下载失败：非法操作，已拦截！
   at Dqy.Syjx.Web.Controllers.FileController.DownloadFile(String filePath, Int32 delete) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\Controllers\FileController.cs:line 53
   at lambda_method1185(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
 |url: http://localhost/File/DownloadFile|action: DownloadFile
2025-06-17 17:59:12.3185||INFO||URL1:/InstrumentManage/PurchaseDeclaration/PurchaseFilling |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-17 17:59:14.7658||INFO||URL1:/InstrumentManage/PurchaseDeclaration/PurchaseDeclarationIndex |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-17 18:00:33.4829||DEBUG||程序启动 |ContentRootPath:D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web |WebRootPath:D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\wwwroot |IsDevelopment:True |url: |action: 
2025-06-17 18:01:09.6666||INFO||URL1:/InstrumentManage/PurchaseDeclaration/PurchaseFilling |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-17 18:01:11.4665||INFO||URL1:/InstrumentManage/PurchaseAudit/PurchaseAuditIndex |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-17 18:01:13.7990||INFO||URL1:/InstrumentManage/PurchaseDeclaration/PurchaseDeclarationIndex |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
