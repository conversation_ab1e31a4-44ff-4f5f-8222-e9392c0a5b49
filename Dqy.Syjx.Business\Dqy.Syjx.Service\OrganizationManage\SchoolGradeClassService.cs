﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.OrganizationManage;
using Dqy.Syjx.Model.Param.OrganizationManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Entity.SystemManage;
using Dqy.Syjx.Entity;
using Dqy.Syjx.Enum.ExperimentTeachManage;
using Org.BouncyCastle.Pqc.Crypto.Falcon;
using Dqy.Syjx.Entity.BusinessManage;

namespace Dqy.Syjx.Service.OrganizationManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-09-17 10:41
    /// 描 述：服务类
    /// </summary>
    public class SchoolGradeClassService : RepositoryFactory
    {
        #region 获取数据
        public async Task<List<SchoolGradeClassEntity>> GetList(SchoolGradeClassListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<SchoolGradeClassEntity>> GetPageList(SchoolGradeClassListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ListFilter(param, strSql);
            var list = await this.BaseRepository().FindList<SchoolGradeClassEntity>(strSql.ToString(), filter.ToArray(), pagination);
            return list.ToList();
        }

        public async Task<List<SchoolGradeClassEntity>> GetPageList2(SchoolGradeClassListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ClassListFilter(param, strSql);
            var list = await this.BaseRepository().FindList<SchoolGradeClassEntity>(strSql.ToString(), filter.ToArray(), pagination);
            return list.ToList();
        }

        public async Task<List<SchoolGradeClassEntity>> GetTotal(SchoolGradeClassListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = SummaryFilter(param, strSql);
            var list = await this.BaseRepository().FindList<SchoolGradeClassEntity>(strSql.ToString(), filter.ToArray());
            return list.ToList();
        }

        /// <summary>
        /// 获取当年班级下拉列表。
        /// 1：如果传入班级Id集合，则返回对应的班级（Id集合可以为任课信息中的任课班级）
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<List<SchoolGradeClassEntity>> GetGradeClassList(SchoolGradeClassListParam param)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ListGradeClassFilter(param, strSql);
            var list = await this.BaseRepository().FindList<SchoolGradeClassEntity>(strSql.ToString(), filter.ToArray());
            return list.ToList();
        }

        /// <summary>
        /// 获取当年班级下拉列表。
        /// 1：如果传入班级Id集合，则返回对应的班级（Id集合可以为任课信息中的任课班级）
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<List<SchoolGradeClassEntity>> GetGradeClassByCourse(SchoolGradeClassListParam param)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = new List<DbParameter>();

            if (!string.IsNullOrEmpty(param.GradeClassIds))
            {
                // 验证并清理 ID 字符串，防止 SQL 注入
                var cleanGradeClassIds = StringFilter.ValidateAndCleanIds(param.GradeClassIds);
                if (cleanGradeClassIds == null)
                    throw new ArgumentException("无效的参数: 只允许数字和逗号组合");

                strSql.Append($@"  SELECT * From (
                SELECT DISTINCT gc2.Id ,gc2.SchoolStage ,gc2.GradeId ,gc2.ClassId ,gc2.ClassDesc ,dic3.DicName ,gc2.UnitId , dic3.DicName + '（'+ gc2.ClassDesc + '）' AS ClassName
                FROM  up_SchoolGradeClass AS gc2
                INNER JOIN  sys_static_dictionary AS dic3 ON gc2.GradeId = dic3.DictionaryId AND dic3.TypeCode = '{DicTypeCodeEnum.Grade.ParseToInt()}' AND dic3.BaseIsDelete = 0
                WHERE gc2.BaseIsDelete = 0 AND gc2.IsGraduate = 0 AND gc2.Id IN ({cleanGradeClassIds})
            ) as tb1 WHERE   1 = 1 ");

                if (param != null)
                {
                    if (param.UnitType == UnitTypeEnum.School.ParseToInt())
                    {
                        strSql.Append(" AND UnitId = @UnitId ");
                        filter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                    }
                    else if (param.UnitType == UnitTypeEnum.System.ParseToInt())
                    {
                        //超级管理员查询所有的。
                    }
                    if (param.StudyPhase > 0)
                    {
                        strSql.Append(" AND SchoolStage = @StudyPhase ");
                        filter.Add(DbParameterExtension.CreateDbParameter("@StudyPhase", param.StudyPhase));
                    }
                    if (!string.IsNullOrEmpty(param.SchoolStage))
                    {
                        strSql.Append($" AND SchoolStage IN ({param.SchoolStage}) ");
                    }
                }
            }
            var list = await this.BaseRepository().FindList<SchoolGradeClassEntity>(strSql.ToString(), filter.ToArray());
            return list.ToList();
        }

        /// <summary>
        /// 获取学科（只获取理化生科学，根据设置的任课学科）
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<List<StaticDictionaryEntity>> GetCourseList(SchoolGradeClassListParam param)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ListCourseFilter(param, strSql);
            var list = await this.BaseRepository().FindList<StaticDictionaryEntity>(strSql.ToString(), filter.ToArray());
            return list.ToList();
        }

        public async Task<SchoolGradeClassEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<SchoolGradeClassEntity>(id);
        }

        /// <summary>
        /// 学校、区县、市级三端获取学校班级信息
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<List<SchoolGradeClassEntity>> GetGradeClassByCountyAndCity(SchoolGradeClassListParam param)
        {
            StringBuilder sql = new StringBuilder();
            sql.Append(@"
                    SELECT * FROM (
	                    SELECT  SG.Id ,SG.UnitId ,SG.StartYear ,SG.SchoolStage ,SG.GradeId ,SG.ClassId ,SG.StudentNum ,SG.Memo ,SG.ClassDesc ,SG.IsGraduate ,
	                            SG.UnitId AS SchoolId ,UR.UnitId AS CountyId ,UR2.UnitId AS CityId
	                    FROM  up_SchoolGradeClass AS SG
	                    INNER JOIN  up_UnitRelation AS UR ON SG.UnitId = UR.ExtensionObjId AND UR.ExtensionType = 3
	                    INNER JOIN  up_UnitRelation AS UR2 ON UR.UnitId = UR2.ExtensionObjId AND UR2.ExtensionType = 3
	                    WHERE SG.BaseIsDelete = 0 AND SG.IsGraduate = 0
                    )T WHERE 1 = 1
            ");
            if (!param.UnitId.IsNullOrZero())
            {
                sql.Append(" AND UnitId = " + param.UnitId);
            }
            if (!param.CountyId.IsNullOrZero())
            {
                sql.Append(" AND CountyId = " + param.CountyId);
            }
            if (!param.CityId.IsNullOrZero())
            {
                sql.Append(" AND CityId = " + param.CityId);
            }
            if (!param.StudyPhase.IsNullOrZero())
            {
                sql.Append(" AND SchoolStage = " + param.StudyPhase);
            }
            return (await this.BaseRepository().FindList<SchoolGradeClassEntity>(sql.ToString())).ToList();
        }
        #endregion

        #region 提交数据
        /// <summary>
        /// 更新指定的字段信息。
        /// </summary>
        /// <param name="entity">数据实体</param>
        /// <param name="fields">需要更新的字段</param>
        /// <returns></returns>
        public async Task UpdateForm(SchoolGradeClassEntity entity, List<string> fields)
        {
            if (fields != null)
            {
                await entity.Modify();
                fields.Add("BaseModifyTime");
                fields.Add("BaseModifierId");
            }
            await this.BaseRepository().Update(entity, fields);
        }

        /// <summary>
        /// 更新指定的字段信息。
        /// </summary>
        /// <param name="entity">数据实体</param>
        /// <param name="fields">需要更新的字段</param>
        /// <returns></returns>
        public async Task UpdateTransForm(SchoolGradeClassEntity entity, List<string> fields, Repository db)
        {
            if (fields != null)
            {
                await entity.Modify();
                fields.Add("BaseModifyTime");
                fields.Add("BaseModifierId");
            }
            await db.Update(entity, fields);
        }

        public async Task SaveForm(SchoolGradeClassEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }
        public async Task SaveTransForm(SchoolGradeClassEntity entity, Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }
        public async Task DeleteForm(string ids)
        {
            long[] idArr = TextHelper.SplitToArray<long>(ids, ',');
            await this.BaseRepository().Delete<SchoolGradeClassEntity>(idArr);
        }

        public async Task DeleteTransForm(string ids, Repository db)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if (ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");

            // 使用参数化查询和跨数据库兼容的 IN 语法
            var parameters = new List<DbParameter>
            {
                DbParameterExtension.CreateDbParameter("@BaseIsDelete", 1)
            };

            string strSql = $"UPDATE up_SchoolGradeClass SET BaseIsDelete = @BaseIsDelete WHERE Id IN ({ids})";
            await db.ExecuteBySql(strSql, parameters.ToArray());
        }

        public async Task SetStatuzTransForm(string ids, int statuz, Repository db)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if (ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");

            // 使用参数化查询和跨数据库兼容的 IN 语法
            var parameters = new List<DbParameter>
            {
                DbParameterExtension.CreateDbParameter("@Statuz", statuz)
            };

            string strSql = $"UPDATE up_SchoolGradeClass SET Statuz = @Statuz WHERE Id IN ({ids})";
            await db.ExecuteBySql(strSql, parameters.ToArray());
        }

        /// <summary>
        /// 验证。实验室登记
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public async Task<long> VerifyFunRoomUseForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if (ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");

            long num = 0;
            string strSql = $@" SELECT COUNT(sg.Id) AS Num FROM  up_SchoolGradeClass AS sg
                            INNER JOIN  bn_FunRoomUse AS fr2 ON sg.Id = fr2.UseClass
                            WHERE sg.Id IN ({ids})";

            var list = await this.BaseRepository().FindList<BaseStatisticsEntity>(strSql);
            if (list != null && list.Count() > 0)
            {
                num = list.FirstOrDefault().Num;
            }
            return num;
        }

        /// <summary>
        /// 验证实验教学，预约信息。
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public async Task<long> VerifyExperimentBookingForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if (ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");

            long num = 0;
            string strSql = $@" SELECT COUNT(sg.Id) AS NUM FROM  up_SchoolGradeClass AS sg
                            INNER JOIN  ex_ExperimentBooking AS fr2 ON sg.Id = fr2.SchoolGradeClassId
                            WHERE sg.Id IN ({ids})";
            var list = await this.BaseRepository().FindList<BaseStatisticsEntity>(strSql);
            if (list != null && list.Count() > 0)
            {
                num = list.FirstOrDefault().Num;
            }
            return num;
        }

        #endregion

        #region 私有方法
        private Expression<Func<SchoolGradeClassEntity, bool>> ListFilter(SchoolGradeClassListParam param)
        {
            var expression = LinqExtensions.True<SchoolGradeClassEntity>();
            expression = expression.And(m => m.BaseIsDelete == IsEnum.No.ParseToInt());
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (!param.UnitId.IsNullOrZero())
                {
                    expression = expression.And(m => m.UnitId == param.UnitId);
                }
                if (!param.SchoolStage.IsNullOrZero())
                {
                    expression = expression.And(m => m.SchoolStage == param.SchoolStage.ParseToInt());
                }
                if (!param.GradeId.IsNullOrZero())
                {
                    expression = expression.And(m => m.GradeId == param.GradeId);
                }
                if (!param.StartYear.IsNullOrZero())
                {
                    expression = expression.And(m => m.StartYear == param.StartYear);
                }
                if (param.IsGraduate > -1)
                {
                    expression = expression.And(m => m.IsGraduate == param.IsGraduate);
                }
                if (param.StartYear > 0)
                {
                    expression = expression.And(m => m.StartYear == param.StartYear);
                }
                if (param.SubjectId > 0)
                {
                    if (param.CompulsoryType == ClassCompulsoryTypeEnum.Select.ParseToInt())
                    {
                        expression = expression.And(m => m.SelectSubject.Contains(param.SubjectId.ToString()));
                    }
                    else
                    {
                        expression = expression.And(m => m.SelectSubject == null || m.SelectSubject == "" || !m.SelectSubject.Contains(param.SubjectId.ToString()));
                    }
                }
                if (param.Statuz != -10000)
                {
                    expression = expression.And(m => m.Statuz == param.Statuz);
                }
            }
            return expression;
        }

        private List<DbParameter> ListFilter(SchoolGradeClassListParam param, StringBuilder strSql)
        {
            strSql.Append(@"  SELECT * From (
                             SELECT a1.* ,c3.DicName as GradeName ,d4.DicName AS SchoolStageName,
                            u5.Name AS UnitName,u5.Code AS UnitCode ,
                            c3.DicName + '（'+ a1.ClassDesc + '）' AS ClassName
                            FROM  up_SchoolGradeClass AS a1
                            INNER JOIN  up_Unit AS u5 on a1.UnitId = u5.Id
                            INNER JOIN  sys_static_dictionary AS c3 ON a1.GradeId = c3.DictionaryId
                            INNER JOIN  sys_static_dictionary AS d4 ON a1.SchoolStage = d4.DictionaryId
                          ) as tb1 WHERE   1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = 0 ");

                if (!param.UnitId.IsNullOrZero())
                {
                    strSql.Append(" AND UnitId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                if (!param.SchoolStage.IsEmpty() && param.SchoolStage != "-1")
                {
                    strSql.Append($" AND SchoolStage IN ({param.SchoolStage}) ");
                }
                if (param.Ids != null && param.Ids.Length > 0)
                {
                    strSql.Append($" AND Id IN ({param.Ids}) ");
                }
                if (param.GradeId > 0)
                {
                    strSql.Append(" AND GradeId = @GradeId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@GradeId", param.GradeId));
                }
                if (param.IsGraduate >-1)
                {
                    strSql.Append(" AND IsGraduate = @IsGraduate ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@IsGraduate", param.IsGraduate));
                }
                if (!string.IsNullOrEmpty(param.Key))
                {
                    strSql.Append(" AND ClassDesc like @ClassDesc ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ClassDesc", $"%{param.Key}%"));
                }
                if (param.SubjectId > 0)
                {
                    strSql.Append(" AND SelectSubject like @SubjectId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SubjectId", $"%{param.SubjectId}%"));
                }
                if (param.Statuz != -10000)
                {
                    strSql.Append(" AND Statuz = @Statuz ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Statuz", param.Statuz));
                }
            }
            return parameter;
        }

        private List<DbParameter> ClassListFilter(SchoolGradeClassListParam param, StringBuilder strSql)
        {
            strSql.Append(@"  SELECT * From (
                             SELECT a1.* ,c3.DicName as GradeName ,d4.DicName AS SchoolStageName,
                            u5.Name AS UnitName,u5.Code AS UnitCode ,
                            '（'+a1.ClassDesc+'）' AS ClassName
                            FROM  up_SchoolGradeClass AS a1
                            INNER JOIN  up_Unit AS u5 on a1.UnitId = u5.Id
                            INNER JOIN  sys_static_dictionary AS c3 ON a1.GradeId = c3.DictionaryId
                            INNER JOIN  sys_static_dictionary AS d4 ON a1.SchoolStage = d4.DictionaryId
                          ) as tb1 WHERE   1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = 0 ");

                if (!param.UnitId.IsNullOrZero())
                {
                    strSql.Append(" AND UnitId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                if (!param.SchoolStage.IsEmpty() && param.SchoolStage != "-1")
                {
                    strSql.Append($" AND SchoolStage IN ({param.SchoolStage}) ");
                }
                if (param.Ids != null && param.Ids.Length > 0)
                {
                    strSql.Append($" AND Id IN ({param.Ids}) ");
                }
                if (param.GradeId > 0)
                {
                    strSql.Append(" AND GradeId = @GradeId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@GradeId", param.GradeId));
                }
                if (param.IsGraduate > -1)
                {
                    strSql.Append(" AND IsGraduate = @IsGraduate ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@IsGraduate", param.IsGraduate));
                }
                if (param.Statuz != -10000)
                {
                    strSql.Append(" AND Statuz = @Statuz ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Statuz", param.Statuz));
                }
            }
            return parameter;
        }

        private List<DbParameter> SummaryFilter(SchoolGradeClassListParam param, StringBuilder strSql)
        {
            strSql.Append(@"  SELECT Sum(StudentNum) as StudentNum ,Count(Id) AS ClassDesc From (  SELECT * From (
                             SELECT a1.* ,c3.DicName as GradeName ,d4.DicName AS SchoolStageName,
                            u5.Name AS UnitName,u5.Code AS UnitCode ,
                            c3.DicName + '（'+ a1.ClassDesc + '）' AS ClassName
                            FROM  up_SchoolGradeClass AS a1
                            INNER JOIN  up_Unit AS u5 on a1.UnitId = u5.Id
                            INNER JOIN  sys_static_dictionary AS c3 ON a1.GradeId = c3.DictionaryId
                            INNER JOIN  sys_static_dictionary AS d4 ON a1.SchoolStage = d4.DictionaryId
                          ) as tb1 WHERE   1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = 0 ");

                if (!param.UnitId.IsNullOrZero())
                {
                    strSql.Append(" AND UnitId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                if (!param.SchoolStage.IsEmpty() && param.SchoolStage != "-1")
                {
                    strSql.Append($" AND SchoolStage IN ({param.SchoolStage}) ");
                }
                if (param.Ids != null && param.Ids.Length > 0)
                {
                    strSql.Append($" AND Id IN ({param.Ids}) ");
                }
                if (param.GradeId > 0)
                {
                    strSql.Append(" AND GradeId = @GradeId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@GradeId", param.GradeId));
                }
                if (param.IsGraduate > -1)
                {
                    strSql.Append(" AND IsGraduate = @IsGraduate ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@IsGraduate", param.IsGraduate));
                }
            }

            strSql.Append(" ) as tb2 ");
            return parameter;
        }

        /// <summary>
        /// 获取班级信息
        /// </summary>
        /// <param name="param"></param>
        /// <param name="strSql"></param>
        /// <returns></returns>
        private List<DbParameter> ListGradeClassFilter(SchoolGradeClassListParam param, StringBuilder strSql)
        {
            string sqlUserClassInfo = "";
            if (!string.IsNullOrEmpty(param.GradeClassIds))
            {
                sqlUserClassInfo = $" AND gc2.Id in ({param.GradeClassIds})";
            }
            strSql.Append($@"  SELECT * From (
                SELECT DISTINCT gc2.Id ,gc2.SchoolStage ,gc2.GradeId ,gc2.ClassId ,gc2.ClassDesc ,dic3.DicName AS GradeName ,gc2.UnitId , dic3.DicName + '('+ gc2.ClassDesc + ')' AS ClassName
                FROM  up_SchoolGradeClass AS gc2
                INNER JOIN  sys_static_dictionary AS dic3 ON gc2.GradeId = dic3.DictionaryId AND dic3.TypeCode = '{DicTypeCodeEnum.Grade.ParseToInt()}' AND dic3.BaseIsDelete = 0

                WHERE gc2.BaseIsDelete = 0 AND gc2.IsGraduate = 0 AND gc2.Statuz = {StatusEnum.Yes.ParseToInt()}  {sqlUserClassInfo}
            ) as tb1 WHERE   1 = 1 ");

            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    strSql.Append(" AND UnitId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                else if (param.UnitType == UnitTypeEnum.System.ParseToInt())
                {
                    //超级管理员查询所有的。
                }
                if (param.StudyPhase > 0)
                {
                    strSql.Append(" AND SchoolStage = @StudyPhase ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@StudyPhase", param.StudyPhase));
                }
                if (!string.IsNullOrEmpty(param.SchoolStage))
                {
                    strSql.Append($" AND SchoolStage IN ({param.SchoolStage}) ");
                }
            }
            return parameter;
        }

        /// <summary>
        /// 获取学科信息信息
        /// </summary>
        /// <param name="param"></param>
        /// <param name="strSql"></param>
        /// <returns></returns>
        private List<DbParameter> ListCourseFilter(SchoolGradeClassListParam param, StringBuilder strSql)
        {
            string sqlSubjectInfo = "";
            if (!string.IsNullOrEmpty(param.SubjectIds))
            {
                sqlSubjectInfo = $" AND dic1.DictionaryId in ({param.SubjectIds})";

            }
            string selectCol = "";
            string sqlStudyPhase = "";
            if (param.StudyPhase > 0 || !string.IsNullOrEmpty(param.SchoolStage))
            {
                selectCol = "  ,dic3.DictionaryId AS SchoolStage ";
                sqlStudyPhase = $" INNER JOIN  sys_static_dictionary AS dic3 ON dic3.TypeCode = '{DicTypeCodeEnum.SchoolStage.ParseToInt()}' AND dicr2.DictionaryToId = dic3.DictionaryId AND dic3.BaseIsDelete = 0 ";

            }
            strSql.Append($@"  SELECT * From (
                SELECT DISTINCT dic1.DictionaryId ,dic1.DicName ,ISNULL(dic1.Nature,0) AS Nature {selectCol}
                FROM  sys_static_dictionary AS dic1
                INNER JOIN  sys_dictionary_relation AS dicr2 ON dic1.DictionaryId = dicr2.DictionaryId AND dic1.BaseIsDelete = 0

                {sqlStudyPhase}
                WHERE dic1.TypeCode = '{DicTypeCodeEnum.Course.ParseToInt()}'  {sqlSubjectInfo}
                ) as tb1 WHERE   1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.Id > 0)
                {
                    strSql.Append(" AND Id = @Id ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Id", param.Id));
                }
                if (param.StudyPhase > 0)
                {
                    strSql.Append(" AND SchoolStage = @SchoolStage ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStage", param.StudyPhase));
                }
                if (!string.IsNullOrEmpty(param.SchoolStage))
                {
                    strSql.Append($" AND SchoolStage IN ({param.SchoolStage}) ");
                }
                if (param.Nature >= 0)
                {
                    strSql.Append(" AND Nature = @Nature ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Nature", param.Nature));
                }
            }
            return parameter;
        }

        /// <summary>
        /// 统计班级数量
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<int> GetStatisticsClassNum(SchoolGradeClassListParam param)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(@$"  SELECT Id From (
                SELECT a1.Id ,a1.SchoolStage ,c3.DicName as GradeName ,d4.DicName AS SchoolStageName
                ,c3.DicName + '（'+ a1.ClassDesc + '）' AS ClassName
			    ,a1.UnitId
				,UR.UnitId AS CountyId
				,ur2.UnitId AS CityId
                FROM  up_SchoolGradeClass AS a1
                --INNER JOIN  up_Unit AS u5 on a1.UnitId = u5.Id
			    INNER JOIN  up_UnitRelation AS UR ON a1.UnitId = UR.ExtensionObjId AND UR.ExtensionType = 3 AND UR.BaseIsDelete = 0
				INNER JOIN  up_UnitRelation AS ur2 ON UR.UnitId = ur2.ExtensionObjId AND ur2.ExtensionType = 3 AND ur2.BaseIsDelete = 0
                INNER JOIN  sys_static_dictionary AS c3 ON a1.GradeId = c3.DictionaryId
                INNER JOIN  sys_static_dictionary AS d4 ON a1.SchoolStage = d4.DictionaryId
                WHERE a1.BaseIsDelete = 0 AND a1.IsGraduate = 0
                ) as tb1 WHERE 1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    strSql.Append(" AND UnitId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                else if (param.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    strSql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
                else if (param.UnitType == UnitTypeEnum.City.ParseToInt())
                {
                    strSql.Append(" AND CityId = @CityId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CityId", param.CityId));
                }

                if (string.IsNullOrEmpty(param.SchoolStage))
                {
                    var tempSchoolStage = 0;
                    int.TryParse(param.SchoolStage, out tempSchoolStage);
                    strSql.Append(" AND SchoolStage = @SchoolStage ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStage", tempSchoolStage));
                }
            }
            var list = await this.BaseRepository().FindList<SchoolGradeClassEntity>(strSql.ToString(), parameter.ToArray());
            return list == null ? 0 : list.Count();
        }


        /// <summary>
        /// 统计班级
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<IEnumerable<SchoolGradeClassEntity>> GetStatistics(SchoolGradeClassListParam param)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(@$"  SELECT * From (
                SELECT a1.*
                ,c3.DicName + '（'+ a1.ClassDesc + '）' AS ClassName
                ,se11.SchoolProp
				,UR.UnitId AS CountyId
				,ur2.UnitId AS CityId
                FROM  up_SchoolGradeClass AS a1
                --INNER JOIN  up_Unit AS u5 on a1.UnitId = u5.Id
			    INNER JOIN  up_UnitRelation AS UR ON a1.UnitId = UR.ExtensionObjId AND UR.ExtensionType = 3 AND UR.BaseIsDelete = 0
				INNER JOIN  up_UnitRelation AS ur2 ON UR.UnitId = ur2.ExtensionObjId AND ur2.ExtensionType = 3 AND ur2.BaseIsDelete = 0
                INNER JOIN  sys_static_dictionary AS c3 ON a1.GradeId = c3.DictionaryId
                INNER JOIN  sys_static_dictionary AS d4 ON a1.SchoolStage = d4.DictionaryId
                INNER JOIN up_SchoolExtension AS se11 ON se11.BaseIsDelete = 0 AND se11.UnitId = a1.UnitId
                WHERE a1.BaseIsDelete = 0 AND a1.IsGraduate = 0  AND a1.Statuz = 1
                ) as tb1 WHERE 1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    strSql.Append(" AND UnitId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                else if (param.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    strSql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
                else if (param.UnitType == UnitTypeEnum.City.ParseToInt())
                {
                    strSql.Append(" AND CityId = @CityId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CityId", param.CityId));
                }

                if (!string.IsNullOrEmpty(param.SchoolStage))
                {
                    var tempSchoolStage = 0;
                    int.TryParse(param.SchoolStage, out tempSchoolStage);
                    if (tempSchoolStage > 0)
                    {
                        strSql.Append(" AND SchoolStage = @SchoolStage ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStage", tempSchoolStage));
                    }
                }
                if (param.SchoolProple != -10000)
                {
                    strSql.Append(" AND SchoolProp <> @SchoolProple ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolProple", param.SchoolProple));
                }

            }
            return await this.BaseRepository().FindList<SchoolGradeClassEntity>(strSql.ToString(), parameter.ToArray());
        }

        /// <summary>
        /// 统计班级
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<IEnumerable<SchoolGradeClassEntity>> GetHistoryStatistics(SchoolGradeClassListParam param)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(@$"  SELECT * From (
                SELECT a1.SchoolGradeClassId as Id ,
                    a1.SchoolYearStart ,
                    a1.SchoolYearEnd ,
                    a1.UnitId ,
                    a1.StartYear ,
                    a1.SchoolStage ,
                    a1.GradeId ,
                    a1.ClassId ,
                    a1.ClassDesc ,
                    a1.StudentNum ,
                    a1.Memo ,
                    a1.IsGraduate ,
                    a1.SelectSubject ,
                c3.DicName + '（'+ a1.ClassDesc + '）' AS ClassName ,
                se11.SchoolProp ,
				UR.UnitId AS CountyId ,
				ur2.UnitId AS CityId
                FROM  up_SchoolGradeClassHistory AS a1
			    INNER JOIN  up_UnitRelation AS UR ON a1.UnitId = UR.ExtensionObjId AND UR.ExtensionType = 3 AND UR.BaseIsDelete = 0
				INNER JOIN  up_UnitRelation AS ur2 ON UR.UnitId = ur2.ExtensionObjId AND ur2.ExtensionType = 3 AND ur2.BaseIsDelete = 0
                INNER JOIN  sys_static_dictionary AS c3 ON a1.GradeId = c3.DictionaryId
                INNER JOIN  sys_static_dictionary AS d4 ON a1.SchoolStage = d4.DictionaryId
                INNER JOIN up_SchoolExtension AS se11 ON se11.BaseIsDelete = 0 AND se11.UnitId = a1.UnitId
                WHERE a1.BaseIsDelete = 0  AND  a1.Statuz = 1   AND  a1.IsGraduate = 0
                ) as tb1 WHERE 1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    strSql.Append(" AND UnitId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                else if (param.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    strSql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
                else if (param.UnitType == UnitTypeEnum.City.ParseToInt())
                {
                    strSql.Append(" AND CityId = @CityId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CityId", param.CityId));
                }
                if (param.SchoolYear>0)
                {
                    strSql.Append(" AND SchoolYearStart = @SchoolYear ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolYear", param.SchoolYear));
                }
                if (!string.IsNullOrEmpty(param.SchoolStage))
                {
                    var tempSchoolStage = 0;
                    int.TryParse(param.SchoolStage, out tempSchoolStage);
                    if (tempSchoolStage > 0)
                    {
                        strSql.Append(" AND SchoolStage = @SchoolStage ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStage", tempSchoolStage));
                    }
                }
                if (param.SchoolProple != -10000)
                {
                    strSql.Append(" AND SchoolProp <> @SchoolProple ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolProple", param.SchoolProple));
                }

            }
            return await this.BaseRepository().FindList<SchoolGradeClassEntity>(strSql.ToString(), parameter.ToArray());
        }


        /// <summary>
        /// 统计班级
        /// 1：主要是根据学年统计，统计哪一年的（）
        /// 注：不用加是否毕业，只要是存在的没有删除的，都会统计，
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<IEnumerable<SchoolGradeClassEntity>> GetStatisticsByYear(SchoolGradeClassListParam param)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(@$"  SELECT * From (
                SELECT a1.*
                 ,c3.DicName + '（'+ a1.ClassDesc + '）' AS ClassName
                 ,se11.SchoolProp
                 ,UR.UnitId AS CountyId
                 ,ur2.UnitId AS CityId
                 ,(a1.StartYear  + c3.Nature -1	)  AS SchoolYear
                 ,c3.DictionaryId AS  StaticGradeId
                 FROM  up_SchoolGradeClass AS a1
                 INNER JOIN  up_UnitRelation AS UR ON a1.UnitId = UR.ExtensionObjId AND UR.ExtensionType = 3 AND UR.BaseIsDelete = 0
                 INNER JOIN  up_UnitRelation AS ur2 ON UR.UnitId = ur2.ExtensionObjId AND ur2.ExtensionType = 3 AND ur2.BaseIsDelete = 0
                 INNER JOIN up_SchoolExtension AS se11 ON se11.BaseIsDelete = 0 AND se11.UnitId = a1.UnitId
                 INNER JOIN sys_dictionary_relation AS dr5 ON dr5.DictionaryId = a1.SchoolStage
                 INNER JOIN  sys_static_dictionary AS c3 ON dr5.DictionaryToId = c3.DictionaryId
                WHERE a1.BaseIsDelete = 0
                ) as tb1 WHERE 1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    strSql.Append(" AND UnitId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                else if (param.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    strSql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
                else if (param.UnitType == UnitTypeEnum.City.ParseToInt())
                {
                    strSql.Append(" AND CityId = @CityId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CityId", param.CityId));
                }

                //if (!string.IsNullOrEmpty(param.SchoolStage))
                //{
                //    var tempSchoolStage = 0;
                //    int.TryParse(param.SchoolStage, out tempSchoolStage);
                //    if (tempSchoolStage > 0)
                //    {
                //        strSql.Append(" AND SchoolStage = @SchoolStage ");
                //        parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStage", tempSchoolStage));
                //    }
                //}
                //if (param.SchoolProple != -10000)
                //{
                //    strSql.Append(" AND SchoolProp <> @SchoolProple ");
                //    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolProple", param.SchoolProple));
                //}
                if (param.SchoolYear != -10000)
                {
                    strSql.Append(" AND SchoolYear = @SchoolYear ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolYear", param.SchoolYear));
                }

            }
            return await this.BaseRepository().FindList<SchoolGradeClassEntity>(strSql.ToString(), parameter.ToArray());
        }
        #endregion
    }
}
