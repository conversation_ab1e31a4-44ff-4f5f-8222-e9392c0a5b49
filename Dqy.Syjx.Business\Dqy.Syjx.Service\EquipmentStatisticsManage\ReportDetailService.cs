﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.EquipmentStatisticsManage;
using Dqy.Syjx.Model.Param.EquipmentStatisticsManage;
using Dqy.Syjx.Model.Param.OrganizationManage;
using Dqy.Syjx.Entity.OrganizationManage;
using Dqy.Syjx.Model.Param.InstrumentManage;
using System.Data;

namespace Dqy.Syjx.Service.EquipmentStatisticsManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2024-01-29 10:52
    /// 描 述：学校上报详情服务类
    /// </summary>
    public class ReportDetailService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<ReportDetailEntity>> GetList(ReportDetailListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<ReportDetailEntity>> GetAllList(ReportEquipmentParam param)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ListFilter(param, strSql);
            var list = await this.BaseRepository().FindList<ReportDetailEntity>(strSql.ToString(), filter.ToArray());
            return list.ToList();
        }

        public async Task<List<ReportDetailEntity>> GetPageList(ReportDetailListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<ReportDetailEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<ReportDetailEntity>(id);
        }

        public async Task<List<ReportDetailEntity>> GetPageList(ReportEquipmentParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ListFilter(param, strSql);
            var list = await this.BaseRepository().FindList<ReportDetailEntity>(strSql.ToString(), filter.ToArray(), pagination);
            return list.ToList();
        }

        public async Task<List<ReportDetailEntity>> GetAllUnitPageList(ReportEquipmentParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ListAllUnitFilter(param, strSql);
            var list = await this.BaseRepository().FindList<ReportDetailEntity>(strSql.ToString(), filter.ToArray(), pagination);
            return list.ToList();
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(ReportDetailEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(ReportDetailEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }
        public async Task UpdateTransForm(ReportDetailEntity entity, List<string> fields, Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                if (fields != null && fields.Count > 0)
                {
                    fields.Add("BaseModifierId");
                    fields.Add("BaseModifyTime");
                    fields.Add("BaseVersion");
                }
                if (fields != null && fields.Count > 0)
                {
                    await db.Update(entity, fields);
                }
                else
                {
                    //不存在更细所有
                    await db.Update(entity);
                }
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
               strSql = $" update zb_ReportDetail set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
               strSql = $"update zb_ReportDetail set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法

        /// <summary>
        ///
        /// </summary>
        /// <param name="param"></param>
        /// <param name="strSql"></param>
        /// <returns></returns>
        private List<DbParameter> ListFilter(ReportEquipmentParam param, StringBuilder strSql, string funSql = "*")
        {
            strSql.Append($@"  SELECT {funSql} From (
                               SELECT RD.Id,U.Sort,U.Id AS SchoolId,U.Code AS SchoolCode,U.Name AS SchoolName,RD.TotalAmount,RD.CurrentYearAmount
                               
                               ,RD.MediaDeviceNum,RD.MediaFunroomNum,RD.MediaInteractiveNum,
	                           RD.MedialaboratoryNum,RD.MediaOtherNum,RD.ComputerStudentNum,RD.ComputerTeacherNum,
	                           RD.ComputerPadNum,RD.ComputerPortableNum,RD.NetworkAdminNum,RD.NetworkFulltimeNum,
	                           RD.BookTotalNum,RD.BookNewYearNum,RD.BookCabinetNum,RD.BookPavilionNum,RD.BookLibraryArea,
	                           RD.BookRoomSeatNum,RD.SportRunwayLength,RD.SportRoomNum,RD.SportPingPongNum,RD.SportBasketballNum,
	                           RD.SportVolleyballNum,RD.SportFootballNum,RD.SysNetworkWired,RD.SysNetworkWireless,RD.SysCampusBroadcasting,
	                           RD.SysMediaPublish,RD.SysSecurityMonitor,RD.SysCampusVisitor,RD.SysOneCard,RD.SysLoT,RD.SysCampusTV,RD.SysStandardizedRoom,
	                           RD.SysNormalizedBroadcast,RD.BookAdminNum,RD.BookFulltimeNum ,
                               RD.MediaInteractiveLabNum , RD.MediaInteractiveOtherNum , RD.BookReadersTerminal , RD.SysElectronicClassCard , RD.SysOutdoorsLEDBig
                               ,SE20.ClassNum ,SE20.StudentNum ,SE20.TeacherNum ,SE20.SchoolProp
                                ,UR.UnitId AS CountyId,SE20.TracksNumStatic as SchoolRail
                        FROM zb_ReportDetail AS RD
                        INNER JOIN zb_Report AS R ON RD.ReportId = R.Id AND R.BaseIsDelete = 0
                        INNER JOIN up_Unit AS U ON R.SchoolId = U.Id AND U.BaseIsDelete = 0
                        INNER JOIN up_SchoolExtension AS SE20 ON SE20.BaseIsDelete = 0  AND U.Id = SE20.UnitId
                        INNER JOIN  up_UnitRelation AS UR ON R.SchoolId = UR.ExtensionObjId AND UR.ExtensionType = 3 AND UR.BaseIsDelete = 0
                        WHERE R.IsReport = 1 AND R.IsCurrent = 1 AND RD.BaseIsDelete = 0
                        ) as T WHERE 1 = 1 ");

            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.SchoolId > 0)
                {
                    strSql.Append(" AND SchoolId = @SchoolId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
                }
                if (param.CountyId > 0)
                {
                    strSql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
            }
            return parameter;
        }

        private List<DbParameter> ListAllUnitFilter(ReportEquipmentParam param, StringBuilder strSql, string funSql = "*")
        {
            strSql.Append($@"  SELECT {funSql} From (
                              SELECT RD.Id,U.Sort,U.Id AS SchoolId,U.Code AS SchoolCode,U.Name AS SchoolName,RD.TotalAmount,RD.CurrentYearAmount
                                  
                                   ,RD.MediaDeviceNum,RD.MediaFunroomNum,RD.MediaInteractiveNum,
                                   RD.MedialaboratoryNum,RD.MediaOtherNum,RD.ComputerStudentNum,RD.ComputerTeacherNum,
                                   RD.ComputerPadNum,RD.ComputerPortableNum,RD.NetworkAdminNum,RD.NetworkFulltimeNum,
                                   RD.BookTotalNum,RD.BookNewYearNum,RD.BookCabinetNum,RD.BookPavilionNum,RD.BookLibraryArea,
                                   RD.BookRoomSeatNum,RD.SportRunwayLength,RD.SportRoomNum,RD.SportPingPongNum,RD.SportBasketballNum,
                                   RD.SportVolleyballNum,RD.SportFootballNum,RD.SysNetworkWired,RD.SysNetworkWireless,RD.SysCampusBroadcasting,
                                   RD.SysMediaPublish,RD.SysSecurityMonitor,RD.SysCampusVisitor,RD.SysOneCard,RD.SysLoT,RD.SysCampusTV,RD.SysStandardizedRoom,
                                   RD.SysNormalizedBroadcast,RD.BookAdminNum,RD.BookFulltimeNum ,
                                   RD.MediaInteractiveLabNum , RD.MediaInteractiveOtherNum , RD.BookReadersTerminal , RD.SysElectronicClassCard , RD.SysOutdoorsLEDBig
                                   ,SE20.ClassNum ,SE20.StudentNum ,SE20.TeacherNum ,SE20.SchoolProp
                                    ,UR.UnitId AS CountyId,SE20.TracksNumStatic as SchoolRail
                            FROM up_Unit AS U
                            INNER JOIN up_SchoolExtension AS SE20 ON SE20.BaseIsDelete = 0  AND U.Id = SE20.UnitId
                            INNER JOIN  up_UnitRelation AS UR ON U.Id = UR.ExtensionObjId AND UR.ExtensionType = 3 AND UR.BaseIsDelete = 0
                            LEFT JOIN zb_Report AS R ON R.BaseIsDelete = 0 AND U.Id = R.SchoolId AND R.IsReport = 1 AND R.IsCurrent = 1
                            LEFT JOIN zb_ReportDetail AS RD ON RD.BaseIsDelete = 0 AND R.Id = RD.ReportId
                            WHERE U.BaseIsDelete = 0
                        ) as T WHERE 1 = 1 ");

            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.SchoolId > 0)
                {
                    strSql.Append(" AND SchoolId = @SchoolId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
                }
                if (param.CountyId > 0)
                {
                    strSql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
            }
            return parameter;
        }
        /// <summary>
        ///
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<ReportDetailEntity> GetAllUnitFundsTotalSum(ReportEquipmentParam param)
        {
            StringBuilder sql = new StringBuilder();
            var expression = ListAllUnitFilter(param, sql, " '<b>总计：<b>' AS SchoolName,ISNULL(SUM(ClassNum) ,0) AS ClassNum,ISNULL(SUM(StudentNum) ,0) AS StudentNum,ISNULL(SUM(TeacherNum) ,0) AS TeacherNum,ISNULL(SUM(TotalAmount) ,0) AS TotalAmount,ISNULL(SUM(CurrentYearAmount) ,0) AS CurrentYearAmount ");
            var list = await this.BaseRepository().FindList<ReportDetailEntity>(sql.ToString(), expression.ToArray());
            return list.FirstOrDefault();
        }
        /// <summary>
        ///
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<ReportDetailEntity> GetFundsTotalSum(ReportEquipmentParam param)
        {
            StringBuilder sql = new StringBuilder();
            var expression = ListFilter(param, sql, " '<b>总计：<b>' AS SchoolName,ISNULL(SUM(ClassNum) ,0) AS ClassNum,ISNULL(SUM(StudentNum) ,0) AS StudentNum,ISNULL(SUM(TeacherNum) ,0) AS TeacherNum,ISNULL(SUM(TotalAmount) ,0) AS TotalAmount,ISNULL(SUM(CurrentYearAmount) ,0) AS CurrentYearAmount ");
            var list = await this.BaseRepository().FindList<ReportDetailEntity>(sql.ToString(), expression.ToArray());
            return list.FirstOrDefault();
        }

        /// <summary>
        /// 信息技术
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<ReportDetailEntity> GetInfoTotalSum(ReportEquipmentParam param)
        {
            StringBuilder sql = new StringBuilder();
            var expression = ListFilter(param, sql, @" '<b>总计：<b>' AS SchoolName ,
                                    ISNULL(SUM(MediaDeviceNum), 0) AS MediaDeviceNum ,
                                    ISNULL(SUM(MediaFunroomNum), 0) AS MediaFunroomNum ,
                                    ISNULL(SUM(MediaInteractiveNum), 0) AS MediaInteractiveNum ,
                                    ISNULL(SUM(MedialaboratoryNum), 0) AS MedialaboratoryNum ,
                                    ISNULL(SUM(MediaOtherNum), 0) AS MediaOtherNum ,
                                    ISNULL(SUM(MediaInteractiveLabNum), 0) AS MediaInteractiveLabNum ,
                                    ISNULL(SUM(MediaInteractiveOtherNum), 0) AS MediaInteractiveOtherNum ,
                                    ISNULL(SUM(ComputerStudentNum), 0) AS ComputerStudentNum ,
                                    ISNULL(SUM(ComputerTeacherNum), 0) AS ComputerTeacherNum ,
                                    ISNULL(SUM(ComputerPadNum), 0) AS ComputerPadNum ,
                                    ISNULL(SUM(ComputerPortableNum), 0) AS ComputerPortableNum ,
                                    ISNULL(SUM(SysNetworkWired), 0) AS SysNetworkWired ,
                                    ISNULL(SUM(SysNetworkWireless), 0) AS SysNetworkWireless ,
                                    ISNULL(SUM(SysCampusBroadcasting), 0) AS SysCampusBroadcasting ,
                                    ISNULL(SUM(SysMediaPublish), 0) AS SysMediaPublish ,
                                    ISNULL(SUM(SysSecurityMonitor), 0) AS SysSecurityMonitor ,
                                    ISNULL(SUM(SysCampusVisitor), 0) AS SysCampusVisitor ,
                                    ISNULL(SUM(SysOneCard), 0) AS SysOneCard ,
                                    ISNULL(SUM(SysLoT), 0) AS SysLoT ,
                                    ISNULL(SUM(SysCampusTV), 0) AS SysCampusTV ,
                                    ISNULL(SUM(SysStandardizedRoom), 0) AS SysStandardizedRoom ,
                                    ISNULL(SUM(SysNormalizedBroadcast), 0) AS SysNormalizedBroadcast ,
                                    ISNULL(SUM(SysElectronicClassCard), 0) AS SysElectronicClassCard ,
                                    ISNULL(SUM(SysOutdoorsLEDBig), 0) AS SysOutdoorsLEDBig ,
                                    ISNULL(SUM(NetworkAdminNum), 0) AS NetworkAdminNum ,
                                    ISNULL(SUM(NetworkFulltimeNum), 0) AS NetworkFulltimeNum,
                                    ISNULL(SUM(StudentNum), 0) AS StudentNum ,
                                    ISNULL(SUM(TeacherNum), 0) AS TeacherNum");
            var list = await this.BaseRepository().FindList<ReportDetailEntity>(sql.ToString(), expression.ToArray());
            return list.FirstOrDefault();
        }

        /// <summary>
        /// 信息技术
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<ReportDetailEntity> GetAllUnitInfoTotalSum(ReportEquipmentParam param)
        {
            StringBuilder sql = new StringBuilder();
            var expression = ListAllUnitFilter(param, sql, @" '<b>总计：<b>' AS SchoolName ,
                                    ISNULL(SUM(MediaDeviceNum), 0) AS MediaDeviceNum ,
                                    ISNULL(SUM(MediaFunroomNum), 0) AS MediaFunroomNum ,
                                    ISNULL(SUM(MediaInteractiveNum), 0) AS MediaInteractiveNum ,
                                    ISNULL(SUM(MedialaboratoryNum), 0) AS MedialaboratoryNum ,
                                    ISNULL(SUM(MediaOtherNum), 0) AS MediaOtherNum ,
                                    ISNULL(SUM(MediaInteractiveLabNum), 0) AS MediaInteractiveLabNum ,
                                    ISNULL(SUM(MediaInteractiveOtherNum), 0) AS MediaInteractiveOtherNum ,
                                    ISNULL(SUM(ComputerStudentNum), 0) AS ComputerStudentNum ,
                                    ISNULL(SUM(ComputerTeacherNum), 0) AS ComputerTeacherNum ,
                                    ISNULL(SUM(ComputerPadNum), 0) AS ComputerPadNum ,
                                    ISNULL(SUM(ComputerPortableNum), 0) AS ComputerPortableNum ,
                                    ISNULL(SUM(SysNetworkWired), 0) AS SysNetworkWired ,
                                    ISNULL(SUM(SysNetworkWireless), 0) AS SysNetworkWireless ,
                                    ISNULL(SUM(SysCampusBroadcasting), 0) AS SysCampusBroadcasting ,
                                    ISNULL(SUM(SysMediaPublish), 0) AS SysMediaPublish ,
                                    ISNULL(SUM(SysSecurityMonitor), 0) AS SysSecurityMonitor ,
                                    ISNULL(SUM(SysCampusVisitor), 0) AS SysCampusVisitor ,
                                    ISNULL(SUM(SysOneCard), 0) AS SysOneCard ,
                                    ISNULL(SUM(SysLoT), 0) AS SysLoT ,
                                    ISNULL(SUM(SysCampusTV), 0) AS SysCampusTV ,
                                    ISNULL(SUM(SysStandardizedRoom), 0) AS SysStandardizedRoom ,
                                    ISNULL(SUM(SysNormalizedBroadcast), 0) AS SysNormalizedBroadcast ,
                                    ISNULL(SUM(SysElectronicClassCard), 0) AS SysElectronicClassCard ,
                                    ISNULL(SUM(SysOutdoorsLEDBig), 0) AS SysOutdoorsLEDBig ,
                                    ISNULL(SUM(NetworkAdminNum), 0) AS NetworkAdminNum ,
                                    ISNULL(SUM(NetworkFulltimeNum), 0) AS NetworkFulltimeNum,
                                    ISNULL(SUM(StudentNum), 0) AS StudentNum ,
                                    ISNULL(SUM(TeacherNum), 0) AS TeacherNum");
            var list = await this.BaseRepository().FindList<ReportDetailEntity>(sql.ToString(), expression.ToArray());
            return list.FirstOrDefault();
        }


        /// <summary>
        /// 图书
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<ReportDetailEntity> GetBookTotalSum(ReportEquipmentParam param)
        {
            StringBuilder sql = new StringBuilder();
            var expression = ListFilter(param, sql, @" '<b>总计：<b>' AS SchoolName,ISNULL(SUM(BookTotalNum) ,0) AS BookTotalNum,ISNULL(SUM(BookNewYearNum) ,0) AS BookNewYearNum
                ,ISNULL(SUM(BookCabinetNum) ,0) AS BookCabinetNum,ISNULL(SUM(BookPavilionNum) ,0) AS BookPavilionNum
                ,ISNULL(SUM(BookLibraryArea) ,0) AS BookLibraryArea,ISNULL(SUM(BookRoomSeatNum) ,0) AS BookRoomSeatNum
                ,ISNULL(SUM(BookReadersTerminal) ,0) AS BookReadersTerminal
                ,ISNULL(SUM(BookAdminNum) ,0) AS BookAdminNum,ISNULL(SUM(BookFulltimeNum) ,0) AS BookFulltimeNum");
            var list = await this.BaseRepository().FindList<ReportDetailEntity>(sql.ToString(), expression.ToArray());
            return list.FirstOrDefault();
        }

        /// <summary>
        /// 图书
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<ReportDetailEntity> GetAllUnitBookTotalSum(ReportEquipmentParam param)
        {
            StringBuilder sql = new StringBuilder();
            var expression = ListAllUnitFilter(param, sql, @" '<b>总计：<b>' AS SchoolName,ISNULL(SUM(BookTotalNum) ,0) AS BookTotalNum,ISNULL(SUM(BookNewYearNum) ,0) AS BookNewYearNum
                ,ISNULL(SUM(BookCabinetNum) ,0) AS BookCabinetNum,ISNULL(SUM(BookPavilionNum) ,0) AS BookPavilionNum
                ,ISNULL(SUM(BookLibraryArea) ,0) AS BookLibraryArea,ISNULL(SUM(BookRoomSeatNum) ,0) AS BookRoomSeatNum
                ,ISNULL(SUM(BookReadersTerminal) ,0) AS BookReadersTerminal
                ,ISNULL(SUM(BookAdminNum) ,0) AS BookAdminNum,ISNULL(SUM(BookFulltimeNum) ,0) AS BookFulltimeNum");
            var list = await this.BaseRepository().FindList<ReportDetailEntity>(sql.ToString(), expression.ToArray());
            return list.FirstOrDefault();
        }

        /// <summary>
        /// 体育场所
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<ReportDetailEntity> GetSportTotalSum(ReportEquipmentParam param)
        {
            StringBuilder sql = new StringBuilder();
            var expression = ListFilter(param, sql, @" '<b>总计：<b>' AS SchoolName,ISNULL(SUM(SportRunwayLength) ,0) AS SportRunwayLength,ISNULL(SUM(SportRoomNum) ,0) AS SportRoomNum
                ,ISNULL(SUM(SportPingPongNum) ,0) AS SportPingPongNum,ISNULL(SUM(SportBasketballNum) ,0) AS SportBasketballNum
                ,ISNULL(SUM(SportVolleyballNum) ,0) AS SportVolleyballNum,ISNULL(SUM(SportFootballNum) ,0) AS SportFootballNum");
            var list = await this.BaseRepository().FindList<ReportDetailEntity>(sql.ToString(), expression.ToArray());
            return list.FirstOrDefault();
        }

        /// <summary>
        /// 体育场所
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<ReportDetailEntity> GetAllUnitSportTotalSum(ReportEquipmentParam param)
        {
            StringBuilder sql = new StringBuilder();
            var expression = ListAllUnitFilter(param, sql, @" '<b>总计：<b>' AS SchoolName,ISNULL(SUM(SportRunwayLength) ,0) AS SportRunwayLength,ISNULL(SUM(SportRoomNum) ,0) AS SportRoomNum
                ,ISNULL(SUM(SportPingPongNum) ,0) AS SportPingPongNum,ISNULL(SUM(SportBasketballNum) ,0) AS SportBasketballNum
                ,ISNULL(SUM(SportVolleyballNum) ,0) AS SportVolleyballNum,ISNULL(SUM(SportFootballNum) ,0) AS SportFootballNum");
            var list = await this.BaseRepository().FindList<ReportDetailEntity>(sql.ToString(), expression.ToArray());
            return list.FirstOrDefault();
        }

        private Expression<Func<ReportDetailEntity, bool>> ListFilter(ReportDetailListParam param)
        {
            var expression = LinqExtensions.True<ReportDetailEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (param.ReportId > 0)
                {
                    expression = expression.And(t => t.ReportId == param.ReportId);
                }
            }
            return expression;
        }
        #endregion
    }
}
