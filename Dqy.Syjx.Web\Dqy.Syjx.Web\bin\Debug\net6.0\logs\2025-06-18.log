2025-06-18 10:31:08.2773||DEBUG||程序启动 |ContentRootPath:D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web |WebRootPath:D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\wwwroot |IsDevelopment:True |url: |action: 
2025-06-18 10:33:31.3664||INFO||URL1:/InstrumentManage/PurchaseStatistics/PurchasePlanAnalyseIndex |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-18 10:44:32.8406||ERROR||Value cannot be null. (Parameter 'source')
   at System.Linq.ThrowHelper.ThrowArgumentNullException(ExceptionArgument argument)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Dqy.Syjx.Service.InstrumentManage.PurchaseDeclarationService.GetCityPurchasePlanAnalyseList(PurchaseDeclarationListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Service\InstrumentManage\PurchaseDeclarationService.cs:line 662
   at Dqy.Syjx.Business.InstrumentManage.PurchaseDeclarationBLL.GetCityPurchasePlanAnalyseList(PurchaseDeclarationListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Business\InstrumentManage\PurchaseDeclarationBLL.cs:line 391
   at Dqy.Syjx.Web.Areas.InstrumentManage.Controllers.PurchaseStatisticsController.GetCityPurchasePlanAnalyseListJson(PurchaseDeclarationListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\Areas\InstrumentManage\Controllers\PurchaseStatisticsController.cs:line 287
   at lambda_method2086(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
 |url: http://localhost/InstrumentManage/PurchaseStatistics/GetCityPurchasePlanAnalyseListJson|action: GetCityPurchasePlanAnalyseListJson
2025-06-18 10:53:42.5305||ERROR||Value cannot be null. (Parameter 'source')
   at System.Linq.ThrowHelper.ThrowArgumentNullException(ExceptionArgument argument)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Dqy.Syjx.Service.InstrumentManage.PurchaseDeclarationService.GetCityPurchasePlanAnalyseList(PurchaseDeclarationListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Service\InstrumentManage\PurchaseDeclarationService.cs:line 662
   at Dqy.Syjx.Business.InstrumentManage.PurchaseDeclarationBLL.GetCityPurchasePlanAnalyseList(PurchaseDeclarationListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Business\InstrumentManage\PurchaseDeclarationBLL.cs:line 391
   at Dqy.Syjx.Web.Areas.InstrumentManage.Controllers.PurchaseStatisticsController.GetCityPurchasePlanAnalyseListJson(PurchaseDeclarationListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\Areas\InstrumentManage\Controllers\PurchaseStatisticsController.cs:line 287
   at lambda_method2086(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
 |url: http://localhost/InstrumentManage/PurchaseStatistics/GetCityPurchasePlanAnalyseListJson|action: GetCityPurchasePlanAnalyseListJson
2025-06-18 10:57:51.3771||INFO||URL1:/QueryStatisticsManage/Instrument/CountyInstrumentSum |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-18 10:57:56.5898||WARN||耗时的Sql：SELECT COUNT(1) FROM (SELECT M.SchoolId AS Id,M.SchoolId,Sort,SchoolName,CountyId,IFNULL(AddNum,0) AS AddNum,IFNULL(OutNum,0) AS OutNum,(IFNULL(Num,0)-IFNULL(MinusNum,0)) AS Num  FROM 
                            (
	                            SELECT U.Id AS SchoolId,U.Sort,U.Name AS SchoolName,U2.Id AS CountyId,SUM(A.Num) AS AddNum
	                            FROM up_Unit AS U
	                            INNER JOIN  up_UnitRelation AS UR ON U.Id = UR.ExtensionObjId AND UR.ExtensionType = 3
	                            INNER JOIN  up_UnitRelation AS UR2 ON UR.UnitId = UR2.ExtensionObjId AND UR2.ExtensionType = 3
	                            INNER JOIN  up_Unit AS U2 ON UR.UnitId = U2.Id
	                            LEFT JOIN eq_SchoolInstrument AS A ON U.Id = A.SchoolId AND A.BaseIsDelete = 0 AND A.StockNum > 0 AND A.Statuz = 30
                             AND YEAR(A.PurchaseDate) = 2025 GROUP BY U.Id,U.Sort,U.Name,U2.Id ) AS M  LEFT JOIN
                            (
	                            SELECT SchoolId,SUM(OutNum) AS OutNum FROM
	                            (
		                            SELECT A.Id,A.SchoolId,A.OutNum 
		                            FROM eq_InstrumentOutList  AS A
		                            INNER JOIN  eq_SchoolInstrument AS B ON A.SchoolInstrumentId = B.Id
		                            WHERE A.BaseIsDelete = 0 AND A.OutNum > 0   
		                            AND YEAR(A.OutDate) = 2025  UNION 
		                            SELECT A.Id,A.SchoolId,A.ScrapNum AS OutNum
		                            FROM eq_InstrumentScrap AS A
		                            INNER JOIN  eq_SchoolInstrument AS B ON A.SchoolInstrumentId = B.Id
		                            WHERE A.BaseIsDelete = 0
	                            AND YEAR(A.ScrapTime) = 2025  ) AS S
	
	                            GROUP BY S.SchoolId
                            ) AS N ON M.SchoolId = N.SchoolId LEFT JOIN
	                    (
		                    SELECT SchoolId,SUM(Num) AS Num FROM eq_SchoolInstrument
		                    WHERE BaseIsDelete = 0 AND StockNum > 0 AND Statuz = 30 
		                    AND PurchaseDate <= '2025-12-31'  GROUP BY SchoolId
	                    ) AS G ON G.SchoolId = M.SchoolId  LEFT JOIN
	                (
		                SELECT SchoolId,SUM(OutNum) AS MinusNum FROM
		                (
			                SELECT A.Id,A.SchoolId,A.OutNum 
			                FROM eq_InstrumentOutList  AS A
			                INNER JOIN  eq_SchoolInstrument AS B ON A.SchoolInstrumentId = B.Id
			                WHERE A.BaseIsDelete = 0 AND A.OutNum > 0   
			                     AND A.OutDate <= '2025-12-31' UNION 
			                SELECT A.Id,A.SchoolId,A.ScrapNum AS OutNum
			                FROM eq_InstrumentScrap AS A
			                INNER JOIN  eq_SchoolInstrument AS B ON A.SchoolInstrumentId = B.Id
			                WHERE A.BaseIsDelete = 0
		                     AND A.ScrapTime <= '2025-12-31'  ) AS S
	
		                GROUP BY S.SchoolId
	                ) AS F ON M.SchoolId = F.SchoolId   WHERE M.CountyId = 357537171000791042 ) t |url: http://localhost/QueryStatisticsManage/Instrument/GetCountyInstrumentStatisticsJson|action: GetCountyInstrumentStatisticsJson
2025-06-18 10:58:08.0174||INFO||URL1:/QueryStatisticsManage/Instrument/CountyInstrumentStat?SchoolId0&Purchase2025 |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-18 10:58:27.0059||INFO||URL1:/EvaluateManage/InstrumentAttendStatic/InstrumentGroupStaticIndex |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-18 10:58:38.1785||INFO||URL1:/EvaluateManage/InstrumentAttendStatic/StandardResultAnalyseIndex |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-18 10:58:42.6037||INFO||URL1:/InstrumentManage/PurchaseStatistics/PurchaseGroupStaticIndex |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-18 10:58:47.2662||INFO||URL1:/InstrumentManage/PurchaseStatistics/PurchaseStatisticsIndex?purchaseYear2025&schoolId395948030106275840&countyId357537171000791042&stageId1002002&courseId1005002 |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-18 10:58:51.6916||INFO||URL1:/QueryStatisticsManage/Instrument/CountyInstrumentStatistic |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-18 10:59:28.6198||INFO||URL1:/QueryStatisticsManage/ExperimentTeach/PlanExamRateList |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-18 10:59:30.8414||INFO||URL1:/QueryStatisticsManage/ExperimentTeach/PlanExamExpRateCountyList |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-18 10:59:34.1905||INFO||URL1:/QueryStatisticsManage/ExperimentTeach/PlanExamSchoolCourseRateList |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-18 10:59:35.7254||INFO||URL1:/QueryStatisticsManage/ExperimentTeach/PlanExamSchoolRateList |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-18 10:59:39.9059||INFO||URL1:/QueryStatisticsManage/Plan/UnitTotalList |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-18 10:59:41.3659||INFO||URL1:/QueryStatisticsManage/ExperimentTeach/UnitTotalList |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-18 10:59:41.7457||ERROR||Value cannot be null. (Parameter 'source')
   at System.Linq.ThrowHelper.ThrowArgumentNullException(ExceptionArgument argument)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Dqy.Syjx.Service.ExperimentTeachManage.ExperimentBookingService.GetExperimentBookingLog(ExperimentBookingListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Service\ExperimentTeachManage\ExperimentBookingService.cs:line 3486
   at Dqy.Syjx.Business.ExperimentTeachManage.ExperimentBookingBLL.GetUnitTotalList(ExperimentBookingListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Business\ExperimentTeachManage\ExperimentBookingBLL.cs:line 1473
   at Dqy.Syjx.Web.Areas.QueryStatisticsManage.Controllers.ExperimentTeachController.GetUnitTotalJson(ExperimentBookingListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\Areas\QueryStatisticsManage\Controllers\ExperimentTeachController.cs:line 354
   at lambda_method3309(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
 |url: http://localhost/QueryStatisticsManage/ExperimentTeach/GetUnitTotalJson|action: GetUnitTotalJson
2025-06-18 10:59:46.2301||INFO||URL1:/QueryStatisticsManage/ExperimentTeach/ExperimentAttendAnalysis |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
