﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .input-group-addon {
        cursor: pointer;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <div id="TextbookVersionBaseId" col="TextbookVersionBaseId" style="display: inline-block;width:180px;"></div>
                    </li>
                    <li>
                        <div id="ExperimentType" col="ExperimentType" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <div id="IsNeedDo" col="IsNeedDo" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <div id="IsEvaluate" col="IsEvaluate" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <div id="IsExam" col="IsExam" style="display: inline-block;width:120px;"></div>
                    </li>
                    <li>
                        <input id="Name" col="Name" placeholder="实验教材版本、实验名称、章节" type="text" />
                        @* <div id="app">
                        <el-input-number v-model="num" v-on:change="handleChange" :min="1" :max="10" label="描述文字"></el-input-number>
                        </div>*@
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                        <a class="btn btn-default btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group-sm hidden-xs" id="toolbar">
            <a id="btnAdd" class="btn btn-success" onclick="showBatchForm()"><i class="fa fa-plus"></i> 添加实验</a>
            <span style="color:#999;padding-left:20px;">友情提示：下表中的实验只有添加了“周次”才纳入已编制实验计划；下表“实验类型”默认教材定义，可根据需求自行修改。</span>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
     var planinfoid = ys.request("id");
     var planstartyear = ys.request("startyear");
     var planschoolterm = ys.request("schoolterm");
     var plangradeid = ys.request("gradeid");

     $(function () {
         $("#IsNeedDo").ysComboBox({
             data: ys.getJson(@Html.Raw(typeof(IsNeedEnum).EnumToDictionaryString())),
             defaultName: '实验要求'
         });
         $("#ExperimentType").ysComboBox({
             data: ys.getJson(@Html.Raw(typeof(ExperimentTypeEnum).EnumToDictionaryString())),
             defaultName: '实验类型'
         });
         $("#IsEvaluate").ysComboBox({
             data: ys.getJson(@Html.Raw(typeof(IsStatusEnum).EnumToDictionaryString())),
             defaultName: '是否考核'
         });
         $("#IsExam").ysComboBox({
             data: ys.getJson(@Html.Raw(typeof(IsStatusEnum).EnumToDictionaryString())),
             defaultName: '按主管部门要求'
         });

         loadVersionBase();
         initGrid();

         //窗口大小改变的时候出发全屏/还原
         $(window).resize(function () {
             setTimeout(function () {
                 loadExperimentType();//刷新调用
             }, 500);
         });

     });

     function loadVersionBase() {
         ys.ajax({
             url: '@Url.Content("~/ExperimentTeachManage/PlanDetail/GetVersionSelectJson")' + "?planinfoid=" + planinfoid,
             type: 'get',
             success: function (obj) {
                 if (obj.Tag == 1) {
                     var arrData = [{ VersionName: "校本实验", Id: 0 }];
                     if (obj.Data != undefined && obj.Data.length > 0) {
                         arrData = arrData.concat(obj.Data);
                     }
                     $('#TextbookVersionBaseId').ysComboBox({
                         data: arrData,
                         key: 'Id',
                         value: 'VersionName',
                         defaultName: '实验教材版本'
                     });
                     $('#TextbookVersionBaseId').ysComboBox('setValue', -1);
                 }
             }
         });
     }

     function initGrid() {
         var queryUrl = '@Url.Content("~/ExperimentTeachManage/PlanDetail/GetPageListJson")' + '?PlanInfoId=' + planinfoid;
         $('#gridTable').ysTable({
             url: queryUrl,
             showRefresh: false,
             showToggle: false,
             showColumns: false,
             columns: [
                 { field: 'index', title: '序号', width: 60, halign: 'center', valign: 'middle', align: 'center', formatter: function (value, row, index) { return index + 1; } },
                 { checkbox: true, visible: true },
                 { field: 'ExperimentName', title: '实验名称', sortable: true, width: 300, halign: 'center', valign: 'middle',
                     formatter: function (value, row, index) {
                         var html = '--';
                         if (row.Id) {
                             html = '';
                             if (row.IsExam == 1) {
                                 html = Syjx.GetCircleShenRoomHtml();
                             }
                             if (value && value.length > 0) {
                                 html += value;
                             }
                         }
                         return html;
                     }
                 },
                 { field: 'VersionBaseName', title: '实验教材', sortable: true, width: 180, halign: 'center', valign: 'middle' },
                 { field: 'Chapter', title: '章节', sortable: true, width: 80, halign: 'center', valign: 'middle', align: 'center' },
                 {
                     field: 'IsNeedDo',
                     title: '实验要求',
                     sortable: true,
                     width: 100,
                     halign: 'center',
                     valign: 'middle',
                     align: 'center',
                     formatter: function (value, row, index) {
                         var html = '@IsNeedEnum.MustDo.GetDescription()';
                         if (@IsNeedEnum.SelectToDo.ParseToInt() == value) {
                             html = '@IsNeedEnum.SelectToDo.GetDescription()';
                         }
                         return html;
                     }
                 },
                 {
                     field: 'ExperimentType',
                     title: '实验类型',
                     sortable: true,
                     width: 100,
                     halign: 'center',
                     valign: 'middle',
                     align: 'center',
                     formatter: function (value, row, index) {
                         var html = '<div class="ExperimentType" style="width:90px;" id="ExperimentType_' + row.Id + '" index="' + index + '"  plandetailid="' + row.Id + '" experimenttype="' + row.ExperimentType + '" col="ExperimentType_' + row.Id + '"></div>';
                         return html;
                     }
                 },
                 {
                     field: 'IsEvaluate', title: '是否考核', sortable: true, halign: 'center', align: 'center', width: 70,
                     formatter: function (value, row, index) {
                         return value == @IsStatusEnum.Yes.ParseToInt() ? "@IsStatusEnum.Yes.GetDescription()" : "<span style='color:red'>@IsStatusEnum.No.GetDescription()</span>";
                     }
                 },
                 {
                     field: 'WeekNum',
                     title: '周次',
                     sortable: true,
                     width: 280,
                     halign: 'center',
                     valign: 'middle',
                     align: 'center',
                     formatter: function (value, row, index) {
                         var html = '';
                         if (!(parseInt(value)>0)) {
                             value = '';
                         }
                         html += '<div class="input-group" style="width: 320px;display: inline-table;">';
                         html += '<span class="input-group-addon input-group-add" index="' + index + '" style="width: 34px;"> <b>+</b> </span>';
                         html += '<input type="text" class="form-control weeknum" index="' + index + '"  style="width: 60px;" plandetailid="' + row.Id + '" termstart="' + row.FirstWeekDate + '" id="txtWeek_' + index + '" aria-label="请输入周次" value="' + value + '">';
                         html += '<span class="input-group-addon input-group-cut" index="' + index + '" style="width: 34px;"> <b>-</b> </span>';
                         html += '<span class="input-group-addon" style="width: 100%;" id="spanWeekDate_' + index + '">' + getDateStr(value, row.FirstWeekDate) + '</span>';
                         html += '</div>';
                         return html;
                     }
                 },
                 {
                     title: '操作',
                     halign: 'center',
                     valign: 'middle',
                     formatter: function (value, row, index) {
                         var actions = [];
                         actions.push('&nbsp;<a class="btn btn-danger btn-xs" href="#" onclick="deleteForm(\'' + row.Id + '\')"><i class="fa fa-remove"></i>删除</a>&nbsp;');
                         return actions.join('');
                     }
                 }
             ],
             queryParams: function (params) {
                 var pagination = $('#gridTable').ysTable('getPagination', params);
                 var queryString = $('#searchDiv').getWebControls(pagination);
                 return queryString;
             },
             onLoadSuccess: function () {
                 loadExperimentType();
                 loadWeekNumClick();
                 IsLoadingExperimentType = 0;
             }
         });
     }

     function searchGrid() {
         IsLoadingExperimentType = 1;
         $('#gridTable').ysTable('search');
         resetToolbarStatus();
     }

     function resetGrid() {
         $('#TextbookVersionBaseId').ysComboBox('setValue', -1);
         $('#IsNeedDo').ysComboBox('setValue', -1);
         $('#IsEvaluate').ysComboBox('setValue', -1);
         $('#IsExam').ysComboBox('setValue', -1);
         $('#ExperimentType').ysComboBox('setValue', -1);
         $('#Name').val('');
         $('#gridTable').ysTable('search');
     }

     function showWeekNumForm(id, num) {
         ys.openDialog({
             title: '编辑周次',
             content: '@Url.Content("~/ExperimentTeachManage/PlanDetail/SetWeekNumForm")' + '?id=' + id + '&num=' + num,
             width: '768px',
             height: '380px',
             callback: function (index, layero) {
                 var iframeWin = window[layero.find('iframe')[0]['name']];
                 iframeWin.saveForm(index);
             }
         });
     }

     function showBatchForm() {
         ys.openDialog({
             title: '添加实验',
             content: '@Url.Content("~/ExperimentTeachManage/PlanDetail/BatchForm")',
             width: '1000px',
             btn: ['添加', '取消'],
             callback: function (index, layero) {
                 var iframeWin = window[layero.find('iframe')[0]['name']];
                 iframeWin.saveForm(index);
             }
         });
     }

     function deleteForm(id) {
         ys.confirm('确认要删除当前这条数据吗', function () {
             ys.ajax({
                 url: '@Url.Content("~/ExperimentTeachManage/PlanDetail/DeleteFormJson")' + '?ids=' + id,
                 type: 'post',
                 success: function (obj) {
                     if (obj.Tag == 1) {
                         layer.msg("删除成功。", { icon: 1, time: 3000, area: ['400px'], shade: false, offset: [15,] });
                         searchGrid();
                     } else {
                         layer.msg(obj.Message, { icon: 2, time: 3000, area: ['400px'], shade: false, offset: [15,] });
                     }
                 }
             });
         });
     }

     var IsLoadingExperimentType = 1;

     function loadExperimentType() {
         $('.ExperimentType').each(function () {
             var experimenttype = $(this).attr('experimenttype');
             $(this).ysComboBox({
                 data: ys.getJson(@Html.Raw(typeof(ExperimentTypeEnum).EnumToDictionaryString())),
                 class: 'form-control',
                 onChange: function () {
                     var currentExperimentType = $(this).parent().attr("experimenttype");
                     var newExperimentType = $(this).val();
                     if (IsLoadingExperimentType != 1 && parseInt(currentExperimentType) != parseInt(newExperimentType)) {
                         var plandetailid = $(this).parent().attr("plandetailid");
                         var postData = { id: plandetailid, experimenttype: newExperimentType };
                         $.ajax({
                             url: '@Url.Content("~/ExperimentTeachManage/PlanDetail/SaveExperimentTypeFormJson")',
                             type: 'post',
                             data: postData,
                             dataType: 'json',
                             success: function (obj) {
                                 if (obj.Tag == 1) {
                                     $("#ExperimentType_"+plandetailid).attr("experimenttype", newExperimentType);
                                     layer.msg(obj.Message, { icon: 1, time: 3000, area: ['400px'], shade: false, offset: [15,] });
                                     //layer.closeAll();
                                 } else {
                                     layer.msg(obj.Message, { icon: 2, time: 3000, area: ['400px'], shade: false, offset: [15,] });
                                 }
                             }
                         });
                     }
                 }
             });
             $(this).ysComboBox("setValue", experimenttype);
         });
         IsLoadingExperimentType = 0;
     }

     function loadWeekNumClick() {
         $(".input-group-add").on("click", function () {
             setWeekNumDateStr(this, 1);
         });

         $(".input-group-cut").on("click", function () {
             setWeekNumDateStr(this, 2);
         });

         $(".weeknum").change(function () {
             setWeekNumDateStr(this, 3);
         });
     }

     /**
      * 设置周次 ，和周次对应的时间
      *  obj 操作对象
      *  otpytpe 1:添加按钮  2：减按钮  3：文本修改事件
      **/
     function setWeekNumDateStr(obj, opttype) {
         var index = $(obj).attr("index");
         var weeknum = $("#txtWeek_" + index).val();
         if (opttype == 1) {
             if (!(parseInt(weeknum) > 0)) {
                 weeknum = 0;
             }
             weeknum = parseInt(weeknum) + 1;
         } else if (opttype == 2) {
             if (parseInt(weeknum) == 1) {
                 return layer.msg('当前已经是最小周次。', { icon: 2, time: 3000, area: ['400px'], shade: false, offset: [15,] });
             }else if (!(parseInt(weeknum) > 1)) {
                 weeknum = 2;
             }
             weeknum = parseInt(weeknum) - 1;
         }
         $("#txtWeek_" + index).val(weeknum);

         var termstart = $("#txtWeek_" + index).attr("termstart");
         //计算日期。
         $("#spanWeekDate_" + index).html(getDateStr(weeknum, termstart));

         var plandetailid = $("#txtWeek_" + index).attr("plandetailid");
         saveWeekNumForm(plandetailid, weeknum);
     }

     function getDateStr(weeknum, termstartdate) {
         if (weeknum === '' || isNaN(weeknum)) return '';

         const startDate = new Date(termstartdate);
         // 处理周日：0转换为7
         const startDayOfWeek = startDate.getDay() || 7;

         // 计算第一周的周一（学期开始日期所在周的周一）
         const firstMonday = new Date(startDate);
         firstMonday.setDate(startDate.getDate() - (startDayOfWeek - 1));

         // 计算目标周的周一和周日
         const targetMonday = new Date(firstMonday);
         targetMonday.setDate(firstMonday.getDate() + (weeknum - 1) * 7);

         const targetSunday = new Date(targetMonday);
         targetSunday.setDate(targetMonday.getDate() + 6);

         // 格式化日期（自动补零）
         const format = (date) =>
             `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;

         return `${format(targetMonday)}--${format(targetSunday)}`;

    }

     function saveWeekNumForm(plandetailid, weeknum) {
         if (!(parseInt(weeknum) > 0)) {
             weeknum = 0;
         }
         var postData = { id: plandetailid, num: weeknum };
         $.ajax({
             url: '@Url.Content("~/ExperimentTeachManage/PlanDetail/SaveWeekNumFormJson")',
             type: 'post',
             data: postData,
             dataType: 'json',
             success: function (obj) {
                 if (obj.Tag == 1) {
                     layer.msg(obj.Message, { icon: 1, time: 3000, area: ['400px'], shade: false, offset: [15,] });
                     //layer.closeAll();
                 } else {
                     layer.msg(obj.Message, { icon: 2, time: 3000, area: ['400px'], shade: false, offset: [15,] });
                 }
             }
         });
     }
</script>