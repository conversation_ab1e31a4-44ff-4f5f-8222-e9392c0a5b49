﻿using System;
using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations;
using Dqy.Syjx.Util;

namespace Dqy.Syjx.Model.Input.BusinessThirdManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2025-05-22 10:26
    /// 描 述：无锡市签名验签InputModel
    /// </summary>
    public class SignWxInputModel 
    {
        /// <summary>
        /// 主键
        /// </summary>
        /// <returns></returns>
        [Display(Name = "主键")]
        public long Id { get; set; }
        /// <summary>
        /// 业务类型
        /// </summary>
        /// <returns></returns>
        [Display(Name = "业务类型")]
        [Required(ErrorMessage = @"请输入{0}")] 
        public long BizType { get; set; }
        /// <summary>
        /// 对应业务类型下Id
        /// </summary>
        /// <returns></returns>
        [Display(Name = "对应业务类型下Id")]
        public long? ObjId { get; set; }
        private string _InData;
        /// <summary>
        /// 签名原文
        /// </summary>
        /// <returns></returns>
        [Display(Name = "签名原文")]
        public string InData
        { 
           get { return _InData; }
           set { _InData = value; }
        }
        private string _SignValue;
        /// <summary>
        /// 签名结果
        /// </summary>
        /// <returns></returns>
        [Display(Name = "签名结果")]
        public string SignValue
        {
            //实际数据，所以不能过滤 "MEQCIANbDmgcjXRDHzD44cNVCGtOuY1cneLKEoan9VYFtItgAiBRRGiYcT12NnoICXl6qjagN9MYEzdyef5fqnOiEtebLg=="
            get { return _SignValue; }
            set { _SignValue = value; }
        }
    }
}
