﻿using System;
using System.Collections.Generic;
using Newtonsoft.Json;
using Dqy.Syjx.Util;

namespace Dqy.Syjx.Model.Param.ExperimentTeachManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-12-08 17:19
    /// 描 述：编制实验计划实体查询类
    /// </summary>
    public class PlanDetailListParam
    {
        public PlanDetailListParam()
        {
            BaseIsDelete = 0;
            OptType = 0;
            TextbookVersionBaseId = -1;
        }
        private string _ids;
        public string Ids
        {
            get { return StringFilter.SearchSql(_ids); }
            set { _ids = value; }
        }
        /// <summary>
        /// 操作类型。
        /// 0：默认，正常通用查询
        /// 1:根据TextbookVersionDetailId查询验证。
        /// </summary>
        public int OptType { get; set; }
        [JsonConverter(typeof(StringJsonConverter))]
        public long PlanInfoId { get; set; }

        public int BaseIsDelete { get; set; }

        public int UnitType { get; set; }
        [JsonConverter(typeof(StringJsonConverter))]
        public long UnitId { get; set; }
        [JsonConverter(typeof(StringJsonConverter))]
        public long UserId { get; set; }

        [JsonConverter(typeof(StringJsonConverter))]
        public long SchoolId { get; set; }
        [JsonConverter(typeof(StringJsonConverter))]
        public long CountyId { get; set; }
        public int? ExperimentType { get; set; }
        public long? TextbookVersionBaseId { get; set; }
        public long? TextbookVersionDetailId { get; set; }
        public int? IsNeedDo { get; set; }
        private string _name;
        public string Name
        {
            get { return StringFilter.SearchSql(_name); }
            set { _name = value; }
        }
        /// <summary>
        /// 年级
        /// </summary>
        public int GradeId { get; set; }
        /// <summary>
        /// 班级
        /// </summary>
        public int ClassId { get; set; }
        /// <summary>
        /// 学科
        /// </summary>
        [JsonConverter(typeof(StringJsonConverter))]
        public int CourseId { get; set; }
        /// <summary>
        /// 上课时间
        /// </summary>
        public DateTime? ClassTime { get; set; }
        /// <summary>
        /// 学期
        /// </summary>
        public int? SchoolTerm { get; set; }
        /// <summary>
        /// 学年起始年度
        /// </summary>
        public int SchoolYearStart { get; set; }
        /// <summary>
        /// 学年终止年度
        /// </summary>
        public int SchoolYearEnd { get; set; }

        [JsonConverter(typeof(StringJsonConverter))]
        public long? SchoolGradeClassId { get; set; }
        /// <summary>
        /// 班级集合
        /// </summary>
        private string _schoolgradeclassidz;
        public string SchoolGradeClassIdz
        {
            get { return StringFilter.SearchSql(_schoolgradeclassidz); }
            set { _schoolgradeclassidz = value; }
        }

        public int? SchoolStage { get; set; }

        /// <summary>
        /// 实验来源路径
        /// </summary>
        public int? SourcePath { get; set; }
        /// <summary>
        /// 校本实验Id
        /// </summary>
        public long? SchoolExperimentId { get; set; }

        /// <summary>
        /// 校本实验删除的时候验证使用
        /// </summary>
        public string SchoolExperimentIds { get; set; }

        /// <summary>
        /// 是否考核
        /// </summary>
        public int? IsEvaluate { get; set; }

        /// <summary>
        /// 教材类型（1：必修（选修）  2：选择性必修（非选修））
        /// </summary>
        public int? CompulsoryType { get; set; }

        /// <summary>
        /// 按主管部门要求
        /// </summary>
        public int? IsExam { get; set; } = -1;
        /// <summary>
        /// 课程活动类型(1: 课内 2:课外)
        /// </summary>
        public int ActivityType { get; set; } = -10000;
        /// <summary>
        /// 周次
        /// </summary>
        public int WeekNum {  get; set; } = -10000;

        /// <summary>
        /// 周次(大于)
        /// </summary>
        public int WeekNumlt { get; set; } = -10000;
    }


    /// <summary>
    /// 手机端实验选择列表API查询类
    /// </summary>
    public class PlanDetailListApiParam : SerachApiParam
    {
        /// <summary>
        /// 学校Id
        /// </summary>
        public long? SchoolId { get; set; }

        /// <summary>
        /// 年级班级Id
        /// </summary>
        public long SchoolGradeClassId { get; set; }

        /// <summary>
        /// 学科
        /// </summary>
        public int CourseId { get; set; }

        /// <summary>
        /// 上课时间
        /// </summary>
        public DateTime? ClassTime { get; set; }

        /// <summary>
        /// 学期
        /// </summary>

        public int? SchoolTerm { get; set; }
        /// <summary>
        /// 学年
        /// </summary>
        public int? SchoolYearStart { get; set; }

        /// <summary>
        /// 学段
        /// </summary>
        public int? SchoolStage { get; set; }

        /// <summary>
        /// 学科Ids
        /// </summary>
        public string SchoolGradeClassIdz { get; set; }

        /// <summary>
        /// 实验名称关键字搜索
        /// </summary>
        public string keyWord { get; set; }

        /// <summary>
        /// 实验类型
        /// </summary>
        public int? ExperimentType { get; set; }
        /// <summary>
        /// 是否本校
        /// </summary>
        public int IsThisSchool { get; set; } = -10000;
    }
}
