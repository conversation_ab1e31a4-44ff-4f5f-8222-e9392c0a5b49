﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.ExperimentTeachManage;
using Dqy.Syjx.Model.Param.ExperimentTeachManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Enum.ExperimentTeachManage;

namespace Dqy.Syjx.Service.ExperimentTeachManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2023-08-01 10:28
    /// 描 述：实验仪器借出服务类
    /// </summary>
    public class ExperiInstruLogService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<ExperiInstruLogEntity>> GetList(ExperiInstruLogListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<ExperiInstruLogEntity>> GetPageList(ExperiInstruLogListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<ExperiInstruLogEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<ExperiInstruLogEntity>(id);
        }


        /// <summary>
        /// 实验借出查询列表
        /// </summary>
        /// <param name="param">查询参数</param>
        /// <param name="pagination">分页参数</param>
        /// <returns></returns>
        public async Task<List<ExperiInstruStatuzEntity>> GetStatuzPage(ExperiInstruStatuzListParam param, Pagination pagination)
        {
            var parameter = new List<DbParameter>();
            StringBuilder strSql = new StringBuilder();
            strSql.Append(@$"SELECT * FROM (
                SELECT instrustatuz.*
                ,booking.SchoolYearStart
                ,booking.SchoolYearEnd
                ,booking.SchoolTerm
                --,booking.SchoolId
				,booking.ExperimentName
                ,booking.SourceType
                ,booking.SourcePath
                ,booking.ExperimentType
                ,booking.Groupz
                ,booking.ClassTime
                ,booking.SectionId
                ,booking.SectionShow
                ,booking.ArrangerId
                ,booking.GradeId
                ,booking.CourseId
                ,booking.FunRoomId
                ,booking.ClassName
                ,booking.SectionIndex
                ,booking.SectionBgnTime
                ,booking.SectionEndTime
                ,booking.Statuz AS ExperimentStatuz
				,room.Name as FunRoomName
                ,D2.DicName AS CourseName ,D1.DicName AS GradeName
                ,su.RealName AS LoanName
				FROM  ex_ExperimentBooking AS booking
				LEFT JOIN  bn_FunRoom AS room ON booking.FunRoomId = room.Id
				INNER JOIN  sys_static_dictionary AS D2 ON booking.CourseId = D2.DictionaryId AND D2.TypeCode = '{DicTypeCodeEnum.Course.ParseToInt()}'
                INNER JOIN  sys_static_dictionary AS D1 ON booking.GradeId = D1.DictionaryId AND D1.TypeCode = '{DicTypeCodeEnum.Grade.ParseToInt()}'
				INNER JOIN  ex_ExperiInstruStatuz AS instrustatuz ON booking.Id = instrustatuz.ExperimentBookingId
                LEFT JOIN  SysUser AS su ON instrustatuz.LoanSysUserId = su.Id
                Where booking.Statuz >= {ExperimentBookStatuzEnum.WaitRecord.ParseToInt()}
                ) as T WHERE  BaseIsDelete = 0 ");
            if (param != null)
            {
                if (!param.Id.IsNullOrZero())
                {
                    strSql.Append($" AND Id = {param.Id}");
                }
                if (!param.SchoolId.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolId = {param.SchoolId}");//根据用户登录的单位ID进行查询
                }
                if (!param.SchoolYearStart.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolYearStart = {param.SchoolYearStart}"); //开始学年
                }
                if (!param.SchoolTerm.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolTerm = {param.SchoolTerm}"); //学期
                }
                if (!param.LoanSysUserId.IsNullOrZero())
                {
                    strSql.Append($" AND LoanSysUserId = {param.LoanSysUserId}"); // 借出人
                }
                if (param.IsShowAll != 1)
                {
                    strSql.Append($" AND ( Statuz < {ExperiInstruStatuzEnum.Backed.ParseToInt()}"); //已归还的不显示。
                                                                                                     //待借出，超时的不显示
                    strSql.Append($" OR (Statuz < {ExperiInstruStatuzEnum.Loaned.ParseToInt()} AND  ClassTime > getDate() ) ) "); //待借出已超期不显示。DATEADD (day , 1, ClassTime() )
                }
                if (param.Statuz > -1)
                {
                    strSql.Append($" AND Statuz = {param.Statuz} ");
                }
                if (param.ExperimentStatuz > 0)
                {
                    strSql.Append($" AND ExperimentStatuz = {param.ExperimentStatuz} ");
                }
                if (!string.IsNullOrEmpty(param.ExperimentName))
                {
                    strSql.Append($" AND ExperimentName like @ExperimentName ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ExperimentName", $"%{param.ExperimentName}%"));
                }
            }
            var list = await this.BaseRepository().FindList<ExperiInstruStatuzEntity>(strSql.ToString(), parameter.ToArray(), pagination);
            return list.ToList();
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(ExperiInstruLogEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(ExperiInstruLogEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
               strSql = $" update ex_ExperiInstruLog set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
               strSql = $"update ex_ExperiInstruLog set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<ExperiInstruLogEntity, bool>> ListFilter(ExperiInstruLogListParam param)
        {
            var expression = LinqExtensions.True<ExperiInstruLogEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (param.ExperiInstruStatuzId >0)
                {
                    expression = expression.And(t => t.ExperiInstruStatuzId == param.ExperiInstruStatuzId);
                }
            }
            return expression;
        }
        #endregion
    }
}
