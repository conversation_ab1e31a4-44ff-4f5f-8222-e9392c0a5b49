﻿using System;
using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations.Schema;
using Dqy.Syjx.Util;
using System.ComponentModel;
using NPOI.SS.UserModel;
using Dqy.Syjx.Util.Extension;

namespace Dqy.Syjx.Entity.CameraManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2022-01-10 10:02
    /// 描 述：摄像头管理实体类
    /// </summary>
    [Table("bn_SchoolCamera")]
    public class SchoolCameraEntity : BaseExtensionEntity
    {
        /// <summary>
        /// 所在单位id
        /// </summary>
        /// <returns></returns>
        public long UnitId { get; set; }
        /// <summary>
        /// 学校名称
        /// </summary>
        /// <returns></returns>
        [Description("学校名称")]
        [ExportExcelAttribute(HorizontalAlignment.Left, 30)]
        public string SchoolName { get; set; }
        /// <summary>
        /// 摄像机名称
        /// </summary>
        /// <returns></returns>
        [Description("摄像机名称")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 25)]
        public string SrcName { get; set; }

        /// <summary>
        /// 全平台摄像头唯一编号
        /// </summary>
        /// <returns></returns>
        [Description("摄像机编码")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 25)]
        public string SrcIndex { get; set; }

        /// <summary>
        /// 摄像头所在功能室和地点
        /// </summary>
        /// <returns></returns>
        [Description("摄像头所在功能室和地点")]
        [ExportExcelAttribute(HorizontalAlignment.Left, 40)]
        public string Address { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        /// <returns></returns>
        [Description("备注")]
        [ExportExcelAttribute(HorizontalAlignment.Left, 40)]
        public string Remark { get; set; }
        /// <summary>
        /// 链接地址
        /// </summary>
        /// <returns></returns>
        public string LinkUrl { get; set; }
        /// <summary>
        /// 状态（1：启用 2：禁用）
        /// </summary>
        /// <returns></returns>
        public int Statuz { get; set; }
        /// <summary>
        /// 功能室Id(关联后反写)
        /// </summary>
        /// <returns></returns>
        [JsonConverter(typeof(StringJsonConverter))]
        public long? FunRoomId { get; set; }

        /// <summary>
        /// 关联人Id
        /// </summary>
        /// <returns></returns>
        [JsonConverter(typeof(StringJsonConverter))]
        public long? OptUserId { get; set; }

        /// <summary>
        /// 关联摄像头Id
        /// </summary>
        /// <returns></returns>
        [JsonConverter(typeof(StringJsonConverter))]
        public long CameraId { get; set; }

        [NotMapped]
        public string UnitName { get; set; }
        /// <summary>
        /// 实验室名称
        /// </summary>
        [NotMapped]
        public string FunRoomName { get; set; }
        /// <summary>
        /// 房间名称
        /// </summary>
        [NotMapped]
        public string RoomName { get; set; }
        /// <summary>
        /// 楼宇名称
        /// </summary>
        [NotMapped]
        public string HouseName { get; set; }
        /// <summary>
        /// 区县Id
        /// </summary>
        [NotMapped]
        public long CountyId { get; set; }
        /// <summary>
        /// 区县名称
        /// </summary>
        [NotMapped]
        [Description("区县名称")]
        [ExportExcelAttribute(HorizontalAlignment.Left, 25)]
        public string CountyName { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        [NotMapped]
        [Description("状态")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 15)]
        public string StrStatuz { get; set; }
    }
}
