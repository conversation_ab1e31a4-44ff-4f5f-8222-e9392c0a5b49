﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.OrganizationManage;
using Dqy.Syjx.Model.Param.OrganizationManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Model.Result.OrganizationManage;
using Dqy.Syjx.Util.Tools;
using NPOI.Util;

namespace Dqy.Syjx.Service.OrganizationManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-09-24 10:32
    /// 描 述：服务类
    /// </summary>
    public class CourseSectionService : RepositoryFactory
    {
        #region 获取数据
        public async Task<List<CourseSectionEntity>> GetList(CourseSectionListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }
         
        public async Task<CourseSectionEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<CourseSectionEntity>(id);
        }

        public async Task<List<CourseSectionEntity>> GetCourseSectionList(CourseSectionListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            var parameter = new List<DbParameter>();
            strSql.Append($@"SELECT CS.Id,CS.UnitId,CS.BeginTime,CS.EndTime,CS.GradeId,D.DicName AS GradeName,CS.Statuz,CS.Remark,CS.RowIndex AS RowIndex,CS.WeekId
                          , '' AS SectionName 
                          FROM  up_CourseSection AS CS
                          INNER JOIN  sys_static_dictionary AS D ON CS.GradeId = D.DictionaryId AND D.TypeCode = '{DicTypeCodeEnum.Grade.ParseToInt()}' AND D.BaseIsDelete = 0 
                          INNER JOIN  sys_static_dictionary AS D2 ON D2.DictionaryId = cs.WeekId AND D2.TypeCode = '{DicTypeCodeEnum.Week.ParseToInt()}' AND D2.BaseIsDelete = 0     
                          WHERE CS.Statuz = 1 ");
            strSql.Append($" AND D2.Memo = '{DateTime.Now.DayOfWeek.ParseToInt()}'");
            if (param != null)
            {
                if (param.UnitId > -1)
                {
                    strSql.Append(" AND CS.UnitId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                if (param.Id > 0)
                {
                    strSql.Append(" AND CS.Id = @Id ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Id", param.Id));
                }
                if (param.TimeIsNotEmpty == 1)
                {
                    strSql.Append(" AND (BeginTime <> '' OR EndTime <> '')");
                }
                if(param.GradeId > 0)
                {
                    strSql.Append(" AND GradeId = @GradeId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@GradeId", param.GradeId));
                }
            }
           
            pagination.Sort = "RowIndex";
            pagination.SortType = "asc";
            var list = await this.BaseRepository().FindList<CourseSectionEntity>(strSql.ToString(), parameter.ToArray(), pagination);
            if (list != null)
            {
                list.ForEach(m => m.SectionName = string.Format("第{0}节({1}~{2})", m.RowIndex, m.BeginTime ?? "", m.EndTime ?? ""));
            }
            return list.ToList();
        }
        
        /// <summary>
        /// 根据Id,单位信息获取学科节次表信息
        /// </summary>
        /// <param name="unitId">单位Id</param>
        /// <param name="id">单位节次表Id</param>
        /// <returns></returns>
        public async Task<CourseSectionEntity> GetEntityById(long unitId, long id)
        {
            StringBuilder strSql = new StringBuilder();
            var parameter = new List<DbParameter>();
            strSql.Append($@" SELECT CS.Id,CS.UnitId,CS.BeginTime,CS.EndTime,CS.Statuz,CS.Remark,CS.RowIndex,CS.DictionaryId 
                    FROM  up_CourseSection AS CS 
                    WHERE CS.Statuz = 1  AND UnitId = {unitId} AND CS.Id = {id}");
            var list = await this.BaseRepository().FindList<CourseSectionEntity>(strSql.ToString());
            if (list != null)
            {
                list.ForEach(m => m.SectionName = string.Format("第{0}节({1}~{2})", m.RowIndex, m.BeginTime ?? "", m.EndTime ?? ""));
            }
            return list.FirstOrDefault();
        }
        
        /// <summary>
        /// 取当前时间所属于的节次
        /// </summary>
        /// <param name="unitId">当前单位id</param>
        /// <returns></returns>
        public async Task<CourseSectionEntity> GetSectionByCurrentTime(long unitId)
        {
            string currentTime = DateTime.Now.ToString("HH:mm");

            StringBuilder strSql = new StringBuilder();
            strSql.Append($@"  SELECT TOP(1) CS.Id ,CS.RowIndex ,CS.BeginTime ,CS.EndTime , '' AS SectionName 
             FROM  up_CourseSection AS 
             WHERE CS.Statuz = 1 AND LEN(CS.BeginTime) > 0 AND LEN(CS.EndTime) > 0 
             AND CS.UnitId = {unitId}  AND EndTime <= '{currentTime}' 
             ORDER BY D.Sequence DESC");
            IEnumerable<CourseSectionEntity> list = await this.BaseRepository().FindList<CourseSectionEntity>(strSql.ToString());
            if (list != null && list.Count() > 0)
            {
                if (list != null)
                {
                    list.ForEach(m => m.SectionName = string.Format("第{0}节({1}~{2})", m.RowIndex, m.BeginTime ?? "", m.EndTime ?? ""));
                }
                return list.First();
            }
            else
                return null;
        }

        /// <summary>
        /// 查询课程节次信息
        /// </summary>
        /// <param name="unitId">单位Id</param>
        /// <param name="gradeId">年级Id</param>
        /// <param name="weekId">星期Id</param>
        /// <returns></returns>
        public async Task<List<CourseSectionEntity>> GetCourseSectionList(long unitId, int gradeId,int weekId)
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat(@"
                            SELECT Id,GradeId,RowIndex AS RIndex,UnitId,
	                               WeekId,BeginTime,EndTime,DjBeginTime,DjEndTime,
                                   Remark
                            FROM  up_CourseSection
                            WHERE UnitId ={0} AND GradeId = {1} AND WeekId = {2}
                             ORDER BY RowIndex ASC ", unitId, gradeId, weekId);
            var list = await this.BaseRepository().FindList<CourseSectionEntity>(sb.ToString());

            #region 如果未查询到单位年级星期节次信息默认显示默认数据的“空”数据
            if (list.ToList().Count == 0)
            {
                sb = new StringBuilder();
                sb.Append(@"
                            SELECT 0 AS Id,GradeId,RowIndex AS RIndex,UnitId,
	                               WeekId,'' AS BeginTime,'' AS EndTime,'' AS DjBeginTime,'' AS DjEndTime,
                                   Remark
                            FROM  up_CourseSection AS CS
                            WHERE UnitId = 0 AND GradeId = 0 AND WeekId = 0
		                    ORDER BY RowIndex ASC ");
                list = await this.BaseRepository().FindList<CourseSectionEntity>(sb.ToString());
            }
            #endregion

            return list.ToList();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="unitId"></param>
        /// <param name="gradeId"></param>
        /// <param name="weekId"></param>
        /// <returns></returns>
        public async Task<List<CourseSectionEntity>> GetSectionList(long unitId, int gradeId, int weekId)
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendFormat(@"
                            SELECT Id,BaseIsDelete,BaseCreateTime,BaseModifyTime,BaseCreatorId,BaseModifierId,BaseVersion,
                                   DictionaryId,RowIndex,Statuz,
                                   GradeId,RowIndex AS RIndex,UnitId,
	                               WeekId,BeginTime,EndTime,DjBeginTime,DjEndTime,
                                   Remark
                            FROM  up_CourseSection
                            WHERE UnitId ={0} AND GradeId = {1} AND WeekId = {2}", unitId, gradeId, weekId);
            var list = await this.BaseRepository().FindList<CourseSectionEntity>(sb.ToString());
            return list.ToList();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="scheduleType">1:夏令时，2：冬令时</param>
        /// <param name="unitId">单位Id</param>
        /// <param name="gradeId">年级Id</param>
        /// <param name="weekId">星期Id</param>
        /// <returns></returns>
        public async Task<List<CourseSelectModel>> GetCourseSelectList(int scheduleType, long unitId, int gradeId, int weekId)
        {
            StringBuilder sb = new StringBuilder();
            if (scheduleType == 1)
            {
                sb.Append(@$"SELECT CS.Id AS Id,RowIndex ,'' AS SectionName,
	                        BeginTime,EndTime  ,CS.WeekId FROM 
	                         up_CourseSection AS CS
                            INNER JOIN  sys_static_dictionary AS D ON CS.WeekId = D.DictionaryId AND D.TypeCode = '1015'
                            WHERE UnitId = {unitId} AND GradeId = {gradeId} AND CS.WeekId = {weekId}
                             ORDER BY RowIndex ASC ");
            }
            else
            {
                sb.Append(@$"SELECT CS.Id AS Id,RowIndex ,'' AS SectionName,
	                        DjBeginTime AS BeginTime,DjEndTime AS EndTime ,CS.WeekId FROM 
	                         up_CourseSection  AS CS
                             INNER JOIN  sys_static_dictionary AS D ON CS.WeekId = D.DictionaryId AND D.TypeCode = '1015'
                            WHERE UnitId = {unitId} AND GradeId = {gradeId} AND CS.WeekId = {weekId}
                             ORDER BY RowIndex ASC ");
            }
            var list = await this.BaseRepository().FindList<CourseSelectModel>(sb.ToString());
            //未获取到单位配置的数据获取系统默认的
            if(list.ToList().Count == 0)
            {
                sb=new StringBuilder();
                sb.Append(@$"SELECT Id,RowIndex ,'' AS SectionName,
	                        DjBeginTime AS BeginTime,DjEndTime AS EndTime   FROM 
	                         up_CourseSection WHERE UnitId = 0 
                            ORDER BY RowIndex ASC ");
                list = await this.BaseRepository().FindList<CourseSelectModel>(sb.ToString());
                list.ForEach(f => f.WeekId = weekId);

            }
            if (list != null)
            {
                list.ForEach(m => m.SectionName = string.Format("第{0}节({1}~{2})", m.RowIndex, m.BeginTime ?? "", m.EndTime ?? ""));
            }
            return list.ToList();
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(CourseSectionEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }


        public async Task SaveTransForm(CourseSectionEntity entity, Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteTransForm(string ids, Repository db)
        {
            long[] idArr = TextHelper.SplitToArray<long>(ids, ',');
            await db.Delete<CourseSectionEntity>(idArr);
        }

        public async Task DeleteForm(string ids)
        {
            long[] idArr = TextHelper.SplitToArray<long>(ids, ',');
            await this.BaseRepository().Delete<CourseSectionEntity>(idArr);
        }

        public bool ExistDictionaryId(CourseSectionEntity entity)
        {
            var expression = LinqExtensions.True<CourseSectionEntity>();
            expression = expression.And(t => t.Statuz == 1);
            expression = expression.And(t => t.UnitId == entity.UnitId);
            if (entity.Id.IsNullOrZero())
            {
                expression = expression.And(t => t.DictionaryId == entity.DictionaryId);
            }
            else
            {
                expression = expression.And(t => t.DictionaryId == entity.DictionaryId && t.Id != entity.Id);
            }
            return this.BaseRepository().IQueryable(expression).Count() > 0 ? true : false;
        }
        #endregion

        #region 私有方法
        private Expression<Func<CourseSectionEntity, bool>> ListFilter(CourseSectionListParam param)
        {
            var expression = LinqExtensions.True<CourseSectionEntity>();
            if (param != null)
            {
                if (param.BaseIsDelete>=0)
                {
                    expression = expression.And(m => m.BaseIsDelete == param.BaseIsDelete);
                }
                if (param.UnitId >= 0)
                {
                    expression = expression.And(m => m.UnitId == param.UnitId);
                }
                if(param.Statuz >= 0)
                {
                    expression = expression.And(m => m.Statuz == param.Statuz);
                }
                if (param.RowIndex>0)
                {
                    expression = expression.And(m => m.RowIndex == param.RowIndex);
                }
                if (param.GradeId > 0)
                {
                    expression = expression.And(m => m.GradeId == param.GradeId);
                }
            }
            return expression;
        }
        #endregion
    }
}
