﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
 }
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .modelShow {
        word-break: break-all;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li class="select-time">
                        <label>借出时间： </label><input id="startDate" col="StartDate" type="text" class="time-input" placeholder="开始时间" style="width:100px" />
                        <span>-</span>
                        <input id="endDate" col="EndDate" type="text" class="time-input" placeholder="结束时间" style="width:100px" />
                    </li>
                    <li>
                        <span id="lendUserId" col="LendUserId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <div id="storagePlace" col="StoragePlace" style="display:inline-block;"></div>
                    </li>
                    <li>
                        <span id="statuz" col="Statuz" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <input id="keyWord" col="KeyWord" placeholder="仪器代码、名称" style="display:inline-block;width:150px;" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-default btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group-sm hidden-xs" id="toolbar">
            <a id="btnExportRevert" class="btn btn-warning btn-sm" onclick="exportForm()"><i class="fa fa-download"></i> 导出</a>
            <a id="btnRevert" class="btn btn-success disabled" onclick="revert(1)"><i class="fa fa-edit"></i> 批量归还</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>

<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    var lendStatuz = ys.request("statuz");
    $(function () {
        laydate.render({ elem: '#startDate', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed' });
        laydate.render({ elem: '#endDate', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed' });
        loadLendUser();
        loadStoragePlace();
        $("#statuz").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(InstrumentLendStatuzEnum).EnumToDictionaryString())) ,defaultName: '状态' });
        if(lendStatuz && lendStatuz > 0){
             $("#statuz").ysComboBox('setValue', lendStatuz);
        }
        
        initGrid();

        $("#gridTable").on("check.bs.table uncheck.bs.table check-all.bs.table uncheck-all.bs.table", function () {
            var ids = $("#gridTable").bootstrapTable("getSelections");
            if ($('#btnRevert')) {
                $('#btnRevert').toggleClass('disabled', !ids.length);
            }
        });
    });

    function initGrid() {
        var queryUrl = '@Url.Content("~/InstrumentManage/InstrumentLend/GetRevertListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            columns: [
                { checkbox: true, visible: true },
                {
                    field: 'opt', title: '操作', halign: 'center', align: 'center', width: commonWidth.Instrument.Opt3,
                    formatter: function (value, row, index) {
                        var html = '';
                        if (row.Statuz == @InstrumentLendStatuzEnum.WaitConfirm.ParseToInt()) {
                            html += $.Format('<a class="btn btn-warning btn-xs" href="#" onclick="withdraw(this)" value="{0}"><i class="fa fa-reply"></i>撤销借出</a> ', row.Id);
                        }
                        else if (row.Statuz == @InstrumentLendStatuzEnum.WaitRevert.ParseToInt()) {
                            html += $.Format('<a class="btn btn-success btn-xs" href="#" onclick="revert(0,this)" value="{0}" t="1"><i class="fa fa-edit"></i>归还</a> ', row.Id);
                        }
                        html += $.Format('<a class="btn btn-info btn-xs" href="#" onclick="lookForm(this)" value="{0}"><i class="fa fa-eye"></i>查看</a> ', row.Id);
                        return html;
                    }
                },
                {
                    field: 'Code', title: '分类代码', halign: 'center', align: 'center', sortable: true, width: 80, visible: false,
                    formatter: function (value, row, index) {
                        if (row.Id) return value;
                        else return '';
                    }
                },
                {
                    field: 'Name', title: '仪器名称', halign: 'center', align: 'left', sortable: true, width: commonWidth.Instrument.Name,
                    formatter: function (value, row, index) {
                        var html = "";
                        if (row.IsDangerChemical == 1) {
                            html += Syjx.GetDangerHtml();
                        }
                        if (row.IsSelfMade == 1) {
                            html += Syjx.GetSelfMadeHtml();
                        }
                        html += value;
                        return html;
                    }
                },
                {
                    field: 'Model', title: '规格属性', halign: 'center', align: 'left', sortable: true, width: commonWidth.Instrument.Model,
                    formatter: function (value, row, index) {
                        var html = value == null ? "" : value;
                        html = `<span class='modelShow' data-toggle='tooltip' data-placement='top' data-content='${html}'>${html}</span>`;
                        return html;
                    }
                },
                { field: 'LendNum', title: '借用数量', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.Num },
                { field: 'UnitName', title: '单位', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.UnitName },
                {
                    field: 'FunRoom', title: '存放地', halign: 'center', align: 'left', sortable: true, width: commonWidth.Instrument.FunRoom,
                    formatter: function (value, row, index) {
                        if (row.Cupboard) {
                            value += '>' + row.Cupboard;
                        }
                        if (row.Floor) {
                            value += '>' + row.Floor;
                        }
                        return value;
                    }
                },
                
                { field: 'LendUserName', title: '借用人', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.UserName },
                {
                    field: 'BaseCreateTime', title: '借出时间', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.BaseCreateTime,
                    formatter: function (value, row, index) {
                        if (value) {
                            return value.substring(0, 10);
                        }
                    }
                },
                {
                    field: 'RevertTime', title: '归还时间', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.BaseCreateTime,
                    formatter: function (value, row, index) {
                        if (row.Statuz == @InstrumentLendStatuzEnum.AlreadyRevert.ParseToInt()) {
                            return value.substring(0, 10);
                        }
                        else {
                            if (row.OverRevertDay > 0) return '<font style="color:red;">已超' + row.OverRevertDay + '天</font>';
                            else return '--';
                        }
                    }
                },
                {
                    field: 'Statuz', title: '状态', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.Statuz,
                    formatter: function (value, row, index) {
                        return row.StatuzDesc;
                    }
                },
                
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            },
            onLoadSuccess: function () {
                $(".modelShow").popover({
                    trigger: 'hover',
                    html: true
                });
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function lookForm(obj) {
        var id = $(obj).attr('value');
        ys.openDialog({
            title: '查看',
            content: '@Url.Content("~/InstrumentManage/InstrumentLend/LendForm")' + '?id=' + id + '&isLook=1',
            width: '768px',
            height: '550px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function withdraw(obj) {
        var id = $(obj).attr('value');
        ys.confirm('您确认要撤销借出吗？', function () {
            ys.ajax({
                url: '@Url.Content("~/InstrumentManage/InstrumentLend/RevokeForm")' + '?id=' + id,
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.alertError(obj.Message);
                    }
                }
            });
        });
    }

    function revert(isBatch, obj) {
        var id = '';
        if (isBatch == 1) {
            var selectedRow = $('#gridTable').bootstrapTable('getSelections');
            if (selectedRow.length > 0) {
                var lendUserId = '';
                var istrue = true;
                $.each(selectedRow, function (i, obj) {
                    if (i == 0) {
                        lendUserId = obj.LendUserId;
                    }
                    else if (lendUserId != obj.LendUserId) {
                        istrue = false;
                        return false;
                    }
                });
                if (!istrue) {
                    ys.alertError('只有同一个借用人才能批量归还！');
                    return false;
                }
                id = ys.getIds(selectedRow);
            }
            else {
                ys.alertError('请至少选择一项！');
                return false;
            }
        }
        else id = $(obj).attr('value');
        ys.confirm('您确认已全部归还吗？', function () {
            ys.ajax({
                url: '@Url.Content("~/InstrumentManage/InstrumentLend/RevertForm")' + '?ids=' + id,
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.alertError(obj.Message);
                    }
                }
            });
        });
    }

    function loadLendUser() {
        $('#lendUserId').ysComboBox({
            url: '@Url.Content("~/InstrumentManage/InstrumentLend/GetUserListJson")',
            key: 'Id',
            value: 'RealName',
            defaultName: '借用人'
        });
    }

    function loadStoragePlace() {
        $('#storagePlace').ysComboBoxTree({
            url: '@Url.Content("~/BusinessManage/Cupboard/GetCupboardTreeListAllJson")',
            class: "form-control",
            key: 'id',
            value: 'name',
            defaultName: '存放地'
        });
        $('#storagePlace').ysComboBoxTree('setValue', -1);
    }

    function resetGrid() {
        //清空条件
        $('#startDate').val('');
        $('#endDate').val('');
        $('#lendUserId').ysComboBox('setValue', -1);
        $('#storagePlace').ysComboBoxTree('setValue', -1);
        $('#statuz').ysComboBox('setValue', -1);
        $('#keyWord').val('');
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function hideDialogButton() {
        $('.layui-layer-btn0').hide();
    }

    function exportForm() { //导出
        var url = '/InstrumentManage/InstrumentLend/ExportRevert';
        var pagination = $('#gridTable').ysTable('getPagination', { "sort": "Id", "order": "asc", "offset": 0, "limit": 10 });
        var postData = $("#searchDiv").getWebControls(pagination);
        ys.exportExcel(url, postData);
    }
</script>
