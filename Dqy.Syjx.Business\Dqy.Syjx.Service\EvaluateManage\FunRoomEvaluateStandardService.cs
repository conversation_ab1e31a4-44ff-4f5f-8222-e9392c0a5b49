﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.EvaluateManage;
using Dqy.Syjx.Model.Param.EvaluateManage;

namespace Dqy.Syjx.Service.EvaluateManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-11-23 17:33
    /// 描 述：标准库服务类
    /// </summary>
    public class FunRoomEvaluateStandardService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<FunRoomEvaluateStandardEntity>> GetList(FunRoomEvaluateStandardListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<FunRoomEvaluateStandardEntity>> GetPageList(FunRoomEvaluateStandardListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ListFilter(param, strSql);
            var list = await this.BaseRepository().FindList<FunRoomEvaluateStandardEntity>(strSql.ToString(), filter.ToArray(), pagination);
            return list.ToList();
        }

        public async Task<FunRoomEvaluateStandardEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<FunRoomEvaluateStandardEntity>(id);
        }

        public async Task<FunRoomEvaluateStandardEntity> GetEntity(FunRoomEvaluateStandardListParam param)
        {
            return await this.BaseRepository().FindEntity<FunRoomEvaluateStandardEntity>(p => p.VersionName == param.VersionName && p.DictionaryId1006A == param.DictionaryId1006A && p.DictionaryId1006B == param.DictionaryId1006B && p.SchoolStage == param.SchoolStage && p.DictionaryId1005 == param.DictionaryId1005 && p.TargetName == param.TargetName);
        }

        #endregion

        #region 提交数据
        public async Task SaveForm(FunRoomEvaluateStandardEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(FunRoomEvaluateStandardEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
               strSql = $" update bn_FunRoomEvaluateStandard set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
               strSql = $"update bn_FunRoomEvaluateStandard set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<FunRoomEvaluateStandardEntity, bool>> ListFilter(FunRoomEvaluateStandardListParam param)
        {
            var expression = LinqExtensions.True<FunRoomEvaluateStandardEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }

                if (param.SchoolStage > 0)
                {
                    expression = expression.And(t => t.SchoolStage==param.SchoolStage);
                }
                if (param.DictionaryId1005 > 0)
                {
                    expression = expression.And(t => t.DictionaryId1005 == param.DictionaryId1005);
                }
                if (param.DictionaryId1006A > 0)
                {
                    expression = expression.And(t => t.DictionaryId1006A == param.DictionaryId1006A);
                    if (param.DictionaryId1006B > 0)
                    {
                        expression = expression.And(t => t.DictionaryId1006B == param.DictionaryId1006B);
                    }
                }
            }
            return expression;
        }

        private List<DbParameter> ListFilter(FunRoomEvaluateStandardListParam param, StringBuilder strSql)
        {
            strSql.Append(@"  SELECT * From (
                 SELECT
                    a1.Id ,
                    a1.BaseIsDelete ,
                    a1.BaseCreateTime ,
                    a1.BaseModifyTime ,
                    a1.BaseCreatorId ,
                    a1.BaseModifierId ,
                    a1.BaseVersion ,
                    a1.VersionName ,
                    a1.DictionaryId1006A ,
                    a1.DictionaryId1006B ,
                    a1.SchoolStage ,
                    a1.DictionaryId1005 ,
                    a1.TargetName ,
                    a1.Statuz ,
                    a1.Remark ,
                    dic2.DicName AS SchoolStageName ,
                    dic3.DicName AS SubjectName ,
                    dic4.DicName AS ClassOneName,
                    dic5.DicName AS ClassTwoName
                    FROM  bn_FunRoomEvaluateStandard AS a1
                    INNER JOIN  sys_static_dictionary AS dic2 ON a1.SchoolStage = dic2.DictionaryId AND dic2.BaseIsDelete = 0
                    INNER JOIN  sys_static_dictionary AS dic3 ON a1.DictionaryId1005  = dic3.DictionaryId AND dic3.BaseIsDelete = 0
                    INNER JOIN  sys_static_dictionary AS dic4 ON a1.DictionaryId1006A = dic4.DictionaryId AND dic4.BaseIsDelete = 0
                    INNER JOIN  sys_static_dictionary AS dic5 ON a1.DictionaryId1006B = dic5.DictionaryId AND dic5.BaseIsDelete = 0
            ) as tb1 WHERE   1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = @BaseIsDelete ");
                parameter.Add(DbParameterExtension.CreateDbParameter("@BaseIsDelete", param.BaseIsDelete));
                if (param.SchoolStage > 0)
                {
                    strSql.Append(" AND SchoolStage = @SchoolStage ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStage", param.SchoolStage));
                }
                if (param.DictionaryId1005 > 0)
                {
                    strSql.Append(" AND DictionaryId1005 = @DictionaryId1005 ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId1005", param.DictionaryId1005));
                }
                if (param.DictionaryId1006A >  0)
                {
                    strSql.Append(" AND DictionaryId1006A = @DictionaryId1006A ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId1006A", param.DictionaryId1006A));

                    if (param.DictionaryId1006B > 0)
                    {
                        strSql.Append(" AND DictionaryId1006B = @DictionaryId1006B ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId1006B", param.DictionaryId1006B));
                    }
                }
                if (!string.IsNullOrEmpty(param.VersionName))
                {
                    strSql.Append(" AND VersionName like @VersionName ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@VersionName", $"%{param.VersionName}%"));
                }
                if (!string.IsNullOrEmpty(param.TargetName))
                {
                    strSql.Append(" AND TargetName like @TargetName ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@TargetName", $"%{param.TargetName}%"));
                }
            }
            return parameter;
        }
        #endregion
    }
}
