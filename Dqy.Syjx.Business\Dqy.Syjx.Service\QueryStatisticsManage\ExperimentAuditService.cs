﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.QueryStatisticsManage;
using Dqy.Syjx.Model.Param.QueryStatisticsManage;
using Dqy.Syjx.Entity.OrganizationManage;
using System.Diagnostics.Metrics;

namespace Dqy.Syjx.Service.QueryStatisticsManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2024-12-04 11:33
    /// 描 述：服务类
    /// </summary>
    public class ExperimentAuditService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<ExperimentAuditEntity>> GetList(ExperimentAuditListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<ExperimentAuditEntity>> GetPageList(ExperimentAuditListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<ExperimentAuditEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<ExperimentAuditEntity>(id);
        }

        public async Task<ExperimentAuditEntity> GetEntityBySchoolId(long schoolId)
        {
            return await this.BaseRepository().FindEntity<ExperimentAuditEntity>(f => f.SchoolId == schoolId);
        }

        /// <summary>
        /// 查询审核列表
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<List<ExperimentAuditListEntity>> GetAuditList(ExperimentAuditListParam param, Pagination pagination)
        {
            StringBuilder sbSql = new StringBuilder();
            var parameter = new List<DbParameter>();
            StringBuilder strList = new StringBuilder();
            string where = $" CountyId = {param.UnitId}  ";
            if (param != null)
            {
                if (param.StageId != -1)
                {
                    where += @$" AND DictionaryId = {param.StageId}";
                }
                if (param.SchoolId != -1)
                {
                    where += @$" AND Id = {param.SchoolId}";
                }
                if (param.FunRoom != -1)
                {
                    where += @$" AND FunRoom = {param.FunRoom}";
                }
                if (param.Instrument != -1)
                {
                    where += $@" AND Instrument = {param.Instrument}";
                }
                if (param.OpenRate != -1)
                {
                    where += $@" AND OpenRate = {param.OpenRate}";
                }
            }
            string strBody = @$" SELECT * FROM
                            (SELECT U.Id ,U.Name AS SchoolName,UR.UnitId AS CountyId,D.DictionaryId,D.DicName AS SchoolProp,
	                               ISNULL(EA.FunRoom,0) AS FunRoom,ISNULL(EA.Instrument,0) AS Instrument,ISNULL(EA.OpenRate,0) AS OpenRate
                            FROM up_Unit AS U
                            INNER JOIN up_UnitRelation AS UR ON U.Id = UR.ExtensionObjId AND UR.ExtensionType = 3 AND U.UnitType = 3 AND U.BaseIsDelete = 0
                            LEFT JOIN up_SchoolExtension AS SE ON U.Id = SE.UnitId
                            LEFT JOIN sys_dictionary_relation AS R ON SE.SchoolProp = R.DictionaryId
                            LEFT JOIN sys_static_dictionary AS D ON R.DictionaryToId = D.DictionaryId AND D.TypeCode = '1002'
                            LEFT JOIN ex_ExperimentAudit AS EA ON U.Id = EA.SchoolId) A WHERE {where}";

            IEnumerable<ExperimentAuditListEntity> list = null;
            strList.Append(@$"SELECT Id,SchoolName ,CountyId,FunRoom,Instrument,OpenRate
                            FROM
                            (
	                           {strBody}
                            ) AS B
                            GROUP BY Id,SchoolName ,CountyId,FunRoom,Instrument,OpenRate");

            string strRole = @$"SELECT Id,DictionaryId,SchoolProp
                               FROM
                               (
                                  {strBody}
                               ) AS B
                               GROUP BY Id,DictionaryId ,SchoolProp";

            var listUser = await this.BaseRepository().FindList<ExperimentAuditListEntity>(strList.ToString(), parameter.ToArray(), pagination);
            var roleList = await this.BaseRepository().FindList<ExperimentAuditListEntity>(strRole);
            var userRole = roleList.GroupBy(x => x.Id)
                  .Select(g => new
                  {
                      Id = g.Key,
                      StrDictionaryIds = String.Join(",", g.Where(f => f.DictionaryId > 0).Select(x => x.DictionaryId)),
                      StrSchoolProps = String.Join(",", g.Where(f => f.DictionaryId > 0).Select(x => x.SchoolProp))
                  })
                  .ToList();

            var userList = listUser.Join(userRole, p1 => p1.Id, p2 => p2.Id, (p1, p2) =>
         new ExperimentAuditListEntity()
         {
             Id = p1.Id,
             SchoolName = p1.SchoolName,
             FunRoom = p1.FunRoom,
             Instrument = p1.Instrument,
             OpenRate = p1.OpenRate,
             StrDictionaryIds = p2.StrDictionaryIds,
             StrSchoolProps = p2.StrSchoolProps

         }).ToList();

            return userList;
        }

        /// <summary>
        /// 获取开出率数据
        /// </summary>
        /// <param name="countyId">区县Id</param>
        /// <returns></returns>
        public async Task<List<OpenRateEntity>> GetSchoolOpenRate(long countyId)
        {
            StringBuilder sbSql = new StringBuilder();
            var parameter = new List<DbParameter>();
            IEnumerable<OpenRateEntity> list = null;
            sbSql.Append(@$"SELECT  ISNULL(EB.Id,0) AS IsTrue,PEP.UnitId AS CountyId,UR.ExtensionObjId AS SchoolId
                            FROM ex_PlanExamExperiment AS PEE
                            INNER JOIN ex_PlanExamParameter AS PEP ON PEE.PlanParameterSetId = PEP.Id
                            INNER JOIN up_UnitRelation AS UR ON PEP.UnitId = UR.UnitId
                            LEFT JOIN ex_ExperimentBooking AS EB ON UR.ExtensionObjId = EB.SchoolId AND EB.IsMain = 0 AND EB.Statuz = 100 AND EB.BaseIsDelete = 0
		                             AND PEP.SchoolYearStart = EB.StaticSchoolYearStart AND PEP.SchoolTerm =  EB.StaticSchoolTerm AND PEE.ExperimentId = EB.ExperimentId
                            WHERE PEP.UnitId = {countyId} ");
            list = await this.BaseRepository().FindList<OpenRateEntity>(sbSql.ToString());
            return list.ToList();
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(ExperimentAuditEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(ExperimentAuditEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
               strSql = $" update ex_ExperimentAudit set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
               strSql = $"update ex_ExperimentAudit set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<ExperimentAuditEntity, bool>> ListFilter(ExperimentAuditListParam param)
        {
            var expression = LinqExtensions.True<ExperimentAuditEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
            }
            return expression;
        }
        #endregion
    }
}
