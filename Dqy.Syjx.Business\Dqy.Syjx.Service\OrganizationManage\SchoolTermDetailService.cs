﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.OrganizationManage;
using Dqy.Syjx.Model.Param.OrganizationManage;

namespace Dqy.Syjx.Service.OrganizationManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-11-17 13:46
    /// 描 述：学年学期详情服务类
    /// </summary>
    public class SchoolTermDetailService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<SchoolTermDetailEntity>> GetList(SchoolTermDetailListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<SchoolTermDetailEntity>> GetPageList(SchoolTermDetailListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<SchoolTermDetailEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<SchoolTermDetailEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(SchoolTermDetailEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(SchoolTermDetailEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task UpdateBatchDeleteTransForm(long id,long userid,Repository db)
        {
            await db.ExecuteBySql($" update bn_SchoolTermDetail set BaseIsDelete = 1 ,BaseModifyTime = '{DateTime.Now}',BaseModifierId ={userid}  where SchoolTermId = {id} ");
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
               strSql = $" update bn_SchoolTermDetail set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
               strSql = $"update bn_SchoolTermDetail set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<SchoolTermDetailEntity, bool>> ListFilter(SchoolTermDetailListParam param)
        {
            var expression = LinqExtensions.True<SchoolTermDetailEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (param.SchooltermId > 0)
                {
                    expression = expression.And(t => t.SchoolTermId == param.SchooltermId);
                }
            }
            return expression;
        }
        #endregion
    }
}
