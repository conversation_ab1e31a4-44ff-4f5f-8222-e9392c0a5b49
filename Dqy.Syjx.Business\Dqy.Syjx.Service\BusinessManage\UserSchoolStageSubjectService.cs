﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.BusinessManage;
using Dqy.Syjx.Model.Param.BusinessManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Entity.SystemManage;
using Dqy.Syjx.Entity.OrganizationManage;
using Dqy.Syjx.Model.Param.OrganizationManage;

namespace Dqy.Syjx.Service.BusinessManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-10-12 16:43
    /// 描 述：服务类
    /// </summary>
    public class UserSchoolStageSubjectService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<UserSchoolStageSubjectEntity>> GetList(UserSchoolStageSubjectListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<UserSchoolStageSubjectEntity>> GetPageList(UserSchoolStageSubjectListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ListFilter(param, strSql);
            var list = await this.BaseRepository().FindList<UserSchoolStageSubjectEntity>(strSql.ToString(), filter.ToArray(), pagination);
            return list.ToList();
        }

        public async Task<UserSchoolStageSubjectEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<UserSchoolStageSubjectEntity>(id);
        }

        /// <summary>
        /// 获取用户管理科目
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<IEnumerable<StaticDictionaryEntity>> GetSubjectByUser(UserSchoolStageSubjectListParam param, int schoolStage = 0)
        {
            //string sql = $@"  SELECT dic1.DictionaryId ,dic1.DicName
            //                  FROM  sys_static_dictionary AS dic1
            //                  INNER JOIN  f_split(
            //                       ( SELECT TOP(1) SubjectIdz FROM bn_UserSchoolStageSubject
            //                         WHERE IsCurrentUnit = {IsEnum.Yes.ParseToInt()} AND UnitId = {param.UnitId} AND UserId = {param.UserId} ) ,',') AS cl1
            //                         ON dic1.DictionaryId = cl1.col
            //                  WHERE dic1.TypeCode = '{DicTypeCodeEnum.Course.ParseToInt()}' AND dic1.BaseIsDelete = 0
            //              ";

            string sql = $@"SELECT DISTINCT dic1.DictionaryId ,dic1.DicName
                            FROM  sys_static_dictionary AS dic1
                            INNER JOIN  bn_UserSchoolStageSubject AS USS ON dic1.DictionaryId = USS.SubjectIdz
                            INNER JOIN
                            (
                                SELECT D3.DictionaryId,D3.DicName
                                FROM up_SchoolExtension AS SE
                                INNER JOIN sys_static_dictionary AS D1 ON SE.SchoolProp = D1.DictionaryId AND D1.TypeCode = '1001'
                                INNER JOIN sys_dictionary_relation AS DR ON D1.DictionaryId = DR.DictionaryId
                                INNER JOIN sys_static_dictionary AS D2 ON DR.DictionaryToId = D2.DictionaryId
                                INNER JOIN sys_dictionary_relation AS DR1 ON D2.DictionaryId = DR1.DictionaryToId
                                INNER JOIN sys_static_dictionary AS D3 ON DR1.DictionaryId = D3.DictionaryId AND D3 .TypeCode = '1005'
                                WHERE SE.UnitId = {param.UnitId} AND D3.Pid > 0
                            )A ON dic1.DictionaryId = A.DictionaryId
                            WHERE dic1.TypeCode = '1005' AND dic1.BaseIsDelete = 0 AND USS.BaseIsDelete = 0 AND USS.IsCurrentUnit = {IsEnum.Yes.ParseToInt()} AND USS.UnitId = {param.UnitId} AND USS.UserId = {param.UserId}";

            //根据学段查询学科
            if (!schoolStage.IsNullOrZero())
            {
                sql += ($@" AND dic1.DictionaryId IN (
	                            SELECT r.DictionaryId FROM  sys_dictionary_relation AS r
	                            INNER JOIN  sys_static_dictionary AS d ON r.DictionaryId = d.DictionaryId AND d.TypeCode = '{DicTypeCodeEnum.Course.ParseToInt()}'
	                            WHERE r.DictionaryToId = '{schoolStage}'
                            )");
            }
            var list = await this.BaseRepository().FindList<StaticDictionaryEntity>(sql);
            return list;
        }
        /// <summary>
        /// 获取用户管理学段
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<IEnumerable<StaticDictionaryEntity>> GetSchoolStageByUser(UserSchoolStageSubjectListParam param)
        {
            //string sql = $@"  SELECT dic1.DictionaryId ,dic1.DicName
            //                  FROM  sys_static_dictionary AS dic1
            //                  INNER JOIN  f_split(
            //                       ( SELECT TOP(1) SchoolStageIdz FROM bn_UserSchoolStageSubject
            //                         WHERE IsCurrentUnit = {IsEnum.Yes.ParseToInt()} AND UnitId = {param.UnitId} AND UserId = {param.UserId} AND BaseIsDelete = 0 ) ,',') AS cl1
            //                         ON dic1.DictionaryId = cl1.col
            //                  WHERE dic1.TypeCode = '{DicTypeCodeEnum.SchoolStage.ParseToInt()}' AND dic1.BaseIsDelete = 0
            //              ";

            string sql = $@"SELECT DISTINCT dic1.DictionaryId ,dic1.DicName
                        FROM  sys_static_dictionary AS dic1
                        INNER JOIN  bn_UserSchoolStageSubject AS USS ON dic1.DictionaryId = USS.SchoolStageIdz
                        INNER JOIN
                        (
	                        SELECT D2.DictionaryId,D2.DicName
	                        FROM up_SchoolExtension AS SE
	                        INNER JOIN sys_static_dictionary AS D1 ON SE.SchoolProp = D1.DictionaryId AND D1.TypeCode = '1001'
	                        INNER JOIN sys_dictionary_relation AS DR ON D1.DictionaryId = DR.DictionaryId
	                        INNER JOIN sys_static_dictionary AS D2 ON DR.DictionaryToId = D2.DictionaryId
	                        WHERE SE.UnitId = {param.UnitId}
                        ) A ON dic1.DictionaryId = A.DictionaryId
                        WHERE dic1.TypeCode = '1002' AND dic1.BaseIsDelete = 0 AND USS.BaseIsDelete = 0 AND USS.IsCurrentUnit = {IsEnum.Yes.ParseToInt()} AND USS.UnitId = {param.UnitId} AND USS.UserId = {param.UserId}";
            if (!param.CourseId.IsNullOrZero())
            {
                //根据学科获取学段
                sql += $" and dic1.DictionaryId IN (SELECT DictionaryToId FROM  sys_dictionary_relation WHERE DictionaryId = {param.CourseId}  )";
            }
            var list = await this.BaseRepository().FindList<StaticDictionaryEntity>(sql);
            return list;
        }

        /// <summary>
        /// 根据单位Id、学段获取实验员授权信息
        /// </summary>
        /// <param name="unitId"></param>
        /// <param name="unitStageIdz"></param>
        /// <returns></returns>
        public async Task<List<UserSchoolStageSubjectEntity>> GetStageSubjectList(UserSchoolStageSubjectListParam param,long unitId,string unitStageIdz)
        {
            //此脚本存在严重bug 当bn_UserSchoolStageSubject中 SubjectIdz 值为多个的时候，如 '1005002,1005003'时，ON A.SubjectId = USS.SubjectIdz会报错
            string sql = $@"SELECT * FROM
                            (
                            SELECT SchoolStageId,SchoolStageName,SubjectId,SubjectName,ISNULL(USS.Id,0) AS Id,USS.UnitId,USS.IsCurrentUnit,USS.UserId,USS.SchoolStageIdz,
	                        USS.SubjectIdz,USS.Remark,U.RealName,UES.IsExperimenter,UES.ExperimenterNature,
                            (CASE UES.ExperimenterNature WHEN 1 THEN '专职' WHEN  2 THEN '兼职' ELSE '' END) AS ExperimenterNatureName,
		                    StageSequence,SubjectSequence,Nature
                            FROM
                            (
	                            SELECT SSD.DictionaryId AS SubjectId,SSD.DicName AS SubjectName,SG.DictionaryId AS SchoolStageId,SG.DicName AS SchoolStageName,
		                               SG.Sequence AS StageSequence,SSD.Sequence AS SubjectSequence,SSD.Nature
	                            FROM  sys_static_dictionary  AS SSD
	                            INNER JOIN  sys_dictionary_relation AS DR ON SSD.DictionaryId = DR.DictionaryId
	                            INNER JOIN  sys_static_dictionary SG ON DR.DictionaryToId = SG.DictionaryId AND SG.TypeCode = '1002'
	                            WHERE SSD.TypeCode = '1005' AND SSD.Pid > 0 AND DR.DictionaryToId IN({unitStageIdz})
                            ) AS A
                            LEFT JOIN  bn_UserSchoolStageSubject AS USS ON A.SubjectId = USS.SubjectIdz AND A.SchoolStageId = USS.SchoolStageIdz AND USS.UnitId = {unitId} AND USS.BaseIsDelete = 0
                            LEFT JOIN  SysUser AS U ON USS.UserId = U.Id
                            LEFT JOIN  up_UserExtension AS UES ON USS.UserId = UES.SysUserId  AND UES.BaseIsDelete = 0
                            ) B WHERE 1 = 1 ";
            if(param.SchoolStage > 0)
            {
                sql += $" AND SchoolStageId = {param.SchoolStage}";
            }
            if(param.SubjectId > 0)
            {
                sql += $" AND SubjectId = {param.SubjectId}";
            }
            if (!string.IsNullOrEmpty(param.RealName))
            {
                sql += $" AND RealName LIKE '%{param.RealName}%'";
            }
            if (param.UserId > 0)
            {
                sql += $" AND UserId = {param.UserId}";
            }
            if (param.Nature > 0)
            {
                sql += $" AND Nature = {param.Nature}";
            }
            if (param.Id > 0)
            {
                sql += $" AND Id <> {param.Id}";
            }
            sql += " ORDER BY StageSequence,SubjectSequence ";
            var list = await this.BaseRepository().FindList<UserSchoolStageSubjectEntity>(sql);
            return list.ToList();

        }

        /// <summary>
        /// 根据单位Id、用户Id、学校学段集合获取用户适用学科学段信息
        /// </summary>
        /// <param name="unitId"></param>
        /// <param name="userId"></param>
        /// <param name="unitStageIdz"></param>
        /// <returns></returns>
        public async Task<List<StaticDictionaryEntity>> GetUserSchoolStageSubject(long unitId,long userId, string unitStageIdz)
        {

            string sql = $@"SELECT DISTINCT (CAST(USS.SchoolStageIdz AS VARCHAR)+','+CAST(USS.SubjectIdz AS VARCHAR)) AS DictionaryIds,(DR1.DicName+'->'+DR2.DicName) AS DictionaryNames
                            FROM  bn_UserSchoolStageSubject AS USS
                            INNER JOIN  sys_static_dictionary AS  DR1 ON USS.SchoolStageIdz = DR1.DictionaryId AND DR1.TypeCode = '1002'
                            INNER JOIN  sys_static_dictionary AS DR2 ON USS.SubjectIdz = DR2.DictionaryId AND DR2.TypeCode = '1005'
                            WHERE USS.BaseIsDelete = 0 AND USS.UnitId = {unitId} AND USS.SchoolStageIdz IN({unitStageIdz}) AND USS.UserId = {userId} ";

            var list = await this.BaseRepository().FindList<StaticDictionaryEntity>(sql);
            return list.ToList();
        }

        /// <summary>
        /// 根据用户Id获取学科、学段信息
        /// </summary>
        /// <param name="unitId"></param>
        /// <param name="userId"></param>
        /// <param name="unitStageIdz"></param>
        /// <returns></returns>
        public async Task<List<StaticDictionaryEntity>> GetSchoolCourseByUser(long unitId, long userId, string unitStageIdz)
        {
            string sql = $@"SELECT DISTINCT USS.SubjectIdz AS DictionaryId,DR2.DicName AS DicName,
                            USS.SchoolStageIdz AS DictionaryIds,DR1.DicName AS DictionaryNames
                            FROM  bn_UserSchoolStageSubject AS USS
                            INNER JOIN  sys_static_dictionary AS  DR1 ON USS.SchoolStageIdz = DR1.DictionaryId AND DR1.TypeCode = '1002'
                            INNER JOIN  sys_static_dictionary AS DR2 ON USS.SubjectIdz = DR2.DictionaryId AND DR2.TypeCode = '1005'
                            WHERE USS.BaseIsDelete = 0 AND USS.UnitId = {unitId} AND USS.SchoolStageIdz IN({unitStageIdz}) AND USS.UserId = {userId} ";

            var list = await this.BaseRepository().FindList<StaticDictionaryEntity>(sql);
            return list.ToList();
        }

        /// <summary>
        /// 实验员更换下拉框列表
        /// </summary>
        /// <param name="unitId">单位Id</param>
        /// <param name="excludeUserId">排除用户Id</param>
        /// <param name="roleId">角色Id</param>
        /// <param name="subjectIdz">科目Id</param>
        /// <returns></returns>
        public async Task<List<UserEntity>> GetUserChangeList(long unitId,long excludeUserId,long roleId,string subjectIdz)
        {
            string sql = @$"SELECT DISTINCT USS.UserId AS Id,SU.RealName,USS.SubjectIdz,USS.UnitId,R.Id AS RoleId
                FROM  bn_UserSchoolStageSubject AS USS
                INNER JOIN  SysUser AS SU ON USS.UserId = SU.Id
	            INNER JOIN SysUserBelong AS SUB ON SU.Id = SUB.UserId
	            INNER JOIN SysRole AS R ON SUB.BelongId = R.Id AND SUB.BelongType = 2
                WHERE USS.BaseIsDelete = 0 AND SU.BaseIsDelete = 0 AND USS.UnitId = {unitId}
	            AND USS.UserId <> {excludeUserId} AND R.Id = {roleId} AND SubjectIdz = {subjectIdz}
	            ORDER BY RealName ASC";
            var list = await this.BaseRepository().FindList<UserEntity>(sql);
            return list.ToList();
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(UserSchoolStageSubjectEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }
        public async Task SaveTransForm(UserSchoolStageSubjectEntity entity, Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }
        public async Task DeleteTransForm(string ids, Repository db)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
               strSql = $" update bn_UserSchoolStageSubject set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
               strSql = $"update bn_UserSchoolStageSubject set BaseIsDelete = 1 where id = {ids}";
            }
            await db.ExecuteBySql(strSql);
        }

        /// <summary>
        /// 根据Id删除数据
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        public async Task DeleteById(long Id)
        {
            string strSql=$"DELETE FROM  bn_UserSchoolStageSubject WHERE Id = {Id}";
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<UserSchoolStageSubjectEntity, bool>> ListFilter(UserSchoolStageSubjectListParam param)
        {
            var expression = LinqExtensions.True<UserSchoolStageSubjectEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (param.UnitId > 0)
                {
                    expression = expression.And(t => t.UnitId == param.UnitId);
                }
                if (param.UserId > 0)
                {
                    expression = expression.And(t => t.UserId == param.UserId);
                }
                if(param.Id > 0)
                {
                    expression = expression.And(t => t.Id != param.Id);
                }
                if(param.SubjectId > 0)
                {
                    expression = expression.And(t => t.SubjectIdz == param.SubjectId);
                }
                if(param.SchoolStage > 0)
                {
                    expression = expression.And(t => t.SchoolStageIdz == param.SchoolStage);
                }
            }
            return expression;
        }


        /// <summary>
        /// 已查，不需要修改，已没有页面调用
        /// </summary>
        /// <param name="param"></param>
        /// <param name="strSql"></param>
        /// <returns></returns>
        private List<DbParameter> ListFilter(UserSchoolStageSubjectListParam param, StringBuilder strSql)
        {
            strSql.Append($@"  SELECT * From (
                             SELECT
                                a1.Id ,
                                a1.BaseIsDelete ,
                                a1.BaseCreateTime ,
                                a1.BaseModifyTime ,
                                a1.BaseCreatorId ,
                                a1.BaseModifierId ,
                                a1.BaseVersion ,
                                a1.UnitId ,
                                a1.IsCurrentUnit ,
                                a1.UserId ,
                                a1.SchoolStageIdz ,
                                a1.SubjectIdz ,
                                a1.Remark ,
                                s2.RealName ,
                               ''' AS SubjectNamez ,
                                es4.IsExperimenter ,
								es4.ExperimenterNature
                                FROM  bn_UserSchoolStageSubject AS a1
                                INNER JOIN  SysUser AS s2 ON s2.BaseIsDelete = 0 AND a1.UserId = s2.Id
                                INNER JOIN  up_UnitRelation AS u3 ON s2.Id = u3.ExtensionObjId AND u3.ExtensionType = {UnitRelationTypeEnum.User.ParseToInt()} AND a1.UnitId = u3.UnitId
                                LEFT JOIN  bn_UserExperimenterSet AS es4 ON a1.UnitId = es4.UnitId AND a1.UserId = es4.SysUserId AND es4.IsCurrentUnit = {IsEnum.Yes.ParseToInt()} AND es4.BaseIsDelete = 0
                          ) as tb1 WHERE   1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = @BaseIsDelete ");
                parameter.Add(DbParameterExtension.CreateDbParameter("@BaseIsDelete", param.BaseIsDelete));
                if (param.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    strSql.Append(" AND UnitId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }

                if (param.IsCurrentUnit > -1)
                {
                    strSql.Append(" AND IsCurrentUnit = @IsCurrentUnit ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@IsCurrentUnit", param.IsCurrentUnit));
                }
                if (!string.IsNullOrEmpty(param.RealName))
                {
                    strSql.Append(" AND RealName like @RealName ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@RealName",$"%{param.RealName}%" ));
                }
                if (param.SchoolStage > 0)
                {
                    strSql.Append($" AND SchoolStageIdz = {param.SchoolStage} ");
                }
                if (param.DictionaryId1005 > 0)
                {
                    strSql.Append($" AND SubjectIdz = {param.DictionaryId1005} ");
                }
            }
            return parameter;
        }


        #endregion
    }
}
