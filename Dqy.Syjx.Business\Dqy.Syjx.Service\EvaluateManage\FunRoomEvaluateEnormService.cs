﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.EvaluateManage;
using Dqy.Syjx.Model.Param.EvaluateManage;

namespace Dqy.Syjx.Service.EvaluateManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-11-24 15:39
    /// 描 述：指标参数服务类
    /// </summary>
    public class FunRoomEvaluateEnormService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<FunRoomEvaluateEnormEntity>> GetList(FunRoomEvaluateEnormListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<FunRoomEvaluateEnormEntity>> GetPageList(FunRoomEvaluateEnormListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ListFilter(param, strSql);
            var list = await this.BaseRepository().FindList<FunRoomEvaluateEnormEntity>(strSql.ToString(), filter.ToArray(), pagination);
            return list.ToList();
        }

        public async Task<List<FunRoomEvaluateEnormEntity>> GetStandardPageList(FunRoomEvaluateEnormListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = StandardListFilter(param, strSql);
            var list = await this.BaseRepository().FindList<FunRoomEvaluateEnormEntity>(strSql.ToString(), filter.ToArray(), pagination);
            return list.ToList();
        }

        public async Task<FunRoomEvaluateEnormEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<FunRoomEvaluateEnormEntity>(id);
        }

        public async Task<FunRoomEvaluateEnormEntity> GetEntity(FunRoomEvaluateEnormListParam param)
        {
            return await this.BaseRepository().FindEntity<FunRoomEvaluateEnormEntity>(p => p.EvaluateStandardId == param.EvaluateStandardId && p.RailStart == param.RailStart && p.RailEnd == param.RailEnd && p.StandardLevel == param.Level); ;
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(FunRoomEvaluateEnormEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(FunRoomEvaluateEnormEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task UpdateStateForm(long evaluatestandardid, string ids,int state)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update bn_FunRoomEvaluateEnorm set Statuz = {state} where Id in ({ids}) AND EvaluateStandardId = {evaluatestandardid} ";
            }
            else
            {
                strSql = $"update bn_FunRoomEvaluateEnorm set Statuz = {state} where id = {ids} AND EvaluateStandardId = {evaluatestandardid}  ";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        public async Task UpdateEvaluateForm(long evaluatestandardid, string ids, int isevaluate)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");

            // 使用参数化查询和跨数据库兼容的 IN 语法
            var parameters = new List<DbParameter>
            {
                DbParameterExtension.CreateDbParameter("@IsEvaluate", isevaluate),
                DbParameterExtension.CreateDbParameter("@EvaluateStandardId", evaluatestandardid)
            };

            string strSql = $"UPDATE bn_FunRoomEvaluateEnorm SET IsEvaluate = @IsEvaluate WHERE Id IN ({ids}) AND EvaluateStandardId = @EvaluateStandardId";

            await this.BaseRepository().ExecuteBySql(strSql, parameters.ToArray());
        }
        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update bn_FunRoomEvaluateEnorm set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
                strSql = $"update bn_FunRoomEvaluateEnorm set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<FunRoomEvaluateEnormEntity, bool>> ListFilter(FunRoomEvaluateEnormListParam param)
        {
            var expression = LinqExtensions.True<FunRoomEvaluateEnormEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (param.OptType == 1)
                {
                    expression = expression.And(t => t.EvaluateStandardId == param.StandardId);
                    expression = expression.And(t => t.StandardLevel == param.Level);
                    if (param.Id > 0)
                    {
                        expression = expression.And(t => t.Id != param.Id);
                    }
                    /**轨数判断
                     * 1:开始和结束轨数不能在其他轨数中
                     * 2：开始和结束轨数不能把其他轨数包含
                     */
                    expression = expression.And(t => (t.RailStart <= param.RailStart && param.RailStart <= t.RailEnd) || (t.RailStart <= param.RailEnd && param.RailEnd <= t.RailEnd) || (param.RailStart <= t.RailStart && t.RailStart <= param.RailEnd));
                }
                else
                {
                    if (!string.IsNullOrEmpty(param.Ids))
                    {
                        long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                        expression = expression.And(t => idArr.Contains(t.Id.Value));
                    }
                    if (param.StandardId > 0)
                    {
                        expression = expression.And(t => t.EvaluateStandardId == param.StandardId);
                    }
                }
            }
            return expression;
        }

        private List<DbParameter> ListFilter(FunRoomEvaluateEnormListParam param, StringBuilder strSql)
        {
            strSql.Append(@"  SELECT * From (
                 SELECT
                    a1.Id ,
                    a1.BaseIsDelete ,
                    a1.BaseCreateTime ,
                    a1.BaseModifyTime ,
                    a1.BaseCreatorId ,
                    a1.BaseModifierId ,
                    a1.BaseVersion ,
                    a1.EvaluateStandardId ,
                    a1.RailStart ,
                    a1.RailEnd ,
                    a1.StandardLevel ,
                    a1.RoomNum ,
                    a1.RoomArea ,
                    a1.IsEvaluate ,
                    a1.Statuz ,
                    a1.Remark ,
                    a1.RoomNum * a1.RoomArea AS TotalRoomArea ,
                    sd1.DicName AS LevelName
                    FROM  bn_FunRoomEvaluateEnorm AS a1
                    INNER JOIN  sys_static_dictionary AS sd1 ON a1.StandardLevel = sd1.DictionaryId AND sd1.BaseIsDelete = 0
            ) as tb1 WHERE   1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = @BaseIsDelete ");
                parameter.Add(DbParameterExtension.CreateDbParameter("@BaseIsDelete", param.BaseIsDelete));
                if (param.StandardId > 0)
                {
                    strSql.Append(" AND EvaluateStandardId = @EvaluateStandardId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@EvaluateStandardId", param.StandardId));
                }
                if (param.Level > 0)
                {
                    strSql.Append(" AND StandardLevel = @StandardLevel ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@StandardLevel", param.Level));
                }
                if (param.IsEvaluate >= 0)
                {
                    strSql.Append(" AND IsEvaluate = @IsEvaluate ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@IsEvaluate", param.IsEvaluate));
                }
            }
            return parameter;
        }


        /// <summary>
        /// 实验室达标结果表
        /// </summary>
        /// <param name="param"></param>
        /// <param name="strSql"></param>
        /// <returns></returns>
        private List<DbParameter> StandardListFilter(FunRoomEvaluateEnormListParam param, StringBuilder strSql)
        {
            strSql.Append(@$" SELECT * From (
                                SELECT  EP.Id ,ES.SchoolStage ,ES.DictionaryId1005 ,SD.DicName AS SchoolStageName ,SD2.DicName AS CourseName ,
                                        ES.DictionaryId1006A ,ES.DictionaryId1006B ,ES.TargetName ,
                                        SD3.DicName AS OneClassName ,SD4.DicName AS TwoClassName ,
                                        FR.FunRoomNum ,EE.RoomNum ,( ISNULL(FR.FunRoomNum,0) - EE.RoomNum ) AS RoomNumDifference ,
                                        FR.UseArea ,(EE.RoomArea * EE.RoomNum) AS RoomArea ,( ISNULL(FR.UseArea,0) - (EE.RoomArea * EE.RoomNum) ) AS RoomAreaDifference ,EE.StandardLevel
                                FROM     bn_FunRoomEvaluateProject AS EP
                                INNER JOIN  bn_FunRoomEvaluateProjectVersion AS EPV ON EP.Id = EPV.EvaluateProjectId AND EPV.BaseIsDelete = 0
                                INNER JOIN  bn_FunRoomEvaluateStandard AS ES ON EPV.EvaluateStandardId = ES.Id AND ES.BaseIsDelete = 0
                                INNER JOIN  bn_FunRoomEvaluateEnorm AS EE ON ES.Id = EE.EvaluateStandardId AND EE.BaseIsDelete = 0
                                                        AND EE.IsEvaluate = 1 AND EE.Statuz = 1 AND RailStart <= {param.TrackNum} AND RailEnd >= {param.TrackNum}
                                INNER JOIN  sys_static_dictionary AS SD ON ES.SchoolStage = SD.DictionaryId AND SD.TypeCode = '1002' AND SD.BaseIsDelete = 0
                                INNER JOIN  sys_static_dictionary AS SD2 ON ES.DictionaryId1005 = SD2.DictionaryId AND SD2.TypeCode = '1005' AND SD2.BaseIsDelete = 0
                                INNER JOIN  sys_static_dictionary AS SD3 ON ES.DictionaryId1006A = SD3.DictionaryId AND SD3.TypeCode = '1006' AND SD3.BaseIsDelete = 0
                                INNER JOIN  sys_static_dictionary AS SD4 ON ES.DictionaryId1006B = SD4.DictionaryId AND SD4.TypeCode = '1006' AND SD4.BaseIsDelete = 0
                                 LEFT JOIN ( SELECT  @SchoolStageParam AS SchoolStage ,fr1.DictionaryId1005 ,fr1.DictionaryId1006A ,fr1.DictionaryId1006B ,
                                                    COUNT(1) AS FunRoomNum ,ISNULL(SUM(fr1.UseArea), 0) AS UseArea
                                            FROM     bn_FunRoom AS fr1
                                            WHERE fr1.UnitId = @SchoolId AND fr1.Statuz = 1
                                GROUP BY fr1.DictionaryId1005 ,fr1.DictionaryId1006A ,fr1.DictionaryId1006B
                              ) FR ON ES.SchoolStage = FR.SchoolStage AND ES.DictionaryId1006A = FR.DictionaryId1006A
                                      AND ES.DictionaryId1006B = FR.DictionaryId1006B AND ES.DictionaryId1005 = FR.DictionaryId1005
                            WHERE  EP.BaseIsDelete = 0 AND EP.Id = {param.FunRoomEvaluateProjectId}
                          ) as T WHERE  1 = 1 ");
            var parameter = new List<DbParameter>();
            parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
            parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStageParam", param.SchoolStageId ?? 0));
            if (param != null)
            {
                if (!param.SchoolStageId.IsNullOrZero())
                {
                    strSql.Append(" AND SchoolStage = @SchoolStage ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStage", param.SchoolStageId));
                }
                if (!param.Level.IsNullOrZero())
                {
                    strSql.Append(" AND StandardLevel = @StandardLevel ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@StandardLevel", param.Level));
                }
                if (!param.OneClassId.IsNullOrZero())
                {
                    strSql.Append(" AND DictionaryId1006A = @OneClassId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@OneClassId", param.OneClassId));
                }
                if (!param.CourseId.IsNullOrZero())
                {
                    strSql.Append(" AND DictionaryId1005 = @CourseId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (!string.IsNullOrWhiteSpace(param.KeyWord))
                {
                    strSql.Append($" AND TargetName LIKE '%{param.KeyWord.Trim()}%'  ");
                }
                if (param.IsOnlyShowNoStandard == 1)
                {
                    strSql.Append(" AND (RoomNumDifference < 0 OR RoomAreaDifference < 0) ");
                }
            }
            return parameter;
        }
        #endregion
    }
}
