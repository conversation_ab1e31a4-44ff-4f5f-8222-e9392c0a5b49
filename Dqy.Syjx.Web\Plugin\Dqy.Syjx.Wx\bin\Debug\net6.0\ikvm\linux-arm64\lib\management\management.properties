#####################################################################
#	Default Configuration File for Java Platform Management
#####################################################################
#
# The Management Configuration file (in java.util.Properties format)
# will be read if one of the following system properties is set:
#    -Dcom.sun.management.jmxremote.port=<port-number>
# or -Dcom.sun.management.snmp.port=<port-number>
# or -Dcom.sun.management.config.file=<this-file>
#
# The default Management Configuration file is:
#
#       $JRE/lib/management/management.properties
#
# Another location for the Management Configuration File can be specified
# by the following property on the Java command line:
#
#    -Dcom.sun.management.config.file=<this-file>
#
# If -Dcom.sun.management.config.file=<this-file> is set, the port
# number for the management agent can be specified in the config file
# using the following lines:
#
# ################ Management Agent Port #########################
#
# For setting the JMX RMI agent port use the following line
# com.sun.management.jmxremote.port=<port-number>
#
# For setting the SNMP agent port use the following line
# com.sun.management.snmp.port=<port-number>

#####################################################################
#                   Optional Instrumentation
#####################################################################
#
# By default only the basic instrumentation with low overhead is on.
# The following properties allow to selectively turn on optional
# instrumentation which are off by default and may have some
# additional overhead.
#
# com.sun.management.enableThreadContentionMonitoring
#
#      This option enables thread contention monitoring if the
#      Java virtual machine supports such instrumentation.
#      Refer to the specification for the java.lang.management.ThreadMBean
#      interface - see isThreadContentionMonitoringSupported() method.
#

# To enable thread contention monitoring, uncomment the following line
# com.sun.management.enableThreadContentionMonitoring

#####################################################################
#			SNMP Management Properties
#####################################################################
#
# If the system property -Dcom.sun.management.snmp.port=<port-number>
# is set then
#	- The SNMP agent (with the Java virtual machine MIB) is started
#	  that listens on the specified port for incoming SNMP requests.
#	- the following properties for read for SNMP management.
#
# The configuration can be specified only at startup time.
# Later changes to the above system property (e.g. via setProperty method), this
# config file, or the ACL file has no effect to the running SNMP agent.
#

#
# ##################### SNMP Trap Port #########################
#
# com.sun.management.snmp.trap=<trap-destination-port-number>
#      Specifies the remote port number at which managers are expected
#      to listen for trap. For each host defined in the ACL file,
#      the SNMP agent will send traps at <host>:<trap-destination-port-number>
#      Default for this property is 162.
#

# To set port for sending traps to a different port use the following line
# com.sun.management.snmp.trap=<trap-destination-port-number>

#
# ################ SNMP listen interface #########################
#
# com.sun.management.snmp.interface=<InetAddress>
#      Specifies the local interface on which the SNMP agent will bind.
#      This is useful when running on machines which have several
#      interfaces defined. It makes it possible to listen to a specific
#      subnet accessible through that interface.
#      Default for this property is "localhost".
#
#      The format of the value for that property is any string accepted
#      by java.net.InetAddress.getByName(String).
#

# For restricting the port on which SNMP agent listens use the following line
# com.sun.management.snmp.interface=<InetAddress>

#
# #################### SNMP ACL file #########################
#
# com.sun.management.snmp.acl=true|false
#      Default for this property is true. (Case for true/false ignored)
#      If this property is specified as false then the ACL file
#      is not checked:  all manager hosts are allowed all access.
#

# For SNMP without checking ACL file uncomment the following line
# com.sun.management.snmp.acl=false

#
# com.sun.management.snmp.acl.file=filepath
#      Specifies location for ACL file
#      This is optional - default location is
#      $JRE/lib/management/snmp.acl
#
#      If the property "com.sun.management.snmp.acl" is set to false,
#      then this property and the ACL file are ignored.
#      Otherwise the ACL file must exist and be in the valid format.
#      If the ACL file is empty or non existent then no access is allowed.
#
#      The SNMP agent will read the ACL file at startup time.
#      Modification to the ACL file has no effect to any running SNMP
#      agents which read that ACL file at startup.
#

# For a non-default acl file location use the following line
# com.sun.management.snmp.acl.file=filepath

#####################################################################
#			RMI Management Properties
#####################################################################
#
# If system property -Dcom.sun.management.jmxremote.port=<port-number>
# is set then
#     - A MBean server is started
#     - JRE Platform MBeans are registered in the MBean server
#     - RMI connector is published  in a private readonly registry at
#       specified port using a well known name, "jmxrmi"
#     - the following properties are read for JMX remote management.
#
# The configuration can be specified only at startup time.
# Later changes to above system property (e.g. via setProperty method),
# this config file, the password file, or the access file have no effect to the
# running MBean server, the connector, or the registry.
#

#
# ########## RMI connector settings for local management ##########
#
# com.sun.management.jmxremote.local.only=true|false
#      Default for this property is true. (Case for true/false ignored)
#      If this property is specified as true then the local JMX RMI connector
#      server will only accept connection requests from clients running on
#      the host where the out-of-the-box JMX management agent is running.
#      In order to ensure backwards compatibility this property could be
#      set to false. However, deploying the local management agent in this
#      way is discouraged because the local JMX RMI connector server will
#      accept connection requests from any client either local or remote.
#      For remote management the remote JMX RMI connector server should
#      be used instead with authentication and SSL/TLS encryption enabled.
#

# For allowing the local management agent accept local
# and remote connection requests use the following line
# com.sun.management.jmxremote.local.only=false

#
# ###################### RMI SSL #############################
#
# com.sun.management.jmxremote.ssl=true|false
#      Default for this property is true. (Case for true/false ignored)
#      If this property is specified as false then SSL is not used.
#

# For RMI monitoring without SSL use the following line
# com.sun.management.jmxremote.ssl=false

# com.sun.management.jmxremote.ssl.config.file=filepath
#      Specifies the location of the SSL configuration file. A properties
#      file can be used to supply the keystore and truststore location and
#      password settings thus avoiding to pass them as cleartext in the
#      command-line.
#
#      The current implementation of the out-of-the-box management agent will
#      look up and use the properties specified below to configure the SSL
#      keystore and truststore, if present:
#          javax.net.ssl.keyStore=<keystore-location>
#          javax.net.ssl.keyStorePassword=<keystore-password>
#          javax.net.ssl.trustStore=<truststore-location>
#          javax.net.ssl.trustStorePassword=<truststore-password>
#      Any other properties in the file will be ignored. This will allow us
#      to extend the property set in the future if required by the default
#      SSL implementation.
#
#      If the property "com.sun.management.jmxremote.ssl" is set to false,
#      then this property is ignored.
#

# For supplying the keystore settings in a file use the following line
# com.sun.management.jmxremote.ssl.config.file=filepath

# com.sun.management.jmxremote.ssl.enabled.cipher.suites=<cipher-suites>
#      The value of this property is a string that is a comma-separated list
#      of SSL/TLS cipher suites to enable. This property can be specified in
#      conjunction with the previous property "com.sun.management.jmxremote.ssl"
#      in order to control which particular SSL/TLS cipher suites are enabled
#      for use by accepted connections. If this property is not specified then
#      the SSL/TLS RMI Server Socket Factory uses the SSL/TLS cipher suites that
#      are enabled by default.
#

# com.sun.management.jmxremote.ssl.enabled.protocols=<protocol-versions>
#      The value of this property is a string that is a comma-separated list
#      of SSL/TLS protocol versions to enable. This property can be specified in
#      conjunction with the previous property "com.sun.management.jmxremote.ssl"
#      in order to control which particular SSL/TLS protocol versions are
#      enabled for use by accepted connections. If this property is not
#      specified then the SSL/TLS RMI Server Socket Factory uses the SSL/TLS
#      protocol versions that are enabled by default.
#

# com.sun.management.jmxremote.ssl.need.client.auth=true|false
#      Default for this property is false. (Case for true/false ignored)
#      If this property is specified as true in conjunction with the previous
#      property "com.sun.management.jmxremote.ssl" then the SSL/TLS RMI Server
#      Socket Factory will require client authentication.
#

# For RMI monitoring with SSL client authentication use the following line
# com.sun.management.jmxremote.ssl.need.client.auth=true

# com.sun.management.jmxremote.registry.ssl=true|false
#      Default for this property is false. (Case for true/false ignored)
#      If this property is specified as true then the RMI registry used
#      to bind the RMIServer remote object is protected with SSL/TLS
#      RMI Socket Factories that can be configured with the properties:
#          com.sun.management.jmxremote.ssl.config.file
#          com.sun.management.jmxremote.ssl.enabled.cipher.suites
#          com.sun.management.jmxremote.ssl.enabled.protocols
#          com.sun.management.jmxremote.ssl.need.client.auth
#      If the two properties below are true at the same time, i.e.
#          com.sun.management.jmxremote.ssl=true
#          com.sun.management.jmxremote.registry.ssl=true
#      then the RMIServer remote object and the RMI registry are
#      both exported with the same SSL/TLS RMI Socket Factories.
#

# For using an SSL/TLS protected RMI registry use the following line
# com.sun.management.jmxremote.registry.ssl=true

#
# ################ RMI User authentication ################
#
# com.sun.management.jmxremote.authenticate=true|false
#      Default for this property is true. (Case for true/false ignored)
#      If this property is specified as false then no authentication is
#      performed and all users are allowed all access.
#

# For RMI monitoring without any checking use the following line
# com.sun.management.jmxremote.authenticate=false

#
# ################ RMI Login configuration ###################
#
# com.sun.management.jmxremote.login.config=<config-name>
#      Specifies the name of a JAAS login configuration entry to use when
#      authenticating users of RMI monitoring.
#
#      Setting this property is optional - the default login configuration
#      specifies a file-based authentication that uses the password file.
#
#      When using this property to override the default login configuration
#      then the named configuration entry must be in a file that gets loaded
#      by JAAS. In addition, the login module(s) specified in the configuration
#      should use the name and/or password callbacks to acquire the user's
#      credentials. See the NameCallback and PasswordCallback classes in the
#      javax.security.auth.callback package for more details.
#
#      If the property "com.sun.management.jmxremote.authenticate" is set to
#      false, then this property and the password & access files are ignored.
#

# For a non-default login configuration use the following line
# com.sun.management.jmxremote.login.config=<config-name>

#
# ################ RMI Password file location ##################
#
# com.sun.management.jmxremote.password.file=filepath
#      Specifies location for password file
#      This is optional - default location is
#      $JRE/lib/management/jmxremote.password
#
#      If the property "com.sun.management.jmxremote.authenticate" is set to
#      false, then this property and the password & access files are ignored.
#      Otherwise the password file must exist and be in the valid format.
#      If the password file is empty or non-existent then no access is allowed.
#

# For a non-default password file location use the following line
# com.sun.management.jmxremote.password.file=filepath

#
# ################ RMI Access file location #####################
#
# com.sun.management.jmxremote.access.file=filepath
#      Specifies location for access  file
#      This is optional - default location is
#      $JRE/lib/management/jmxremote.access
#
#      If the property "com.sun.management.jmxremote.authenticate" is set to
#      false, then this property and the password & access files are ignored.
#      Otherwise, the access file must exist and be in the valid format.
#      If the access file is empty or non-existent then no access is allowed.
#

# For a non-default password file location use the following line
# com.sun.management.jmxremote.access.file=filepath
