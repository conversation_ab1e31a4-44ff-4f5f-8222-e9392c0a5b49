﻿@using Dqy.Syjx.Entity.ArticleManager
@using Dqy.Syjx.Util.Model
@{
    Layout = "~/Views/Shared/_FormGray.cshtml";
    OperatorInfo operatorInfo = ViewBag.OperatorInfo;
    string unitName = null;
    string realName = null;
    string roleNames = null;
    string roleIds = null;
    int unitType = 0;
    long userId = 0;
    if (operatorInfo != null)
    {
        unitName = operatorInfo.UnitName ?? "";
        realName = operatorInfo.RealName ?? "";
        roleNames = operatorInfo.RoleNames ?? "";
        unitType = operatorInfo.UnitType;
        roleIds = "," + operatorInfo.RoleValues + ",";
        userId = operatorInfo.UserId.Value;
    }
    TData<List<ArticleCategoryEntity>> list = ViewBag.Article as TData<List<ArticleCategoryEntity>>;

    List<MenuEntity> menuList = ViewBag.MenuList;
}
@section header {
    @* <link href='@Url.Content("~/lib/kanban/comon0.css")' rel="stylesheet" /> *@
    <script src="~/lib/report/echarts/echarts.js"></script>
    <script src="~/lib/kanban/jquery.waypoints.min.js"></script>
    <script src="~/lib/kanban/jquery.countup.min.js"></script>
}

<style>
    .gray-bg {
        background-color: #fff !important;
    }

    .x_wrapper-content {
        width: 1682px;
        display: flex;
        margin: 13px auto;
    }

    .x_content_left {
        width: 1360px;
        flex-shrink: 0;
        margin-right: 12px;
    }

    .navUi {
        width: 100%;
        display: flex;
        justify-content: space-between;
        gap: 14px;
    }

        .navUi li {
            width: 20%;
            height: 110px;
            background: linear-gradient(to bottom, #e4eefe, #ffffff);
            display: flex;
            justify-content: center;
            align-items: center;
        }

    .page-title {
        font-size: 18px;
        color: #060606;
    }

    .x_menu {
        display: flex;
        justify-content: space-between;
        /* align-items: center; */
        flex-wrap: wrap;
        gap: 14px;
    }

        .x_menu .menu-class {
            width: 672px;
            height: 385px;
            background-color: #f4f8fe;
        }

            .x_menu .menu-class .class-text {
                height: 58px;
                width: 100%;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

    .class-text .text_left {
        font-size: 22px;
        color: #000000;
        margin-left: 33px;
    }

    .class-text .text_right {
        font-size: 14px;
        color: #868686;
        margin-right: 28px;
    }

    .x_date {
        width: 311px;
        height: 368px;
        border-radius: 5px;
        border: 1px solid rgba(125, 180, 248, 0.3);
        background: url(/image/时间背景图.png) no-repeat;
    }

    .x_Information {
        width: 311px;
        height: 561px;
        margin-top: 14px;
        background-color: #ffffff;
        border-radius: 5px;
        border: 1px solid rgba(125, 180, 248, 0.3);
    }

    .class-content {
    }

    .todo-items,
    .basic-data {
        margin: 36px 48px;
    }

    .custom-ordered-list {
        list-style-type: none; /* 移除默认的列表样式 */
        padding: 0;
    }

        .custom-ordered-list li {
            margin-bottom: 15px; /* 设置列表项之间的间距 */
            display: flex;
            align-items: center;
        }

    .custom-number {
        display: inline-block;
        width: 22px; /* 设置序号的大小 */
        height: 22px;
        font-family: Arial;
        font-weight: bold;
        font-size: 18px;
        color: #ffffff;
        line-height: 22px; /* 使文字垂直居中 */
        border-radius: 50%; /* 圆形 */
        text-align: center;
        margin-right: 10px; /* 序号和文本之间的间距 */
        background-color: #cbcbcb;
    }

    .process-button {
        display: inline-block;
        padding: 4px 14px;
        font-size: 14px;
        /* background-color: #7db4f8; */
        background-color: #0056b3;
        color: #fff; /* 白色文字 */
        border: none;
        border-radius: 8px;
        cursor: pointer;
        text-decoration: none;
        margin-left: auto; /* 将按钮推到右侧 */
    }

        /* .process-button:hover {
            background-color: #0056b3;  
        } */

    /* 第一个子元素 */
    .custom-ordered-list li:first-child .custom-number {
        background-color: blue; /* 或者其他你想要的样式 */
    }

    /* 第二个子元素 */
    .custom-ordered-list li:nth-child(2) .custom-number {
        background-color: green; /* 或者其他你想要的样式 */
    }

    /* 第三个子元素 */
    .custom-ordered-list li:nth-child(3) .custom-number {
        background-color: orange; /* 或者其他你想要的样式 */
    }

    .custom-hint {
        font-size: 15px;
        color: #000000;
    }

    .custom-item {
        color: blue;
        text-decoration: underline;
    }

    .basic-data {
        display: flex;
        flex-wrap: wrap;
    }

    .data-item {
        width: 50%;
        display: flex;
        align-items: center;
        margin-bottom: 39px;
    }

  

    .basic-content {
        margin-left: 16px;
    }

        .basic-content p:first-of-type {
            font-size: 14px;
            line-height: 14px;
            color: #666;
            margin: 0;
        }

        .basic-content p:last-of-type {
            font-size: 30px;
            line-height: 30px;
            color: #424242;
            font-weight: 500;
            margin: 10px 0 0 0;
        }

    .table-container {
        width: 638px;
        background: #fcfdff;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        padding: 13px 17px;
        margin: 0 19px;
        max-width: 800px;
    }

    table {
        width: 580px;
        margin: 0 auto;
        border-collapse: collapse;
    }

    thead {
        background-color: #ffffff;
        border-radius: 5px;
        border: 1px solid rgba(125, 180, 248, 0.3) !important;
    }

        thead tr {
            /* width: 560px; */
            height: 42px;
            padding: 0 10px;
            background-color: #ffffff;
        }

    tbody {
    }

    th,
    td {
        text-align: center;
    }

    th {
        font-size: 16px;
        color: #000000;
        font-weight: 400;
    }

    tbody td {
        font-size: 16px;
        padding: 6px 2px;
    }

    .text-left {
        padding-left: 10px;
        text-align: left;
    }

    .overdue span {
        font-size: 16px;
        color: #ff0a0a;
        display: inline-block;
        padding: 2px 14px;
        background: #fff3e5;
        border-radius: 5px;
    }

    .platform-usage th {
        font-size: 14px;
    }

    .platform-usage td {
        font-size: 14px;
        padding: 8px 2px;
    }

    .x_date {
        background-color: #ffffff;
        border-radius: 12px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        padding: 0 20px 20px;
        text-align: center;
        width: 311px;
    }

        .x_date .date-info {
            /* margin-bottom: 20px; */
            width: 180px;
            margin: 0px auto;
        }

    .date-info .day {
        font-size: 20px;
        font-weight: bold;
        color: #333;
        /* margin-bottom: 10px; */
    }

        .date-info .day .day-number {
            font-family: Candara;
            font-weight: 400;
            font-size: 76px;
            color: #2b5deb;
        }

    .date-info .week {
        font-size: 24px;
        color: #333333;
        margin-bottom: 5px;
    }

    .month-year {
        /* font-weight: bold; */
        font-size: 14px;
        color: #333333;
    }

    .year-number {
        color: #2b5deb;
    }

    .term {
        font-size: 16px;
        color: #333333;
        margin-top: 5px;
    }

    .week-number {
        font-size: 24px;
        color: #424242;
        margin-top: 30px;
    }

    .week-num {
        background: #edf4ff;
        border-radius: 5px;
        color: #2b5deb;
        padding: 2px 8px;
    }

    .calendar-icon {
        font-size: 24px;
        color: #0078ff;
    }

    .start-date {
        font-size: 14px;
        color: #666666;
        text-align: left;
        margin-top: 10px;
    }

    .start-date-range {
        color: #333333;
    }

    .x_Information {
        margin: 20px auto;
        background-color: #fff;
        padding: 20px;
        border-radius: 12px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

        .x_Information .header {
            font-size: 22px;
            color: #000000;
            margin-bottom: 10px 0;
        }

        .x_Information .nav {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
        }

            .x_Information .nav .nav-button {
                display: inline-block;
                text-align: center;
                font-size: 16px;
                color: #333333;
                background: #f5f5f5;
                border-radius: 5px;
                padding: 2px 10px;
                cursor: pointer;
            }

    .nav-button-item {
        color: #2b5deb !important;
        background: #edf4ff !important;
    }

    .x_Information .content {
        list-style-type: none;
        padding: 0;
    }

        .x_Information .content li {
            margin-bottom: 10px;
        }

        .x_Information .content a {
            color: #007bff;
            text-decoration: none;
        }

    ul.nav-content {
        background: #f0f7ff;
        border-radius: 5px;
        padding: 10px;
    }

        ul.nav-content li {
            padding-left: 10px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        ul.nav-content a {
            font-size: 15px;
            color: #666666;
        }

        ul.nav-content li .dian {
            color: #007bff; /* 圆点颜色 */
        }

    ul.item-content {
        padding: 10px;
    }

        ul.item-content li {
            padding: 6px 0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        ul.item-content a {
            font-size: 14px;
            color: #666666;
        }

    a:hover {
        text-decoration: underline;
    }

    .button_right {
        margin-right: 28px;
        background-color: #ffffff;
        padding: 2px 7px;
    }

        .button_right .rate-button {
            background-color: #ffffff;
            color: #000000;
            border-radius: 4px;
            display: inline-block;
            padding: 4px 14px;
            font-size: 14px;
            border: none;
            cursor: pointer;
            text-decoration: none;
        }

    .rate-button-item {
        background: #3c76ed !important;
        color: #ffffff !important;
    }

    .btndisabled{
        background-color: #7db4f8 !important;
        cursor: not-allowed !important;
    }

    .modelShow {
        word-break: break-all;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
</style>

<div class="x_wrapper-content">
    <div class="x_content_left">
        <div class="x_nav">
            <ul class="navUi">
                @if (unitType == 2)
                {
                    <li onclick="openMenu('@Url.Content("~/QueryStatisticsManage/ExperimentTeach/UnitTotalList")', '实验开出记录')">
                        <div class="page-img">
                            <img src="~/image/实验统计.png" alt="" />
                        </div>
                        <div class="page-title">实验统计</div>
                    </li>
                    <li onclick="openMenu('@Url.Content("~/QueryStatisticsManage/ExperimentTeach/PlanExamSchoolCourseRateList")', '按学校综合开出率')">
                        <div class="page-img">
                            <img src="~/image/实验开出率.png" alt="" />
                        </div>
                        <div class="page-title">实验开出率</div>
                    </li>
                    <li onclick="openMenu('@Url.Content("~/QueryStatisticsManage/ExperimentTeach/PlanExamRateList")', '实验计划编制率')">
                        <div class="page-img">
                            <img src="~/image/计划编制率.png" alt="" />
                        </div>
                        <div class="page-title">计划编制率</div>
                    </li>
                    <li onclick="openMenu('@Url.Content("~/QueryStatisticsManage/Instrument/CountyInstrumentStatistic")', '仪器汇总统计')">
                        <div class="page-img">
                            <img src="~/image/实验室统计.png" alt="" />
                        </div>
                        <div class="page-title">实验室统计</div>
                    </li>
                    <li onclick="openMenu('@Url.Content("~/QueryStatisticsManage/FunRoom/UnitTotalList")', '实验室统计')">
                        <div class="page-img">
                            <img src="~/image/仪器管理.png" alt="" />
                        </div>
                        <div class="page-title">仪器统计</div>
                    </li>
                }
                else if (unitType == 3)
                {
                    <li onclick="openMenu('@Url.Content("~/ExperimentTeachManage/ExperimentBooking/PlanList")', '按计划预约')">
                        <div class="page-img">
                            <img src="~/image/实验预约.png" alt="" />
                        </div>
                        <div class="page-title">实验预约</div>
                    </li>
                    <li onclick="openMenu('@Url.Content("~/BusinessManage/FunRoomUse/AddForm")', '专用室使用登记')">
                        <div class="page-img">
                            <img src="~/image/使用登记.png" alt="" />
                        </div>
                        <div class="page-title">使用登记</div>
                    </li>
                    <li onclick="openMenu('@Url.Content("~/ExperimentTeachManage/PlanInfo/List")', '编制计划')">
                        <div class="page-img">
                            <img src="~/image/编制计划.png" alt="" />
                        </div>
                        <div class="page-title">编制计划</div>
                    </li>
                    <li onclick="openMenu('@Url.Content("~/BusinessManage/FunRoom/Index")', '实验室列表')">
                        <div class="page-img">
                            <img src="~/image/实验室管理.png" alt="" />
                        </div>
                        <div class="page-title">实验室管理</div>
                    </li>
                    <li onclick="openMenu('@Url.Content("~/InstrumentManage/SchoolInstrument/EditInfoIndex")', '仪器清单')">
                        <div class="page-img">
                            <img src="~/image/仪器管理.png" alt="" />
                        </div>
                        <div class="page-title">仪器管理</div>
                    </li>
                }
            </ul>
        </div>
        <div class="x_menu">
            <div class="menu-class"  id="todoItems" style="display:none;">
                <div class="class-text">
                    <div class="text_left">待办事项</div>
                    <div class="text_right"></div>
                </div>
                <div class="class-content todo-items">
                    <ul class="custom-ordered-list">
                        <li>
                            <span class="custom-number">1</span>
                            <span class="custom-hint">
                                您有
                                <span class="custom-item" id="WaitArrangerNum">0</span>
                                条预约需要安排！
                            </span>
                            <button class="process-button" id="btnArrangerNum">处理</button>
                        </li>
                        <li>
                            <span class="custom-number">2</span>
                            <span class="custom-hint">
                                您有
                                <span class="custom-item" id="WaitRecordNum">0</span>
                                个实验需要登记！
                            </span>
                            <button class="process-button" id="btnRecordNum">处理</button>
                        </li>
                        <li>
                            <span class="custom-number">3</span>
                            <span class="custom-hint">
                                您有
                                <span class="custom-item" id="WaitBackNum">0</span>
                                件仪器需要归还！
                            </span>
                            <button class="process-button" id="btnBackNum">处理</button>
                        </li>
                        <li>
                            <span class="custom-number">4</span>
                            <span class="custom-hint">
                                您有
                                <span class="custom-item" id="WaitPurchaseAuditNum">0</span>
                                条仪器采购需要审批！
                            </span>
                            <button class="process-button" id="btnPurchaseAuditNum">处理</button>
                        </li>
                        <li>
                            <span class="custom-number">5</span>
                            <span class="custom-hint">
                                您下周有
                                <span class="custom-item" id="WaitBookingNum">0</span>
                                节实验课需预约！
                            </span>
                            <button class="process-button" id="btnBookingNum">处理</button>
                        </li>
                        @*
                  <li>
                    <span class="custom-number">6</span>
                    <span class="custom-hint">
                      您有
                      <span class="custom-item" id="WaitArrangerNum">5</span>
                      条编制计划未完成，可以继续编辑！
                    </span>
                    <button class="process-button">处理</button>
                  </li>
                  *@
                    </ul>
                </div>
            </div>
            <div class="menu-class">
                <div class="class-text">
                    <div class="text_left">基础数据</div>
                    <div class="text_right"></div>
                </div>
                <div class="class-content basic-data">
                    <div class="data-item">
                        <div class="basic-icon">
                            <img src="~/image/已编实验计划.png" alt="" />
                        </div>
                        <div class="basic-content">
                            <p>已编实验计划（个）</p>
                            <p id="Planed">10</p>
                        </div>
                    </div>
                    <div class="data-item">
                        <div class="basic-icon">
                            <img src="~/image/已开出实验.png" alt="" />
                        </div>
                        <div class="basic-content">
                            <p>已开出实验（个）</p>
                            <p id="Recorded">10</p>
                        </div>
                    </div>
                    <div class="data-item">
                        <div class="basic-icon">
                            <img src="~/image/仪器存量.png" alt="" />
                        </div>
                        <div class="basic-content">
                            <p>仪器存量（件）</p>
                            <p id="InstrumentNum">10</p>
                        </div>
                    </div>
                    <div class="data-item">
                        <div class="basic-icon">
                            <img src="~/image/实验室数量.png" alt="" />
                        </div>
                        <div class="basic-content">
                            <p>实验室数量（个）</p>
                            <p id="FunroomNum">10</p>
                        </div>
                    </div>
                    <div class="data-item">
                        <div class="basic-icon">
                            <img src="~/image/实验管理员.png" alt="" />
                        </div>
                        <div class="basic-content">
                            <p>实验管理员（人）</p>
                            <p id="ExperimenterUserNum">10</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="menu-class" id="platformUsage" style="display:none;">
                <div class="class-text">
                    <div class="text_left">平台使用情况</div>
                    <div class="text_right">
                        <span>查看更多</span>
                        <img src="~/image/更多.png" alt="" />
                    </div>
                </div>
                <div class="class-content platform-usage">
                    <div class="table-container">
                        <table>
                            <thead>
                                <tr>
                                    <th class="text-left">单位名称</th>
                                    <th>学段</th>
                                    <th>仪器存量（件）</th>
                                    <th>实验室（个）</th>
                                    @* <th>实验员（人）</th> *@
                                    <th>已编实验计划（个）</th>
                                    <th>实验开出率</th>
                                </tr>
                            </thead>
                            <tbody id="usage_tbody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            @* <div class="menu-class">
                <div class="class-text">
                    <div class="text_left">实验开出率</div>
                    <div class="button_right">
                        <button class="rate-button rate-button-item">小学</button>
                        <button class="rate-button">初中</button>
                        <button class="rate-button">高中</button>
                    </div>
                </div>
                <div class="class-content">
                    <div class="boxnav"
                         id="echarts-chemical"
                         style="height: 380px; width: 550px"></div>
                </div>
            </div> *@
            <div class="menu-class">
                <div class="class-text">
                    <div class="text_left">实验开出率</div>
                    <div class="button_right">
                        <button class="rate-button rate-button-item">小学</button>
                        <button class="rate-button">初中</button>
                        <button class="rate-button">高中</button>
                    </div>
                </div>
                <div class="class-content">
                    <div class="boxnav"
                         id="echarts-chemica12222"
                         style="height: 380px; width: 550px"></div>
                </div>
            </div>
            <div class="menu-class" >
                <div class="class-text">
                    <div class="text_left">实验开出超期</div>
                    <div class="text_right">
                        <span>查看更多</span>
                        <img src="~/image/更多.png" alt="" />
                    </div>
                </div>
                <div class="class-content experiment">
                    <div class="table-container">
                        <table>
                            <thead>
                                <tr>
                                    @if (unitType == 2)
                                    {
                                        <th class="text-left">单位</th>
                                    }
                                    <th>学科</th>
                                    <th>年级</th>
                                    @if (unitType == 3)
                                    {
                                        <th>班级</th>
                                    }
                                    <th>超期数</th>
                                </tr>
                            </thead>
                            <tbody id="experiment_tbody"> 
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="x_content_right">
        <div class="x_date">
            <div class="date-info">
                <div style="
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                ">
                    <div class="day"><span class="day-number" id="dataDay">11</span> 日</div>
                    <div>
                        <div class="week" id="weekday">星期四</div>
                        <div class="month-year">
                            <span class="year">
                                <span class="year-number" id="dataYear">2025</span>年
                                <span class="year-number" id="dataMonthr">05</span>月
                            </span>
                        </div>
                    </div>
                </div>
                <div class="term">2024-2025学年第二学期</div>
                <div class="week-number">
                    第 <span class="week-num" id="weekNumber">20</span> 周
                    <img src="~/image/时间.png" alt="" />
                    @* <span class="calendar-icon">🗓️</span> *@
                </div>
                <div style="
                  width: 84px;
                  margin: 18px auto;
                  border-bottom: 2px solid #edf4ff;
                "></div>
                <div class="start-date">
                    起止日期：
                    <div class="start-date-range">2024-02-26 至 2025-07-14</div>
                </div>
            </div>
        </div>
        <div class="x_Information">
            <div class="header">
                <h1 style="font-size: 22px; font-weight: bold; margin: 10px 0">
                    资讯信息
                </h1>
            </div>
            <div class="nav">
                <span class="nav-button nav-button-item">通知公告</span>
                <span class="nav-button">政策法规</span>
                <span class="nav-button">帮助文档</span>
            </div>
            <ul class="nav-content">
                <li>
                    <a href="#">
                        <span class="dian">•</span> 学实验室及实验教学平台上线啦
                    </a>
                </li>
                <li>
                    <a href="#">
                        <span class="dian">•</span>
                        教育部关于加强和改进中小学实验教学的通知
                    </a>
                </li>
                <li>
                    <a href="#">
                        <span class="dian">•</span>
                        请参加第十届全国中小学实验教学说课活;
                    </a>
                </li>
            </ul>
            <ul class="item-content">
                <li><a href="#">中小学实验室及实验教学平台上线啦</a></li>
                <li><a href="#">教育部关于加强和改进中小学实验教学的通知</a></li>
                <li><a href="#">平台使用说明</a></li>
                <li><a href="#">中小学实验教学指导与创新案例新书发布会</a></li>
                <li>
                    <a href="#">公布2024年度全国中小学实验教学说课活动相关信息</a>
                </li>
                <li><a href="#">邀请参加第十届全国中小学实验教学说课活动</a></li>
                <li><a href="#">第十届全国中小学实验教学说课活动详情</a></li>
                <li>
                    <a href="#">关于第十届全国中小学实验教学说课活动的更多信息</a>
                </li>
                <li>
                    <a href="#">南京市第七届中小学实验教学说课暨创新实验设计评选活动</a>
                </li>
            </ul>
        </div>
    </div>
</div>

<script>
    $(function () {
      // echarts_chemical();
      $(".nav-button").click(function () {
        // 将当前点击的按钮背景颜色设置为蓝色
        $(this).addClass("nav-button-item");
        // 将其他按钮的背景颜色设置为灰色
        $(this).siblings(".nav-button").removeClass("nav-button-item");
      });
      $(".rate-button").click(function () {
        // 将当前点击的按钮背景颜色设置为蓝色
        $(this).addClass("rate-button-item");
        // 将其他按钮的背景颜色设置为灰色
        $(this).siblings(".rate-button").removeClass("rate-button-item");
      });
      
      if(Number('@unitType')==3){
          $("#todoItems").show()
      WaitDoNum();
       }else if(Number('@unitType')==2){
      UseInfo()
       $("#platformUsage").show()
       }
       getDate()
      BaseInfoNum();
     GetOverDue();
    $('[data-toggle="tooltip"]').tooltip();//  初始化所有 tooltip
    // $('.modelShow').attr('title', '新的提示内容新的提示内容');  
      ys.ajax({
        url: '@Url.Content("~/Home/GetSchoolStageJson")',
        data: null,
        type: "get",
        success: function (obj) {},
      });
      ys.ajax({
        url: '@Url.Content("~/Home/ExpertmentRecordRatio?id=1002002")',
        data: null,
        type: "get",
        success: function (obj) {},
      });

      var myChart2 = echarts.init(
        document.getElementById("echarts-chemica12222")
      );
      const xAxis = ["物理", "化学", "生物"];
      const bar1 = [10, 2, null];
      const bar2 = [8, null, 66];
      const bar3 = [null, null, 17];

      function dealBar(arr, name) {
        const bar = [];
        arr.forEach((item, index) => {
          const data = [];
          for (let i = 0; i < index; i++) {
            data.push(null);
          } 
          if (item || item === 0) {
            data.push(item);
            bar.push({
              name,
              type: "bar",
              yAxisIndex: index,
              barWidth: 10,
              data,
              barGap: 2,
              showBackground: true, 
              backgroundStyle: {
               color:  "rgba(255, 255, 255,1)" ,
              }, 
               
            });
          }
        });

        return bar;
      }
      var option2 = {
        legend: {},
        xAxis: {
          type: "value",
        },
        yAxis: xAxis.map((item, index) => {
          const data = Array(xAxis.length).fill("");
          data[index] = item;
          return {
            type: "category",
            position: "left",
            data: data,
          };
        }),
        series: [
          ...dealBar(bar1, "九年级"),
          ...dealBar(bar2, "八年级"),
          ...dealBar(bar3, "七年级"),
        ],

      }; 
      myChart2.setOption(option2);
    });

    function echarts_chemical() {
      // Initialize ECharts instance
      var myChart = echarts.init(
        document.getElementById("echarts-chemical")
      );

      // Data from the image
      var data = [
        { subject: "物理", grade: "九年级", value: 63 },
        { subject: "物理", grade: "八年级", value: 68 },
        { subject: "化学", grade: "九年级", value: 43 },
        { subject: "生物", grade: "八年级", value: 0 },
        { subject: "生物", grade: "七年级", value: 85 },
      ];

      // 数据处理 - 按学科和年级组织
      var subjects = [...new Set(data.map((item) => item.subject))];
      var grades = [...new Set(data.map((item) => item.grade))].sort(); // 获取所有年级并排序
      console.log("grades", grades);
      // 定义颜色方案 - 每个学科一个颜色
      var subjectColors = {
        物理: "#5470C6",
        化学: "#91CC75",
        生物: "#EE6666",
      };

      // 准备系列数据
      var seriesData = grades.map((grade) => {
        return {
          name: grade,
          type: "bar",
          data: subjects.map((subject) => {
            console.log("subject", subject, "grade", grade);
            // 查找对应学科和年级的数据
            var item = data.find(
              (d) => d.subject === subject && d.grade === grade
            );
            return item ? item.value : null; // 如果没有数据则返回0
          }),
          label: {
            show: true,
            position: "top",
            formatter: function (params) {
              return `{left|${params.seriesName}} {right|${params.value}%}`;
            },
            rich: {
              left: {
                align: "left",
                width: "50%",
                color: "#ffcc00",
                fontSize: 12,
                padding: [0, 0, 0, 0],
              },
              space: {
                width: "10%",
                align: "center",
              },
              right: {
                align: "right",
                width: "50%",
                color: "#ffcc00",
                fontSize: 12,
                fontWeight: "bold",
                padding: [0, 0, 0, 0],
              },
            },
          },

          itemStyle: {
            color: function (params) {
              console.log("FF6B81FF6B81", params);
              return params.name === "物理"
                ? "#5470C6"
                : params.name === "化学"
                ? "#91CC75"
                : "#EE6666";
            },
          },
          showBackground: true, //柱子背景色
          backgroundStyle: {
            color: "#ffffff",
          },
        };
      });

      // 配置项
      var option = {
        title: {
          text: "学科成绩统计",
          left: "center",
        },
        tooltip: {
          // trigger: 'axis',
          trigger: "item",
          axisPointer: {
            type: "shadow",
          },
          formatter: function (params) {
            var result = "";
            console.log("params", params);
            result += `${params.seriesName} ${
              subjects[params.dataIndex]
            }: ${params.value}%<br/>`;

            return result || "无数据";
          },
        },
        legend: {
          data: grades,
          bottom: 10,
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "15%",
          containLabel: true,
        },
        xAxis: {
          type: "value",
          axisLabel: {
            formatter: "{value}%",
          },
          max: 100,
        },
        yAxis: {
          type: "category",
          data: subjects,
        },
        series: seriesData,
      };
      console.log("option", option);
      // 使用配置项显示图表
      myChart.setOption(option);
    }

    // 基础信息数量。
    function BaseInfoNum() {
      ys.ajax({
        url: '@Url.Content("~/Home/BaseInfoNum")',
        data: null,
        type: "get",
        success: function (obj) {
          // if (obj.Tag == 1) {
          $("#Planed").html(obj.Data.Planed);
          $("#Recorded").html(obj.Data.Recorded);
          $("#InstrumentNum").html(obj.Data.InstrumentNum);
          $("#FunroomNum").html(obj.Data.FunroomNum);
          $("#ExperimenterUserNum").html(obj.Data.ExperimenterUserNum);
          // }
        },
      });
    }
    //  待办事项。
    function WaitDoNum() {
      ys.ajax({
        url: '@Url.Content("~/Home/WaitDoNum")',
        data: null,
        type: "get",
        success: function (obj) {
          // if (obj.Tag == 1) {
          let WaitArrangerNum=obj.Data.WaitArrangerNum||0;
          let WaitRecordNum=obj.Data.WaitRecordNum||0;
          let WaitBackNum=obj.Data.WaitBackNum||0;
          let WaitPurchaseAuditNum=obj.Data.WaitPurchaseAuditNum||0;
          let WaitBookingNum=obj.Data.WaitBookingNum||0;
          $("#WaitArrangerNum").html(WaitArrangerNum);
          $("#WaitRecordNum").html(WaitRecordNum);
          $("#WaitBackNum").html(WaitBackNum);
          $("#WaitPurchaseAuditNum").html(WaitPurchaseAuditNum);
          $("#WaitBookingNum").html(WaitBookingNum); 
        const taskData = [
      {
        num: WaitArrangerNum,
        waitElement: "#WaitArrangerNum",
        btnElement: "#btnArrangerNum"
      },
      {
        num: WaitRecordNum,
        waitElement: "#WaitRecordNum",
        btnElement: "#btnRecordNum"
      },
      {
        num: WaitBackNum,
        waitElement: "#WaitBackNum",
        btnElement: "#btnBackNum"
      },
      {
        num: WaitPurchaseAuditNum,
        waitElement: "#WaitPurchaseAuditNum",
        btnElement: "#btnPurchaseAuditNum"
      },
      {
        num: WaitBookingNum,
        waitElement: "#WaitBookingNum",
        btnElement: "#btnBookingNum"
      }
    ];
          taskData.forEach(item=>{
              if(item.num===0){
              $(item.waitElement).prop("disabled", true);
              $(item.btnElement).addClass("btndisabled");
              }else{
              $(item.waitElement).prop("disabled", false);
              $(item.btnElement).removeClass("btndisabled");
              }
          }) 

          // }
        },
      });
    }

    //   实验开出超期。
    function GetOverDue() {
      ys.ajax({
        url: '@Url.Content("~/Home/GetOverDue")',
        data: null,
        type: "get",
        success: function (obj) {
          if (obj.Tag == 1) {
              let data=obj.Data||[]
              console.log("Number('@unitType')",Number('@unitType'))
              var comData= data.slice(0, 5)
                  var html = '' 
                  for (var i = 0; i < comData.length; i++) {
                      html += "<tr>";
                      if(Number('@unitType')===2){
                      html += "<td class='text-left'>";
                      html += "<div style='width: 160px' class='modelShow' data-toggle='tooltip' title=''>" + comData[i].Name + "dsadsasdasdasdasdasddddddddddd</div>";
                      html += "</td>";
                      html += "<td style='width: 130px'>" + comData[i].ExperimentNum + "</td>";
                      html += "<td style='width: 130px'>" + comData[i].GradeName + "</td>";
                      }
                      if(Number('@unitType')===3){
                      html += "<td style='width: 140px'>" + comData[i].ExperimentNum + "</td>";
                      html += "<td style='width: 140px'>" + comData[i].GradeName + "</td>";
                      html += "<td style='width: 140px'>" + comData[i].ClassName + "</td>";
                      }
                      html += "<td style='width: 140px'  class='overdue'>" + "<span>" + comData[i].ExperimentNum + "</span>" + "</td>";
                      html += "</tr>";
                  }
                  $("#experiment_tbody").html(html);
                   
          }
        },
      });
    }
    //页面跳转
        function openMenu(url, content) {
        createMenuItem(url, content);
    }

    function getDate() {
    let now = new Date();// 获取当前日期对象
    let year = now.getFullYear(); // 获取年份（4位数）
    let month = now.getMonth() + 1;  // 获取月份（0-11，0表示一月）
    let day = now.getDate();// 获取日期（1-31）
    let weekday = now.getDay(); // 获取星期几（0-6，0表示周日）
    let d = new Date();
    d.setHours(0, 0, 0, 0);
    d.setDate(d.getDate() + 3 - (d.getDay() + 6) % 7);// 设置到当年的第一个星期四
    let yearStart = new Date(d.getFullYear(), 0, 1); // 获取当年的第一天
    let weekNumber= Math.ceil(((d - yearStart) / 86400000 + 1) / 7);// 计算周数
    console.log(`当前时间：${year}年${month}月${day}日，第${weekNumber}周星期${['日', '一', '二', '三', '四', '五', '六'][weekday]}` );

    $("#dataYear").html(year);
    $("#dataMonthr").html(month);
    $("#dataDay").html(day);
    $("#weekday").html(`星期${['日', '一', '二', '三', '四', '五', '六'][weekday]}`);
     $("#weekNumber").html(weekNumber);

    }
    //平台使用情况
     function UseInfo() { 
            ys.ajax({
                url: '@Url.Content("~/Home/UseInfo")',
                type: 'post',
                data: {},
                success: function (obj) {
                    if (obj.Tag == 1) { 
                        let data=obj.Data||[]
                        var comData= data.slice(0, 6)
                        var html = ''
                        for (var i = 0; i < comData.length; i++) {
                            html += "<tr>";
                            html += "<td class='text-left'>";
                            html += "<div style='width: 160px' class='modelShow' data-toggle='tooltip' title=''>" + comData[i].Name + "</div>";
                            html += "</td>";
                            html += "<td style='width: 70px'>" + comData[i].SchoolStageName + "</td>";
                            html += "<td style='width: 80px'>" + comData[i].InstrumentNum + "</td>";
                            html += "<td style='width: 70px'>" + comData[i].FunroomNum + "</td>";
                            html += "<td style='width: 100px'>" + comData[i].PlanExperimentNum + "</td>";
                            html += "<td style='width: 80px'>" + comData[i].ExperimentRate + '%' + "</td>";
                            html += "</tr>";
                        }
                        $("#usage_tbody").html(html);

                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
    }
</script>
