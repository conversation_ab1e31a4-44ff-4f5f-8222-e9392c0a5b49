﻿@using Dqy.Syjx.Entity.ArticleManager
@using Dqy.Syjx.Util.Model
@{
    Layout = "~/Views/Shared/_FormGray.cshtml";
    OperatorInfo operatorInfo = ViewBag.OperatorInfo;
    string unitName = null;
    string realName = null;
    string roleNames = null;
    string roleIds = null;
    int unitType = 0;
    long userId = 0;
    if (operatorInfo != null)
    {
        unitName = operatorInfo.UnitName ?? "";
        realName = operatorInfo.RealName ?? "";
        roleNames = operatorInfo.RoleNames ?? "";
        unitType = operatorInfo.UnitType;
        roleIds = "," + operatorInfo.RoleValues + ",";
        userId = operatorInfo.UserId.Value;
    }
    TData<List<ArticleCategoryEntity>> list = ViewBag.Article as TData<List<ArticleCategoryEntity>>;

    List<MenuEntity> menuList = ViewBag.MenuList;
}
@section header {
    @* <link href='@Url.Content("~/lib/kanban/comon0.css")' rel="stylesheet" /> *@
    <script src="~/lib/report/echarts/echarts.js"></script>
    <script src="~/lib/kanban/jquery.waypoints.min.js"></script>
    <script src="~/lib/kanban/jquery.countup.min.js"></script>
}

<style>
    .gray-bg {
        background-color: #fff !important;
    }

    .x_wrapper-content {
        width: 1682px;
        display: flex;
        /* margin: 10px auto; */
        margin-top:10px;
        margin-left:10px;
    }

    .x_content_left {
        width: 1360px;
        flex-shrink: 0;
        margin-right: 12px;
    }

    .navUi {
        width: 100%;
        display: flex;
        justify-content: space-between;
        gap: 14px;
    }

        .navUi li {
            width: 20%;
            height: 100px;
            background: linear-gradient(to bottom, #e4eefe, #ffffff);
            display: flex;
            justify-content: center;
            align-items: center;
            cursor:pointer;
        }

    .page-title {
        font-size: 18px;
        color: #060606;
    }

    .x_menu {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        gap: 14px;
    }

        .x_menu .menu-class {
            width: 672px;
            height: 340px;
            background-color: #f4f8fe;
        }

            .x_menu .menu-class .class-text {
                height: 58px;
                width: 100%;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

    .class-text .text_left {
        font-size: 22px;
        color: #000000;
        margin-left: 33px;
    }

    .class-text .text_right {
        font-size: 14px;
        color: #868686;
        margin-right: 28px;
        cursor: pointer;
    }
     
    .class-content {
    }

    .todo-items,
    .basic-data {
        margin: 20px 48px;
    }

    .custom-ordered-list {
        list-style-type: none; /* 移除默认的列表样式 */
        padding: 0;
    }

        .custom-ordered-list li {
            margin-bottom: 15px; /* 设置列表项之间的间距 */
            display: flex;
            align-items: center;
        }

    .custom-number {
        display: inline-block;
        width: 22px; /* 设置序号的大小 */
        height: 22px;
        font-family: Arial;
        font-weight: bold;
        font-size: 18px;
        color: #ffffff;
        line-height: 22px; /* 使文字垂直居中 */
        border-radius: 50%; /* 圆形 */
        text-align: center;
        margin-right: 10px; /* 序号和文本之间的间距 */
        background-color: #cbcbcb;
    }

    .process-button {
        display: inline-block;
        padding: 4px 14px;
        font-size: 14px;
        background-color: #498ef6;
        color: #fff; /* 白色文字 */
        border: none;
        border-radius: 8px;
        cursor: pointer;
        text-decoration: none;
        margin-left: auto; /* 将按钮推到右侧 */
    } 

    /* 第一个子元素 */
    .custom-ordered-list li:first-child .custom-number {
        background-color: #498ef6; 
    }

    /* 第二个子元素 */
    .custom-ordered-list li:nth-child(2) .custom-number {
        background-color: #54bf6f; /* 或者其他你想要的样式 */
    }

    /* 第三个子元素 */
    .custom-ordered-list li:nth-child(3) .custom-number {
        background-color: #f49a56; /* 或者其他你想要的样式 */
    }

    .custom-hint {
        font-size: 15px;
        color: #000000;
    }

    .custom-item {
        color: #498ef6;
        text-decoration: underline;
    }

    .basic-data {
        display: flex;
        flex-wrap: wrap;
    }

    .data-item {
        width: 50%;
        display: flex;
        align-items: center;
        margin-bottom: 20px;
    }
     
    .basic-content {
        margin-left: 16px;
    }

        .basic-content p:first-of-type {
            font-size: 14px;
            line-height: 14px;
            color: #666;
            margin: 0;
        }

        .basic-content p:last-of-type {
            font-size: 30px;
            line-height: 30px;
            color: #424242;
            font-weight: 500;
            margin: 10px 0 0 0;
        }

    .table-container {
        width: 638px;
        background: #fcfdff;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        padding: 13px 17px;
        margin: 0 19px;
        max-width: 800px;
    }

    table {
        width: 580px;
        margin: 0 auto;
        border-collapse: collapse;
    }

    thead {
        
    }

        thead tr {
            height: 42px;
            border: 1px solid rgba(125, 180, 248, 0.3) !important;
            border-radius: 5px;
        }
         
    th,
    td {
        text-align: center;
    }

    th {
        font-size: 16px;
        color: #000000;
        font-weight: 400;
    }

    .experiment td {
        font-size: 16px;
        padding: 3px 2px;
    }

    .text-left {
        padding-left: 10px !important;
        text-align: left;
    }

    .overdue span {
        font-size: 16px;
        color: #ff0a0a;
        display: inline-block;
        padding: 2px 14px;
        background: #fff3e5;
        border-radius: 5px;
    }

    .platform-usage th {
        font-size: 14px;
    }

    .platform-usage td {
        font-size: 14px;
        padding: 6px 2px;
    }

    .x_date {
        width: 311px;
        height: 320px; 
        border: 1px solid rgba(125, 180, 248, 0.3);
        background: url(/image/时间背景图.png) no-repeat; 
        background-color: #ffffff;
        border-radius: 12px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        padding: 0 20px 20px;
        text-align: center; 
    }

        .x_date .date-info {
            width: 180px;
            margin: 0px auto;
        }

    .date-info .day {
        font-size: 20px;
        font-weight: bold;
        color: #333;
        padding-top:10px;
    }

        .date-info .day .day-number {
            font-family: Arial;
            font-weight: 400;
            font-size: 62px;
            color: #2b5deb;
        }

    .date-info .week {
        font-size: 24px;
        color: #333333;
        margin-bottom: 5px;
    }

    .month-year {
        /* font-weight: bold; */
        font-size: 14px;
        color: #333333;
    }

    .year-number {
        color: #2b5deb;
    }

    .term {
        font-size: 16px;
        color: #333333;
        margin-top: 5px;
    }

    .week-number {
        font-size: 24px;
        color: #424242;
        margin-top: 30px;
    }

    .week-num {
        background: #edf4ff;
        border-radius: 5px;
        color: #2b5deb;
        padding: 2px 8px;
    }

    .calendar-icon {
        font-size: 24px;
        color: #0078ff;
    }

    .start-date {
        font-size: 14px;
        color: #666666;
        text-align: left;
        margin-top: 10px;
    }

    .start-date-range {
        color: #333333;
    }

    .x_Information {
        width: 311px;
        height: 450px;
        border: 1px solid rgba(125, 180, 248, 0.3); 
        margin: 10px auto 0;
        background-color: #fff;
        padding: 20px;
        border-radius: 12px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

        .x_Information .header {
            font-size: 22px;
            color: #000000;
            margin-bottom: 10px 0;
        }

        .x_Information .nav {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
        }

            .x_Information .nav .nav-button {
                display: inline-block;
                text-align: center;
                font-size: 16px;
                color: #333333;
                background: #f5f5f5;
                border-radius: 5px;
                padding: 2px 10px;
                cursor: pointer;
            }

    .nav-button-item {
        color: #2b5deb !important;
        background: #edf4ff !important;
    }

    .x_Information .content {
        list-style-type: none;
        padding: 0;
    }

        .x_Information .content li {
            margin-bottom: 10px;
        }

        .x_Information .content a {
            color: #007bff;
            text-decoration: none;
        }

    ul.nav-content {
        background: #f0f7ff;
        border-radius: 5px;
        padding: 10px;
    }

        ul.nav-content li {
            padding-left: 3px 0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        ul.nav-content a {
            font-size: 15px;
            color: #666666;
        }

        ul.nav-content li .dian {
            color: #007bff; 
            padding-right: 3px;
        }

    ul.item-content {
        padding: 10px;
    }

        ul.item-content li {
            padding: 3px 0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        ul.item-content a {
            font-size: 14px;
            color: #666666;
        }

    a:hover {
        text-decoration: underline;
        color: #337ab7 !important;
    }

    .button_right {
        margin-right: 28px;
        background-color: #ffffff;
        padding: 2px 7px;
    }

        .button_right .rate-button {
            background-color: #ffffff;
            color: #000000;
            border-radius: 4px;
            display: inline-block;
            padding: 4px 14px;
            font-size: 14px;
            border: none;
            cursor: pointer;
            text-decoration: none;
        }

    .rate-button-item {
        background: #3c76ed !important;
        color: #ffffff !important;
    }

    .btndisabled{
        background-color: #7db4f8 !important;
        pointer-events: none !important;
        cursor: not-allowed !important;
        opacity: 0.6 !important;
    }

    .modelShow {
        word-break: break-all;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
     
</style>

<div class="x_wrapper-content">
    <div class="x_content_left">
        <div class="x_nav">
            <ul class="navUi">
                @if (unitType == 2)
                {
                    <li onclick="openMenu('@Url.Content("~/QueryStatisticsManage/ExperimentTeach/UnitTotalList")', '实验开出记录')">
                        <div class="page-img">
                            <img src="~/image/实验统计.png" alt="" />
                        </div>
                        <div class="page-title">实验统计</div>
                    </li>
                    <li onclick="openMenu('@Url.Content("~/QueryStatisticsManage/ExperimentTeach/PlanExamSchoolCourseRateList")', '按学校综合开出率')">
                        <div class="page-img">
                            <img src="~/image/实验开出率.png" alt="" />
                        </div>
                        <div class="page-title">实验开出率</div>
                    </li>
                    <li onclick="openMenu('@Url.Content("~/QueryStatisticsManage/ExperimentTeach/PlanExamRateList")', '实验计划编制率')">
                        <div class="page-img">
                            <img src="~/image/计划编制率.png" alt="" />
                        </div>
                        <div class="page-title">计划编制率</div>
                    </li>
                    <li onclick="openMenu('@Url.Content("~/QueryStatisticsManage/FunRoom/UnitTotalList")', '实验室统计')">
                        <div class="page-img">
                            <img src="~/image/实验室统计.png" alt="" />
                        </div>
                        <div class="page-title">实验室统计</div>
                    </li>
                    <li onclick="openMenu('@Url.Content("~/QueryStatisticsManage/Instrument/CountyInstrumentStatistic")', '仪器汇总统计')">
                        <div class="page-img">
                            <img src="~/image/仪器管理.png" alt="" />
                        </div>
                        <div class="page-title">仪器统计</div>
                    </li>
                }
                else if (unitType == 3)
                {
                    <li onclick="openMenu('@Url.Content("~/ExperimentTeachManage/ExperimentBooking/PlanList")', '按计划预约')">
                        <div class="page-img">
                            <img src="~/image/实验预约.png" alt="" />
                        </div>
                        <div class="page-title">实验预约</div>
                    </li>
                    <li onclick="openMenu('@Url.Content("~/BusinessManage/FunRoomUse/AddForm")', '专用室使用登记')">
                        <div class="page-img">
                            <img src="~/image/使用登记.png" alt="" />
                        </div>
                        <div class="page-title">使用登记</div>
                    </li>
                    <li onclick="openMenu('@Url.Content("~/ExperimentTeachManage/PlanInfo/List")', '编制计划')">
                        <div class="page-img">
                            <img src="~/image/编制计划.png" alt="" />
                        </div>
                        <div class="page-title">编制计划</div>
                    </li>
                    <li onclick="openMenu('@Url.Content("~/BusinessManage/FunRoom/Index")', '实验室列表')">
                        <div class="page-img">
                            <img src="~/image/实验室管理.png" alt="" />
                        </div>
                        <div class="page-title">实验室管理</div>
                    </li>
                    <li onclick="openMenu('@Url.Content("~/InstrumentManage/SchoolInstrument/EditInfoIndex")', '仪器清单')">
                        <div class="page-img">
                            <img src="~/image/仪器管理.png" alt="" />
                        </div>
                        <div class="page-title">仪器管理</div>
                    </li>
                }
            </ul>
        </div>
        <div class="x_menu">
            @if (unitType == 3)
            {
                <div class="menu-class" id="todoItems" style="height:330px;">
                    <div class="class-text">
                    <div class="text_left">待办事项</div>
                    <div class="text_right"></div>
                </div>
                <div class="class-content todo-items">
                    <ul class="custom-ordered-list">
                        <li>
                            <span class="custom-number">1</span>
                            <span class="custom-hint">
                                您有
                                <span class="custom-item" id="WaitArrangerNum">0</span>
                                条预约需要安排！
                            </span>
                            <button class="process-button" id="btnArrangerNum" onclick="openMenu('@Url.Content("~/ExperimentTeachManage/ExperimentBooking/ArrangeList")', '待安排实验')">处理</button>
                        </li>
                        <li>
                            <span class="custom-number">2</span>
                            <span class="custom-hint">
                                您有
                                <span class="custom-item" id="WaitRecordNum">0</span>
                                个实验需要登记！
                            </span>
                            <button class="process-button" id="btnRecordNum" onclick="openMenu('@Url.Content("~/ExperimentTeachManage/ExperimentBooking/RecordList")', '待登记实验')">处理</button>
                        </li>
                        <li>
                            <span class="custom-number">3</span>
                            <span class="custom-hint">
                                您有
                                <span class="custom-item" id="WaitBackNum">0</span>
                                件仪器需要归还！
                            </span>
                                <button class="process-button" id="btnBackNum" onclick="openMenu('@Url.Content("~/InstrumentManage/InstrumentLend/LendRevertIndex?statuz=20")', '仪器归还' )">处理</button>
                        </li>
                        <li>
                            <span class="custom-number">4</span>
                            <span class="custom-hint">
                                您有
                                <span class="custom-item" id="WaitPurchaseAuditNum">0</span>
                                条仪器采购需要审批！
                            </span>
                            <button class="process-button" id="btnPurchaseAuditNum" onclick="openMenu('@Url.Content("~/InstrumentManage/PurchaseAudit/PurchaseAuditIndex")', '待审批计划')">处理</button>
                        </li>
                        <li>
                            <span class="custom-number">5</span>
                            <span class="custom-hint">
                                您下周有
                                <span class="custom-item" id="WaitBookingNum">0</span>
                                节实验课需预约！
                            </span>
                            <button class="process-button" id="btnBookingNum" onclick="openMenu('@Url.Content("~/ExperimentTeachManage/ExperimentBooking/PlanList")', '按计划预约')">处理</button>
                        </li>
                        <li>
                            <span class="custom-number">6</span>
                            <span class="custom-hint">
                                您有
                                    <span class="custom-item" id="WaitPlanNum">0</span>
                                条实验计划需编制！
                            </span>
                                <button class="process-button" id="btnPlanNum" onclick="openMenu('@Url.Content("~/ExperimentTeachManage/PlanInfo/List")', '编制计划')">处理</button>
                        </li>
                    </ul>
                </div>
            </div>
            }
            <div class="menu-class" style="height:330px;">
                <div class="class-text">
                    <div class="text_left">基础数据</div>
                    <div class="text_right"></div>
                </div>
                <div class="class-content basic-data">
                    <div class="data-item">
                        <div class="basic-icon">
                            <img src="~/image/已编实验计划.png" alt="" />
                        </div>
                        <div class="basic-content">
                            <p>已编实验计划（个）</p>
                            <p id="Planed">10</p>
                        </div>
                    </div>
                    <div class="data-item">
                        <div class="basic-icon">
                            <img src="~/image/仪器存量.png" alt="" />
                        </div>
                        <div class="basic-content">
                            <p>仪器存量（件）</p>
                            <p id="InstrumentNum">10</p>
                        </div>
                    </div>
                    <div class="data-item">
                        <div class="basic-icon">
                            <img src="~/image/已预约实验.png" alt="" />
                        </div>
                        <div class="basic-content">
                            <p>已预约实验（个）</p>
                            <p id="ExprimentBookingNum">10</p>
                        </div>
                    </div> 
                    <div class="data-item">
                        <div class="basic-icon">
                            <img src="~/image/实验室数量.png" alt="" />
                        </div>
                        <div class="basic-content">
                            <p>实验室数量（个）</p>
                            <p id="FunroomNum">10</p>
                        </div>
                    </div>
                    <div class="data-item">
                        <div class="basic-icon">
                            <img src="~/image/已开出实验.png" alt="" />
                        </div>
                        <div class="basic-content">
                            <p>已开出实验（个）</p>
                            <p id="Recorded">10</p>
                        </div>
                    </div>
                    <div class="data-item">
                        <div class="basic-icon">
                            <img src="~/image/实验管理员.png" alt="" />
                        </div>
                        <div class="basic-content">
                            <p>实验管理员（人）</p>
                            <p id="ExperimenterUserNum">10</p>
                        </div>
                    </div>
                </div>
            </div>
            @if (unitType == 2)
            {
                <div class="menu-class" id="platformUsage" style="height:330px;">
                    <div class="class-text">
                        <div class="text_left">平台使用情况</div>
                        <div class="text_right" onclick="toUseInfoList()">
                            <span>查看更多</span>
                            <img src="~/image/更多.png" alt="" />
                        </div>
                    </div>
                <div class="class-content platform-usage">
                    <div class="table-container">
                        <table>
                            <thead>
                                <tr>
                                    <th class="text-left">单位名称</th>
                                    <th>学段</th>
                                    <th>仪器数</th>
                                    <th>实验室数</th> 
                                    <th>已填计划</th>
                                    <th>开出率</th>
                                </tr>
                            </thead>
                            <tbody id="usage_tbody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            }
            <div class="menu-class" >
                <div class="class-text">
                    <div class="text_left">实验开出率</div>
                    <div class="button_right" id="rateDiv"> </div>
                </div>
                <div class="class-content">
                    <div class="boxnav"
                         id="echarts2"
                         style="height: 280px; width: 550px;margin-left:30px;"></div>
                </div>
            </div>
            <div class="menu-class" >
                <div class="class-text">
                    <div class="text_left">实验开出超期</div>
                    <div class="text_right" onclick="toOverDueList()">
                        <span>查看更多</span>
                        <img src="~/image/更多.png" alt="" />
                    </div>
                </div>
                <div class="class-content experiment">
                    <div class="table-container">
                        <table>
                            <thead>
                                <tr>
                                    @if (unitType == 2)
                                    {
                                        <th class="text-left">单位名称</th>
                                    }
                                    <th>学科</th>
                                    <th>年级</th>
                                    @if (unitType == 3)
                                    {
                                        <th>班级</th>
                                    }
                                    <th>超期数</th>
                                </tr>
                            </thead>
                            <tbody id="experiment_tbody"> 
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="x_content_right">
        <div class="x_date">
            <div class="date-info">
                <div style="
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                ">
                    <div class="day"><span class="day-number" id="dataDay">11</span> 日</div>
                    <div>
                        <div class="week" id="weekday">星期四</div>
                        <div class="month-year">
                            <span class="year">
                                <span class="year-number" id="dataYear">2025</span>年
                                <span class="year-number" id="dataMonthr">05</span>月
                            </span>
                        </div>
                    </div>
                </div>
                <div class="term" id="timeTerm">2024-2025学年第二学期</div>
                <div class="week-number">
                    第 <span class="week-num" id="weekNumber">20</span> 周
                    <img src="~/image/时间.png" alt="" />
                </div>
                <div style="
                  width: 84px;
                  margin: 18px auto;
                  border-bottom: 2px solid #edf4ff;
                "></div>
                <div class="start-date">
                    起止日期：
                    <div class="start-date-range" id="schoolYear">2024-02-26 至 2025-07-14</div>
                </div>
            </div>
        </div>
        <div class="x_Information">
            <div class="header">
                <h1 style="font-size: 22px; font-weight: bold; margin: 10px 0">
                    资讯信息
                </h1>
            </div>
            <div class="nav">
                <span class="nav-button" onclick="toAricelList('homegg','通知公告')">通知公告</span>
                <span class="nav-button" onclick="toAricelList('homefg','政策法规')">政策法规</span>
                <span class="nav-button" onclick="toAricelList('homewd','帮助文档')">帮助文档</span>
            </div>
            <ul class="nav-content" id="cateogry_nav">
            </ul>
            <ul class="item-content" id="article_item">
            </ul>
        </div>
    </div>
</div>

<script>
    $(function () {
        // console.log("window.innerWidth",window.innerWidth)
        // document.documentElement.style.fontSize = (window.innerWidth / 1720) * 16 + 'px';

      if(Number('@unitType')==3){
          WaitDoNum();
      }else if(Number('@unitType')==2){
          UseInfo()
      }
      getDate()
      BaseInfoNum();
      GetOverDue();
      GetArticleJson();
      GetTermInfoJson() 
      GetSchoolStageJson()
    });
     
    // 基础信息数量。
    function BaseInfoNum() {
      ys.ajax({
        url: '@Url.Content("~/Home/BaseInfoNum")',
        data: null,
        type: "get",
        success: function (obj) {
          // if (obj.Tag == 1) {
          $("#Planed").html(obj.Data.Planed);
          $("#Recorded").html(obj.Data.Recorded);
          $("#InstrumentNum").html(obj.Data.InstrumentNum);
          $("#FunroomNum").html(obj.Data.FunroomNum);
          $("#ExprimentBookingNum").html(obj.Data.ExprimentBookingNum);
          $("#ExperimenterUserNum").html(obj.Data.ExperimenterUserNum);
          // }
        },
      });
    }
    //  待办事项。
    function WaitDoNum() {
      ys.ajax({
        url: '@Url.Content("~/Home/WaitDoNum")',
        data: null,
        type: "get",
        success: function (obj) {
          // if (obj.Tag == 1) {
          let WaitArrangerNum=obj.Data.WaitArrangerNum||0;
          let WaitRecordNum=obj.Data.WaitRecordNum||0;
          let WaitBackNum=obj.Data.WaitBackNum||0;
          let WaitPurchaseAuditNum=obj.Data.WaitPurchaseAuditNum||0;
          let WaitBookingNum=obj.Data.WaitBookingNum||0;
          let WaitPlanNum=obj.Data.WaitPlanNum||0;
          
          $("#WaitArrangerNum").html(WaitArrangerNum);
          $("#WaitRecordNum").html(WaitRecordNum);
          $("#WaitBackNum").html(WaitBackNum);
          $("#WaitPurchaseAuditNum").html(WaitPurchaseAuditNum);
           $("#WaitBookingNum").html(WaitBookingNum);
          $("#WaitPlanNum").html(WaitPlanNum);
          const taskData = [{
                               num: WaitArrangerNum,
                               waitElement: "#WaitArrangerNum",
                               btnElement: "#btnArrangerNum"
                             },
                             {
                               num: WaitRecordNum,
                               waitElement: "#WaitRecordNum",
                               btnElement: "#btnRecordNum"
                             },
                             {
                               num: WaitBackNum,
                               waitElement: "#WaitBackNum",
                               btnElement: "#btnBackNum"
                             },
                             {
                               num: WaitPurchaseAuditNum,
                               waitElement: "#WaitPurchaseAuditNum",
                               btnElement: "#btnPurchaseAuditNum"
                             },
                             {
                               num: WaitBookingNum,
                               waitElement: "#WaitBookingNum",
                               btnElement: "#btnBookingNum"
                             },
                             {
                               num: WaitPlanNum,
                               waitElement: "#WaitPlanNum",
                               btnElement: "#btnPlanNum"
                             }];
          taskData.forEach(item=>{
              if(item.num===0){
                  $(item.waitElement).prop("disabled", true);
                  $(item.btnElement).addClass("btndisabled");
              }else{
                  $(item.waitElement).prop("disabled", false);
                  $(item.btnElement).removeClass("btndisabled");
              }
          }) 

          // }
        },
      });
    }

    //   实验开出超期。
    function GetOverDue() {
      ys.ajax({
        url: '@Url.Content("~/Home/GetOverDue")',
        data: null,
        type: "get",
        success: function (obj) {
          if (obj.Tag == 1) {
              let data=obj.Data||[]
              // if(data.length>0){
                  var comData= data.slice(0, 6) 
                  var html = ''
                  for (var i = 0; i < 6; i++) {
                      console.log("comData[i]",comData[i])
                      if(comData[i]){
                          html += "<tr>";
                          if(Number('@unitType')===2){
                              html += "<td class='text-left'>";
                              html += `<div style='width: 160px' class='modelShow' data-toggle='tooltip' title=${comData[i].Name}>` + comData[i].Name + "</div>";
                              html += "</td>";
                              html += "<td style='width: 130px'>" + comData[i].CourseName + "</td>";
                              html += "<td style='width: 130px'>" + comData[i].GradeName + "</td>";
                          }
                          if(Number('@unitType')===3){
                              html += "<td style='width: 140px'>" + comData[i].CourseName + "</td>";
                              html += "<td style='width: 140px'>" + comData[i].GradeName + "</td>";
                              html += "<td style='width: 140px'>" + comData[i].ClassName + "</td>";
                          }
                          html += "<td style='width: 140px'  class='overdue'>" + "<span>" + comData[i].ExperimentNum + "</span>" + "</td>";
                          html += "</tr>";
                          
                      }else{
                          html += "<tr>";
                          if(Number('@unitType')===2){
                              html += "<td class='text-left'>";
                              html += "<div style='width: 160px;height:23px'><span></span> </div>";
                              html += "</td>";
                              html += "<td style='width: 130px;'> </td>";
                              html += "<td style='width: 130px;'> </td>";
                          }                                           
                          if(Number('@unitType')===3){                
                              html += "<td ><div style='width: 160px;height:23px'></div></td>";
                              html += "<td style='width: 140px;'> </td>";
                              html += "<td style='width: 140px;'> </td>";
                          }
                          html += "<td style='width: 140px;'> </td>";
                          html += "</tr>";
                      }
                  }
                  $("#experiment_tbody").html(html);
                  $('[data-toggle="tooltip"]').tooltip({
                      trigger: 'hover',  // 鼠标悬停时显示
                      placement: 'top'   // 提示显示在元素上方
                  });
              // }else{
              //     var html = '<tr><td colspan="4">没有开出超期的实验</td></tr>'
              //     $("#experiment_tbody").html(html);
              // }
                  
          }
        },
      });
    }
    //页面跳转
    function openMenu(url, content) {
        createMenuItem(url, content);
    }
    // 时间
    function getDate() {
        let now = new Date();// 获取当前日期对象
        let year = now.getFullYear(); // 获取年份（4位数）
        let month = now.getMonth() + 1;  // 获取月份（0-11，0表示一月）
        let day = now.getDate();// 获取日期（1-31） 
        // console.log(`当前时间：${year}年${month}月${day}日` );
        $("#dataYear").html(year);
        $("#dataMonthr").html(month);
        $("#dataDay").html(day); 
    }
    //平台使用情况
     function UseInfo() { 
         ys.ajax({
             url: '@Url.Content("~/Home/UseInfo")',
             type: 'post',
             data: {},
             success: function (obj) {
                 if (obj.Tag == 1) { 
                     let data=obj.Data||[]
                     var comData= data.slice(0, 6)
                     var html = ''
                     for (var i = 0; i < comData.length; i++) {
                         html += "<tr>";
                         html += "<td class='text-left'>";
                         html += `<div style='width: 160px' class='modelShow' data-toggle='tooltip' title=${comData[i].Name}>` + comData[i].Name + "</div>";
                         html += "</td>";
                         html += "<td style='width: 70px'>" + comData[i].SchoolStageName + "</td>";
                         html += "<td style='width: 80px'>" + comData[i].InstrumentNum + "</td>";
                         html += "<td style='width: 70px'>" + comData[i].FunroomNum + "</td>";
                         html += "<td style='width: 100px'>" + comData[i].PlanExperimentNum + "</td>";
                         html += "<td style='width: 80px'>" + comData[i].ExperimentRate + '%' + "</td>";
                         html += "</tr>";
                     }
                     $("#usage_tbody").html(html);
                     $('[data-toggle="tooltip"]').tooltip({
                          trigger: 'hover',  // 鼠标悬停时显示
                          placement: 'top'   // 提示显示在元素上方
                     });
                 }
                 else {
                     ys.msgError(obj.Message);
                 }
             }
         });
    }
    //资讯
    function GetArticleJson() {
        ys.ajax({
            url: '@Url.Content("~/Home/GetArticleJson")',
            type: 'get',
            data: {},
            success: function (obj) {
                if (obj.Tag == 1) {
                    let articleList = obj.Data.ArticleList||[];
                    let categoryData= articleList.slice(0, 3);
                    let articleData= articleList.slice(3, 12);
                    var html1 = ''
                    for (var i = 0; i < categoryData.length; i++) {
                        let url = '@Url.Content("/ArticleManager/Article/ArticleDetail/")' + categoryData[i].Id
                        html1 += "<li>";
                        html1 += `<a href=${url} target='_blank' title=${categoryData[i].ShortTitle}>`;
                        html1 += "<span class='dian'>•</span>" + categoryData[i].ShortTitle;
                        html1 += "</a>";
                        html1 += "</li>";
                    }
                    $("#cateogry_nav").html(html1);
                    var html2 = ''
                    for (var i = 0; i < articleData.length; i++) {
                        let url = '@Url.Content("/ArticleManager/Article/ArticleDetail/")' + articleData[i].Id
                        html2 += "<li>";
                        html2 += `<a href=${url} target='_blank' title=${articleData[i].ShortTitle}>` + articleData[i].ShortTitle;
                        html2 += "</a>";
                        html2 += "</li>";
                    }
                    $("#article_item").html(html2);
                }
                else {
                    ys.msgError(obj.Message);
                }
            }
        });
    }
   
    function toAricelList(code,title) {
        var url = '@Url.Content("~/ArticleManager/Article/ArticleList")'+'?code='+ code;
        createMenuItem(url,title);
    }
    function toUseInfoList() {
        var url = '@Url.Content("~/Home/UseInfoList")';
        createMenuItem(url, "平台使用情况");
    }
    function toOverDueList() {
        var url = '@Url.Content("~/Home/OverDueList")';
        createMenuItem(url, "实验开出超期");
    }
    
    //获取日期学期
    function GetTermInfoJson() {
        ys.ajax({
            url: '@Url.Content("~/Home/GetTermInfoJson")',
            type: 'get',
            data: {},
            success: function (obj) {
                // if (obj.Tag == 1) {
                let term=''
                if( obj.Data.SchoolTerm==1){
                    term='第一学期'
                }else{
                    term='第二学期'
                }
                let timeTerm = obj.Data.SchoolYearStart + '-' + obj.Data.SchoolYearEnd + '学年' + term
                let schoolYear = obj.Data.TermStart + ' 至 ' + obj.Data.TermEnd
                $("#timeTerm").html(timeTerm)
                $("#schoolYear").html(schoolYear)
                $("#weekNumber").html(obj.Data.WeekNum);
                $("#weekday").html(`星期${['一', '二', '三', '四', '五', '六','日' ][obj.Data.DayWeekName-1]}`)
                // } 
            }
        });
    }

    //获取学段
    function GetSchoolStageJson() {
        ys.ajax({
            url: '@Url.Content("~/Home/GetSchoolStageJson")',
            data: null,
            type: "get",
            success: function (obj) {
                if (obj.Tag == 1) {
                    let schoolStageData = obj.Data;
                    if(schoolStageData.length>1){
                        var html = ''
                        for (var i = 0; i < schoolStageData.length; i++) {
                            html += `<button class='rate-button' id='rate${i}' DictionaryId=${schoolStageData[i].DictionaryId}>` + schoolStageData[i].DicName + "</button>";
                        }
                        $("#rateDiv").html(html);
                        $(".rate-button").click(function () {
                        // 将当前点击的按钮背景颜色设置为蓝色
                        $(this).addClass("rate-button-item");
                        // 将其他按钮的背景颜色设置为灰色
                        $(this).siblings(".rate-button").removeClass("rate-button-item");
                        ExpertmentRecordRatio($(this).attr('DictionaryId'))
                        });
                    }

                    let index = schoolStageData.findIndex(item => item.DictionaryId === 1002002);
                    if (index !== -1) {
                        $("#rate" + index).addClass('rate-button-item')
                        ExpertmentRecordRatio(schoolStageData[index].DictionaryId)
                    } else {
                        $("#rate0").addClass('rate-button-item')
                        ExpertmentRecordRatio(schoolStageData[0].DictionaryId)
                    } 
                    
                }
            },
        });
    }
    function ExpertmentRecordRatio(id){ 
       var myChart = echarts.init(document.getElementById('echarts2'));
       ys.ajax({
          url: '@Url.Content("~/Home/ExpertmentRecordRatio")' + "?schoolstageid=" + id,
          data: null,
          type: "get",
          success: function (obj) {
              if (obj.Tag == 1) {
                  let list=obj.Data;
                  var AvgArr = list.map(item => item.value);
                  var TitleArr = list.map(item => [item.subject, item.grade]);
                  // console.log("AvgArr",AvgArr,"TitleArr",TitleArr)
                  function toEachartsOption(data, levels, tickLength, seriesData) {
                                var labelOffset = "{offset|}"
                                var yAxis = []
                                // 指定图表的配置项和数据
                                var option = {
                                    tooltip: {
                                        trigger: 'axis',
                                        axisPointer: { type: 'shadow' }, 
                                        formatter(params) {
                                            let val = params[0]["value"];
                                            let i = params[0]["dataIndex"];
                                            let circle = `<span style="display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;left:5px;background-color:`;
                                            let data = `${circle}${params[0]["color"]}"></span> ${params[0]["name"]
                                                }${TitleArr[i][0]}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;${val}%`;
                                            return data;
                                        }
                                    },
                                    grid: {
                                        left: '-20',
                                        top: '20',
                                        right: '65',
                                        bottom: '0',
                                        containLabel: true
                                    },
                                    xAxis: {
                                        type: 'value',
                                        show: false
                                    },
                                    yAxis: yAxis,
                                    series: [{
                                        type: 'bar',
                                        data: seriesData,
                                        barWidth: "20%",
                                        itemStyle: {
                                            normal: {
                                                barBorderRadius: [0, 10, 10, 0],
                                                label: {
                                                    show: true, //开启显示
                                                    position: 'right', //在右侧显示
                                                    textStyle: { //数值样式
                                                        color: '#000',
                                                        fontSize: 16
                                                    },
                                                    formatter: function (params) {
                                                        return params.value + '%';
                                                    }
                                                },
                                                color: function (params) {
                                                    switch (params.name) {
                                                        case '七年级':
                                                            return '#FF9A45'
                                                            break;
                                                        case '八年级':
                                                            return '#61CD53'
                                                            break;
                                                        case '九年级':
                                                            return '#618AFF'
                                                            break;
                                                         case '高一':
                                                            return '#FF9A45'
                                                            break;
                                                        case '高二':
                                                            return '#61CD53'
                                                            break;
                                                        case '高三':
                                                            return '#618AFF'
                                                            break;
                                                         default:
                                                            return '#618AFF'
                                                    }
                                                }
                                            }
                                        },
                                        showBackground: true,//柱子背景色
                                        backgroundStyle: {
                                            color: 'rgba(255, 255, 255, 1)'
                                        }
                                    }]
                                };
                                var labelMargin = 10
                                for (var lvl = levels - 1; lvl >= 0; lvl--) {
                                    var yAxisData = []
                                    var yAxisItem = {
                                        type: "category",
                                        position: "left",
                                        data: yAxisData,
                                        axisTick: {
                                            length: tickLength * (levels - lvl)
                                        },
                                        axisLine: { lineStyle: { color: "rgba(244, 248, 254,1)" } },
                                        axisLabel: {
                                            interval: 0,
                                            margin: 5 + labelMargin * (levels - lvl - 1) + tickLength * (levels - lvl - 1),
                                            textStyle: { color: "rgba(0,0,0,1)", fontSize: lvl == 1? '13': '16',fontFamily: 'Microsoft YaHei'},
                                            rotate: 90 * (levels - lvl - 1),
                                            verticalAlign: "middle",
                                        }
                                    }
                                    if (lvl == levels - 1) {
                                        for (let i = 0; i < data.length; i++) {
                                            yAxisData.push(data[i][lvl])
                                        }
                                    } else {
                                        var tickSeparates = []
                                        var prevData = ""
                                        var prevDataNum = 1
                                        for (let i = 0; i < data.length; i++) {
                                            var lvlData = data[i][lvl]
                                            yAxisData.push("")
                                            if (prevData != lvlData) {
                                                tickSeparates.push(true)
                                                if (i > 0) {
                                                    if (prevDataNum % 2 == 0) {
                                                        yAxisData[i - parseInt(prevDataNum / 2) - 1] = labelOffset + prevData
                                                    } else {
                                                        yAxisData[i - parseInt(prevDataNum / 2) - 1] = prevData
                                                    }
                                                   
                                                }
                                                
                                                prevDataNum = 1
                                            } else {
                                                tickSeparates.push(false)
                                                prevDataNum++
                                            }
                                            prevData = lvlData
                                        }
                                       
                                        if (prevDataNum % 2 == 0) {
                                            yAxisData[yAxisData.length - parseInt(prevDataNum / 2) - 1] = labelOffset + prevData
                                        } else {
                                            yAxisData[yAxisData.length - parseInt(prevDataNum / 2) - 1] = prevData
                                        }
                                       // yAxisData=['物理','化学','生物']
                                        yAxisItem.axisTick.interval = ((tickSeparates) => {
                                            return (index, value) => {
                                                return tickSeparates[index]
                                            }
                                        })(tickSeparates)
                                        yAxisItem.axisLabel.rich = {
                                            offset: {
                                                width: 0
                                            }
                                        }
                                    }
                                    yAxis.push(yAxisItem)
                                       console.log("yAxisData",yAxisData,lvl)
                                }
                                return option
                            }
                            var levels = 2
                            var option = toEachartsOption(TitleArr, levels, 60, AvgArr)
                            myChart.lastBandWidth = 0;
                            // 监听渲染事件，只要bandWidth发生变化，重新设置标签偏移的值
                            myChart.on('rendered', function () {
                                var bandWidth = myChart._chartsViews[0].renderTask.context.outputData._layout.bandWidth;
                                if (myChart.lastBandWidth != bandWidth) {
                                    myChart.lastBandWidth = bandWidth;
                                    // 延时执行否则echarts渲染异常
                                    setTimeout(() => {
                                        // 加上偏移后重新绘制
                                        var optionNew = { yAxis: [{}, {}] };
                                        // 增量更新偏移值
                                        optionNew.yAxis[1] = { axisLabel: { rich: { offset: { width: myChart.lastBandWidth } } } };
                                        //console.info(optionNew);
                                        myChart.setOption(optionNew);
                                    }, 0);
                                }
                            });
                            // 绘制
                            myChart.setOption(option);
              }
          },
       }); 
    }
</script>
