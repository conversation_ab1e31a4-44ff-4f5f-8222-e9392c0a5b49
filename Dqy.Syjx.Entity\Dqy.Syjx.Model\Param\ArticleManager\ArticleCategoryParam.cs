﻿using System;
using System.Collections.Generic;
using Newtonsoft.Json;
using Dqy.Syjx.Util;

namespace Dqy.Syjx.Model.Param.ArticleManager
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2022-01-21 10:43
    /// 描 述：实体查询类
    /// </summary>
    public class ArticleCategoryListParam
    {
        private string _ids;
        public string Ids
        {
            get { return StringFilter.SearchSql(_ids); }
            set { _ids = value; }
        }

        private string _name;
        public string Name 
        {
            get { return StringFilter.SearchSql(_name); }
            set { _name = value; }
        }
        public long? Pid { get; set; }


        /// <summary>
        /// 创建人单位Id
        /// </summary>
        public long UnitId { get; set; }

        /// <summary>
        /// 是否管理员
        /// </summary>
        public int? IsSystem { get; set; }
    }
}
