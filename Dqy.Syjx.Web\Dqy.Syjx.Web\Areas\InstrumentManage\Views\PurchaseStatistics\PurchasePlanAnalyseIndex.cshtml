﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
    int UnitType = (int)ViewBag.UnitType;
}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .select-list li input {
        width: auto !important;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <span id="purchaseYear" col="PurchaseYear" style="display:inline-block;width:90px;"></span>
                    </li>
                    <li>
                        <span id="schoolStageId" col="StageId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li id="liCountyId" style="display:none;">
                        <span id="countyId" col="CountyId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li id="liSchoolId" style="display:none;">
                        <span id="schoolId" col="SchoolId" style="display:inline-block;width:200px;"></span>
                    </li>
                    <li>
                        <span id="courseId" col="CourseId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-default btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group-sm hidden-xs" id="toolbar">
            <span id="schoolAvgTip"></span>
            <a id="btnLookBySchool" class="btn btn-success btn-sm" onclick="switchLookType()"><i class="fa fa-navicon"></i>&nbsp;按学校查看</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">

    var listLookType = 1; //列表查看类型，1：按学校查看，2：按区县查看
    var SortName = '';  //列表默认排序
    var queryUrl = '@Url.Content("~/InstrumentManage/PurchaseStatistics/GetCountyPurchasePlanAnalyseListJson")'; //查询方法

    $(function () {

        if (@UnitType == @UnitTypeEnum.County.ParseToInt()) {
            loadSchool(0);
            $('#liSchoolId').show();
            SortName = 'SchoolSort asc ,SchoolId asc ,StageId asc';
        }
        else if (@UnitType == @UnitTypeEnum.City.ParseToInt()) {
            listLookType = 2;
            loadCounty();
            $('#liCountyId').show();
            SortName = 'CountySort asc ,CountyId asc ,StageId asc';
            queryUrl  = '@Url.Content("~/InstrumentManage/PurchaseStatistics/GetCityPurchasePlanAnalyseListJson")'; //查询方法
        }

        getYear();
        loadSchoolStage();
        
        

        initGrid();
    });

    function initGrid() {
        $('#gridTable').ysTable({
            url: queryUrl,
            sortName: SortName,
            columns: [
                {
                    field: 'AreaName', title: '区县名称', halign: 'center', align: 'center', visible: (@UnitType == @UnitTypeEnum.City.ParseToInt()), width: 100,
                    formatter: function (value, row, index) {
                        if (@UnitType == @UnitTypeEnum.City.ParseToInt() && !row.StageId) {
                            return '<b>总计（元）：</b>';
                        }
                        else {
                            return value || '';
                        }
                    }
                },
                {
                    field: 'SchoolName', title: '单位名称',  halign: 'center', align: 'left', visible: (@UnitType != @UnitTypeEnum.School.ParseToInt() && listLookType == 1), width: 150,
                    formatter: function (value, row, index) {
                        if (@UnitType == @UnitTypeEnum.County.ParseToInt() && !row.StageId) {
                            return '<b>总计（元）：</b>';
                        }
                        else {
                            return value || '';
                        }
                    }
                },
                {
                    field: 'PurchaseYear', title: '采购年度', halign: 'center', align: 'center', width: commonWidth.Instrument.PlanYear,
                    formatter: function (value, row, index) {
                        return value || '';
                    }
                },
                {
                    field: 'Stage', title: '学段', halign: 'center', align: 'center',  width: commonWidth.Instrument.StageName,
                    formatter: function (value, row, index) {
                        return value || '';
                    }
                },
                {
                    field: 'AmountSum', title: '采购总额', halign: 'center', align: 'right', width: 100,
                    formatter: function (value, row, index) {
                        if (row.StageId) return ComBox.ToLocaleString(value);
                        else return '<b>' + ComBox.ToLocaleString(value); + '</b>';
                    }
                },
                {
                    field: 'StudentAvgAmount', title: '生均额', halign: 'center', align: 'right', width: 80,
                    formatter: function (value, row, index) {
                        if (row.StageId) {
                            if (value < 0) return '<span style="color:red" title="该单位尚未初始化学生人数">--</span>';
                            else return ComBox.ToLocaleString(value);
                        }
                        else return '';
                    }
                },
                {
                    field: 'StudentDiffMount', title: '生均差额', halign: 'center', align: 'right',  width: 80,
                    formatter: function (value, row, index) {
                        if (row.StageId) {
                            if (row.StudentAvgAmount < 0) return '<span style="color:red" title="该单位尚未初始化学生人数">--</span>';
                            else return ComBox.ToLocaleString(value);
                        }
                        else return '';
                    }
                },
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function resetGrid() {
        //清空条件
        $('#countyId').ysComboBox('setValue', -1);
        $("#schoolStageId").ysComboBox('setValue', -1);
        $("#schoolId").ysComboBox('setValue', -1);
        if (@UnitType == @UnitTypeEnum.County.ParseToInt()) 
        {
            loadSchool(0);
        }
        $("#courseId").ysComboBox('setValue', -1);
        $("#purchaseYear").ysComboBox('setValue', new Date().getFullYear());
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function loadSchool(schoolStageId) {
        ys.ajax({
            url: '@Url.Content("~/OrganizationManage/Unit/GetChildrenPageList")' + "?PageSize=10000&SchoolStageId=" + schoolStageId,
            data: null,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1 && obj.Data) {
                    $('#schoolId').ysComboBox({
                        data: obj.Data,
                        key: 'Id',
                        value: 'Name',
                        defaultName: '单位名称'
                    });
                }
            }
        });
    }

    function loadCounty() {
        ys.ajax({
            url: '@Url.Content("~/OrganizationManage/Unit/GetCountyBoxByCityIdJson")',
            data: null,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1 && obj.Data) {
                    $('#countyId').ysComboBox({
                        data: obj.Data,
                        key: 'Id',
                        value: 'Name',
                        defaultName: '区县名称',
                        onChange: function () {
                            if (listLookType == 1)
                                loadSchoolByCountyId($('#countyId').ysComboBox('getValue'));
                        }
                    });
                }
            }
        });
    }

    function loadSchoolByCountyId(countyId) {
        if (countyId > 0) {
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/Unit/GetUnitList?")' + 'Pid=' + countyId,
                data: null,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1 && obj.Data) {
                        $('#schoolId').ysComboBox({
                            data: obj.Data,
                            key: 'Id',
                            value: 'Name',
                            defaultName: '单位名称'
                        });
                    }
                }
            });
        }
        else {
            $('#schoolId').ysComboBox({ defaultName: '单位名称' });
        }
    }

    function getYear() {
        var currDate = new Date();
        var currYear = currDate.getFullYear();
        var yearData = [];
        for (i = 0; i < 10; i++) {
            if (currYear + 1 - i < 2021)
                continue;
            yearData.push({ "id": currYear + 1 - i, "text": currYear + 1 - i });
        }
        $("#purchaseYear").ysComboBox({
            data: yearData,
            key: "id",
            value: "text",
            defaultName: '采购年度'
        }).ysComboBox('setValue', currYear);
    }

    //市级查询时，切换按学校/区县查看
    function switchLookType() {
        //列表查看类型，1：按学校查看，2：按区县查看
        if (listLookType == 1) {
            listLookType = 2;
            SortName = 'CountySort asc ,CountyId asc ,StageId asc';
            queryUrl = '@Url.Content("~/InstrumentManage/PurchaseStatistics/GetCityPurchasePlanAnalyseListJson")'; //查询方法
            $('#liSchoolId').hide();
            $('#btnLookBySchool').html('<i class="fa fa-navicon"></i>&nbsp;按学校查看');
        }
        else {
            listLookType = 1;
            SortName = 'CountySort asc ,CountyId asc ,SchoolSort asc ,SchoolId asc ,StageId asc';
            queryUrl = '@Url.Content("~/InstrumentManage/PurchaseStatistics/GetSchoolPurchasePlanAnalyseByCityJson")'; //查询方法
            $('#liSchoolId').show();
            loadSchoolByCountyId($('#countyId').ysComboBox('getValue'));
            $('#btnLookBySchool').html('<i class="fa fa-navicon"></i>&nbsp;按区县查看');
        }
        $('#gridTable').bootstrapTable('destroy');
        initGrid();
    }

    function loadSchoolStage() {
        $('#schoolStageId').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetStageByUserId")',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '学段',
            onChange: function () {
                if (@UnitType == @UnitTypeEnum.County.ParseToInt()) {
                    var schoolStageId = $('#schoolStageId').ysComboBox("getValue");
                    if (schoolStageId > 0) {
                        loadSchool(schoolStageId);
                    }
                }
            }
        });
        $("#courseId").ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetCourseByUserId")',
            defaultName: '适用学科',
            key: 'DictionaryId',
            value: 'DicName'
        });
    }
</script>
