using System;
using System.IO;
using System.Web;
using System.Text;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util;
using Dqy.Syjx.Entity.SystemManage;
using Dqy.Syjx.Model.Input.SystemManage;
using Dqy.Syjx.Business.SystemManage;
using Dqy.Syjx.Model.Param.SystemManage;
using Dqy.Syjx.Entity.BusinessManage;
using Dqy.Syjx.Service.BusinessManage;

namespace Dqy.Syjx.Business
{
    public class FileHelper
    {
       

        #region 创建文本文件
        /// <summary>
        /// 创建文件
        /// </summary>
        /// <param name="path"></param>
        /// <param name="content"></param>
        public static void CreateFile(string path, string content)
        {
            if (!Directory.Exists(Path.GetDirectoryName(path)))
            {
                Directory.CreateDirectory(Path.GetDirectoryName(path));
            }
            using (StreamWriter sw = new StreamWriter(path, false, Encoding.UTF8))
            {
                sw.Write(content);
            }
        }
        #endregion

        #region 上传单个文件
        /// <summary>
        /// 上传单个文件
        /// </summary>
        /// <param name="fileCategory">文件分类</param>
        /// <param name="fileCollection">文件集合</param>
        /// <returns></returns>
        public async static Task<TData<string>> UploadFile(int fileCategory, IFormFileCollection files ,string savePath = "")
        {
            string dirModule = string.Empty;
            TData<string> obj = new TData<string>();
            if (files == null || files.Count == 0)
            {
                obj.Message = "请先选择文件！";
                return obj;
            }
            if (files.Count > 1)
            {
                obj.Message = "一次只能上传一个文件！";
                return obj;
            }
             AttachmentConfigBLL attachmentConfigBLL = new AttachmentConfigBLL();
            var attCfgList = await attachmentConfigBLL.GetList(new AttachmentConfigListParam { FileCategory = fileCategory });
            if(attCfgList.Data.Count <= 0)
            {
                obj.Message = "上传文件未配置，无法上传！";
                return obj;
            }
            var attCfg = attCfgList.Data.FirstOrDefault();
                      
            IFormFile file = files[0];
            TData objCheck = CheckFileExtension(Path.GetExtension(file.FileName), attCfg.UploadFileExt);
            if (objCheck.Tag != 1)
            {
                obj.Message = objCheck.Message;
                return obj;
            }
            if (file.Length > attCfg.UploadFileSize * 1024 * 1024) // MB
            {
                obj.Message = $"文件最大限制为 {attCfg.UploadFileSize}MB";
                return obj;
            }
            dirModule = attCfg.SaveDir;


            string fileExtension = TextHelper.GetCustomValue(Path.GetExtension(file.FileName), ".png");

            string newFileName = SecurityHelper.GetGuid(true) + fileExtension;
            string dir = "Resource" + Path.DirectorySeparatorChar + dirModule + Path.DirectorySeparatorChar + DateTime.Now.ToString("yyyy-MM-dd").Replace('-', Path.DirectorySeparatorChar) + Path.DirectorySeparatorChar;

            string absoluteDir = "";
            if (!savePath.IsEmpty())
            {
                absoluteDir = Path.Combine(savePath, dir);
            }
            else
            {
                absoluteDir = Path.Combine(GlobalContext.HostingEnvironment.ContentRootPath, dir);
            }
            string absoluteFileName = string.Empty;
            if (!Directory.Exists(absoluteDir))
            {
                Directory.CreateDirectory(absoluteDir);
            }
            absoluteFileName = absoluteDir + newFileName;
            try
            {
                using (FileStream fs = File.Create(absoluteFileName))
                {
                    await file.CopyToAsync(fs);
                    fs.Flush();
                }
                //路径
                obj.Data = Path.AltDirectorySeparatorChar + ConvertDirectoryToHttp(dir) + newFileName;
                //文件名
                obj.Message = Path.GetFileNameWithoutExtension(TextHelper.GetCustomValue(file.FileName, newFileName));
                //文件大小
                obj.Total = Convert.ToInt32(Math.Round(file.Length / 1024d, 0)); // KB
                obj.Tag = 1;

                var entity = new AttachmentEntity
                {
                    Title = obj.Message,
                    FileCategory = fileCategory,
                    Path = obj.Data,                   
                    Ext = fileExtension,
                    IsDefault = 0,
                    IsShow = 1,
                    IsDelete = 0
                };
               
                AttachmentService attachmentService = new AttachmentService();
                await attachmentService.SaveForm(entity);
                //附件Id
                obj.Description = entity.Id.ParseToString();


            }
            catch (Exception ex)
            {
                obj.Message = ex.Message;
                throw new Exception("程序出现异常，异常信息为：" + ex.Message);
            }
            return obj;
        }

        public async static Task<TData<string>> UploadHelpFile(int fileCategory, IFormFileCollection files, string savePath = "")
        {
            string dirModule = string.Empty;
            TData<string> obj = new TData<string>();
            if (files == null || files.Count == 0)
            {
                obj.Message = "请先选择文件！";
                return obj;
            }
            if (files.Count > 1)
            {
                obj.Message = "一次只能上传一个文件！";
                return obj;
            }
            AttachmentConfigBLL attachmentConfigBLL = new AttachmentConfigBLL();
            var attCfgList = await attachmentConfigBLL.GetList(new AttachmentConfigListParam { FileCategory = fileCategory });
            if (attCfgList.Data.Count <= 0)
            {
                obj.Message = "上传文件未配置，无法上传！";
                return obj;
            }
            var attCfg = attCfgList.Data.FirstOrDefault();

            IFormFile file = files[0];
            TData objCheck = CheckFileExtension(Path.GetExtension(file.FileName), attCfg.UploadFileExt);
            if (objCheck.Tag != 1)
            {
                obj.Message = objCheck.Message;
                return obj;
            }
            if (file.Length > attCfg.UploadFileSize * 1024 * 1024) // MB
            {
                obj.Message = $"文件最大限制为 {attCfg.UploadFileSize}MB";
                return obj;
            }
            dirModule = attCfg.SaveDir;


            string fileExtension = TextHelper.GetCustomValue(Path.GetExtension(file.FileName), ".png");

            string newFileName = SecurityHelper.GetGuid(true) + fileExtension;
            string dir = "Resource" + Path.DirectorySeparatorChar + dirModule + Path.DirectorySeparatorChar + DateTime.Now.ToString("yyyy-MM-dd").Replace('-', Path.DirectorySeparatorChar) + Path.DirectorySeparatorChar;

            string absoluteDir = "";
            if (!savePath.IsEmpty())
            {
                absoluteDir = Path.Combine(savePath, dir);
            }
            else
            {
                absoluteDir = Path.Combine(GlobalContext.HostingEnvironment.ContentRootPath, dir);
            }
            string absoluteFileName = string.Empty;
            if (!Directory.Exists(absoluteDir))
            {
                Directory.CreateDirectory(absoluteDir);
            }
            absoluteFileName = absoluteDir + newFileName;
            try
            {
                using (FileStream fs = File.Create(absoluteFileName))
                {
                    await file.CopyToAsync(fs);
                    fs.Flush();
                }
                //路径
                obj.Data = Path.AltDirectorySeparatorChar + ConvertDirectoryToHttp(dir) + newFileName;
                //文件名
                obj.Message = Path.GetFileNameWithoutExtension(TextHelper.GetCustomValue(file.FileName, newFileName));
                //文件大小
                obj.Total = Convert.ToInt32(Math.Round(file.Length / 1024d, 0)); // KB
                obj.Tag = 1;

                var entity = new AttachmentEntity
                {
                    Title = obj.Message,
                    FileCategory = fileCategory,
                    Path = obj.Data,
                    Ext = fileExtension,
                    IsDefault = 0,
                    IsShow = 1,
                    IsDelete = 0
                };

                AttachmentService attachmentService = new AttachmentService();
                await attachmentService.SaveForm(entity);
                //附件Id
                obj.Description = entity.Id.ParseToString();


            }
            catch (Exception ex)
            {
                obj.Message = ex.Message;
                throw new Exception("程序出现异常，异常信息为：" + ex.Message);
            }
            return obj;
        }
        #endregion

        #region 删除单个文件
        /// <summary>
        /// 删除单个文件
        /// </summary>
        /// <param name="fileModule"></param>
        /// <param name="filePath"></param>
        /// <returns></returns>
        public static TData<string> DeleteFile(int fileModule, string filePath)
        {
            TData<string> obj = new TData<string>();
            string dirModule = fileModule.GetDescriptionByEnum<UploadFileType>();

            if (string.IsNullOrEmpty(filePath))
            {
                obj.Message = "请先选择文件！";
                return obj;
            }
            filePath = "Resource" + Path.DirectorySeparatorChar + dirModule + Path.DirectorySeparatorChar + filePath;
            string absoluteDir = Path.Combine(GlobalContext.HostingEnvironment.ContentRootPath, filePath);
            try
            {
                if (File.Exists(absoluteDir))
                {
                    File.Delete(absoluteDir);
                }
                else
                {
                    obj.Message = "文件不存在";
                }
                obj.Tag = 1;
            }
            catch (Exception ex)
            {
                obj.Message = ex.Message;
                throw new Exception("程序出现异常，异常信息为：" + ex.Message);
            }
            return obj;
        }
        #endregion

        #region 下载文件 高危，且未使用
        ///// <summary>
        ///// 下载文件
        ///// </summary>
        ///// <param name="filePath"></param>
        ///// <param name="delete"></param>
        ///// <returns></returns>
        //public static TData<FileContentResult> DownloadFile(string filePath, int delete)
        //{
        //    TData<FileContentResult> obj = new TData<FileContentResult>();
        //    string absoluteFilePath = GlobalContext.HostingEnvironment.ContentRootPath + Path.DirectorySeparatorChar + filePath?.Replace(Path.AltDirectorySeparatorChar, Path.DirectorySeparatorChar);
        //    byte[] fileBytes = File.ReadAllBytes(absoluteFilePath);
        //    //if (delete == 1)
        //    //{
        //    //    File.Delete(absoluteFilePath);
        //    //}
        //    string fileNamePrefix = DateTime.Now.ToString("yyyyMMddHHmmss");
        //    string fileNameWithoutExtension = Path.GetFileNameWithoutExtension(filePath);
        //    string title = string.Empty;
        //    if (fileNameWithoutExtension.Contains("_"))
        //    {
        //        title = fileNameWithoutExtension.Split('_')[1].Trim();
        //    }
        //    string fileExtensionName = Path.GetExtension(filePath);
        //    obj.Data = new FileContentResult(fileBytes, "application/octet-stream")
        //    {
        //        FileDownloadName = string.Format("{0}_{1}{2}", fileNamePrefix, title, fileExtensionName)
        //    };
        //    obj.Tag = 1;
        //    return obj;
        //}
        #endregion 

        #region GetContentType
        public static string GetContentType(string path)
        {
            var types = GetMimeTypes();
            var ext = Path.GetExtension(path).ToLowerInvariant();
            var contentType = types[ext];
            if (string.IsNullOrEmpty(contentType))
            {
                contentType = "application/octet-stream";
            }
            return contentType;
        }
        #endregion

        #region GetMimeTypes
        public static Dictionary<string, string> GetMimeTypes()
        {
            return new Dictionary<string, string>
            {
                {".txt", "text/plain"},
                {".pdf", "application/pdf"},
                {".doc", "application/vnd.ms-word"},
                {".docx", "application/vnd.ms-word"},
                {".xls", "application/vnd.ms-excel"},
                {".xlsx", "application/vnd.openxmlformats officedocument.spreadsheetml.sheet"},
                {".png", "image/png"},
                {".jpg", "image/jpeg"},
                {".jpeg", "image/jpeg"},
                {".gif", "image/gif"},
                {".csv", "text/csv"}
            };
        }
        #endregion

        public static void CreateDirectory(string directory)
        {
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }
        }

        public static void DeleteDirectory(string filePath)
        {
            try
            {
                if (Directory.Exists(filePath)) //如果存在这个文件夹删除之 
                {
                    foreach (string d in Directory.GetFileSystemEntries(filePath))
                    {
                        if (File.Exists(d))
                            File.Delete(d); //直接删除其中的文件                        
                        else
                            DeleteDirectory(d); //递归删除子文件夹 
                    }
                    Directory.Delete(filePath, true); //删除已空文件夹                 
                }
            }
            catch (Exception ex)
            {
                throw new Exception("程序出现异常，异常信息为：" + ex.Message);
            }
        }

        public static string ConvertDirectoryToHttp(string directory)
        {
            directory = directory.ParseToString();
            directory = directory.Replace(Path.DirectorySeparatorChar, Path.AltDirectorySeparatorChar);
            return directory;
        }

        public static string ConvertHttpToDirectory(string http)
        {
            http = http.ParseToString();
            http = http.Replace(Path.AltDirectorySeparatorChar, Path.DirectorySeparatorChar);
            return http;
        }

        public static TData CheckFileExtension(string fileExtension, string allowExtension)
        {
            TData obj = new TData();
            string[] allowArr = TextHelper.SplitToArray<string>(allowExtension.ToLower(), '|');
            if (allowArr.Where(p => p.Trim() == fileExtension.ParseToString().ToLower()).Any())
            {
                obj.Tag = 1;
            }
            else
            {
                obj.Message = "只有文件扩展名是 " + allowExtension + " 的文件才能上传";
            }
            return obj;
        }
    }
}
