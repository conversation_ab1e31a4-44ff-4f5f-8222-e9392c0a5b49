﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.EvaluateManage;
using Dqy.Syjx.Model.Param.EvaluateManage;
using Dqy.Syjx.Enum.InstrumentManage;
using Dqy.Syjx.Enum;

namespace Dqy.Syjx.Service.EvaluateManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-11-29 13:16
    /// 描 述：仪器清单服务类
    /// </summary>
    public class InstrumentEvaluateListService : RepositoryFactory
    {
        #region 获取数据
        public async Task<List<InstrumentEvaluateListEntity>> GetList(InstrumentEvaluateListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<InstrumentEvaluateListEntity>> GetPageList(InstrumentEvaluateListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<InstrumentEvaluateListEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<InstrumentEvaluateListEntity>(id);
        }

        /// <summary>
        /// 获取达标结果列表
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<List<InstrumentEvaluateListEntity>> GetStandardPageList(InstrumentEvaluateListParam param, Pagination pagination)
        {
            StringBuilder sql = new StringBuilder();
            var expression = ListFilter(param, sql);
            var list = await this.BaseRepository().FindList<InstrumentEvaluateListEntity>(sql.ToString(), expression.ToArray(), pagination);
            return list.ToList();
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(InstrumentEvaluateListEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(InstrumentEvaluateListEntity entity, Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql = $" update eq_InstrumentEvaluateList set BaseIsDelete = 1 where Id in ({ids}) ";
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        /// <summary>
        /// 更新状态
        /// </summary>
        /// <param name="standardid"></param>
        /// <param name="ids"></param>
        /// <param name="statuz"></param>
        /// <returns></returns>
        public async Task UpdateStatuzForm(long standardid, string ids,int statuz)
        {
            string strSql = $" update eq_InstrumentEvaluateList set Statuz = {statuz} where Id in ({ids}) AND EvaluateStandardId = {standardid} ";
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        /// <summary>
        /// 更新是否评估
        /// </summary>
        /// <param name="standardid"></param>
        /// <param name="ids"></param>
        /// <param name="isevaluate"></param>
        /// <returns></returns>
        public async Task UpdateEvaluateForm(long standardid, string ids, int isevaluate)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");

            // 使用参数化查询和跨数据库兼容的 IN 语法
            var parameters = new List<DbParameter>
            {
                DbParameterExtension.CreateDbParameter("@IsEvaluate", isevaluate),
                DbParameterExtension.CreateDbParameter("@EvaluateStandardId", standardid)
            };

            string strSql = $"UPDATE eq_InstrumentEvaluateList SET IsEvaluate = @IsEvaluate WHERE Id IN ({ids}) AND EvaluateStandardId = @EvaluateStandardId";
            await this.BaseRepository().ExecuteBySql(strSql, parameters.ToArray());
        }
        /// <summary>
        /// 更新调控系数
        /// </summary>
        /// <param name="standardid"></param>
        /// <param name="ids"></param>
        /// <param name="isevaluate"></param>
        /// <returns></returns>
        public async Task UpdateRatioForm(long standardid, string ids, decimal ratio)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");

            // 使用参数化查询和跨数据库兼容的 IN 语法
            var parameters = new List<DbParameter>
            {
                DbParameterExtension.CreateDbParameter("@RegulationRatio", ratio),
                DbParameterExtension.CreateDbParameter("@EvaluateStandardId", standardid)
            };

            string strSql = $"UPDATE eq_InstrumentEvaluateList SET RegulationRatio = @RegulationRatio WHERE Id IN ({ids}) AND EvaluateStandardId = @EvaluateStandardId";
            await this.BaseRepository().ExecuteBySql(strSql, parameters.ToArray());
        }
        /// <summary>
        /// 设置忽略小数值
        /// </summary>
        /// <param name="standardid"></param>
        /// <param name="ids"></param>
        /// <param name="isevaluate"></param>
        /// <returns></returns>
        public async Task UpdateSmallNumForm(long standardid, string ids, decimal smallnum)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update eq_InstrumentEvaluateList set NeglectSmallNum = {smallnum} where Id in ({ids}) AND EvaluateStandardId = {standardid} ";
            }
            else
            {
                strSql = $"update eq_InstrumentEvaluateList set NeglectSmallNum = {smallnum} where id = {ids} AND EvaluateStandardId = {standardid} ";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<InstrumentEvaluateListEntity, bool>> ListFilter(InstrumentEvaluateListParam param)
        {
            var expression = LinqExtensions.True<InstrumentEvaluateListEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == param.BaseIsDelete);
            if (param != null)
            {
                if (param.EvaluateStandardId > 0)
                {
                    expression = expression.And(t => t.EvaluateStandardId == param.EvaluateStandardId);
                }
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }

                if (param.AllocateType > 0)
                {
                    expression = expression.And(t => t.AllocateType == param.AllocateType);
                }
                if (param.IsEvaluate>=0)
                {
                    expression = expression.And(t => t.IsEvaluate == param.IsEvaluate);
                }
                if (!string.IsNullOrEmpty(param.Name))
                {
                    expression = expression.And(t => t.InstrumentName.Contains(param.Name) || t.EvaluateCode.Contains(param.Name));
                }
            }
            return expression;
        }


        /// <summary>
        /// 仪器达标结果表
        /// </summary>
        /// <param name="param"></param>
        /// <param name="strSql"></param>
        /// <returns></returns>
        private List<DbParameter> ListFilter(InstrumentEvaluateListParam param, StringBuilder strSql)
        {
            strSql.Append(@$"SELECT * ,(StockNum - StandardValue) AS Difference FROM
                            (
	                            SELECT EL.Id ,ES.SchoolStage ,ES.DictionaryId1005 , SD.DicName AS SchoolStageName ,SD2.DicName AS CourseName ,
		                               IAS.InstrumentCode AS Code ,IAS.Name AS InstrumentName ,IAS.Model AS ParamDemand ,IAS.UnitName ,IAS.AllocateType ,
		                               IAS.StandardNum AS StandardValue ,IAS.StockNum
	                            FROM eq_InstrumentAttendStatic AS IAS
	                            INNER JOIN eq_InstrumentEvaluateList AS EL ON IAS.EvaluateListId = EL.Id AND EL.BaseIsDelete = 0 AND EL.Statuz = 1
	                            INNER JOIN eq_InstrumentEvaluateProject AS EP ON IAS.InstrumentEvaluateProjectId = EP.Id
	                            INNER JOIN eq_InstrumentEvaluateStandard AS ES ON EL.EvaluateStandardId = ES.Id AND ES.Statuz = 1 AND ES.BaseIsDelete = 0
	                            INNER JOIN sys_static_dictionary AS SD ON ES.SchoolStage = SD.DictionaryId AND SD.TypeCode = '1002' AND SD.BaseIsDelete = 0
	                            INNER JOIN sys_static_dictionary AS SD2 ON ES.DictionaryId1005 = SD2.DictionaryId AND SD2.TypeCode = '1005' AND SD2.BaseIsDelete = 0
	                            WHERE EP.BaseIsDelete = 0 AND EP.Id = {param.InstrumentEvaluateProjectId} AND IAS.SchoolId = {param.SchoolId}
                            ) AS T WHERE 1 = 1");

            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (!param.SchoolStageId.IsNullOrZero())
                {
                    strSql.Append(" AND SchoolStage = @SchoolStage ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStage", param.SchoolStageId));
                }
                if (!param.CourseId.IsNullOrZero())
                {
                    strSql.Append(" AND DictionaryId1005 = @CourseId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (!string.IsNullOrWhiteSpace(param.KeyWord))
                {
                    strSql.Append($" AND (InstrumentName LIKE '%{param.KeyWord.Trim()}%' OR Code LIKE '%{param.KeyWord.Trim()}%') ");
                }
                if (param.IsOnlyShowNoStandard == 1)
                {
                    strSql.Append(" AND (StockNum - StandardValue) < 0 ");
                }
            }
            return parameter;
        }
        #endregion
    }
}
