﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.BusinessManage;
using Dqy.Syjx.Model.Param.BusinessManage;
using Dqy.Syjx.Enum;

namespace Dqy.Syjx.Service.BusinessManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-11-16 09:12
    /// 描 述：服务类
    /// </summary>
    public class EmergencyPlanService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<EmergencyPlanEntity>> GetList(EmergencyPlanListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<EmergencyPlanEntity>> GetPageList(EmergencyPlanListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        /// <summary>
        /// 应急预案与演练列表
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<List<EmergencyPlanEntity>> GetPlanPageList(EmergencyPlanListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            var parameter = new List<DbParameter>();
            strSql.Append(@"SELECT * FROM
                            (SELECT  TSE.Id, TSE.BaseIsDelete,TSE.UnitId,TSE.Name,TSE.PersonCharge,TSE.Address, TSE.EffectiveDate,
	                                YEAR(EffectiveDate) AS EffectiveYear,Trainees,U.Name AS SchoolName,UR.UnitId AS CountyId,TSE.BaseCreateTime
                            FROM  bn_EmergencyPlan AS TSE
                            INNER JOIN  up_Unit AS U ON TSE.UnitId = U.Id
                            INNER JOIN  up_UnitRelation AS UR ON U.Id = UR.ExtensionObjId AND UR.ExtensionType = 3
                            WHERE TSE.BaseIsDelete = 0) A WHERE 1 = 1");

            if (!string.IsNullOrEmpty(param.StartTime.ParseToString()))
            {
                strSql.Append(" AND EffectiveDate >= @StartTime ");
                parameter.Add(DbParameterExtension.CreateDbParameter("@StartTime", param.StartTime));
            }
            if (!string.IsNullOrEmpty(param.EndTime.ParseToString()))
            {
                param.EndTime = param.EndTime.Value.Date.Add(new TimeSpan(23, 59, 59));
                strSql.Append(" AND EffectiveDate <= @EndTime ");
                parameter.Add(DbParameterExtension.CreateDbParameter("@EndTime", param.EndTime));
            }
            if (param.Trainees > -1)
            {
                if (param.Trainees.Equals(TrainingObject.StudentAndTeacher.ParseToInt()))
                {
                    strSql.Append(" AND Trainees = @Trainees");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Trainees", param.Trainees));
                }
                else
                {
                    strSql.AppendFormat(" AND (Trainees = {0} OR Trainees = {1})", param.Trainees, TrainingObject.StudentAndTeacher.ParseToInt());
                }
            }
            if (param.UnitId > -1)
            {
                strSql.Append(" AND UnitId = @UnitId");
                parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
            }
            if (param.CountyId > -1)
            {
                strSql.Append(" AND CountyId = @CountyId");
                parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
            }
            if (param.Year > -1)
            {
                strSql.Append(" AND EffectiveYear = @EffectiveYear");
                parameter.Add(DbParameterExtension.CreateDbParameter("@EffectiveYear", param.Year));
            }
            var list = await this.BaseRepository().FindList<EmergencyPlanEntity>(strSql.ToString(), parameter.ToArray(), pagination);
            return list.ToList();
        }

        public async Task<EmergencyPlanEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<EmergencyPlanEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(EmergencyPlanEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(EmergencyPlanEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
               strSql = $" update bn_EmergencyPlan set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
               strSql = $"update bn_EmergencyPlan set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<EmergencyPlanEntity, bool>> ListFilter(EmergencyPlanListParam param)
        {
            var expression = LinqExtensions.True<EmergencyPlanEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }

                if (!string.IsNullOrEmpty(param.StartTime.ParseToString()))
                {
                    expression = expression.And(t => t.EffectiveDate >= param.StartTime);
                }
                if (!string.IsNullOrEmpty(param.EndTime.ParseToString()))
                {
                    param.EndTime = param.EndTime.Value.Date.Add(new TimeSpan(23, 59, 59));
                    expression = expression.And(t => t.EffectiveDate <= param.EndTime);
                }

                if (param.Trainees > -1)
                {
                    expression = expression.And(t => t.Trainees == param.Trainees);
                }
                if (param.UnitId > -1)
                {
                    expression = expression.And(t => t.UnitId == param.UnitId);
                }
            }
            return expression;
        }
        #endregion
    }
}
