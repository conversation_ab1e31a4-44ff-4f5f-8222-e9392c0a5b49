﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.OrganizationManage;
using Dqy.Syjx.Model.Param.OrganizationManage;
using Dqy.Syjx.Entity.SystemManage;
using Dqy.Syjx.Entity.ExperimentTeachManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Model.Param.ExperimentTeachManage;

namespace Dqy.Syjx.Service.OrganizationManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2023-10-12 13:51
    /// 描 述：服务类
    /// </summary>
    public class UserSubjectSetService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<UserSubjectSetEntity>> GetList(UserSubjectSetListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<UserSubjectSetEntity>> GetPageList(UserSubjectSetListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<UserSubjectSetEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<UserSubjectSetEntity>(id);
        }

        /// <summary>
        /// 根据用户Id获取设置学段信息
        /// </summary>
        /// <param name="userId">用户Id</param>
        /// <returns></returns>
        public async Task<List<StaticDictionaryEntity>> GetUserSetStageList(long userId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(@$"
                SELECT SD.Id,SD.DictionaryId,SD.DicName,SD.Sequence,ISNULL(USS.Id,0) AS UserSubjectSetId
                FROM sys_static_dictionary AS SD
                LEFT JOIN up_UserSubjectSet AS USS ON SD.DictionaryId = USS.SubjectValue AND USS.SubjectTypeId = 1 AND USS.UserId = {userId} 
                WHERE SD.TypeCode = '1002' AND SD.BaseIsDelete = 0 AND SD.Statuz = 1 AND SD.DictionaryId <> 1002000");
           
            var list = await this.BaseRepository().FindList<StaticDictionaryEntity>(strSql.ToString());
            return list.ToList();
        }

        /// <summary>
        /// 根据用户Id获取设置学科信息
        /// </summary>
        /// <param name="userId">用户Id</param>
        /// <returns></returns>
        public async Task<List<StaticDictionaryEntity>> GetUserSetCourseList(long userId, int nature = 0)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(@$"
                SELECT SD.Id,SD.DictionaryId,SD.DicName,SD.Sequence,ISNULL(USS.Id,0) AS UserSubjectSetId
                FROM sys_static_dictionary AS SD
                LEFT JOIN up_UserSubjectSet AS USS ON SD.DictionaryId = USS.SubjectValue AND USS.SubjectTypeId = 2 AND USS.UserId = {userId}
                WHERE SD.TypeCode = '1005' AND SD.Pid > 0 AND SD.BaseIsDelete = 0 AND SD.Statuz = 1");
            if (nature == 1)
            {
                strSql.Append(" AND SD.Nature = 1 ");
            }
            var list = await this.BaseRepository().FindList<StaticDictionaryEntity>(strSql.ToString());
            return list.ToList();
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(UserSubjectSetEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(UserSubjectSetEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task UpdateForm(UserSubjectSetEntity entity, List<string> fields, Repository db=null)
        {
            if (fields != null)
            {
                await entity.Modify();
                fields.Add("BaseModifyTime");
                fields.Add("BaseModifierId");
            }
            if (db != null)
            {
                await db.Update(entity, fields);
            }
            else
            {
                await this.BaseRepository().Update(entity, fields);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update up_UserSubjectSet set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
                strSql = $"update up_UserSubjectSet set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task DeleteByUserIdForm(long userId,string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql = $"DELETE FROM up_UserSubjectSet WHERE UserId = {userId} AND SubjectValue IN({ids})";
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<UserSubjectSetEntity, bool>> ListFilter(UserSubjectSetListParam param)
        {
            var expression = LinqExtensions.True<UserSubjectSetEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (param.UnitId > 0)
                {
                    expression = expression.And(f => f.UnitId == param.UnitId);
                }
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }

                if (param.SubjectTypeId > 0)
                {
                    expression = expression.And(f=>f.SubjectTypeId == param.SubjectTypeId);
                }
                if (param.UserId > 0)
                {
                    expression = expression.And(f => f.UserId == param.UserId);
                }
                if (param.SubjectValue>0)
                {
                    expression = expression.And(f => f.SubjectValue == param.SubjectValue);
                }
                if (param.Idnt> 0)
                {
                    expression = expression.And(f => f.Id != param.Idnt);
                }
            }
            return expression;
        }


        /// <summary>
        /// 编制计划实验数量以学校为单位
        /// </summary>
        /// <param name="param"></param>
        /// <param name="strSql"></param>
        /// <returns></returns>
        public async Task<List<UserSubjectSetEntity>> GetOutPageList(UserSubjectSetListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(@$"SELECT * From (SELECT us.*
                ,su.RealName
                ,su.UserName
                ,dic.DicName  AS Subjectz
                FROM up_UserSubjectSet AS us
                INNER JOIN SysUser AS su ON su.BaseIsDelete = 0 AND su.UserStatus = 1 AND us.UserId = su.Id
                INNER JOIN sys_static_dictionary AS dic ON dic.BaseIsDelete = 0 AND dic.TypeCode = '{DicTypeCodeEnum.Course.ParseToInt().ToString()}' AND us.SubjectValue = dic.DictionaryId 
                          ) as T WHERE  SubjectTypeId = 3  ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = @BaseIsDelete ");
                parameter.Add(DbParameterExtension.CreateDbParameter("@BaseIsDelete", param.BaseIsDelete));
                if (param.UnitId > 0)
                {
                    strSql.Append(" AND UnitId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }

                if (param.UserId > 0)
                {
                    strSql.Append(" AND UserId = @UserId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UserId", param.UserId));
                }
                if (param.SubjectValue > 0)
                {
                    strSql.Append(" AND SubjectValue = @SubjectValue ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SubjectValue", param.SubjectValue));
                }
                if (!string.IsNullOrEmpty(param.Name))
                {
                    strSql.Append(" AND RealName like @RealName ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@RealName", $"%{param.Name}%"));
                }

            }
            var list = await this.BaseRepository().FindList<UserSubjectSetEntity>(strSql.ToString(), parameter.ToArray(), pagination);
            return list.ToList();
        }
        #endregion
    }
}
