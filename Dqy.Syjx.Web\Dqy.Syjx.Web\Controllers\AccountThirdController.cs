﻿using Dqy.Syjx.Business.OrganizationManage;
using Dqy.Syjx.Business.PersonManage;
using Dqy.Syjx.Business.SystemManage;
using Dqy.Syjx.Cache.Factory;
using Dqy.Syjx.Data.EF;
using Dqy.Syjx.Entity.OrganizationManage;
using Dqy.Syjx.Entity.PersonManage;
using Dqy.Syjx.Entity.SystemManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Model.Input;
using Dqy.Syjx.Model.Param.OrganizationManage;
using Dqy.Syjx.Model.Param.SystemManage;
using Dqy.Syjx.Model.Result;
using Dqy.Syjx.Service.HttpService;
using Dqy.Syjx.Service.HttpService.Wxzhjy;
using Dqy.Syjx.Service.HttpService.Wxzhjy.Models;
using Dqy.Syjx.Service.OrganizationManage;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Util.ThirdOAuth;
using Dqy.Syjx.Util.ThirdOAuth.Self;
using Dqy.Syjx.Util.Tools;
using Dqy.Syjx.Web.Code;
using Humanizer;
using MathNet.Numerics.Distributions;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.CodeAnalysis.Differencing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using NetTaste;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NPOI.SS.Formula.Functions;
using NPOI.Util;
using NuGet.Configuration;
using Org.BouncyCastle.Crypto.Agreement;
using Refit;
using Senparc.Weixin.MP.AdvancedAPIs.Wxa.MerchantJson;
using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.DirectoryServices.ActiveDirectory;
using System.IdentityModel.Tokens.Jwt;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Reflection;
using System.Reflection.Emit;
using System.Reflection.Metadata;
using System.Runtime.InteropServices;
using System.Text;
using System.Text.Encodings.Web;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Xml;
using static Dqy.Syjx.Model.Result.XmShuangShiApiModel;
using static Dqy.Syjx.Util.ThirdOAuth.CzsSoftAuthCore;
using static Dqy.Syjx.Util.ThirdOAuth.TzSoftAcore;
using static Dqy.Syjx.Util.ThirdOAuth.TztxSoftAcore;
using static ICSharpCode.SharpZipLib.Zip.ExtendedUnixData;
using static System.Formats.Asn1.AsnWriter;

namespace Dqy.Syjx.Web.Controllers
{
    public class AccountThirdController : Controller
    {
        private UserThirdAuthBLL userThirdAuthBLL = new UserThirdAuthBLL();
        private UserBLL userBLL = new UserBLL();
        private LogLoginBLL logLoginBLL = new LogLoginBLL();
        private UnitBLL unitBLL = new UnitBLL();
        private DepartmentBLL departmentBLL = new DepartmentBLL();
        private RoleBLL roleBLL = new RoleBLL();
        private UnitRelationBLL unitRelationBLL = new UnitRelationBLL();
        private UserService userService = new UserService();
        private StudentBLL studentBLL = new StudentBLL();
        private StaticDictionaryBLL dictionaryBLL = new StaticDictionaryBLL();
        private ParentStudentBLL parentStudentBLL = new ParentStudentBLL();

        public IConfiguration Configuration { get; }
        private readonly IHttpClientFactory _httpClientfactory;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private AppManageBLL appManagerBLL = new AppManageBLL();
        private readonly NetHelper _netHelper;

        public AccountThirdController(IConfiguration configuration, IHttpClientFactory httpClientfactory,
            IHttpContextAccessor httpContextAccessor, NetHelper netHelper)
        {
            Configuration = configuration;
            _httpClientfactory = httpClientfactory;
            _httpContextAccessor = httpContextAccessor;
            _netHelper = netHelper;
        }

        public IActionResult LoginOff()
        {
            var ThirdSSO = Configuration.GetSection("sso").Get<ThirdSSO>();
            return Redirect(ThirdSSO.logout);
        }

        #region 对接公司内部统一身份认证

        public async Task<IActionResult> AuthCenterCallback()
        {
            string code = Request.Query["code"];

            if (!string.IsNullOrEmpty(code))
            {
                var sesssionCode = GetSession("sso_third_authcenter");
                if (string.IsNullOrEmpty(sesssionCode))
                {
                    SetSession("sso_third_authcenter", code);
                }
                else if (sesssionCode == code)
                {
                    throw new BusinessException("重复请求，系统已经阻止！");
                }
                else //code不一致，更新
                {
                    SetSession("sso_third_authcenter", code);
                }
            }

            var authCenterConfig = Configuration.GetSection("AuthCenter").Get<ThirdSSO>();

            using var tokenHttpClient = new HttpClient(new HttpClientBusinessHandler("获取认证中心Token失败"));

            var authority = authCenterConfig.authHost;
            tokenHttpClient.BaseAddress = new Uri(authority);

            if (string.IsNullOrEmpty(code))
            {
                var authorizeQueryParams = new Dictionary<string, string>
                {
                    { "grant_type", "authorization_code" },
                    { "client_id", authCenterConfig.clientID },
                    { "redirect_uri", authCenterConfig.callBackUrl },
                    { "state", Guid.NewGuid().ToString() },
                    {"response_type","code" }
                };

                var authorizeQueryString = string.Join("&", authorizeQueryParams.Select(
                    kvp => $"{Uri.EscapeDataString(kvp.Key)}={Uri.EscapeDataString(kvp.Value)}"
                ));

                var authorizeUrl = $"{authCenterConfig.authHost}/connect/authorize?{authorizeQueryString}";
                LogHelper.Info($"---->获取授权码请求地址信息为：{authorizeUrl}");
                return Redirect(authorizeUrl);
            }

            var parameters = new Dictionary<string, string>
            {
                { "grant_type", "authorization_code" },
                { "code", code },
                { "redirect_uri", authCenterConfig.callBackUrl },
                { "client_id", authCenterConfig.clientID }
            };

            var content = new FormUrlEncodedContent(parameters);

            var response = await tokenHttpClient.PostAsync("/connect/token", content);

            var tokenResponseTemplate = new
            {
                access_token = "",
                expires_in = 0,
                token_type = "",
                refresh_token = ""
            };

            var res = await response.Content.ReadAsStringAsync();

            var tokenResponse = JsonConvert.DeserializeAnonymousType(res, tokenResponseTemplate);

            using var userInfoClient = new HttpClient(new HttpClientBusinessHandler("获取认证中心用户信息失败"));
            userInfoClient.BaseAddress = new Uri(authCenterConfig.dataHost);

            var requestMessage = new HttpRequestMessage(HttpMethod.Get, "/api/authcenter/userinfo");
            requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", tokenResponse.access_token);

            var userInfoResponse = await userInfoClient.SendAsync(requestMessage);

            var userInfoJson = await userInfoResponse.Content.ReadAsStringAsync();
            var userInfo = JsonConvert.DeserializeObject<TData<SSOUserInfo>>(userInfoJson);
            if (userInfo != null && userInfo.Tag == 1)
            {
                UserThirdAuth tus = new UserThirdAuth();

                Util.ThirdOAuth.Self.User um = userInfo.Data.User;
                Unit un = userInfo.Data.Unit;

                tus.ThirdUnitId = un.UnitId;
                tus.ThirdUserId = um.UserId;
                tus.Mobile = um.Mobile;
                tus.UserName = um.AccountName;
                tus.RealName = um.RealName;
                tus.UnitType = (UnitTypeEnum)un.UnitType;
                if (un.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    tus.ParentUnitId = un.CountyId;
                }
                else if (un.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    tus.ParentUnitId = un.CityId;
                }
                else
                {
                    tus.ParentUnitId = "";
                }

                await userThirdAuthBLL.SaveUnit(un.UnitType, un.UnitName, un.UnitId, un.CountyName ?? "0", un.CountyId ?? "0", un.CityName ?? "0", un.CityId ?? "0");

                if (um.AccountType == 1)
                {
                    switch (un.UnitType)
                    {
                        case 1:
                            tus.RoleId = 10;
                            break;

                        case 2:
                            tus.RoleId = 20;
                            break;

                        case 3:
                            tus.RoleId = 30;
                            break;

                        case 4:
                            tus.RoleId = 40;
                            break;

                        case 9:
                            tus.IsSystemAdmin = true;
                            tus.RoleId = 90;
                            break;
                    }
                }
                else
                {
                    switch (un.UnitType)
                    {
                        case 1:
                            tus.RoleId = 11;
                            break;

                        case 2:
                            tus.RoleId = 21;
                            break;

                        case 3:
                            tus.RoleId = 31;
                            break;

                        case 4:
                            tus.RoleId = 41;
                            break;

                        case 9:
                            tus.RoleId = 90;
                            break;
                    }
                }

                LogHelper.Info(string.Format("SSO:保存信息:{0}", JsonConvert.SerializeObject(tus)));

                TData<UserEntity> userObj = await userThirdAuthBLL.SaveForm(tus);
                if (userObj.Tag == 1)
                {
                    var loginData = await LoginAuto(userObj.Data.UserName, userObj.Data.Password);
                    if (loginData.Tag == 1)
                    {
                        return Redirect("~/");
                    }
                    else
                    {
                        throw new BusinessException(loginData.Message);
                    }
                }
                else
                {
                    throw new BusinessException(userObj.Message);
                }
            }
            else
            {
                LogHelper.Info(string.Format("SSO:获取用户角色信息失败:{0}", JsonConvert.SerializeObject(userInfoJson)));
                throw new BusinessException("获取用户角色信息失败！请联系在线客服。");
            }
        }

        /// <summary>
        /// 对接公司内部统一身份认证
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> Auth_Self()
        {
            var ThirdSSO = Configuration.GetSection("sso").Get<ThirdSSO>();
            string code = Request.Query["code"];

            var sesssionCode = GetSession("sso_third_self");
            if (string.IsNullOrEmpty(sesssionCode))
            {
                if (!string.IsNullOrEmpty(code))
                    SetSession("sso_third_self", code);
            }
            else if (sesssionCode == code)
            {
                ViewBag.Msg = "重复请求，系统已经阻止！";
                LogHelper.Info("重复请求，系统已经阻止！");
                return View();
            }
            else //code不一致，更新
            {
                SetSession("sso_third_self", code);
            }

            string AppSecret = ThirdSSO.clientSecret;    //第三方系统对接秘钥
            string ClientBacuUrl = ThirdSSO.callBackUrl;//
            string authWebUrl = ThirdSSO.authHost;
            //string authDataUrl = ThirdSSO.dataHost;
            string dataHostUrl = ThirdSSO.dataHost;
            string clientID = ThirdSSO.clientID;
            string TimeSpan = Extention.GetTimeSpan().ToString();
            string EncryptString = SecurityHelper.MD5ToHex(clientID + TimeSpan + AppSecret);
            if (string.IsNullOrEmpty(code))
            {
                string url = string.Format("{0}/Auth?ClientId={1}&EncryptString={2}&TimeSpan={3}&ReturnUrl={4}",
                    authWebUrl, clientID, EncryptString, TimeSpan, ClientBacuUrl);
                return Redirect(url);
            }
            else
            {
                EncryptString = SecurityHelper.MD5ToHex(clientID + code + TimeSpan + AppSecret);
                var getTokenUrl = string.Format("{0}/ssoauth/AccessToken?ClientId={1}&EncryptString={2}&TimeSpan={3}&Code={4}", dataHostUrl, clientID, EncryptString, TimeSpan, code);
                LogHelper.Info($"getTokenUrl: {getTokenUrl}");
                string jsonToken = (await SendHelper.SendGetAsync(getTokenUrl)).Data;
                string tokenId;
                if (!string.IsNullOrEmpty(jsonToken))
                {
                    LogHelper.Info("jsonToken:" + jsonToken);
                    TData<TokenModel> tokenModel = JsonConvert.DeserializeObject<TData<TokenModel>>(jsonToken);
                    if (tokenModel != null && tokenModel.Tag == 1)
                    {
                        tokenId = tokenModel.Data.Token;
                        EncryptString = SecurityHelper.MD5ToHex(clientID + tokenId + TimeSpan + AppSecret);
                        string jsonUser = (await SendHelper.SendPostAsync(string.Format("{0}/ssoauth/GetUserInfoByTokenID?ClientId={1}&EncryptString={2}&TimeSpan={3}&Token={4}", dataHostUrl, clientID, EncryptString, TimeSpan, tokenId), "")).Data;
                        if (!string.IsNullOrEmpty(jsonUser))
                        {
                            LogHelper.Info("jsonUser:" + jsonUser);

                            TData<SSOUserInfo> userInfo = JsonConvert.DeserializeObject<TData<SSOUserInfo>>(jsonUser);
                            if (userInfo != null && userInfo.Tag == 1)
                            {
                                UserThirdAuth tus = new UserThirdAuth();

                                Util.ThirdOAuth.Self.User um = userInfo.Data.User;
                                Unit un = userInfo.Data.Unit;

                                tus.ThirdUnitId = un.UnitId;
                                tus.ThirdUserId = um.UserId;
                                tus.Mobile = um.Mobile;
                                tus.UserName = um.AccountName;
                                tus.RealName = um.RealName;
                                tus.UnitType = (UnitTypeEnum)un.UnitType;
                                if (un.UnitType == UnitTypeEnum.School.ParseToInt())
                                {
                                    tus.ParentUnitId = un.CountyId;
                                }
                                else if (un.UnitType == UnitTypeEnum.County.ParseToInt())
                                {
                                    tus.ParentUnitId = un.CityId;
                                }
                                else
                                {
                                    tus.ParentUnitId = "";
                                }

                                await userThirdAuthBLL.SaveUnit(un.UnitType, un.UnitName, un.UnitId, un.CountyName ?? "0", un.CountyId ?? "0", un.CityName ?? "0", un.CityId ?? "0");

                                if (um.AccountType == 1)
                                {
                                    switch (un.UnitType)
                                    {
                                        case 1:
                                            tus.RoleId = 10;
                                            break;

                                        case 2:
                                            tus.RoleId = 20;
                                            break;

                                        case 3:
                                            tus.RoleId = 30;
                                            break;

                                        case 4:
                                            tus.RoleId = 40;
                                            break;

                                        case 9:
                                            tus.IsSystemAdmin = true;
                                            tus.RoleId = 90;
                                            break;
                                    }
                                }
                                else
                                {
                                    switch (un.UnitType)
                                    {
                                        case 1:
                                            tus.RoleId = 11;
                                            break;

                                        case 2:
                                            tus.RoleId = 21;
                                            break;

                                        case 3:
                                            tus.RoleId = 31;
                                            break;

                                        case 4:
                                            tus.RoleId = 41;
                                            break;

                                        case 9:
                                            tus.RoleId = 90;
                                            break;
                                    }
                                }

                                LogHelper.Info(string.Format("SSO:保存信息:{0}", JsonConvert.SerializeObject(tus)));

                                TData<UserEntity> userObj = await userThirdAuthBLL.SaveForm(tus);
                                if (userObj.Tag == 1)
                                {
                                    var loginData = await LoginAuto(userObj.Data.UserName, userObj.Data.Password);
                                    if (loginData.Tag == 1)
                                    {
                                        return Redirect("~/");
                                    }
                                    else
                                    {
                                        ViewBag.Msg = loginData.Message;
                                        return View();
                                    }
                                }
                                else
                                {
                                    ViewBag.Msg = userObj.Message;
                                    return View();
                                }
                            }
                            else
                            {
                                LogHelper.Info(string.Format("SSO:获取用户角色信息失败:{0}", JsonConvert.SerializeObject(jsonUser)));
                                ViewBag.Msg = "获取用户角色信息失败！请联系在线客服。";
                                return View();
                            }
                        }
                        else
                        {
                            LogHelper.Info("调用GetUserInfoByTokenID失败");
                            ViewBag.Msg = "获取用户角色信息失败！请联系在线客服。";
                            return View();
                        }
                    }
                    else
                    {
                        LogHelper.Info("调用GetUserInfoByTokenID失败");
                        ViewBag.Msg = $"获取Token失败，日志信息：{tokenModel.Message} 请联系在线客服。";
                        return View();
                    }
                }
                else
                {
                    LogHelper.Info("ssoauth/AccessToken调用失败");
                    ViewBag.Msg = $"系统异常，请联系在线客服。";
                    return View();
                }
            }
        }

        #endregion 对接公司内部统一身份认证

        #region 泰兴统一身份认证处理程序

        /// <summary>
        /// 泰兴统一身份认证处理程序
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> Auth_Tx()
        {
            var ThirdSSO = Configuration.GetSection("sso").Get<ThirdSSO>();
            string code = Request.Query["iflyssost"];
            TztxSoftAcore.SetConfig(ThirdSSO);
            if (string.IsNullOrEmpty(code)) //如果有code，获取用户Id
            {
                string url = TztxSoftAcore.GetLoginUrl();
                return Redirect(url);
            }
            var sesssionCode = GetSession("sso_third_tx");
            if (string.IsNullOrEmpty(sesssionCode)) //首次
            {
                if (!string.IsNullOrEmpty(code))
                    SetSession("sso_third_tx", code);
            }
            else if (sesssionCode == code) //重复访问
            {
                ViewBag.Msg = "重复请求，系统已经阻止！";
                LogHelper.Info("重复请求，系统已经阻止！");
                return View();
            }
            else //code不一致，更新
            {
                SetSession("sso_third_tx", code);
            }

            try
            {
                string strUserId = "";
                LogHelper.Info(string.Format("SSO:iflyssost:{0}", code));

                TztxSoftAcore2.SetConfig(ThirdSSO);
                //if (!string.IsNullOrEmpty(code)) //如果有code，获取用户Id
                //{
                //获取用户Id
                var UserIdModel = TztxSoftAcore.GetUserIdModel(code);
                if (UserIdModel.code == "1")
                    strUserId = UserIdModel.data;
                LogHelper.Info(string.Format("SSO:UserIdModel:{0}", JsonConvert.SerializeObject(UserIdModel)));
                if (string.IsNullOrEmpty(strUserId))
                {
                    ViewBag.Msg = "获取用户ID失败！请联系认证中心。";
                    return View();
                }

                //获取用户角色
                Dictionary<string, string> parm = new Dictionary<string, string>();
                parm.Add("userId", strUserId);
                var roleResult = TztxSoftAcore2.GetDataByUserid(parm, "/listRoleByUserId");

                LogHelper.Info(string.Format("SSO:listRoleByUserId:{0}", JsonConvert.SerializeObject(roleResult)));

                if (roleResult != null && roleResult.data != null && roleResult.code == "1")
                {
                    var roleList = JsonConvert.DeserializeObject<List<TztxSoftAcore.Role>>(roleResult.data.ToString());
                    var role = roleList.Where(m => m.roleNature == "0" && (m.enName == "teacher" || m.enName == "edupersonnel" || m.enName == "instructor")).FirstOrDefault();
                    if (role == null)
                    {
                        LogHelper.Info(string.Format("SSO:获取用户角色信息失败:{0}", JsonConvert.SerializeObject(roleList)));

                        ViewBag.Msg = "未找到角色信息！您无权使用此功能，只有（教研员：instructor机构用户：edupersonnel  教师：teacher）才能使用本平台。";
                        return View();
                    }
                    var userResult = TztxSoftAcore2.GetDataByUserid(parm, "/getUserByUserId");
                    if (userResult == null || userResult.data == null || userResult.code != "1")
                    {
                        LogHelper.Info(string.Format("SSO:获取用户信息失败:{0}", JsonConvert.SerializeObject(userResult)));
                        ViewBag.Msg = "获取用户信息失败，请确认是否登录超时。";
                        return View();
                    }
                    else
                    {
                        LogHelper.Info(string.Format("SSO:getUserByUserId:{0}", JsonConvert.SerializeObject(userResult)));
                    }
                    var user = JsonConvert.DeserializeObject<TztxSoftAcore.User>(userResult.data.ToString());
                    if (user == null)
                    {
                        LogHelper.Info(string.Format("SSO:获取用户信息失败:{0}", JsonConvert.SerializeObject(userResult)));

                        ViewBag.Msg = "获取用户信息失败，请确认是否登录超时。";
                        return View();
                    }

                    UserThirdAuth tus = new UserThirdAuth();

                    tus.ThirdUserId = strUserId;
                    tus.Mobile = user.mobile;
                    tus.UserName = user.loginName;
                    tus.RealName = user.userName;
                    //获取手机号码：by wanglei 2022-10-12
                    try
                    {
                        Dictionary<string, string> parmUpe = new Dictionary<string, string>();
                        parmUpe.Add("appId", ThirdSSO.clientID);
                        parmUpe.Add("userIds", "[" + strUserId + "]");
                        parmUpe.Add("attributes", "[loginName,mobile]");
                        parmUpe.Add("isEncrypt", "false");
                        var UserPlainInfoEcs = TztxSoftAcore2.GetDataByUserid(parmUpe, "/batchListUserPlainInfoEcs");
                        LogHelper.Info(string.Format("SSO:获取手机账号:{0}", JsonConvert.SerializeObject(UserPlainInfoEcs)));

                        //                           {
                        //                               "code": "1",
                        //"message": "success",
                        //"data": {
                        //                                   "fbc284072a40da840d54b3c76597f44e116ce4d9115ba381883b2d5ec85632ed8647e547ab50fc510fb229011f9036a250d7c02afa734bd3": {
                        //                                       "loginName": "tch5983311",
                        //		"mobile": "18961035656"

                        //       }
                        //    }
                        //}
                        if (UserPlainInfoEcs.code == "1" && UserPlainInfoEcs.data.ToString().Length > 10)
                        {
                            //讯飞可能返回这个结果：{"code":"1","message":"success","data":{}}
                            string strUserInfos = UserPlainInfoEcs.data.ToString().Replace(strUserId, "userInfo");
                            var uInfo = JsonConvert.DeserializeObject<TztxSoftAcore2.UserBase>(strUserInfos);
                            if (uInfo != null)
                            {
                                tus.Mobile = uInfo.userInfo.mobile;
                                tus.UserName = uInfo.userInfo.loginName;
                            }
                            else //如果取不到数据，则表示学校未授权，禁止用户使用平台，否则会造成用户UserName不唯一，导致串号
                            {
                                LogHelper.Info("未获取到用户用户手机号码与登录账号，系统阻止登录");
                                ViewBag.Msg = "未获取到正确的手机号码与账号，暂时无法使用平台，请联系服务商解决。";
                                return View();
                            }
                        }
                        else //如果取不到数据，则表示学校未授权，禁止用户使用平台，否则会造成用户UserName不唯一，导致串号
                        {
                            LogHelper.Info("未获取到用户用户手机号码与登录账号，系统阻止登录");
                            ViewBag.Msg = "未获取到正确的手机号码与账号，暂时无法使用平台，请联系服务商解决。";
                            return View();
                        }
                    }
                    catch (Exception exp)
                    {
                        LogHelper.Info(string.Format("SSO:获取手机账号_error:{0}", exp.Message));
                        LogHelper.Info("未获取到用户用户手机号码与登录账号，系统阻止登录");
                        ViewBag.Msg = "未获取到正确的手机号码与账号，暂时无法使用平台，请联系服务商解决。";
                        //日志写异常
                        LogHelper.Error(exp);
                        return View();
                    }
                    new SessionHelper().WriteSession("auth_idtoken", strUserId);

                    TztxSoftAcore.School unit = new TztxSoftAcore.School();
                    TztxSoftAcore2.Result unitResult = new TztxSoftAcore2.Result();
                    if (role.enName == "teacher")  //学校
                    {
                        tus.UnitType = UnitTypeEnum.School;

                        unitResult = TztxSoftAcore2.GetDataByUserid(parm, "/listSchoolByUser");
                        if (unitResult == null || unitResult.data == null || unitResult.code != "1")
                        {
                            LogHelper.Info(string.Format("SSO:获取学校信息失败:{0}", JsonConvert.SerializeObject(unitResult)));
                            ViewBag.Msg = "获取学校信息失败，请确认是否登录超时。";
                            return View();
                        }
                        LogHelper.Info(string.Format("SSO:listSchoolByUser:{0}", JsonConvert.SerializeObject(unitResult)));

                        var unitList = JsonConvert.DeserializeObject<List<TztxSoftAcore.School>>(unitResult.data.ToString());

                        unit = unitList.FirstOrDefault();
                        if (unit == null)
                        {
                            LogHelper.Info(string.Format("SSO:获取学校信息失败:{0}", JsonConvert.SerializeObject(unitResult)));

                            ViewBag.Msg = "获取学校信息失败，请确认是否登录超时。";
                            return View();
                        }
                        string schoolProp = "";

                        if (unit.phaseCode.IndexOf(',') != -1)
                        {
                            Regex regNum = new Regex(@"\d+(\.\d)?", RegexOptions.None);

                            var tempData = regNum.Matches(unit.phaseCode);
                            foreach (var t in tempData)
                            {
                                schoolProp += getTztxSchoolSection(t.ToString());
                            }
                            schoolProp = getSchoolProp(schoolProp);
                        }
                        else
                        {
                            schoolProp = getTztxSchoolSection(unit.phaseCode);
                        }
                        tus.UnitCode = string.IsNullOrEmpty(unit.schoolCode) ? unit.id : unit.schoolCode;
                        tus.ParentUnitId = "Txjyj20220809";
                        tus.UnitName = unit.schoolName;
                        tus.Brief = unit.shortName;
                        tus.Address = unit.address;
                        tus.SchoolProp = schoolProp;
                        tus.ThirdUnitId = unit.id;
                        tus.RoleId = 31; //学科教师
                    }
                    else if (role.enName == "edupersonnel" || role.enName == "instructor")
                    {
                        tus.UnitType = UnitTypeEnum.County;
                        tus.ThirdUnitId = "Txjyj20220809";
                        tus.UnitCode = "Txjyj20220809";
                        tus.RoleId = 21; //区县教研员
                    }

                    LogHelper.Info(string.Format("SSO:保存信息:{0}", JsonConvert.SerializeObject(tus)));

                    TData<UserEntity> userObj = await userThirdAuthBLL.SaveForm(tus);
                    if (userObj.Tag == 1)
                    {
                        var loginData = await LoginAuto(userObj.Data.UserName, userObj.Data.Password);
                        if (loginData.Tag == 1)
                        {
                            return Redirect("~/");
                        }
                        else
                        {
                            ViewBag.Msg = loginData.Message;
                            return View();
                        }
                    }
                    else
                    {
                        ViewBag.Msg = userObj.Message;
                        return View();
                    }
                }
                else
                {
                    LogHelper.Info(string.Format("SSO:获取用户角色信息失败:{0}", JsonConvert.SerializeObject(roleResult)));
                    ViewBag.Msg = "获取用户角色信息失败！请联系认证中心。";
                    return View();
                }
            }
            catch (Exception exp)
            {
                LogHelper.Info(string.Format("SSO:login_error:{0} {1}", exp.Message, exp.StackTrace));
                ViewBag.Msg = "接口未获取到单位信息，认证失败，请重新认证！" + exp.Message;
                //日志写异常
                LogHelper.Error(exp);
            }

            return View();
        }

        /// <summary>
        /// 泰兴统一身份认证学段转码
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        private string getTztxSchoolSection(string code)
        {
            string schoolStage;
            switch (code)
            {
                //(01:幼儿园；02：学前班；03：小学；04：初中；05：高中；06；大学；07：演示；08：职教；09：特殊教育)
                case "01":
                    schoolStage = "幼儿园";
                    break;

                case "02":
                    schoolStage = "学前班";
                    break;

                case "03":
                    schoolStage = "小学";
                    break;

                case "04":
                    schoolStage = "初中";
                    break;

                case "05":
                    schoolStage = "高中";
                    break;

                case "06":
                    schoolStage = "大学";
                    break;

                case "07":
                    schoolStage = "演示";
                    break;

                case "08":
                    schoolStage = "中高职";
                    break;

                case "09":
                    schoolStage = "特殊教育";
                    break;

                default:
                    schoolStage = "";
                    break;
            }

            return schoolStage;
        }

        /// <summary>
        /// 将学段转换成平台的单位属性
        /// </summary>
        /// <param name="schoolSection"></param>
        /// <returns></returns>
        private string getSchoolProp(string schoolSection)
        {
            if (schoolSection.Contains("小学") && schoolSection.Contains("初中") && schoolSection.Contains("高中"))
            {
                return "十二年制";
            }
            else if (schoolSection.Contains("初中") && schoolSection.Contains("高中"))
            {
                return "完中";
            }
            else if (schoolSection.Contains("小学") && schoolSection.Contains("初中"))
            {
                return "九年制";
            }
            else
            {
                return schoolSection;
            }
        }

        private async Task<TData> LoginAuto(string userName, string password, int validtype = 3)
        {
            TData obj = new TData();
            if (string.IsNullOrEmpty(userName))
            {
                obj.Tag = 0;
                obj.Message = "未能成功创建账号，请联系系统运维人员。";
                return obj;
            }
            //NetHelper.SetConfigHttpContext(HttpContext);
            string ip = _netHelper.GetIp();

            TData<UserEntity> userObj = await userBLL.CheckLogin(userName, password, (int)PlatformEnum.ThirdSSO, validtype, ip: ip);
            LogHelper.Info($"LoginAuto userObj:{JsonConvert.SerializeObject(userObj)}", null);
            if (userObj.Tag == 1)
            {
                await new UserBLL().UpdateUser(userObj.Data);
                await Code.Operator.Instance.AddCurrent(userObj.Data.WebToken);
            }

            string userAgent = _netHelper.GetUserAgent();
            string browser = _netHelper.GetBrowser(userAgent);
            string os = _netHelper.GetOSVersion(userAgent);

            Action taskAction = async () =>
            {
                LogLoginEntity logLoginEntity = new LogLoginEntity
                {
                    LogStatus = userObj.Tag == 1 ? OperateStatusEnum.Success.ParseToInt() : OperateStatusEnum.Fail.ParseToInt(),
                    Remark = userObj.Message,
                    IpAddress = ip,
                    IpLocation = IpLocationHelper.GetIpLocation(ip),
                    Browser = browser,
                    OS = os,
                    ExtraRemark = userAgent,
                    BaseCreatorId = userObj.Data?.Id
                };

                // 让底层不用获取HttpContext
                logLoginEntity.BaseCreatorId = logLoginEntity.BaseCreatorId ?? 0;

                await logLoginBLL.SaveForm(logLoginEntity);
            };
            AsyncTaskHelper.StartTask(taskAction);

            obj.Tag = userObj.Tag;
            obj.Message = userObj.Message;
            obj.ExtendData = userObj.Data;
            return obj;
        }

        #endregion 泰兴统一身份认证处理程序

        #region 成华区统一身份认证处理程序

        /// <summary>
        /// 成华区统一身份认证处理程序
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> Auth_chq()
        {
            var ThirdSSO = Configuration.GetSection("sso").Get<ThirdSSO>();
            string code = Request.Query["ticket"];

            var sesssionCode = GetSession("sso_third_chq");
            if (string.IsNullOrEmpty(sesssionCode))
            {
                if (!string.IsNullOrEmpty(code))
                    SetSession("sso_third_chq", code);
            }
            else if (sesssionCode == code)
            {
                ViewBag.Msg = "重复请求，系统已经阻止！";
                LogHelper.Info("重复请求，系统已经阻止！");
                return View();
            }
            else //code不一致，更新
            {
                SetSession("sso_third_chq", code);
            }

            try
            {
                string strUserId = "";
                LogHelper.Info(string.Format("SSO:ticket:{0}", code));
                TzSoftAcore.SetConfig(ThirdSSO);
                if (!string.IsNullOrEmpty(code)) //如果有code，获取用户Id
                {
                    var accesstokenModel = TzSoftAcore.GetAccesstoken();
                    LogHelper.Info(string.Format("SSO:accesstokenModel:{0}", JsonConvert.SerializeObject(accesstokenModel)));

                    if (accesstokenModel.retCode != "000000")
                    {
                        ViewBag.Msg = accesstokenModel.retDesc;
                        return View();
                    }
                    var ticketModel = TzSoftAcore.ValidaTicket(accesstokenModel.tokenInfo.accessToken, code);
                    LogHelper.Info(string.Format("SSO:ticketModel:{0}", JsonConvert.SerializeObject(ticketModel)));

                    if (ticketModel.code == "100001")
                    {
                        string url = TzSoftAcore.GetLoginUrl2();
                        return Redirect(url);
                    }
                    else if (ticketModel.code != "000000")
                    {
                        ViewBag.Msg = ticketModel.message;
                        return View();
                    }

                    //用户类型0：学生1:老师2:家长3:机构4:学校5:学校工作人员6:机构工作人员
                    string userType = ticketModel.result.last_user_type;
                    if (!userType.Contains("1") && !userType.Contains("3") && !userType.Contains("4")
                        && !userType.Contains("5") && !userType.Contains("6"))
                    {
                        ViewBag.Msg = string.Format("您没有权限使用平台，用户类型为【{0}】;用户类型0：学生1:老师2:家长3:机构4:学校5:学校工作人员6:机构工作人员", userType);
                        return View();
                    }

                    //var userInfoModel = TzSoftAcore.GetUserInfo(accesstokenModel.tokenInfo.accessToken, ticketModel.result.user_id);
                    //LogHelper.Info(string.Format("SSO:userInfoModel:{0}", JsonConvert.SerializeObject(userInfoModel)));

                    //if (userInfoModel.retCode != "000000")
                    //{
                    //    ViewBag.Msg = userInfoModel.retDesc;
                    //    return View();
                    //}

                    var user = ticketModel.result;
                    //if (user == null)
                    //{
                    //    LogHelper.Info(string.Format("SSO:获取用户信息失败:{0}", JsonConvert.SerializeObject(userInfoModel)));
                    //    ViewBag.Msg = "获取用户信息失败，请确认是否登录超时。";
                    //    return View();
                    //}

                    var schoolInfoModel = TzSoftAcore.GetSchoolInfo(accesstokenModel.tokenInfo.accessToken, user.last_top_org_id);
                    LogHelper.Info(string.Format("SSO:SchoolInfoModel:{0}", JsonConvert.SerializeObject(schoolInfoModel)));

                    if (schoolInfoModel.code != "000000")
                    {
                        ViewBag.Msg = schoolInfoModel.message;
                        return View();
                    }
                    var unit = schoolInfoModel.result;
                    if (unit == null)
                    {
                        LogHelper.Info(string.Format("SSO:获取单位信息失败:{0}", JsonConvert.SerializeObject(schoolInfoModel)));
                        ViewBag.Msg = "获取单位信息失败，请确认是否登录超时。";
                        return View();
                    }
                    ////获取手机号码
                    //try
                    //{
                    //    string strMobileInfo = TzSoftAcore.GetMobileInfo(accesstokenModel.tokenInfo.accessToken, user.personId);
                    //    if (!string.IsNullOrEmpty(strMobileInfo))
                    //    {
                    //        LogHelper.Info(string.Format("SSO:获取手机号码:{0}", strMobileInfo));

                    //        var objMobile = JsonConvert.DeserializeObject<TzSoftAcore.MobileInfo>(strMobileInfo);
                    //        if (objMobile != null && objMobile.code == "000000")
                    //        {
                    //            //网关提供固定解密秘钥，需要按照下文算法生成动态秘钥解密数据。
                    //            //动态秘钥是 MD5(appId+secret + yyyyMMdd)其中 MD5加密方式是16位小写，secret为固定秘钥，yyyyMMdd为当天时间：如20190808
                    //            string dyKey = Extention.ToMD5String16(ThirdSSO.clientID + ThirdSSO.key + DateTime.Now.ToString("yyyyMMdd")).ToLower();

                    //            LogHelper.Info(string.Format("SSO:动态秘钥:{0}", dyKey));

                    //            user.phoneNumbers = SecurityHelper.AESDecodeHex(objMobile.result, dyKey);

                    //        }
                    //    }
                    //}
                    //catch (Exception expM)
                    //{
                    //    LogHelper.Info(string.Format("SSO:获取手机号码失败:{0}", expM.Message));
                    //}

                    strUserId = user.user_id;
                    UserThirdAuth tus = new UserThirdAuth();

                    tus.ThirdUserId = user.user_id;
                    tus.Mobile = user.phone;
                    tus.UserName = user.user_id;  //user.account;
                    tus.RealName = user.name;

                    new SessionHelper().WriteSession("auth_idtoken", strUserId);
                    //成都市成华区教育局(413e9d8a823744b7814b1bf273a84aee)下的所有教育局人员的last_user_type为
                    if (unit.org_id == "413e9d8a823744b7814b1bf273a84aee")
                    {
                        tus.UnitType = UnitTypeEnum.County;
                        tus.ThirdUnitId = "Chqjyj20220809";
                        tus.UnitCode = "Chqjyj20220809";
                    }//last_user_type 用户类型0：学生1:老师2:家长3:机构4:学校5:学校工作人员6:机构工作人员
                    //经发现，可能存在单位为教育局，但是用户类型为学校教师的情况，所以加else。by wanglei 2023-08-21
                    else if (user.last_user_type.Contains("1") || user.last_user_type.Contains("3"))
                    {
                        tus.UnitType = UnitTypeEnum.School;

                        string schoolProp = "";

                        int org_category = 0;
                        int.TryParse(unit.org_category, out org_category);
                        switch (org_category)
                        {
                            //0:小学1:初中2:高中3:完中4:机构5:教学点6:九年一贯制7: 十二年一贯制9： 其它教育类学校10: 中职11: 高职12：幼儿园
                            case 12:
                                schoolProp = "幼儿园";
                                break;

                            case 0:
                                schoolProp = "小学";
                                break;

                            case 1:
                                schoolProp = "初中";
                                break;

                            case 2:
                                schoolProp = "高中";
                                break;

                            case 3:
                                schoolProp = "完中";
                                break;

                            case 6:
                                schoolProp = "九年制";
                                break;

                            case 7:
                                schoolProp = "十二年制";
                                break;

                            case 10:
                            case 11:
                                schoolProp = "中高职";
                                break;

                            default:
                                schoolProp = "";
                                break;
                        }
                        tus.UnitCode = string.IsNullOrEmpty(unit.org_coding) ? unit.org_id : unit.org_coding;
                        //tus.ParentUnitId = unit.org_parent_id; //对方org_parent_id和我们的pid含义不一致，非学校与区县的对应关系
                        tus.UnitName = user.last_top_org_name;
                        tus.Brief = unit.org_short_name;
                        tus.Address = unit.org_address;
                        tus.SchoolProp = schoolProp;
                        tus.ThirdUnitId = unit.org_id;
                        tus.ParentUnitId = "Chqjyj20220809";
                        tus.RoleId = 31; // 应天俞（谢红平 ）先给所有用户授权一个学科教师的授权，让他能进去，其他权限还是单独授权，最好引导学校管理员去授权
                    }
                    else if (user.last_user_type.Contains("4"))
                    {
                        tus.UnitType = UnitTypeEnum.County;
                        tus.ThirdUnitId = "Chqjyj20220809";
                        tus.UnitCode = "Chqjyj20220809";
                    }
                    else
                    {
                        ViewBag.Msg = "您的账号类型不允许使用平台";
                        return View();
                    }
                    ////增加1分钟内禁止重复提交限制
                    //SessionHelper sessinHelp = new SessionHelper();
                    //var sessionTime = sessinHelp.GetSession(tus.ThirdUserid);
                    //if (!string.IsNullOrEmpty(sessionTime))
                    //{
                    //    sessinHelp.WriteSession(tus.ThirdUserid, DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                    //}
                    //else
                    //{
                    //    DateTime dt;
                    //    DateTime.TryParse(sessionTime, out dt);

                    //    if ((DateTime.Now - dt).Seconds < 60)
                    //    {
                    //        ViewBag.Msg = "您操作太频繁，请关闭当前页面，稍后再试！";
                    //        return View();
                    //    }
                    //}

                    LogHelper.Info(JsonConvert.SerializeObject(tus));

                    if (tus.SchoolProp == "幼儿园" || tus.SchoolProp == "")
                    {
                        ViewBag.Msg = "您的账号类型不允许使用平台";
                        return View();
                    }

                    TData<UserEntity> userObj = await userThirdAuthBLL.SaveForm(tus);
                    if (userObj.Tag == 1)
                    {
                        var loginData = await LoginAuto(userObj.Data.UserName, userObj.Data.Password);
                        if (loginData.Tag == 1)
                        {
                            return Redirect("~/");
                        }
                        else
                        {
                            ViewBag.Msg = loginData.Message;
                            return View();
                        }
                    }
                    else
                    {
                        ViewBag.Msg = userObj.Message;
                        return View();
                    }
                }

                else//ticket不存在
                {
                    string url = TzSoftAcore.GetLoginUrl2();
                    return Redirect(url);
                    //ViewBag.Msg = "ticket不存在，非法的访问，系统已经阻止！";
                }
            }
            catch (Exception exp)
            {
                LogHelper.Info(string.Format("SSO:login_error:{0} {1}", exp.Message, exp.StackTrace));
                ViewBag.Msg = "接口未获取到单位信息，认证失败，请重新认证！" + exp.Message;
                //日志写异常
                LogHelper.Error(exp);
            }

            return View();
        }

        #endregion 成华区统一身份认证处理程序

        #region 泰州市统一身份认证处理程序

        /// <summary>
        /// 泰州市统一身份认证处理程序
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> Auth_tzs()
        {
            var ThirdSSO = Configuration.GetSection("sso").Get<ThirdSSO>();
            TzSoftAcore.SetConfig(ThirdSSO);

            string code = Request.Query["ticket"];
            if (string.IsNullOrEmpty(code))
            {
                string url = TzSoftAcore.GetLoginUrl();
                return Redirect(url);
                //ViewBag.Msg = "ticket不存在，非法的访问，系统已经阻止！";
            }
            var sesssionCode = GetSession("sso_third_tzs");
            if (string.IsNullOrEmpty(sesssionCode))
            {
                if (!string.IsNullOrEmpty(code))
                    SetSession("sso_third_tzs", code);
            }
            else if (sesssionCode == code)
            {
                ViewBag.Msg = "重复请求，系统已经阻止！";
                LogHelper.Info("重复请求，系统已经阻止！");
                return View();
            }
            else //code不一致，更新
            {
                SetSession("sso_third_tzs", code);
            }

            try
            {
                string strUserId = "";
                LogHelper.Info(string.Format("SSO:ticket:{0}", code));

                var accesstokenModel = TzSoftAcore.GetAccesstoken();
                LogHelper.Info(string.Format("SSO:accesstokenModel:{0}", JsonConvert.SerializeObject(accesstokenModel)));

                if (accesstokenModel.retCode != "000000")
                {
                    ViewBag.Msg = accesstokenModel.retDesc;
                    return View();
                }
                var ticketModel = TzSoftAcore.ValidaTicket(accesstokenModel.tokenInfo.accessToken, code);
                LogHelper.Info(string.Format("SSO:ticketModel:{0}", JsonConvert.SerializeObject(ticketModel)));

                if (ticketModel.code == "100001")
                {
                    string url = TzSoftAcore.GetLoginUrl();
                    return Redirect(url);
                }
                else if (ticketModel.code != "000000")
                {
                    ViewBag.Msg = ticketModel.message;
                    return View();
                }

                //用户类型0：学生1:老师2:家长3:机构4:学校5:学校工作人员6:机构工作人员
                string userType = ticketModel.result.last_user_type;
                if (!userType.Contains("1") && !userType.Contains("3") && !userType.Contains("4")
                    && !userType.Contains("5") && !userType.Contains("6"))
                {
                    ViewBag.Msg = string.Format("您没有权限使用平台，用户类型为【{0}】;用户类型0：学生1:老师2:家长3:机构4:学校5:学校工作人员6:机构工作人员", userType);
                    return View();
                }

                //var userInfoModel = TzSoftAcore.GetUserInfo(accesstokenModel.tokenInfo.accessToken, ticketModel.result.user_id);
                //LogHelper.Info(string.Format("SSO:userInfoModel:{0}", JsonConvert.SerializeObject(userInfoModel)));

                //if (userInfoModel.retCode != "000000")
                //{
                //    ViewBag.Msg = userInfoModel.retDesc;
                //    return View();
                //}

                var user = ticketModel.result;
                //if (user == null)
                //{
                //    LogHelper.Info(string.Format("SSO:获取用户信息失败:{0}", JsonConvert.SerializeObject(userInfoModel)));
                //    ViewBag.Msg = "获取用户信息失败，请确认是否登录超时。";
                //    return View();
                //}

                var schoolInfoModel = TzSoftAcore.GetSchoolInfo(accesstokenModel.tokenInfo.accessToken, user.last_top_org_id);
                LogHelper.Info(string.Format("SSO:SchoolInfoModel:{0}", JsonConvert.SerializeObject(schoolInfoModel)));

                if (schoolInfoModel.code != "000000")
                {
                    ViewBag.Msg = schoolInfoModel.message;
                    return View();
                }
                var unit = schoolInfoModel.result;
                if (unit == null)
                {
                    LogHelper.Info(string.Format("SSO:获取单位信息失败:{0}", JsonConvert.SerializeObject(schoolInfoModel)));
                    ViewBag.Msg = "获取单位信息失败，请确认是否登录超时。";
                    return View();
                }
                ////获取手机号码
                //try
                //{
                //    string strMobileInfo = TzSoftAcore.GetMobileInfo(accesstokenModel.tokenInfo.accessToken, user.personId);
                //    if (!string.IsNullOrEmpty(strMobileInfo))
                //    {
                //        LogHelper.Info(string.Format("SSO:获取手机号码:{0}", strMobileInfo));

                //        var objMobile = JsonConvert.DeserializeObject<TzSoftAcore.MobileInfo>(strMobileInfo);
                //        if (objMobile != null && objMobile.code == "000000")
                //        {
                //            //网关提供固定解密秘钥，需要按照下文算法生成动态秘钥解密数据。
                //            //动态秘钥是 MD5(appId+secret + yyyyMMdd)其中 MD5加密方式是16位小写，secret为固定秘钥，yyyyMMdd为当天时间：如20190808
                //            string dyKey = Extention.ToMD5String16(ThirdSSO.clientID + ThirdSSO.key + DateTime.Now.ToString("yyyyMMdd")).ToLower();

                //            LogHelper.Info(string.Format("SSO:动态秘钥:{0}", dyKey));

                //            user.phoneNumbers = SecurityHelper.AESDecodeHex(objMobile.result, dyKey);

                //        }
                //    }
                //}
                //catch (Exception expM)
                //{
                //    LogHelper.Info(string.Format("SSO:获取手机号码失败:{0}", expM.Message));
                //}

                strUserId = user.user_id;
                UserThirdAuth tus = new UserThirdAuth();

                tus.ThirdUserId = user.user_id;
                tus.Mobile = user.phone;
                tus.UserName = "";  //user.account;
                tus.RealName = user.name;

                //var test = TzSoftAcore.GetAdminInfo(accesstokenModel.tokenInfo.accessToken, "实验教学");
                //LogHelper.Info(string.Format("SSO:GetAdminInfo:{0}", test));

                new SessionHelper().WriteSession("auth_idtoken", strUserId);
                //天俞角色类型，2022-11-11
                //0学生
                //1教师
                //3学校工作人员
                //4机构行政人员
                bool IsMultUnit = false; //支持一个账号跨区市两个单位
                if (user.last_user_type.Contains("1") || user.last_user_type.Contains("3"))  //学校
                {
                    tus.UnitType = UnitTypeEnum.School;

                    string schoolProp = "";
                    int org_category = 0;
                    int.TryParse(unit.org_category, out org_category);
                    switch (org_category)
                    {
                        //0:小学1:初中2:高中3:完中4:机构5:教学点6:九年一贯制7: 十二年一贯制9： 其它教育类学校10: 中职11: 高职12：幼儿园
                        case 12:
                            schoolProp = "幼儿园";
                            break;

                        case 0:
                            schoolProp = "小学";
                            break;

                        case 1:
                            schoolProp = "初中";
                            break;

                        case 2:
                            schoolProp = "高中";
                            break;

                        case 3:
                            schoolProp = "完中";
                            break;

                        case 6:
                            schoolProp = "九年制";
                            break;

                        case 7:
                            schoolProp = "十二年制";
                            break;

                        case 10:
                        case 11:
                            schoolProp = "中高职";
                            break;

                        default:
                            schoolProp = "";
                            break;
                    }
                    tus.UnitCode = string.IsNullOrEmpty(unit.org_coding) ? unit.org_id : unit.org_coding;
                    //tus.ParentUnitId = unit.org_parent_id; //对方org_parent_id和我们的pid含义不一致，非学校与区县的对应关系
                    tus.UnitName = user.last_top_org_name;
                    tus.Brief = unit.org_short_name;
                    tus.Address = unit.org_address;
                    tus.SchoolProp = schoolProp;
                    if (unit.org_parent_id == "64760173378a4f759b594392f1ce773b" || unit.org_parent_id == "5054bb9315b548fa81ae4438bb1f22a1")
                        tus.ParentUnitId = "Tzsjyj20220809";
                    else
                    {
                        tus.ParentUnitId = unit.org_parent_id;
                    }
                    tus.ThirdUnitId = unit.org_id;
                }
                else if (user.last_user_type.Contains("4", StringComparison.Ordinal))
                {
                    tus.UnitType = UnitTypeEnum.County;
                    if (unit.org_id == "5054bb9315b548fa81ae4438bb1f22a1")
                    {
                        tus.ThirdUnitId = "Tzsjyj20220809";
                        tus.UnitCode = "Tzsjyj20220809";
                        tus.IsMultUnit = true;
                        IsMultUnit = true;
                    }
                    else
                    {
                        tus.UnitName = user.last_top_org_name;
                        tus.Brief = unit.org_short_name;
                        tus.Address = unit.org_address;
                        tus.ThirdUnitId = unit.org_id;
                        tus.UnitCode = string.IsNullOrEmpty(unit.org_coding) ? unit.org_id : unit.org_coding;
                    }
                    tus.ParentUnitId = "TzCity20240410";
                    try
                    {
                        var adminInfo = TzSoftAcore.GetAdminInfo(accesstokenModel.tokenInfo.accessToken, "实验教学");

                        //如果当前登录用户id在装备管理中，则为顶级管理员——超管，否则不给角色
                        if (!string.IsNullOrEmpty(adminInfo) && adminInfo.Contains(user.user_id))
                        {
                            tus.RoleId = 20;
                        }
                    }
                    catch (Exception exp)
                    {
                        LogHelper.Info(string.Format("SSO:检测系统超管信息失败:{0} {1}", exp.Message, exp.StackTrace));
                        ViewBag.Msg = "检测系统超管信息失败，请重新认证！原因：" + exp.Message;
                        //日志写异常
                        LogHelper.Error(exp);
                    }
                }
                else
                {
                    ViewBag.Msg = "您的账号类型不允许使用平台";
                    return View();
                }

                LogHelper.Info(string.Format("SSO:savetodb:{0}", JsonConvert.SerializeObject(tus)));

                TData<UserEntity> userObj = await userThirdAuthBLL.SaveForm(tus); //因为市级单位已经初始完成，所以此处先插入区县不影响

                if (IsMultUnit) //插入市级账号，泰州市区两级单位在数据库中先手动添加，市TzCity20240410
                {
                    tus.UnitType = UnitTypeEnum.City;
                    tus.ThirdUnitId = "TzCity20240410";
                    tus.UnitCode = "TzCity20240410";
                    tus.ParentUnitId = "";
                    if (tus.RoleId == 20)
                    {
                        tus.RoleId = 10;
                    }
                    userObj = await userThirdAuthBLL.SaveForm(tus);
                }

                if (userObj.Tag == 1)
                {
                    var loginData = await LoginAuto(userObj.Data.UserName, userObj.Data.Password);
                    if (loginData.Tag == 1)
                    {
                        return Redirect("~/");
                    }
                    else
                    {
                        ViewBag.Msg = loginData.Message;
                        return View();
                    }
                }
                else
                {
                    ViewBag.Msg = userObj.Message;
                    return View();
                }
            }
            catch (Exception exp)
            {
                LogHelper.Info(string.Format("SSO:login_error:{0} {1}", exp.Message, exp.StackTrace));
                ViewBag.Msg = "接口未获取到单位信息，认证失败，请重新认证！" + exp.Message;
                //日志写异常
                LogHelper.Error(exp);
            }

            return View();
        }

        #endregion 泰州市统一身份认证处理程序

        #region 连云港海州区OA对接

        /// <summary>
        /// 连云港海州区登录页面视图
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult HzLogin()
        {
            ViewBag.Title = "海州区实验教学";
            ViewBag.IsThirdLogin = 1; //是否调用第三方登录接口
            return View();
        }

        /// <summary>
        /// 海州区第三方统一登录接口进行登录
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> LoginByThirdApi(string userName, string password)
        {
            TData obj = new TData();
            var ThirdSSO = Configuration.GetSection("sso").Get<ThirdSSO>();
            string webserviceUrl = ThirdSSO.dataHost; //webservice总接口
            //NetHelper.SetConfigHttpContext(HttpContext);
            string ip = _netHelper.GetIp();
            try
            {
                //调用login登录接口
                string sbData = "";
                sbData += "<soapenv:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:urn=\"urn:API\">";
                sbData += "<soapenv:Header/>";
                sbData += "<soapenv:Body>";
                sbData += "<urn:login soapenv:encodingStyle=\"http://schemas.xmlsoap.org/soap/encoding/\">";
                sbData += "<json xsi:type=\"xsd:string\">{\"code\":\"586A6560\",\"username\":\"" + userName + "\",\"userpass\":\"" + password + "\"}</json>";
                sbData += "</urn:login>";
                sbData += "</soapenv:Body>";
                sbData += "</soapenv:Envelope>";
                HttpContent httpContent = new StringContent(sbData, Encoding.UTF8, "text/xml");
                string loginResult = await this.PostHelper($"{webserviceUrl}/login", httpContent);
                LogHelper.Info($"login接口获取到的数据：{loginResult}", null);
                if (!string.IsNullOrEmpty(loginResult))
                {
                    LyqUserLoginModel loginModel = Util.Tools.JsonExtention.ToObject<LyqUserLoginModel>(loginResult);
                    if (loginModel.flag == "true") //登录成功
                    {
                        string org_id = ""; //机构id
                        long unitid = 0; //单位Id
                        UnitEntity unit = new UnitEntity(); //单位

                        if (string.IsNullOrEmpty(loginModel.value.org_id) || loginModel.value.org_id == null)
                        {
                            obj.Tag = 0;
                            obj.Message = $"{userName} 登录失败,未能从OA中获取到您的组织机构，请联系OA管理员！";
                            LogHelper.Info($"{userName} 登录失败,未能从OA中获取到用户的组织机构org_id：{loginModel.value.org_id}");
                        }
                        else
                        {
                            org_id = loginModel.value.org_id; //机构id
                            var unitlist = await unitBLL.GetPageList(new UnitListParam() { ThirdUnitId = org_id }, new Pagination() { PageIndex = 1, PageSize = int.MaxValue }); //根据机构id（第三方单位id）查询单位信息
                            if (unitlist.Data.Count > 0)
                            {
                                unitid = unitlist.Data.LastOrDefault().Id.Value;
                                unit = unitlist.Data.LastOrDefault();
                                LogHelper.Info($"获取到的单位信息：{unit.Id},{unit.Name},{unit.Code}", null);
                            }
                            else
                            {
                                LogHelper.Info($"未获取到单位信息", null);
                            }

                            #region 保存用户信息（使用平台统一方法）
                            UserThirdAuth tus = new UserThirdAuth();
                            tus.ThirdUserId = loginModel.value.id;
                            tus.UserName = loginModel.value.username;
                            tus.RealName = loginModel.value.truename;
                            if (unit != null)
                            {
                                tus.UnitType = (UnitTypeEnum)unit.UnitType;
                                tus.UnitName = unit.Name;
                                tus.ThirdUnitId = org_id;
                                tus.UnitCode = unit.Code;

                                if (unit.UnitType == (int)UnitTypeEnum.School) //学校账号默认学科教师的角色
                                {
                                    tus.RoleId = 31; //学科教师
                                }
                                else if (unit.UnitType == (int)UnitTypeEnum.County) //区县账号默认区县教研员
                                {
                                    tus.RoleId = 21; //区县教研员
                                }
                            }

                            LogHelper.Info(string.Format("SSO:保存信息:{0}", JsonConvert.SerializeObject(tus)));
                            TData<UserEntity> userObj = await userThirdAuthBLL.SaveForm(tus);
                            obj.Tag = userObj.Tag;
                            obj.Message = userObj.Message;
                            LogHelper.Info($"{userName} 登录结果：{userObj.Tag},{userObj.Message}", null);

                            if (userObj.Tag == 1)
                            {
                                var loginData = await LoginAuto(userObj.Data.UserName, userObj.Data.Password);
                                LogHelper.Info($"loginData：{loginData.Tag},{loginData.Message}", null);
                                if (loginData.Tag == 1)
                                {
                                    obj.Tag = 1;
                                    obj.Message = loginData.Message;
                                }
                                else
                                {
                                    obj.Tag = loginData.Tag;
                                    obj.Message = loginData.Message;
                                }
                            }
                            else
                            {
                                obj.Tag = 0;
                                obj.Message = userObj.Message;
                            }
                            #endregion 保存用户信息（使用平台统一方法）
                        }
                    }
                    else
                    {
                        obj.Tag = 0;
                        obj.Message = $"{userName} 登录失败,{loginModel.flag},{loginModel.tips}";
                        LogHelper.Info($"{userName} 登录失败,{loginModel.flag},{loginModel.tips}");
                    }
                }
                else
                {
                    obj.Tag = 0;
                    obj.Message = "登录接口异常，未能获取到数据";
                    LogHelper.Info($"登录接口{webserviceUrl}/login 未能获取到{userName}数据");
                }
            }
            catch (Exception ex)
            {
                obj.Tag = 0;
                obj.Message = "登录异常";
                LogHelper.Info($"登录异常：{ex.Message},{ex.StackTrace}");
                throw new Exception("程序出现异常，异常信息为：" + ex.Message);
            }

            return Json(obj);
        }

        /// <summary>
        /// 连云港海州区统一身份认证回调页面
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> Auth_hzq()
        {
            var ThirdSSO = Configuration.GetSection("sso").Get<ThirdSSO>();
            ViewBag.Msg = "连云港海州区统一身份认证";
            string code = Request.Query["code"];
            string session = Request.Query["session"];
            LogHelper.Info($"Auth_hzq: code {code}，session {session}", null);

            var sesssionCode = GetSession("sso_third_hzq");
            if (string.IsNullOrEmpty(sesssionCode))
            {
                if (!string.IsNullOrEmpty(code))
                    SetSession("sso_third_hzq", code);
            }
            else if (sesssionCode == code)
            {
                ViewBag.Msg = "重复请求，系统已经阻止！";
                LogHelper.Info($"重复请求，系统已经阻止！", null);
                return View();
            }
            else //code不一致，更新
            {
                SetSession("sso_third_hzq", code);
            }

            if (string.IsNullOrEmpty(code))
            {
                ViewBag.Msg = "未能取到code，系统已经阻止请求！";
                LogHelper.Info($"未能取到code，系统已经阻止请求！", null);
                return View();
            }
            else if (string.IsNullOrEmpty(session))
            {
                ViewBag.Msg = "未能取到session，系统已经阻止请求！";
                LogHelper.Info($"未能取到session，系统已经阻止请求！", null);
                return View();
            }

            try
            {
                string webserviceUrl = ThirdSSO.dataHost; //webservice总接口
                //string loginUrl = ThirdSSO.dataHost2; //登录接口

                // //调用isLogin判断是否可直接登录第三方平台
                StringBuilder sbData = new StringBuilder();
                sbData.Append("<soapenv:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:urn=\"urn:API\">");
                sbData.Append("<soapenv:Header/>");
                sbData.Append("<soapenv:Body>");
                sbData.Append("<urn:isLogin soapenv:encodingStyle=\"http://schemas.xmlsoap.org/soap/encoding/\">");
                sbData.AppendFormat("<json xsi:type=\"xsd:string\">{0}</json>", "{\"code\":\"111\",\"sesskey\":\"222\"}");
                sbData.Append("</urn:isLogin>");
                sbData.Append("</soapenv:Body>");
                sbData.Append("</soapenv:Envelope>");
                //bool isError = false;
                HttpContent httpContent = new StringContent(sbData.ToString(), Encoding.UTF8, "text/xml");
                string isLoginResult = await this.PostHelper($"{webserviceUrl}/isLogin", httpContent);
                LogHelper.Info($"isLogin接口返回结果： {isLoginResult}", null);
                if (!string.IsNullOrEmpty(isLoginResult) && isLoginResult.Length > 0)
                {
                    LyqUserModel islogin = Util.Tools.JsonExtention.ToObject<LyqUserModel>(isLoginResult);

                    if (islogin.flag == "true") //登录成功
                    {
                        string strUserId = "";
                        strUserId = islogin.value.id;
                        //判断用户是否存在
                        var userlist = userBLL.GetList(new Model.Param.OrganizationManage.UserListParam { ThirdUserId = strUserId });
                        if (userlist.Result.Data.Count > 0)
                        {
                            new SessionHelper().WriteSession("auth_idtoken", strUserId);
                            return Redirect("~/"); //存在-直接跳转
                        }
                        else
                        {
                            UserThirdAuth tus = new UserThirdAuth();
                            tus.ThirdUserId = islogin.value.id;
                            tus.Mobile = "";
                            tus.UserName = islogin.value.username;
                            tus.RealName = islogin.value.truename;

                            //不存在-创建用户
                            LogHelper.Info($"Auth_hzq:保存用户信息:{JsonConvert.SerializeObject(tus)}", null);

                            TData<UserEntity> userObj = await userThirdAuthBLL.SaveForm(tus);
                            if (userObj.Tag == 1)
                            {
                                var loginData = await LoginAuto(userObj.Data.UserName, userObj.Data.Password);
                                if (loginData.Tag == 1)
                                {
                                    new SessionHelper().WriteSession("auth_idtoken", strUserId);
                                    return Redirect("~/");
                                }
                                else
                                {
                                    userObj.Tag = 0;
                                    ViewBag.Msg = loginData.Message;
                                    return View();
                                }
                            }
                            else
                            {
                                userObj.Tag = 0;
                                ViewBag.Msg = userObj.Message;
                                return View();
                            }
                        }
                    }
                    else
                    {
                        ViewBag.Msg = "登录失败。";
                        LogHelper.Info($"登录失败。");
                        return View();
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.Info($"Auth_hzq: 异常：{ex.Message}");
                throw new Exception("程序出现异常，异常信息为：" + ex.Message);
            }
            return View();
        }

        /// <summary>
        /// 同步组织机构数据（连云港海州区）
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<TData> SyncOrgData()
        {
            TData obj = new TData();
            var ThirdSSO = Configuration.GetSection("sso").Get<ThirdSSO>();
            try
            {
                string webserviceUrl = ThirdSSO.dataHost; //webservice总接口

                //获取组织机构数据
                string sbData = "";
                sbData += "<soapenv:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:urn=\"urn:API\">";
                sbData += "<soapenv:Header/>";
                sbData += "<soapenv:Body>";
                sbData += "<urn:getOrgList soapenv:encodingStyle=\"http://schemas.xmlsoap.org/soap/encoding/\">";
                sbData += "<json xsi:type=\"xsd:string\">{\"code\":\"586A6560\"}</json>"; //默认 - 1获取所有不分层级，0获取一级（学校）数据
                sbData += "</urn:getOrgList>";
                sbData += "</soapenv:Body>";
                sbData += "</soapenv:Envelope>";
                HttpContent httpContent = new StringContent(sbData, Encoding.UTF8, "text/xml");
                string getOrgListResult = await this.PostHelper($"{webserviceUrl}/getOrgList", httpContent);
                LogHelper.Info($"getOrgList获取组织机构数据：{getOrgListResult}", null);
                if (!string.IsNullOrEmpty(getOrgListResult))
                {
                    LyqOrgModel orgModel = Util.Tools.JsonExtention.ToObject<LyqOrgModel>(getOrgListResult);
                    if (orgModel.flag == "true")
                    {
                        if (orgModel.value.Count > 0)
                        {
                            List<LyqOrgItem> orglist = orgModel.value; //所有组织机构数据
                            List<LyqOrgItem> toporglist = orgModel.value.FindAll(t => t.parent_id == "0"); //一级组织机构数据
                            long exteObjId = 0;
                            var countrylist = await unitBLL.GetList(new UnitListParam() { UnitType = (int)UnitTypeEnum.County, Statuz = StatusEnum.Yes.ParseToInt() });
                            if (countrylist.Data.Count > 0)
                            {
                                exteObjId = countrylist.Data.LastOrDefault().Id.Value;
                            }
                            LogHelper.Info($"country：{countrylist.Data.LastOrDefault().Id},{countrylist.Data.LastOrDefault().Name}", null);
                            foreach (var item in toporglist)
                            {
                                if (item.parent_id == "0") //0：一级（学校）数据
                                {
                                    UnitInputModel unit = new UnitInputModel();
                                    unit.Name = item.name;
                                    unit.ThirdUnitId = item.id;
                                    unit.UnitType = (int)UnitTypeEnum.School;
                                    unit.Code = item.id.ToString();
                                    unit.Statuz = StatusEnum.Yes.ParseToInt();
                                    TData<string> unitObj = await unitBLL.SaveForm(unit); //保存单位

                                    if (unitObj.Data != "")
                                    {
                                        //保存单位关联关系
                                        var unitrelationObj = await unitRelationBLL.SaveForm(new UnitRelationEntity()
                                        {
                                            Id = 0,
                                            UnitId = exteObjId,
                                            ExtensionType = 3,
                                            ExtensionObjId = unitObj.Data.ToLong()
                                        });

                                        if (!string.IsNullOrEmpty(item.children))
                                        {
                                            //将子级Id转为数组-保存至部门表
                                            string[] childrenArry = TextHelper.SplitToArray<string>(item.children, ',');
                                            foreach (var child in childrenArry)
                                            {
                                                if (child != "")
                                                {
                                                    DepartmentEntity department = new DepartmentEntity();
                                                    department.UnitId = unitObj.Data.ToLong(); //单位Id
                                                    department.ParentId = 0;
                                                    department.DepartmentName = orglist.Find(t => t.id == child).name;
                                                    department.DepartmentSort = child.ParseToInt();
                                                    department.Statuz = StatusEnum.Yes.ParseToInt();
                                                    await departmentBLL.SaveForm(department);
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            obj.Tag = 1;
                            obj.Message = "同步成功";
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                obj.Tag = 0;
                obj.Message = "同步失败";
                LogHelper.Info($"获取组织机构数据异常：{ex.Message}", null);
                throw new Exception("程序出现异常，异常信息为：" + ex.Message);
            }
            return obj;
        }

        /// <summary>
        /// 使用HttpClient调用WebService
        /// </summary>
        /// <param name="url">URL地址</param>
        /// <param name="content">参数</param>
        /// <returns></returns>
        private async Task<string> PostHelper(string url, HttpContent content)
        {
            var result = string.Empty;
            //LogHelper.Info($"PostHelper：url {url}", null, "Auth_hzq\\");
            //LogHelper.Info($"PostHelper：content {content}", null, "Auth_hzq\\");
            try
            {
                using (var client = _httpClientfactory.CreateClient())
                using (var response = await client.PostAsync(url, content))
                {
                    LogHelper.Info($"PostHelper：response {response}", null);
                    if (response.StatusCode == HttpStatusCode.OK)
                    {
                        var result1 = await response.Content.ReadAsStringAsync();
                        LogHelper.Info($"PostHelper：result1 {result1}", null);

                        XmlDocument doc = new XmlDocument();
                        doc.LoadXml(result1);
                        result = doc.InnerText;
                        LogHelper.Info($"PostHelper：result {result}", null);
                    }
                }
            }
            catch (Exception ex)
            {
                result = ex.Message;
                LogHelper.Info($"PostHelper异常：{ex.Message},{ex.StackTrace}", null);
                throw new Exception("程序出现异常，异常信息为：" + ex.Message);
            }
            return result;
        }

        #endregion 连云港海州区OA对接

        #region 南通市通州区统一身份认证

        /// <summary>
        /// 南通市通州区统一身份认证
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> Auth_tz()
        {
            var ThirdSSO = Configuration.GetSection("sso").Get<ThirdSSO>();
            string code = Request.Query["iflyssost"];
            TztxSoftAcore.SetConfig(ThirdSSO);
            if (string.IsNullOrEmpty(code)) //如果有code，获取用户Id
            {
                string url = TztxSoftAcore.GetLoginUrl();
                return Redirect(url);
            }
            var sesssionCode = GetSession("sso_third_nttz");
            if (string.IsNullOrEmpty(sesssionCode)) //首次
            {
                if (!string.IsNullOrEmpty(code))
                    SetSession("sso_third_nttz", code);
            }
            else if (sesssionCode == code) //重复访问
            {
                ViewBag.Msg = "重复请求，系统已经阻止！";
                LogHelper.Info("重复请求，系统已经阻止！");
                return View();
            }
            else //code不一致，更新
            {
                SetSession("sso_third_nttz", code);
            }

            try
            {
                string strUserId = "";
                LogHelper.Info($"SSO:iflyssost:{code}", null);
                TztxSoftAcore2.SetConfig(ThirdSSO);

                //获取用户Id
                var UserIdModel = TztxSoftAcore.GetUserIdModel(code);
                if (UserIdModel.code == "1")
                    strUserId = UserIdModel.data;
                LogHelper.Info($"SSO:UserIdModel:{JsonConvert.SerializeObject(UserIdModel)}", null);
                if (string.IsNullOrEmpty(strUserId))
                {
                    ViewBag.Msg = "获取用户ID失败！请联系认证中心。";
                    return View();
                }

                //获取用户角色
                Dictionary<string, string> parm = new Dictionary<string, string>();
                parm.Add("userId", strUserId);
                var roleResult = TztxSoftAcore2.GetDataByUserid(parm, "/listRoleByUserId");
                LogHelper.Info($"SSO:listRoleByUserId:{JsonConvert.SerializeObject(roleResult)}", null);

                if (roleResult != null && roleResult.data != null && roleResult.code == "1")
                {
                    var roleList = JsonConvert.DeserializeObject<List<TztxSoftAcore.Role>>(roleResult.data.ToString());
                    var role = roleList.Where(m => m.roleNature == "0" && (m.enName == "teacher" || m.enName == "edupersonnel" || m.enName == "instructor")).FirstOrDefault();
                    if (role == null)
                    {
                        LogHelper.Info($"SSO:获取用户角色信息失败:{JsonConvert.SerializeObject(roleList)}", null);

                        ViewBag.Msg = "未找到角色信息！您无权使用此功能，只有（教研员：instructor 机构用户：edupersonnel  教师：teacher）才能使用本平台。";
                        return View();
                    }
                    var userResult = TztxSoftAcore2.GetDataByUserid(parm, "/getUserByUserId");
                    if (userResult == null || userResult.data == null || userResult.code != "1")
                    {
                        LogHelper.Info($"SSO:获取用户信息失败:{JsonConvert.SerializeObject(userResult)}", null);
                        ViewBag.Msg = "获取用户信息失败，请确认是否登录超时。";
                        return View();
                    }
                    else
                    {
                        LogHelper.Info($"SSO:getUserByUserId:{JsonConvert.SerializeObject(userResult)}", null);
                    }
                    var user = JsonConvert.DeserializeObject<TztxSoftAcore.User>(userResult.data.ToString());
                    if (user == null)
                    {
                        LogHelper.Info($"SSO:获取用户信息失败:{JsonConvert.SerializeObject(userResult)}", null);

                        ViewBag.Msg = "获取用户信息失败，请确认是否登录超时。";
                        return View();
                    }

                    UserThirdAuth tus = new UserThirdAuth();

                    tus.ThirdUserId = strUserId;
                    tus.Mobile = user.mobile;
                    tus.UserName = user.loginName;
                    tus.RealName = user.userName;
                    #region
                    ////获取手机号码 (代码注销，通州区教育云平台没有此接口)
                    //try
                    //{
                    //    Dictionary<string, string> parmUpe = new Dictionary<string, string>();
                    //    parmUpe.Add("appId", ThirdSSO.clientID);
                    //    parmUpe.Add("userIds", "[" + strUserId + "]");
                    //    parmUpe.Add("attributes", "[loginName,mobile]");
                    //    parmUpe.Add("isEncrypt", "false");
                    //    var UserPlainInfoEcs = TztxSoftAcore2.GetDataByUserid(parmUpe, "/batchListUserPlainInfoEcs");
                    //    LogHelper.Info($"SSO:获取手机账号:{JsonConvert.SerializeObject(UserPlainInfoEcs)}", null, "Auth_Tz\\");

                    //    if (UserPlainInfoEcs.code == "1" && UserPlainInfoEcs.data.ToString().Length > 10)
                    //    {
                    //        //讯飞可能返回这个结果：{"code":"1","message":"success","data":{}}
                    //        string strUserInfos = UserPlainInfoEcs.data.ToString().Replace(strUserId, "userInfo");
                    //        var uInfo = JsonConvert.DeserializeObject<TztxSoftAcore2.UserBase>(strUserInfos);
                    //        if (uInfo != null)
                    //        {
                    //            tus.Mobile = uInfo.userInfo.mobile;
                    //            tus.UserName = uInfo.userInfo.loginName;
                    //        }
                    //        else //如果取不到数据，则表示学校未授权，禁止用户使用平台，否则会造成用户UserName不唯一，导致串号
                    //        {
                    //            LogHelper.Info($"未获取到用户用户手机号码与登录账号，系统阻止登录", null, "Auth_Tz\\");
                    //            ViewBag.Msg = "未获取到正确的手机号码与账号，暂时无法使用平台，请联系服务商解决。";
                    //            return View();
                    //        }
                    //    }
                    //    else //如果取不到数据，则表示学校未授权，禁止用户使用平台，否则会造成用户UserName不唯一，导致串号
                    //    {
                    //        LogHelper.Info($"未获取到用户用户手机号码与登录账号，系统阻止登录", null, "Auth_Tz\\");
                    //        ViewBag.Msg = "未获取到正确的手机号码与账号，暂时无法使用平台，请联系服务商解决。";
                    //        return View();
                    //    }
                    //}
                    //catch (Exception exp)
                    //{
                    //    LogHelper.Info($"SSO:获取手机账号_error:{exp.Message}", null, "Auth_Tz\\");
                    //    LogHelper.Info($"未获取到用户用户手机号码与登录账号，系统阻止登录", null, "Auth_Tz\\");
                    //    ViewBag.Msg = "未获取到正确的手机号码与账号，暂时无法使用平台，请联系服务商解决。";
                    //    return View();
                    //}
                    #endregion 南通市通州区统一身份认证

                    new SessionHelper().WriteSession("auth_idtoken", strUserId);

                    TztxSoftAcore.School unit = new TztxSoftAcore.School();
                    TztxSoftAcore2.Result unitResult = new TztxSoftAcore2.Result();
                    if (role.enName == "teacher")  //学校
                    {
                        tus.UnitType = UnitTypeEnum.School;

                        unitResult = TztxSoftAcore2.GetDataByUserid(parm, "/listSchoolByUser");
                        if (unitResult == null || unitResult.data == null || unitResult.code != "1")
                        {
                            LogHelper.Info($"SSO:获取学校信息失败:{JsonConvert.SerializeObject(unitResult)}", null);
                            ViewBag.Msg = "获取学校信息失败，请确认是否登录超时。";
                            return View();
                        }
                        LogHelper.Info($"SSO:listSchoolByUser:{JsonConvert.SerializeObject(unitResult)}", null);

                        var unitList = JsonConvert.DeserializeObject<List<TztxSoftAcore.School>>(unitResult.data.ToString());

                        unit = unitList.FirstOrDefault();
                        if (unit == null)
                        {
                            LogHelper.Info($"SSO:获取学校信息失败:{JsonConvert.SerializeObject(unitResult)}", null);

                            ViewBag.Msg = "获取学校信息失败，请确认是否登录超时。";
                            return View();
                        }
                        string schoolProp = "";

                        if (unit.phaseCode.IndexOf(',') != -1)
                        {
                            Regex regNum = new Regex(@"\d+(\.\d)?", RegexOptions.None);

                            var tempData = regNum.Matches(unit.phaseCode);
                            foreach (var t in tempData)
                            {
                                schoolProp += getTztxSchoolSection(t.ToString());
                            }
                            schoolProp = getSchoolProp(schoolProp);
                        }
                        else
                        {
                            schoolProp = getTztxSchoolSection(unit.phaseCode);
                        }
                        tus.UnitCode = unit.id;
                        tus.ParentUnitId = "Tzjyj20230907";
                        tus.UnitName = unit.schoolName;
                        tus.Brief = unit.shortName;
                        tus.Address = unit.address;
                        tus.SchoolProp = schoolProp;
                        tus.ThirdUnitId = unit.id;
                        tus.RoleId = 31; //学科教师
                    }
                    else if (role.enName == "edupersonnel" || role.enName == "instructor")
                    {
                        tus.UnitType = UnitTypeEnum.County;
                        tus.ThirdUnitId = "Tzjyj20230907";
                        tus.UnitName = "通州区教育局";
                        tus.UnitCode = "Tzjyj20230907";
                        tus.RoleId = 21; //区县教研员
                    }

                    LogHelper.Info($"SSO:保存信息:{JsonConvert.SerializeObject(tus)}", null);

                    TData<UserEntity> userObj = await userThirdAuthBLL.SaveForm(tus);
                    if (userObj.Tag == 1)
                    {
                        var loginData = await LoginAuto(userObj.Data.UserName, userObj.Data.Password);
                        if (loginData.Tag == 1)
                        {
                            return Redirect("~/");
                        }
                        else
                        {
                            ViewBag.Msg = loginData.Message;
                            return View();
                        }
                    }
                    else
                    {
                        ViewBag.Msg = userObj.Message;
                        return View();
                    }
                }
                else
                {
                    LogHelper.Info($"SSO:获取用户角色信息失败:{JsonConvert.SerializeObject(roleResult)}", null);
                    ViewBag.Msg = "获取用户角色信息失败！请联系认证中心。";
                    return View();
                }
            }
            catch (Exception exp)
            {
                LogHelper.Info($"SSO:login_error:{exp.Message} {exp.StackTrace}", null);
                ViewBag.Msg = "接口未获取到单位信息，认证失败，请重新认证！" + exp.Message;
                //日志写异常
                LogHelper.Error(exp);
            }

            return View();
        }

        #endregion

        #region 昆山市统一身份认证

        /// <summary>
        /// 昆山市统一身份认证
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> authkss()
        {
            /*
            实验教学服务平台
            对接AppCode：SYJXFWPT2308120001 AppKey：b2792acc-a249-40da-9235-02606a415391
            测试账户：testwanglei 密码：Test@1234
            登录地址：https://sso.ksedu.cn
            门户地址：https://uia.ksedu.cn
             */
            var ThirdSSO = Configuration.GetSection("sso").Get<ThirdSSO>();
            string code = Request.Query["Code"]; //获取临时授权码
            string AppCode = ThirdSSO.clientID;
            string AppKey = ThirdSSO.clientSecret;
            string AuthAddress = ThirdSSO.callBackUrl;
            string SSOAddress = ThirdSSO.authHost;
            string TimeSpan = Extention.GetTimeSpan().ToString();
            string EncryptString = SecurityHelper.MD5ToHex(AppCode + TimeSpan + AppKey);

            if (string.IsNullOrEmpty(code))
            {
                string url = string.Format("{0}/Authorization.aspx?AppCode={1}&EncryptString={2}&TimeSpan={3}&ReturnUrl={4}",
                    SSOAddress, AppCode, EncryptString, TimeSpan, AuthAddress);

                return Redirect(url);
            }
            var sesssionCode = GetSession("sso_third_kss");
            if (string.IsNullOrEmpty(sesssionCode)) //首次
            {
                if (!string.IsNullOrEmpty(code))
                    SetSession("sso_third_kss", code);
            }
            else if (sesssionCode == code) //重复访问
            {
                ViewBag.Msg = "重复请求，系统已经阻止！";
                LogHelper.Info("重复请求，系统已经阻止！", null);
                return View();
            }
            else //code不一致，更新
            {
                SetSession("sso_third_kss", code);
            }

            try
            {
                EncryptString = SecurityHelper.MD5ToHex(AppCode + code + TimeSpan + AppKey);
                string requestUrl = string.Format("{0}/api/ssoauth/AccessToken?AppCode={1}&EncryptString={2}&TimeSpan={3}&Code={4}", SSOAddress, AppCode, EncryptString, TimeSpan, code);
                string jsonToken = (await SendHelper.SendPostAsync(string.Format("{0}/api/ssoauth/AccessToken?AppCode={1}&EncryptString={2}&TimeSpan={3}&Code={4}", SSOAddress, AppCode, EncryptString, TimeSpan, code), "")).Data;
                string tokenId = "";
                if (!string.IsNullOrEmpty(jsonToken))
                {
                    SzkssApiModel tokenModel = JsonConvert.DeserializeObject<SzkssApiModel>(jsonToken);
                    if (tokenModel != null && tokenModel.Code == "200")
                    {
                        tokenId = tokenModel.Token;
                        EncryptString = SecurityHelper.MD5ToHex(AppCode + tokenId + TimeSpan + AppKey);
                        string jsonUser = (await SendHelper.SendPostAsync(string.Format("{0}/api/ssoauth/GetUserInfoByTokenID?AppCode={1}&EncryptString={2}&TimeSpan={3}&TokenID={4}", SSOAddress, AppCode, EncryptString, TimeSpan, tokenId), "")).Data;
                        if (!string.IsNullOrEmpty(jsonUser))
                        {
                            LogHelper.Info($"【昆山智慧教育云平台】jsonUser: {jsonUser}", null);
                            SzkssUserModel userInfo = JsonConvert.DeserializeObject<SzkssUserModel>(jsonUser);
                            if (userInfo != null && userInfo.Code == "200")
                            {
                                var thirdUm = userInfo.Result;
                                string third_id = thirdUm.UserName;
                                string third_unitId = thirdUm.OrgCode;

                                if (third_id != "testwanglei")
                                {
                                    #region 重新通过凤凰接口获取学校信息，确保学校名称正确性
                                    long ReqDate = DateTimeHelper.GetTimeSpan(Convert.ToDateTime(DateTime.Now.ToString("yyyy-MM-dd")));

                                    //Dictionary<string, string> heard = new Dictionary<string, string>();
                                    //heard.Add("ServiceName", "GetUserList");
                                    //heard.Add("AccountId", _accountid);
                                    //heard.Add("ReqTime", ReqDate.ToString());
                                    //heard.Add("DigitalSign", BitConverter.ToString(new
                                    //MD5CryptoServiceProvider().ComputeHash(Encoding.UTF8.GetBytes(string.Format("{0}&{1}&{2}", _accountid, _accountpwd, ReqDate)))));
                                    object objData = new { LoginIds = thirdUm.UserName };
                                    string bodyData = Base64.EncodeBase64(JsonStringHelper.Object2JSON(objData));

                                    object objPost = new
                                    {
                                        Header = new
                                        {
                                            ServiceName = "GetUserList",
                                            AccountId = ThirdSSO.accountid,
                                            DigitalSign = SecurityHelper.MD5ToHex(string.Format("{0}&{1}&{2}", ThirdSSO.accountid, ThirdSSO.accountpwd, ReqDate)),
                                            ReqTime = ReqDate
                                        },
                                        Body = bodyData
                                    };

                                    string jsonFhUser = (await SendHelper.SendPostAsync(ThirdSSO.dataHost2, JsonStringHelper.Object2JSON(objPost))).Data;
                                    LogHelper.Info($"【凤凰接口】获取数据: {jsonFhUser}", null);

                                    if (!string.IsNullOrEmpty(jsonFhUser))
                                    {
                                        RetFhData retFhData = JsonConvert.DeserializeObject<RetFhData>(jsonFhUser);
                                        if (retFhData.Header.RspCode != 1)
                                        {
                                            LogHelper.Info($"{retFhData.Header.RspDesc}", null);
                                            ViewBag.Msg = retFhData.Header.RspDesc;
                                            return View();
                                        }
                                        string retBody = Base64.DecodeBase64(retFhData.Body);
                                        var fhUser = JsonConvert.DeserializeObject<RetFhUser>(retBody);
                                        LogHelper.Info($"【凤凰接口】Body: {retBody}", null);

                                        if (fhUser.ResultList.Count > 0 && fhUser.ResultList.Exists(m => m.Status == 99))
                                        {
                                            var uf = fhUser.ResultList.Where(m => m.Status == 99).FirstOrDefault();
                                            uf.UserRoleId = ',' + uf.UserRoleId + ',';
                                            //排除学生0，家长 -4
                                            if (uf.UserRoleId.IndexOf(",0,") != -1 || uf.UserRoleId.IndexOf(",-4,") != -1)
                                                thirdUm.UserType = "学生";
                                            else if (ThirdSSO.countyusercode.IndexOf(',' + uf.SchoolId + ',') != -1
                                                || (uf.SchoolId == "0" && uf.UserRoleId != "-1"))
                                                thirdUm.UserType = "教育局";
                                            else
                                                thirdUm.UserType = "教师";

                                            third_id = uf.Id.ToString();
                                            thirdUm.UserName = uf.LoginId;
                                            thirdUm.RealName = uf.UserName;
                                            third_unitId = uf.SchoolId;
                                            thirdUm.Org = uf.SchoolName;
                                            thirdUm.OrgCode = third_unitId;
                                            thirdUm.thirdUserId = third_id;
                                        }
                                        else
                                        {
                                            LogHelper.Info($"【凤凰接口】获取用户信息失败。", null);
                                            ViewBag.Msg = "获取用户信息失败。";
                                            return View();
                                        }
                                    }
                                    else
                                    {
                                        LogHelper.Info($"【凤凰接口】：{ThirdSSO.dataHost2}获取数据失败。", null);
                                        ViewBag.Msg = "获取用户信息失败。";
                                        return View();
                                    }
                                    #endregion 通过凤凰接口获取学校信息结束
                                }
                                else
                                {
                                    thirdUm.UserType = "教师";
                                    thirdUm.UserName = "测试老师";
                                    thirdUm.RealName = "测试老师";
                                    thirdUm.Org = "测试单位";
                                    thirdUm.OrgCode = "CSDW001";
                                    thirdUm.thirdUserId = "CSYH001";
                                    third_id = thirdUm.thirdUserId;
                                    third_unitId = thirdUm.OrgCode;
                                }

                                UserThirdAuth tus = new UserThirdAuth();
                                new SessionHelper().WriteSession("auth_idtoken", third_id);
                                string schoolStage = "1001001";
                                if (thirdUm.UserType == "学生")
                                {
                                    LogHelper.Info($"您当前没有权限使用平台，用户角色：{thirdUm.UserType}。", null);
                                    ViewBag.Msg = "您当前没有权限使用平台。";
                                    return View();
                                }
                                else if (thirdUm.UserType == "教师")  //学校
                                {
                                    tus.UnitType = UnitTypeEnum.School;
                                    tus.ThirdUserId = third_id;
                                    tus.Mobile = thirdUm.Telephone;
                                    tus.UserName = thirdUm.UserName;
                                    tus.RealName = thirdUm.RealName;
                                    tus.UnitCode = third_unitId;
                                    tus.ParentUnitId = "Kssjyj20230913";
                                    tus.UnitName = thirdUm.Org;
                                    tus.SchoolProp = schoolStage;
                                    tus.ThirdUnitId = third_unitId;
                                    tus.RoleId = 31; //学科教师
                                }
                                else //教育局
                                {
                                    tus.ThirdUserId = third_id;
                                    tus.Mobile = thirdUm.Telephone;
                                    tus.UserName = thirdUm.UserName;
                                    tus.RealName = thirdUm.RealName;
                                    tus.UnitType = UnitTypeEnum.County;
                                    tus.ThirdUnitId = "Kssjyj20230913";
                                    tus.UnitName = thirdUm.Org;
                                    tus.UnitCode = "Kssjyj20230913";
                                    tus.RoleId = 21; //区县教研员
                                }

                                LogHelper.Info($"SSO:保存信息:{JsonConvert.SerializeObject(tus)}", null);
                                TData<UserEntity> userObj = await userThirdAuthBLL.SaveForm(tus);
                                LogHelper.Info($"SSO:保存结果:{JsonConvert.SerializeObject(userObj)}", null);
                                if (userObj.Tag == 1)
                                {
                                    var loginData = await LoginAuto(userObj.Data.UserName, userObj.Data.Password);
                                    if (loginData.Tag == 1)
                                    {
                                        return Redirect("~/");
                                    }
                                    else
                                    {
                                        ViewBag.Msg = loginData.Message;
                                        return View();
                                    }
                                }
                                else
                                {
                                    ViewBag.Msg = userObj.Message;
                                    return View();
                                }
                            }
                            else
                            {
                                ViewBag.Msg = userInfo.Description;
                            }
                        }
                        else
                        {
                            LogHelper.Info($"从【昆山智慧教育云平台】获取登录数据失败。", null);
                            ViewBag.Msg = "从【昆山智慧教育云平台】获取登录数据失败。";
                            return View();
                        }
                    }
                    else
                    {
                        LogHelper.Info($"【昆山智慧教育云平台】Token数据获取失败。", null);
                        EncryptString = SecurityHelper.MD5ToHex(AppCode + TimeSpan + AppKey);
                        string url = string.Format("{0}/Authorization.aspx?AppCode={1}&EncryptString={2}&TimeSpan={3}&ReturnUrl={4}",
                        SSOAddress, AppCode, EncryptString, TimeSpan, AuthAddress);

                        return Redirect(url);
                    }
                }
            }
            catch (WebException exp)
            {
                LogHelper.Info($"SSO:login_error:{exp.Message} {exp.StackTrace}", null);
                ViewBag.Msg = "从【昆山智慧教育云平台】获取登录数据失败。" + exp.Message;
                //日志写异常
                LogHelper.Error(exp);
            }
            return View();
        }

        #endregion

        #region 吴中区统一身份认证

        /// <summary>
        /// 吴中区平台介绍
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult Wzqpt()
        {
            return View();
        }

        /// <summary>
        /// 吴中区CAS统一身份认证处理页面
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        public async Task<IActionResult> AuthWzq()
        {
            var ThirdSSO = Configuration.GetSection("sso").Get<ThirdSSO>();
            try
            {
                #region CAS认证
                string CASHOST = ThirdSSO.authHost; //cas服务器地址

                string tkt = Request.Query["ticket"];
                string service = Request.GetDisplayUrl();
                if (service.IndexOf("AuthWzq") == -1)
                {
                    service += "AccountThird/AuthWzq";
                }
                if (tkt == null || tkt.Length == 0)   //检查未带ticket，重定向到cas登录页
                {
                    string redir = CASHOST + "/login?service=" + service;
                    LogHelper.Info($"redir: {redir}");
                    Response.Redirect(redir);
                    return View();
                }
                else
                {
                    LogHelper.Info($"ticket: {tkt}");
                    string newServiceUrl = service.Substring(0, service.IndexOf("?"));
                    LogHelper.Info($"newServiceUrl: {newServiceUrl}");
                    string validateurl = CASHOST + $"/serviceValidate?ticket={tkt}&service={newServiceUrl}";
                    LogHelper.Info($"validateurl: {validateurl}");
                    StreamReader Reader = new StreamReader(new WebClient().OpenRead(validateurl));   //根据ticket验证取回用户信息
                    string resp = Reader.ReadToEnd();
                    LogHelper.Info($"resp: {resp}");
                    NameTable nt = new NameTable();
                    XmlNamespaceManager nsmgr = new XmlNamespaceManager(nt);
                    XmlParserContext context = new XmlParserContext(null, nsmgr, null, XmlSpace.None);
                    XmlTextReader reader = new XmlTextReader(resp, XmlNodeType.Element, context);
                    while (reader.Read())  //从返回信息中读取用户账号等
                    {
                        if (reader.IsStartElement())
                        {
                            string tag = reader.LocalName;
                            if (tag == "user") //认证成功
                            {
                                string loginname = reader.ReadString(); //获取登录用户
                                #region 调用接口查询用户信息
                                LogHelper.Info($"获取到的用户: {reader.ReadString()}");
                                if (!string.IsNullOrEmpty(loginname))
                                {
                                    string apiUrl = ThirdSSO.dataHost; //接口地址
                                    string timeSpanStr = DateTime.Now.ToString("yyyyMMddHHmmss"); //时间戳格式：yyyyMMddHHmmss
                                                                                                  //根据登录名查询用户信息
                                    string apiName = "/api/sync/teacher";
                                    string appKey = "appKey=" + ThirdSSO.clientID;
                                    string authTimeSpan = "authTimeSpan=" + timeSpanStr;
                                    string currentPage = "currentPage=" + 1;
                                    string loginName = "loginName=" + loginname;
                                    string pageSize = "pageSize=" + 1;
                                    string secrect = ThirdSSO.clientSecret;
                                    string authSignStr = apiName + appKey + authTimeSpan + currentPage + loginName + pageSize + secrect;
                                    string authSign = SecurityHelper.MD5ToHex(authSignStr); //md5加密
                                    Dictionary<string, string> parm = new Dictionary<string, string>();
                                    parm.Add("appKey", ThirdSSO.clientID);
                                    parm.Add("authTimeSpan", timeSpanStr);
                                    parm.Add("currentPage", "1");
                                    parm.Add("loginName", loginname);
                                    parm.Add("pageSize", "1");
                                    var teacherResult = await SendHelper.SendPostAsyncByHeader(apiUrl + "/onlineService/api/sync/teacher", parm, "authSign", authSign);
                                    if (teacherResult.Tag == 1 && !string.IsNullOrEmpty(teacherResult.Data))
                                    {
                                        LogHelper.Info($"调用teacher接口获取到的数据：{teacherResult.Data}", null);
                                        SzwzqTeacherModel teachermodel = JsonExtention.ToObject<SzwzqTeacherModel>(teacherResult.Data);
                                        if (teachermodel.code == 200 || teachermodel.success == "true")
                                        {
                                            if (teachermodel.data.Count > 0)
                                            {
                                                string schoolCode = teachermodel.data.LastOrDefault().schoolCode; //学校唯一码
                                                if (!string.IsNullOrEmpty(schoolCode))  //根据学校唯一码查询学校信息
                                                {
                                                    //根据学校编码查询单位信息
                                                    string schoolApiName = "/api/sync/school";
                                                    string schoolcodeStr = "schoolCode=" + schoolCode;
                                                    string schoolauthSignStr = schoolApiName + appKey + authTimeSpan + currentPage + pageSize + schoolcodeStr + secrect;
                                                    string schoolauthSign = SecurityHelper.MD5ToHex(schoolauthSignStr); //md5加密
                                                                                                                        //LogHelper.Info($"schoolauthSignStr：{schoolauthSignStr}", null, "Auth_Wzq\\");
                                                    Dictionary<string, string> schoolparm = new Dictionary<string, string>
                                                    {
                                                        { "appKey", ThirdSSO.clientID },
                                                        { "authTimeSpan", timeSpanStr },
                                                        { "currentPage", "1" },
                                                        { "pageSize", "1" },
                                                        { "schoolCode", schoolCode }
                                                    };

                                                    var schoolResult = await SendHelper.SendPostAsyncByHeader(apiUrl + "/onlineService/api/sync/school", schoolparm, "authSign", schoolauthSign);
                                                    if (schoolResult.Tag == 1 && !string.IsNullOrEmpty(schoolResult.Data))
                                                    {
                                                        LogHelper.Info($"调用school接口获取到的数据：{schoolResult.Data}", null);
                                                        SzwzqSchoolModel schoolmodel = JsonExtention.ToObject<SzwzqSchoolModel>(schoolResult.Data);
                                                        if (schoolmodel.code == 200 || schoolmodel.success == "true")
                                                        {
                                                            if (schoolmodel.data.Count > 0)
                                                            {
                                                                var unit = schoolmodel.data.LastOrDefault();
                                                                UserThirdAuth tus = new UserThirdAuth();
                                                                string strUserId = teachermodel.data.LastOrDefault().teacherCode; //教师唯一码
                                                                tus.ThirdUserId = teachermodel.data.LastOrDefault().teacherCode;
                                                                tus.Mobile = teachermodel.data.LastOrDefault().mobile;
                                                                tus.UserName = teachermodel.data.LastOrDefault().loginAccount;
                                                                tus.RealName = teachermodel.data.LastOrDefault().name;

                                                                new SessionHelper().WriteSession("auth_idtoken", strUserId);
                                                                //1001001 小学
                                                                //1001002 初中
                                                                //1001003 高中
                                                                //1001004 九年制
                                                                //1001005 完中
                                                                //1001006 十二年制
                                                                //1001000 幼儿园

                                                                //312    九年一贯制
                                                                //341 完全中学
                                                                //345 十二年一贯制
                                                                if (unit.type == "211" || unit.type == "311" || unit.type == "342"
                                                                    || unit.type == "312"
                                                                    || unit.type == "341"
                                                                    || unit.type == "345") //办学类型 111其他（含幼儿园）,211小学,311初中,342高中
                                                                {
                                                                    string schoolProp = "1001004";
                                                                    if (unit.type == "211")
                                                                    {
                                                                        schoolProp = "1001001";
                                                                    }
                                                                    else if (unit.type == "311")
                                                                    {
                                                                        schoolProp = "1001002";
                                                                    }
                                                                    else if (unit.type == "342")
                                                                    {
                                                                        schoolProp = "1001003";
                                                                    }
                                                                    else if (unit.type == "312")
                                                                    {
                                                                        schoolProp = "1001004"; //九年制
                                                                    }
                                                                    else if (unit.type == "341")
                                                                    {
                                                                        schoolProp = "1001005";
                                                                    }
                                                                    else if (unit.type == "345")
                                                                    {
                                                                        schoolProp = "1001006";
                                                                    }

                                                                    tus.UnitType = UnitTypeEnum.School;
                                                                    tus.UnitCode = unit.schNo;
                                                                    tus.ParentUnitId = "Wzqjyj20231019";
                                                                    tus.UnitName = unit.name;
                                                                    tus.SchoolProp = schoolProp;
                                                                    tus.ThirdUnitId = unit.schId;
                                                                    tus.RoleId = 31; //学科教师
                                                                }
                                                                else if (unit.schId == "5d6019c872882646e050130a06003caf")
                                                                {
                                                                    //"name":"苏州市吴中区教师发展中心",
                                                                    //"schId":"5d6019c872882646e050130a06003caf",
                                                                    tus.UnitType = UnitTypeEnum.County;
                                                                    tus.ThirdUnitId = "Wzqjyj20231019";
                                                                    tus.UnitName = "吴中区教育局";
                                                                    tus.UnitCode = "Wzqjyj20231019";
                                                                }
                                                                else  //无主单位归为校级，以降低权限
                                                                {
                                                                    tus.UnitType = UnitTypeEnum.School;
                                                                    tus.UnitCode = unit.schNo;
                                                                    tus.ParentUnitId = "Wzqjyj20231019";
                                                                    tus.UnitName = unit.name;
                                                                    tus.SchoolProp = "1001001";
                                                                    tus.ThirdUnitId = unit.schId;
                                                                    tus.RoleId = 31;
                                                                }

                                                                LogHelper.Info($"SSO:保存信息:{JsonConvert.SerializeObject(tus)}", null);

                                                                TData<UserEntity> userObj = await userThirdAuthBLL.SaveForm(tus);
                                                                LogHelper.Info($"SSO:保存的结果:{JsonConvert.SerializeObject(userObj)}", null);
                                                                ViewBag.Msg = userObj.Tag.ToString() + userObj.Message;
                                                                LogHelper.Info($"SSO:userObj.Tag:{userObj.Tag},{userObj.Message}", null);
                                                                if (userObj.Tag == 1)
                                                                {
                                                                    var loginData = await LoginAuto(userObj.Data.UserName, userObj.Data.Password);
                                                                    LogHelper.Info($"SSO:loginData:{JsonConvert.SerializeObject(loginData)}", null);
                                                                    if (loginData.Tag == 1)
                                                                    {
                                                                        return Redirect("~/Home/Index");
                                                                    }
                                                                    else
                                                                    {
                                                                        ViewBag.Msg = loginData.Message;
                                                                        return View();
                                                                    }
                                                                }
                                                                else if (userObj.Tag == 2)
                                                                {
                                                                    //查询区县管理员账号
                                                                    string countyManager = "";
                                                                    List<UserEntity> obj = await userService.GetCountyUserList(new UserListParam { UnitId = userObj.Data.UnitId, RoleId = 16508640061130148 }, new Pagination { PageIndex = 1, PageSize = int.MaxValue });
                                                                    if (obj.Count > 0)
                                                                    {
                                                                        countyManager = obj.LastOrDefault().RealName;
                                                                    }
                                                                    //ViewBag.Msg = $"首次使用平台，需联系区县管理员({countyManager})为您授权，您的账号信息:{teachermodel.data.LastOrDefault().loginAccount},{teachermodel.data.LastOrDefault().name}";
                                                                    ViewBag.Msg = $"您是首次登录的用户，为了您后续的正常使用，请联系区县管理员为您授权。";
                                                                    return View();
                                                                }
                                                                else
                                                                {
                                                                    ViewBag.Msg = userObj.Message;
                                                                    return View();
                                                                }
                                                            }
                                                            else
                                                            {
                                                                ViewBag.Msg = "接口未获取到单位数据，认证失败，请重新认证！";
                                                                return View();
                                                            }
                                                        }
                                                    }
                                                }
                                                else
                                                {
                                                    ViewBag.Msg = "接口未获取到单位编码，认证失败，请重新认证！";
                                                    return View();
                                                }
                                            }
                                        }
                                        else
                                        {
                                            ViewBag.Msg = "接口未获取到用户信息，认证失败，请重新认证！";
                                            return View();
                                        }
                                    }
                                }
                                #endregion
                            }

                            if (tag == "authenticationFailure") //认证失败
                            {
                                ViewBag.Msg = "CAS认证失败，请重新认证。";
                                return View();
                            }
                        }
                    }
                    reader.Close();
                }

                #endregion
            }
            catch (Exception ex)
            {
                ViewBag.Msg = "CAS认证失败，请重新认证：" + ex.Message;
                LogHelper.Info($"CAS认证失败，请重新认证：{ex.Message}");
                throw new Exception("程序出现异常，异常信息为：" + ex.Message);
            }

            return View();
        }

        /// <summary>
        /// 吴中区CAS单点退出系统
        /// </summary>
        /// <returns></returns>
        public IActionResult WzqLoginOff()
        {
            return Redirect("http://www.szwzedu.cn/logout");
        }

        #endregion

        #region 常熟市统一身份认证

        public async Task<IActionResult> AuthCss()
        {
            var ThirdSSO = Configuration.GetSection("sso").Get<ThirdSSO>();
            TzSoftAcore.SetConfig(ThirdSSO);
            string code = Request.Query["ticket"];
            LogHelper.Info(string.Format("SSO:ticket:{0}", code));
            if (string.IsNullOrEmpty(code))
            {
                string url = TzSoftAcore.GetLoginUrl();
                return Redirect(url);
            }

            var sesssionCode = GetSession("sso_third_css");
            if (string.IsNullOrEmpty(sesssionCode))
            {
                if (!string.IsNullOrEmpty(code))
                    SetSession("sso_third_css", code);
            }
            else if (sesssionCode == code)
            {
                ViewBag.Msg = "重复请求，系统已经阻止！";
                LogHelper.Info("重复请求，系统已经阻止！");
                return View();
            }
            else //code不一致，更新
            {
                SetSession("sso_third_css", code);
            }

            try
            {
                string strUserId = "";

                if (!string.IsNullOrEmpty(code)) //如果有code，获取用户Id
                {
                    var accesstokenModel = TzSoftAcore.GetAccesstoken(); //获取用户accesstoken
                    LogHelper.Info(string.Format("SSO:accesstokenModel:{0}", JsonConvert.SerializeObject(accesstokenModel)));

                    if (accesstokenModel.retCode != "000000")
                    {
                        ViewBag.Msg = accesstokenModel.retDesc;
                        return View();
                    }
                    var ticketModel = TzSoftAcore.ValidaTicket(accesstokenModel.tokenInfo.accessToken, code);
                    LogHelper.Info(string.Format("SSO:ticketModel:{0}", JsonConvert.SerializeObject(ticketModel)));

                    if (ticketModel.code == "100001")
                    {
                        string url = TzSoftAcore.GetLoginUrl2();
                        return Redirect(url);
                    }
                    else if (ticketModel.code != "000000")
                    {
                        ViewBag.Msg = ticketModel.message;
                        return View();
                    }

                    //用户类型0：学生1:老师2:家长3:机构4:学校5:学校工作人员6:机构工作人员
                    string userType = ticketModel.result.last_user_type;
                    if (!userType.Contains("1") && !userType.Contains("3") && !userType.Contains("4")
                        && !userType.Contains("5") && !userType.Contains("6"))
                    {
                        ViewBag.Msg = string.Format("您没有权限使用平台，用户类型为【{0}】;用户类型0：学生1:老师2:家长3:机构4:学校5:学校工作人员6:机构工作人员", userType);
                        return View();
                    }

                    var user = ticketModel.result;
                    LogHelper.Info($"SSO:user:{JsonConvert.SerializeObject(user)}");
                    if (user == null)
                    {
                        LogHelper.Info("SSO:获取用户信息失败");
                        ViewBag.Msg = "获取用户信息失败，请确认是否登录超时。";
                        return View();
                    }

                    var schoolInfoModel = TzSoftAcore.GetSchoolInfo(accesstokenModel.tokenInfo.accessToken, user.last_top_org_id);
                    LogHelper.Info(string.Format("SSO:SchoolInfoModel:{0}", JsonConvert.SerializeObject(schoolInfoModel)));

                    if (schoolInfoModel.code != "000000")
                    {
                        ViewBag.Msg = schoolInfoModel.message;
                        return View();
                    }
                    var unit = schoolInfoModel.result;
                    if (unit == null)
                    {
                        LogHelper.Info($"SSO:获取单位信息失败");
                        ViewBag.Msg = "获取单位信息失败，请确认是否登录超时。";
                        return View();
                    }

                    strUserId = user.user_id;
                    UserThirdAuth tus = new UserThirdAuth();

                    tus.ThirdUserId = user.user_id;
                    tus.Mobile = user.phone;
                    tus.UserName = user.user_id;
                    tus.RealName = user.name;

                    new SessionHelper().WriteSession("auth_idtoken", strUserId);

                    if (user.last_user_type.Contains("1") || user.last_user_type.Contains("3"))  //学校
                    {
                        tus.UnitType = UnitTypeEnum.School;

                        string schoolProp = "";
                        int org_category = 0;
                        int.TryParse(unit.org_category, out org_category);
                        switch (org_category)
                        {
                            //0:小学1:初中2:高中3:完中4:机构5:教学点6:九年一贯制7: 十二年一贯制9： 其它教育类学校10: 中职11: 高职12：幼儿园
                            case 12:
                                schoolProp = "幼儿园";
                                break;

                            case 0:
                                schoolProp = "小学";
                                break;

                            case 1:
                                schoolProp = "初中";
                                break;

                            case 2:
                                schoolProp = "高中";
                                break;

                            case 3:
                                schoolProp = "完中";
                                break;

                            case 6:
                                schoolProp = "九年制";
                                break;

                            case 7:
                                schoolProp = "十二年制";
                                break;

                            case 10:
                            case 11:
                                schoolProp = "中高职";
                                break;

                            default:
                                schoolProp = "";
                                break;
                        }
                        tus.UnitCode = string.IsNullOrEmpty(unit.org_coding) ? unit.org_id : unit.org_coding;
                        tus.UnitName = user.last_top_org_name;
                        tus.Brief = unit.org_short_name;
                        tus.Address = unit.org_address;
                        tus.SchoolProp = schoolProp;
                        tus.ParentUnitId = "Cssjyj20231226";
                        tus.ThirdUnitId = unit.org_id;
                        tus.RoleId = 31; //学校默认角色：学科教师
                    }
                    else if (user.last_user_type.Contains("4", StringComparison.Ordinal)) //区县
                    {
                        tus.UnitType = UnitTypeEnum.County;
                        tus.ThirdUnitId = "Cssjyj20231226";
                        tus.UnitCode = "Cssjyj20231226";
                        tus.RoleId = 21; //教育局默认角色：区县教研员
                    }
                    else
                    {
                        ViewBag.Msg = "您的账号类型不允许使用平台";
                        return View();
                    }

                    LogHelper.Info($"SSO:保存第三方用户信息:{JsonConvert.SerializeObject(tus)}");
                    TData<UserEntity> userObj = await userThirdAuthBLL.SaveForm(tus);
                    LogHelper.Info($"SSO:保存结果:{JsonConvert.SerializeObject(userObj)}");
                    if (userObj.Tag == 1)
                    {
                        var loginData = await LoginAuto(userObj.Data.UserName, userObj.Data.Password);
                        if (loginData.Tag == 1)
                        {
                            return Redirect("~/");
                        }
                        else
                        {
                            ViewBag.Msg = loginData.Message;
                            return View();
                        }
                    }
                    else
                    {
                        ViewBag.Msg = userObj.Message;
                        return View();
                    }
                }
                else //ticket不存在
                {
                    LogHelper.Info("ticket不存在！");
                    ViewBag.Msg = "未能获取到ticket！";
                    //string url = TzSoftAcore.GetLoginUrl();
                    //return Redirect(url);
                    return View();
                }
            }
            catch (Exception exp)
            {
                LogHelper.Info(string.Format("SSO:login_error:{0} {1}", exp.Message, exp.StackTrace));
                ViewBag.Msg = "接口未获取到单位信息，认证失败，请重新认证！" + exp.Message;
                //日志写异常
                LogHelper.Error(exp);
            }

            return View();
        }

        #endregion

        #region 富阳魔方统一身份认证对接

        /// <summary>
        /// 富阳魔方统一身份认证对接
        /// </summary>
        /// <returns></returns>
        public IActionResult Auth_fyq()
        {
            return View();
        }

        public async Task<IActionResult> Auth_fyq_result()
        {
            var ThirdSSO = Configuration.GetSection("sso").Get<ThirdSSO>();
            string sessionId = Request.Query["sid"]; //sessionid
            string cubeCommonUserId = Request.Query["uid"]; //魔方用户id
            string cubeCorpId = Request.Query["cid"]; //魔方组织id
            LogHelper.Info($"Auth_fyq_result接收到的参数:cubeCommonUserId:{cubeCommonUserId},cubeCorpId:{cubeCorpId},sessionId:{sessionId}");
            if (string.IsNullOrEmpty(sessionId) || string.IsNullOrEmpty(cubeCommonUserId) || string.IsNullOrEmpty(cubeCorpId))
            {
                LogHelper.Info($"Auth_fyq_result页面未获取到sessionId");
                ViewBag.Msg = "认证失败，请重新认证！";
                return View();
            }

            string apiUrl = "https://cube.zjedu.com";// ThirdSSO.dataHost; //接口地址
            string apiName = "/user/api/v1/login/getUserInfoBySessionId"; //根据sessionId获取用户信息

            var sessionResult = await SendHelper.SendPostAsync(apiUrl + apiName, "", "sessionId", sessionId);
            if (sessionResult.Tag == 1 && !string.IsNullOrEmpty(sessionResult.Data))
            {
                LogHelper.Info($"调用session接口获取到的数据：{sessionResult.Data}", null);
                FyqSessionModel sessionmodel = JsonExtention.ToObject<FyqSessionModel>(sessionResult.Data);
                if (sessionmodel.code == "200" || sessionmodel.success == "true")
                {
                    UserThirdAuth tus = new UserThirdAuth();

                    tus.ThirdUserId = cubeCommonUserId;
                    tus.Mobile = sessionmodel.data.mobile;
                    tus.UserName = sessionmodel.data.name;
                    tus.RealName = sessionmodel.data.name;

                    new SessionHelper().WriteSession("auth_idtoken", cubeCommonUserId);

                    if (sessionmodel.data.CorpName.IndexOf("教育局") > -1)
                    {
                        //区县
                        tus.UnitType = UnitTypeEnum.County;
                        tus.ThirdUnitId = "Fyqjyj20240618";
                        tus.UnitCode = cubeCorpId;
                        tus.UnitName = "富阳区教育局";
                        tus.RoleId = 21; //教育局默认角色：区县教研员
                    }
                    else
                    {
                        //学校
                        tus.UnitType = UnitTypeEnum.School;
                        tus.UnitCode = cubeCorpId;
                        tus.UnitName = sessionmodel.data.CorpName;
                        tus.Brief = sessionmodel.data.CorpName;
                        tus.ParentUnitId = "Fyqjyj20240618";
                        tus.ThirdUnitId = cubeCorpId;
                        tus.RoleId = 31; //学校默认角色：学科教师
                    }

                    LogHelper.Info($"SSO:保存第三方用户信息:{JsonConvert.SerializeObject(tus)}");
                    TData<UserEntity> userObj = await userThirdAuthBLL.SaveForm(tus);
                    LogHelper.Info($"SSO:保存结果:{JsonConvert.SerializeObject(userObj)}");
                    if (userObj.Tag == 1)
                    {
                        var loginData = await LoginAuto(userObj.Data.UserName, userObj.Data.Password);
                        if (loginData.Tag == 1)
                        {
                            return Redirect("~/");
                        }
                        else
                        {
                            ViewBag.Msg = loginData.Message;
                            return View();
                        }
                    }
                    else
                    {
                        ViewBag.Msg = userObj.Message;
                        return View();
                    }
                }
                else
                {
                    ViewBag.Msg = "接口未获取到用户信息，认证失败，请重新认证！";
                    LogHelper.Info($"调用sessionId获取用户信息接口获取数据失败：{sessionmodel.code},{sessionmodel.message}", null);
                    return View();
                }
            }
            else
            {
                ViewBag.Msg = "认证失败，请重新认证！";
                LogHelper.Info($"调用sessionId获取用户信息接口获取数据失败！", null);
                return View();
            }
        }

        #endregion

        #region 研发管理平台账号授权访问对接

        public async Task<IActionResult> Auth_Visit()
        {
            var ThirdSSO = Configuration.GetSection("authvisit").Get<ThirdSSO>();
            string code = Request.Query["code"];
            LogHelper.Info("code:" + code);

            var sesssionCode = GetSession("sso_third_visit");
            if (string.IsNullOrEmpty(sesssionCode))
            {
                if (!string.IsNullOrEmpty(code))
                    SetSession("sso_third_visit", code);
            }
            else if (sesssionCode == code)
            {
                ViewBag.Msg = "重复请求，系统已经阻止！";
                LogHelper.Info("重复请求，系统已经阻止！");
                return View();
            }
            else //code不一致，更新
            {
                SetSession("sso_third_self", code);
            }
            if (string.IsNullOrEmpty(code))
            {
                ViewBag.Msg = "未能获取到code，请重新访问！";
                LogHelper.Info("未能获取到code，请重新访问！");
                return View();
            }
            else
            {
                string ClientBacuUrl = ThirdSSO.callBackUrl;
                string authWebUrl = ThirdSSO.authHost;
                string dataHostUrl = ThirdSSO.dataHost;
                string timeSpan = Extention.GetTimeSpan().ToString();
                LogHelper.Info("dataHostUrl：" + dataHostUrl);

                //根据code获取token
                string jsonapiUrl = $"{dataHostUrl}/AccountOpera/AccessToken";
                object objData = new { TimeSpan = timeSpan, Code = code };
                string jsonToken = (await SendHelper.SendPostAsync(jsonapiUrl, JsonStringHelper.Object2JSON(objData))).Data;
                string tokenId;
                if (jsonToken != "")
                {
                    LogHelper.Info("jsonToken:" + JsonConvert.SerializeObject(jsonToken));
                    VisitTokenResult resultModel = JsonConvert.DeserializeObject<VisitTokenResult>(jsonToken);
                    if (resultModel != null && resultModel.success == true)
                    {
                        tokenId = resultModel.response.Token;
                        int aId = resultModel.response.AId;
                        string jwttoken = "Bearer " + tokenId; //JWTToken

                        string jsonuserUrl = $"{dataHostUrl}/AccountOpera/GetAccessUser";
                        object userobjData = new { AccessId = aId.ToString() };
                        string jsonUser = (await SendHelper.SendPostAsync(jsonapiUrl, JsonStringHelper.Object2JSON(objData), jwttoken)).Data;

                        LogHelper.Info("jsonUser:" + JsonConvert.SerializeObject(jsonUser));
                        if (jsonUser != "")
                        {
                            AccessUserResult userResult = JsonConvert.DeserializeObject<AccessUserResult>(jsonUser);
                            if (userResult != null && userResult.success)
                            {
                                AccessUserModel um = userResult.response;
                                //查询账号是否存在并关联登录
                                var userlist = await userBLL.GetList(new UserListParam() { UserName = um.Name });
                                if (userlist.Data.Count > 0)
                                {
                                    var userObj = userlist.Data.LastOrDefault();
                                    var loginData = await LoginAuto(userObj.UserName, userObj.Password);
                                    if (loginData.Tag == 1)
                                    {
                                        LogHelper.Info($"{DateTime.Now}:{userObj.Id}-{userObj.UserName}用户登录平台");
                                        return Redirect("~/");
                                    }
                                    else
                                    {
                                        ViewBag.Msg = loginData.Message;
                                        return View();
                                    }
                                }
                                else
                                {
                                    ViewBag.Msg = "未能获取到相关用户的账号！";
                                    return View();
                                }
                            }
                            else
                            {
                                LogHelper.Info(string.Format("SSO:获取用户信息失败:{0}", JsonConvert.SerializeObject(jsonUser)));
                                ViewBag.Msg = "获取用户信息失败！";
                                return View();
                            }
                        }
                        else
                        {
                            LogHelper.Info("调用AccountOpera/GetAccessUser接口失败");
                            ViewBag.Msg = "调用AccountOpera/GetAccessUser接口失败";
                            return View();
                        }
                    }
                    else
                    {
                        LogHelper.Info("调用/AccountOpera/AccessToken接口失败");
                        ViewBag.Msg = "获取Token失败";
                        return View();
                    }
                }
                else
                {
                    LogHelper.Info("AccountOpera/AccessToken接口调用失败");
                    ViewBag.Msg = "AccountOpera/AccessToken调用失败";
                    return View();
                }
            }
        }

        #endregion

        #region 姑苏区统一身份认证对接

        public async Task<IActionResult> Auth_gsq()
        {
            string code = Request.Query["code"]; //code：单点登录系统颁发的一次性令牌，该令牌在访问一次接口后，无论成功与否均会失效。
            string state = Request.Query["state"];
            LogHelper.Info($"code: {code},state: {state}");
            var ThirdSSO = Configuration.GetSection("sso").Get<ThirdSSO>();
            if (string.IsNullOrEmpty(code))
            {
                string url = ThirdSSO.logout;
                return Redirect(url);
            }

            var sesssionCode = GetSession("sso_third_gsq");
            if (string.IsNullOrEmpty(sesssionCode))
            {
                if (!string.IsNullOrEmpty(code))
                    SetSession("sso_third_gsq", code);
            }
            else if (sesssionCode == code)
            {
                ViewBag.Msg = "重复请求，系统已经阻止！";
                LogHelper.Info("重复请求，系统已经阻止！");
                return View();
            }
            else //code不一致，更新
            {
                SetSession("sso_third_gsq", code);
            }

            string authAddress = ThirdSSO.authHost;
            string dataAddress = ThirdSSO.dataHost;
            string clientID = ThirdSSO.clientID;
            string clientSecret = ThirdSSO.clientSecret;
            string redirectUri = ThirdSSO.callBackUrl;
            string userid = "";
            string username = "";
            string usertype = "";
            string unitid = "";
            string gettokenUrl = $"{authAddress}/auth/oauth/token?grant_type=authorization_code&code={code}&redirect_uri={redirectUri}&response_content_type=";
            string tokenAuth = "Basic " + Base64.EncodeBase64(clientID + ":" + clientSecret);
            //使用code换取token
            var gettokenObj = await SendHelper.SendGetAsyncByHeader(gettokenUrl, "Authorization", tokenAuth);
            LogHelper.Info($"SSO: 获取token接口的数据 {JsonConvert.SerializeObject(gettokenObj)}");
            if (gettokenObj.Tag == 1 && !string.IsNullOrEmpty(gettokenObj.Data))
            {
                OauthTokenModel tokenmodel = JsonExtention.ToObject<OauthTokenModel>(gettokenObj.Data);
                if (tokenmodel != null)
                {
                    if (string.IsNullOrEmpty(tokenmodel.access_token))
                    {
                        LogHelper.Info($"SSO:未能获取到token");
                        ViewBag.Msg = $"未能获取到token";
                        return View();
                    }
                    string token = tokenmodel.access_token;
                    SetSession("sso_third_gsqtoken", token);
                    userid = tokenmodel.user_id;
                    username = tokenmodel.username;
                    usertype = tokenmodel.gsdataObjectType; //1为学生 2为教师 3为其他
                    unitid = tokenmodel.gsdataSchoolId;
                    if (usertype == "1") //1为学生 2为教师 3为其他
                    {
                        LogHelper.Info($"SSO:gsdataObjectType:{usertype},类型学生");
                        ViewBag.Msg = $"您没有权限使用平台，用户类型为【{usertype}】:学生";
                        return View();
                    }
                    string getuserinfoUrl = $"{authAddress}/admin/user/info";
                    string userAuth = "Bearer " + tokenmodel.access_token;
                    var getuserinfoObj = await SendHelper.SendGetAsyncByHeader(getuserinfoUrl, "Authorization", userAuth);
                    LogHelper.Info($"SSO: 获取到userinfo接口数据 {JsonConvert.SerializeObject(getuserinfoObj)}");
                    if (getuserinfoObj.Tag == 1 && getuserinfoObj.Data != null)
                    {
                        GsqUserInfoModel usermodel = JsonExtention.ToObject<GsqUserInfoModel>(getuserinfoObj.Data);
                        if (usermodel != null)
                        {
                            if (usermodel.data.sysUser == null)
                            {
                                LogHelper.Info($"SSO:获取sysUser数据失败");
                                ViewBag.Msg = "获取用户信息失败";
                                return View();
                            }

                            //获取单位信息
                            string getunitinfoUrl = $"{authAddress}/admin/user/unitInfo";
                            var getunitinfoObj = await SendHelper.SendGetAsyncByHeader(getunitinfoUrl, "Authorization", userAuth);
                            LogHelper.Info($"SSO: 获取到unitInfo接口数据 {JsonConvert.SerializeObject(getunitinfoObj)}");
                            if (getunitinfoObj.Tag == 1 && getunitinfoObj.Data != null)
                            {
                                GsqUnitInfoModel unitmodel = JsonExtention.ToObject<GsqUnitInfoModel>(getunitinfoObj.Data);
                                if (unitmodel != null)
                                {
                                    if (unitmodel.data == null)
                                    {
                                        LogHelper.Info($"SSO:获取unit数据失败");
                                        ViewBag.Msg = "获取单位信息失败";
                                        return View();
                                    }
                                    if (unitmodel.data.schoolType == "幼儿园") //幼儿园、小学、初中、机构
                                    {
                                        ViewBag.Msg = string.Format("您没有权限使用平台，单位类型为【{0}】;", unitmodel.data.schoolType);
                                        return View();
                                    }
                                }

                                UserThirdAuth tus = new UserThirdAuth();
                                tus.ThirdUserId = userid;
                                tus.Mobile = usermodel.data.sysUser.phone;
                                tus.UserName = usermodel.data.sysUser.username;
                                tus.RealName = usermodel.data.sysUser.name;
                                string schoolProp = ""; //学段
                                if (unitmodel.data.schoolType == "小学")
                                {
                                    schoolProp = "1001001";
                                }
                                else if (unitmodel.data.schoolType == "初中")
                                {
                                    schoolProp = "1001002";
                                }
                                //89319CFE2FF24BC09806DABDFD798AB2 苏州市姑苏区教育科学研究中心
                                new SessionHelper().WriteSession("auth_idtoken", userid);
                                if (usertype == "2" && unitmodel.data.schoolId != "89319CFE2FF24BC09806DABDFD798AB2") //1为学生 2为教师 3为其他
                                {
                                    tus.UnitType = UnitTypeEnum.School;
                                    tus.UnitCode = unitmodel.data.schoolId;
                                    tus.ParentUnitId = "Gsqjyj20240703";
                                    tus.UnitName = unitmodel.data.schoolName;
                                    tus.SchoolProp = schoolProp;
                                    tus.ThirdUnitId = unitmodel.data.schoolId;
                                    tus.RoleId = 31; //学科教师
                                }
                                else if (usertype == "3" || unitmodel.data.schoolId == "89319CFE2FF24BC09806DABDFD798AB2")
                                {
                                    tus.UnitType = UnitTypeEnum.County;
                                    tus.ThirdUnitId = "Gsqjyj20240703";
                                    tus.UnitName = "姑苏区教育局";
                                    tus.UnitCode = "Gsqjyj20240703";
                                    tus.RoleId = 21; //区县教研员
                                }

                                LogHelper.Info($"SSO:保存信息:{JsonConvert.SerializeObject(tus)}", null);

                                TData<UserEntity> userObj = await userThirdAuthBLL.SaveForm(tus);
                                LogHelper.Info($"SSO:保存的结果:{JsonConvert.SerializeObject(userObj)}", null);
                                ViewBag.Msg = userObj.Tag.ToString() + userObj.Message;
                                LogHelper.Info($"SSO:userObj.Tag:{userObj.Tag},{userObj.Message}", null);
                                if (userObj.Tag == 1)
                                {
                                    var loginData = await LoginAuto(userObj.Data.UserName, userObj.Data.Password);
                                    LogHelper.Info($"SSO:loginData:{JsonConvert.SerializeObject(loginData)}", null);
                                    if (loginData.Tag == 1)
                                    {
                                        return Redirect("~/Home/Index");
                                    }
                                    else
                                    {
                                        ViewBag.Msg = loginData.Message;
                                        return View();
                                    }
                                }
                                else
                                {
                                    ViewBag.Msg = userObj.Message;
                                    return View();
                                }
                            }
                            else
                            {
                                LogHelper.Info($"SSO:未能获取到unit信息");
                                ViewBag.Msg = "未能获取到单位信息";
                                return View();
                            }
                        }
                        else
                        {
                            LogHelper.Info($"SSO:未能获取到用户信息");
                            ViewBag.Msg = "未能获取到用户信息";
                            return View();
                        }
                    }
                    else
                    {
                        LogHelper.Info($"SSO:获取userinfo接口数据失败");
                        ViewBag.Msg = "获取用户信息失败";
                        return View();
                    }
                }
            }
            else
            {
                LogHelper.Info($"SSO:获取token失败:{JsonConvert.SerializeObject(gettokenObj)}");
                ViewBag.Msg = "获取token失败";
                return View();
            }
            return View();
        }

        /// <summary>
        /// 姑苏区-退出登录
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> Logout_gsq()
        {
            var ThirdSSO = Configuration.GetSection("sso").Get<ThirdSSO>();
            string authAddress = ThirdSSO.authHost;
            string access_token = GetSession("sso_third_gsqtoken");
            string redirect_url = ThirdSSO.authHost;
            string logoutUrl = $"{authAddress}/auth/token/logout?redirect_uri={redirect_url}";
            string logoutAuth = "Bearer " + access_token;
            var logoutObj = await SendHelper.SendGetAsyncByHeader(logoutUrl, "Authorization", logoutAuth);
            if (logoutObj.Tag == 1 && !string.IsNullOrEmpty(logoutObj.Data))
            {
                GsqLogoutModel logoutmodel = JsonExtention.ToObject<GsqLogoutModel>(logoutObj.Data);
                LogHelper.Info($"SSO:logout:{JsonConvert.SerializeObject(logoutmodel)}");
                return Redirect(ThirdSSO.logout);
            }
            return View();
        }

        #endregion

        #region 常州市智慧教育统一身份认证对接。

        /// <summary>
        ///  常州市智慧教育统一身份认证对接。
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> Auth_czs()
        {
            var ThirdSSO = Configuration.GetSection("sso").Get<ThirdSSO>();
            CzsSoftAuthCore.SetConfig(ThirdSSO);
            string user_token = Request.Query["user_token"];
            if (string.IsNullOrEmpty(user_token))
            {
                return Redirect(CzsSoftAuthCore.GetLoginUrl(ThirdSSO.callBackUrl));
            }
            LogHelper.Info(string.Format("user_token:{0}", user_token));
            var nopowerurl = "https://jyjapps.czerc.cn/iedu/unmatch/unmatch?appId=" + ThirdSSO.clientID;
            try
            {
                string host = string.Format("{0}/eumpApi/OAuth/access_token", ThirdSSO.authHost);

                string postStr = string.Format("app_id={0}&secret={1}", ThirdSSO.clientID, ThirdSSO.clientSecret);

                LogHelper.Info(string.Format("get_access_token:host:{0} post:{1}", host, postStr));

                var access_token = CzsSoftAuthCore.GetAccesstoken();
                if (string.IsNullOrEmpty(access_token))
                {
                    LogHelper.Info("从“常州市智慧教育应用基础服务平台”获取access_token失败， 接口/eumpApi/OAuth/access_token");
                    return Redirect(nopowerurl);
                }

                object postData = new { user_token = user_token };
                string param = RSAUtils.EncryptByPublicKey(JsonStringHelper.Object2JSON(postData), ThirdSSO.key);
                var thirdUserInfoSource = CzsSoftAuthCore.GetUser(param, access_token);
                if (thirdUserInfoSource.code == "-1")
                {
                    LogHelper.Info("从“常州市智慧教育应用基础服务平台”获取用户信息失败， 接口/eumpApi/OAuth/user" + thirdUserInfoSource.msg);
                    return Redirect(nopowerurl);
                }
                else
                {
                    LogHelper.Info("获取原始数据信息user" + thirdUserInfoSource.msg);
                }
                var thirdUserInfo = JsonConvert.DeserializeObject<ResultModel>(thirdUserInfoSource.msg); //CzsSoftAuthCore.GetUser(param, access_token);
                //LogHelper.Info(string.Format("thirdUserInfo:{0}", thirdUserInfo));
                if (thirdUserInfo != null && thirdUserInfo.code == "1")
                {
                    string strData = thirdUserInfo.dataset.rows[0].ToString();
                    string strJson = RSAUtils.DecryptByPrivateKey(strData, ThirdSSO.privatekey);
                    LogHelper.Info(string.Format("thirdUserList:{0}", strJson));
                    var thirdUserList = JsonConvert.DeserializeObject<List<CzsSoftAuthCore.UserInfoModel>>(strJson);
                    if (thirdUserList.Count > 0)
                    {
                        var thirdUser = thirdUserList.FirstOrDefault();
                        if (string.IsNullOrEmpty(thirdUser.OPEN_ID))
                        {
                            LogHelper.Info("从“常州市智慧教育应用基础服务平台”获取用户信息失败， 接口/eumpApi/api/v1.0/user/getUser。");
                            return Redirect(nopowerurl);
                        }
                        postData = new { open_id = thirdUser.OPEN_ID };
                        param = RSAUtils.EncryptByPublicKey(JsonStringHelper.Object2JSON(postData), ThirdSSO.key);
                        if (thirdUser.LX == "学生")
                        {
                            LogHelper.Info("当前登录人员为学生，暂无权限。");
                            return Redirect(nopowerurl);
                        }
                        var thirdUserDetailResultStr = CzsSoftAuthCore.GetUserDetail(param, access_token);
                        LogHelper.Info("获取原始数据信息GetUserDetail::" + thirdUserDetailResultStr.msg);
                        var thirdUserDetailResult = JsonConvert.DeserializeObject<ResultModel>(thirdUserDetailResultStr.msg);

                        if (thirdUserDetailResult != null && thirdUserDetailResult.code == "1")
                        {
                            if (thirdUserDetailResult.dataset == null || thirdUserDetailResult.dataset.rows.Length <= 0)
                            {
                                LogHelper.Info("请先在【常州市智慧教育应用基础服务平台】完善用户信息，然后才能使用本功能。< br /> 参考信息：" + JsonStringHelper.Object2JSON(thirdUserDetailResult));
                                return Redirect(nopowerurl);
                            }

                            strData = thirdUserDetailResult.dataset.rows[0].ToString();

                            strJson = RSAUtils.DecryptByPrivateKey(strData, ThirdSSO.privatekey);
                            LogHelper.Info(string.Format("thirdUserDetailList:{0}", strJson));

                            var thirdUserDetailList = JsonConvert.DeserializeObject<List<CzsSoftAuthCore.UserInfoDetailModel>>(strJson);

                            if (thirdUserDetailList.Count > 0)
                            {
                                // DEPT_TYPE (教育局，学校，公司)
                                var thirdUd = thirdUserDetailList.FirstOrDefault();
                                var thirdUnit = thirdUd.deptList.FirstOrDefault();
                                if (thirdUnit == null)
                                {
                                    LogHelper.Info("请先在【常州市智慧教育应用基础服务平台】完善用户信息，然后才能使用本功能。< br /> 参考信息：" + JsonStringHelper.Object2JSON(thirdUserDetailList));
                                    return Redirect(nopowerurl);
                                }

                                UserThirdAuth tus = new UserThirdAuth();
                                tus.ThirdUnitId = thirdUnit.DEPT_ID;
                                tus.ThirdUserId = thirdUser.USERID;
                                tus.UserName = thirdUser.LOGINNAME;  //user.account;
                                tus.RealName = thirdUser.USERNAME;
                                tus.Mobile = thirdUser.PHONE;
                                tus.UnitName = thirdUnit.DEPTNAME;

                                switch (thirdUnit.DEPT_TYPE)
                                {
                                    case "学校":

                                        tus.UnitType = UnitTypeEnum.School;
                                        tus.RoleId = RoleValueEnum.CommonPerson.ParseToInt();//学科教师
                                        tus.UnitCode = thirdUnit.DEPT_ID;
                                        tus.ParentUnitId = "Czsjyj20240911";
                                        tus.UnitName = thirdUnit.DEPTNAME;
                                        tus.SchoolProp = "";
                                        //string schoolProp = "";
                                        //int org_category = 0;
                                        //int.TryParse(thirdUser.LX, out org_category);
                                        //switch (org_category)
                                        //{
                                        //    //0:小学1:初中2:高中3:完中4:机构5:教学点6:九年一贯制7: 十二年一贯制9： 其它教育类学校10: 中职11: 高职12：幼儿园
                                        //    case 12:
                                        //        schoolProp = "幼儿园";
                                        //        break;
                                        //    case 0:
                                        //        schoolProp = "小学";
                                        //        break;
                                        //    case 1:
                                        //        schoolProp = "初中";
                                        //        break;
                                        //    case 2:
                                        //        schoolProp = "高中";
                                        //        break;
                                        //    case 3:
                                        //        schoolProp = "完中";
                                        //        break;
                                        //    case 6:
                                        //        schoolProp = "九年制";
                                        //        break;
                                        //    case 7:
                                        //        schoolProp = "十二年制";
                                        //        break;
                                        //    case 10:
                                        //    case 11:
                                        //        schoolProp = "中高职";
                                        //        break;
                                        //    default:
                                        //        schoolProp = "";
                                        //        break;
                                        //}

                                        break;

                                    case "教育局":
                                        tus.UnitType = UnitTypeEnum.County;
                                        tus.RoleId = RoleValueEnum.CountyInstructor.ParseToInt(); //区县教研员

                                        tus.UnitType = UnitTypeEnum.County;
                                        tus.ThirdUnitId = "Czsjyj20240911";
                                        tus.UnitName = "常州市教育局";
                                        tus.UnitCode = "Czsjyj20240911";
                                        break;

                                    default:
                                        tus.UnitType = 0;
                                        break;
                                }
                                if (tus.UnitType <= 0)
                                {
                                    LogHelper.Info("账号信息中【DEPT_TYPE】未识别，，无权使用本功能。请联系【常州市智慧教育应用基础服务平台】。<br />参考信息：" + strJson);
                                    return Redirect(nopowerurl);
                                }
                                new SessionHelper().WriteSession("auth_idtoken", thirdUser.USERID);

                                TData<UserEntity> userObj = await userThirdAuthBLL.SaveForm(tus);
                                if (userObj.Tag == 1)
                                {
                                    var loginData = await LoginAuto(userObj.Data.UserName, userObj.Data.Password);
                                    if (loginData.Tag == 1)
                                    {
                                        return Redirect("~/");
                                    }
                                    else
                                    {
                                        ViewBag.Msg = loginData.Message;
                                        return View();
                                    }
                                }
                                else
                                {
                                    ViewBag.Msg = userObj.Message;
                                    return View();
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception exp)
            {
                LogHelper.Info(string.Format("SSO:login_error:{0} {1}", exp.Message, exp.StackTrace));
                ViewBag.Msg = "接口未获取到单位信息，认证失败，请重新认证！" + exp.Message;
                //日志写异常
                LogHelper.Error(exp);
            }

            return View();
        }

        #endregion

        #region 厦门双十中学海沧附属学校统一身份认证对接

        /// <summary>
        /// 厦门双十中学海沧附属学校统一身份认证对接
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> XmShuangshix()

        {
            LogHelper.Info($"------------>开始进入厦门双十中学装备平台统一身份认证对接", null);

            string code = Request.Query["Code"];
            string jftoken = Request.Query["jftoken"];
            string identityEnum = Request.Query["identityEnum"];
            int userType = 0;
            int.TryParse(identityEnum, out userType);

            if (userType == 2)
            {
                LogHelper.Info($"当前角色无权操作,当前角色枚举值为：{userType}。1-教师，2-家长,3-学生", null);
                ViewBag.Msg = "当前角色无权操作。";
                return View();
            }

            TData<List<AppManageEntity>> obj = await appManagerBLL.GetList(new AppManageListParam() { AppKey = jftoken, AppType = 1, IsMain = 2 });
            if (obj.Total == 0)
            {
                LogHelper.Info($"请联系运维人员在【第三方应用管理】->【第三方应用列表】中配置相关内容。", null);
                ViewBag.Msg = "请联系运维人员在【第三方应用管理】->【第三方应用列表】中配置相关内容。";
                return View();
            }
            AppManageEntity objEntity = obj.Data.FirstOrDefault();

            try
            {
                int roleId = RoleValueEnum.CommonPerson.ParseToInt();
                if (userType == 3)
                {
                    roleId = 500;
                }

                //获取Token
                string url = $"{objEntity.AuthHost}/auth/api/v1.0/inquireAccessToken.json?appkey={objEntity.ClientId}&appSecret={objEntity.ClientSecret}&code={code}";

                ////测试开始
                //string userName = "zhiyuan";
                //string userPswd = "3f0ea22060c164bf4bdc3a67d0e12cdf";
                //var loginData = await LoginAuto(userName, userPswd);
                //LogHelper.Info($"---->登录返回数据信息为：{JsonConvert.SerializeObject(loginData)}", null);
                //if (loginData.Tag == 1)
                //{
                //    List<string> listAgent = new List<string>() { "iphone", "ipad", "ipod", "android", "silk", "blackberry", "windows phone", "pad", "pod", "symbian", "fennec" };
                //    string userAgent = NetHelper.UserAgent.ToLower();
                //    if (listAgent.Exists(f => userAgent.Contains(f)))
                //    {
                //        obj = await appManagerBLL.GetList(new AppManageListParam() { AppKey = "6f0521v3w3522g88", AppType = 2, IsMain = 1 });
                //        if (obj.Total > 0)
                //        {
                //            objEntity = obj.Data.FirstOrDefault();
                //            UserEntity userEntity = loginData.ExtendData as UserEntity;
                //            TData<List<AppManageEntity>> listAppData = await appManagerBLL.GetList(new AppManageListParam() { ClientId = objEntity.AppKey });
                //            if (listAppData.Total > 0)
                //            {
                //                string cred_token = SecurityHelper.Base64Encrypt(SecurityHelper.MD5ToHex(userEntity.Id.ToString() + listAppData.Data[0].ClientId + listAppData.Data[0].ClientSecret + DateTime.Now.ToString("yyyyMMdd")));
                //                userEntity.ApiToken = cred_token;
                //                await new UserBLL().UpdateUser(userEntity);
                //            }
                //            url = $"{objEntity.AuthHost}?userid={userEntity.Id.ToString()}&token={userEntity.WebToken}&appid={objEntity.AppKey}";
                //            LogHelper.Info($"---->进入移动端跳转，跳转地址为：{url}", null);
                //            Response.Redirect(url);
                //        }
                //    }
                //    else
                //    {
                //        SetSession("third_token", objEntity.AppKey);
                //        return Redirect("~/Home/Index");
                //    }
                //}
                //else
                //{
                //    ViewBag.Msg = loginData.Message;
                //    LogHelper.Info($"---->自动登录失败，失败原因：{loginData.Message}", null);
                //    return View();
                //}
                ////测试结束

                LogHelper.Info($"---->获取Token请求地址信息为：{url}", null);
                string returnData = (await SendHelper.SendGetAsync(url)).Data;
                LogHelper.Info($"---->获取Token返回数据信息为：{returnData}", null);
                RetrunXmResult<TokenXmModel> tokenModel = JsonConvert.DeserializeObject<RetrunXmResult<TokenXmModel>>(returnData);
                if (tokenModel.status.Equals("200"))
                {
                    TokenXmModel objToken = tokenModel.datas.FirstOrDefault();
                    if (!string.IsNullOrEmpty(objToken.accessToken) && !string.IsNullOrEmpty(objToken.openid))
                    {
                        url = $"{objEntity.AuthHost}/auth/api/v1.0/inquireAuthAccountInfo.json?accessToken={objToken.accessToken}&openid={objToken.openid}";
                        LogHelper.Info($"---->获取用户信息请求地址信息为：{url}", null);
                        returnData = (await SendHelper.SendGetAsync(url)).Data;
                        LogHelper.Info($"---->获取用户信息返回数据信息为：{returnData}", null);
                        RetrunXmResult<UserXmModel> userModel = Newtonsoft.Json.JsonConvert.DeserializeObject<RetrunXmResult<UserXmModel>>(returnData);
                        if (userModel.status.Equals("200"))
                        {
                            UserXmModel objUser = userModel.datas.FirstOrDefault();
                            if (objUser != null)
                            {
                                #region 保存第三方用户信息
                                UserThirdAuth tus = new UserThirdAuth();
                                tus.ThirdUnitId = ""; //单位唯一标识
                                tus.ThirdUserId = objUser.openid;
                                tus.UserName = objUser.openid;          //用户唯一标识
                                tus.RealName = objUser.fullName;        //用户名称
                                tus.Mobile = objUser.mobile;
                                tus.UnitName = objUser.tenantName;      //单位名称
                                tus.UnitType = UnitTypeEnum.School;     //单位类型
                                tus.RoleId = roleId;
                                tus.UnitCode = objUser.openid;
                                tus.ParentUnitId = "Xmshuangshi7831";
                                tus.SchoolProp = "";

                                var unitInfo = (await unitBLL.GetEntity(objEntity.UnitId.Value)).Data;
                                if (unitInfo == null)
                                {
                                    ViewBag.Msg = "单位不存在，请联系运维人员处理。";
                                    LogHelper.Info($"---->单位不存在，请联系运维人员处理。", null);
                                    return View();
                                }
                                if (unitInfo != null && string.IsNullOrEmpty(unitInfo.ThirdUnitId))
                                {
                                    if (!string.IsNullOrEmpty(objUser.openTenantId))
                                    {
                                        unitInfo.ThirdUnitId = "yuexun" + objUser.openTenantId;
                                    }
                                    else
                                    {
                                        unitInfo.ThirdUnitId = "yuexun" + unitInfo.Id;
                                    }

                                    await unitBLL.SaveFormInner(unitInfo);
                                }
                                tus.ThirdUnitId = unitInfo.ThirdUnitId;

                                LogHelper.Info($"---->保存前tus数据为：{JsonConvert.SerializeObject(tus)}", null);

                                TData<UserEntity> userObj = await userThirdAuthBLL.SaveForm(tus);

                                LogHelper.Info($"---->保存后userObj数据为：{JsonConvert.SerializeObject(userObj)}", null);
                                ViewBag.Msg = userObj.Tag.ToString() + userObj.Message;
                                LogHelper.Info($"---->保存数据结果为：{userObj.Tag},{userObj.Message}", null);
                                if (userObj.Tag == 1)
                                {
                                    var loginData = await LoginAuto(userObj.Data.UserName, userObj.Data.Password);
                                    LogHelper.Info($"---->登录返回数据信息为：{JsonConvert.SerializeObject(loginData)}", null);
                                    if (loginData.Tag == 1)
                                    {
                                        UserEntity userEntity = loginData.ExtendData as UserEntity;

                                        //此处增加如果为学生要更新学生信息
                                        if (userType == 3)
                                        {
                                            //调用接口获取学生信息
                                            url = $"{objEntity.AuthHost}/auth/api/v1.0/inquireAuthAccountStudentInfo.json?accessToken={objToken.accessToken}&openid={objToken.openid}";

                                            LogHelper.Info($"---->获取学生信息请求地址信息为：{url}", null);
                                            returnData = (await SendHelper.SendGetAsync(url)).Data;
                                            LogHelper.Info($"---->获取学生信息返回数据信息为：{returnData}", null);
                                            RetrunXmResult<StudentXmModel> studentModel = Newtonsoft.Json.JsonConvert.DeserializeObject<RetrunXmResult<StudentXmModel>>(returnData);
                                            if (studentModel.status.Equals("200"))
                                            {
                                                StudentXmModel objStudent = studentModel.datas.FirstOrDefault();
                                                if (objStudent != null)
                                                {
                                                    int gradeId = 0;
                                                    if (objStudent.gradeName.Equals("高一年级"))
                                                    {
                                                        objStudent.gradeName = "高一";
                                                    }
                                                    if (objStudent.gradeName.Equals("高二年级"))
                                                    {
                                                        objStudent.gradeName = "高二";
                                                    }
                                                    if (objStudent.gradeName.Equals("高三年级"))
                                                    {
                                                        objStudent.gradeName = "高三";
                                                    }
                                                    string idCard = objStudent.openStudentId;
                                                    string idCard6 = string.Empty;
                                                    if (objStudent.openStudentId.Length > 18)
                                                    {
                                                        idCard = objStudent.openStudentId.Substring(0, 18);
                                                    }

                                                    if (!string.IsNullOrEmpty(objUser.idCard))
                                                    {
                                                        idCard = objUser.idCard;
                                                    }

                                                    TData<StaticDictionaryEntity> objDictionary = await dictionaryBLL.GetEntityByGradeName(objStudent.gradeName);
                                                    if (objDictionary.Data != null)
                                                    {
                                                        gradeId = objDictionary.Data.DictionaryId.Value;
                                                    }

                                                    if (idCard.Length == 18)
                                                    {
                                                        idCard6 = idCard.Substring(12, 6);
                                                    }
                                                    else if (idCard.Length > 6)
                                                    {
                                                        idCard6 = idCard.Substring(0, 6);
                                                    }
                                                    else
                                                    {
                                                        idCard6 = idCard;
                                                    }
                                                    idCard = Base64.EncodeBase64(idCard);

                                                    TData<StudentEntity> studentEntity = await studentBLL.GetEntityByUserId(userEntity.Id.Value);
                                                    StudentEntity stu = studentEntity.Data;
                                                    if (stu != null)
                                                    {
                                                        LogHelper.Info($"---->获取学生表up_Student信息数据为：{JsonConvert.SerializeObject(stu)}", null);

                                                        stu.SchoolId = objEntity.UnitId.Value;
                                                        stu.StudentNo = objStudent.studentNumber;
                                                        stu.StudentName = objStudent.fullName;
                                                        stu.Sex = objStudent.genderEnum == 1 ? "男" : "女";
                                                        stu.IDCard = idCard;
                                                        stu.IDCard6 = idCard6;
                                                        stu.Mobile = objUser.mobile;
                                                        stu.AuthStatuz = 2;
                                                        stu.GradeId = gradeId;
                                                        stu.ClassName = objStudent.className;
                                                        stu.UserId = userEntity.Id.Value;
                                                        await studentBLL.SaveUnifiedForm(stu);

                                                        TData<ParentStudentEntity> parentStudentEntity = await parentStudentBLL.GetEntityByStudentId(stu.Id.Value);
                                                        ParentStudentEntity parent = parentStudentEntity.Data;
                                                        if (parent != null)
                                                        {
                                                            LogHelper.Info($"---->获取家长学生信息表up_ParentStudent数据为：{JsonConvert.SerializeObject(parent)}", null);

                                                            parent.UserId = userEntity.Id.Value;
                                                            parent.Mobile = objUser.mobile;
                                                            parent.StudentName = objStudent.fullName;
                                                            parent.Sex = objStudent.genderEnum == 1 ? "男" : "女";
                                                            parent.IDCard = idCard;
                                                            parent.IDCard6 = idCard6;
                                                            parent.GradeId = gradeId;
                                                            parent.ClassName = objStudent.className;
                                                            parent.StudentId = stu.Id.Value;
                                                            parent.SchoolId = objEntity.UnitId.Value;
                                                            parent.AuthStatuz = 2;
                                                            await parentStudentBLL.SaveUnifiedForm(parent);
                                                        }
                                                    }
                                                    else
                                                    {
                                                        StudentEntity stuAdd = new StudentEntity();
                                                        stuAdd.SchoolId = objEntity.UnitId.Value;
                                                        stuAdd.StudentNo = objStudent.studentNumber;
                                                        stuAdd.StudentName = objStudent.fullName;
                                                        stuAdd.Sex = objStudent.genderEnum == 1 ? "男" : "女";
                                                        stuAdd.IDCard = idCard;
                                                        stuAdd.IDCard6 = idCard6;
                                                        stuAdd.Mobile = objUser.mobile;
                                                        stuAdd.AuthStatuz = 2;
                                                        stuAdd.GradeId = gradeId;
                                                        stuAdd.ClassName = objStudent.className;
                                                        stuAdd.UserId = userEntity.Id.Value;
                                                        LogHelper.Info($"---->新增学生表up_Student信息数据为：{JsonConvert.SerializeObject(stuAdd)}", null);
                                                        TData<string> studentData = await studentBLL.SaveUnifiedForm(stuAdd);

                                                        if (studentData.Tag == 1)
                                                        {
                                                            ParentStudentEntity parentStudentAdd = new ParentStudentEntity();
                                                            parentStudentAdd.UserId = userEntity.Id.Value;
                                                            parentStudentAdd.Mobile = objUser.mobile;
                                                            parentStudentAdd.StudentName = objStudent.fullName;
                                                            parentStudentAdd.Sex = objStudent.genderEnum == 1 ? "男" : "女";
                                                            parentStudentAdd.IDCard = idCard;
                                                            parentStudentAdd.IDCard6 = idCard6;
                                                            parentStudentAdd.GradeId = gradeId;
                                                            parentStudentAdd.ClassName = objStudent.className;
                                                            parentStudentAdd.StudentId = long.Parse(studentData.Data);
                                                            parentStudentAdd.SchoolId = objEntity.UnitId.Value;
                                                            parentStudentAdd.AuthStatuz = 2;
                                                            LogHelper.Info($"---->新增家长学生信息表up_ParentStudent数据为：{JsonConvert.SerializeObject(parentStudentAdd)}", null);
                                                            await parentStudentBLL.SaveUnifiedForm(parentStudentAdd);
                                                        }
                                                    }
                                                }
                                            }
                                        }

                                        List<string> listAgent = new List<string>() { "iphone", "ipad", "ipod", "android", "silk", "blackberry", "windows phone", "pad", "pod", "symbian", "fennec" };
                                        string userAgent = _netHelper.GetUserAgent().ToLower();
                                        if (listAgent.Exists(f => userAgent.Contains(f)))
                                        {
                                            obj = await appManagerBLL.GetList(new AppManageListParam() { AppKey = "6f0521v3w3522g88", AppType = 2, IsMain = 1 });
                                            if (obj.Total > 0)
                                            {
                                                objEntity = obj.Data.FirstOrDefault();
                                                TData<List<AppManageEntity>> listAppData = await appManagerBLL.GetList(new AppManageListParam() { ClientId = objEntity.AppKey, AppType = 2, IsMain = 2 });
                                                if (listAppData.Total > 0)
                                                {
                                                    string cred_token = SecurityHelper.Base64Encrypt(SecurityHelper.MD5ToHex(userEntity.Id.ToString() + listAppData.Data[0].ClientId + listAppData.Data[0].ClientSecret + DateTime.Now.ToString("yyyyMMdd")));
                                                    userEntity.ApiToken = cred_token;
                                                    await new UserBLL().UpdateUser(userEntity);
                                                }
                                                //密钥主表需要配置跳转至前端H5页面地址,接收userid、token、appid 3个字段数据
                                                url = $"{objEntity.AuthHost}/h5/#/pages/login_third/webh5?userid={userEntity.Id.ToString()}&token={userEntity.WebToken}&appid={objEntity.AppKey}";
                                                LogHelper.Info($"---->进入移动端跳转，跳转地址为：{url}", null);
                                                Response.Redirect(url);
                                            }
                                            else
                                            {
                                                ViewBag.Msg = $"移动端获取失败，失败原因：移动端密钥主表Appkey:{jftoken}未配置";
                                                LogHelper.Info($"---->移动端获取失败，失败原因：移动端密钥主表Appkey:{jftoken}未配置", null);
                                                return View();
                                            }
                                        }
                                        else
                                        {
                                            SetSession("third_token", objEntity.AppKey);
                                            return Redirect("~/Home/Index");
                                        }
                                    }
                                    else
                                    {
                                        ViewBag.Msg = loginData.Message;
                                        LogHelper.Info($"---->自动登录失败，失败原因：{loginData.Message}", null);
                                        return View();
                                    }
                                }
                                else
                                {
                                    ViewBag.Msg = userObj.Message;
                                    LogHelper.Info($"---->保存数据失败，失败原因：{userObj.Message}", null);
                                    return View();
                                }
                                #endregion
                            }
                        }
                        else
                        {
                            LogHelper.Info($"---->获取用户信息失败，失败原因：{userModel.message}", null);
                            ViewBag.Msg = $"获取用户信息失败，失败原因：{userModel.message}。";
                            return View();
                        }
                    }
                }
                else
                {
                    LogHelper.Info($"---->获取Token失败，失败原因：{tokenModel.message}", null);
                    ViewBag.Msg = $"获取Token失败，失败原因：{tokenModel.message}。";
                    return View();
                }
            }
            catch (Exception ex)
            {
                LogHelper.Info($"---->从【悦讯智慧教育云平台】获取数据失败，错误信息为:{ex.Message} {ex.StackTrace}", null);
                ViewBag.Msg = $"从【悦讯智慧教育云平台】获取数据失败。" + ex.Message;
                //日志写异常
                LogHelper.Error(ex);
            }

            return View();
        }

        #endregion

        #region 宿城区海康平台统一身份认证对接

        /// <summary>
        /// 宿城区统一身份认证(1：没有code,重定向；2：获取token；3获取用户使用固定用户登录)
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        public async Task<IActionResult> AuthScq()
        {
            string code = Request.Query["code"];

            var sesssionCode = GetSession("sso_third_scq");
            if (string.IsNullOrEmpty(sesssionCode))
            {
                if (!string.IsNullOrEmpty(code))
                    SetSession("sso_third_scq", code);
            }
            else if (sesssionCode == code)
            {
                ViewBag.Msg = "重复请求，系统已经阻止！";
                LogHelper.Info("重复请求，系统已经阻止！");
                return View();
            }
            else //code不一致，更新
            {
                SetSession("sso_third_scq", code);
            }
            string appcode = Request.Query["appcode"];
            if (string.IsNullOrEmpty(appcode))
            {
                appcode = "bVJlSklmTUJxY25GNVlnSg";
            }
            TData<List<AppManageEntity>> obj = await appManagerBLL.GetList(new AppManageListParam() { AppCode = appcode, AppType = 1, IsMain = 2 });
            if (obj.Total == 0)
            {
                LogHelper.Info($"请联系运维人员在【第三方应用管理】->【第三方应用列表】中配置相关内容。", null);
                ViewBag.Msg = "请联系运维人员在【第三方应用管理】->【第三方应用列表】中配置相关内容。";
                return View();
            }
            AppManageEntity objEntity = obj.Data.FirstOrDefault();
            int port = 443;
            int.TryParse(objEntity.CorpId, out port);
            var url = "";
            url = $"{objEntity.AuthHost}/artemis/api/application/auth/v2/oauth2/authorize?response_type=code&state=10001&appKey={objEntity.AppKey}";
            url += $"&redirect_uri={WebUtility.UrlEncode(objEntity.CallBackUrl)}";
            url += $"&menu_uri={WebUtility.UrlEncode(objEntity.CallBackUrl)}";
            if (string.IsNullOrEmpty(code))
            {
                LogHelper.Info($"---->获取授权码请求地址信息为：{url}");
                return Redirect(url);
            }
            try
            {
                //获取授权码成功。获取token
                AuthScqTokenParam tokenRequest = new AuthScqTokenParam();
                tokenRequest.appKey = objEntity.AppKey;
                tokenRequest.appSecret = objEntity.AppSecret;
                tokenRequest.code = code;
                tokenRequest.redirect_uri = WebUtility.UrlEncode(objEntity.CallBackUrl);//回调地址，获取code的重定向地址。
                LogHelper.Info($"---->获取token请求参数信息为：{JsonConvert.SerializeObject(tokenRequest)}");

                var urltoken = "/artemis/api/application/auth/v2/oauth2/accessToken";
                LogHelper.Info($"---->获取token请求地址信息为：{urltoken}");

                bool isHttps = true;
                HkHttpUtillib.SetPlatformInfo(objEntity.AppKey, objEntity.AppSecret, objEntity.AngentId, port, isHttps);
                // 发起POST请求，超时时间15秒，返回响应字节数组
                string body = JsonConvert.SerializeObject(tokenRequest);
                byte[] result = HkHttpUtillib.HttpPost(urltoken, body, 15);
                if (result == null)
                {
                    LogHelper.Info($"---->获取token失败");
                    ViewBag.Msg = $"获取token失败。返回值：null";
                    return View();
                }
                LogHelper.Info($"---->获取token返回信息为：{result}");
                string authTokenJson = System.Text.Encoding.UTF8.GetString(result);
                AuthScqTokenResult authToken = JsonConvert.DeserializeObject<AuthScqTokenResult>(authTokenJson);
                if (authToken != null && authToken.code == "0" && authToken.data != null && authToken.data.access_token != null)
                {
                    var urluser = $"/artemis/api/application/auth/v2/oauth2/userinfo";
                    LogHelper.Info($"---->获取用户请求地址信息为：{urluser}");
                    HkHttpUtillib.SetPlatformInfo(objEntity.AppKey, objEntity.AppSecret, objEntity.AngentId, port, isHttps);
                    result = HkHttpUtillib.HttpGet(urluser, 15, authToken.data.access_token);
                    string userReturnData = System.Text.Encoding.UTF8.GetString(result);
                    LogHelper.Info($"---->获取用户返回数据信息为：{userReturnData}");
                    AuthScqUserResult userReturnModel = JsonConvert.DeserializeObject<AuthScqUserResult>(userReturnData);
                    if (userReturnModel != null && userReturnModel.code == "0" && userReturnModel.data != null && userReturnModel.data.userId != null)
                    {
                        //获取用户成功
                        LogHelper.Info($"---->获取用户成功。");
                        var userObj = await userBLL.GetEntity(815542719186210816);
                        if (userObj == null)
                        {
                            LogHelper.Info($"---->获取本地用户815542719186210816信息失败，用户不存在。");
                        }
                        var loginData = await LoginAuto(userObj.Data.UserName, userObj.Data.Password, 1);
                        if (loginData.Tag == 1)
                        {
                            return Redirect("~/");
                        }
                        else
                        {
                            ViewBag.Msg = loginData.Message;
                            return View();
                        }
                    }
                    else
                    {
                        LogHelper.Info($"---->获取用户返回数据转换失败。result:{userReturnData}");
                        ViewBag.Msg = $"获取用户失败。返回值:{userReturnData}";
                    }
                }
                else
                {
                    LogHelper.Info($"---->获取token失败", null);
                    ViewBag.Msg = $"获取token失败。返回值：{authTokenJson}";
                }
            }
            catch (Exception ex)
            {
                LogHelper.Info($"---->从【宿城区统一身份认证】获取数据失败，错误信息为:{ex.Message} {ex.StackTrace}", null);
                ViewBag.Msg = $"从【宿城区统一身份认证】获取数据失败。" + ex.Message;
                //日志写异常
                LogHelper.Error(ex);
            }
            return View();
        }

        #endregion

        #region 无锡市智慧教育
        public async Task<IActionResult> AuthWXSIndex()
        {
            OperatorInfo operatorInfo = await Code.Operator.Instance.Current();

            if(operatorInfo == null)
            {
                return Redirect("https://jyzn.wxeic.cn/login");
            }
            else
            {
                return Redirect("~/");
            }             
        }

        public async Task<IActionResult> AuthWXS()
        {
            string code = Request.Query["code"];

            if (!string.IsNullOrEmpty(code))
            {
                var sesssionCode = GetSession("sso_third_wxzhjy");
                if (string.IsNullOrEmpty(sesssionCode))
                {
                    SetSession("sso_third_wxzhjy", code);
                }
                else if (sesssionCode == code)
                {
                    ViewBag.Msg = "重复请求，系统已经阻止！";
                    LogHelper.Info("重复请求，系统已经阻止！");
                    return View();
                }
                else //code不一致，更新
                {
                    SetSession("sso_third_wxzhjy", code);
                }
            }

            TData<List<AppManageEntity>> obj = await appManagerBLL.GetList(new AppManageListParam() { AppCode = "NTJidUZpdzBaMzZzSHlFdw", AppType = 1, IsMain = 2 });
            if (obj.Total == 0)
            {
                throw new BusinessException("请联系运维人员在【第三方应用管理】->【第三方应用列表】中配置相关内容。");
            }
            AppManageEntity objEntity = obj.Data.FirstOrDefault();

            if (string.IsNullOrEmpty(code))
            {
                var authorizeQueryParams = new Dictionary<string, string>
                {
                    { "grant_type", "authorization_code" },
                    { "client_id", objEntity.ClientId },
                    { "redirect_uri", objEntity.CallBackUrl },
                    { "state", Guid.NewGuid().ToString() },
                    {"response_type","code" }
                };

                var authorizeQueryString = string.Join("&", authorizeQueryParams.Select(
                    kvp => $"{Uri.EscapeDataString(kvp.Key)}={Uri.EscapeDataString(kvp.Value)}"
                ));

                var authorizeUrl = $"{objEntity.AuthHost}api/oauth/authorize?{authorizeQueryString}";
                LogHelper.Info($"---->获取授权码请求地址信息为：{authorizeUrl}");
                return Redirect(authorizeUrl);
              
            }

            try
            {
                var _iwxzhjyApi = await GetWxszhjyAuthCodeApiService(code, objEntity);
                var userInfoResponse = await _iwxzhjyApi.GetUserInfo();
                var userInfo = userInfoResponse.Data;

                UserThirdAuth tus = new()
                {
                    ThirdUserId = userInfo.Uuid,
                    UserName = userInfo.Name,
                    RealName = userInfo.Name,
                    Mobile = userInfo.Mobile,
                };

                if (!string.IsNullOrEmpty(userInfo.SchoolCode))
                {
                    tus.UnitType = UnitTypeEnum.School;
                    tus.UnitCode = userInfo.SchoolCode;
                    tus.ThirdUnitId = userInfo.SchoolCode;
                    tus.RoleId = userInfo.IfAppAdmin != 0 || userInfo.IfMain != 0 ? 30 : 31;
                    var schoolInfo = await _iwxzhjyApi.GetSchoolInfo(userInfo.SchoolCode);
                    tus.UnitName = schoolInfo.Data.Name;
                    tus.ParentUnitId = schoolInfo.Data.OrganCode;
                    if (schoolInfo.Data.OrganCode == "G32024512")
                    {
                        tus.ParentUnitId = "SSG32024512";
                    }
                }
                if (!string.IsNullOrEmpty(userInfo.OrganCode))
                {
                    tus.UnitType = UnitTypeEnum.County;
                    tus.UnitCode = userInfo.OrganCode;
                    tus.ThirdUnitId = userInfo.OrganCode;
                    tus.RoleId = userInfo.IfAppAdmin != 0 || userInfo.IfMain != 0 ? 20 : 21;
                    var organInfo = await _iwxzhjyApi.GetOrganInfo(userInfo.OrganCode);
                    tus.ParentUnitId = "G32025288";
                    tus.UnitName = organInfo.Data.Name;

                    // 装备中心账号，市级账号都作为装备账号
                    if (tus.ThirdUnitId == "G32024512" || tus.ThirdUnitId == "G32025288")
                    {
                        tus.IsMultUnit = true;

                        var countryUser = JsonConvert.DeserializeObject<UserThirdAuth>(JsonConvert.SerializeObject(tus));
                        countryUser.UnitCode = "SSG32024512";
                        countryUser.ThirdUnitId = "SSG32024512";
                        countryUser.ParentUnitId = "G32025288";
                        countryUser.UnitName = "无锡市市属学校";
                        await userThirdAuthBLL.SaveForm(countryUser);

                        tus.UnitType = UnitTypeEnum.City;
                        tus.ThirdUnitId = "G32025288";
                        tus.UnitCode = "G32025288";
                        tus.UnitName = "无锡市教育信息化和装备管理服务中心";
                        tus.ParentUnitId = "";
                        if (tus.RoleId == 20)
                        {
                            tus.RoleId = 10;
                        }
                    }
                }

                var userObj = await userThirdAuthBLL.SaveForm(tus);

                var user = await userBLL.GetEntity(userObj.Data.Id.Value);

                var loginData = await LoginAuto(user.Data.UserName, user.Data.Password, 1);
                if (loginData.Tag == 1)
                {
                    return Redirect("~/");
                }
                else
                {
                    throw new BusinessException(loginData.Message);
                }
            }
            catch (ApiException ex)
            {
                LogHelper.Error($"---->从【无锡市智慧教育】获取数据失败，状态码{ex.StatusCode}，失败请求为:{ex.Uri}，{ex.StackTrace}", null);
                throw new BusinessException($"从【无锡市智慧教育】获取数据失败。" + ex.Message);
            }
            catch (Exception ex)
            {
                LogHelper.Error($"---->从【无锡市智慧教育】SSO失败，错误信息为:{ex.Message} {ex.StackTrace}", null);
                throw new BusinessException($"从【无锡市智慧教育】SSO失败。" + ex.Message);
            }
        }

        [HttpPost]
        public async Task<TData> InitWxszhjyOrgan()
        {
            var res = new TData();
            try
            {
                var _iwxzhjyApi = await GetWxszhjyClientApiService();

                var organListResponse = await _iwxzhjyApi.GetOrganList();

                var organList = organListResponse.Data.Organs;

                var cityOrgan = organList.First(o => string.IsNullOrEmpty(o.PCode));

                UnitInputModel pUnit = new UnitInputModel
                {
                    Name = cityOrgan.Name,
                    ThirdUnitId = cityOrgan.Code,
                    UnitType = (int)UnitTypeEnum.City,
                    Code = cityOrgan.Code,
                    Statuz = StatusEnum.Yes.ParseToInt(),
                };

                // 保存无锡市教育局
                TData<string> pUnitEntity = await unitBLL.WxsSaveForm(pUnit);
                organList.Remove(cityOrgan);

                // 保存区县教育局
                foreach (var organ in organList)
                {
                    UnitInputModel unit = new UnitInputModel
                    {
                        Name = organ.Name,
                        // 设置上级单位ID为无锡市教育局
                        PId = pUnitEntity.Data.ParseToLong(),
                        ThirdUnitId = organ.Code,
                        UnitType = (int)UnitTypeEnum.County,
                        Code = organ.Code,
                        Statuz = StatusEnum.Yes.ParseToInt(),
                    };
                    TData<string> unitObj = await unitBLL.WxsSaveForm(unit);
                }

                await unitBLL.WxsSaveForm(new UnitInputModel()
                {
                    Name = "无锡市市属学校",
                    PId = pUnitEntity.Data.ParseToLong(),
                    ThirdUnitId = "SS" + pUnit.Code,
                    UnitType = (int)UnitTypeEnum.County,
                    Code = "SS" + pUnit.Code,
                    Statuz = StatusEnum.Yes.ParseToInt(),
                });
                res.Tag = 1;
                res.Message = "同步成功";
                return res;
            }
            catch (Exception ex)
            {
                res.Tag = 0;
                res.Message = "同步失败";
                LogHelper.Info($"获取组织机构数据异常：{ex.Message}", null);
                return res;
            }
        }

        [HttpPost]
        public async Task<TData> InitWxszhjySchools()
        {
            var res = new TData();
            try
            {
                var _iwxzhjyApi = await GetWxszhjyClientApiService();

                // 请求获取所有学校
                var schoolListResponse = await _iwxzhjyApi.GetSchoolList();

                var schoolList = schoolListResponse.Data.Schools;

                // 获取库中存在的区县单位
                var organList = await unitBLL.GetUnitList(new UnitListParam() { UnitType = (int)UnitTypeEnum.County });

                foreach (var school in schoolList)
                {
                    // 获取学校的上级单位本系统内的UnitId
                    var schoolOrganUnitId = organList.Data.First(o => o.Code == school.OrganCode || o.Code == "SS" + school.OrganCode).Id;

                    UnitInputModel unit = new UnitInputModel
                    {
                        Name = school.Name,
                        PId = schoolOrganUnitId,
                        ThirdUnitId = school.Code,
                        UnitType = (int)UnitTypeEnum.School,
                        Code = school.Code,
                        Statuz = StatusEnum.Yes.ParseToInt(),
                        SchoolProp = school.FirstLevel,
                        SchoolNature = school.Nature
                    };
                    TData<string> unitObj = await unitBLL.WxsSaveForm(unit);
                }

                res.Tag = 1;
                res.Message = "同步成功";
                return res;
            }
            catch (Exception ex)
            {
                res.Tag = 0;
                res.Message = "同步失败";
                LogHelper.Info($"获取组织机构数据异常：{ex.Message}", null);
                return res;
            }
        }

        private async Task<IWxzhjyApiServers> GetWxszhjyClientApiService()
        {
            TData<List<AppManageEntity>> obj = await appManagerBLL.GetList(new AppManageListParam() { AppCode = "NTJidUZpdzBaMzZzSHlFdw", AppType = 1, IsMain = 2 });
            AppManageEntity objEntity = obj.Data.FirstOrDefault();

            using var httpClient = new HttpClient(new HttpClientBusinessHandler("获取Token失败"));
            httpClient.BaseAddress = new Uri(objEntity.AuthHost);

            var tokenQueryParams = new Dictionary<string, string>
                {
                    { "grant_type", "client_credentials" },
                };

            var requestTokenUri = $"/api/oauth/token";

            var content = new FormUrlEncodedContent(tokenQueryParams);

            var credentials = $"{objEntity.ClientId}:{objEntity.ClientSecret}";

            var base64Credentials = Convert.ToBase64String(Encoding.ASCII.GetBytes(credentials));
            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", base64Credentials);
            var response = await httpClient.PostAsync(requestTokenUri, content);
            var tokenResponseJson = await response.Content.ReadAsStringAsync();

            var tokenResponse = JsonConvert.DeserializeObject<AuthTokenResponse>(tokenResponseJson);

            CacheFactory.Cache.SetCache($"client_token", tokenResponse.AccessToken, DateTime.Now.AddMinutes(5));

            var _iwxzhjyApi = RestService.For<IWxzhjyApiServers>(new HttpClient(new WxzhjyApiHandler(_httpContextAccessor))
            {
                BaseAddress = new Uri(objEntity.AuthHost)
            }, new RefitSettings
            {
                ContentSerializer = new NewtonsoftJsonContentSerializer()
            });

            return _iwxzhjyApi;
        }

        private async Task<IWxzhjyApiServers> GetWxszhjyAuthCodeApiService(string code, AppManageEntity objEntity)
        {
            if (objEntity == null)
            {
                TData<List<AppManageEntity>> obj = await appManagerBLL.GetList(new AppManageListParam() { AppCode = "NTJidUZpdzBaMzZzSHlFdw", AppType = 1, IsMain = 2 });
                if (obj.Total == 0)
                {
                    throw new BusinessException("请联系运维人员在【第三方应用管理】->【第三方应用列表】中配置相关内容。");
                }
                objEntity = obj.Data.FirstOrDefault();
            }

            var tokenQueryParams = new Dictionary<string, string>
                {
                    { "grant_type", "authorization_code" },
                    { "code",code },
                    { "redirect_uri", objEntity.CallBackUrl },
                };

            LogHelper.Info($"---->获取token请求参数信息为：{JsonConvert.SerializeObject(tokenQueryParams)}");

            var requestTokenUri = $"/api/oauth/token";
            var content = new FormUrlEncodedContent(tokenQueryParams);

            using var httpClient = new HttpClient(new HttpClientBusinessHandler("获取token失败。"));
            httpClient.BaseAddress = new Uri(objEntity.AuthHost);
            var credentials = $"{objEntity.ClientId}:{objEntity.ClientSecret}";
            var base64Credentials = Convert.ToBase64String(Encoding.ASCII.GetBytes(credentials));
            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", base64Credentials);
            var response = await httpClient.PostAsync(requestTokenUri, content);
            var tokenResponseJson = await response.Content.ReadAsStringAsync();
            var tokenResponse = JsonConvert.DeserializeObject<AuthTokenResponse>(tokenResponseJson);

            CacheFactory.Cache.SetCache($"{code}_token", tokenResponse.AccessToken, DateTime.Now.AddMinutes(5));
            SetSession("wxs_third_sysendmsg_token", tokenResponse.AccessToken);//无锡市统一身份认证，实验预约后给实验员推送消息token
            var _iwxzhjyApi = RestService.For<IWxzhjyApiServers>(new HttpClient(new WxzhjyApiHandler(_httpContextAccessor))
            {
                BaseAddress = new Uri(objEntity.AuthHost)
            }, new RefitSettings
            {
                ContentSerializer = new NewtonsoftJsonContentSerializer()
            });

            return _iwxzhjyApi;
        }

        #endregion

        /// <summary>
        /// 设置Session
        /// </summary>
        /// <param name="key">键</param>
        /// <param name="value">值</param>
        protected void SetSession(string key, string value)
        {
            HttpContext.Session.SetString(key, value);
        }

        /// <summary>
        /// 获取Session
        /// </summary>
        /// <param name="key">键</param>
        /// <returns>返回对应的值</returns>
        protected string GetSession(string key)
        {
            var value = HttpContext.Session.GetString(key);
            if (string.IsNullOrEmpty(value))
                value = string.Empty;
            return value;
        }

        #region 钉钉免登开始

        /// <summary>
        /// 钉钉跳转页面
        /// </summary>
        /// <returns></returns>
        public IActionResult AuthDd()
        {
            return View();
        }

        /// <summary>
        /// 钉钉免登
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> AuthDdLogin(string code)
        {
            //参考：https://open.dingtalk.com/document/orgapp/enterprise-internal-application-logon-free

            LogHelper.Info($"钉钉code:{code}");

            var ThirdSSO = Configuration.GetSection("sso").Get<ThirdSSO>();
            LogHelper.Info($"钉钉ThirdSSO:{JsonConvert.SerializeObject(ThirdSSO)}");

            DdTokenRequest accessTokenRequest = new DdTokenRequest
            {
                appKey = ThirdSSO.clientID,
                appSecret = ThirdSSO.clientSecret,
            };
            try
            {
                //获取企业内部应用的accessToken,参考：https://open.dingtalk.com/document/orgapp/obtain-the-access_token-of-an-internal-app
                var ddToken = await SendHelper.SendPostAsync<DdToken>($"{ThirdSSO.authHost}/v1.0/oauth2/accessToken", JsonConvert.SerializeObject(accessTokenRequest));

                LogHelper.Info($"钉钉获取Token:{JsonConvert.SerializeObject(ddToken)}");
                string accessToken = ddToken.accessToken;

                //https://open.dingtalk.com/document/orgapp/obtain-the-userid-of-a-user-by-using-the-log-free
                var ssoUserInfoRequest = new DdSsoUserInfoRequest
                {
                    code = code
                };
                var userInfo = await SendHelper.SendPostAsync<DdUserGetUserInfoByCodeResponse>($"{ThirdSSO.dataHost}/topapi/v2/user/getuserinfo?access_token={accessToken}", JsonConvert.SerializeObject(ssoUserInfoRequest)); ;
                LogHelper.Info($"钉钉获取Token:{JsonConvert.SerializeObject(userInfo)}");

                if (userInfo.errcode != 0)
                {
                    ViewBag.Msg = userInfo.errmsg;
                    return View();
                }

                //查询用户详情，https://oapi.dingtalk.com/topapi/v2/user/get?access_token= 需“成员信息读权限”
                //因为个人信息里面有名字，所以此处只获取手机号码 需“企业员工手机号信息” https://open.dingtalk.com/document/orgapp/query-user-details
                //https://oapi.dingtalk.com/topapi/v2/user/get?access_token=7dd01a7018253024ac884776eb7d8db2

                var userDetail = await SendHelper.SendPostAsync<DdUserDetail>($"{ThirdSSO.dataHost}/topapi/v2/user/get?access_token={accessToken}", JsonConvert.SerializeObject(new { userid = userInfo.result.userid })); ;
                LogHelper.Info($"钉钉获取userInfo:{JsonConvert.SerializeObject(userDetail)}");
                if (userDetail.errcode != 0)
                {
                    ViewBag.Msg = userDetail.errmsg;
                    return View();
                }
                var mobile = userDetail.result.mobile;
                var name = userDetail.result.name;
                //根据手机号码匹配账号并登录
                var userResult = await userBLL.GetEntityByMobile(mobile);
                if (userResult.Tag == 1)
                {
                    LogHelper.Info($"根据手机号码匹配账号:{JsonConvert.SerializeObject(userResult)}");

                    var loginData = await LoginAuto(userResult.Data.UserName, userResult.Data.Password);
                    if (loginData.Tag == 1)
                    {
                        return Redirect("~/");
                    }
                    else
                    {
                        ViewBag.Msg = loginData.Message;
                        return View();
                    }
                }
                else
                {
                    ViewBag.Msg = "对不起，未找到您的账号，如果您在本平台有账号，请核实您的手机号码与钉钉手机号码是否一致。一致才能正确登录。";
                    return View();
                }
            }
            catch (Exception _err)
            {
                LogHelper.Info($"钉钉免登异常:{_err.Message}");
                ViewBag.Msg = _err.Message;
            }
            return View();
        }

        #endregion 钉钉免登结束

        #region 盐城市平台 --对接华为统一身份认证

        public async Task<IActionResult> AuthYanchengshi()
        {
            string token = Request.Query["token"];
            if (string.IsNullOrEmpty(token))
            {
                ViewBag.Msg = "必须从盐政通平台进入";
                return View();
            }
            string jftoken = Request.Query["jftoken"];
            var sesssionCode = GetSession("sso_third_ycs");
            if (string.IsNullOrEmpty(sesssionCode))
            {
                if (!string.IsNullOrEmpty(token))
                    SetSession("sso_third_ycs", token);
            }
            else if (sesssionCode == token)
            {
                ViewBag.Msg = "重复请求，系统已经阻止！";
                LogHelper.Info("重复请求，系统已经阻止！");
                return View();
            }
            else //code不一致，更新
            {
                SetSession("sso_third_ycs", token);
            }
            if (string.IsNullOrEmpty(jftoken))
            {
                jftoken = "yancheng"; //需管理员在后台配置
            }

            TData<List<AppManageEntity>> appList = await appManagerBLL.GetList(new AppManageListParam() { AppKey = jftoken, AppType = 1, IsMain = 2 });
            if (appList.Total == 0)
            {
                LogHelper.Info($"请联客服在【第三方应用管理】->【第三方应用列表】中配置相关内容。", null);
                ViewBag.Msg = "请联客服在【第三方应用管理】->【第三方应用列表】中配置相关内容。";
                return View();
            }
            AppManageEntity appConfig = appList.Data.FirstOrDefault();
            var huaweiBaseConfig = new CommonLib.Huawei.BaseConfig(appConfig.ClientId, appConfig.ClientSecret);
            
            try
            {      
                var getUserUrl = $"/openapi-cgw/openapi-login/sso/getUserInfoByToken?ssoToken={token}";
              
                Dictionary<string, string> extHeaders = huaweiBaseConfig.GetHeaders(0); //对于GET请求请设置为0               
                LogHelper.Info($"---->获取用户信息URL：{getUserUrl}");
                LogHelper.Info($"Headers: {string.Join(", ", extHeaders.Select(kv => $"{kv.Key}={kv.Value}"))}");
                var getResult = await SendHelper.SendGetAsync(getUserUrl, extHeaders);
           
                if (getResult.Tag != 1)
                {                   
                    ViewBag.Msg = $"获取用户信息失败。返回值：{getResult.Message}";
                    return View();
                }
                LogHelper.Info($"---->获取用户信息：{getResult.Data}");


                var yanchengApiResponse = JsonConvert.DeserializeObject<YanchengApiResponse<YanChengUserData>>(getResult.Data);
                if (yanchengApiResponse.Success)
                {
                    var mobile = yanchengApiResponse.Data.Mobile;
                    var name = yanchengApiResponse.Data.Name;
                    //根据手机号码匹配账号并登录
                    var userResult = await userBLL.GetEntityByMobile(mobile);
                    if (userResult.Tag == 1)
                    {
                        LogHelper.Info($"根据手机号码匹配账号:{JsonConvert.SerializeObject(userResult)}");

                        var loginData = await LoginAuto(userResult.Data.UserName, userResult.Data.Password);
                        if (loginData.Tag == 1)
                        {
                            return Redirect("~/");
                        }
                        else
                        {
                            ViewBag.Msg = loginData.Message;
                            return View();
                        }
                    }
                    else
                    {
                        ViewBag.Msg = "对不起，未找到您的账号，如果您在本平台有账号，请核实您的手机号码是否一致。一致才能正确登录。";
                        return View();
                    }
                }
                else
                {
                   
                    ViewBag.Msg = $"获取用户信息失败。返回值：{yanchengApiResponse.Msg}";
                }
            }
            catch (Exception ex)
            {
                LogHelper.Info($"---->从【盐政通平台】登录失败，错误信息为:{ex.Message} {ex.StackTrace}", null);
                ViewBag.Msg = $"从【盐政通平台】获取登录失败。" + ex.Message;
                //日志写异常
                LogHelper.Error(ex);
            }
            return View();
        }


        #endregion 盐城市平台结束

        #region 东台统一身份认证

        public async Task<IActionResult> AuthDongtaiIndex()
        {
            OperatorInfo operatorInfo = await Code.Operator.Instance.Current();

            if (operatorInfo == null)
            {
                TData<List<AppManageEntity>> appList = await appManagerBLL.GetList(new AppManageListParam() { AppKey = "yanchengdongtai", AppType = 1, IsMain = 2 });
                if (appList.Total == 0)
                {
                    LogHelper.Info($"请联客服在【第三方应用管理】->【第三方应用列表】中配置相关内容。", null);
                    ViewBag.Msg = "请联客服在【第三方应用管理】->【第三方应用列表】中配置相关内容。";
                    return View();
                }
                AppManageEntity appConfig = appList.Data.FirstOrDefault();
                return Redirect(appConfig.WebHost);
            }
            else
            {
                return Redirect("~/");
            }
        }
        /// <summary>
        /// 东台统一身份认证，根据手机号码自动登录，如果有多个手机号码，则提示用户选择号码登录
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> AuthDongtai()
        {
            ViewBag.Msg = "";
            string code = Request.Query["code"];

            string jftoken = Request.Query["jftoken"];

            if (string.IsNullOrEmpty(jftoken))
            {
                jftoken = "yanchengdongtai"; //需管理员在后台配置
            }

            TData<List<AppManageEntity>> appList = await appManagerBLL.GetList(new AppManageListParam() { AppKey = jftoken, AppType = 1, IsMain = 2 });
            if (appList.Total == 0)
            {
                LogHelper.Info($"请联客服在【第三方应用管理】->【第三方应用列表】中配置相关内容。", null);
                ViewBag.Msg = "请联客服在【第三方应用管理】->【第三方应用列表】中配置相关内容。";
                return View();
            }
            AppManageEntity appConfig = appList.Data.FirstOrDefault();
            var redirect_uri = WebUtility.UrlEncode(appConfig.CallBackUrl);
            if (string.IsNullOrEmpty(code))
            {
                var url = $"{appConfig.AuthHost}/xcoffice/api/v1/xckj/oauth/authorize?client_id={appConfig.ClientId}&redirect_uri={appConfig.CallBackUrl}&response_type=code";
                return Redirect(url);
            }

            //appConfig.ClientId, appConfig.ClientSecret

            try
            {
                /*
                 * 使用授权码获取登录用户身份
                 * 请求方式：POST
                 * 请求地址：https://{HostName}/xcoffice/api/v1/api-oauth2/oauth/token
                 * 请求体：
                 * grant_type = authorization_code & client_id ={ CLIENT_id}
                 *  &client_secret ={ CLIENT_SECRET}
                 *   &code ={ CODE}&& redirect_uri ={ REDIRECT_URI}   
                */


                string tokenUrl = $"{appConfig.AuthHost}/xcoffice/api/v1/api-oauth2/oauth/token";
                string strData = $"grant_type=authorization_code&client_id={appConfig.ClientId}&client_secret={appConfig.ClientSecret}&code={code}&&redirect_uri={redirect_uri}";
                string jsonData = (await SendHelper.SendPostAsync(tokenUrl, strData)).Data;
                if (jsonData.Contains("access_token")) //因为成功和失败返回的数据格式不一样，所以需判断
                {
                    var userResult = JsonConvert.DeserializeObject<DongTaiAuthResponse>(jsonData);
                    
                }
                else
                {
                    LogHelper.Info($"{jsonData}。", null);
                    ViewBag.Msg = $"登录异常{jsonData}。";
                }
                
            }
            catch (Exception ex) { 
            
            }
                return View()   ;
        }
        #endregion
    }
}