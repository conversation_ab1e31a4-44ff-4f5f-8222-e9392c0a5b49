﻿using Dqy.Syjx.Business.BusinessThirdManage;
using Dqy.Syjx.Business.SystemManage;
using Dqy.Syjx.Entity;
using Dqy.Syjx.Entity.BusinessThirdManage;
using Dqy.Syjx.Model;
using Dqy.Syjx.Model.Input.BusinessThirdManage;
using Dqy.Syjx.Model.Param.BusinessThirdManage;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Web.Areas.BusinessThirdManage.Controllers.SignWxHandlers;
using Dqy.Syjx.Web.Controllers;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Org.BouncyCastle.Cms;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace Dqy.Syjx.Web.Areas.BusinessThirdManage.Controllers
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2025-05-22 10:26
    /// 描 述：无锡市签名验签控制器类
    /// </summary>
    [Area("BusinessThirdManage")]
    public class SignWxController : BaseController
    {
        private SignWxBLL signWxBLL = new SignWxBLL();
        private readonly IConfiguration Configuration;

        public SignWxController(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        #region 视图功能

        [AuthorizeFilter("businessthird:signwx:view")]
        public ActionResult SignWxIndex()
        {
            return View();
        }

        public ActionResult SignWxForm()
        {
            return View();
        }

        #endregion 视图功能

        #region 获取数据

        [HttpGet]
        [AuthorizeFilter("businessthird:signwx:search")]
        public async Task<ActionResult> GetListJson(SignWxListParam param)
        {
            TData<List<SignWxEntity>> obj = await signWxBLL.GetList(param);
            return Json(obj);
        }

        [HttpGet]
        [AuthorizeFilter("businessthird:signwx:search")]
        public async Task<ActionResult> GetPageListJson(SignWxListParam param, Pagination pagination)
        {
            TData<List<SignWxEntity>> obj = await signWxBLL.GetPageList(param, pagination);
            return Json(obj);
        }

        [HttpGet]
        public async Task<ActionResult> GetFormJson(long id)
        {
            TData<SignWxEntity> obj = await signWxBLL.GetEntity(id);
            return Json(obj);
        }

        #endregion 获取数据

        #region 提交数据

        [HttpPost]
        [AuthorizeFilter("businessthird:signwx:add,businessthird:signwx:edit")]
        public async Task<ActionResult> SaveFormJson(SignWxInputModel model)
        {
            TData<string> obj = await signWxBLL.SaveForm(model);
            return Json(obj);
        }

        [HttpPost]
        [AuthorizeFilter("businessthird:signwx:delete")]
        public async Task<ActionResult> DeleteFormJson(string ids)
        {
            TData obj = await signWxBLL.DeleteForm(ids);
            return Json(obj);
        }

        #endregion 提交数据

        [HttpPost]
        [AuthorizeFilter("businessthird:signwx:verify")]
        public async Task<ActionResult> Verify(long bizType, long id)
        {
            //await new SignWxBLL().SaveForm(new SignWxInputModel
            //{
            //    BizType = bizType,
            //    ObjId = id,
            //    InData = "message",
            //    SignValue = "MEQCIANbDmgcjXRDHzD44cNVCGtOuY1cneLKEoan9VYFtItgAiBRRGiYcT12NnoICXl6qjagN9MYEzdyef5fqnOiEtebLg=="
            //});
            //return Json(new TData { Tag = 0, Message = "验签失败" });

            try
            {
                if (bizType == 0 || id == 0)
                {
                    return Json(new TData { Tag = 0, Message = "参数错误" });
                }

                var signWxHandler = SignWxFactory.GetHandler(bizType);
                var handleResult = await signWxHandler.GetExistSignature(id);

                if (handleResult.Tag != 1)
                {
                    return Json(new TData { Tag = handleResult.Tag, Message = handleResult.Message });
                }

                (SignWxEntity signWx, string message) = handleResult.Data;
                var signature = signWx?.SignValue;
                using var httpClient = new HttpClient
                {
                    Timeout = TimeSpan.FromSeconds(30)
                };
                var baseUrl = Configuration["WxSign:Url"];
                LogHelper.Info($"baseUrl: {baseUrl}");
                // 如果没有签名，先获取签名
                if (string.IsNullOrEmpty(signature))
                {
                    signature = await GetSignature(httpClient, baseUrl, message);
                    if (string.IsNullOrEmpty(signature))
                    {
                        LogHelper.Info("请求获取签名，签名为null");
                        return Json(new TData { Tag = 0, Message = "验签失败" });
                    }
                    else
                    {
                        await new SignWxBLL().SaveForm(new SignWxInputModel
                        {                            
                            BizType = bizType,
                            ObjId = id,
                            InData = message,
                            SignValue = signature
                        });
                    }
                }

                // 验证签名
                var verifyResult = await VerifySignature(httpClient, baseUrl, signature, message);

                // 如果首次验证失败，重新获取签名并再次验证
                //if (!verifyResult)
                //{
                //    signature = await GetSignature(httpClient, baseUrl, message);
                //    if (string.IsNullOrEmpty(signature))
                //    {
                //        LogHelper.Info("重新请求获取签名，签名为null");
                //        return Json(new TData { Tag = 0, Message = "验签失败" });
                //    }
                //    else
                //    {
                //        verifyResult = true;
                //    }
                //    if (verifyResult && signWx?.Id != null)
                //    {
                //        await new SignWxBLL().SaveForm(new SignWxInputModel
                //        {
                //            Id = signWx.Id.Value,
                //            BizType = signWx.BizType,
                //            ObjId = signWx.ObjId,
                //            InData = message,
                //            SignValue = signature
                //        });
                //    }
                //}

                return Json(new TData
                {
                    Tag = verifyResult ? 1 : 0,
                    Message = verifyResult ? "验签成功" : "验签失败"
                });
            }
            catch (HttpRequestException ex)
            {
                LogHelper.Warn($"网络请求错误: {ex.Message}");
                return Json(new TData { Tag = 0, Message = $"网络请求错误" });
            }
            catch (Exception ex)
            {
                LogHelper.Warn($"验签过程发生错误: {ex.Message}");
                return Json(new TData { Tag = 0, Message = $"验签过程发生错误" });
            }
        }

        private async Task<string> GetSignature(HttpClient httpClient, string baseUrl, string message)
        {
            //return "teset";
            var signRequest = new { message = message };
            LogHelper.Info($"调用签名接口：{baseUrl}/api/Sign/signData， 数据{JsonConvert.SerializeObject(signRequest)}");
            var response = await httpClient.PostAsync(
                $"{baseUrl}/api/Sign/signData",
                new StringContent(JsonConvert.SerializeObject(signRequest), Encoding.UTF8, "application/json")
            );

            response.EnsureSuccessStatusCode();
            var responseString = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<string>(responseString);
        }

        private async Task<bool> VerifySignature(HttpClient httpClient, string baseUrl, string signature, string message)
        {
            //return true;   
            var verifyRequest = new { signature = signature, message = message };
            LogHelper.Info($"调用验签接口：{baseUrl}/api/Sign/verify， 数据{JsonConvert.SerializeObject(verifyRequest)}");
            var response = await httpClient.PostAsync(
                $"{baseUrl}/api/Sign/verify",
                new StringContent(JsonConvert.SerializeObject(verifyRequest), Encoding.UTF8, "application/json")
            );

            response.EnsureSuccessStatusCode();
            var responseString = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<bool>(responseString);
        }
    }
}