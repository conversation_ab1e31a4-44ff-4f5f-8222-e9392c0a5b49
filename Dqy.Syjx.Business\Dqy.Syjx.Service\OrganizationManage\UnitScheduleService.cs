﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.OrganizationManage;
using Dqy.Syjx.Model.Param.OrganizationManage;
using NPOI.SS.Formula.Functions;
using Castle.DynamicProxy.Generators.Emitters.SimpleAST;
using NPOI.POIFS.FileSystem;
using Dqy.Syjx.Web.Code;

namespace Dqy.Syjx.Service.OrganizationManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2023-06-09 09:22
    /// 描 述：服务类
    /// </summary>
    public class UnitScheduleService :  RepositoryFactory
    {

        /// <summary>
        /// ApiToken值（仅WebApi端传入）
        /// </summary>
        public string ApiToken = string.Empty;


        #region 获取数据
        public async Task<List<UnitScheduleEntity>> GetList(UnitScheduleListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<UnitScheduleEntity>> GetPageList(UnitScheduleListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<UnitScheduleEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<UnitScheduleEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(UnitScheduleEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(UnitScheduleEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
               strSql = $" update up_UnitSchedule set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
               strSql = $"update up_UnitSchedule set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<UnitScheduleEntity, bool>> ListFilter(UnitScheduleListParam param)
        {
            var expression = LinqExtensions.True<UnitScheduleEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (param.UnitId >= 0)
                {
                    expression = expression.And(a => a.UnitId == param.UnitId);
                }
                if (param.GradeId >= 0)
                {
                    expression = expression.And(a=>a.GradeId == param.GradeId);

                }

            }
            return expression;
        }
        #endregion
    }
}
