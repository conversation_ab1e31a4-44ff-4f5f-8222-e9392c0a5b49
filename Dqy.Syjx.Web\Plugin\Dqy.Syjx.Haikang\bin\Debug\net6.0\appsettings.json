﻿{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "AllowedHosts": "*",
  "SystemConfig": {
    "Demo": false, // 是否是演示模式
    "LoginMultiple": true, // 是否允许一个账户在多处登录
    "LoginProvider": "Cookie", // 登录信息保存方式 Cookie Session WebApi
    "SnowFlakeWorkerId": 1, // SnowFlake 节点序号
    "ApiSite": "http://localhost:8021", // Api地址，例如可以上传文件到Api
    "VirtualDirectory": "/haikangPic", // 虚拟目录 
    "WebSite": "http://localhost:8021", //网站发布地址
    "DBProvider": "SqlServer",
    "DBConnectionString": "Data Source=***********;Initial Catalog=Syjx;Persist Security Info=True;User ID=****;Password=****", //实验教学主数据库
    "DBConnectionString2": "Data Source=***********;Initial Catalog=Syjx_hk;Persist Security Info=True;User ID=****;Password=****", //海康使用数据库
    "DBCommandTimeout": 180, // 数据库超时时间，单位秒
    "DBBackup": "", // 数据库备份路径

    "CacheProvider": "Memory", // 缓存使用方式 Memory Redis
    "RedisConnectionString": "127.0.0.1:6379",
    "IP": "*", //控制台启动IP
    "Port": "5108", //控制台启动端口

    "HaiKangVideoExcludeTime": 5, //（摄像头拍摄需要剔除时间 单位：分钟）
    "HaiKangReplaceHttpUrl": "http://************:18001/", //替换后的http地址
    "HaiKangSavePath": "D:\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Web\\Dqy.Syjx.Web\\Resource\\UploadFile\\", //海康接口图片本地存放路径
    "HaiKangVirtualDirectory": "/Resource/UploadFile/", //海康图片虚拟目录 
    "HaiKangIP": "************", //海康综合安防管理平台IP地址
    "HaiKangPort": 18001, //海康综合安防管理平台端口

    "CommonLogFilePath": "D:\\SYJX\\Common\\", //普通日志写入地址
    "ErrorLogFilePath": "D:\\SYJX\\Error\\", //错误日志写入地址
    "WarnLogFilePath": "D:\\SYJX\\Warn\\" //警告日志写入地址
  }
}
