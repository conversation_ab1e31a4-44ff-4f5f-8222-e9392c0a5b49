﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.BusinessManage;
using Dqy.Syjx.Model.Param.BusinessManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Model.Param.CameraManage;
using System.Data;
using Dqy.Syjx.Service.OrganizationManage;
using Dqy.Syjx.Util.Tools;
using Dynamitey.DynamicObjects;
using Castle.DynamicProxy.Generators.Emitters.SimpleAST;

namespace Dqy.Syjx.Service.BusinessManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-11-01 09:27
    /// 描 述：基本信息管理服务类
    /// </summary>
    public class FunRoomService : RepositoryFactory
    {

        private UserSubjectSetService userSubjectSetService = new UserSubjectSetService();

        #region 获取数据

        public async Task<List<FunRoomEntity>> GetList()
        {
            var expression = LinqExtensions.True<FunRoomEntity>();
            var list = await this.BaseRepository().FindList(expression.And(f => f.BaseIsDelete == 0 && f.RoomAttribute == 1009001));
            return list.ToList();
        }

        public async Task<List<FunRoomEntity>> GetList(FunRoomListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<FunRoomEntity>> GetUserList(FunRoomListParam param)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ListFilter(param, strSql);
            var list = await this.BaseRepository().FindList<FunRoomEntity>(strSql.ToString(), filter.ToArray());
            if (list!=null)
            {
                list.ForEach(m => m.RoomNatureName = $"{m.Name}({m.NatureName})");

            }
            return list.ToList();
        }
        /// <summary>
        /// 获取学校端实验（专用）室统计数据
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<List<FunRoomEntity>> GetStatisticsList(FunRoomListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = StatisticsListFilter(param, strSql);
            var list = await this.BaseRepository().FindList<FunRoomEntity>(strSql.ToString(), filter.ToArray(), pagination);
            return list.ToList();
        }

        /// <summary>
        /// 获取区县/市级端实验（专用）室统计数据
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<List<FunRoomEntity>> GetCountyStatisticsList(FunRoomListParam param, Pagination pagination, string selectSql, string groupSql)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = StatisticsListFilter(param, strSql, " * ", selectSql, groupSql);
            var list = await this.BaseRepository().FindList<FunRoomEntity>(strSql.ToString(), filter.ToArray(), pagination);
            return list.ToList();
        }

        /// <summary>
        /// 获取实验（专用）室统计页面的总计数量、使用面积
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<FunRoomEntity> GetTotalSum(FunRoomListParam param,string selectSql,string groupSql)
        {
            FunRoomEntity funRoom = new FunRoomEntity();
            StringBuilder sql = new StringBuilder();
            var expression = StatisticsListFilter(param, sql, " ISNULL(SUM(FunRoomCount),0) AS FunRoomCount ,ISNULL(SUM(UseAreaTotal),0) AS UseAreaTotal ", selectSql, groupSql);
            DataTable dt = await this.BaseRepository().FindTable(sql.ToString(), expression.ToArray());

            if (dt != null && dt.Rows.Count > 0)
            {
                funRoom.RowType = -1;
                funRoom.FunRoomCount = dt.Rows[0]["FunRoomCount"].ParseToInt();
                funRoom.UseAreaTotal = dt.Rows[0]["UseAreaTotal"].ParseToDecimal();
            }
            return funRoom;
        }

        public async Task<List<FunRoomEntity>> GetPageList(FunRoomListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ListFilter(param, strSql);
            var list = await this.BaseRepository().FindList<FunRoomEntity>(strSql.ToString(), filter.ToArray(), pagination);
            if (list != null)
            {
                list.ForEach(m => m.RoomNatureName = $"{m.Name}({m.NatureName})");

            }
            return list.ToList();
        }

        /// <summary>
        /// 获取实验（专用）室明细页面的总计使用面积
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<FunRoomEntity> GetPageListTotalSum(FunRoomListParam param)
        {
            FunRoomEntity funRoom = new FunRoomEntity();
            StringBuilder sql = new StringBuilder();
            var expression = ListFilter(param, sql, " ISNULL(SUM(UseArea),0) AS SumUseArea ");
            DataTable dt = await this.BaseRepository().FindTable(sql.ToString(), expression.ToArray());

            if (dt != null && dt.Rows.Count > 0)
            {
                funRoom.RowType = -1;
                funRoom.UseArea = dt.Rows[0]["SumUseArea"].ParseToInt();
            }
            return funRoom;
        }

        public async Task<List<FunRoomEntity>> GetCountyPageList(FunRoomListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            StringBuilder strTotalSql = new StringBuilder();
            List<DbParameter> filter = ListCountyFilter(param, strSql, strTotalSql);
            var list = await this.BaseRepository().FindList<FunRoomEntity>(strSql.ToString(), filter.ToArray(), pagination);
            var data = list.ToList();
            if (list != null && list.Count() > 0)
            {
                var dt = await this.BaseRepository().FindTable(strTotalSql.ToString(), filter.ToArray());
                if (dt != null && dt.Rows.Count > 0)
                {
                    int FunRoomNum = 0;
                    int UseArea = 0;
                    int.TryParse(dt.Rows[0]["FunRoomNum"].ToString(), out FunRoomNum);
                    int.TryParse(dt.Rows[0]["UseArea"].ToString(), out UseArea);
                    data.Add(new FunRoomEntity() { FunRoomNum = FunRoomNum, UseArea = UseArea });
                }
            }
            else
            {
                data.Add(new FunRoomEntity() { FunRoomNum = 0, UseArea = 0 });
            }
            return data;
        }
        public async Task<FunRoomEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<FunRoomEntity>(id);
        }

        /// <summary>
        /// 查询单条数据
        /// </summary>
        /// <param name="id">Id</param>
        /// <returns></returns>
        public async Task<FunRoomEntity> GetFunRoomSingle(long id)
        {
            StringBuilder strSql = new StringBuilder();
            var parameter = new List<DbParameter>();
            strSql.Append(@" SELECT * From (
                 SELECT fr1.Id ,
                 fr1.BaseIsDelete ,
                 fr1.BaseCreateTime ,
                 fr1.BaseModifyTime ,
                 fr1.BaseCreatorId ,
                 fr1.BaseModifierId ,
                 fr1.BaseVersion ,
                 fr1.UnitId ,
                 fr1.DictionaryId1005 ,
                 fr1.SchoolStagez ,
                 fr1.DictionaryId1006A ,
                 fr1.DictionaryId1006B ,
                 fr1.Name ,
                 fr1.UseArea ,
                 fr1.RoomAttribute ,
                 fr1.SeatNum ,
                 fr1.IsDigitalize ,
                 fr1.SysDepartmentId ,
                 fr1.SysUserId ,
                 fr1.BuildTime ,
                 fr1.ReformTime ,
                 fr1.Address ,
                 fr1.Statuz ,
                 fr1.SafeguardUserId ,
                 fr1.UploadBriefInfoNum ,
                 fr1.UploadSystemNum ,
                 fr1.LaboratoryGroupNum ,
		         sd1.DicName AS ClassNameA ,
		         sd2.DicName AS ClassNameB ,
                 sd6.DicName AS NatureName ,
		         sd3.DicName AS SubjectName,
		         d4.DepartmentName ,
		         su5.RealName
                 ,fr1.FillInStatuz
				 ,U.Name AS SchoolName
				 ,UR.UnitId AS CountyId
                 ,(A2.Name + '('+A1.Name+')') AS AddressName
		         FROM  bn_FunRoom AS fr1
		         INNER JOIN  sys_static_dictionary AS sd1 ON fr1.DictionaryId1006A = sd1.DictionaryId AND sd1.BaseIsDelete = 0
		         INNER JOIN  sys_static_dictionary AS sd2 ON fr1.DictionaryId1006B = sd2.DictionaryId AND sd2.BaseIsDelete = 0
		         INNER JOIN  sys_static_dictionary AS sd3 ON fr1.DictionaryId1005 = sd3.DictionaryId AND sd3.BaseIsDelete = 0
                 INNER JOIN  sys_static_dictionary AS sd6 ON sd2.Nature = sd6.DictionaryId AND sd6.BaseIsDelete = 0
                 left JOIN  SysDepartment AS d4 ON fr1.SysDepartmentId = d4.Id AND d4.BaseIsDelete = 0
		         left JOIN  SysUser AS su5 ON fr1.SysUserId = su5.Id AND su5.BaseIsDelete = 0
                 LEFT JOIN  up_Address AS A1 ON fr1.Address = A1.Id
				 LEFT JOIN  up_Address AS A2 ON A1.Pid = A2.Id
                 INNER JOIN  up_Unit AS U ON fr1.UnitId = U.Id AND U.BaseIsDelete = 0
				 INNER JOIN  up_UnitRelation AS UR ON U.Id = UR.ExtensionObjId AND UR.ExtensionType = 3 AND UR.BaseIsDelete = 0
            ) as tb1");
            strSql.Append($" WHERE Id = {id} AND BaseIsDelete = 0");
            var list = await this.BaseRepository().FindList<FunRoomEntity>(strSql.ToString());

            var entity = list.FirstOrDefault();
            if (entity != null)
            {
                entity.RoomNatureName = $"{entity.Name}({entity.NatureName})";
            }
            return entity;
        }
        /// <summary>
        /// 摄像头关联，实验室地点下拉列表。
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<List<FunRoomEntity>> GetListByCamera(SchoolCameraListParam param)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ListByCameraFilter(param, strSql);
            var list = await this.BaseRepository().FindList<FunRoomEntity>(strSql.ToString(), filter.ToArray());
            return list.ToList();
        }

        /// <summary>
        /// 根据二维码编号获取功能室信息
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        public async Task<FunRoomEntity> GetEntityByQrcode(string code, long unitId)
        {
            var expression = LinqExtensions.True<FunRoomEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == IsEnum.No.ParseToInt());
            expression = expression.And(t => t.UnitId == unitId);
            expression = expression.And(t => t.QRCode.Equals(code));
            var entity = await this.BaseRepository().FindEntity(expression);
            return entity;
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(FunRoomEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }
        /// <summary>
        /// 更新指定的字段信息。
        /// </summary>
        /// <param name="entity">数据实体</param>
        /// <param name="fields">需要更新的字段</param>
        /// <returns></returns>
        public async Task UpdateForm(FunRoomEntity entity, List<string> fields)
        {
            if (fields != null)
            {
                await entity.Modify();
                fields.Add("BaseModifyTime");
                fields.Add("BaseModifierId");
            }
            await this.BaseRepository().Update(entity, fields);
        }

        public async Task SaveTransForm(FunRoomEntity entity, Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update bn_FunRoom set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
                strSql = $"update bn_FunRoom set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<FunRoomEntity, bool>> ListFilter(FunRoomListParam param)
        {
            var expression = LinqExtensions.True<FunRoomEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (param.OptType==1)
                {
                    if (param.Id > 0)
                    {
                        expression = expression.And(t => t.Id != param.Id);
                    }
                }
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (param.Name.IsNotEmptyOrNull())
                {
                    expression = expression.And(t => t.Name == param.Name);
                }
                if (param.UnitId > 0)
                {
                    expression = expression.And(t => t.UnitId == param.UnitId);
                }
                if (param.SchoolStage > 0)
                {
                    expression = expression.And(t => t.SchoolStagez.Contains(param.SchoolStage.ToString()));
                }
                if (param.DictionaryId1005 > 0)
                {
                    expression = expression.And(t => t.DictionaryId1005 == param.DictionaryId1005);
                }
                if (param.DictionaryId1006B > 0)
                {
                    expression = expression.And(t => t.DictionaryId1006B == param.DictionaryId1006B);
                }
                if (param.RoomAttribute > 0)
                {
                    expression = expression.And(t => t.RoomAttribute == param.RoomAttribute);
                }
                if (param.SafeguardUserId > 0)
                {
                    expression = expression.And(t => t.SafeguardUserId == param.SafeguardUserId);
                }
                if (param.AddressId.HasValue)
                {
                    expression = expression.And(t => t.Address == param.AddressId.Value);
                }
                if(param.SysUserId > 0)
                {
                    expression = expression.And(t => t.SysUserId == param.SysUserId);
                }
            }
            return expression;
        }

        /// <summary>
        /// 实验（专用）室统计查询
        /// </summary>
        /// <param name="param"></param>
        /// <param name="strSql"></param>
        /// <param name="funcSql"></param>
        /// <returns></returns>
        private List<DbParameter> StatisticsListFilter(FunRoomListParam param, StringBuilder strSql, string funcSql = "*" , string selectSql = "", string groupSql = "")
        {
            strSql.Append($"SELECT {funcSql} FROM ( ");
            strSql.Append(@$"SELECT {selectSql} 0 AS RowType  ,DictionaryId1006A ,ClassNameA ,DictionaryId1006B ,ClassNameB ,DictionaryId1005 ,SubjectName ,RoomAttribute ,NatureName ,Nature ,SUM(UseArea) AS UseAreaTotal ,COUNT(1) AS FunRoomCount From (
                                 SELECT
                                 fr1.UnitId ,
                                 fr1.DictionaryId1005 ,
                                 fr1.DictionaryId1006A ,
                                 fr1.DictionaryId1006B ,
                                 fr1.UseArea ,
                                 fr1.RoomAttribute ,
                                 fr1.SchoolStagez ,
		                         sd1.DicName AS ClassNameA ,
		                         sd2.DicName AS ClassNameB ,
                                 sd6.DicName AS NatureName ,
		                         sd3.DicName AS SubjectName
				                 ,sd6.Nature
				                 ,U.Name AS SchoolName
				                 ,UR.UnitId AS CountyId
                                 ,fr1.BaseIsDelete AS BaseIsDelete
								 ,fr1.Statuz AS Statuz
								 ,sa.AreaName AS CountyName
								 ,ur2.UnitId AS CityId
								 ,u3.Name AS CityName
		                         FROM  bn_FunRoom AS fr1
		                         INNER JOIN  sys_static_dictionary AS sd1 ON fr1.DictionaryId1006A = sd1.DictionaryId AND sd1.BaseIsDelete = 0
		                         INNER JOIN  sys_static_dictionary AS sd2 ON fr1.DictionaryId1006B = sd2.DictionaryId AND sd2.BaseIsDelete = 0
		                         INNER JOIN  sys_static_dictionary AS sd3 ON fr1.DictionaryId1005 = sd3.DictionaryId AND sd3.BaseIsDelete = 0
                                 INNER JOIN  sys_static_dictionary AS sd6 ON sd2.Nature = sd6.DictionaryId AND sd6.BaseIsDelete = 0
                                 INNER JOIN  up_Unit AS U ON fr1.UnitId = U.Id AND U.BaseIsDelete = 0
				                 INNER JOIN  up_UnitRelation AS UR ON U.Id = UR.ExtensionObjId AND UR.ExtensionType = 3 AND UR.BaseIsDelete = 0
                                 INNER JOIN  up_Unit AS u2 ON UR.UnitId = u2.Id AND u2.BaseIsDelete = 0
								 INNER JOIN  up_UnitRelation AS ur2 ON u2.Id = ur2.ExtensionObjId AND ur2.ExtensionType = 3 AND ur2.BaseIsDelete = 0
                                 INNER JOIN  up_Unit AS u3 ON ur2.UnitId = u3.Id AND u3.BaseIsDelete = 0
                                 LEFT JOIN  SysArea AS sa ON sa.AreaCode = u2.AreaId AND sa.BaseIsDelete = 0
                            ) as tb1 WHERE 1 = 1  AND BaseIsDelete = 0 AND Statuz = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    strSql.Append(" AND UnitId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                else if (param.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    strSql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                    if (param.UnitId > 0)
                    {
                        strSql.Append(" AND UnitId = @UnitId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                    }
                }
                else if (param.UnitType == UnitTypeEnum.City.ParseToInt())
                {
                    strSql.Append(" AND CityId = @CityId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CityId", param.CityId));
                    if (param.CountyId > 0)
                    {
                        strSql.Append(" AND CountyId = @CountyId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                    }
                    if (param.UnitId > 0)
                    {
                        strSql.Append(" AND UnitId = @UnitId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                    }
                }
                if (param.SchoolStage > 0)
                {
                    strSql.Append(" AND SchoolStagez like @SchoolStage ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStage", $"%{param.SchoolStage}%"));
                }
                if (param.DictionaryId1006A > 0)
                {
                    strSql.Append(" AND DictionaryId1006A = @DictionaryId1006A ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId1006A", param.DictionaryId1006A));
                }
                if (param.DictionaryId1006B > 0)
                {
                    strSql.Append(" AND DictionaryId1006B = @DictionaryId1006B ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId1006B", param.DictionaryId1006B));
                }
                if (param.DictionaryId1005 > 0)
                {
                    strSql.Append(" AND DictionaryId1005 = @DictionaryId1005 ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId1005", param.DictionaryId1005));
                }
                if (param.RoomAttribute > 0)
                {
                    strSql.Append(" AND RoomAttribute = @RoomAttribute ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@RoomAttribute", param.RoomAttribute));
                }
                if (param.Nature > 0)
                {
                    strSql.Append(" AND Nature = @Nature ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Nature", param.Nature));
                }

                if (param.SetUserId > 0)
                {
                    //判断用户是否设置了学段、学科
                    var listStage = userSubjectSetService.GetUserSetStageList(param.UserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listStage.Count > 0)
                    {
                        string where = "";
                        int stageLength = listStage.Count;
                        int index = 1;
                        foreach (var stage in listStage)
                        {
                            where += $" SchoolStagez LIKE '%{stage.DictionaryId}%' ";
                            if (index != stageLength)
                            {
                                where += " OR ";
                            }
                            index++;
                        }
                        strSql.Append($" AND ({where})");
                    }
                    var listCourse = userSubjectSetService.GetUserSetCourseList(param.UserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listCourse.Count > 0)
                    {
                        string courseId = string.Join(",", listCourse.Select(f => f.DictionaryId));
                        strSql.Append($" AND DictionaryId1005 IN ({courseId})");
                    }
                }
            }
            strSql.Append($" GROUP BY DictionaryId1006A ,ClassNameA ,DictionaryId1006B ,ClassNameB ,RoomAttribute ,NatureName ,Nature ,DictionaryId1005 ,SubjectName {groupSql}");
            strSql.Append(" ) tb2");
            return parameter;
        }

        private List<DbParameter> ListFilter(FunRoomListParam param, StringBuilder strSql,string funcSql = "*")
        {
            strSql.Append(@$"  SELECT {funcSql} From (
                 SELECT fr1.Id ,
                 fr1.BaseIsDelete ,
                 fr1.BaseCreateTime ,
                 fr1.BaseModifyTime ,
                 fr1.BaseCreatorId ,
                 fr1.BaseModifierId ,
                 fr1.BaseVersion ,
                 fr1.UnitId ,
                 fr1.DictionaryId1005 ,
                 fr1.SchoolStagez ,
                 fr1.DictionaryId1006A ,
                 fr1.DictionaryId1006B ,
                 fr1.Name ,
                 fr1.UseArea ,
                 fr1.RoomAttribute ,
                 fr1.SeatNum ,
                 fr1.IsDigitalize ,
                 fr1.SysDepartmentId ,
                 fr1.SysUserId ,
                 fr1.BuildTime ,
                 fr1.ReformTime ,
                 fr1.Address ,
                 fr1.Statuz ,
                 fr1.SafeguardUserId ,
                 fr1.UploadBriefInfoNum ,
                 fr1.UploadSystemNum ,
                 fr1.QRCode ,
                 fr1.LaboratoryGroupNum ,
		         sd1.DicName AS ClassNameA ,
		         sd2.DicName AS ClassNameB ,
                 sd6.DicName AS NatureName ,
		         sd3.DicName AS SubjectName,
		         d4.DepartmentName ,
		         su5.RealName
                 ,sd6.Nature
                 ,fr1.FillInStatuz
				 ,U.Name AS SchoolName
                 ,U.Sort
				 ,UR.UnitId AS CountyId
                 ,u2.Name AS CountyName
                 ,ad1.Id AS AddressId
                 ,ISNULL(ad1.Name,'') AS RoomName
				 ,IsNULL(ad2.Name,'') AS HouseName
                 ,ur2.UnitId AS CityId
				 ,u3.Name AS CityName
                 ,sd3.DictionaryId AS SubjectId
		         FROM  bn_FunRoom AS fr1
		         INNER JOIN  sys_static_dictionary AS sd1 ON fr1.DictionaryId1006A = sd1.DictionaryId AND sd1.BaseIsDelete = 0
		         INNER JOIN  sys_static_dictionary AS sd2 ON fr1.DictionaryId1006B = sd2.DictionaryId AND sd2.BaseIsDelete = 0
		         INNER JOIN  sys_static_dictionary AS sd3 ON fr1.DictionaryId1005 = sd3.DictionaryId AND sd3.BaseIsDelete = 0
                 INNER JOIN  sys_static_dictionary AS sd6 ON sd2.Nature = sd6.DictionaryId AND sd6.BaseIsDelete = 0
                 INNER JOIN  up_Unit AS U ON fr1.UnitId = U.Id AND U.BaseIsDelete = 0
				 INNER JOIN  up_UnitRelation AS UR ON U.Id = UR.ExtensionObjId AND UR.ExtensionType = 3 AND UR.BaseIsDelete = 0
                 left JOIN  SysDepartment AS d4 ON fr1.SysDepartmentId = d4.Id AND d4.BaseIsDelete = 0
		         left JOIN  SysUser AS su5 ON fr1.SafeguardUserId = su5.Id AND su5.BaseIsDelete = 0
                 LEFT JOIN  up_Address AS ad1 ON fr1.Address = CONVERT(NVARCHAR(128),ad1.Id)
				 LEFT JOIN  up_Address AS ad2 ON ad2.Id = ad1.Pid
                 INNER JOIN  up_Unit AS u2 ON UR.UnitId = u2.Id AND u2.BaseIsDelete = 0
				 INNER JOIN  up_UnitRelation AS ur2 ON u2.Id = ur2.ExtensionObjId AND ur2.ExtensionType = 3 AND ur2.BaseIsDelete = 0
                 INNER JOIN  up_Unit AS u3 ON ur2.UnitId = u3.Id AND u3.BaseIsDelete = 0
            ) as tb1 WHERE   1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = @BaseIsDelete ");
                parameter.Add(DbParameterExtension.CreateDbParameter("@BaseIsDelete", param.BaseIsDelete));
                if (param.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    strSql.Append(" AND UnitId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                else if (param.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    strSql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                    if (param.UnitId > 0)
                    {
                        strSql.Append(" AND UnitId = @UnitId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                    }
                }
                else if (param.UnitType == UnitTypeEnum.City.ParseToInt())
                {
                    strSql.Append(" AND CityId = @CityId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CityId", param.CityId));
                    if (param.CountyId > 0)
                    {
                        strSql.Append(" AND CountyId = @CountyId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                    }
                    if (param.UnitId > 0)
                    {
                        strSql.Append(" AND UnitId = @UnitId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                    }
                }
                else if (param.UnitType == UnitTypeEnum.System.ParseToInt())
                {
                    if (param.UnitId > 0)
                    {
                        strSql.Append(" AND UnitId = @UnitId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                    }
                }
                if (param.Id > 0)
                {
                    strSql.Append(" AND Id = @Id ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Id", param.Id));
                }
                if (param.DictionaryId1006A > 0)
                {
                    strSql.Append(" AND DictionaryId1006A = @DictionaryId1006A ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId1006A", param.DictionaryId1006A));
                }
                if (param.DictionaryId1006B > 0)
                {
                    strSql.Append(" AND DictionaryId1006B = @DictionaryId1006B ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId1006B", param.DictionaryId1006B));
                }
                if (param.DictionaryId1005 > 0)
                {
                    strSql.Append(" AND DictionaryId1005 = @DictionaryId1005 ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId1005", param.DictionaryId1005));
                }
                if (param.RoomAttribute > 0)
                {
                    strSql.Append(" AND RoomAttribute = @RoomAttribute ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@RoomAttribute", param.RoomAttribute));
                }
                if (param.Nature > 0)
                {
                    strSql.Append(" AND Nature = @Nature ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Nature", param.Nature));
                }
                if (param.Statuz > 0)
                {
                    strSql.Append(" AND Statuz = @Statuz ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Statuz", param.Statuz));
                }
                if (param.SysDepartmentId > 0)
                {
                    strSql.Append(" AND SysDepartmentId = @SysDepartmentId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SysDepartmentId", param.SysDepartmentId));
                }
                if (param.SysUserId > 0)
                {
                    strSql.Append(" AND SafeguardUserId = @SysUserId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SysUserId", param.SysUserId));
                }
                if (!string.IsNullOrEmpty(param.Name))
                {
                    strSql.Append(" AND Name like @Name ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Name", $"%{param.Name}%"));
                }
                if (param.SchoolStage>0)
                {
                    strSql.Append(" AND SchoolStagez like @SchoolStage ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStage", $"%{param.SchoolStage}%"));
                }
                if (param.SafeguardUserId > 0)
                {
                    strSql.Append(" AND SafeguardUserId = @SafeguardUserId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SafeguardUserId", param.SafeguardUserId));
                }
                if (param.AddressId > 0)
                {
                    strSql.Append(" AND AddressId = @AddressId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@AddressId", param.AddressId));

                }
                if (param.FillInStatuz > -1)
                {
                    strSql.Append(" AND FillInStatuz = @FillInStatuz ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@FillInStatuz", param.FillInStatuz));
                }

                if (!string.IsNullOrEmpty(param.QRCode))
                {
                    strSql.Append(" AND QRCode like @QRCode ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@QRCode", $"%{param.QRCode}%"));
                }

                if (param.SetUserId > 0 && param.UnitType == UnitTypeEnum.School.ParseToInt())
                {

                    //判断用户是否设置了学段、学科
                    var listStage = userSubjectSetService.GetUserSetStageList(param.UserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listStage.Count > 0)
                    {
                        string where = "";
                        int stageLength = listStage.Count;
                        int index = 1;
                        foreach (var stage in listStage)
                        {
                            where += $" SchoolStagez LIKE '{stage.DictionaryId}' ";
                            if (index != stageLength)
                            {
                                where += " OR ";
                            }
                            index++;
                        }
                        strSql.Append($" AND ({where})");
                    }

                    var listCourse = userSubjectSetService.GetUserSetCourseList(param.UserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listCourse.Count > 0)
                    {
                        string courseId = string.Join(",", listCourse.Select(f => f.DictionaryId));
                        strSql.Append($" AND DictionaryId1005 IN ({courseId})");
                    }
                }
            }

            return parameter;
        }

        private List<DbParameter> ListCountyFilter(FunRoomListParam param, StringBuilder strSql, StringBuilder strTotalSql)
        {
            StringBuilder sql = new StringBuilder();
            sql.Append($@" SELECT
                     tb1.BaseIsDelete ,
                     tb1.UnitId ,
                     tb1.DictionaryId1005 ,
                     tb1.DictionaryId1006A ,
                     tb1.DictionaryId1006B ,
                     tb1.UseArea ,
                     tb1.RoomAttribute ,
                     tb1.FunRoomNum ,
                     tb1.Statuz ,
		             sd1.DicName AS ClassNameA ,
		             sd2.DicName AS ClassNameB ,
                     sd6.DicName AS NatureName ,
		             sd3.DicName AS SubjectName,
                     sd6.Nature ,
				     U.Name AS SchoolName ,
                     U.Sort ,
				     UR.UnitId AS CountyId
                     ,sa.AreaName AS CountyName
					 ,ur2.UnitId AS CityId
					 ,u3.Name AS CityName
		             FROM (SELECT fr1.BaseIsDelete , fr1.Statuz ,fr1.UnitId ,fr1.DictionaryId1005 ,fr1.DictionaryId1006A ,fr1.DictionaryId1006B ,fr1.RoomAttribute
						    ,COUNT(1) AS FunRoomNum ,SUM(fr1.UseArea) AS UseArea
						    FROM  bn_FunRoom AS fr1
						    GROUP BY fr1.UnitId ,fr1.BaseIsDelete ,fr1.Statuz  ,fr1.DictionaryId1005 ,fr1.DictionaryId1006A ,fr1.DictionaryId1006B ,fr1.RoomAttribute ) AS tb1
		             INNER JOIN  sys_static_dictionary AS sd1 ON tb1.DictionaryId1006A = sd1.DictionaryId AND sd1.BaseIsDelete = 0
		             INNER JOIN  sys_static_dictionary AS sd2 ON tb1.DictionaryId1006B = sd2.DictionaryId AND sd2.BaseIsDelete = 0
		             INNER JOIN  sys_static_dictionary AS sd3 ON tb1.DictionaryId1005 = sd3.DictionaryId AND sd3.BaseIsDelete = 0
                     INNER JOIN  sys_static_dictionary AS sd6 ON sd2.Nature = sd6.DictionaryId AND sd6.BaseIsDelete = 0
                     INNER JOIN  up_Unit AS U ON tb1.UnitId = U.Id AND U.BaseIsDelete = 0
				     INNER JOIN  up_UnitRelation AS UR ON U.Id = UR.ExtensionObjId AND UR.ExtensionType = 3 AND UR.BaseIsDelete = 0
                     INNER JOIN  up_Unit AS u2 ON UR.UnitId = u2.Id AND u2.BaseIsDelete = 0
					 INNER JOIN  up_UnitRelation AS ur2 ON u2.Id = ur2.ExtensionObjId AND ur2.ExtensionType = 3 AND ur2.BaseIsDelete = 0
                     INNER JOIN  up_Unit AS u3 ON ur2.UnitId = u3.Id AND u3.BaseIsDelete = 0
                     LEFT JOIN  SysArea AS sa ON sa.AreaCode = u2.AreaId AND sa.BaseIsDelete = 0
            ");
            strSql.Append($@"  SELECT * From ( {sql.ToString()} ) as tb1 WHERE   1 = 1 ");
            strTotalSql.Append($@"  SELECT Sum(FunRoomNum) AS FunRoomNum ,Sum(UseArea) AS UseArea  From ( {sql.ToString()} ) as tb1 WHERE   1 = 1 ");

            var parameter = new List<DbParameter>();
            StringBuilder sqlWhere = new StringBuilder();
            if (param != null)
            {
                sqlWhere.Append(" AND BaseIsDelete = @BaseIsDelete ");
                parameter.Add(DbParameterExtension.CreateDbParameter("@BaseIsDelete", param.BaseIsDelete));
                if (param.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    sqlWhere.Append(" AND UnitId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                else if (param.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    sqlWhere.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                    if (param.UnitId > 0)
                    {
                        sqlWhere.Append(" AND UnitId = @UnitId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                    }
                }else if (param.UnitType == UnitTypeEnum.City.ParseToInt())
                {
                    sqlWhere.Append(" AND CityId = @CityId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CityId", param.CityId));
                    if (param.CountyId > 0)
                    {
                        sqlWhere.Append(" AND CountyId = @CountyId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                    }
                    if (param.UnitId > 0)
                    {
                        sqlWhere.Append(" AND UnitId = @UnitId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                    }
                }
                if (param.DictionaryId1006A > 0)
                {
                    sqlWhere.Append(" AND DictionaryId1006A = @DictionaryId1006A ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId1006A", param.DictionaryId1006A));
                }
                if (param.DictionaryId1006B > 0)
                {
                    sqlWhere.Append(" AND DictionaryId1006B = @DictionaryId1006B ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId1006B", param.DictionaryId1006B));
                }
                if (param.DictionaryId1005 > 0)
                {
                    sqlWhere.Append(" AND DictionaryId1005 = @DictionaryId1005 ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId1005", param.DictionaryId1005));
                }
                if (param.RoomAttribute > 0)
                {
                    sqlWhere.Append(" AND RoomAttribute = @RoomAttribute ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@RoomAttribute", param.RoomAttribute));
                }
                if (param.Statuz > 0)
                {
                    sqlWhere.Append(" AND Statuz = @Statuz ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Statuz", param.Statuz));
                }
                if (param.Id > 0)
                {
                    sqlWhere.Append(" AND Id = @Id ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Id", param.Id));
                }
            }
            strSql.Append(sqlWhere.ToString());
            strTotalSql.Append(sqlWhere.ToString());
            return parameter;
        }

        private List<DbParameter> ListByCameraFilter(SchoolCameraListParam param, StringBuilder strSql)
        {
            strSql.Append($@"  SELECT * From (
                 SELECT fr1.Id ,
                  fr1.BaseIsDelete ,
                 fr1.BaseCreateTime ,
                 fr1.BaseModifyTime ,
                 fr1.BaseCreatorId ,
                 fr1.BaseModifierId ,
                 fr1.BaseVersion ,
                 fr1.UnitId ,
                 fr1.DictionaryId1005 ,
                 fr1.SchoolStagez ,
                 fr1.DictionaryId1006A ,
                 fr1.DictionaryId1006B ,
                 fr1.Name ,
                 fr1.UseArea ,
                 fr1.RoomAttribute ,
                 fr1.SeatNum ,
                 fr1.IsDigitalize ,
                 fr1.SysDepartmentId ,
                 fr1.SysUserId ,
                 fr1.BuildTime ,
                 fr1.ReformTime ,
                 fr1.Address ,
                 fr1.Statuz ,
                 fr1.SafeguardUserId ,
                 fr1.UploadBriefInfoNum ,
                 fr1.UploadSystemNum ,
                 fr1.LaboratoryGroupNum ,
                 sd6.DicName AS NatureName
                 ,sd6.Nature
                 ,fr1.FillInStatuz
				 ,U.Name AS SchoolName
                 ,ad1.Id AS AddressId
                 ,ISNULL(ad1.Name,'') AS RoomName
				 ,IsNULL(ad2.Name,'') AS HouseName
				 ,CASE WHEN ad1.Id > 0 THEN   fr1.Name + '+' + IsNULL(ad2.Name,'') + '（' + ISNULL(ad1.Name,'') + '）' ELSE  fr1.Name END AS AddressName
				 ,ISNULL(sc10.Id ,0) AS CameraId
		         FROM  bn_FunRoom AS fr1
		         INNER JOIN  sys_static_dictionary AS sd2 ON fr1.DictionaryId1006B = sd2.DictionaryId AND sd2.BaseIsDelete = 0
                 INNER JOIN  sys_static_dictionary AS sd6 ON sd2.Nature = sd6.DictionaryId AND sd6.BaseIsDelete = 0
                 INNER JOIN  up_Unit AS U ON fr1.UnitId = U.Id AND U.BaseIsDelete = 0
                 LEFT JOIN  up_Address AS ad1 ON fr1.Address = CONVERT(NVARCHAR(128),ad1.Id)
				 LEFT JOIN  up_Address AS ad2 ON ad2.Id = ad1.Pid
				 LEFT JOIN  bn_SchoolCamera AS sc10 ON fr1.Id = sc10.FunRoomId
            ) as tb1 WHERE   1 = 1 AND ( Nature = {FunNatureTypeEnum.Room.ParseToInt()} OR  Nature = {FunNatureTypeEnum.ZhuanRoom.ParseToInt()} ) AND CameraId = 0 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = @BaseIsDelete ");
                parameter.Add(DbParameterExtension.CreateDbParameter("@BaseIsDelete", param.BaseIsDelete));
                if (param.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    strSql.Append(" AND UnitId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                else if (param.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    strSql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                    if (param.UnitId > 0)
                    {
                        strSql.Append(" AND UnitId = @UnitId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                    }
                }
                else
                {
                    if (param.UnitId > 0)
                    {
                        strSql.Append(" AND UnitId = @UnitId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                    }
                }
                if (param.Nature > 0)
                {
                    strSql.Append(" AND Nature = @Nature ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Nature", param.Nature));
                }
                if (param.Statuz > 0)
                {
                    strSql.Append(" AND Statuz = @Statuz ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Statuz", param.Statuz));
                }
            }
            return parameter;
        }

        /// <summary>
        /// 实验（专用）室统计数量
        /// </summary>
        /// <param name="param"></param>
        /// <param name="strSql"></param>
        /// <param name="funcSql"></param>
        /// <returns></returns>
        public async Task<DataTable> GetStatisticsNum(FunRoomListParam param)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(@$" SELECT  CityId ,Nature ,NatureName ,Count(Id) as Num From (
                    SELECT
                    fr1.Id
                    ,fr1.SchoolStagez
                    ,fr1.UnitId
                    ,fr1.Statuz AS Statuz
                    ,UR.UnitId AS CountyId
                    ,ur2.UnitId AS CityId

                    ,sd6.Nature
                    ,sd6.DicName AS NatureName
                    FROM  bn_FunRoom AS fr1
                    INNER JOIN  sys_static_dictionary AS sd2 ON fr1.DictionaryId1006B = sd2.DictionaryId AND sd2.BaseIsDelete = 0

                    INNER JOIN  sys_static_dictionary AS sd6 ON sd2.Nature = sd6.DictionaryId AND sd6.BaseIsDelete = 0

                    INNER JOIN  up_UnitRelation AS UR ON fr1.UnitId = UR.ExtensionObjId AND UR.ExtensionType = 3 AND UR.BaseIsDelete = 0

                    INNER JOIN  up_UnitRelation AS ur2 ON UR.UnitId = ur2.ExtensionObjId AND ur2.ExtensionType = 3 AND ur2.BaseIsDelete = 0
                    WHERE fr1.BaseIsDelete = 0
                ) as tb1 WHERE 1 = 1 AND Statuz = 1  ");

            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    strSql.Append(" AND UnitId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                else if (param.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    strSql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
                else if (param.UnitType == UnitTypeEnum.City.ParseToInt())
                {
                    strSql.Append(" AND CityId = @CityId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CityId", param.CityId));
                }
                if (param.Nature > 0)
                {
                    strSql.Append(" AND Nature = @Nature ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Nature", param.Nature));
                }
                if (param.SchoolStage > 0)
                {
                    strSql.Append(" AND SchoolStagez like @SchoolStage ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStage", $"%{param.SchoolStage}%"));
                }
            }
            strSql.Append(" GROUP BY CityId  ,Nature ,NatureName ");
            return await this.BaseRepository().FindTable(strSql.ToString(), parameter.ToArray());
        }

        /// <summary>
        /// 实验（专用）室一级分类统计数量
        /// </summary>
        /// <param name="param"></param>
        /// <param name="strSql"></param>
        /// <param name="funcSql"></param>
        /// <returns></returns>
        public async Task<List<FunRoomEntity>> GetStatisticsClassifyNum(FunRoomListParam param)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(@$"  SELECT  CityId ,DicName AS ClassNameA ,Count(Id) as FunRoomNum From (
                    SELECT
                    fr1.Id
                    ,fr1.UnitId
                    ,fr1.Statuz AS Statuz
                    ,UR.UnitId AS CountyId
                    ,ur2.UnitId AS CityId
					,sd3.DicName  ,sd4.Nature
                    FROM  bn_FunRoom AS fr1
                    INNER JOIN  sys_static_dictionary AS sd2 ON fr1.DictionaryId1006B = sd2.DictionaryId AND sd2.BaseIsDelete = 0
                    INNER JOIN  sys_static_dictionary AS sd3 ON sd2.Pid = sd3.DictionaryId AND sd3.BaseIsDelete = 0
				    INNER JOIN  sys_static_dictionary AS sd4 ON sd2.Nature = sd4.DictionaryId AND sd4.BaseIsDelete = 0
                    INNER JOIN  up_UnitRelation AS UR ON fr1.UnitId = UR.ExtensionObjId AND UR.ExtensionType = 3 AND UR.BaseIsDelete = 0
                    INNER JOIN  up_UnitRelation AS ur2 ON UR.UnitId = ur2.ExtensionObjId AND ur2.ExtensionType = 3 AND ur2.BaseIsDelete = 0
                    WHERE fr1.BaseIsDelete = 0
                ) as tb1 WHERE 1 = 1 AND Statuz = 1  ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    strSql.Append(" AND UnitId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                else if (param.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    strSql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
                else if (param.UnitType == UnitTypeEnum.City.ParseToInt())
                {
                    strSql.Append(" AND CityId = @CityId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CityId", param.CityId));
                }

                if (param.SchoolStage > 0)
                {
                    strSql.Append(" AND SchoolStagez like @SchoolStage ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStage", $"%{param.SchoolStage}%"));
                }
                if (param.Nature > 0)
                {
                    strSql.Append(" AND Nature <> @Nature ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Nature", param.Nature));

                }
            }
            strSql.Append(" GROUP BY CityId ,DicName ");
            var list =  await this.BaseRepository().FindList<FunRoomEntity>(strSql.ToString(), parameter.ToArray());
            return list.ToList();
        }

        /// <summary>
        /// 实验（专用）室二级分类统计数量
        /// </summary>
        /// <param name="param"></param>
        /// <param name="strSql"></param>
        /// <param name="funcSql"></param>
        /// <returns></returns>
        public async Task<List<FunRoomEntity>> GetStatisticsClassifyTwoNum(FunRoomListParam param)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(@$"  SELECT  CityId ,DictionaryId1006B ,DicName AS ClassNameB ,Count(Id) as FunRoomNum From (
                    SELECT
                    fr1.Id
                    ,fr1.SchoolStagez
                    ,fr1.UnitId
                    ,fr1.Statuz AS Statuz
                    ,UR.UnitId AS CountyId
                    ,ur2.UnitId AS CityId
                    ,fr1.DictionaryId1006B
					,sd2.DicName
                    ,sd4.Nature
                    FROM  bn_FunRoom AS fr1
                    INNER JOIN  sys_static_dictionary AS sd2 ON fr1.DictionaryId1006B = sd2.DictionaryId AND sd2.BaseIsDelete = 0
				    INNER JOIN  sys_static_dictionary AS sd4 ON sd2.Nature = sd4.DictionaryId AND sd4.BaseIsDelete = 0
                    INNER JOIN  up_UnitRelation AS UR ON fr1.UnitId = UR.ExtensionObjId AND UR.ExtensionType = 3 AND UR.BaseIsDelete = 0
                    INNER JOIN  up_UnitRelation AS ur2 ON UR.UnitId = ur2.ExtensionObjId AND ur2.ExtensionType = 3 AND ur2.BaseIsDelete = 0
                    WHERE fr1.BaseIsDelete = 0
                ) as tb1 WHERE 1 = 1 AND Statuz = 1  ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    strSql.Append(" AND UnitId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                else if (param.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    strSql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
                else if (param.UnitType == UnitTypeEnum.City.ParseToInt())
                {
                    strSql.Append(" AND CityId = @CityId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CityId", param.CityId));
                }
                if (param.SchoolStage > 0)
                {
                    strSql.Append(" AND SchoolStagez like @SchoolStage ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStage", $"%{param.SchoolStage}%"));
                }
                if (param.Nature > 0)
                {
                    strSql.Append(" AND Nature <> @Nature ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Nature", param.Nature));

                }
            }
            strSql.Append(" GROUP BY CityId , DictionaryId1006B,DicName ");
            var list = await this.BaseRepository().FindList<FunRoomEntity>(strSql.ToString(), parameter.ToArray());
            return list.ToList();
        }

        /// <summary>
        /// 实验（专用）室统计
        /// </summary>
        /// <param name="param"></param>
        /// <param name="strSql"></param>
        /// <param name="funcSql"></param>
        /// <returns></returns>
        public async Task<IEnumerable<FunRoomEntity>> GetStatistics(FunRoomListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(@$" SELECT * From (
                    SELECT
                    fr1.*
                    ,UR.UnitId AS CountyId
                    ,ur2.UnitId AS CityId
                    ,sd6.Nature
                    ,sd6.DicName AS NatureName
                    ,se11.SchoolProp
                    FROM  bn_FunRoom AS fr1
                    INNER JOIN  sys_static_dictionary AS sd2 ON fr1.DictionaryId1006B = sd2.DictionaryId AND sd2.BaseIsDelete = 0
                    INNER JOIN  sys_static_dictionary AS sd6 ON sd2.Nature = sd6.DictionaryId AND sd6.BaseIsDelete = 0
                    INNER JOIN  up_UnitRelation AS UR ON fr1.UnitId = UR.ExtensionObjId AND UR.ExtensionType = 3 AND UR.BaseIsDelete = 0
                    INNER JOIN  up_UnitRelation AS ur2 ON UR.UnitId = ur2.ExtensionObjId AND ur2.ExtensionType = 3 AND ur2.BaseIsDelete = 0
                    INNER JOIN up_SchoolExtension AS se11 ON se11.BaseIsDelete = 0 AND se11.UnitId = fr1.UnitId
                    WHERE fr1.BaseIsDelete = 0
                ) as tb1 WHERE 1 = 1 AND Statuz = 1  ");

            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.UnitId > 0)
                {
                    strSql.Append(" AND UnitId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                if (param.CountyId > 0)
                {
                    strSql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
                if (param.CityId > 0)
                {
                    strSql.Append(" AND CityId = @CityId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CityId", param.CityId));
                }
                if (param.Nature > 0)
                {
                    strSql.Append(" AND Nature = @Nature ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Nature", param.Nature));
                }
                if (param.SchoolStage > 0)
                {
                    strSql.Append(" AND SchoolStagez like @SchoolStage ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStage", $"%{param.SchoolStage}%"));
                }
                if (param.SchoolProple != -10000)
                {
                    strSql.Append(" AND SchoolProp <> @SchoolProple ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolProple", param.SchoolProple));
                }
                if (param.DictionaryId1006Bs != null && param.DictionaryId1006Bs.Length > 0)
                {
                    var idArr = param.DictionaryId1006Bs.Split(",");
                    strSql.Append($" AND ({string.Join(" OR ",idArr.Select(n=>string.Format(" DictionaryId1006B = {0} ", n)))})");
                }
            }
            if (pagination != null)
            {
                return await this.BaseRepository().FindList<FunRoomEntity>(strSql.ToString(), parameter.ToArray(), pagination);
            }
            return await this.BaseRepository().FindList<FunRoomEntity>(strSql.ToString(), parameter.ToArray());
        }

        /// <summary>
        /// 实验（专用）室统计
        /// </summary>
        /// <param name="param"></param>
        /// <param name="strSql"></param>
        /// <param name="funcSql"></param>
        /// <returns></returns>
        public async Task<IEnumerable<FunRoomEntity>> GetZbReportStatistics(FunRoomListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(@$" SELECT * From (
                    SELECT
                    fr1.*
                    ,UR.UnitId AS CountyId
                    ,ur2.UnitId AS CityId

                    ,sd6.Nature
                    ,sd6.DicName AS NatureName
                    ,se11.SchoolProp
                    FROM  bn_FunRoom AS fr1
                    INNER JOIN  sys_static_dictionary AS sd2 ON fr1.DictionaryId1006B = sd2.DictionaryId AND sd2.BaseIsDelete = 0

                    INNER JOIN  sys_static_dictionary AS sd6 ON sd2.Nature = sd6.DictionaryId AND sd6.BaseIsDelete = 0

                    INNER JOIN  up_UnitRelation AS UR ON fr1.UnitId = UR.ExtensionObjId AND UR.ExtensionType = 3 AND UR.BaseIsDelete = 0

                    INNER JOIN  up_UnitRelation AS ur2 ON UR.UnitId = ur2.ExtensionObjId AND ur2.ExtensionType = 3 AND ur2.BaseIsDelete = 0
                    INNER JOIN up_SchoolExtension AS se11 ON se11.BaseIsDelete = 0 AND se11.UnitId = fr1.UnitId
                    INNER JOIN zb_Report AS rp ON rp.BaseIsDelete = 0 AND rp.IsReport = 1 AND rp.IsCurrent = 1 AND fr1.UnitId = rp.SchoolId
                    INNER JOIN zb_ReportDetail AS rd ON rd.BaseIsDelete = 0 AND rp.Id = rd.ReportId
                    WHERE fr1.BaseIsDelete = 0
                ) as tb1 WHERE 1 = 1 AND Statuz = 1  ");

            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.UnitId > 0)
                {
                    strSql.Append(" AND UnitId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                if (param.CountyId > 0)
                {
                    strSql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
                if (param.CityId > 0)
                {
                    strSql.Append(" AND CityId = @CityId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CityId", param.CityId));
                }
                if (param.Nature > 0)
                {
                    strSql.Append(" AND Nature = @Nature ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Nature", param.Nature));
                }
                if (param.SchoolStage > 0)
                {
                    strSql.Append(" AND SchoolStagez like @SchoolStage ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStage", $"%{param.SchoolStage}%"));
                }
                if (param.SchoolProple != -10000)
                {
                    strSql.Append(" AND SchoolProp <> @SchoolProple ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolProple", param.SchoolProple));
                }
                if (param.DictionaryId1006Bs != null && param.DictionaryId1006Bs.Length > 0)
                {
                    var idArr = param.DictionaryId1006Bs.Split(",");
                    strSql.Append($" AND ({string.Join(" OR ", idArr.Select(n => string.Format(" DictionaryId1006B = {0} ", n)))})");
                }
            }
            return await this.BaseRepository().FindList<FunRoomEntity>(strSql.ToString(), parameter.ToArray(), pagination);
        }

        /// <summary>
        /// 获取实验室最大编码
        /// </summary>
        /// <returns></returns>
        public async Task<string> GetMaxQRCode()
        {
            string maxQrCode = "0";
            string strSql = " SELECT ISNULL(MAX(Right('0000000000'+cast(QRCode as nvarchar(50)),10)) ,0) AS QRCode FROM  bn_FunRoom ";
            var tb = await this.BaseRepository().FindTable(strSql);
            if (tb != null && tb.Rows != null && tb.Rows.Count > 0)
            {
                maxQrCode = tb.Rows[0][0].ToString();
            }
            return maxQrCode;
        }
        #endregion
    }
}
