﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.ArticleManager;
using Dqy.Syjx.Model.Param.ArticleManager;

namespace Dqy.Syjx.Service.ArticleManager
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2022-01-21 10:43
    /// 描 述：服务类
    /// </summary>
    public class ArticleCategoryService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<ArticleCategoryEntity>> GetList(ArticleCategoryListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<ArticleCategoryEntity>> GetPageList(ArticleCategoryListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<ArticleCategoryEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<ArticleCategoryEntity>(id);
        }

        /// <summary>
        /// 获取首页前3条资讯分类数据
        /// </summary>
        /// <param name="top">搜索前几条</param>
        /// <returns></returns>
        public async Task<List<ArticleCategoryEntity>> GetIndexCategoryList(int top)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append($"SELECT TOP {top} Id,Name,Sort FROM  d_ArticleCategory WHERE BaseIsDelete = 0 AND Pid = 0 AND CateType =  1 ORDER BY Sort ASC");
            var list = await this.BaseRepository().FindList<ArticleCategoryEntity>(strSql.ToString());
            return list.ToList();
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(ArticleCategoryEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(ArticleCategoryEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update d_ArticleCategory set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
                strSql = $"update d_ArticleCategory set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<ArticleCategoryEntity, bool>> ListFilter(ArticleCategoryListParam param)
        {
            var expression = LinqExtensions.True<ArticleCategoryEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if(param.UnitId != 100000000000000001)
                {
                    expression = expression.And(t => t.UnitId == param.UnitId);
                }
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }

                if (param.Pid != null)
                {
                    expression = expression.And(t => t.Pid == param.Pid);
                }
                if (!string.IsNullOrEmpty(param.Name))
                {
                    expression = expression.And(t => t.Name.Contains(param.Name));
                }
            }
            return expression;
        }
        #endregion
    }
}
