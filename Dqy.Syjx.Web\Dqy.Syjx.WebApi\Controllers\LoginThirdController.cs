﻿using Dqy.Syjx.Business.OrganizationManage;
using Dqy.Syjx.Business.SystemManage;
using Dqy.Syjx.Business.WxManage;
using Dqy.Syjx.Cache.Factory;
using Dqy.Syjx.Entity.OrganizationManage;
using Dqy.Syjx.Entity.SystemManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Model.Input;
using Dqy.Syjx.Model.Param.OrganizationManage;
using Dqy.Syjx.Model.Param.SystemManage;
using Dqy.Syjx.Model.Result;
using Dqy.Syjx.Service.HttpService;
using Dqy.Syjx.Service.HttpService.Wxzhjy;
using Dqy.Syjx.Service.HttpService.Wxzhjy.Models;
using Dqy.Syjx.Service.OrganizationManage;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Util.ThirdOAuth;
using Dqy.Syjx.Util.Tools;
using Dqy.Syjx.Web.Code;
using Dqy.Syjx.WebApi.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Rewrite;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using NPOI.SS.Formula.Functions;
using Refit;
using Senparc.NeuChar.App.AppStore;
using Senparc.Weixin;
using Senparc.Weixin.WxOpen.AdvancedAPIs.Sns;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core.Tokenizer;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Xml;
using static Dqy.Syjx.Util.ThirdOAuth.TztxSoftAcore;
using static System.Net.WebRequestMethods;
using Config = Senparc.Weixin.Config;

namespace Dqy.Syjx.WebApi.Controllers
{
    [Route("api/loginthird")]
    [ApiController]
    [AuthorizeFilter]
    public class LoginThirdController : ControllerBase
    {
        private UserThirdAuthBLL userThirdAuthBLL = new UserThirdAuthBLL();
        private UserBLL userBLL = new UserBLL();

        //private LogLoginBLL logLoginBLL = new LogLoginBLL();
        private UserBindOpenidBLL userBindOpenidBLL = new UserBindOpenidBLL();

        //private UnitBLL unitBLL = new UnitBLL();
        private AppManageBLL appManagerBLL = new AppManageBLL();

        public IConfiguration Configuration { get; }
        private readonly IHttpClientFactory _httpClientfactory;
        private readonly NetHelper _netHelper;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public LoginThirdController(IConfiguration configuration, IHttpClientFactory httpClientfactory, NetHelper netHelper, IHttpContextAccessor httpContextAccessor)
        {
            Configuration = configuration;
            _httpClientfactory = httpClientfactory;
            _netHelper = netHelper;
            _httpContextAccessor = httpContextAccessor;
        }

        /// <summary>
        /// 用户登录
        /// </summary>
        /// <param name="code">code</param>
        /// <returns></returns>
        [Route("auth_chqh5")]
        [HttpPost]
        public async Task<TData<OperatorInfo>> Auth_chq_h5(string code)
        {
            TData<OperatorInfo> obj = new TData<OperatorInfo>();
            try
            {
                string strUserId = "";
                LogHelper.Info($"SSO:ticket:{code}");

                var ThirdSSO = Configuration.GetSection("sso").Get<ThirdSSO>();

                TzSoftAcore.SetConfig(ThirdSSO);
                if (!string.IsNullOrEmpty(code)) //如果有code，获取用户Id
                {
                    var accesstokenModel = TzSoftAcore.GetAccesstoken();
                    LogHelper.Info($"SSO:accesstokenModel:{JsonConvert.SerializeObject(accesstokenModel)}");

                    if (accesstokenModel.retCode != "000000")
                    {
                        obj.Tag = 0;
                        obj.Message = accesstokenModel.retDesc;
                        return obj;
                    }
                    var ticketModel = TzSoftAcore.ValidaTicket(accesstokenModel.tokenInfo.accessToken, code);
                    LogHelper.Info($"SSO:ticketModel:{JsonConvert.SerializeObject(ticketModel)}");

                    if (ticketModel.code != "000000")
                    {
                        obj.Tag = 0;
                        obj.Message = JsonConvert.SerializeObject(ticketModel) + " 解决方案：请关闭当前页面，重新打开";
                        return obj;
                    }

                    //天俞角色类型，2022-11-11
                    //0学生
                    //1教师
                    //3学校工作人员
                    //4机构行政人员
                    string userType = ticketModel.result.last_user_type;
                    if (!userType.Contains('1', StringComparison.Ordinal) && !userType.Contains('3', StringComparison.Ordinal) && !userType.Contains('4', StringComparison.Ordinal)
                        && !userType.Contains('5', StringComparison.Ordinal) && !userType.Contains('6', StringComparison.Ordinal))
                    {
                        obj.Tag = 0;
                        obj.Message = $"您没有权限使用平台，用户类型为【{userType}】;用户类型0：学生1:老师2:家长3:机构4:学校5:学校工作人员6:机构工作人员";
                        return obj;
                    }

                    var user = ticketModel.result;
                    //if (user == null)
                    //{
                    //    LogHelper.Info($"SSO:获取用户信息失败:{0}", JsonConvert.SerializeObject(userInfoModel)));
                    //    ViewBag.Msg = "获取用户信息失败，请确认是否登录超时。";
                    //    return View();
                    //}

                    var schoolInfoModel = TzSoftAcore.GetSchoolInfo(accesstokenModel.tokenInfo.accessToken, user.last_top_org_id);
                    LogHelper.Info($"SSO:SchoolInfoModel:{JsonConvert.SerializeObject(schoolInfoModel)}");

                    if (schoolInfoModel.code != "000000")
                    {
                        obj.Tag = 0;
                        obj.Message = schoolInfoModel.message;
                        return obj;
                    }
                    var unit = schoolInfoModel.result;
                    if (unit == null)
                    {
                        LogHelper.Info($"SSO:获取单位信息失败:{JsonConvert.SerializeObject(schoolInfoModel)}");
                        obj.Tag = 0;
                        obj.Message = "获取单位信息失败，请确认是否登录超时。";
                        return obj;
                    }
                    ////获取手机号码
                    //try
                    //{
                    //    string strMobileInfo = TzSoftAcore.GetMobileInfo(accesstokenModel.tokenInfo.accessToken, user.personId);
                    //    if (!string.IsNullOrEmpty(strMobileInfo))
                    //    {
                    //        LogHelper.Info($"SSO:获取手机号码:{0}", strMobileInfo));

                    //        var objMobile = JsonConvert.DeserializeObject<TzSoftAcore.MobileInfo>(strMobileInfo);
                    //        if (objMobile != null && objMobile.code == "000000")
                    //        {
                    //            //网关提供固定解密秘钥，需要按照下文算法生成动态秘钥解密数据。
                    //            //动态秘钥是 MD5(appId+secret + yyyyMMdd)其中 MD5加密方式是16位小写，secret为固定秘钥，yyyyMMdd为当天时间：如20190808
                    //            string dyKey = Extention.ToMD5String16(ThirdSSO.clientID + ThirdSSO.key + DateTime.Now.ToString("yyyyMMdd")).ToLower();

                    //            LogHelper.Info($"SSO:动态秘钥:{0}", dyKey));

                    //            user.phoneNumbers = SecurityHelper.AESDecodeHex(objMobile.result, dyKey);

                    //        }
                    //    }
                    //}
                    //catch (Exception expM)
                    //{
                    //    LogHelper.Info($"SSO:获取手机号码失败:{0}", expM.Message));
                    //}

                    strUserId = user.user_id;
                    UserThirdAuth tus = new UserThirdAuth();

                    tus.ThirdUserId = user.user_id;
                    tus.Mobile = user.phone;
                    tus.UserName = user.user_id;
                    tus.RealName = user.name;

                    //成都市成华区教育局(413e9d8a823744b7814b1bf273a84aee)下的所有教育局人员的last_user_type为
                    if (unit.org_id == "413e9d8a823744b7814b1bf273a84aee")
                    {
                        tus.UnitType = UnitTypeEnum.County;
                        tus.ThirdUnitId = "Chqjyj20220809";
                        tus.UnitCode = "Chqjyj20220809";
                    }//last_user_type 用户类型0：学生1:老师2:家长3:机构4:学校5:学校工作人员6:机构工作人员
                    else if (user.last_user_type.Contains('1', StringComparison.Ordinal) || user.last_user_type.Contains('3', StringComparison.Ordinal))  //学校
                    {
                        tus.UnitType = UnitTypeEnum.School;

                        string schoolProp = "";

                        int org_category = 0;
                        bool parseResult = int.TryParse(unit.org_category, out org_category);
                        if (!parseResult)
                        {
                            org_category = 0;
                        }
                        switch (org_category)
                        {
                            //0:小学1:初中2:高中3:完中4:机构5:教学点6:九年一贯制7: 十二年一贯制9： 其它教育类学校10: 中职11: 高职12：幼儿园
                            case 12:
                                schoolProp = "幼儿园";
                                break;

                            case 0:
                                schoolProp = "小学";
                                break;

                            case 1:
                                schoolProp = "初中";
                                break;

                            case 2:
                                schoolProp = "高中";
                                break;

                            case 3:
                                schoolProp = "完中";
                                break;

                            case 6:
                                schoolProp = "九年制";
                                break;

                            case 7:
                                schoolProp = "十二年制";
                                break;

                            case 10:
                            case 11:
                                schoolProp = "中高职";
                                break;

                            default:
                                schoolProp = "";
                                break;
                        }
                        tus.UnitCode = string.IsNullOrEmpty(unit.org_coding) ? unit.org_id : unit.org_coding;
                        tus.UnitName = user.last_top_org_name;
                        tus.Brief = unit.org_short_name;
                        tus.Address = unit.org_address;
                        tus.SchoolProp = schoolProp;
                        tus.ThirdUnitId = unit.org_id;
                        tus.ParentUnitId = "Chqjyj20220809";
                        tus.RoleId = 31; // 应天俞（谢红平 ）先给所有用户授权一个学科教师的授权，让他能进去，其他权限还是单独授权，最好引导学校管理员去授权
                    }
                    else if (user.last_user_type.Contains('4', StringComparison.Ordinal))
                    {
                        tus.UnitType = UnitTypeEnum.County;
                        tus.ThirdUnitId = "Chqjyj20220809";
                        tus.UnitCode = "Chqjyj20220809";
                    }
                    else
                    {
                        obj.Tag = 0;
                        obj.Message = "您的账号类型不允许使用平台。";
                        return obj;
                    }

                    LogHelper.Info(JsonConvert.SerializeObject(tus));
                    TData<UserEntity> userObj = await userThirdAuthBLL.SaveForm(tus);
                    if (userObj.Tag == 1)
                    {
                        //NetHelper.SetConfigHttpContext(HttpContext);
                        userObj = await userBLL.CheckLogin(userObj.Data.UserName, userObj.Data.Password, (int)PlatformEnum.ThirdSSOApi, 3, ip: _netHelper.GetIp());
                        LogHelper.Info($"登录成功返回:{JsonConvert.SerializeObject(obj)}");
                        if (userObj.Tag == 1)
                        {
                            await new UserBLL().UpdateUser(userObj.Data);
                            await Operator.Instance.AddCurrent(userObj.Data.ApiToken);
                            obj.Data = await Operator.Instance.Current(userObj.Data.ApiToken);
                        }
                        obj.Tag = userObj.Tag;
                        obj.Message = userObj.Message;
                    }
                    else
                    {
                        obj.Tag = 0;
                        obj.Message = userObj.Message;
                        return obj;
                    }
                }

                else//ticket不存在
                {
                    obj.Tag = 0;
                    obj.Message = "ticket不存在， 可能原因：登录超时，或不正确的访问方式。";
                    return obj;
                }
            }
            catch (WebException exp)
            {
                LogHelper.Info($"SSO:login_error:{exp.Message} {exp.StackTrace}");

                obj.Tag = 0;
                obj.Message = "接口未获取到单位信息，认证失败，请重新认证！" + exp.Message;
                return obj;
            }

            LogHelper.Info($"登录成功返回:{JsonConvert.SerializeObject(obj)}");

            return obj;
        }

        /// <summary>
        /// 用户登录
        /// </summary>
        /// <param name="code">code</param>
        /// <returns></returns>
        [Route("auth_chqh1")]
        [HttpPost]
        public async Task<TData<OperatorInfo>> Auth_chq_h1(string code)
        {
            TData<OperatorInfo> obj = new TData<OperatorInfo>();
            try
            {
                string strUserId = "";
                LogHelper.Info($"SSO:ticket:{code}");

                var ThirdSSO = Configuration.GetSection("sso").Get<ThirdSSO>();

                TzSoftAcore.SetConfig(ThirdSSO);
                if (!string.IsNullOrEmpty(code)) //如果有code，获取用户Id
                {
                    var accesstokenModel = TzSoftAcore.GetAccesstoken();
                    LogHelper.Info($"SSO:accesstokenModel:{JsonConvert.SerializeObject(accesstokenModel)}");

                    if (accesstokenModel.retCode != "000000")
                    {
                        obj.Tag = 0;
                        obj.Message = accesstokenModel.retDesc;
                        return obj;
                    }
                    var ticketModel = TzSoftAcore.ValidaTicket(accesstokenModel.tokenInfo.accessToken, code);
                    LogHelper.Info($"SSO:ticketModel:{JsonConvert.SerializeObject(ticketModel)}");

                    if (ticketModel.code != "000000")
                    {
                        obj.Tag = 0;
                        obj.Message = JsonConvert.SerializeObject(ticketModel) + " 解决方案：请关闭当前页面，重新打开";
                        return obj;
                    }

                    //天俞角色类型，2022-11-11
                    //0学生
                    //1教师
                    //3学校工作人员
                    //4机构行政人员
                    string userType = ticketModel.result.last_user_type;
                    if (!userType.Contains('1', StringComparison.Ordinal) && !userType.Contains('3', StringComparison.Ordinal) && !userType.Contains('4', StringComparison.Ordinal)
                        && !userType.Contains('5', StringComparison.Ordinal) && !userType.Contains('6', StringComparison.Ordinal))
                    {
                        obj.Tag = 0;
                        obj.Message = $"您没有权限使用平台，用户类型为【{userType}】;用户类型0：学生1:老师2:家长3:机构4:学校5:学校工作人员6:机构工作人员";
                        return obj;
                    }

                    var user = ticketModel.result;
                    //if (user == null)
                    //{
                    //    LogHelper.Info($"SSO:获取用户信息失败:{0}", JsonConvert.SerializeObject(userInfoModel)));
                    //    ViewBag.Msg = "获取用户信息失败，请确认是否登录超时。";
                    //    return View();
                    //}

                    var schoolInfoModel = TzSoftAcore.GetSchoolInfo(accesstokenModel.tokenInfo.accessToken, user.last_top_org_id);
                    LogHelper.Info($"SSO:SchoolInfoModel:{JsonConvert.SerializeObject(schoolInfoModel)}");

                    if (schoolInfoModel.code != "000000")
                    {
                        obj.Tag = 0;
                        obj.Message = schoolInfoModel.message;
                        return obj;
                    }
                    var unit = schoolInfoModel.result;
                    if (unit == null)
                    {
                        LogHelper.Info($"SSO:获取单位信息失败:{JsonConvert.SerializeObject(schoolInfoModel)}");
                        obj.Tag = 0;
                        obj.Message = "获取单位信息失败，请确认是否登录超时。";
                        return obj;
                    }
                    ////获取手机号码
                    //try
                    //{
                    //    string strMobileInfo = TzSoftAcore.GetMobileInfo(accesstokenModel.tokenInfo.accessToken, user.personId);
                    //    if (!string.IsNullOrEmpty(strMobileInfo))
                    //    {
                    //        LogHelper.Info($"SSO:获取手机号码:{0}", strMobileInfo));

                    //        var objMobile = JsonConvert.DeserializeObject<TzSoftAcore.MobileInfo>(strMobileInfo);
                    //        if (objMobile != null && objMobile.code == "000000")
                    //        {
                    //            //网关提供固定解密秘钥，需要按照下文算法生成动态秘钥解密数据。
                    //            //动态秘钥是 MD5(appId+secret + yyyyMMdd)其中 MD5加密方式是16位小写，secret为固定秘钥，yyyyMMdd为当天时间：如20190808
                    //            string dyKey = Extention.ToMD5String16(ThirdSSO.clientID + ThirdSSO.key + DateTime.Now.ToString("yyyyMMdd")).ToLower();

                    //            LogHelper.Info($"SSO:动态秘钥:{0}", dyKey));

                    //            user.phoneNumbers = SecurityHelper.AESDecodeHex(objMobile.result, dyKey);

                    //        }
                    //    }
                    //}
                    //catch (Exception expM)
                    //{
                    //    LogHelper.Info($"SSO:获取手机号码失败:{0}", expM.Message));
                    //}

                    strUserId = user.user_id;
                    UserThirdAuth tus = new UserThirdAuth();

                    tus.ThirdUserId = user.user_id;
                    tus.Mobile = user.phone;
                    tus.UserName = user.user_id;
                    tus.RealName = user.name;

                    //成都市成华区教育局(413e9d8a823744b7814b1bf273a84aee)下的所有教育局人员的last_user_type为
                    if (unit.org_id == "413e9d8a823744b7814b1bf273a84aee")
                    {
                        tus.UnitType = UnitTypeEnum.County;
                        tus.ThirdUnitId = "Chqjyj20220809";
                        tus.UnitCode = "Chqjyj20220809";
                    }//last_user_type 用户类型0：学生1:老师2:家长3:机构4:学校5:学校工作人员6:机构工作人员
                    else if (user.last_user_type.Contains('1', StringComparison.Ordinal) || user.last_user_type.Contains('3', StringComparison.Ordinal))  //学校
                    {
                        tus.UnitType = UnitTypeEnum.School;

                        string schoolProp = "";

                        int org_category = 0;
                        bool parseResult = int.TryParse(unit.org_category, out org_category);
                        if (!parseResult)
                        {
                            org_category = 0;
                        }
                        switch (org_category)
                        {
                            //0:小学1:初中2:高中3:完中4:机构5:教学点6:九年一贯制7: 十二年一贯制9： 其它教育类学校10: 中职11: 高职12：幼儿园
                            case 12:
                                schoolProp = "幼儿园";
                                break;

                            case 0:
                                schoolProp = "小学";
                                break;

                            case 1:
                                schoolProp = "初中";
                                break;

                            case 2:
                                schoolProp = "高中";
                                break;

                            case 3:
                                schoolProp = "完中";
                                break;

                            case 6:
                                schoolProp = "九年制";
                                break;

                            case 7:
                                schoolProp = "十二年制";
                                break;

                            case 10:
                            case 11:
                                schoolProp = "中高职";
                                break;

                            default:
                                schoolProp = "";
                                break;
                        }
                        tus.UnitCode = string.IsNullOrEmpty(unit.org_coding) ? unit.org_id : unit.org_coding;
                        tus.UnitName = user.last_top_org_name;
                        tus.Brief = unit.org_short_name;
                        tus.Address = unit.org_address;
                        tus.SchoolProp = schoolProp;
                        tus.ThirdUnitId = unit.org_id;
                        tus.ParentUnitId = "Chqjyj20220809";
                    }
                    else if (user.last_user_type.Contains('4', StringComparison.Ordinal))
                    {
                        tus.UnitType = UnitTypeEnum.County;
                        tus.ThirdUnitId = "Chqjyj20220809";
                        tus.UnitCode = "Chqjyj20220809";
                    }
                    else
                    {
                        obj.Tag = 0;
                        obj.Message = "您的账号类型不允许使用平台。";
                        return obj;
                    }

                    LogHelper.Info(JsonConvert.SerializeObject(tus));
                    TData<UserEntity> userObj = await userThirdAuthBLL.SaveForm(tus);
                    if (userObj.Tag == 1)
                    {
                        //NetHelper.SetConfigHttpContext(HttpContext);
                        userObj = await userBLL.CheckLogin(userObj.Data.UserName, userObj.Data.Password, (int)PlatformEnum.ThirdSSO, 3, ip: _netHelper.GetIp());
                        if (userObj.Tag == 1)
                        {
                            await new UserBLL().UpdateUser(userObj.Data);
                            await Operator.Instance.AddCurrent(userObj.Data.ApiToken);
                            obj.Data = await Operator.Instance.Current(userObj.Data.ApiToken);
                        }
                        obj.Tag = userObj.Tag;
                        obj.Message = userObj.Message;
                    }
                    else
                    {
                        obj.Tag = 0;
                        obj.Message = userObj.Message;
                        return obj;
                    }
                }

                else//ticket不存在
                {
                    obj.Tag = 0;
                    obj.Message = "ticket不存在， 可能原因：登录超时，或不正确的访问方式。";
                    return obj;
                }
            }
            catch (WebException exp)
            {
                LogHelper.Info($"SSO:login_error:{exp.Message} {exp.StackTrace}");

                obj.Tag = 0;
                obj.Message = "接口未获取到单位信息，认证失败，请重新认证！" + exp.Message;
                return obj;
            }

            LogHelper.Info($"登录成功返回:{JsonConvert.SerializeObject(obj)}");

            return obj;
        }

        /// <summary>
        /// 对接第三方统一身份认证：先通过h5页面对接，然后在h5页面中打开微信小程序
        /// </summary>
        /// <param name="token">token</param>
        /// <param name="clientType">客户端类型</param>
        /// <param name="code">code</param>
        /// <returns></returns>
        [Route("loginunion")]
        [HttpPost]
        public async Task<TData<OperatorInfo>> LoginUnion(string token, int clientType, string code)
        {
            LogHelper.Info($"loginunion:获取到的token {token}", null);
            TData<OperatorInfo> obj = new TData<OperatorInfo>();
            if (string.IsNullOrEmpty(token))
            {
                obj.Tag = 0;
                obj.Message = "token为空。";
                return obj;
            }
            else
            {
                try
                {
                    OperatorInfo operatorinfo = await Operator.Instance.Current(token);
                    if (operatorinfo == null)
                    {
                        obj.Tag = -1;
                        obj.Message = "登录超时，请重新登录。";
                        return obj;
                    }
                    else
                    {
                        string openId = "", unionId = "";
                        //绑定微信信息
                        if (clientType == ClientTypeEnum.MPWEIXIN.ParseToInt())
                        {
                            var jsonResult = await SnsApi.JsCode2JsonAsync(Config.SenparcWeixinSetting.WxOpenAppId, Config.SenparcWeixinSetting.WxOpenAppSecret, code);
                            if (jsonResult.errcode == ReturnCode.请求成功)
                            {
                                openId = jsonResult.openid;
                                unionId = jsonResult.unionid;
                            }

                            LogHelper.Info($"wx_jsonResult:{JsonConvert.SerializeObject(jsonResult)}", null);
                        }
                        LogHelper.Info($"loginunion:获取到的UserName {operatorinfo.UserName}，{operatorinfo.UserId}", null);
                        //NetHelper.SetConfigHttpContext(HttpContext);
                        TData<UserEntity> userObj = await userBLL.CheckLogin(operatorinfo.UserName, "", (int)PlatformEnum.ThirdSSOApi, 3, ip: _netHelper.GetIp(), clientType, openId, unionId);
                        if (userObj.Tag == 1)
                        {
                            await new UserBLL().UpdateUser(userObj.Data);
                            await Operator.Instance.AddCurrent(userObj.Data.ApiToken);
                            obj.Tag = 1;
                            obj.Data = await Operator.Instance.Current(userObj.Data.ApiToken);
                            obj.Data.OpenId = openId;
                            obj.Message = "登录成功返回。";

                            LogHelper.Info($"loginunion:登录成功返回:{JsonConvert.SerializeObject(obj)}", null);
                        }
                        else
                        {
                            obj.Tag = 0;
                            obj.Message = "登录失败。";
                            LogHelper.Info($"loginunion:登录失败，未能获取到用户信息。", null);
                        }

                        if (!string.IsNullOrEmpty(token)) //如果有token，则更新token状态
                        {
                            int result = await userBindOpenidBLL.UpdateQrLogin(token, userObj.Data.Id.Value, openId);
                            LogHelper.Info($"loginunion:更新token状态 {result}", null);
                        }
                    }
                }
                catch (WebException ex)
                {
                    LogHelper.Info($"loginunion:异常 {ex.Message}", null);

                    obj.Tag = 0;
                    obj.Message = $"loginunion:异常 {ex.Message}";
                    return obj;
                }
            }
            return obj;
        }

        /// <summary>
        /// 通州区统一身份认证
        /// </summary>
        /// <param name="iflyssost"></param>
        /// <param name="requesturl"></param>
        /// <returns></returns>
        [Route("auth_tzqh5")]
        [HttpPost]
        public async Task<TData<OperatorInfo>> Auth_tzq_h5(string iflyssost, string requesturl)
        {
            LogHelper.Info($"SSO:获取到的参数值:{iflyssost}", null);
            LogHelper.Info($"SSO:获取到的requesturl地址:{requesturl}", null);
            TData<OperatorInfo> obj = new TData<OperatorInfo>();

            if (string.IsNullOrEmpty(iflyssost))
            {
                LogHelper.Info($"ticket不存在:{iflyssost}", null);
                obj.Tag = 0;
                obj.Message = "ticket不存在,可能原因:登录超时或不正确的访问方式。";
                return obj;
            }
            else if (iflyssost.ToString() == "undefined")
            {
                LogHelper.Info($"ticket异常:{iflyssost}", null);
                obj.Tag = 0;
                obj.Message = "ticket不存在,可能原因:登录超时或不正确的访问方式。";
                return obj;
            }

            var ThirdSSO = Configuration.GetSection("sso").Get<ThirdSSO>();
            string code = iflyssost;
            TztxSoftAcore.SetConfig(ThirdSSO);
            TztxSoftAcore2.SetConfig(ThirdSSO);
            try
            {
                //应用调用 CAS Serve校验Ticket接口
                //式例：https://open.changyan.com/sso/serviceValidate?service=https%3a%2f%2fwww.iflytek.com%2findex.html&ticket=ST-7039489-V7iyfQljIcM52G5WuuYE-open.changyan.com
                string url = "https://sygl.jstzjy.net/h5/#/pages/login_third/tzqh5?";
                string paramStr = "";
                if (requesturl != null && requesturl.IndexOf(";") > -1)
                {
                    string paramtmp = "";
                    foreach (string param in TextHelper.SplitToArray<string>(requesturl, ';'))
                    {
                        if (param.IndexOf("ticket") == -1)
                        {
                            paramtmp += param + ";";
                        }
                    }

                    paramtmp = paramtmp.Replace(";", "&").Replace(":", "=");
                    LogHelper.Info($"paramtmp 参数：{paramtmp}", null);
                    if (paramtmp.LastIndexOf("&") > -1)
                    {
                        paramStr = paramtmp.Substring(0, paramtmp.LastIndexOf("&"));
                    }
                }

                url = url + paramStr;
                LogHelper.Info($"url 参数：{url}", null);

                string serviceStr = System.Web.HttpUtility.UrlEncode(url, System.Text.Encoding.UTF8);
                LogHelper.Info($"serviceStr:{serviceStr}", null);

                string validateUrl = $"https://open.changyan.com/sso/serviceValidate?service={serviceStr}&ticket={code}";
                LogHelper.Info($"validateUrl:{validateUrl}", null);

                var serviceValidateResult = await this.PostHelper($"{validateUrl}", null);
                LogHelper.Info($"serviceValidate接口返回数据：{JsonConvert.SerializeObject(serviceValidateResult)}", null);

                if (serviceValidateResult.Tag == 1)
                {
                    string loginname = serviceValidateResult.Message; //登录用户
                    string strUserId = ""; //用户Id
                    LogHelper.Info($"当前用户的登录名：{loginname}", null);

                    //根据用户loginName 获取用户信息
                    Dictionary<string, string> userUniqueparm = new Dictionary<string, string>();
                    userUniqueparm.Add("key", "login_name");
                    userUniqueparm.Add("value", loginname);
                    var userUniqueInfoResult = TztxSoftAcore2.GetUserDataByUniqueInfo(userUniqueparm, "/getUserByUniqueInfo");
                    LogHelper.Info($"SSO:getUserByUniqueInfo:{JsonConvert.SerializeObject(userUniqueInfoResult)}", null);
                    if (userUniqueInfoResult != null && userUniqueInfoResult.data != null && userUniqueInfoResult.code == "1")
                    {
                        var userUniqueInfo = JsonConvert.DeserializeObject<TztxSoftAcore.UserUniqueInfo>(userUniqueInfoResult.data.ToString());
                        if (userUniqueInfo != null)
                        {
                            if (string.IsNullOrEmpty(userUniqueInfo.id))
                            {
                                LogHelper.Info($"SSO:获取用户Id失败:{userUniqueInfo.id}", null);
                                obj.Tag = 0;
                                obj.Message = "获取用户Id失败！请联系认证中心。";
                                return obj;
                            }
                            else
                            {
                                strUserId = userUniqueInfo.id; //获取到的用户Id
                                LogHelper.Info($"当前用户Id：{strUserId}", null);

                                #region 获取用户角色

                                //获取用户角色
                                Dictionary<string, string> parm = new Dictionary<string, string>();
                                parm.Add("userId", strUserId);
                                var roleResult = TztxSoftAcore2.GetDataByUserid(parm, "/listRoleByUserId");
                                LogHelper.Info($"SSO:listRoleByUserId:{JsonConvert.SerializeObject(roleResult)}", null);
                                if (roleResult != null && roleResult.data != null && roleResult.code == "1")
                                {
                                    var roleList = JsonConvert.DeserializeObject<List<TztxSoftAcore.Role>>(roleResult.data.ToString());
                                    var role = roleList.Where(m => m.roleNature == "0" && (m.enName == "teacher" || m.enName == "edupersonnel" || m.enName == "instructor")).FirstOrDefault();
                                    if (role == null)
                                    {
                                        LogHelper.Info($"SSO:获取用户角色信息失败:{JsonConvert.SerializeObject(roleList)}", null);

                                        obj.Tag = 0;
                                        obj.Message = "未找到角色信息！您无权使用此功能，只有（教研员：instructor 机构用户：edupersonnel  教师：teacher）才能使用本平台。";
                                        return obj;
                                    }

                                    UserThirdAuth tus = new UserThirdAuth();
                                    tus.ThirdUserId = strUserId;
                                    tus.Mobile = userUniqueInfo.mobile;
                                    tus.UserName = userUniqueInfo.loginName;
                                    tus.RealName = userUniqueInfo.userName;

                                    TztxSoftAcore.School unit = new TztxSoftAcore.School();
                                    TztxSoftAcore2.Result unitResult = new TztxSoftAcore2.Result();
                                    if (role.enName == "teacher")  //学校
                                    {
                                        tus.UnitType = UnitTypeEnum.School;

                                        unitResult = TztxSoftAcore2.GetDataByUserid(parm, "/listSchoolByUser");
                                        if (unitResult == null || unitResult.data == null || unitResult.code != "1")
                                        {
                                            LogHelper.Info($"SSO:获取学校信息失败:{JsonConvert.SerializeObject(unitResult)}", null);
                                            obj.Tag = 0;
                                            obj.Message = "获取学校信息失败，请确认是否登录超时。";
                                            return obj;
                                        }
                                        LogHelper.Info($"SSO:listSchoolByUser:{JsonConvert.SerializeObject(unitResult)}", null);

                                        var unitList = JsonConvert.DeserializeObject<List<TztxSoftAcore.School>>(unitResult.data.ToString());

                                        unit = unitList.FirstOrDefault();
                                        if (unit == null)
                                        {
                                            LogHelper.Info($"SSO:获取学校信息失败:{JsonConvert.SerializeObject(unitResult)}", null);
                                            obj.Tag = 0;
                                            obj.Message = "获取学校信息失败，请确认是否登录超时。";
                                            return obj;
                                        }
                                        string schoolProp = "";

                                        if (unit.phaseCode.IndexOf(',') != -1)
                                        {
                                            Regex regNum = new Regex(@"\d+(\.\d)?", RegexOptions.None);

                                            var tempData = regNum.Matches(unit.phaseCode);
                                            foreach (var t in tempData)
                                            {
                                                schoolProp += getSchoolSection(t.ToString());
                                            }
                                            schoolProp = getSchoolProp(schoolProp);
                                        }
                                        else
                                        {
                                            schoolProp = getSchoolSection(unit.phaseCode);
                                        }
                                        tus.UnitCode = unit.id;
                                        tus.ParentUnitId = "Tzjyj20230907";
                                        tus.UnitName = unit.schoolName;
                                        tus.Brief = unit.shortName;
                                        tus.Address = unit.address;
                                        tus.SchoolProp = schoolProp;
                                        tus.ThirdUnitId = unit.id;
                                        tus.RoleId = 31; //学科教师
                                    }
                                    else if (role.enName == "edupersonnel" || role.enName == "instructor")
                                    {
                                        tus.UnitType = UnitTypeEnum.County;
                                        tus.ThirdUnitId = "Tzjyj20230907";
                                        tus.UnitName = "通州区教育局";
                                        tus.UnitCode = "Tzjyj20230907";
                                        tus.RoleId = 21; //区县教研员
                                    }

                                    LogHelper.Info($"SSO:保存信息:{JsonConvert.SerializeObject(tus)}", null);

                                    TData<UserEntity> userObj = await userThirdAuthBLL.SaveForm(tus);
                                    if (userObj.Tag == 1)
                                    {
                                        //NetHelper.SetConfigHttpContext(HttpContext);
                                        userObj = await userBLL.CheckLogin(userObj.Data.UserName, userObj.Data.Password, (int)PlatformEnum.ThirdSSOApi, 3, ip: _netHelper.GetIp());
                                        LogHelper.Info($"登录CheckLogin返回:{JsonConvert.SerializeObject(obj)}", null);
                                        if (userObj.Tag == 1)
                                        {
                                            await new UserBLL().UpdateUser(userObj.Data);
                                            await Operator.Instance.AddCurrent(userObj.Data.ApiToken);
                                            obj.Data = await Operator.Instance.Current(userObj.Data.ApiToken);
                                        }
                                        obj.Tag = userObj.Tag;
                                        obj.Message = userObj.Message;
                                    }
                                    else
                                    {
                                        obj.Tag = 0;
                                        obj.Message = userObj.Message;
                                        return obj;
                                    }
                                }
                                else
                                {
                                    LogHelper.Info($"SSO:获取用户角色信息失败:{roleResult.message}", null);

                                    obj.Tag = 0;
                                    obj.Message = "获取用户角色信息失败！请联系认证中心。";
                                }

                                #endregion 获取用户角色
                            }
                        }
                    }
                    else
                    {
                        LogHelper.Info($"SSO:获取用户信息失败:{userUniqueInfoResult.message}", null);
                        obj.Tag = 0;
                        obj.Message = "获取用户信息失败！请联系认证中心。";
                        return obj;
                    }
                }
                else
                {
                    obj.Tag = 0;
                    obj.Message = "认证失败，" + serviceValidateResult.Message;
                }
            }
            catch (Exception exp)
            {
                LogHelper.Info($"SSO:login_error:{exp.Message} {exp.StackTrace}", null);
                obj.Tag = 0;
                obj.Message = "认证失败，请重新认证！" + exp.Message;
                return obj;
            }

            LogHelper.Info($"登录结果:{JsonConvert.SerializeObject(obj)}", null);
            return obj;
        }

        /// <summary>
        /// 通州统一身份认证学段转码
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        private string getSchoolSection(string code)
        {
            string schoolStage;
            switch (code)
            {
                //(01:幼儿园；02：学前班；03：小学；04：初中；05：高中；06；大学；07：演示；08：职教；09：特殊教育)
                case "01":
                    schoolStage = "幼儿园";
                    break;

                case "02":
                    schoolStage = "学前班";
                    break;

                case "03":
                    schoolStage = "小学";
                    break;

                case "04":
                    schoolStage = "初中";
                    break;

                case "05":
                    schoolStage = "高中";
                    break;

                case "06":
                    schoolStage = "大学";
                    break;

                case "07":
                    schoolStage = "演示";
                    break;

                case "08":
                    schoolStage = "中高职";
                    break;

                case "09":
                    schoolStage = "特殊教育";
                    break;

                default:
                    schoolStage = "";
                    break;
            }

            return schoolStage;
        }

        /// <summary>
        /// 将学段转换成平台的单位属性
        /// </summary>
        /// <param name="schoolSection"></param>
        /// <returns></returns>
        private string getSchoolProp(string schoolSection)
        {
            if (schoolSection.Contains("小学") && schoolSection.Contains("初中") && schoolSection.Contains("高中"))
            {
                return "十二年制";
            }
            else if (schoolSection.Contains("初中") && schoolSection.Contains("高中"))
            {
                return "完中";
            }
            else if (schoolSection.Contains("小学") && schoolSection.Contains("初中"))
            {
                return "九年制";
            }
            else
            {
                return schoolSection;
            }
        }

        /// <summary>
        /// 使用HttpClient调用WebService
        /// </summary>
        /// <param name="url">URL地址</param>
        /// <param name="content">参数</param>
        /// <returns></returns>
        private async Task<TData<string>> PostHelper(string url, HttpContent content)
        {
            TData<string> obj = new TData<string>();
            var result = string.Empty;
            try
            {
                using (var client = _httpClientfactory.CreateClient())
                using (var response = await client.PostAsync(url, content))
                {
                    if (response.StatusCode == HttpStatusCode.OK)
                    {
                        var result1 = await response.Content.ReadAsStringAsync();
                        LogHelper.Info($"PostHelper：result1 {result1}", null);

                        XmlDocument doc = new XmlDocument();
                        doc.LoadXml(result1);
                        result = doc.InnerText; //返回结果
                        string nodeName = doc.FirstChild.FirstChild.LocalName; //获取子节点名称
                        if (nodeName == "authenticationSuccess") //正确节点
                        {
                            obj.Tag = 1;
                            obj.Message = result;
                        }
                        else
                        {
                            obj.Tag = 0;
                            obj.Message = result;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                result = ex.Message;
                obj.Tag = 0;
                obj.Message = "执行失败。";
                LogHelper.Info($"PostHelper异常：{ex.Message},{ex.StackTrace}", null);
            }
            return obj;
        }

        /// <summary>
        /// 常熟市统一身份认证
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        [Route("auth_cssh5")]
        [HttpPost]
        public async Task<TData<OperatorInfo>> Auth_css_h5(string code)
        {
            TData<OperatorInfo> obj = new TData<OperatorInfo>();
            try
            {
                string strUserId = "";
                LogHelper.Info($"SSO:ticket:{code}");

                var ThirdSSO = Configuration.GetSection("sso").Get<ThirdSSO>();

                TzSoftAcore.SetConfig(ThirdSSO);
                if (!string.IsNullOrEmpty(code)) //如果有code，获取用户Id
                {
                    var accesstokenModel = TzSoftAcore.GetAccesstoken();
                    LogHelper.Info($"SSO:accesstokenModel:{JsonConvert.SerializeObject(accesstokenModel)}");

                    if (accesstokenModel.retCode != "000000")
                    {
                        obj.Tag = 0;
                        obj.Message = accesstokenModel.retDesc;
                        return obj;
                    }
                    var ticketModel = TzSoftAcore.ValidaTicket(accesstokenModel.tokenInfo.accessToken, code);
                    LogHelper.Info($"SSO:ticketModel:{JsonConvert.SerializeObject(ticketModel)}");

                    if (ticketModel.code != "000000")
                    {
                        obj.Tag = 0;
                        obj.Message = JsonConvert.SerializeObject(ticketModel) + " 解决方案：请关闭当前页面，重新打开";
                        return obj;
                    }

                    //天俞角色类型，2022-11-11
                    //0学生
                    //1教师
                    //3学校工作人员
                    //4机构行政人员
                    string userType = ticketModel.result.last_user_type;
                    if (!userType.Contains('1', StringComparison.Ordinal) && !userType.Contains('3', StringComparison.Ordinal) && !userType.Contains('4', StringComparison.Ordinal)
                        && !userType.Contains('5', StringComparison.Ordinal) && !userType.Contains('6', StringComparison.Ordinal))
                    {
                        obj.Tag = 0;
                        obj.Message = $"您没有权限使用平台，用户类型为【{userType}】;用户类型0：学生1:老师2:家长3:机构4:学校5:学校工作人员6:机构工作人员";
                        return obj;
                    }

                    var user = ticketModel.result;
                    LogHelper.Info($"SSO:user:{JsonConvert.SerializeObject(user)}");
                    if (user == null)
                    {
                        LogHelper.Info("SSO:获取用户信息失败");
                        obj.Tag = 0;
                        obj.Message = "获取用户信息失败。";
                        return obj;
                    }

                    var schoolInfoModel = TzSoftAcore.GetSchoolInfo(accesstokenModel.tokenInfo.accessToken, user.last_top_org_id);
                    LogHelper.Info($"SSO:SchoolInfoModel:{JsonConvert.SerializeObject(schoolInfoModel)}");

                    if (schoolInfoModel.code != "000000")
                    {
                        obj.Tag = 0;
                        obj.Message = schoolInfoModel.message;
                        return obj;
                    }
                    var unit = schoolInfoModel.result;
                    if (unit == null)
                    {
                        LogHelper.Info($"SSO:获取单位信息失败:{JsonConvert.SerializeObject(schoolInfoModel)}");
                        obj.Tag = 0;
                        obj.Message = "获取单位信息失败，请确认是否登录超时。";
                        return obj;
                    }

                    strUserId = user.user_id;
                    UserThirdAuth tus = new UserThirdAuth();

                    tus.ThirdUserId = user.user_id;
                    tus.Mobile = user.phone;
                    tus.UserName = user.user_id;
                    tus.RealName = user.name;

                    if (user.last_user_type.Contains("1") || user.last_user_type.Contains("3"))  //学校
                    {
                        tus.UnitType = UnitTypeEnum.School;

                        string schoolProp = "";
                        int org_category = 0;
                        int.TryParse(unit.org_category, out org_category);
                        switch (org_category)
                        {
                            //0:小学1:初中2:高中3:完中4:机构5:教学点6:九年一贯制7: 十二年一贯制9： 其它教育类学校10: 中职11: 高职12：幼儿园
                            case 12:
                                schoolProp = "幼儿园";
                                break;

                            case 0:
                                schoolProp = "小学";
                                break;

                            case 1:
                                schoolProp = "初中";
                                break;

                            case 2:
                                schoolProp = "高中";
                                break;

                            case 3:
                                schoolProp = "完中";
                                break;

                            case 6:
                                schoolProp = "九年制";
                                break;

                            case 7:
                                schoolProp = "十二年制";
                                break;

                            case 10:
                            case 11:
                                schoolProp = "中高职";
                                break;

                            default:
                                schoolProp = "";
                                break;
                        }
                        tus.UnitCode = string.IsNullOrEmpty(unit.org_coding) ? unit.org_id : unit.org_coding;
                        tus.UnitName = user.last_top_org_name;
                        tus.Brief = unit.org_short_name;
                        tus.Address = unit.org_address;
                        tus.SchoolProp = schoolProp;
                        tus.ParentUnitId = "Cssjyj20231226";
                        tus.ThirdUnitId = unit.org_id;
                        tus.RoleId = 31; //学校默认角色：学科教师
                    }
                    else if (user.last_user_type.Contains("4", StringComparison.Ordinal)) //区县
                    {
                        tus.UnitType = UnitTypeEnum.County;
                        tus.ThirdUnitId = "Cssjyj20231226";
                        tus.UnitCode = "Cssjyj20231226";
                        tus.RoleId = 21; //教育局默认角色：区县教研员
                    }
                    else
                    {
                        LogHelper.Info("SSO:您的账号类型不允许使用平台");
                        obj.Tag = 0;
                        obj.Message = "您的账号类型不允许使用平台";
                        return obj;
                    }

                    LogHelper.Info($"SSO:保存第三方用户信息:{JsonConvert.SerializeObject(tus)}");
                    TData<UserEntity> userObj = await userThirdAuthBLL.SaveForm(tus);
                    LogHelper.Info($"SSO:保存结果:{JsonConvert.SerializeObject(userObj)}");

                    if (userObj.Tag == 1)
                    {
                        //NetHelper.SetConfigHttpContext(HttpContext);
                        userObj = await userBLL.CheckLogin(userObj.Data.UserName, userObj.Data.Password, (int)PlatformEnum.ThirdSSOApi, 3, ip: _netHelper.GetIp());
                        LogHelper.Info($"登录成功返回:{JsonConvert.SerializeObject(obj)}");
                        if (userObj.Tag == 1)
                        {
                            await new UserBLL().UpdateUser(userObj.Data);
                            await Operator.Instance.AddCurrent(userObj.Data.ApiToken);
                            obj.Data = await Operator.Instance.Current(userObj.Data.ApiToken);
                        }
                        obj.Tag = userObj.Tag;
                        obj.Message = userObj.Message;
                    }
                    else
                    {
                        obj.Tag = 0;
                        obj.Message = userObj.Message;
                        return obj;
                    }
                }

                else//ticket不存在
                {
                    obj.Tag = 0;
                    obj.Message = "ticket不存在， 可能原因：登录超时，或不正确的访问方式。";
                    return obj;
                }
            }
            catch (WebException exp)
            {
                LogHelper.Info($"SSO:login_error:{exp.Message} {exp.StackTrace}");

                obj.Tag = 0;
                obj.Message = "接口未获取到单位信息，认证失败，请重新认证！" + exp.Message;
                return obj;
            }

            LogHelper.Info($"登录成功返回:{JsonConvert.SerializeObject(obj)}");

            return obj;
        }

        /// <summary>
        /// 吴中区统一身份认证
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [Route("auth_wzqh5")]
        [HttpPost]
        public async Task<TData<OperatorInfo>> Auth_wzq_h5(WxLoginInfo model)
        {
            TData<OperatorInfo> obj = new TData<OperatorInfo>();
            try
            {
                string userId = "";
                LogHelper.Info($"SSO:loginname:{model.LoginName}");
                var ThirdSSO = Configuration.GetSection("sso").Get<ThirdSSO>();

                TzSoftAcore.SetConfig(ThirdSSO);
                if (!string.IsNullOrEmpty(model.LoginName) && !string.IsNullOrEmpty(model.Pwd))
                {
                    string apiUrl = ThirdSSO.dataHost; //接口地址
                    string timeSpanStr = DateTime.Now.ToString("yyyyMMddHHmmss"); //时间戳格式：yyyyMMddHHmmss
                    string loginApiName = "/api/login/login";
                    string appKey = "appKey=" + ThirdSSO.clientID;
                    string authTimeSpan = "authTimeSpan=" + timeSpanStr;
                    string loginName = "loginName=" + model.LoginName;
                    string passWord = "passWord=" + model.Pwd;
                    string secrect = ThirdSSO.clientSecret;
                    string authSignStr = loginApiName + appKey + authTimeSpan + loginName + passWord + secrect;
                    string authSign = SecurityHelper.MD5ToHex(authSignStr); //md5加密
                    Dictionary<string, string> parm = new Dictionary<string, string>();
                    parm.Add("appKey", ThirdSSO.clientID);
                    parm.Add("authTimeSpan", timeSpanStr);
                    parm.Add("loginName", model.LoginName);
                    parm.Add("passWord", model.Pwd);
                    var loginResult = await SendHelper.SendPostAsyncByHeader(apiUrl + "/onlineService/api/login/login", parm, "authSign", authSign);
                    LogHelper.Info($"SSO:loginResult:{loginResult}");
                    if (loginResult.Tag == 1 && !string.IsNullOrEmpty(loginResult.Data))
                    {
                        SzwzqLoginModel loginmodel = JsonExtention.ToObject<SzwzqLoginModel>(loginResult.Data);
                        if (loginmodel.code == 200 || loginmodel.success == "true")
                        {
                            if (loginmodel.data != null && !string.IsNullOrEmpty(loginmodel.data.loginName)) //登录成功，获取登录用户名
                            {
                                string username = loginmodel.data.loginName; //登录用户名
                                LogHelper.Info($"获取到的用户名: {username}");

                                #region 调用接口查询用户信息

                                //根据登录名查询用户信息
                                string teacherapiName = "/api/sync/teacher";
                                string teacherappKey = "appKey=" + ThirdSSO.clientID;
                                string teacherauthTimeSpan = "authTimeSpan=" + timeSpanStr;
                                string currentPage = "currentPage=" + 1;
                                string teacherloginName = "loginName=" + username;
                                string pageSize = "pageSize=" + 1;
                                string teachersecrect = ThirdSSO.clientSecret;
                                string teacherauthSignStr = teacherapiName + teacherappKey + teacherauthTimeSpan + currentPage + teacherloginName + pageSize + teachersecrect;
                                string teacherauthSign = SecurityHelper.MD5ToHex(teacherauthSignStr); //md5加密
                                Dictionary<string, string> teacherparm = new Dictionary<string, string>();
                                teacherparm.Add("appKey", ThirdSSO.clientID);
                                teacherparm.Add("authTimeSpan", timeSpanStr);
                                teacherparm.Add("currentPage", "1");
                                teacherparm.Add("loginName", username);
                                teacherparm.Add("pageSize", "1");
                                var teacherResult = await SendHelper.SendPostAsyncByHeader(apiUrl + "/onlineService/api/sync/teacher", teacherparm, "authSign", teacherauthSign);
                                if (teacherResult.Tag == 1 && !string.IsNullOrEmpty(teacherResult.Data))
                                {
                                    LogHelper.Info($"调用teacher接口获取到的数据：{teacherResult.Data}", null);
                                    SzwzqTeacherModel teachermodel = JsonExtention.ToObject<SzwzqTeacherModel>(teacherResult.Data);
                                    if (teachermodel.code == 200 || teachermodel.success == "true")
                                    {
                                        if (teachermodel.data.Count > 0)
                                        {
                                            string schoolCode = teachermodel.data.LastOrDefault().schoolCode; //学校唯一码
                                            if (!string.IsNullOrEmpty(schoolCode))  //根据学校唯一码查询学校信息
                                            {
                                                //根据学校编码查询单位信息
                                                string schoolApiName = "/api/sync/school";
                                                string schoolcodeStr = "schoolCode=" + schoolCode;
                                                string schoolauthSignStr = schoolApiName + teacherappKey + teacherauthTimeSpan + currentPage + pageSize + schoolcodeStr + teachersecrect;
                                                string schoolauthSign = SecurityHelper.MD5ToHex(schoolauthSignStr); //md5加密

                                                Dictionary<string, string> schoolparm = new Dictionary<string, string>();
                                                schoolparm.Add("appKey", ThirdSSO.clientID);
                                                schoolparm.Add("authTimeSpan", timeSpanStr);
                                                schoolparm.Add("currentPage", "1");
                                                schoolparm.Add("pageSize", "1");
                                                schoolparm.Add("schoolCode", schoolCode);

                                                var schoolResult = await SendHelper.SendPostAsyncByHeader(apiUrl + "/onlineService/api/sync/school", schoolparm, "authSign", schoolauthSign);
                                                if (schoolResult.Tag == 1 && !string.IsNullOrEmpty(schoolResult.Data))
                                                {
                                                    LogHelper.Info($"调用school接口获取到的数据：{schoolResult.Data}", null);
                                                    SzwzqSchoolModel schoolmodel = JsonExtention.ToObject<SzwzqSchoolModel>(schoolResult.Data);
                                                    if (schoolmodel.code == 200 || schoolmodel.success == "true")
                                                    {
                                                        if (schoolmodel.data.Count > 0)
                                                        {
                                                            var unit = schoolmodel.data.LastOrDefault();
                                                            UserThirdAuth tus = new UserThirdAuth();
                                                            string strUserId = teachermodel.data.LastOrDefault().teacherCode; //教师唯一码
                                                            tus.ThirdUserId = teachermodel.data.LastOrDefault().teacherCode;
                                                            tus.Mobile = teachermodel.data.LastOrDefault().mobile;
                                                            tus.UserName = teachermodel.data.LastOrDefault().loginAccount;
                                                            tus.RealName = teachermodel.data.LastOrDefault().name;

                                                            if (unit.type == "211" || unit.type == "311" || unit.type == "342") //办学类型 111其他（含幼儿园）,211小学,311初中,342高中
                                                            {
                                                                string schoolProp = "1001004";
                                                                if (unit.type == "211")
                                                                {
                                                                    schoolProp = "1001001";
                                                                }
                                                                else if (unit.type == "311")
                                                                {
                                                                    schoolProp = "1001002";
                                                                }
                                                                else if (unit.type == "342")
                                                                {
                                                                    schoolProp = "1001003";
                                                                }
                                                                else
                                                                {
                                                                    schoolProp = "1001004"; //九年制
                                                                }
                                                                tus.UnitType = UnitTypeEnum.School;
                                                                tus.UnitCode = unit.schNo;
                                                                tus.ParentUnitId = "Wzqjyj20231019";
                                                                tus.UnitName = unit.name;
                                                                tus.SchoolProp = schoolProp;
                                                                tus.ThirdUnitId = unit.schId;
                                                                tus.RoleId = 31; //学科教师
                                                            }
                                                            else
                                                            {
                                                                tus.UnitType = UnitTypeEnum.County;
                                                                tus.ThirdUnitId = "Wzqjyj20231019";
                                                                tus.UnitName = "吴中区教育局";
                                                                tus.UnitCode = "Wzqjyj20231019";
                                                            }

                                                            LogHelper.Info($"SSO:保存第三方用户信息:{JsonConvert.SerializeObject(tus)}");
                                                            TData<UserEntity> userObj = await userThirdAuthBLL.SaveForm(tus);
                                                            LogHelper.Info($"SSO:保存结果:{JsonConvert.SerializeObject(userObj)}");

                                                            if (userObj.Tag == 1)
                                                            {
                                                                //NetHelper.SetConfigHttpContext(HttpContext);
                                                                userObj = await userBLL.CheckLogin(userObj.Data.UserName, userObj.Data.Password, (int)PlatformEnum.ThirdSSOApi, 3, ip: _netHelper.GetIp());
                                                                LogHelper.Info($"登录成功返回:{JsonConvert.SerializeObject(obj)}");
                                                                if (userObj.Tag == 1)
                                                                {
                                                                    await new UserBLL().UpdateUser(userObj.Data);
                                                                    await Operator.Instance.AddCurrent(userObj.Data.ApiToken);
                                                                    obj.Data = await Operator.Instance.Current(userObj.Data.ApiToken);
                                                                }
                                                                obj.Tag = userObj.Tag;
                                                                obj.Message = userObj.Message;
                                                            }
                                                            else
                                                            {
                                                                obj.Tag = 0;
                                                                obj.Message = userObj.Message;
                                                                return obj;
                                                            }
                                                        }
                                                        else
                                                        {
                                                            obj.Tag = 0;
                                                            obj.Message = "接口未获取到用户信息，认证失败，请重新认证！";
                                                            return obj;
                                                        }
                                                    }
                                                }
                                            }
                                            else
                                            {
                                                obj.Tag = 0;
                                                obj.Message = "接口未获取到用户信息，认证失败，请重新认证！";
                                                return obj;
                                            }
                                        }
                                    }
                                    else
                                    {
                                        obj.Tag = 0;
                                        obj.Message = "接口未获取到用户信息，认证失败，请重新认证！";
                                        return obj;
                                    }
                                }

                                #endregion 调用接口查询用户信息
                            }
                            else
                            {
                                obj.Tag = 0;
                                obj.Message = $"登录失败，未能获取到用户名。";
                                return obj;
                            }
                        }
                        else
                        {
                            obj.Tag = 0;
                            obj.Message = $"登录失败，{loginmodel.message}。";
                            return obj;
                        }
                    }
                }
                else
                {
                    obj.Tag = 0;
                    obj.Message = "请输入用户名和密码。";
                    return obj;
                }
            }
            catch (WebException exp)
            {
                LogHelper.Info($"SSO:login_error:{exp.Message} {exp.StackTrace}");

                obj.Tag = 0;
                obj.Message = "认证失败，请重新认证！" + exp.Message;
                return obj;
            }

            LogHelper.Info($"登录成功返回:{JsonConvert.SerializeObject(obj)}");

            return obj;
        }

        /// <summary>
        /// 姑苏区移动端统一身份认证
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        [Route("auth_gsqh5")]
        [HttpPost]
        public async Task<TData<OperatorInfo>> Auth_gsq_h5(string code)
        {
            TData<OperatorInfo> obj = new TData<OperatorInfo>();
            try
            {
                LogHelper.Info($"SSO:code:{code}");
                var ThirdSSO = Configuration.GetSection("sso").Get<ThirdSSO>();

                if (!string.IsNullOrEmpty(code)) //如果有code，获取用户Id
                {
                    string authAddress = ThirdSSO.authHost;
                    string dataAddress = ThirdSSO.dataHost;
                    string clientID = ThirdSSO.clientID;
                    string clientSecret = ThirdSSO.clientSecret;
                    string redirectUri = ThirdSSO.callBackUrl;
                    string userid = "";
                    string username = "";
                    string usertype = "";
                    string unitid = "";
                    string gettokenUrl = $"{authAddress}/auth/oauth/token?grant_type=authorization_code&code={code}&redirect_uri={redirectUri}&response_content_type=";
                    string tokenAuth = "Basic " + Base64.EncodeBase64(clientID + ":" + clientSecret);
                    //使用code换取token
                    var gettokenObj = await SendHelper.SendGetAsyncByHeader(gettokenUrl, "Authorization", tokenAuth);
                    LogHelper.Info($"SSO: 获取token接口的数据 {JsonConvert.SerializeObject(gettokenObj)}");
                    if (gettokenObj.Tag == 1 && !string.IsNullOrEmpty(gettokenObj.Data))
                    {
                        OauthTokenModel tokenmodel = JsonExtention.ToObject<OauthTokenModel>(gettokenObj.Data);
                        if (tokenmodel != null)
                        {
                            if (string.IsNullOrEmpty(tokenmodel.access_token))
                            {
                                LogHelper.Info($"SSO:未能获取到token");
                                obj.Tag = 0;
                                obj.Message = $"未能获取到token";
                                return obj;
                            }
                            string token = tokenmodel.access_token;
                            userid = tokenmodel.user_id;
                            username = tokenmodel.username;
                            usertype = tokenmodel.gsdataObjectType; //1为学生 2为教师 3为其他
                            unitid = tokenmodel.gsdataSchoolId;
                            if (usertype == "1") //1为学生 2为教师 3为其他
                            {
                                LogHelper.Info($"SSO:gsdataObjectType:{usertype},类型学生");
                                obj.Tag = 0;
                                obj.Message = $"您没有权限使用平台，用户类型为【{usertype}】:学生";
                                return obj;
                            }
                            string getuserinfoUrl = $"{authAddress}/admin/user/info";
                            string userAuth = "Bearer " + tokenmodel.access_token;
                            var getuserinfoObj = await SendHelper.SendGetAsyncByHeader(getuserinfoUrl, "Authorization", userAuth);
                            LogHelper.Info($"SSO: 获取到userinfo接口数据 {JsonConvert.SerializeObject(getuserinfoObj)}");
                            if (getuserinfoObj.Tag == 1 && getuserinfoObj.Data != null)
                            {
                                GsqUserInfoModel usermodel = JsonExtention.ToObject<GsqUserInfoModel>(getuserinfoObj.Data);
                                if (usermodel != null)
                                {
                                    if (usermodel.data.sysUser == null)
                                    {
                                        LogHelper.Info($"SSO:获取sysUser数据失败");
                                        obj.Tag = 0;
                                        obj.Message = $"获取用户信息失败";
                                        return obj;
                                    }

                                    //获取单位信息
                                    string getunitinfoUrl = $"{authAddress}/admin/user/unitInfo";
                                    var getunitinfoObj = await SendHelper.SendGetAsyncByHeader(getunitinfoUrl, "Authorization", userAuth);
                                    LogHelper.Info($"SSO: 获取到unitInfo接口数据 {JsonConvert.SerializeObject(getunitinfoObj)}");
                                    if (getunitinfoObj.Tag == 1 && getunitinfoObj.Data != null)
                                    {
                                        GsqUnitInfoModel unitmodel = JsonExtention.ToObject<GsqUnitInfoModel>(getunitinfoObj.Data);
                                        if (unitmodel != null)
                                        {
                                            if (unitmodel.data == null)
                                            {
                                                LogHelper.Info($"SSO:获取unit数据失败");
                                                obj.Tag = 0;
                                                obj.Message = $"获取单位信息失败";
                                                return obj;
                                            }
                                            if (unitmodel.data.schoolType == "幼儿园") //幼儿园、小学、初中、机构
                                            {
                                                obj.Tag = 0;
                                                obj.Message = $"您没有权限使用平台，单位类型为【{unitmodel.data.schoolType}】";
                                                return obj;
                                            }
                                        }

                                        UserThirdAuth tus = new UserThirdAuth();
                                        tus.ThirdUserId = userid;
                                        tus.Mobile = usermodel.data.sysUser.phone;
                                        tus.UserName = usermodel.data.sysUser.username;
                                        tus.RealName = usermodel.data.sysUser.name;
                                        string schoolProp = ""; //学段
                                        if (unitmodel.data.schoolType == "小学")
                                        {
                                            schoolProp = "1001001";
                                        }
                                        else if (unitmodel.data.schoolType == "初中")
                                        {
                                            schoolProp = "1001002";
                                        }
                                        else if (unitmodel.data.schoolType == "高中")
                                        {
                                            schoolProp = "1001003";
                                        }

                                        if (usertype == "2") //1为学生 2为教师 3为其他
                                        {
                                            tus.UnitType = UnitTypeEnum.School;
                                            tus.UnitCode = unitmodel.data.schoolId;
                                            tus.ParentUnitId = "Gsqjyj20240703";
                                            tus.UnitName = unitmodel.data.schoolName;
                                            tus.SchoolProp = schoolProp;
                                            tus.ThirdUnitId = unitmodel.data.schoolId;
                                            tus.RoleId = 31; //学科教师
                                        }
                                        else if (usertype == "3")
                                        {
                                            tus.UnitType = UnitTypeEnum.County;
                                            tus.ThirdUnitId = "Gsqjyj20240703";
                                            tus.UnitName = "姑苏区教育局";
                                            tus.UnitCode = "Gsqjyj20240703";
                                            tus.RoleId = 21; //区县教研员
                                        }

                                        LogHelper.Info($"SSO:保存信息:{JsonConvert.SerializeObject(tus)}", null);

                                        TData<UserEntity> userObj = await userThirdAuthBLL.SaveForm(tus);
                                        LogHelper.Info($"SSO:保存的结果:{JsonConvert.SerializeObject(userObj)}", null);
                                        if (userObj.Tag == 1)
                                        {
                                            //NetHelper.SetConfigHttpContext(HttpContext);
                                            userObj = await userBLL.CheckLogin(userObj.Data.UserName, userObj.Data.Password, (int)PlatformEnum.ThirdSSOApi, 3, ip: _netHelper.GetIp());
                                            LogHelper.Info($"登录成功返回:{JsonConvert.SerializeObject(obj)}");
                                            if (userObj.Tag == 1)
                                            {
                                                await new UserBLL().UpdateUser(userObj.Data);
                                                await Operator.Instance.AddCurrent(userObj.Data.ApiToken);
                                                obj.Data = await Operator.Instance.Current(userObj.Data.ApiToken);
                                            }
                                            obj.Tag = userObj.Tag;
                                            obj.Message = userObj.Message;
                                        }
                                        else
                                        {
                                            obj.Tag = 0;
                                            obj.Message = userObj.Message;
                                            return obj;
                                        }
                                    }
                                    else
                                    {
                                        LogHelper.Info($"SSO:未能获取到unit信息");
                                        obj.Tag = 0;
                                        obj.Message = "未能获取到单位信息";
                                        return obj;
                                    }
                                }
                                else
                                {
                                    LogHelper.Info($"SSO:未能获取到用户信息");
                                    obj.Tag = 0;
                                    obj.Message = "未能获取到用户信息";
                                    return obj;
                                }
                            }
                            else
                            {
                                LogHelper.Info($"SSO:获取userinfo接口数据失败");
                                obj.Tag = 0;
                                obj.Message = "获取用户信息失败";
                                return obj;
                            }
                        }
                    }
                    else
                    {
                        LogHelper.Info($"SSO:获取token失败:{JsonConvert.SerializeObject(gettokenObj)}");
                        obj.Tag = 0;
                        obj.Message = "获取token失败";
                        return obj;
                    }
                }
                else
                {
                    obj.Tag = 0;
                    obj.Message = "code不存在， 可能原因：登录超时，或不正确的访问方式。";
                    return obj;
                }
            }
            catch (WebException exp)
            {
                LogHelper.Info($"SSO:login_error:{exp.Message} {exp.StackTrace}");

                obj.Tag = 0;
                obj.Message = "认证失败，请重新认证！" + exp.Message;
                return obj;
            }

            LogHelper.Info($"登录成功返回:{JsonConvert.SerializeObject(obj)}");

            return obj;
        }

        /// <summary>
        /// 钉钉用户自动登录
        /// </summary>
        /// <param name="code">code</param>
        /// <returns></returns>
        [Route("ddlogin")]
        [HttpPost]
        public async Task<TData<OperatorInfo>> ddlogin(string code)
        {
            TData<OperatorInfo> obj = new TData<OperatorInfo>();
            try
            {
                LogHelper.Info($"SSO:ticket:{code}");

                var ThirdSSO = Configuration.GetSection("sso").Get<ThirdSSO>();

                TzSoftAcore.SetConfig(ThirdSSO);
                if (!string.IsNullOrEmpty(code)) //如果有code，获取用户Id
                {
                    DdTokenRequest accessTokenRequest = new DdTokenRequest
                    {
                        appKey = ThirdSSO.clientID,
                        appSecret = ThirdSSO.clientSecret,
                    };

                    //获取企业内部应用的accessToken,参考：https://open.dingtalk.com/document/orgapp/obtain-the-access_token-of-an-internal-app
                    var ddToken = await SendHelper.SendPostAsync<DdToken>($"{ThirdSSO.authHost}/v1.0/oauth2/accessToken", JsonConvert.SerializeObject(accessTokenRequest)).ConfigureAwait(false);

                    LogHelper.Info($"钉钉获取Token:{JsonConvert.SerializeObject(ddToken)}");
                    string accessToken = ddToken.accessToken;

                    //https://open.dingtalk.com/document/orgapp/obtain-the-userid-of-a-user-by-using-the-log-free
                    var ssoUserInfoRequest = new DdSsoUserInfoRequest
                    {
                        code = code
                    };
                    var userInfo = await SendHelper.SendPostAsync<DdUserGetUserInfoByCodeResponse>($"{ThirdSSO.dataHost}/topapi/v2/user/getuserinfo?access_token={accessToken}", JsonConvert.SerializeObject(ssoUserInfoRequest)).ConfigureAwait(false);
                    LogHelper.Info($"钉钉获取userInfo:{JsonConvert.SerializeObject(userInfo)}");

                    if (userInfo.errcode != 0)
                    {
                        obj.Tag = 0;
                        obj.Message = userInfo.errmsg;
                    }
                    else
                    {
                        var userDetail = await SendHelper.SendPostAsync<DdUserDetail>($"{ThirdSSO.dataHost}/topapi/v2/user/get?access_token={accessToken}", JsonConvert.SerializeObject(new { userid = userInfo.result.userid })).ConfigureAwait(false);
                        LogHelper.Info($"钉钉获取userInfo:{JsonConvert.SerializeObject(userDetail)}");
                        if (userDetail.errcode != 0)
                        {
                            obj.Tag = 0;
                            obj.Message = userDetail.errmsg;
                        }
                        else
                        {
                            var mobile = userDetail.result.mobile;
                            var name = userDetail.result.name;
                            //根据手机号码匹配账号并登录
                            var userResult = await userBLL.GetEntityByMobile(mobile).ConfigureAwait(true);

                            if (userResult.Tag == 1)
                            {
                                //NetHelper.SetConfigHttpContext(HttpContext);
                                var userCheckObj = await userBLL.CheckLogin(userResult.Data.UserName, userResult.Data.Password, (int)PlatformEnum.ThirdSSOApi, 3, ip: _netHelper.GetIp()).ConfigureAwait(true);
                                LogHelper.Info($"登录成功返回:{JsonConvert.SerializeObject(userCheckObj)}");
                                if (userCheckObj.Tag == 1)
                                {
                                    await Operator.Instance.AddCurrent(userCheckObj.Data.ApiToken).ConfigureAwait(false);
                                    obj.Data = await Operator.Instance.Current(userCheckObj.Data.ApiToken).ConfigureAwait(true);
                                }
                                obj.Tag = userCheckObj.Tag;
                                obj.Message = userCheckObj.Message;
                            }
                            else
                            {
                                obj.Tag = 0;
                                obj.Message = "您的账号不存在，无法登录";
                                return obj;
                            }
                        }
                    }
                }
                else
                {
                    obj.Tag = 0;
                    obj.Message = "code不存在， 可能原因：登录超时，或不正确的访问方式。";
                    return obj;
                }
            }
            catch (WebException exp)
            {
                LogHelper.Info($"钉钉免登失败:{exp.Message} {exp.StackTrace}");

                obj.Tag = 0;
                obj.Message = "钉钉免登失败！" + exp.Message;
                return obj;
            }

            LogHelper.Info($"登录成功返回:{JsonConvert.SerializeObject(obj)}");

            return obj;
        }

        /// <summary>
        /// 实验教学平台统一身份认证对接
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [Route("syjx")]
        [HttpPost]
        public async Task<TData<OperatorInfo>> Syjx(SyjxModel model)
        {
            LogHelper.Info($"------------>开始实验教学平台移动端统一身份认证对接", null);

            TData<OperatorInfo> obj = new TData<OperatorInfo>();
            try
            {
                LogHelper.Info($"body数据为:{JsonConvert.SerializeObject(model)}");
                TData<List<AppManageEntity>> listAppData = await appManagerBLL.GetList(new AppManageListParam() { ClientId = model.appId, AppType = 2, IsMain = 2 });
                if (listAppData.Total == 0)
                {
                    LogHelper.Info($"appId:{model.appId}传入有误，未查询到该appId对应信息。", null);
                    obj.Tag = 0;
                    obj.Message = $"appId:{model.appId}传入有误，未查询到该appId对应信息。";
                    return obj;
                }
                AppManageEntity objEntity = listAppData.Data.FirstOrDefault();
                if (objEntity != null)
                {
                    string strSgin = SecurityHelper.Base64Encrypt(SecurityHelper.MD5ToHex(model.userId + objEntity.ClientId + objEntity.ClientSecret + DateTime.Now.ToString("yyyyMMdd")));
                    TData<UserEntity> userEntity = await userBLL.GetEntity(long.Parse(model.userId));
                    if (userEntity.Tag == 1)
                    {
                        UserEntity objUserEntity = userEntity.Data;
                        if (objUserEntity.LastVisit.Value.AddMinutes(10) < DateTime.Now)
                        {
                            LogHelper.Info($"userId:{model.userId}-token:{model.token}-appId:{model.appId}签名已过有效期。", null);
                            obj.Tag = 0;
                            obj.Message = "签名已过有效期。";
                            return obj;
                        }

                        if (objUserEntity.ApiToken != strSgin)
                        {
                            LogHelper.Info($"userId:{model.userId}-token:{model.token}-appId:{model.appId}签名错误。", null);
                            obj.Tag = 0;
                            obj.Message = "签名错误。";
                            return obj;
                        }

                        //清空apiToken
                        objUserEntity.ApiToken = "";
                        await userBLL.UpdateUser(objUserEntity);
                        //NetHelper.SetConfigHttpContext(HttpContext);
                        TData<UserEntity> userObj = await userBLL.CheckLogin(objUserEntity.UserName, objUserEntity.Password, (int)PlatformEnum.ThirdSSOApi, 3, ip: _netHelper.GetIp());
                        LogHelper.Info($"登录成功返回:{JsonConvert.SerializeObject(userObj)}");
                        if (userObj.Tag == 1)
                        {
                            await new UserBLL().UpdateUser(userObj.Data);
                            await Operator.Instance.AddCurrent(userObj.Data.ApiToken);
                            obj.Data = await Operator.Instance.Current(userObj.Data.ApiToken);
                        }
                        obj.Tag = userObj.Tag;
                        obj.Message = userObj.Message;
                        //AuthHost配置具体跳转到H5的具体页面  junfeipage/working/index
                        if (!string.IsNullOrEmpty(objEntity.AuthHost))
                        {
                            obj.ExtendData = SecurityHelper.Base64Encrypt(objEntity.AuthHost);
                        }
                        else
                        {
                            obj.ExtendData = "";
                        }
                    }
                }
            }
            catch (WebException ex)
            {
                LogHelper.Info($"SSO:login_error:{ex.Message} {ex.StackTrace}");
                obj.Tag = 0;
                obj.Message = "认证失败，请重新认证！" + ex.Message;
                return obj;
            }
            return obj;
        }

        #region 无锡市智慧教育

        [Route("authwxs")]
        public async Task<ActionResult<TData<OperatorInfo>>> AuthWXS()
        {
            TData<OperatorInfo> objResult = new TData<OperatorInfo>();

            string code = Request.Query["code"];

            TData<List<AppManageEntity>> obj = await appManagerBLL.GetList(new AppManageListParam() { AppCode = "NER5aHNaWWI5dG1EQ291VQ", AppType = 1, IsMain = 2 });
            if (obj.Total == 0)
            {
                throw new BusinessException("请联系运维人员在【第三方应用管理】->【第三方应用列表】中配置相关内容。");
            }
            AppManageEntity objEntity = obj.Data.FirstOrDefault();

            if (string.IsNullOrEmpty(code))
            {
                throw new BusinessException("非法访问");
            }

            try
            {
                var _iwxzhjyApi = await GetWxszhjyAuthCodeApiService(code, objEntity);
                var userInfoResponse = await _iwxzhjyApi.GetUserInfo();
                var userInfo = userInfoResponse.Data;

                UserThirdAuth tus = new()
                {
                    ThirdUserId = userInfo.Uuid,
                    UserName = userInfo.Name,
                    RealName = userInfo.Name,
                    Mobile = userInfo.Mobile,
                };

                if (!string.IsNullOrEmpty(userInfo.SchoolCode))
                {
                    tus.UnitType = UnitTypeEnum.School;
                    tus.UnitCode = userInfo.SchoolCode;
                    tus.ThirdUnitId = userInfo.SchoolCode;
                    tus.RoleId = userInfo.IfAppAdmin != 0 || userInfo.IfMain != 0 ? 30 : 31;
                    var schoolInfo = await _iwxzhjyApi.GetSchoolInfo(userInfo.SchoolCode);
                    tus.UnitName = schoolInfo.Data.Name;
                    tus.ParentUnitId = schoolInfo.Data.OrganCode;
                }
                if (!string.IsNullOrEmpty(userInfo.OrganCode))
                {
                    tus.UnitType = UnitTypeEnum.County;
                    tus.UnitCode = userInfo.OrganCode;
                    tus.ThirdUnitId = userInfo.OrganCode;
                    tus.RoleId = userInfo.IfAppAdmin != 0 || userInfo.IfMain != 0 ? 20 : 21;
                    var organInfo = await _iwxzhjyApi.GetOrganInfo(userInfo.OrganCode);
                    tus.ParentUnitId = organInfo.Data.PCode;
                    tus.UnitName = organInfo.Data.Name;
                    if (string.IsNullOrEmpty(tus.ParentUnitId))
                    {
                        tus.UnitCode = "SS" + userInfo.OrganCode;
                        tus.ThirdUnitId = "SS" + userInfo.OrganCode;
                        tus.ParentUnitId = userInfo.OrganCode;
                        tus.UnitName = "无锡市市属学校";
                        tus.IsMultUnit = true;
                        await userThirdAuthBLL.SaveForm(tus);

                        var cityUser = tus;
                        cityUser.UnitType = UnitTypeEnum.City;
                        cityUser.ThirdUnitId = userInfo.OrganCode;
                        cityUser.UnitCode = userInfo.OrganCode;
                        cityUser.UnitName = organInfo.Data.Name;
                        cityUser.ParentUnitId = "";
                        if (tus.RoleId == 20)
                        {
                            cityUser.RoleId = 10;
                        }

                        tus = cityUser;
                    }
                }

                var userObj = await userThirdAuthBLL.SaveForm(tus);

                var user = await userBLL.GetEntity(userObj.Data.Id.Value);
                var loginData = await userBLL.CheckLogin(userObj.Data.UserName, userObj.Data.Password, (int)PlatformEnum.ThirdSSOApi, 3, ip: _netHelper.GetIp());

                if (loginData.Tag == 1)
                {
                    await new UserBLL().UpdateUser(loginData.Data);
                    await Operator.Instance.AddCurrent(loginData.Data.ApiToken);
                    objResult.Data = await Operator.Instance.Current(loginData.Data.ApiToken);
                    objResult.Tag = 1;
                    return Ok(objResult);
                }
                else
                {
                    throw new BusinessException(loginData.Message);
                }
            }
            catch (ApiException ex)
            {
                LogHelper.Error($"---->从【无锡市智慧教育】获取数据失败，状态码{ex.StatusCode}，失败请求为:{ex.Uri}，{ex.StackTrace}", null);
                throw new BusinessException($"从【无锡市智慧教育】获取数据失败。" + ex.Message);
            }
            catch (Exception ex)
            {
                LogHelper.Error($"---->从【无锡市智慧教育】SSO失败，错误信息为:{ex.Message} {ex.StackTrace}", null);
                throw new BusinessException($"从【无锡市智慧教育】SSO失败。" + ex.Message);
            }
        }

        private async Task<IWxzhjyApiServers> GetWxszhjyAuthCodeApiService(string code, AppManageEntity objEntity)
        {
            if (objEntity == null)
            {
                TData<List<AppManageEntity>> obj = await appManagerBLL.GetList(new AppManageListParam() { AppCode = "NER5aHNaWWI5dG1EQ291VQ", AppType = 2, IsMain = 2 });
                if (obj.Total == 0)
                {
                    throw new BusinessException("请联系运维人员在【第三方应用管理】->【第三方应用列表】中配置相关内容。");
                }
                objEntity = obj.Data.FirstOrDefault();
            }

            var tokenQueryParams = new Dictionary<string, string>
                {
                    { "grant_type", "authorization_code" },
                    { "code",code },
                    { "redirect_uri", objEntity.CallBackUrl },
                };

            LogHelper.Info($"---->获取token请求参数信息为：{JsonConvert.SerializeObject(tokenQueryParams)}");

            var requestTokenUri = $"/api/oauth/token";
            var content = new FormUrlEncodedContent(tokenQueryParams);

            using var httpClient = new HttpClient(new HttpClientBusinessHandler("获取token失败。"));
            httpClient.BaseAddress = new Uri(objEntity.AuthHost);
            var credentials = $"{objEntity.ClientId}:{objEntity.ClientSecret}";
            var base64Credentials = Convert.ToBase64String(Encoding.ASCII.GetBytes(credentials));
            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", base64Credentials);
            var response = await httpClient.PostAsync(requestTokenUri, content);
            var tokenResponseJson = await response.Content.ReadAsStringAsync();
            var tokenResponse = JsonConvert.DeserializeObject<AuthTokenResponse>(tokenResponseJson);

            CacheFactory.Cache.SetCache($"{code}_token", tokenResponse.AccessToken, DateTime.Now.AddMinutes(5));
            SetSession("wxs_third_sysendmsg_token", tokenResponse.AccessToken);//无锡市统一身份认证，实验预约后给实验员推送消息token
            var _iwxzhjyApi = RestService.For<IWxzhjyApiServers>(new HttpClient(new WxzhjyApiHandler(_httpContextAccessor))
            {
                BaseAddress = new Uri(objEntity.AuthHost)
            }, new RefitSettings
            {
                ContentSerializer = new NewtonsoftJsonContentSerializer()
            });

            return _iwxzhjyApi;
        }

        #endregion 无锡市智慧教育

        /// <summary>
        /// 设置Session
        /// </summary>
        /// <param name="key">键</param>
        /// <param name="value">值</param>
        protected void SetSession(string key, string value)
        {
            HttpContext.Session.SetString(key, value);
        }
    }
}