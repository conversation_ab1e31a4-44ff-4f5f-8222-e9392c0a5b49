﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.SystemManage;
using Dqy.Syjx.Model.Param.SystemManage;

namespace Dqy.Syjx.Service.SystemManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-12-24 09:38
    /// 描 述：服务类
    /// </summary>
    public class DictionaryRelationService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<DictionaryRelationEntity>> GetList(DictionaryRelationListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<DictionaryRelationEntity>> GetPag
        }

        public async Task<List<DictionaryRelationEntity>> GetList(DictionaryRelationListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<DictionaryRelationEntity>> GetPageList(DictionaryRelationListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<DictionaryRelationEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<DictionaryRelationEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(DictionaryRelationEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(DictionaryRelationEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
               strSql = $" update sys_dictionary_relation set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
               strSql = $"update sys_dictionary_relation set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<DictionaryRelationEntity, bool>> ListFilter(DictionaryRelationListParam param)
        {
            var expression = LinqExtensions.True<DictionaryRelationEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (!param.DictionaryId.IsNullOrZero())
                {
                    expression = expression.And(t => t.DictionaryId == param.DictionaryId);
                }
                if (!param.DictionaryToId.IsNullOrZero())
                {
                    expression = expression.And(t => t.DictionaryToId == param.DictionaryToId);
                }
                if (!param.RelationType.IsNullOrZero())
                {
                    expression = expression.And(t => t.RelationType == param.RelationType);
                }
            }
            return expression;
        }
        #endregion
    }
}

