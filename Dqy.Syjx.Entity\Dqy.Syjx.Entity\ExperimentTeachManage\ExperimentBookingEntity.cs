﻿using System;
using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations.Schema;
using Dqy.Syjx.Util;
using System.Collections.Generic;
using System.ComponentModel;
using Dqy.Syjx.Util.Extension;
using NPOI.SS.UserModel;
using Microsoft.EntityFrameworkCore.ChangeTracking;

namespace Dqy.Syjx.Entity.ExperimentTeachManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-12-14 11:28
    /// 描 述：实验预约实体类
    /// </summary>
    [Table("ex_ExperimentBooking")]
    public class ExperimentBookingEntity : BaseExtensionEntity
    {
        /// <summary>
        /// 学年起始年度
        /// </summary>
        /// <returns></returns>
        [Description("学年起始年度")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 10)]
        public int SchoolYearStart { get; set; }

        /// <summary>
        /// 学年终止年度
        /// </summary>
        /// <returns></returns>
        [Description("学年终止年度")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 10)]
        public int SchoolYearEnd { get; set; }

        /// <summary>
        /// 学期Id
        /// </summary>
        /// <returns></returns>
        public int SchoolTerm { get; set; }

        /// <summary>
        /// 学年起始年度（统计）
        /// </summary>
        /// <returns></returns>
        public int? StaticSchoolYearStart { get; set; }

        /// <summary>
        /// 学年终止年度（统计）
        /// </summary>
        /// <returns></returns>
        public int? StaticSchoolYearEnd { get; set; }

        /// <summary>
        /// 学期（统计）
        /// </summary>
        /// <returns></returns>
        [Description("学期")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 15)]
        public int? StaticSchoolTerm { get; set; }

        /// <summary>
        ///年级Id（统计）
        /// </summary>
        /// <returns></returns>
        public int? StaticGradeId { get; set; }

        /// <summary>
        /// 学期名称
        /// </summary>
        [NotMapped]
        [Description("学期")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 20)]
        public string SchoolTermName { get; set; }

        /// <summary>
        /// 学校Id
        /// </summary>
        /// <returns></returns>
        [JsonConverter(typeof(StringJsonConverter))]
        public long SchoolId { get; set; }
        /// <summary>
        /// 班级
        /// </summary>
        /// <returns></returns>
        [JsonConverter(typeof(StringJsonConverter))]
        public long SchoolGradeClassId { get; set; }
        /// <summary>
        /// 年级
        /// </summary>
        /// <returns></returns>
        public int GradeId { get; set; }
        /// <summary>
        /// 班级Id
        /// </summary>
        /// <returns></returns>
        public int ClassId { get; set; }
        /// <summary>
        /// 学科
        /// </summary>
        /// <returns></returns>
        public int CourseId { get; set; }
        /// <summary>
        /// 实验来源(1：实验计划；2：实验目录)
        /// </summary>
        /// <returns></returns>
        public int SourceType { get; set; }

        /// <summary>
        /// 实验来源名称(1：实验计划；2：实验目录)
        /// </summary>
        [NotMapped]
        [Description("实验来源")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 15)]
        public string SourceTypeName { get; set; }

        /// <summary>
        /// 实验计划Id（当前实验教材版本Id）
        /// </summary>
        /// <returns></returns>
        [JsonConverter(typeof(StringJsonConverter))]
        public long PlanInfoId { get; set; }

        /// <summary>
        /// 计划班级Id集合
        /// </summary>
        [NotMapped]
        public string PlanClassIdz { get; set; }
        /// <summary>
        /// 实验计划详细Id（实验目录清单库）
        /// </summary>
        /// <returns></returns>
        [JsonConverter(typeof(StringJsonConverter))]
        public long PlanDetailId { get; set; }
        /// <summary>
        /// TextbookVersionDetailId
        /// </summary>
        /// <returns></returns>
        [JsonConverter(typeof(StringJsonConverter))]
        public long TextbookVersionDetailId { get; set; }
        /// <summary>
        /// TextbookVersionDetailId
        /// </summary>
        /// <returns></returns>
        [JsonConverter(typeof(StringJsonConverter))]
        public long TextbookVersionCurrentId { get; set; }
        /// <summary>
        /// 实验名称
        /// </summary>
        /// <returns></returns>
        [Description("实验名称")]
        [ExportExcelAttribute(HorizontalAlignment.Left, 30)]
        public string ExperimentName { get; set; }
        /// <summary>
        /// 实验类型
        /// </summary>
        /// <returns></returns>
        public int? ExperimentType { get; set; }

        /// <summary>
        /// 实验要求实验
        /// </summary>
        public int? IsNeedDo { get; set; }
        /// <summary>
        /// 实验类型名称
        /// </summary>
        [NotMapped]
        [Description("实验类型")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 10)]
        public string ExperimentTypeName { get; set; }

        /// <summary>
        /// 分组数
        /// </summary>
        /// <returns></returns>
        [Description("分组数")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 10)]
        public int? Groupz { get; set; }
        /// <summary>
        /// 上课时间
        /// </summary>
        /// <returns></returns>
        [JsonConverter(typeof(DateTimeJsonConverter))]
        [Description("上课时间")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 15)]
        public DateTime? ClassTime { get; set; }
        /// <summary>
        /// 节次（字典1050）
        /// </summary>
        /// <returns></returns>
        [JsonConverter(typeof(StringJsonConverter))]
        public long SectionId { get; set; }
        /// <summary>
        /// 节次显示（字典1050DicName）
        /// </summary>
        /// <returns></returns>
        [Description("上课节次")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 25)]
        public string SectionShow { get; set; }
        /// <summary>
        /// 上课地点(功能室Id)
        /// </summary>
        /// <returns></returns>
        [JsonConverter(typeof(StringJsonConverter))]
        public long? FunRoomId { get; set; }
        /// <summary>
        /// 所需仪器
        /// </summary>
        /// <returns></returns>
        public string EquipmentNeed { get; set; }
        /// <summary>
        /// 实验材料试剂
        /// </summary>
        /// <returns></returns>
        public string MaterialNeed { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        /// <returns></returns>
        public string Remark { get; set; }
        /// <summary>
        /// 状态（0：实验预约中 10：等待实验安排  11：实验安排退回  20：等待登记实验  100：实验结束）
        /// </summary>
        /// <returns></returns>
        public int Statuz { get; set; }
        /// <summary>
        /// 安排人Id
        /// </summary>
        /// <returns></returns>
        [JsonConverter(typeof(StringJsonConverter))]
        public long? ArrangerId { get; set; }
        /// <summary>
        /// 安排时间
        /// </summary>
        /// <returns></returns>
        [JsonConverter(typeof(DateTimeJsonConverter))]
        public DateTime? ArrangerTime { get; set; }
        
        /// <summary>
        /// 是否主节点数据
        /// </summary>
        public int? IsMain { get; set; }
        /// <summary>
        /// 父级Id
        /// </summary>
        [JsonConverter(typeof(StringJsonConverter))]
        public long? Pid { get; set; }
        /// <summary>
        /// 多班级Id
        /// </summary>
        public string SchoolGradeClassIdz { get; set; }
        /// <summary>
        /// 班级名称
        /// </summary>
        public string ClassName { get; set; }

        /// <summary>
        /// 来源路径（1：实验目录；2：校本实验）
        /// </summary>
        /// <returns></returns>
        public int? SourcePath { get; set; }
        /// <summary>
        /// 校本实验Id
        /// </summary>
        [JsonConverter(typeof(StringJsonConverter))]
        public long? SchoolExperimentId { get; set; }
        /// <summary>

        /// 当前操作人名称
        /// </summary>
        [NotMapped]
        public string RealName { get; set; }
        /// <summary>
        /// 记录模式（1：预约模式；2：简易模式）
        /// </summary>
        /// <returns></returns>
        public int RecordMode { get; set; }
        /// <summary>
        /// 安排意见
        /// </summary>
        public string ArrangerRemark { get; set; }
        /// <summary>
        /// 安排所需仪器
        /// </summary>
        public string ArrangerEquipmentNeed { get; set; }
        /// <summary>
        /// 安排实验材料试剂
        /// </summary>
        public string ArrangerMaterialNeed { get; set; }
        /// <summary>
        /// 是否借出仪器
        /// </summary>
        public int? IsEquipmentLend { get; set; }
        /// <summary>
        /// 学段
        /// </summary>
        public int SchoolStage { get; set; }
        /// <summary>
        /// 实验Id
        /// </summary>
        [JsonConverter(typeof(StringJsonConverter))]
        public long? ExperimentId { get; set; }
        
        /// <summary>
        /// 学科节次序号
        /// </summary>
        public int? SectionIndex { get; set; }
        /// <summary>
        /// 学科节次开始时间
        /// </summary>
        public string SectionBgnTime { get; set; }
        /// <summary>
        /// 学科节次结束时间
        /// </summary>
        public string SectionEndTime { get; set; }

        /// <summary>
        /// 课程活动类型(1: 课内 2:课外)
        /// </summary>
        public int ActivityType { get; set; } = 1;

        /// <summary>
        /// 是否评价
        /// </summary>
        /// <returns></returns>
        public int? IsExamine { get; set; } = 0;
        /// <summary>
        /// 登记时间
        /// </summary>
        /// <returns></returns>
        [JsonConverter(typeof(DateTimeJsonConverter))]
        public DateTime? RecordTime { get; set; }
        /// <summary>
        /// 上课开始时间
        /// </summary>
        /// <returns></returns>
        [JsonConverter(typeof(DateTimeJsonConverter))]
        public DateTime? ClassBeginTime { get; set; }
        /// <summary>
        /// 上课结束时间
        /// </summary>
        /// <returns></returns>
        [JsonConverter(typeof(DateTimeJsonConverter))]
        public DateTime? ClassEndTime { get; set; }
        /// <summary>
        /// 年级名称
        /// </summary>
        [NotMapped]
        [Description("年级")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 10)]
        public string GradeName { get; set; }

        /// <summary>
        /// 班级
        /// </summary>
        [NotMapped]
        [Description("班级")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 15)]
        public string ClassDesc { get; set; }

        /// <summary>
        /// 格式化后的班级名称
        /// </summary>
        [NotMapped]
        [Description("班级")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 10)]
        public string ClassDescName { get; set; }
        /// <summary>
        /// 上课时间
        /// </summary>
        /// <returns></returns>
        [NotMapped]
        [JsonConverter(typeof(DateTimeJsonConverter))]
        [Description("上课时间")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 20)]
        public string ClassTimeSection { get; set; }
        /// <summary>
        /// 学科名称
        /// </summary>
        [NotMapped]
        [Description("学科")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 10)]
        public string CourseName { get; set; }

        /// <summary>
        /// 上课地点
        /// </summary>
        [NotMapped]
        [Description("上课地点")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 20)]
        public string FunRoomName { get; set; }

        /// <summary>
        /// 预约人
        /// </summary>
        [NotMapped]
        [JsonConverter(typeof(StringJsonConverter))]
        public long? BookUserId { get; set; }
        /// <summary>
        /// 预约人名称
        /// </summary>
        [NotMapped]
        public string BookUserName { get; set; }
        /// <summary>
        /// 登记人
        /// </summary>
        [NotMapped]
        [JsonConverter(typeof(StringJsonConverter))]
        public long? RecordUserId { get; set; }
        /// <summary>
        /// 登记人名称
        /// </summary>
        [NotMapped]
        [Description("登记人")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 12)]
        public string RecordUserName { get; set; }

        /// <summary>
        /// 实验登记Id
        /// </summary>
        [NotMapped]
        [JsonConverter(typeof(StringJsonConverter))]
        public long? ExperimentRecordId { get; set; }
        /// <summary>
        /// 实验总结
        /// </summary>
        [NotMapped]
        public string ExperimentSummary { get; set; }
        /// <summary>
        /// 运行状态
        /// </summary>
        [NotMapped]
        public int? RunStatuz { get; set; }
        /// <summary>
        /// 问题描述
        /// </summary>
        [NotMapped]
        public string ProblemDesc { get; set; }
        /// <summary>
        /// 登记现场照片
        /// </summary>
        [NotMapped]
        public List<BusinessManage.AttachmentEntity> AttachmentList { get; set; }
        /// <summary>
        /// 学校名称
        /// </summary>
        [NotMapped]
        [Description("单位名称")]
        [ExportExcelAttribute(HorizontalAlignment.Left, 30)]
        public string SchoolName { get; set; }
        /// <summary>
        /// 学段名称
        /// </summary>
        [NotMapped]
        [Description("学段")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 10)]
        public string SchoolStageName { get; set; }

        /// <summary>
        /// 安排人(预约的查看，FunRoomId = 1)
        /// </summary>
        [NotMapped]
        public string ArrangerName { get; set; }
        /// <summary>
        /// 是否关联摄像头
        /// </summary>
        [NotMapped]
        public int IsRelationCamera { get; set; }

        /// <summary>
        /// 实验要求实验
        /// </summary>
        [NotMapped]
        [Description("实验要求")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 10)]
        public string IsNeedDoName { get; set; }

        /// <summary>
        /// 市级Id
        /// </summary>
        [JsonConverter(typeof(StringJsonConverter))]
        [NotMapped]
        public long CityId { get; set; }

        /// <summary>
        /// 区县Id
        /// </summary>
        [JsonConverter(typeof(StringJsonConverter))]
        [NotMapped]
        public long CountyId { get; set; }

        /// <summary>
        /// 区县名称
        /// </summary>
        [NotMapped]
        [Description("区县名称")]
        [ExportExcelAttribute(HorizontalAlignment.Left, 30)]
        public string CountyName { get; set; }


        /// <summary>
        /// 演示必做实验数量
        /// </summary>
        [NotMapped]
        [Description("必做演示")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 10)]
        public int NeedShowNum { get; set; }

        [NotMapped]
        public int NeedShowNumed { get; set; }

        /// <summary>
        /// 分组必做实验数量
        /// </summary>
        [NotMapped]
        [Description("必做分组")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 10)]
        public int NeedGroupNum { get; set; }

        [NotMapped]
        public int NeedGroupNumed { get; set; }

        /// <summary>
        /// 演示选做实验数量
        /// </summary>
        [NotMapped]
        [Description("选做演示")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 10)]
        public int OptionalShowNum { get; set; }
        
        [NotMapped]
        public int OptionalShowNumed { get; set; }

        /// <summary>
        /// 分组选做实验数量
        /// </summary>
        [NotMapped]
        [Description("选做分组")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 10)]
        public int OptionalGroupNum { get; set; }

        [NotMapped]
        public int OptionalGroupNumed { get; set; }

        /// <summary>
        /// 数量小计
        /// </summary>
        [NotMapped]
        [Description("小计")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 10)]
        public int AllNum { get; set; }

        [NotMapped]
        public int AllNumed { get; set; }

        /// <summary>
        /// 必做演示实验开出率
        /// </summary>
        [NotMapped]
        public decimal NeedShowNumRatio { get; set; }
        /// <summary>
        /// 必做分组实验开出率
        /// </summary>
        [NotMapped]
        public decimal NeedGroupNumRatio { get; set; }
        /// <summary>
        /// 选做演示实验开出率
        /// </summary>
        [NotMapped]
        public decimal OptionalShowNumRatio { get; set; }
        /// <summary>
        /// 选做分组实验开出率
        /// </summary>
        [NotMapped]
        public decimal OptionalGroupNumRatio { get; set; }
        /// <summary>
        /// 小计开出率
        /// </summary>
        [NotMapped]
        public decimal TotalRatio { get; set; }

        /// <summary>
        /// 子数据集合
        /// </summary>
        [NotMapped]
        public List<ExperimentBookingEntity> ChildList { get; set; }
        /// <summary>
        /// 版本名称
        /// </summary>
        [NotMapped]
        public string VersionName { get; set; }

        /// <summary>
        /// 班级集合
        /// </summary>
        [NotMapped]
        public List<ExperimentBookingEntity> ClassList { get; set; }

        /// <summary>
        /// 仪器集合
        /// </summary>
        [NotMapped]
        public List<ExperimentInstrumentEntity> InstrumentList { get; set; }

        /// <summary>
        /// 实验员列表
        /// </summary>
        [NotMapped]
        public List<object> ExperimentUserList { get; set; }

        /// <summary>
        /// 周 ：周几
        /// </summary>
        [NotMapped]
        public string WeekName { get; set; }

        /// <summary>
        /// 周次显示：第几周
        /// </summary>
        [NotMapped]
        public string WeekNum { get; set; }

        /// <summary>
        /// 实验室负责人
        /// </summary>
        [NotMapped]
        public long SafeguardUserId { get; set; }

        /// <summary>
        /// 是否图片必传
        /// </summary>
        [NotMapped]
        public int IsImageValidation { get; set; }

        /// <summary>
        /// 是否汇总信息必填
        /// </summary>
        [NotMapped]
        public int IsSummaryValidation { get; set; }

        /// <summary>
        /// 数据列表
        /// </summary>
        [NotMapped]
        public List<object> ObjectList { get; set; }
        /// <summary>
        /// 实验配置的所需仪器
        /// </summary>
        [NotMapped]
        public string TagMaterialNeed { get; set; }
        /// <summary>
        /// 实验配置的所需耗材
        /// </summary>
        [NotMapped]
        public string TagEquipmentNeed { get; set; }

        /// <summary>
        /// 必修、选择性必修
        /// </summary>
        [NotMapped]
        public int? CompulsoryType { get; set; }
        /// <summary>
        /// 是否考核
        /// </summary>
        [NotMapped]
        public int IsExam { get; set; }
        /// <summary>
        /// 是否可登记
        /// </summary>
        [NotMapped]
        public int IsAllowRecord { get; set; }

        /// <summary>
        /// 实验课程发布表Id
        /// </summary>
        /// <returns></returns>
        [JsonConverter(typeof(StringJsonConverter))]
        public long? ExperimentPublishId { get; set; }

        /// <summary>
        /// 实验id集合
        /// </summary>
        [NotMapped]
        public string ExperimentIds { get; set; }
        /// <summary>
        /// 课程节次
        /// </summary>
        [NotMapped]
        public string Chapter { get; set; }
    }

    /// <summary>
    /// 实验人员实体
    /// </summary>
    public class ExperimentPer
    {
        [JsonConverter(typeof(StringJsonConverter))]
        public long id { get; set; }

        public string name { get; set; }
    }
}
