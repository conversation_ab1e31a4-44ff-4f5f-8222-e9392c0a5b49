﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.ArticleManager;
using Dqy.Syjx.Model.Param.ArticleManager;

namespace Dqy.Syjx.Service.ArticleManager
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2022-01-21 10:43
    /// 描 述：服务类
    /// </summary>
    public class ArticleContextService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<ArticleContextEntity>> GetList(ArticleContextListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<ArticleContextEntity>> GetPageList(ArticleContextListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<ArticleContextEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<ArticleContextEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(ArticleContextEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(ArticleContextEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update d_ArticleContext set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
                strSql = $"update d_ArticleContext set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<ArticleContextEntity, bool>> ListFilter(ArticleContextListParam param)
        {
            var expression = LinqExtensions.True<ArticleContextEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }

                if (param.ArticleId != null)
                {
                    expression = expression.And(t => t.ArticleId == param.ArticleId);
                }

                if(param.UnitId > 0)
                {
                    expression = expression.And(t => t.UnitId == param.UnitId);
                }
            }
            return expression;
        }
        #endregion
    }
}
