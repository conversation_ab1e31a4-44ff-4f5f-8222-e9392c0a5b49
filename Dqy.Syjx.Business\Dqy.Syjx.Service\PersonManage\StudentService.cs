﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.PersonManage;
using Dqy.Syjx.Model.Param.PersonManage;
using Dqy.Syjx.Entity.ExperimentTeachManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Model.Param.ExperimentTeachManage;
using Dqy.Syjx.Service.OrganizationManage;

namespace Dqy.Syjx.Service.PersonManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2024-10-29 15:24
    /// 描 述：学生管理服务类
    /// </summary>
    public class StudentService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<StudentEntity>> GetList(StudentListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseReposi
        }

        public async Task<List<StudentEntity>> GetList(StudentListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<StudentEntity>> GetPageList(StudentListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<StudentEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<StudentEntity>(id);
        }

        public async Task<StudentEntity> GetEntityByUserId(long userId)
        {
            return await this.BaseRepository().FindEntity<StudentEntity>(f => f.UserId == userId);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(StudentEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(StudentEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }
        public async Task UpdateForm(StudentEntity entity, List<string> fields, Repository db)
        {
            if (fields != null)
            {
                await entity.Modify();
                fields.Add("BaseModifyTime");
                fields.Add("BaseModifierId");
            }
            if (db == null)
            {
                await this.BaseRepository().Update(entity, fields);
            }
            else
            {
                await db.Update(entity, fields);
            }
        }
        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update pm_Student set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
                strSql = $"update pm_Student set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<StudentEntity, bool>> ListFilter(StudentListParam param)
        {
            var expression = LinqExtensions.True<StudentEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (!string.IsNullOrEmpty(param.StudentName))
                {
                    expression = expression.And(t => t.StudentName == param.StudentName);
                }
                if (!string.IsNullOrEmpty(param.IDCard))
                {
                    expression = expression.And(t => t.IDCard == param.IDCard);
                }
                if (param.GradeId> 0)
                {
                    expression = expression.And(t => t.GradeId == param.GradeId);
                }
                if (param.SchoolId > 0)
                {
                    expression = expression.And(t => t.SchoolId == param.SchoolId);
                }
                if (param.SchoolGradeClassId > 0)
                {
                    expression = expression.And(t => t.SchoolGradeClassId == param.SchoolGradeClassId);
                }
            }
            return expression;
        }


        /// <summary>
        /// 实验预约查询
        /// </summary>
        /// <param name="param">查询参数</param>
        /// <param name="pagination">分页参数</param>
        /// <returns></returns>
        public async Task<List<StudentEntity>> GetList(StudentListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            var parameter = new List<DbParameter>();

            strSql.Append(@$" SELECT *  From ( 
                            SELECT s2.*
                            ,sg1.SchoolStage
                            ,sg1.GradeId
                            ,sg1.ClassDesc AS ClassName
                            ,sg1.StartYear
                            ,sg1.IsGraduate
                            ,d4.DicName AS SchoolStageName
                            ,c3.DicName as GradeName
                            FROM up_SchoolGradeClass AS sg1
                            INNER JOIN up_Student AS s2 ON s2.BaseIsDelete = 0 and sg1.Id = s2.SchoolGradeClassId   
                            INNER JOIN  sys_static_dictionary AS d4 ON sg1.SchoolStage = d4.DictionaryId 
                            INNER JOIN  sys_static_dictionary AS c3 ON sg1.GradeId = c3.DictionaryId
                          ) as T WHERE  BaseIsDelete = 0 ");
            if (param != null)
            {
                if (!param.Id.IsNullOrZero())
                {
                    strSql.Append($" AND Id = {param.Id}");
                }
                if (!param.SchoolId.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolId = {param.SchoolId}");//根据用户登录的单位ID进行查询
                }
                if (!param.IDCard.IsNullOrZero())
                {
                    strSql.Append($" AND IDCard like '%{param.IDCard}%' ");//根据用户登录的单位ID进行查询
                }
                if (!param.StudentName.IsNullOrZero())
                {
                    strSql.Append($" AND StudentName like '%{param.StudentName}%' ");//根据用户登录的单位ID进行查询
                }
                if (!param.SchoolGradeClassId.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolGradeClassId = {param.SchoolGradeClassId} "); //年级班级Id
                }
                if (param.GradeId > 0)
                {
                    strSql.Append($" AND GradeId = {param.GradeId} ");
                }
                if (param.StartYear > 0)
                {
                    strSql.Append($" AND StartYear = {param.StartYear} ");
                }
                if (param.SchoolStageId > 0)
                {
                    strSql.Append($" AND SchoolStage = {param.SchoolStageId} ");
                }
                if (param.IsGraduate.HasValue && param.IsGraduate != -10000 && param.IsGraduate != -1)
                {
                    strSql.Append($" AND IsGraduate = {param.IsGraduate} ");
                }
            }
            var list = await this.BaseRepository().FindList<StudentEntity>(strSql.ToString(), parameter.ToArray(), pagination);
            return list.ToList();
        }

        #endregion
    }
}

