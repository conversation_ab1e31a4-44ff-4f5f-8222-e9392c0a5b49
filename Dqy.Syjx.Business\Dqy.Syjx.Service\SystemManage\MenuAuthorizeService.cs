using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.SystemManage;
using Dqy.Syjx.Model.Param.SystemManage;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;

namespace Dqy.Syjx.Service.SystemManage
{
    public class MenuAuthorizeService : RepositoryFactory
    {
        #region 获取数据
        public async Task<List<MenuAuthorizeEntity>> GetList(MenuAuthorizeEntity param)
        {
            var expression = LinqExtensions.True<MenuAuthorizeEntity>();
            if (param != null)
            {
                if (param.AuthorizeId.ParseToLong() > 0)
                {
                    expression = expression.And(t => t.AuthorizeId == param.AuthorizeId);
                }
                if (param.AuthorizeType.ParseToInt() > 0)
                {
                    expression = expression.And(t => t.AuthorizeType == param.AuthorizeType);
              
        }

        public async Task<List<MenuAuthorizeEntity>> GetList(MenuAuthorizeEntity param)
        {
            var expression = LinqExtensions.True<MenuAuthorizeEntity>();
            if (param != null)
            {
                if (param.AuthorizeId.ParseToLong() > 0)
                {
                    expression = expression.And(t => t.AuthorizeId == param.AuthorizeId);
                }
                if (param.AuthorizeType.ParseToInt() > 0)
                {
                    expression = expression.And(t => t.AuthorizeType == param.AuthorizeType);
                }
                if (!param.AuthorizeIds.IsEmpty())
                {
                    long[] authorizeIdArr = TextHelper.SplitToArray<long>(param.AuthorizeIds, ',');
                    expression = expression.And(t => authorizeIdArr.Contains(t.AuthorizeId.Value));
                }
            }
            var list = await this.BaseRepository().FindList<MenuAuthorizeEntity>(expression);
            return list.ToList();
        }

        public async Task<List<MenuAuthorizeEntity>> GetMenuAuthorizeList(MenuAuthorizeEntity param)
        {
            StringBuilder sql = new StringBuilder();
            sql.Append(@$" select a.ID,a.BaseCreateTime,a.BaseCreatorId,a.MenuId,a.AuthorizeId,a.AuthorizeType 
                            from SysMenuAuthorize a inner join SysMenu m on a.MenuId = m.id AND 1=1");
            if (param != null)
            {
                if (param.AuthorizeId.ParseToLong() > 0)
                {
                    sql.Append($" AND AuthorizeId = {param.AuthorizeId}");
                }
                if (param.AuthorizeType.ParseToInt() > 0)
                {
                    sql.Append($" AND AuthorizeType = {param.AuthorizeType}");
                }
            }

            var list = await this.BaseRepository().FindList<MenuAuthorizeEntity>(sql.ToString());
            return list.ToList();
        }

        public async Task<List<MenuAuthorizeEntity>> GetListByParam(MenuAuthorizeListParam param)
        {
            var expression = LinqExtensions.True<MenuAuthorizeEntity>();
            if (param != null)
            {
                if (param.MenuId > 0)
                {
                    expression = expression.And(t => t.MenuId == param.MenuId);
                }
                if (param.AuthorizeId > 0)
                {
                    expression = expression.And(t => t.AuthorizeId == param.AuthorizeId);
                }
                if (param.AuthorizeType > 0)
                {
                    expression = expression.And(t => t.AuthorizeType == param.AuthorizeType);
                }
                if (!string.IsNullOrEmpty(param.MenuIds))
                {
                    long[] menuIdIdArr = TextHelper.SplitToArray<long>(param.MenuIds, ',');
                    expression = expression.And(t => menuIdIdArr.Contains(t.MenuId.Value));
                }
                if(param.PickAwayMenuId > 0)
                {
                    expression = expression.And(t => t.MenuId != param.PickAwayMenuId);
                }
            }
            var list = await this.BaseRepository().FindList<MenuAuthorizeEntity>(expression);
            return list.ToList();
        }

        public async Task<MenuAuthorizeEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<MenuAuthorizeEntity>(id);
        }
        #endregion

        #region 鎻愪氦鏁版嵁
        public async Task SaveForm(MenuAuthorizeEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task DeleteForm(long id)
        {
            await this.BaseRepository().Delete<MenuAuthorizeEntity>(id);
        }
        #endregion
    }
}

