{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {"Dqy.Syjx.HaikangService/1.0.0": {"dependencies": {"Dqy.Syjx.Data": "1.0.0", "Dqy.Syjx.Data.EF": "1.0.0", "Dqy.Syjx.Data.Repository": "1.0.0", "Dqy.Syjx.Entity": "1.0.0", "Dqy.Syjx.Service": "1.0.0", "Dqy.Syjx.Util": "1.0.0", "Microsoft.Extensions.Hosting": "6.0.0", "Microsoft.Extensions.Hosting.WindowsServices": "6.0.0", "Quartz.Extensions.Hosting": "3.6.3"}, "runtime": {"Dqy.Syjx.HaikangService.dll": {}}}, "AutoMapper/10.1.1": {"dependencies": {"Microsoft.CSharp": "4.7.0", "System.Reflection.Emit": "4.7.0"}, "runtime": {"lib/netstandard2.0/AutoMapper.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "********"}}}, "BouncyCastle.Cryptography/2.2.1": {"runtime": {"lib/net6.0/BouncyCastle.Cryptography.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.1.47552"}}}, "Castle.Core/4.4.1": {"dependencies": {"NETStandard.Library": "1.6.1", "System.Collections.Specialized": "4.3.0", "System.ComponentModel": "4.3.0", "System.ComponentModel.TypeConverter": "4.3.0", "System.Diagnostics.TraceSource": "4.3.0", "System.Dynamic.Runtime": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}, "runtime": {"lib/netstandard1.5/Castle.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Castle.Core.AsyncInterceptor/1.7.0": {"dependencies": {"Castle.Core": "4.4.1"}, "runtime": {"lib/netstandard2.0/Castle.Core.AsyncInterceptor.dll": {"assemblyVersion": "1.7.0.0", "fileVersion": "1.7.0.0"}}}, "dmdbms.DmProvider/1.1.0.16649": {"runtime": {"lib/net6.0/DmProvider.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "1.1.0.16649"}}, "resources": {"lib/net6.0/en/DmProvider.resources.dll": {"locale": "en"}, "lib/net6.0/zh-CN/DmProvider.resources.dll": {"locale": "zh-CN"}}}, "dmdbms.Microsoft.EntityFrameworkCore.Dm/6.0.16.16649": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "6.0.19", "dmdbms.DmProvider": "1.1.0.16649"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Dm.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Dynamitey/**********": {"dependencies": {"Microsoft.CSharp": "4.7.0", "System.ComponentModel": "4.3.0"}, "runtime": {"lib/netstandard2.0/Dynamitey.dll": {"assemblyVersion": "**********", "fileVersion": "**********"}}}, "EFCore.Sharding/5.0.16": {"dependencies": {"Castle.Core": "4.4.1", "Dynamitey": "**********", "LinqKit.Microsoft.EntityFrameworkCore": "5.0.24", "Microsoft.EntityFrameworkCore": "6.0.19", "Microsoft.EntityFrameworkCore.Relational": "6.0.19", "Microsoft.Extensions.Hosting.Abstractions": "6.0.0", "Namotion.Reflection": "1.0.19", "Quartz": "3.6.3", "System.Linq.Dynamic.Core": "1.3.5", "System.ServiceModel.Primitives": "4.8.1"}, "runtime": {"lib/netstandard2.1/EFCore.Sharding.dll": {"assemblyVersion": "*******", "fileVersion": "********"}}}, "EFCore.Sharding.SqlServer/5.0.16": {"dependencies": {"EFCore.Sharding": "5.0.16", "Microsoft.EntityFrameworkCore.SqlServer": "6.0.19", "Microsoft.EntityFrameworkCore.SqlServer.NetTopologySuite": "5.0.5"}, "runtime": {"lib/netstandard2.1/EFCore.Sharding.SqlServer.dll": {"assemblyVersion": "*******", "fileVersion": "********"}}}, "Enums.NET/4.0.1": {"runtime": {"lib/netcoreapp3.0/Enums.NET.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "LinqKit.Microsoft.EntityFrameworkCore/5.0.24": {"dependencies": {"Microsoft.EntityFrameworkCore": "6.0.19"}, "runtime": {"lib/netstandard2.1/LinqKit.Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "MathNet.Numerics.Signed/4.15.0": {"runtime": {"lib/netstandard2.0/MathNet.Numerics.dll": {"assemblyVersion": "4.15.0.0", "fileVersion": "4.15.0.0"}}}, "Microsoft.AspNetCore.Authentication.JwtBearer/6.0.11": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.10.0"}, "runtime": {"lib/net6.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"assemblyVersion": "6.0.11.0", "fileVersion": "6.0.1122.52304"}}}, "Microsoft.AspNetCore.JsonPatch/6.0.19": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/net6.0/Microsoft.AspNetCore.JsonPatch.dll": {"assemblyVersion": "6.0.19.0", "fileVersion": "6.0.1923.31806"}}}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson/6.0.19": {"dependencies": {"Microsoft.AspNetCore.JsonPatch": "6.0.19", "Newtonsoft.Json": "13.0.3", "Newtonsoft.Json.Bson": "1.0.2"}, "runtime": {"lib/net6.0/Microsoft.AspNetCore.Mvc.NewtonsoftJson.dll": {"assemblyVersion": "6.0.19.0", "fileVersion": "6.0.1923.31806"}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.Data.SqlClient/2.1.4": {"dependencies": {"Microsoft.Data.SqlClient.SNI.runtime": "2.1.1", "Microsoft.Identity.Client": "4.21.1", "Microsoft.IdentityModel.JsonWebTokens": "6.17.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.10.0", "Microsoft.Win32.Registry": "4.7.0", "System.Configuration.ConfigurationManager": "6.0.1", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Runtime.Caching": "4.7.0", "System.Security.Principal.Windows": "4.7.0", "System.Text.Encoding.CodePages": "5.0.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll": {"assemblyVersion": "2.0.20168.4", "fileVersion": "2.0.20168.4"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "2.0.20168.4", "fileVersion": "2.0.20168.4"}, "runtimes/win/lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "2.0.20168.4", "fileVersion": "2.0.20168.4"}}}, "Microsoft.Data.SqlClient.SNI.runtime/2.1.1": {"runtimeTargets": {"runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "*******"}}}, "Microsoft.EntityFrameworkCore/6.0.19": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "6.0.19", "Microsoft.EntityFrameworkCore.Analyzers": "6.0.19", "Microsoft.Extensions.Caching.Memory": "7.0.0", "Microsoft.Extensions.DependencyInjection": "6.0.1", "Microsoft.Extensions.Logging": "6.0.0", "System.Collections.Immutable": "6.0.0", "System.Diagnostics.DiagnosticSource": "6.0.1"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "6.0.19.0", "fileVersion": "6.0.1923.31801"}}}, "Microsoft.EntityFrameworkCore.Abstractions/6.0.19": {"runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "6.0.19.0", "fileVersion": "6.0.1923.31801"}}}, "Microsoft.EntityFrameworkCore.Analyzers/6.0.19": {}, "Microsoft.EntityFrameworkCore.Relational/6.0.19": {"dependencies": {"Microsoft.EntityFrameworkCore": "6.0.19", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "6.0.19.0", "fileVersion": "6.0.1923.31801"}}}, "Microsoft.EntityFrameworkCore.SqlServer/6.0.19": {"dependencies": {"Microsoft.Data.SqlClient": "2.1.4", "Microsoft.EntityFrameworkCore.Relational": "6.0.19"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.SqlServer.dll": {"assemblyVersion": "6.0.19.0", "fileVersion": "6.0.1923.31801"}}}, "Microsoft.EntityFrameworkCore.SqlServer.NetTopologySuite/5.0.5": {"dependencies": {"Microsoft.EntityFrameworkCore.SqlServer": "6.0.19", "NetTopologySuite": "2.1.0", "NetTopologySuite.IO.SqlServerBytes": "2.0.0"}, "runtime": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.SqlServer.NetTopologySuite.dll": {"assemblyVersion": "5.0.5.0", "fileVersion": "5.0.521.16102"}}}, "Microsoft.Extensions.Caching.Abstractions/7.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Caching.Memory/7.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Configuration/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}}, "Microsoft.Extensions.Configuration.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}}, "Microsoft.Extensions.Configuration.Binder/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0"}}, "Microsoft.Extensions.Configuration.CommandLine/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "6.0.0", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0"}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "6.0.0", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0"}}, "Microsoft.Extensions.Configuration.FileExtensions/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "6.0.0", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.0", "Microsoft.Extensions.FileProviders.Physical": "6.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}}, "Microsoft.Extensions.Configuration.Json/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "6.0.0", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "6.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.0", "System.Text.Json": "6.0.3"}}, "Microsoft.Extensions.Configuration.UserSecrets/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Configuration.Json": "6.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.0", "Microsoft.Extensions.FileProviders.Physical": "6.0.0"}}, "Microsoft.Extensions.DependencyInjection/6.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1022.47605"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/7.0.0": {"runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.FileProviders.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}}, "Microsoft.Extensions.FileProviders.Physical/6.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "6.0.0", "Microsoft.Extensions.FileSystemGlobbing": "6.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}}, "Microsoft.Extensions.FileSystemGlobbing/6.0.0": {}, "Microsoft.Extensions.Hosting/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "6.0.0", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Configuration.Binder": "6.0.0", "Microsoft.Extensions.Configuration.CommandLine": "6.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "6.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "6.0.0", "Microsoft.Extensions.Configuration.Json": "6.0.0", "Microsoft.Extensions.Configuration.UserSecrets": "6.0.0", "Microsoft.Extensions.DependencyInjection": "6.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.0", "Microsoft.Extensions.FileProviders.Physical": "6.0.0", "Microsoft.Extensions.Hosting.Abstractions": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Configuration": "6.0.0", "Microsoft.Extensions.Logging.Console": "6.0.0", "Microsoft.Extensions.Logging.Debug": "6.0.0", "Microsoft.Extensions.Logging.EventLog": "6.0.0", "Microsoft.Extensions.Logging.EventSource": "6.0.0", "Microsoft.Extensions.Options": "7.0.0"}}, "Microsoft.Extensions.Hosting.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.0"}}, "Microsoft.Extensions.Hosting.WindowsServices/6.0.0": {"dependencies": {"Microsoft.Extensions.Hosting": "6.0.0", "Microsoft.Extensions.Logging.EventLog": "6.0.0", "System.ServiceProcess.ServiceController": "6.0.0"}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Hosting.WindowsServices.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Http/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.0"}}, "Microsoft.Extensions.Http.Polly/6.0.0": {"dependencies": {"Microsoft.Extensions.Http": "6.0.0", "Polly": "7.2.2", "Polly.Extensions.Http": "3.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Http.Polly.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52608"}}}, "Microsoft.Extensions.Logging/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "6.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.0", "System.Diagnostics.DiagnosticSource": "6.0.1"}}, "Microsoft.Extensions.Logging.Abstractions/7.0.0": {"runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Logging.Configuration/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "6.0.0", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Configuration.Binder": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "6.0.0"}}, "Microsoft.Extensions.Logging.Console/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Configuration": "6.0.0", "Microsoft.Extensions.Options": "7.0.0", "System.Text.Json": "6.0.3"}}, "Microsoft.Extensions.Logging.Debug/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0"}}, "Microsoft.Extensions.Logging.EventLog/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.0", "System.Diagnostics.EventLog": "6.0.0"}}, "Microsoft.Extensions.Logging.EventSource/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Json": "6.0.3"}}, "Microsoft.Extensions.Options/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Configuration.Binder": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}}, "Microsoft.Extensions.Primitives/7.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Identity.Client/4.21.1": {"runtime": {"lib/netcoreapp2.1/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.21.1.0", "fileVersion": "4.21.1.0"}}}, "Microsoft.IdentityModel.JsonWebTokens/6.17.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "6.17.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "6.17.0.0", "fileVersion": "6.17.0.30323"}}}, "Microsoft.IdentityModel.Logging/6.17.0": {"runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "6.17.0.0", "fileVersion": "6.17.0.30323"}}}, "Microsoft.IdentityModel.Protocols/6.10.0": {"dependencies": {"Microsoft.IdentityModel.Logging": "6.17.0", "Microsoft.IdentityModel.Tokens": "6.17.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "6.10.0.0", "fileVersion": "6.10.0.20330"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.10.0": {"dependencies": {"Microsoft.IdentityModel.Protocols": "6.10.0", "System.IdentityModel.Tokens.Jwt": "6.17.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "6.10.0.0", "fileVersion": "6.10.0.20330"}}}, "Microsoft.IdentityModel.Tokens/6.17.0": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Microsoft.IdentityModel.Logging": "6.17.0", "System.Security.Cryptography.Cng": "4.5.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "6.17.0.0", "fileVersion": "6.17.0.30323"}}}, "Microsoft.IO.RecyclableMemoryStream/2.3.2": {"runtime": {"lib/net5.0/Microsoft.IO.RecyclableMemoryStream.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Net.Http.Headers/2.2.8": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0", "System.Buffers": "4.5.0"}}, "Microsoft.NETCore.Platforms/5.0.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.Win32.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "Microsoft.Win32.Registry/4.7.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Principal.Windows": "4.7.0"}}, "Microsoft.Win32.SystemEvents/7.0.0": {"runtime": {"lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "MySqlConnector/2.2.5": {"runtime": {"lib/net6.0/MySqlConnector.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Namotion.Reflection/1.0.19": {"dependencies": {"Microsoft.CSharp": "4.7.0"}, "runtime": {"lib/netstandard2.0/Namotion.Reflection.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "NetDevPack.Security.JwtExtensions/6.0.2": {"dependencies": {"Microsoft.AspNetCore.Authentication.JwtBearer": "6.0.11", "System.IdentityModel.Tokens.Jwt": "6.17.0", "System.Text.Json": "6.0.3"}, "runtime": {"lib/net6.0/NetDevPack.Security.JwtExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NETStandard.Library/1.6.1": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.Win32.Primitives": "4.3.0", "System.AppContext": "4.3.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Console": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.Compression.ZipFile": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Net.Http": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Net.Sockets": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Timer": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0"}}, "NetTopologySuite/2.1.0": {"dependencies": {"System.Memory": "4.5.3"}, "runtime": {"lib/netstandard2.0/NetTopologySuite.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NetTopologySuite.IO.SqlServerBytes/2.0.0": {"dependencies": {"NetTopologySuite": "2.1.0"}, "runtime": {"lib/netstandard2.0/NetTopologySuite.IO.SqlServerBytes.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "1*******", "fileVersion": "13.0.3.27908"}}}, "Newtonsoft.Json.Bson/1.0.2": {"dependencies": {"Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/Newtonsoft.Json.Bson.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.2.22727"}}}, "NLog/4.7.11": {"runtime": {"lib/netstandard2.0/NLog.dll": {"assemblyVersion": "*******", "fileVersion": "4.7.11.13229"}}}, "NLog.Extensions.Logging/1.7.4": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "NLog": "4.7.11"}, "runtime": {"lib/net5.0/NLog.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "1.7.4.1610"}}}, "NLog.Web.AspNetCore/4.14.0": {"dependencies": {"NLog.Extensions.Logging": "1.7.4"}, "runtime": {"lib/netcoreapp3.0/NLog.Web.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "4.14.0.2042"}}}, "NPOI/2.6.2": {"dependencies": {"BouncyCastle.Cryptography": "2.2.1", "Enums.NET": "4.0.1", "MathNet.Numerics.Signed": "4.15.0", "Microsoft.IO.RecyclableMemoryStream": "2.3.2", "SharpZipLib": "1.3.3", "SixLabors.Fonts": "1.0.0", "SixLabors.ImageSharp": "2.1.4", "System.Configuration.ConfigurationManager": "6.0.1", "System.Security.Cryptography.Xml": "6.0.1"}, "runtime": {"lib/net6.0/NPOI.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net6.0/NPOI.OOXML.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net6.0/NPOI.OpenXml4Net.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net6.0/NPOI.OpenXmlFormats.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Oracle.ManagedDataAccess.Core/3.21.100": {"dependencies": {"System.Diagnostics.PerformanceCounter": "6.0.1", "System.DirectoryServices": "6.0.1", "System.DirectoryServices.Protocols": "6.0.1"}, "runtime": {"lib/netstandard2.1/Oracle.ManagedDataAccess.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Pipelines.Sockets.Unofficial/2.2.0": {"dependencies": {"System.IO.Pipelines": "5.0.0"}, "runtime": {"lib/net5.0/Pipelines.Sockets.Unofficial.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.45337"}}}, "Polly/7.2.2": {"runtime": {"lib/netstandard2.0/Polly.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Polly.Extensions.Http/3.0.0": {"dependencies": {"Polly": "7.2.2"}, "runtime": {"lib/netstandard2.0/Polly.Extensions.Http.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Pomelo.EntityFrameworkCore.MySql/6.0.2": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "6.0.19", "Microsoft.Extensions.DependencyInjection": "6.0.1", "MySqlConnector": "2.2.5"}, "runtime": {"lib/net6.0/Pomelo.EntityFrameworkCore.MySql.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "QRCoder/1.4.1": {"dependencies": {"System.Drawing.Common": "7.0.0"}, "runtime": {"lib/netstandard2.0/QRCoder.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Quartz/3.6.3": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "7.0.0", "System.Configuration.ConfigurationManager": "6.0.1", "System.Diagnostics.DiagnosticSource": "6.0.1"}, "runtime": {"lib/netstandard2.0/Quartz.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Quartz.Extensions.DependencyInjection/3.6.3": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.0", "Quartz": "3.6.3"}, "runtime": {"lib/net6.0/Quartz.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Quartz.Extensions.Hosting/3.6.3": {"dependencies": {"Microsoft.Extensions.Hosting.Abstractions": "6.0.0", "Quartz.Extensions.DependencyInjection": "3.6.3"}, "runtime": {"lib/net6.0/Quartz.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Refit/8.0.0": {"runtime": {"lib/net6.0/Refit.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Refit.Newtonsoft.Json/8.0.0": {"dependencies": {"Newtonsoft.Json": "13.0.3", "Refit": "8.0.0"}, "runtime": {"lib/net6.0/Refit.Newtonsoft.Json.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.native.System/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"dependencies": {"runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "4.3.0"}}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "Senparc.CO2NET/2.1.7.3": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "7.0.0", "Microsoft.Extensions.DependencyInjection": "6.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Http": "6.0.0", "Microsoft.Extensions.Http.Polly": "6.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "6.0.0", "Microsoft.Net.Http.Headers": "2.2.8", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.1/Senparc.CO2NET.dll": {"assemblyVersion": "2.1.7.3", "fileVersion": "2.1.7.3"}}}, "Senparc.CO2NET.APM/1.2.4.6": {"dependencies": {"Senparc.CO2NET": "2.1.7.3"}, "runtime": {"lib/netstandard2.0/Senparc.CO2NET.APM.dll": {"assemblyVersion": "1.2.4.6", "fileVersion": "1.2.4.6"}}}, "Senparc.NeuChar/*******": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Senparc.CO2NET": "2.1.7.3", "Senparc.CO2NET.APM": "1.2.4.6"}, "runtime": {"lib/netstandard2.1/Senparc.NeuChar.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Senparc.NeuChar.App/1.1.4.5": {"dependencies": {"Senparc.NeuChar": "*******"}, "runtime": {"lib/netstandard2.1/Senparc.NeuChar.App.dll": {"assemblyVersion": "1.1.4.5", "fileVersion": "1.1.4.5"}}}, "Senparc.Weixin/********": {"dependencies": {"Microsoft.Extensions.Options": "7.0.0", "Newtonsoft.Json": "13.0.3", "Senparc.CO2NET": "2.1.7.3", "Senparc.NeuChar": "*******", "System.Diagnostics.TraceSource": "4.3.0", "System.Dynamic.Runtime": "4.3.0", "System.Threading.Thread": "4.3.0", "System.Xml.XmlSerializer": "4.3.0"}, "runtime": {"lib/netstandard2.1/Senparc.Weixin.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Senparc.Weixin.MP/**********": {"dependencies": {"Senparc.CO2NET.APM": "1.2.4.6", "Senparc.NeuChar": "*******", "Senparc.NeuChar.App": "1.1.4.5", "Senparc.Weixin": "********", "System.Xml.XPath.XmlDocument": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}, "runtime": {"lib/netstandard2.1/Senparc.Weixin.MP.dll": {"assemblyVersion": "**********", "fileVersion": "**********"}}}, "Senparc.Weixin.WxOpen/3.15.13": {"dependencies": {"Senparc.NeuChar": "*******", "Senparc.Weixin": "********", "Senparc.Weixin.MP": "**********", "System.Xml.XPath.XmlDocument": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}, "runtime": {"lib/netstandard2.1/Senparc.Weixin.WxOpen.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "SharpZipLib/1.3.3": {"runtime": {"lib/netstandard2.1/ICSharpCode.SharpZipLib.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "SixLabors.Fonts/1.0.0": {"runtime": {"lib/netcoreapp3.1/SixLabors.Fonts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SixLabors.ImageSharp/2.1.4": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encoding.CodePages": "5.0.0"}, "runtime": {"lib/netcoreapp3.1/SixLabors.ImageSharp.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkiaSharp/2.88.8": {"dependencies": {"SkiaSharp.NativeAssets.Win32": "2.88.8", "SkiaSharp.NativeAssets.macOS": "2.88.8"}, "runtime": {"lib/net6.0/SkiaSharp.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "SkiaSharp.NativeAssets.macOS/2.88.8": {"runtimeTargets": {"runtimes/osx/native/libSkiaSharp.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SkiaSharp.NativeAssets.Win32/2.88.8": {"runtimeTargets": {"runtimes/win-arm64/native/libSkiaSharp.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/libSkiaSharp.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/libSkiaSharp.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "StackExchange.Redis/2.2.50": {"dependencies": {"Pipelines.Sockets.Unofficial": "2.2.0", "System.Diagnostics.PerformanceCounter": "6.0.1"}, "runtime": {"lib/net5.0/StackExchange.Redis.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.50.36290"}}}, "System.AppContext/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Buffers/4.5.0": {}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Collections.Concurrent/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Collections.Immutable/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Collections.NonGeneric/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Collections.Specialized/4.3.0": {"dependencies": {"System.Collections.NonGeneric": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.ComponentModel/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.ComponentModel.Primitives/4.3.0": {"dependencies": {"System.ComponentModel": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}}, "System.ComponentModel.TypeConverter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Collections.NonGeneric": "4.3.0", "System.Collections.Specialized": "4.3.0", "System.ComponentModel": "4.3.0", "System.ComponentModel.Primitives": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Configuration.ConfigurationManager/6.0.1": {"dependencies": {"System.Security.Cryptography.ProtectedData": "6.0.0", "System.Security.Permissions": "6.0.0"}, "runtime": {"lib/net6.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.922.41905"}}}, "System.Console/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.DiagnosticSource/6.0.1": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1523.11507"}}}, "System.Diagnostics.EventLog/6.0.0": {}, "System.Diagnostics.PerformanceCounter/6.0.1": {"dependencies": {"System.Configuration.ConfigurationManager": "6.0.1"}, "runtime": {"lib/net6.0/System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Diagnostics.PerformanceCounter.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}}}, "System.Diagnostics.Tools/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.TraceSource/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Diagnostics.Tracing/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.DirectoryServices/6.0.1": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Permissions": "6.0.0"}, "runtime": {"lib/net6.0/System.DirectoryServices.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1423.7309"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.DirectoryServices.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.1423.7309"}}}, "System.DirectoryServices.Protocols/6.0.1": {"runtime": {"lib/net6.0/System.DirectoryServices.Protocols.dll": {"assemblyVersion": "6.0.0.1", "fileVersion": "6.0.222.6406"}}, "runtimeTargets": {"runtimes/linux/lib/net6.0/System.DirectoryServices.Protocols.dll": {"rid": "linux", "assetType": "runtime", "assemblyVersion": "6.0.0.1", "fileVersion": "6.0.222.6406"}, "runtimes/osx/lib/net6.0/System.DirectoryServices.Protocols.dll": {"rid": "osx", "assetType": "runtime", "assemblyVersion": "6.0.0.1", "fileVersion": "6.0.222.6406"}, "runtimes/win/lib/net6.0/System.DirectoryServices.Protocols.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "6.0.0.1", "fileVersion": "6.0.222.6406"}}}, "System.Drawing.Common/7.0.0": {"dependencies": {"Microsoft.Win32.SystemEvents": "7.0.0"}, "runtime": {"lib/net6.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Drawing.Common.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Dynamic.Runtime/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Formats.Asn1/6.0.0": {}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Globalization.Calendars/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Globalization.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}}, "System.IdentityModel.Tokens.Jwt/6.17.0": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "6.17.0", "Microsoft.IdentityModel.Tokens": "6.17.0"}, "runtime": {"lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "6.17.0.0", "fileVersion": "6.17.0.30323"}}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Buffers": "4.5.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.IO.Compression": "4.3.0"}}, "System.IO.Compression.ZipFile/4.3.0": {"dependencies": {"System.Buffers": "4.5.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.IO.FileSystem/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.IO.Pipelines/5.0.0": {}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Dynamic.Core/1.3.5": {"runtime": {"lib/net6.0/System.Linq.Dynamic.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.Linq.Expressions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Memory/4.5.3": {}, "System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Net.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Net.Sockets/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Numerics.Vectors/4.5.0": {}, "System.ObjectModel/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}}, "System.Private.ServiceModel/4.8.1": {"dependencies": {"System.Numerics.Vectors": "4.5.0", "System.Reflection.DispatchProxy": "4.7.1", "System.Security.Cryptography.Xml": "6.0.1", "System.Security.Principal.Windows": "4.7.0"}, "runtime": {"lib/netstandard2.0/System.Private.ServiceModel.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.121.7802"}}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.DispatchProxy/4.7.1": {}, "System.Reflection.Emit/4.7.0": {}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.Lightweight/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Reflection.TypeExtensions/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.Caching/4.7.0": {"dependencies": {"System.Configuration.ConfigurationManager": "6.0.1"}, "runtime": {"lib/netstandard2.0/System.Runtime.Caching.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Runtime.Caching.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Runtime.Numerics/4.3.0": {"dependencies": {"System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Security.AccessControl/6.0.0": {}, "System.Security.Cryptography.Algorithms/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.Apple": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.Cng/4.5.0": {}, "System.Security.Cryptography.Csp/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}}, "System.Security.Cryptography.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.Pkcs/6.0.1": {"dependencies": {"System.Formats.Asn1": "6.0.0"}, "runtime": {"lib/net6.0/System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.522.21309"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Security.Cryptography.Pkcs.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.522.21309"}}}, "System.Security.Cryptography.Primitives/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Security.Cryptography.ProtectedData/6.0.0": {"runtime": {"lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Security.Cryptography.X509Certificates/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Cng": "4.5.0", "System.Security.Cryptography.Csp": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.Xml/6.0.1": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Cryptography.Pkcs": "6.0.1"}, "runtime": {"lib/net6.0/System.Security.Cryptography.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.822.36306"}}}, "System.Security.Permissions/6.0.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Windows.Extensions": "6.0.0"}, "runtime": {"lib/net6.0/System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Security.Principal.Windows/4.7.0": {}, "System.ServiceModel.Primitives/4.8.1": {"dependencies": {"System.Private.ServiceModel": "4.8.1"}, "runtime": {"lib/netcoreapp2.1/System.ServiceModel.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.121.7802"}, "lib/netcoreapp2.1/System.ServiceModel.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.121.7802"}}}, "System.ServiceProcess.ServiceController/6.0.0": {"dependencies": {"System.Diagnostics.EventLog": "6.0.0"}, "runtime": {"lib/net6.0/System.ServiceProcess.ServiceController.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.ServiceProcess.ServiceController.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encoding.CodePages/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0"}}, "System.Text.Encoding.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Text.Encodings.Web/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Text.Json/6.0.3": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "6.0.0"}, "runtime": {"lib/net6.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}}}, "System.Text.RegularExpressions/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading.Tasks.Extensions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Thread/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Threading.Timer/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Windows.Extensions/6.0.0": {"dependencies": {"System.Drawing.Common": "7.0.0"}, "runtime": {"lib/net6.0/System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Windows.Extensions.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Xml.ReaderWriter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Extensions": "4.3.0"}}, "System.Xml.XDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "System.Xml.XmlDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "System.Xml.XmlSerializer/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}}, "System.Xml.XPath/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "System.Xml.XPath.XmlDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XPath": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Xml.XPath.XmlDocument.dll": {"assemblyVersion": "4.0.2.0", "fileVersion": "4.6.24705.1"}}}, "Dqy.Syjx.Cache.Factory/1.0.0": {"dependencies": {"Dqy.Syjx.Cache.Interface": "1.0.0", "Dqy.Syjx.MemoryCache": "1.0.0", "Dqy.Syjx.RedisCache": "1.0.0"}, "runtime": {"Dqy.Syjx.Cache.Factory.dll": {}}}, "Dqy.Syjx.Cache.Interface/1.0.0": {"runtime": {"Dqy.Syjx.Cache.Interface.dll": {}}}, "Dqy.Syjx.Data/1.0.0": {"dependencies": {"Dqy.Syjx.Util": "1.0.0", "Microsoft.EntityFrameworkCore.Relational": "6.0.19", "Microsoft.EntityFrameworkCore.SqlServer": "6.0.19", "MySqlConnector": "2.2.5", "Oracle.ManagedDataAccess.Core": "3.21.100", "ZackEFCore.Batch.Dm_NET6": "6.6.3"}, "runtime": {"Dqy.Syjx.Data.dll": {}}}, "Dqy.Syjx.Data.EF/1.0.0": {"dependencies": {"Dqy.Syjx.Data": "1.0.0", "Microsoft.EntityFrameworkCore.Relational": "6.0.19", "Microsoft.EntityFrameworkCore.SqlServer": "6.0.19", "Newtonsoft.Json": "13.0.3", "Oracle.ManagedDataAccess.Core": "3.21.100", "Pomelo.EntityFrameworkCore.MySql": "6.0.2"}, "runtime": {"Dqy.Syjx.Data.EF.dll": {}}}, "Dqy.Syjx.Data.Repository/1.0.0": {"dependencies": {"Dqy.Syjx.Data.EF": "1.0.0"}, "runtime": {"Dqy.Syjx.Data.Repository.dll": {}}}, "Dqy.Syjx.Entity/1.0.0": {"dependencies": {"Dqy.Syjx.IdGenerator": "1.0.0", "Dqy.Syjx.Web.Code": "1.0.0"}, "runtime": {"Dqy.Syjx.Entity.dll": {}}}, "Dqy.Syjx.Enum/1.0.0": {"runtime": {"Dqy.Syjx.Enum.dll": {}}}, "Dqy.Syjx.IdGenerator/1.0.0": {"dependencies": {"Dqy.Syjx.Util": "1.0.0"}, "runtime": {"Dqy.Syjx.IdGenerator.dll": {}}}, "Dqy.Syjx.MemoryCache/1.0.0": {"dependencies": {"Dqy.Syjx.Cache.Interface": "1.0.0", "Dqy.Syjx.Util": "1.0.0", "Microsoft.Extensions.Caching.Memory": "7.0.0", "MySqlConnector": "2.2.5", "Oracle.ManagedDataAccess.Core": "3.21.100", "Senparc.Weixin.WxOpen": "3.15.13", "System.Drawing.Common": "7.0.0"}, "runtime": {"Dqy.Syjx.MemoryCache.dll": {}}}, "Dqy.Syjx.Model/1.0.0": {"dependencies": {"Dqy.Syjx.Entity": "1.0.0"}, "runtime": {"Dqy.Syjx.Model.dll": {}}}, "Dqy.Syjx.RedisCache/1.0.0": {"dependencies": {"Dqy.Syjx.Cache.Interface": "1.0.0", "Dqy.Syjx.Util": "1.0.0", "StackExchange.Redis": "2.2.50"}, "runtime": {"Dqy.Syjx.RedisCache.dll": {}}}, "Dqy.Syjx.Service/1.0.0": {"dependencies": {"Dqy.Syjx.Data.Repository": "1.0.0", "Dqy.Syjx.Entity": "1.0.0", "Dqy.Syjx.IdGenerator": "1.0.0", "Dqy.Syjx.Model": "1.0.0", "Refit": "8.0.0", "Refit.Newtonsoft.Json": "8.0.0"}, "runtime": {"Dqy.Syjx.Service.dll": {}}}, "Dqy.Syjx.Util/1.0.0": {"dependencies": {"AutoMapper": "10.1.1", "Castle.Core.AsyncInterceptor": "1.7.0", "Dqy.Syjx.Enum": "1.0.0", "EFCore.Sharding.SqlServer": "5.0.16", "Microsoft.AspNetCore.Authentication.JwtBearer": "6.0.11", "Microsoft.AspNetCore.Mvc.NewtonsoftJson": "6.0.19", "NLog.Web.AspNetCore": "4.14.0", "NPOI": "2.6.2", "NetDevPack.Security.JwtExtensions": "6.0.2", "QRCoder": "1.4.1", "SkiaSharp": "2.88.8", "System.Drawing.Common": "7.0.0", "System.Linq.Dynamic.Core": "1.3.5", "System.ServiceModel.Primitives": "4.8.1"}, "runtime": {"Dqy.Syjx.Util.dll": {}}}, "Dqy.Syjx.Web.Code/1.0.0": {"dependencies": {"Dqy.Syjx.Cache.Factory": "1.0.0", "Dqy.Syjx.Data.Repository": "1.0.0"}, "runtime": {"Dqy.Syjx.Web.Code.dll": {}}}, "Zack.EFCore.Batch_NET6/6.1.3": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "6.0.19"}, "runtime": {"Zack.EFCore.Batch_NET6.dll": {}}}, "ZackEFCore.Batch.Dm_NET6/6.6.3": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "6.0.19", "Zack.EFCore.Batch_NET6": "6.1.3", "dmdbms.Microsoft.EntityFrameworkCore.Dm": "6.0.16.16649"}, "runtime": {"ZackEFCore.Batch.Dm_NET6.dll": {}}}}}, "libraries": {"Dqy.Syjx.HaikangService/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AutoMapper/10.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-uMgbqOdu9ZG5cIOty0C85hzzayBH2i9BthnS5FlMqKtMSHDv4ts81a2jS1VFaDBVhlBeIqJ/kQKjQY95BZde9w==", "path": "automapper/10.1.1", "hashPath": "automapper.10.1.1.nupkg.sha512"}, "BouncyCastle.Cryptography/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-A6Zr52zVqJKt18ZBsTnX0qhG0kwIQftVAjLmszmkiR/trSp8H+xj1gUOzk7XHwaKgyREMSV1v9XaKrBUeIOdvQ==", "path": "bouncycastle.cryptography/2.2.1", "hashPath": "bouncycastle.cryptography.2.2.1.nupkg.sha512"}, "Castle.Core/4.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-zanbjWC0Y05gbx4eGXkzVycOQqVOFVeCjVsDSyuao9P4mtN1w3WxxTo193NGC7j3o2u3AJRswaoC6hEbnGACnQ==", "path": "castle.core/4.4.1", "hashPath": "castle.core.4.4.1.nupkg.sha512"}, "Castle.Core.AsyncInterceptor/1.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-l85sNnYIJIQpeH3RGIw5l2L6bxjq7eH7u2NlT9/IfNDBa2oadcRModMBzc92b6kA8EAYpvjKsW7fbnrGrY4FOA==", "path": "castle.core.asyncinterceptor/1.7.0", "hashPath": "castle.core.asyncinterceptor.1.7.0.nupkg.sha512"}, "dmdbms.DmProvider/1.1.0.16649": {"type": "package", "serviceable": true, "sha512": "sha512-E6ZUN352V7gZGvGUtQNro6pJ1gcdYm26dEWWKyPST5FLa9Bk94NPClxxDgEfujh1LmFHj5qZuhZcsAbFOIFl1w==", "path": "dmdbms.dmprovider/1.1.0.16649", "hashPath": "dmdbms.dmprovider.1.1.0.16649.nupkg.sha512"}, "dmdbms.Microsoft.EntityFrameworkCore.Dm/6.0.16.16649": {"type": "package", "serviceable": true, "sha512": "sha512-bxvfjKhd9UzVIWEqCLeHqc3DDjMI2Rgr9T4VLaid0Mi5EhZjhUtFKuXo6cf4YaviO3yHdauNFihNh1of1ri9Aw==", "path": "dmdbms.microsoft.entityframeworkcore.dm/6.0.16.16649", "hashPath": "dmdbms.microsoft.entityframeworkcore.dm.6.0.16.16649.nupkg.sha512"}, "Dynamitey/**********": {"type": "package", "serviceable": true, "sha512": "sha512-sKo8BlCgQTOSFFK4RlMvC8abp0NqZrTd017fishVrkoqggNBpwa4RGq88wPjyedk5GjTWeB8/kGrbXES+Rcl7Q==", "path": "dynamitey/**********", "hashPath": "dynamitey.**********.nupkg.sha512"}, "EFCore.Sharding/5.0.16": {"type": "package", "serviceable": true, "sha512": "sha512-bhuiqo61QRsg5IG+b56gHvnTV3ZYZ4BdWZ8PjEzvt4/UaroNaxguhKEuX6V2wIKxyzPFMVJDJJrgEhHjOHGYoQ==", "path": "efcore.sharding/5.0.16", "hashPath": "efcore.sharding.5.0.16.nupkg.sha512"}, "EFCore.Sharding.SqlServer/5.0.16": {"type": "package", "serviceable": true, "sha512": "sha512-ylCtXWrxYg9YYyePBSS5G/4J8iZmkYemF1GEL3JVA72fNGkTiDnZVpRl9XG8ZCKogHDMsGfUuDEfM9zVfnjcGQ==", "path": "efcore.sharding.sqlserver/5.0.16", "hashPath": "efcore.sharding.sqlserver.5.0.16.nupkg.sha512"}, "Enums.NET/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-OUGCd5L8zHZ61GAf436G0gf/H6yrSUkEpV5vm2CbCUuz9Rx7iLFLP5iHSSfmOtqNpuyo4vYte0CvYEmPZXRmRQ==", "path": "enums.net/4.0.1", "hashPath": "enums.net.4.0.1.nupkg.sha512"}, "LinqKit.Microsoft.EntityFrameworkCore/5.0.24": {"type": "package", "serviceable": true, "sha512": "sha512-PemexcITl6b0EWnZczQG3fEHFoMxUKHRoe54rHFVOlIu3b4sXzZ10pa7KBByaK5O5UGmqCdWS6K1AjXQpF+JOg==", "path": "linqkit.microsoft.entityframeworkcore/5.0.24", "hashPath": "linqkit.microsoft.entityframeworkcore.5.0.24.nupkg.sha512"}, "MathNet.Numerics.Signed/4.15.0": {"type": "package", "serviceable": true, "sha512": "sha512-LFjukMRatkg9dgRM7U/gM4uKgaWAX7E0lt3fsVDTPdtBIVuh7uPlksDie290br1/tv1a4Ar/Bz9ywCPSL8PhHg==", "path": "mathnet.numerics.signed/4.15.0", "hashPath": "mathnet.numerics.signed.4.15.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.JwtBearer/6.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-ivpWC8L84Y+l9VZOa0uJXPoUE+n3TiSRZpfKxMElRtLMYCeXmz5x3O7CuCJkZ65z1520RWuEZDmHefxiz5TqPg==", "path": "microsoft.aspnetcore.authentication.jwtbearer/6.0.11", "hashPath": "microsoft.aspnetcore.authentication.jwtbearer.6.0.11.nupkg.sha512"}, "Microsoft.AspNetCore.JsonPatch/6.0.19": {"type": "package", "serviceable": true, "sha512": "sha512-TvBoWIX1kB5i4yHhtS4zWbD6O1X3dzEb3d79oJ+DxQm5iFkAX02SIcx60lPd+emkbFv1AZjrXP+dd+7Urtbrnw==", "path": "microsoft.aspnetcore.jsonpatch/6.0.19", "hashPath": "microsoft.aspnetcore.jsonpatch.6.0.19.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson/6.0.19": {"type": "package", "serviceable": true, "sha512": "sha512-OvVtiyawqio8BtX2SyqWraSvzn1pvpTJ6FMV5hmg1E62leAN6+t68vMfp6lE+G4WWyanHeOmhaTvm+7XM/t91g==", "path": "microsoft.aspnetcore.mvc.newtonsoftjson/6.0.19", "hashPath": "microsoft.aspnetcore.mvc.newtonsoftjson.6.0.19.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.Data.SqlClient/2.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-cDcKBTKILdRuAzJjbgXwGcUQXzMue+SG02kD4tZTXXfoz4ALrGLpCnA5k9khw3fnAMlMnRzLIGuvRdJurqmESA==", "path": "microsoft.data.sqlclient/2.1.4", "hashPath": "microsoft.data.sqlclient.2.1.4.nupkg.sha512"}, "Microsoft.Data.SqlClient.SNI.runtime/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-JwGDWkyZgm7SATJmFLfT2G4teimvNbNtq3lsS9a5DzvhEZnQrZjZhevCU0vdx8MjheLHoG5vocuO03QtioFQxQ==", "path": "microsoft.data.sqlclient.sni.runtime/2.1.1", "hashPath": "microsoft.data.sqlclient.sni.runtime.2.1.1.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/6.0.19": {"type": "package", "serviceable": true, "sha512": "sha512-rJyVCPookNEBScNenXtU2CRHD6YzdyI0V4NNGQrGMpREuWlrEyB3WhOzBuIvuEAYWKx8B7R82p9loC+OZ4ZvNA==", "path": "microsoft.entityframeworkcore/6.0.19", "hashPath": "microsoft.entityframeworkcore.6.0.19.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/6.0.19": {"type": "package", "serviceable": true, "sha512": "sha512-8lbiDRXU5pAELDvgLZj2NMeeIsnLAIk2n/q3wVc6ctU0rPYbeng3541hbeDj9mjd7kPtt0Vm5FVdcpjJFAy1dw==", "path": "microsoft.entityframeworkcore.abstractions/6.0.19", "hashPath": "microsoft.entityframeworkcore.abstractions.6.0.19.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/6.0.19": {"type": "package", "serviceable": true, "sha512": "sha512-0XxgSQ2iymaIu8esrh8uGjK3BToPkRYdQXufr+7/mFiCHZ+ChfywxiRZJvPTpSUiHbT6lyFAnNT171RMVqMjcw==", "path": "microsoft.entityframeworkcore.analyzers/6.0.19", "hashPath": "microsoft.entityframeworkcore.analyzers.6.0.19.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/6.0.19": {"type": "package", "serviceable": true, "sha512": "sha512-LG0uv5ydT5d8Vxe9iNendVxe3YQCIxpl2l6OlRCRM5h0R8Er3pHD3JKhmZTAX/1rUe1v2mxs15knd0rfJC5SCQ==", "path": "microsoft.entityframeworkcore.relational/6.0.19", "hashPath": "microsoft.entityframeworkcore.relational.6.0.19.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.SqlServer/6.0.19": {"type": "package", "serviceable": true, "sha512": "sha512-tUyKFMYxUz+Ph6iuwp5GqAkD71KCiEhyY1MzApcBnHTEZhU/Au9RnCIYOP98bOOfyDjnri0QkvhtGqwQhRkKww==", "path": "microsoft.entityframeworkcore.sqlserver/6.0.19", "hashPath": "microsoft.entityframeworkcore.sqlserver.6.0.19.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.SqlServer.NetTopologySuite/5.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-2pGy0zqCoIoF//423Y/me5Q4r/xkb0F36byuuHo8UXKEwHUVu6lV+ABC9k7x47xeJNn7hG4tnuQkdpjbZtN30g==", "path": "microsoft.entityframeworkcore.sqlserver.nettopologysuite/5.0.5", "hashPath": "microsoft.entityframeworkcore.sqlserver.nettopologysuite.5.0.5.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IeimUd0TNbhB4ded3AbgBLQv2SnsiVugDyGV1MvspQFVlA07nDC7Zul7kcwH5jWN3JiTcp/ySE83AIJo8yfKjg==", "path": "microsoft.extensions.caching.abstractions/7.0.0", "hashPath": "microsoft.extensions.caching.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xpidBs2KCE2gw1JrD0quHE72kvCaI3xFql5/Peb2GRtUuZX+dYPoK/NTdVMiM67Svym0M0Df9A3xyU0FbMQhHw==", "path": "microsoft.extensions.caching.memory/7.0.0", "hashPath": "microsoft.extensions.caching.memory.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tq2wXyh3fL17EMF2bXgRhU7JrbO3on93MRKYxzz4JzzvuGSA1l0W3GI9/tl8EO89TH+KWEymP7bcFway6z9fXg==", "path": "microsoft.extensions.configuration/6.0.0", "hashPath": "microsoft.extensions.configuration.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qWzV9o+ZRWq+pGm+1dF+R7qTgTYoXvbyowRoBxQJGfqTpqDun2eteerjRQhq5PQ/14S+lqto3Ft4gYaRyl4rdQ==", "path": "microsoft.extensions.configuration.abstractions/6.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-b3<PERSON>rKzND8LIC7o08QAVlKfaEIYEvLJbtmVbFZVBRXeu9YkKfSSzLZfR1SUfQPBIy9mKLhEtJgGYImkcMNaKE0A==", "path": "microsoft.extensions.configuration.binder/6.0.0", "hashPath": "microsoft.extensions.configuration.binder.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3nL1qCkZ1Oxx14ZTzgo4MmlO7tso7F+TtMZAY2jUAtTLyAcDp+EDjk3RqafoKiNaePyPvvlleEcBxh3b2Hzl1g==", "path": "microsoft.extensions.configuration.commandline/6.0.0", "hashPath": "microsoft.extensions.configuration.commandline.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-DjYkzqvhiHCq38LW71PcIxXk6nhtV6VySP9yDcSO0goPl7YCU1VG1f2Wbgy58lkA10pWkjHCblZPUyboCB93ZA==", "path": "microsoft.extensions.configuration.environmentvariables/6.0.0", "hashPath": "microsoft.extensions.configuration.environmentvariables.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-V4Dth2cYMZpw3HhGw9XUDIijpI6gN+22LDt0AhufIgOppCUfpWX4483OmN+dFXRJkJLc8Tv0Q8QK+1ingT2+KQ==", "path": "microsoft.extensions.configuration.fileextensions/6.0.0", "hashPath": "microsoft.extensions.configuration.fileextensions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-GJGery6QytCzS/BxJ96klgG9in3uH26KcUBbiVG/coNDXCRq6LGVVlUT4vXq34KPuM+R2av+LeYdX9h4IZOCUg==", "path": "microsoft.extensions.configuration.json/6.0.0", "hashPath": "microsoft.extensions.configuration.json.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lB0Hb2V4+RUHy+LjEcqEr4EcV4RWc9EnjAV2GdtWQEdljQX+R4hGREftI7sInU9okP93pDrJiaj6QUJ6ZsslOA==", "path": "microsoft.extensions.configuration.usersecrets/6.0.0", "hashPath": "microsoft.extensions.configuration.usersecrets.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-vWXPg3HJQIpZkENn1KWq8SfbqVujVD7S7vIAyFXXqK5xkf1Vho+vG0bLBCHxU36lD1cLLtmGpfYf0B3MYFi9tQ==", "path": "microsoft.extensions.dependencyinjection/6.0.1", "hashPath": "microsoft.extensions.dependencyinjection.6.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-h3j/QfmFN4S0w4C2A6X7arXij/M/OVw3uQHSOFxnND4DyAzO1F9eMX7Eti7lU/OkSthEE0WzRsfT/Dmx86jzCw==", "path": "microsoft.extensions.dependencyinjection.abstractions/7.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0pd4/fho0gC12rQswaGQxbU34jOS1TPS8lZPpkFCH68ppQjHNHYle9iRuHeev1LhrJ94YPvzcRd8UmIuFk23Qw==", "path": "microsoft.extensions.fileproviders.abstractions/6.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-QvkL7l0nM8udt3gfyu0Vw8bbCXblxaKOl7c2oBfgGy4LCURRaL9XWZX1FWJrQc43oMokVneVxH38iz+bY1sbhg==", "path": "microsoft.extensions.fileproviders.physical/6.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ip8jnL1aPiaPeKINCqaTEbvBFDmVx9dXQEBZ2HOBRXPD1eabGNqP/bKlsIcp7U2lGxiXd5xIhoFcmY8nM4Hdiw==", "path": "microsoft.extensions.filesystemglobbing/6.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-M8VzD0ni5VarIRT8njnwK4K2WSAo0kZH4Zc3mKcSGkP4CjDZ91T9ZEFmmwhmo4z7x8AFq+tW0WFi9wX+K2cxkQ==", "path": "microsoft.extensions.hosting/6.0.0", "hashPath": "microsoft.extensions.hosting.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-GcT5l2CYXL6Sa27KCSh0TixsRfADUgth+ojQSD5EkzisZxmGFh7CwzkcYuGwvmXLjr27uWRNrJ2vuuEjMhU05Q==", "path": "microsoft.extensions.hosting.abstractions/6.0.0", "hashPath": "microsoft.extensions.hosting.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.WindowsServices/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qrYcNoYOI7AouQf0edY1NotePLNfz6wwLfjcEL5IQMXQm61k9glChzr7bt4I8uFelAHK8dYJPvCHEd6idxoXOw==", "path": "microsoft.extensions.hosting.windowsservices/6.0.0", "hashPath": "microsoft.extensions.hosting.windowsservices.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Http/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-15+pa2G0bAMHbHewaQIdr/y6ag2H3yh4rd9hTXavtWDzQBkvpe2RMqFg8BxDpcQWssmjmBApGPcw93QRz6YcMg==", "path": "microsoft.extensions.http/6.0.0", "hashPath": "microsoft.extensions.http.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Http.Polly/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-KSi+nlIV0Tt9LTN3459p69pbrKSJX2tcmWFJaNw2j/mpc/rK0KlWbgGHYKHhS6yk/ZVpHoNEux2wz4wWbdAl+w==", "path": "microsoft.extensions.http.polly/6.0.0", "hashPath": "microsoft.extensions.http.polly.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eIbyj40QDg1NDz0HBW0S5f3wrLVnKWnDJ/JtZ+yJDFnDj90VoPuoPmFkeaXrtu+0cKm5GRAwoDf+dBWXK0TUdg==", "path": "microsoft.extensions.logging/6.0.0", "hashPath": "microsoft.extensions.logging.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-kmn78+LPVMOWeITUjIlfxUPDsI0R6G0RkeAMBmQxAJ7vBJn4q2dTva7pWi65ceN5vPGjJ9q/Uae2WKgvfktJAw==", "path": "microsoft.extensions.logging.abstractions/7.0.0", "hashPath": "microsoft.extensions.logging.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZDskjagmBAbv+K8rYW9VhjPplhbOE63xUD0DiuydZJwt15dRyoqicYklLd86zzeintUc7AptDkHn+YhhYkYo8A==", "path": "microsoft.extensions.logging.configuration/6.0.0", "hashPath": "microsoft.extensions.logging.configuration.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Console/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-gsqKzOEdsvq28QiXFxagmn1oRB9GeI5GgYCkoybZtQA0IUb7QPwf1WmN3AwJeNIsadTvIFQCiVK0OVIgKfOBGg==", "path": "microsoft.extensions.logging.console/6.0.0", "hashPath": "microsoft.extensions.logging.console.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Debug/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-M9g/JixseSZATJE9tcMn9uzoD4+DbSglivFqVx8YkRJ7VVPmnvCEbOZ0AAaxsL1EKyI4cz07DXOOJExxNsUOHw==", "path": "microsoft.extensions.logging.debug/6.0.0", "hashPath": "microsoft.extensions.logging.debug.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventLog/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rlo0RxlMd0WtLG3CHI0qOTp6fFn7MvQjlrCjucA31RqmiMFCZkF8CHNbe8O7tbBIyyoLGWB1he9CbaA5iyHthg==", "path": "microsoft.extensions.logging.eventlog/6.0.0", "hashPath": "microsoft.extensions.logging.eventlog.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventSource/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-BeDyyqt7nkm/nr+Gdk+L8n1tUT/u33VkbXAOesgYSNsxDM9hJ1NOBGoZfj9rCbeD2+9myElI6JOVVFmnzgeWQA==", "path": "microsoft.extensions.logging.eventsource/6.0.0", "hashPath": "microsoft.extensions.logging.eventsource.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lP1yBnTTU42cKpMozuafbvNtQ7QcBjr/CcK3bYOGEMH55Fjt+iecXjT6chR7vbgCMqy3PG3aNQSZgo/EuY/9qQ==", "path": "microsoft.extensions.options/7.0.0", "hashPath": "microsoft.extensions.options.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXWINbTn0vC0FYc9GaQTISbxhQLAMrvtbuvD9N6JelEaIS/Pr62wUCinrq5bf1WRBGczt1v4wDhxFtVFNcMdUQ==", "path": "microsoft.extensions.options.configurationextensions/6.0.0", "hashPath": "microsoft.extensions.options.configurationextensions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-um1KU5kxcRp3CNuI8o/GrZtD4AIOXDk+RLsytjZ9QPok3ttLUelLKpilVPuaFT3TFjOhSibUAso0odbOaCDj3Q==", "path": "microsoft.extensions.primitives/7.0.0", "hashPath": "microsoft.extensions.primitives.7.0.0.nupkg.sha512"}, "Microsoft.Identity.Client/4.21.1": {"type": "package", "serviceable": true, "sha512": "sha512-vycgk7S/HAbHaUaK4Tid1fsWHsXdFRRP2KavAIOHCVV27zvuQfYAjXmMvctuuF4egydSumG58CwPZob3gWeYgQ==", "path": "microsoft.identity.client/4.21.1", "hashPath": "microsoft.identity.client.4.21.1.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/6.17.0": {"type": "package", "serviceable": true, "sha512": "sha512-I3cSVE185qF3a222/iQIdmBFhrhZBtz7wZ1RUUbMuHC1un79XCI7vggbWdmbqIttFcUoeziemadO6t+3FLjcSA==", "path": "microsoft.identitymodel.jsonwebtokens/6.17.0", "hashPath": "microsoft.identitymodel.jsonwebtokens.6.17.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/6.17.0": {"type": "package", "serviceable": true, "sha512": "sha512-Ix6/CMLDoo939NDf1ARDuGK6YERY7pAX9WYbfwb4gZqx7r52unMFIykJk+zlEBX7jjtbDz/0uzikQFvheV9KsQ==", "path": "microsoft.identitymodel.logging/6.17.0", "hashPath": "microsoft.identitymodel.logging.6.17.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/6.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-DFyXD0xylP+DknCT3hzJ7q/Q5qRNu0hO/gCU90O0ATdR0twZmlcuY9RNYaaDofXKVbzcShYNCFCGle2G/o8mkg==", "path": "microsoft.identitymodel.protocols/6.10.0", "hashPath": "microsoft.identitymodel.protocols.6.10.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-LVvMXAWPbPeEWTylDrxunlHH2wFyE4Mv0L4gZrJHC4HTESbWHquKZb/y/S8jgiQEDycOP0PDQvbG4RR/tr2TVQ==", "path": "microsoft.identitymodel.protocols.openidconnect/6.10.0", "hashPath": "microsoft.identitymodel.protocols.openidconnect.6.10.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/6.17.0": {"type": "package", "serviceable": true, "sha512": "sha512-mhOe+d9BQg5U45TkTCyXAFOjl7RvwaFj6v9qo8b+WFolkuGsfjSFfQ+WI9D3ho9sD/fK75gvL4JptmjLzyUPkw==", "path": "microsoft.identitymodel.tokens/6.17.0", "hashPath": "microsoft.identitymodel.tokens.6.17.0.nupkg.sha512"}, "Microsoft.IO.RecyclableMemoryStream/2.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-Oh1qXXFdJFcHozvb4H6XYLf2W0meZFuG0A+TfapFPj9z5fd4vxiARGEhAaLj/6XWQaMYIv4SH/9Q6H78Hw0E2Q==", "path": "microsoft.io.recyclablememorystream/2.3.2", "hashPath": "microsoft.io.recyclablememorystream.2.3.2.nupkg.sha512"}, "Microsoft.Net.Http.Headers/2.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-wHdwMv0QDDG2NWDSwax9cjkeQceGC1Qq53a31+31XpvTXVljKXRjWISlMoS/wZYKiqdqzuEvKFKwGHl+mt2jCA==", "path": "microsoft.net.http.headers/2.2.8", "hashPath": "microsoft.net.http.headers.2.2.8.nupkg.sha512"}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ==", "path": "microsoft.netcore.platforms/5.0.0", "hashPath": "microsoft.netcore.platforms.5.0.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.Win32.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-9ZQKCWxH7Ijp9BfahvL2Zyf1cJIk8XYLF6Yjzr2yi0b2cOut/HQ31qf1ThHAgCc3WiZMdnWcfJCgN82/0UunxA==", "path": "microsoft.win32.primitives/4.3.0", "hashPath": "microsoft.win32.primitives.4.3.0.nupkg.sha512"}, "Microsoft.Win32.Registry/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-KSrRMb5vNi0CWSGG1++id2ZOs/1QhRqROt+qgbEAdQuGjGrFcl4AOl4/exGPUYz2wUnU42nvJqon1T3U0kPXLA==", "path": "microsoft.win32.registry/4.7.0", "hashPath": "microsoft.win32.registry.4.7.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-2nXPrhdAyAzir0gLl8Yy8S5Mnm/uBSQQA7jEsILOS1MTyS7DbmV1NgViMtvV1sfCD1ebITpNwb1NIinKeJgUVQ==", "path": "microsoft.win32.systemevents/7.0.0", "hashPath": "microsoft.win32.systemevents.7.0.0.nupkg.sha512"}, "MySqlConnector/2.2.5": {"type": "package", "serviceable": true, "sha512": "sha512-6sinY78RvryhHwpup3awdjYO7d5hhWahb5p/1VDODJhSxJggV/sBbYuKK5IQF9TuzXABiddqUbmRfM884tqA3Q==", "path": "mysqlconnector/2.2.5", "hashPath": "mysqlconnector.2.2.5.nupkg.sha512"}, "Namotion.Reflection/1.0.19": {"type": "package", "serviceable": true, "sha512": "sha512-+eY1uabKkJEvjfHvr8uHLB1lvrb8S+akpXOe6fKMLTeZEMYIQywFL0/5+zrpNFklJIYzaEoApw2p3zFx357LjQ==", "path": "namotion.reflection/1.0.19", "hashPath": "namotion.reflection.1.0.19.nupkg.sha512"}, "NetDevPack.Security.JwtExtensions/6.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-guni36jjtLt1U9C+HdR3zVamjltpPWtVyWy+ntSS8DhshYhLRrmAz7WcdtOYbkAr8if25DYSk6G2M1eXjX2SXA==", "path": "netdevpack.security.jwtextensions/6.0.2", "hashPath": "netdevpack.security.jwtextensions.6.0.2.nupkg.sha512"}, "NETStandard.Library/1.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-WcSp3+vP+yHNgS8EV5J7pZ9IRpeDuARBPN28by8zqff1wJQXm26PVU8L3/fYLBJVU7BtDyqNVWq2KlCVvSSR4A==", "path": "netstandard.library/1.6.1", "hashPath": "netstandard.library.1.6.1.nupkg.sha512"}, "NetTopologySuite/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-v/eGXaJbPMy9kg6OK30mSuF0Ln6TXmWzPS8ojUMr/ecRiNpiZlEDXKq0CWIaE2Tc1uV2RZHLVPfF1QcoGs4ZYg==", "path": "nettopologysuite/2.1.0", "hashPath": "nettopologysuite.2.1.0.nupkg.sha512"}, "NetTopologySuite.IO.SqlServerBytes/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-TuyMB0VSlRJx86UrWeQ+SGgOMudvhIL1qJJdWJw78nWsXIzKRP4ooQAhhhCCH7n8q1lvd0/NW3ByaLlHxxNSPQ==", "path": "nettopologysuite.io.sqlserverbytes/2.0.0", "hashPath": "nettopologysuite.io.sqlserverbytes.2.0.0.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "Newtonsoft.Json.Bson/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-QYFyxhaABwmq3p/21VrZNYvCg3DaEoN/wUuw5nmfAf0X3HLjgupwhkEWdgfb9nvGAUIv3osmZoD3kKl4jxEmYQ==", "path": "newtonsoft.json.bson/1.0.2", "hashPath": "newtonsoft.json.bson.1.0.2.nupkg.sha512"}, "NLog/4.7.11": {"type": "package", "serviceable": true, "sha512": "sha512-A7EpoOjWesV5BPx1cOiBndazZq1VGdagIs6oK8ttcRDl5adCMtHiTqnsD5yYaOrMxOQeCjHBf/w3nKzCmhGbgw==", "path": "nlog/4.7.11", "hashPath": "nlog.4.7.11.nupkg.sha512"}, "NLog.Extensions.Logging/1.7.4": {"type": "package", "serviceable": true, "sha512": "sha512-/dMxI/lBPNhKe9uONCQaZ3JYSHJh9/yez4Uqc6yIo2hIGaoi8sbQg7WQqW5/1WAIquhrG/SeCEZUTGMiRSQAHw==", "path": "nlog.extensions.logging/1.7.4", "hashPath": "nlog.extensions.logging.1.7.4.nupkg.sha512"}, "NLog.Web.AspNetCore/4.14.0": {"type": "package", "serviceable": true, "sha512": "sha512-5fTviEu/f+mIxucGHmcx1q/mtJGo8EZbxACD57H4iJPZbLaD0LsLxMB2xDCxDO9uexlnfMMDoryq1nFdrl/C6A==", "path": "nlog.web.aspnetcore/4.14.0", "hashPath": "nlog.web.aspnetcore.4.14.0.nupkg.sha512"}, "NPOI/2.6.2": {"type": "package", "serviceable": true, "sha512": "sha512-s5lxJQ1Xy2nr3yDvoMH6og2cb2I8reIrUROf2afjKucS+pWNZG07kwITo+CCS3KaXNiPjUn1YaS1PUf8fT9cHg==", "path": "npoi/2.6.2", "hashPath": "npoi.2.6.2.nupkg.sha512"}, "Oracle.ManagedDataAccess.Core/3.21.100": {"type": "package", "serviceable": true, "sha512": "sha512-nsqyUE+v246WB0SOnR1u9lfZxYoNcdj1fRjTt7TOhCN0JurEc6+qu+mMe+dl1sySB2UpyWdfqHG1iSQJYaXEfA==", "path": "oracle.manageddataaccess.core/3.21.100", "hashPath": "oracle.manageddataaccess.core.3.21.100.nupkg.sha512"}, "Pipelines.Sockets.Unofficial/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-7hzHplEIVOGBl5zOQZGX/DiJDHjq+RVRVrYgDiqXb6RriqWAdacXxp+XO9WSrATCEXyNOUOQg9aqQArsjase/A==", "path": "pipelines.sockets.unofficial/2.2.0", "hashPath": "pipelines.sockets.unofficial.2.2.0.nupkg.sha512"}, "Polly/7.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-E6CeKyS513j7taKAq4q2MESDBvzuzWnR1rQ2Y2zqJvpiVtKMm699Aubb20MUPBDmb0Ov8PmcLHTCVFdCjoy2kA==", "path": "polly/7.2.2", "hashPath": "polly.7.2.2.nupkg.sha512"}, "Polly.Extensions.Http/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-drrG+hB3pYFY7w1c3BD+lSGYvH2oIclH8GRSehgfyP5kjnFnHKQuuBhuHLv+PWyFuaTDyk/vfRpnxOzd11+J8g==", "path": "polly.extensions.http/3.0.0", "hashPath": "polly.extensions.http.3.0.0.nupkg.sha512"}, "Pomelo.EntityFrameworkCore.MySql/6.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-KvlZ800CnEuEGnxj5OT1fCKGjQXxW5kpPlCP91JqBYG+2Z3927eqXmlX6LLKUt4swqE8ZsEQ+Zkpab8bqstf4g==", "path": "pomelo.entityframeworkcore.mysql/6.0.2", "hashPath": "pomelo.entityframeworkcore.mysql.6.0.2.nupkg.sha512"}, "QRCoder/1.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-W8KwCwsOJe9SI7Cm7XeIMawd08F/US6xNw34lg2Qnx6m7GYYakxbyBQaNoW1Q75oJOmX/32sJUZr6Cix2B6GUQ==", "path": "qrcoder/1.4.1", "hashPath": "qrcoder.1.4.1.nupkg.sha512"}, "Quartz/3.6.3": {"type": "package", "serviceable": true, "sha512": "sha512-t7hBo4OLbKigRlWW/b3qRO4peWjVg9NFx6nPgMo/Waie67Rl9ODsfHdBkD+/4sPlAvB9mGWWO84B/ZpVSB81lQ==", "path": "quartz/3.6.3", "hashPath": "quartz.3.6.3.nupkg.sha512"}, "Quartz.Extensions.DependencyInjection/3.6.3": {"type": "package", "serviceable": true, "sha512": "sha512-vsgdCH+eFxB6eM3e2BiilMR6oymfWopBh4sEJLrUboYKNI+jNk2hBrPRAzUg6KhgKzqSNKPHxAUtQ1z+lOacEQ==", "path": "quartz.extensions.dependencyinjection/3.6.3", "hashPath": "quartz.extensions.dependencyinjection.3.6.3.nupkg.sha512"}, "Quartz.Extensions.Hosting/3.6.3": {"type": "package", "serviceable": true, "sha512": "sha512-347S//lDKMMj9fXQFhpvab0E6G7z7+7D6zbPHJMcMxEbPNZfQJ1BhYV8qJPkcC9DW7ITxQBcrNjeXi/c8h2sOQ==", "path": "quartz.extensions.hosting/3.6.3", "hashPath": "quartz.extensions.hosting.3.6.3.nupkg.sha512"}, "Refit/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-gXRmIy3Va0mwwr8usnqVRQFoFIJaJGlWTTXBlwxIkHB/xwXnq1Ybs1YNA2BM1O4G46JLLlGjg6YOwrTZusuY3Q==", "path": "refit/8.0.0", "hashPath": "refit.8.0.0.nupkg.sha512"}, "Refit.Newtonsoft.Json/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-kdbgxYm44SPP1hV0Nw41N3viYYgdBICTpYp7AuqAAl4kJHV2WyRO4fnJ8hxCQm0pTHwrjg+EMSeJBaC6OVFkSw==", "path": "refit.newtonsoft.json/8.0.0", "hashPath": "refit.newtonsoft.json.8.0.0.nupkg.sha512"}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-HdSSp5MnJSsg08KMfZThpuLPJpPwE5hBXvHwoKWosyHHfe8Mh5WKT0ylEOf6yNzX6Ngjxe4Whkafh5q7Ymac4Q==", "path": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-+yH1a49wJMy8Zt4yx5RhJrxO/DBDByAiCzNwiETI+1S4mPdCu0OY4djdciC7Vssk0l22wQaDLrXxXkp+3+7bVA==", "path": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c3YNH1GQJbfIPJeCnr4avseugSqPrxwIqzthYyZDN6EuOyNOzq+y2KSUfRcXauya1sF4foESTgwM5e1A8arAKw==", "path": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.native.System/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "path": "runtime.native.system/4.3.0", "hashPath": "runtime.native.system.4.3.0.nupkg.sha512"}, "runtime.native.System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-INBPonS5QPEgn7naufQFXJEp3zX6L4bwHgJ/ZH78aBTpeNfQMtf7C6VrAFhlq2xxWBveIOWyFzQjJ8XzHMhdOQ==", "path": "runtime.native.system.io.compression/4.3.0", "hashPath": "runtime.native.system.io.compression.4.3.0.nupkg.sha512"}, "runtime.native.System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZVuZJqnnegJhd2k/PtAbbIcZ3aZeITq3sj06oKfMBSfphW3HDmk/t4ObvbOk/JA/swGR0LNqMksAh/f7gpTROg==", "path": "runtime.native.system.net.http/4.3.0", "hashPath": "runtime.native.system.net.http.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DloMk88juo0OuOWr56QG7MNchmafTLYWvABy36izkrLI5VledI0rq28KGs1i9wbpeT9NPQrx/wTf8U2vazqQ3Q==", "path": "runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-NS1U+700m4KFRHR5o4vo9DSlTmlCKu/u7dtE5sUHVIPB+xpXxYQvgBgA6wEIeCz6Yfn0Z52/72WYsToCEPJnrw==", "path": "runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-b3pthNgxxFcD+Pc0WSEoC0+md3MyhRS6aCEeenvNE3Fdw1HyJ18ZhRFVJJzIeR/O/jpxPboB805Ho0T3Ul7w8A==", "path": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KeLz4HClKf+nFS7p/6Fi/CqyLXh81FpiGzcmuS8DGi9lUqSnZ6Es23/gv2O+1XVGfrbNmviF7CckBpavkBoIFQ==", "path": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kVXCuMTrTlxq4XOOMAysuNwsXWpYeboGddNGpIgNSZmv1b6r/s/DPk0fYMB7Q5Qo4bY68o48jt4T4y5BVecbCQ==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X7IdhILzr4ROXd8mI1BUCQMSHSQwelUlBjF1JyTKCjXaOGn2fB4EKBxQbCK2VjO3WaWIdlXZL3W6TiIVnrhX4g==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-nyFNiCk/r+VOiIqreLix8yN+q3Wga9+SE8BCgkf+2BwEKiNx6DyvFjCgkfV743/grxv8jHJ8gUK4XEQw7yzRYg==", "path": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ytoewC6wGorL7KoCAvRfsgoJPJbNq+64k2SqW6JcOAebWsFUvCCYgfzQMrnpvPiEl4OrblUlhF2ji+Q1+SVLrQ==", "path": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-I8bKw2I8k58Wx7fMKQJn2R8lamboCAiHfHeV/pS65ScKWMMI0+wJkLYlEKvgW1D/XvSl/221clBoR2q9QNNM7A==", "path": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VB5cn/7OzUfzdnC8tqAIMQciVLiq2epm2NrAm1E9OjNRyG4lVhfR61SMcLizejzQP8R8Uf/0l5qOIbUEi+RdEg==", "path": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "Senparc.CO2NET/2.1.7.3": {"type": "package", "serviceable": true, "sha512": "sha512-0fexvBmuZV8K/cAP/3RtvJF6tuC2tpFxcXO6f3WVnE1h7OMIsE5AK7hMbM59dKwSpttxxmILTsKnUkG+zxwzkg==", "path": "senparc.co2net/2.1.7.3", "hashPath": "senparc.co2net.2.1.7.3.nupkg.sha512"}, "Senparc.CO2NET.APM/1.2.4.6": {"type": "package", "serviceable": true, "sha512": "sha512-ca7o0wxuXpZi2G5Vm96sP/9RVa+psdQ46WG4CB6s9dr+9aNNamslba6QEdKt95jqFp6kDpYwv/T/cLEXlOrMXg==", "path": "senparc.co2net.apm/1.2.4.6", "hashPath": "senparc.co2net.apm.1.2.4.6.nupkg.sha512"}, "Senparc.NeuChar/*******": {"type": "package", "serviceable": true, "sha512": "sha512-QNYhFBKsX6X7oPluOvlSenBoXLV/RyDX0QPj3NoAI6TCN3OLCg02hS40FoERCLr1Uc5of+M6HfK91wkLfpTzcw==", "path": "senparc.neuchar/*******", "hashPath": "senparc.neuchar.*******.nupkg.sha512"}, "Senparc.NeuChar.App/1.1.4.5": {"type": "package", "serviceable": true, "sha512": "sha512-FIoPoJT7joNFhE09Q0JuU/onjXo3yJ2axdddnEX0NFJA+XNSJvLQuIR+g+5TDzxzJbzoFmQmPJ0r7UG/Rmb9Pg==", "path": "senparc.neuchar.app/1.1.4.5", "hashPath": "senparc.neuchar.app.1.1.4.5.nupkg.sha512"}, "Senparc.Weixin/********": {"type": "package", "serviceable": true, "sha512": "sha512-ZQQ3u6RbWDzKDQCt8EHg+hTMCTtOEaTey9c5frMNAfcEjur6tts6NGSK18d3ZjZY9fFmDC5Cr0xzn1eJAtMkSA==", "path": "senparc.weixin/********", "hashPath": "senparc.weixin.********.nupkg.sha512"}, "Senparc.Weixin.MP/**********": {"type": "package", "serviceable": true, "sha512": "sha512-33a7GFfjeS1UV/j+mbgIrQD8ewy17LmyTO2o4NyIYBR8D3wCu18tFie+BlrYJta2yEwXoa8tJH+WDQN5TdPSYA==", "path": "senparc.weixin.mp/**********", "hashPath": "senparc.weixin.mp.**********.nupkg.sha512"}, "Senparc.Weixin.WxOpen/3.15.13": {"type": "package", "serviceable": true, "sha512": "sha512-6RcsPkKka5x98y7wwvabCz0YAxY/SUO79K35RurfhSfR4uR9eXWlEOwfZpUp9TstipIg46vrguaWN1+HBYvvXw==", "path": "senparc.weixin.wxopen/3.15.13", "hashPath": "senparc.weixin.wxopen.3.15.13.nupkg.sha512"}, "SharpZipLib/1.3.3": {"type": "package", "serviceable": true, "sha512": "sha512-N8+hwhsKZm25tDJfWpBSW7EGhH/R7EMuiX+KJ4C4u+fCWVc1lJ5zg1u3S1RPPVYgTqhx/C3hxrqUpi6RwK5+Tg==", "path": "sharpziplib/1.3.3", "hashPath": "sharpziplib.1.3.3.nupkg.sha512"}, "SixLabors.Fonts/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LFQsCZlV0xlUyXAOMUo5kkSl+8zAQXXbbdwWchtk0B4o7zotZhQsQOcJUELGHdfPfm/xDAsz6hONAuV25bJaAg==", "path": "sixlabors.fonts/1.0.0", "hashPath": "sixlabors.fonts.1.0.0.nupkg.sha512"}, "SixLabors.ImageSharp/2.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-K6F39YCm/MaOYT0iw9lMa8tBcz2eF8JaNMneo0EnRrml6k+8EQNSKXqb8yJTq2HHkWLlRHZl6UKMn02YmR/G3g==", "path": "sixlabors.imagesharp/2.1.4", "hashPath": "sixlabors.imagesharp.2.1.4.nupkg.sha512"}, "SkiaSharp/2.88.8": {"type": "package", "serviceable": true, "sha512": "sha512-bRkp3uKp5ZI8gXYQT57uKwil1uobb2p8c69n7v5evlB/2JNcMAXVcw9DZAP5Ig3WSvgzGm2YSn27UVeOi05NlA==", "path": "skiasharp/2.88.8", "hashPath": "skiasharp.2.88.8.nupkg.sha512"}, "SkiaSharp.NativeAssets.macOS/2.88.8": {"type": "package", "serviceable": true, "sha512": "sha512-6Kn5TSkKlfyS6azWHF3Jk2sW5C4jCE5uSshM/5AbfFrR+5n6qM5XEnz9h4VaVl7LTxBvHvMkuPb/3bpbq0vxTw==", "path": "skiasharp.nativeassets.macos/2.88.8", "hashPath": "skiasharp.nativeassets.macos.2.88.8.nupkg.sha512"}, "SkiaSharp.NativeAssets.Win32/2.88.8": {"type": "package", "serviceable": true, "sha512": "sha512-O9QXoWEXA+6cweR4h3BOnwMz+pO9vL9mXdjLrpDd0w1QzCgWmLQBxa1VgySDITiH7nQndrDG1h6937zm9pLj1Q==", "path": "skiasharp.nativeassets.win32/2.88.8", "hashPath": "skiasharp.nativeassets.win32.2.88.8.nupkg.sha512"}, "StackExchange.Redis/2.2.50": {"type": "package", "serviceable": true, "sha512": "sha512-jss1e27GaoI9efYtI+ZVGm2+QYKjFr92n/Jdjs4NY2jf/fFsu8lCNfaZ6tsR5nc5u67htNSRlyNkxFBuD8NVTA==", "path": "stackexchange.redis/2.2.50", "hashPath": "stackexchange.redis.2.2.50.nupkg.sha512"}, "System.AppContext/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-fKC+rmaLfeIzUhagxY17Q9siv/sPrjjKcfNg1Ic8IlQkZLipo8ljcaZQu4VtI4Jqbzjc2VTjzGLF6WmsRXAEgA==", "path": "system.appcontext/4.3.0", "hashPath": "system.appcontext.4.3.0.nupkg.sha512"}, "System.Buffers/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-pL2ChpaRRWI/p4LXyy4RgeWlYF2sgfj/pnVMvBqwNFr5cXg7CXNnWZWxrOONLg8VGdFB8oB+EG2Qw4MLgTOe+A==", "path": "system.buffers/4.5.0", "hashPath": "system.buffers.4.5.0.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Concurrent/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "path": "system.collections.concurrent/4.3.0", "hashPath": "system.collections.concurrent.4.3.0.nupkg.sha512"}, "System.Collections.Immutable/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-l4zZJ1WU2hqpQQHXz1rvC3etVZN+2DLmQMO79FhOTZHMn8tDRr+WU287sbomD0BETlmKDn0ygUgVy9k5xkkJdA==", "path": "system.collections.immutable/6.0.0", "hashPath": "system.collections.immutable.6.0.0.nupkg.sha512"}, "System.Collections.NonGeneric/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-prtjIEMhGUnQq6RnPEYLpFt8AtLbp9yq2zxOSrY7KJJZrw25Fi97IzBqY7iqssbM61Ek5b8f3MG/sG1N2sN5KA==", "path": "system.collections.nongeneric/4.3.0", "hashPath": "system.collections.nongeneric.4.3.0.nupkg.sha512"}, "System.Collections.Specialized/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Epx8PoVZR0iuOnJJDzp7pWvdfMMOAvpUo95pC4ScH2mJuXkKA2Y4aR3cG9qt2klHgSons1WFh4kcGW7cSXvrxg==", "path": "system.collections.specialized/4.3.0", "hashPath": "system.collections.specialized.4.3.0.nupkg.sha512"}, "System.ComponentModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyGn1jGRZVfxnh8EdvDCi71v3bMXrsu8aYJOwoV7SNDLVhiEqwP86pPMyRGsDsxhXAm2b3o9OIqeETfN5qfezw==", "path": "system.componentmodel/4.3.0", "hashPath": "system.componentmodel.4.3.0.nupkg.sha512"}, "System.ComponentModel.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-j8GUkCpM8V4d4vhLIIoBLGey2Z5bCkMVNjEZseyAlm4n5arcsJOeI3zkUP+zvZgzsbLTYh4lYeP/ZD/gdIAPrw==", "path": "system.componentmodel.primitives/4.3.0", "hashPath": "system.componentmodel.primitives.4.3.0.nupkg.sha512"}, "System.ComponentModel.TypeConverter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-16pQ6P+EdhcXzPiEK4kbA953Fu0MNG2ovxTZU81/qsCd1zPRsKc3uif5NgvllCY598k6bI0KUyKW8fanlfaDQg==", "path": "system.componentmodel.typeconverter/4.3.0", "hashPath": "system.componentmodel.typeconverter.4.3.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-jXw9MlUu/kRfEU0WyTptAVueupqIeE3/rl0EZDMlf8pcvJnitQ8HeVEp69rZdaStXwTV72boi/Bhw8lOeO+U2w==", "path": "system.configuration.configurationmanager/6.0.1", "hashPath": "system.configuration.configurationmanager.6.0.1.nupkg.sha512"}, "System.Console/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DHDrIxiqk1h03m6khKWV2X8p/uvN79rgSqpilL6uzpmSfxfU5ng8VcPtW4qsDsQDHiTv6IPV9TmD5M/vElPNLg==", "path": "system.console/4.3.0", "hashPath": "system.console.4.3.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KiLYDu2k2J82Q9BJpWiuQqCkFjRBWVq4jDzKKWawVi9KWzyD0XG3cmfX0vqTQlL14Wi9EufJrbL0+KCLTbqWiQ==", "path": "system.diagnostics.diagnosticsource/6.0.1", "hashPath": "system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512"}, "System.Diagnostics.EventLog/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lcyUiXTsETK2ALsZrX+nWuHSIQeazhqPphLfaRxzdGaG93+0kELqpgEHtwWOlQe7+jSFnKwaCAgL4kjeZCQJnw==", "path": "system.diagnostics.eventlog/6.0.0", "hashPath": "system.diagnostics.eventlog.6.0.0.nupkg.sha512"}, "System.Diagnostics.PerformanceCounter/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-dDl7Gx3bmSrM2k2ZIm+ucEJnLloZRyvfQF1DvfvATcGF3jtaUBiPvChma+6ZcZzxWMirN3kCywkW7PILphXyMQ==", "path": "system.diagnostics.performancecounter/6.0.1", "hashPath": "system.diagnostics.performancecounter.6.0.1.nupkg.sha512"}, "System.Diagnostics.Tools/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-UUvkJfSYJMM6x527dJg2VyWPSRqIVB0Z7dbjHst1zmwTXz5CcXSYJFWRpuigfbO1Lf7yfZiIaEUesfnl/g5EyA==", "path": "system.diagnostics.tools/4.3.0", "hashPath": "system.diagnostics.tools.4.3.0.nupkg.sha512"}, "System.Diagnostics.TraceSource/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VnYp1NxGx8Ww731y2LJ1vpfb/DKVNKEZ8Jsh5SgQTZREL/YpWRArgh9pI8CDLmgHspZmLL697CaLvH85qQpRiw==", "path": "system.diagnostics.tracesource/4.3.0", "hashPath": "system.diagnostics.tracesource.4.3.0.nupkg.sha512"}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "path": "system.diagnostics.tracing/4.3.0", "hashPath": "system.diagnostics.tracing.4.3.0.nupkg.sha512"}, "System.DirectoryServices/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-935IbO7h5FDGYxeO3cbx/CuvBBuk/VI8sENlfmXlh1pupNOB3LAGzdYdPY8CawGJFP7KNrHK5eUlsFoz3F6cuA==", "path": "system.directoryservices/6.0.1", "hashPath": "system.directoryservices.6.0.1.nupkg.sha512"}, "System.DirectoryServices.Protocols/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ndUZlEkAMc1XzM0xGN++SsJrNhRkIHaKI8+te325vrUgoLT1ufWNI6KB8FFrL7NpRMHPrdxP99aF3fHbAPxW0A==", "path": "system.directoryservices.protocols/6.0.1", "hashPath": "system.directoryservices.protocols.6.0.1.nupkg.sha512"}, "System.Drawing.Common/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-K<PERSON>+oBU38pxkKPxvLcLfIkOV5Ien8ReN78wro7OF5/erwcmortzeFx+iBswlh2Vz6gVne0khocQudGwaO1Ey6A==", "path": "system.drawing.common/7.0.0", "hashPath": "system.drawing.common.7.0.0.nupkg.sha512"}, "System.Dynamic.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-SNVi1E/vfWUAs/WYKhE9+qlS6KqK0YVhnlT0HQtr8pMIA8YX3lwy3uPMownDwdYISBdmAF/2holEIldVp85Wag==", "path": "system.dynamic.runtime/4.3.0", "hashPath": "system.dynamic.runtime.4.3.0.nupkg.sha512"}, "System.Formats.Asn1/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-T6fD00dQ3NTbPDy31m4eQUwKW84s03z0N2C8HpOklyeaDgaJPa/TexP4/SkORMSOwc7WhKifnA6Ya33AkzmafA==", "path": "system.formats.asn1/6.0.0", "hashPath": "system.formats.asn1.6.0.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.Globalization.Calendars/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GUlBtdOWT4LTV3I+9/PJW+56AnnChTaOqqTLFtdmype/L500M2LIyXgmtd9X2P2VOkmJd5c67H5SaC2QcL1bFA==", "path": "system.globalization.calendars/4.3.0", "hashPath": "system.globalization.calendars.4.3.0.nupkg.sha512"}, "System.Globalization.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-FhKmdR6MPG+pxow6wGtNAWdZh7noIOpdD5TwQ3CprzgIE1bBBoim0vbR1+AWsWjQmU7zXHgQo4TWSP6lCeiWcQ==", "path": "system.globalization.extensions/4.3.0", "hashPath": "system.globalization.extensions.4.3.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/6.17.0": {"type": "package", "serviceable": true, "sha512": "sha512-G3rY4WLr54Mo+97+AEq0ANpiKvW7E8Qu5bKWfVMa7rkyJtvrOxUqp/OLqrGw/6JDbD5GlxnAtFKukGteUuB0rQ==", "path": "system.identitymodel.tokens.jwt/6.17.0", "hashPath": "system.identitymodel.tokens.jwt.6.17.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YHndyoiV90iu4iKG115ibkhrG+S3jBm8Ap9OwoUAzO5oPDAWcr0SFwQFm0HjM8WkEZWo0zvLTyLmbvTkW1bXgg==", "path": "system.io.compression/4.3.0", "hashPath": "system.io.compression.4.3.0.nupkg.sha512"}, "System.IO.Compression.ZipFile/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-G4HwjEsgIwy3JFBduZ9quBkAu+eUwjIdJleuNSgmUojbH6O3mlvEIme+GHx/cLlTAPcrnnL7GqvB9pTlWRfhOg==", "path": "system.io.compression.zipfile/4.3.0", "hashPath": "system.io.compression.zipfile.4.3.0.nupkg.sha512"}, "System.IO.FileSystem/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "path": "system.io.filesystem/4.3.0", "hashPath": "system.io.filesystem.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "path": "system.io.filesystem.primitives/4.3.0", "hashPath": "system.io.filesystem.primitives.4.3.0.nupkg.sha512"}, "System.IO.Pipelines/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-irMYm3vhVgRsYvHTU5b2gsT2CwT/SMM6LZFzuJjpIvT5Z4CshxNsaoBC1X/LltwuR3Opp8d6jOS/60WwOb7Q2Q==", "path": "system.io.pipelines/5.0.0", "hashPath": "system.io.pipelines.5.0.0.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Dynamic.Core/1.3.5": {"type": "package", "serviceable": true, "sha512": "sha512-G8aRAVHSQ/Jmt6dekX+z4K3L6ecX3CizaIYSQoDUolPRIPBYmBCabT+/pq5fMAFM57aOchFn8PUufuVfLjvEZQ==", "path": "system.linq.dynamic.core/1.3.5", "hashPath": "system.linq.dynamic.core.1.3.5.nupkg.sha512"}, "System.Linq.Expressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "path": "system.linq.expressions/4.3.0", "hashPath": "system.linq.expressions.4.3.0.nupkg.sha512"}, "System.Memory/4.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-3oDzvc/zzetpTKWMShs1AADwZjQ/36HnsufHRPcOjyRAAMLDlu2iD33MBI2opxnezcVUtXyqDXXjoFMOU9c7SA==", "path": "system.memory/4.5.3", "hashPath": "system.memory.4.5.3.nupkg.sha512"}, "System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-sYg+FtILtRQuYWSIAuNOELwVuVsxVyJGWQyOnlAzhV4xvhyFnON1bAzYYC+jjRW8JREM45R0R5Dgi8MTC5sEwA==", "path": "system.net.http/4.3.0", "hashPath": "system.net.http.4.3.0.nupkg.sha512"}, "System.Net.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-qOu+hDwFwoZPbzPvwut2qATe3ygjeQBDQj91xlsaqGFQUI5i4ZnZb8yyQuLGpDGivEPIt8EJkd1BVzVoP31FXA==", "path": "system.net.primitives/4.3.0", "hashPath": "system.net.primitives.4.3.0.nupkg.sha512"}, "System.Net.Sockets/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-m6icV6TqQOAdgt5N/9I5KNpjom/5NFtkmGseEH+AK/hny8XrytLH3+b5M8zL/Ycg3fhIocFpUMyl/wpFnVRvdw==", "path": "system.net.sockets/4.3.0", "hashPath": "system.net.sockets.4.3.0.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.ObjectModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "path": "system.objectmodel/4.3.0", "hashPath": "system.objectmodel.4.3.0.nupkg.sha512"}, "System.Private.ServiceModel/4.8.1": {"type": "package", "serviceable": true, "sha512": "sha512-neXeXhKIlCUBCg5rxKpYnae4vQSiMWjhqeTgsMedHvKKUTsmP98je+G8oPmDdY9uZVruvxqRGjcljn1XYNhC+w==", "path": "system.private.servicemodel/4.8.1", "hashPath": "system.private.servicemodel.4.8.1.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.DispatchProxy/4.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-C1sMLwIG6ILQ2bmOT4gh62V6oJlyF4BlHcVMrOoor49p0Ji2tA8QAoqyMcIhAdH6OHKJ8m7BU+r4LK2CUEOKqw==", "path": "system.reflection.dispatchproxy/4.7.1", "hashPath": "system.reflection.dispatchproxy.4.7.1.nupkg.sha512"}, "System.Reflection.Emit/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-VR4kk8XLKebQ4MZuKuIni/7oh+QGFmZW3qORd1GvBq/8026OpW501SzT/oypwiQl4TvT8ErnReh/NzY9u+C6wQ==", "path": "system.reflection.emit/4.7.0", "hashPath": "system.reflection.emit.4.7.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "path": "system.reflection.emit.lightweight/4.3.0", "hashPath": "system.reflection.emit.lightweight.4.3.0.nupkg.sha512"}, "System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "path": "system.reflection.extensions/4.3.0", "hashPath": "system.reflection.extensions.4.3.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "path": "system.reflection.typeextensions/4.3.0", "hashPath": "system.reflection.typeextensions.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.Caching/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-NdvNRjTPxYvIEhXQszT9L9vJhdQoX6AQ0AlhjTU+5NqFQVuacJTfhPVAvtGWNA2OJCqRiR/okBcZgMwI6MqcZg==", "path": "system.runtime.caching/4.7.0", "hashPath": "system.runtime.caching.4.7.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cbz4YJMqRDR7oLeMRbdYv7mYzc++17lNhScCX0goO2XpGWdvAt60CGN+FHdePUEHCe/Jy9jUlvNAiNdM+7jsOw==", "path": "system.runtime.interopservices.runtimeinformation/4.3.0", "hashPath": "system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512"}, "System.Runtime.Numerics/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yMH+MfdzHjy17l2KESnPiF2dwq7T+xLnSJar7slyimAkUh/gTrS9/UQOtv7xarskJ2/XDSNvfLGOBQPjL7PaHQ==", "path": "system.runtime.numerics/4.3.0", "hashPath": "system.runtime.numerics.4.3.0.nupkg.sha512"}, "System.Security.AccessControl/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AUADIc0LIEQe7MzC+I0cl0rAT8RrTAKFHl53yHjEUzNVIaUlhFY11vc2ebiVJzVBuOzun6F7FBA+8KAbGTTedQ==", "path": "system.security.accesscontrol/6.0.0", "hashPath": "system.security.accesscontrol.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.Algorithms/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-W1kd2Y8mYSCgc3ULTAZ0hOP2dSdG5YauTb1089T0/kRcN2MpSAW1izOFROrJgxSlMn3ArsgHXagigyi+ibhevg==", "path": "system.security.cryptography.algorithms/4.3.0", "hashPath": "system.security.cryptography.algorithms.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-WG3r7EyjUe9CMPFSs6bty5doUqT+q9pbI80hlNzo2SkPkZ4VTuZkGWjpp77JB8+uaL4DFPRdBsAY+DX3dBK92A==", "path": "system.security.cryptography.cng/4.5.0", "hashPath": "system.security.cryptography.cng.4.5.0.nupkg.sha512"}, "System.Security.Cryptography.Csp/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X4s/FCkEUnRGnwR3aSfVIkldBmtURMhmexALNTwpjklzxWU7yjMk7GHLKOZTNkgnWnE0q7+BCf9N2LVRWxewaA==", "path": "system.security.cryptography.csp/4.3.0", "hashPath": "system.security.cryptography.csp.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1DEWjZZly9ae9C79vFwqaO5kaOlI5q+3/55ohmq/7dpDyDfc8lYe7YVxJUZ5MF/NtbkRjwFRo14yM4OEo9EmDw==", "path": "system.security.cryptography.encoding/4.3.0", "hashPath": "system.security.cryptography.encoding.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-h4CEgOgv5PKVF/HwaHzJRiVboL2THYCou97zpmhjghx5frc7fIvlkY1jL+lnIQyChrJDMNEXS6r7byGif8Cy4w==", "path": "system.security.cryptography.openssl/4.3.0", "hashPath": "system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ynmbW2GjIGg9K1wXmVIRs4IlyDolf0JXNpzFQ8JCVgwM+myUC2JeUggl2PwQig2PNVMegKmN1aAx7WPQ8tI3vA==", "path": "system.security.cryptography.pkcs/6.0.1", "hashPath": "system.security.cryptography.pkcs.6.0.1.nupkg.sha512"}, "System.Security.Cryptography.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7bDIyVFNL/xKeFHjhobUAQqSpJq9YTOpbEs6mR233Et01STBMXNAc/V+BM6dwYGc95gVh/Zf+iVXWzj3mE8DWg==", "path": "system.security.cryptography.primitives/4.3.0", "hashPath": "system.security.cryptography.primitives.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rp1gMNEZpvx9vP0JW0oHLxlf8oSiQgtno77Y4PLUBjSiDYoD77Y8uXHr1Ea5XG4/pIKhqAdxZ8v8OTUtqo9PeQ==", "path": "system.security.cryptography.protecteddata/6.0.0", "hashPath": "system.security.cryptography.protecteddata.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.X509Certificates/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-t2Tmu6Y2NtJ2um0RtcuhP7ZdNNxXEgUm2JeoA/0NvlMjAhKCnM1NX07TDl3244mVp3QU6LPEhT3HTtH1uF7IYw==", "path": "system.security.cryptography.x509certificates/4.3.0", "hashPath": "system.security.cryptography.x509certificates.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Xml/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-5e5bI28T0x73AwTsbuFP4qSRzthmU2C0Gqgg3AZ3KTxmSyA+Uhk31puA3srdaeWaacVnHhLdJywCzqOiEpbO/w==", "path": "system.security.cryptography.xml/6.0.1", "hashPath": "system.security.cryptography.xml.6.0.1.nupkg.sha512"}, "System.Security.Permissions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-T/uuc7AklkDoxmcJ7LGkyX1CcSviZuLCa4jg3PekfJ7SU0niF0IVTXwUiNVP9DSpzou2PpxJ+eNY2IfDM90ZCg==", "path": "system.security.permissions/6.0.0", "hashPath": "system.security.permissions.6.0.0.nupkg.sha512"}, "System.Security.Principal.Windows/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-ojD0PX0XhneCsUbAZVKdb7h/70vyYMDYs85lwEI+LngEONe/17A0cFaRFqZU+sOEidcVswYWikYOQ9PPfjlbtQ==", "path": "system.security.principal.windows/4.7.0", "hashPath": "system.security.principal.windows.4.7.0.nupkg.sha512"}, "System.ServiceModel.Primitives/4.8.1": {"type": "package", "serviceable": true, "sha512": "sha512-XjzGCqvWZBHnw2ym8t9PVeTm5Tsp7lSDKEwhDZAU4Jnw+6+zEDu/2ZQYQbRqBR0t/KpfNA4Aya/5aONKJnuyEw==", "path": "system.servicemodel.primitives/4.8.1", "hashPath": "system.servicemodel.primitives.4.8.1.nupkg.sha512"}, "System.ServiceProcess.ServiceController/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qMBvG8ZFbkXoe0Z5/D7FAAadfPkH2v7vSuh2xsLf3U6jNoejpIdeV18A0htiASsLK1CCAc/p59kaLXlt2yB1gw==", "path": "system.serviceprocess.servicecontroller/6.0.0", "hashPath": "system.serviceprocess.servicecontroller.6.0.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NyscU59xX6Uo91qvhOs2Ccho3AR2TnZPomo1Z0K6YpyztBPM/A5VbkzOO19sy3A3i1TtEnTxA7bCe3Us+r5MWg==", "path": "system.text.encoding.codepages/5.0.0", "hashPath": "system.text.encoding.codepages.5.0.0.nupkg.sha512"}, "System.Text.Encoding.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YVMK0Bt/A43RmwizJoZ22ei2nmrhobgeiYwFzC4YAN+nue8RF6djXDMog0UCn+brerQoYVyaS+ghy9P/MUVcmw==", "path": "system.text.encoding.extensions/4.3.0", "hashPath": "system.text.encoding.extensions.4.3.0.nupkg.sha512"}, "System.Text.Encodings.Web/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Vg8eB5Tawm1IFqj4TVK1czJX89rhFxJo9ELqc/Eiq0eXy13RK00eubyU6TJE6y+GQXjyV5gSfiewDUZjQgSE0w==", "path": "system.text.encodings.web/6.0.0", "hashPath": "system.text.encodings.web.6.0.0.nupkg.sha512"}, "System.Text.Json/6.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-1Bi8pSdTTVNVKcGFiU1o9Iq6FwFyGQmgBgGHxy9pPvZANXc1cT6D72j+r/H/NsyVnWxKIxOgI3o7DrAwC3zcaA==", "path": "system.text.json/6.0.3", "hashPath": "system.text.json.6.0.3.nupkg.sha512"}, "System.Text.RegularExpressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-RpT2DA+L660cBt1FssIE9CAGpLFdFPuheB7pLpKpn6ZXNby7jDERe8Ua/Ne2xGiwLVG2JOqziiaVCGDon5sKFA==", "path": "system.text.regularexpressions/4.3.0", "hashPath": "system.text.regularexpressions.4.3.0.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-npvJkVKl5rKXrtl1Kkm6OhOUaYGEiF9wFbppFRWSMoApKzt2PiPHT2Bb8a5sAWxprvdOAtvaARS9QYMznEUtug==", "path": "system.threading.tasks.extensions/4.3.0", "hashPath": "system.threading.tasks.extensions.4.3.0.nupkg.sha512"}, "System.Threading.Thread/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OHmbT+Zz065NKII/ZHcH9XO1dEuLGI1L2k7uYss+9C1jLxTC9kTZZuzUOyXHayRk+dft9CiDf3I/QZ0t8JKyBQ==", "path": "system.threading.thread/4.3.0", "hashPath": "system.threading.thread.4.3.0.nupkg.sha512"}, "System.Threading.Timer/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Z6YfyYTCg7lOZjJzBjONJTFKGN9/NIYKSxhU5GRd+DTwHSZyvWp1xuI5aR+dLg+ayyC5Xv57KiY4oJ0tMO89fQ==", "path": "system.threading.timer/4.3.0", "hashPath": "system.threading.timer.4.3.0.nupkg.sha512"}, "System.Windows.Extensions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IXoJOXIqc39AIe+CIR7koBtRGMiCt/LPM3lI+PELtDIy9XdyeSrwXFdWV9dzJ2Awl0paLWUaknLxFQ5HpHZUog==", "path": "system.windows.extensions/6.0.0", "hashPath": "system.windows.extensions.6.0.0.nupkg.sha512"}, "System.Xml.ReaderWriter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GrprA+Z0RUXaR4N7/eW71j1rgMnEnEVlgii49GZyAjTH7uliMnrOU3HNFBr6fEDBCJCIdlVNq9hHbaDR621XBA==", "path": "system.xml.readerwriter/4.3.0", "hashPath": "system.xml.readerwriter.4.3.0.nupkg.sha512"}, "System.Xml.XDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5zJ0XDxAIg8iy+t4aMnQAu0MqVbqyvfoUVl1yDV61xdo3Vth45oA2FoY4pPkxYAH5f8ixpmTqXeEIya95x0aCQ==", "path": "system.xml.xdocument/4.3.0", "hashPath": "system.xml.xdocument.4.3.0.nupkg.sha512"}, "System.Xml.XmlDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lJ8AxvkX7GQxpC6GFCeBj8ThYVyQczx2+f/cWHJU8tjS7YfI6Cv6bon70jVEgs2CiFbmmM8b9j1oZVx0dSI2Ww==", "path": "system.xml.xmldocument/4.3.0", "hashPath": "system.xml.xmldocument.4.3.0.nupkg.sha512"}, "System.Xml.XmlSerializer/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-MYoTCP7EZ98RrANESW05J5ZwskKDoN0AuZ06ZflnowE50LTpbR5yRg3tHckTVm5j/m47stuGgCrCHWePyHS70Q==", "path": "system.xml.xmlserializer/4.3.0", "hashPath": "system.xml.xmlserializer.4.3.0.nupkg.sha512"}, "System.Xml.XPath/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-v1JQ5SETnQusqmS3RwStF7vwQ3L02imIzl++sewmt23VGygix04pEH+FCj1yWb+z4GDzKiljr1W7Wfvrx0YwgA==", "path": "system.xml.xpath/4.3.0", "hashPath": "system.xml.xpath.4.3.0.nupkg.sha512"}, "System.Xml.XPath.XmlDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-A/uxsWi/Ifzkmd4ArTLISMbfFs6XpRPsXZonrIqyTY70xi8t+mDtvSM5Os0RqyRDobjMBwIDHDL4NOIbkDwf7A==", "path": "system.xml.xpath.xmldocument/4.3.0", "hashPath": "system.xml.xpath.xmldocument.4.3.0.nupkg.sha512"}, "Dqy.Syjx.Cache.Factory/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Dqy.Syjx.Cache.Interface/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Dqy.Syjx.Data/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Dqy.Syjx.Data.EF/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Dqy.Syjx.Data.Repository/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Dqy.Syjx.Entity/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Dqy.Syjx.Enum/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Dqy.Syjx.IdGenerator/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Dqy.Syjx.MemoryCache/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Dqy.Syjx.Model/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Dqy.Syjx.RedisCache/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Dqy.Syjx.Service/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Dqy.Syjx.Util/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Dqy.Syjx.Web.Code/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Zack.EFCore.Batch_NET6/6.1.3": {"type": "project", "serviceable": false, "sha512": ""}, "ZackEFCore.Batch.Dm_NET6/6.6.3": {"type": "project", "serviceable": false, "sha512": ""}}}