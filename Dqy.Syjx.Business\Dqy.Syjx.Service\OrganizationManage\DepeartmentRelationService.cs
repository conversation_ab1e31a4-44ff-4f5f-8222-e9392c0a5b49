﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.OrganizationManage;
using Dqy.Syjx.Model.Param.OrganizationManage;

namespace Dqy.Syjx.Service.OrganizationManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-09-25 15:34
    /// 描 述：服务类
    /// </summary>
    public class DepeartmentRelationService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<DepeartmentRelationEntity>> GetList(DepeartmentRelationListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<DepeartmentRelationEntity>> GetPageList(DepeartmentRelationListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<DepeartmentRelationEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<DepeartmentRelationEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(DepeartmentRelationEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }
        public async Task SaveTransForm(DepeartmentRelationEntity entity, Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }
        public async Task DeleteForm(string ids)
        {
            long[] idArr = TextHelper.SplitToArray<long>(ids, ',');
            await this.BaseRepository().Delete<DepeartmentRelationEntity>(idArr);
        }
        #endregion

        #region 私有方法
        private Expression<Func<DepeartmentRelationEntity, bool>> ListFilter(DepeartmentRelationListParam param)
        {
            var expression = LinqExtensions.True<DepeartmentRelationEntity>();
            if (param != null)
            {
                expression = expression.And(m=>m.ExtensionType ==param.ExtensionType);
                if (param.UnitId > 0)
                {
                    expression = expression.And(m => m.UnitId == param.UnitId);
                }
                if (param.SysDepartmentId > 0)
                {
                    expression = expression.And(m => m.SysDepartmentId == param.SysDepartmentId);
                }
                if (param.ExtensionObjId > 0)
                {
                    expression = expression.And(m => m.ExtensionObjId == param.ExtensionObjId);
                }
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    expression = expression.And(m => param.Ids.Contains(m.ExtensionObjId.Value.ToString()));
                }
            }
            return expression;
        }
        #endregion
    }
}
