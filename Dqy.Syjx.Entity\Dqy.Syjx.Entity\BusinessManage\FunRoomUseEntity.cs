﻿using System;
using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations.Schema;
using Dqy.Syjx.Util;
using System.Collections.Generic;
using Dqy.Syjx.Util.Extension;
using NPOI.SS.UserModel;
using System.ComponentModel;

namespace Dqy.Syjx.Entity.BusinessManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-11-09 16:55
    /// 描 述：已登记列表实体类
    /// </summary>
    [Table("bn_FunRoomUse")]
    public class FunRoomUseEntity : BaseExtensionEntity
    {
        /// <summary>
        /// 所在单位id
        /// </summary>
        /// <returns></returns>
        [JsonConverter(typeof(StringJsonConverter))]
        public long UnitId { get; set; }
        /// <summary>
        /// 功能室Id
        /// </summary>
        /// <returns></returns>
        [JsonConverter(typeof(StringJsonConverter))]
        public long FunRoomId { get; set; }
        /// <summary>
        /// 适用学段（字典表sys_static_dictionary  typecode=1002字段：DictionaryId）
        /// </summary>
        /// <returns></returns>
        public int SchoolStage { get; set; }
        /// <summary>
        /// 任课学科（字典表sys_static_dictionary typecode=1005 字段：DictionaryId）
        /// </summary>
        /// <returns></returns>
        public int DictionaryId1005 { get; set; }
        /// <summary>
        /// 任课班级
        /// </summary>
        /// <returns></returns>
        [JsonConverter(typeof(StringJsonConverter))]
        public long UseClass { get; set; }

        /// <summary>
        /// 年级
        /// </summary>
        /// <returns></returns>
        public int GradeId { get; set; }
        /// <summary>
        /// 班级Id
        /// </summary>
        /// <returns></returns>
        public int ClassId { get; set; }
        /// <summary>
        /// 使用日期
        /// </summary>
        /// <returns></returns>
        [Description("使用日期")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 15)]
        [JsonConverter(typeof(DateTimeJsonConverter))]
        public DateTime? UseDate { get; set; }
        /// <summary>
        /// 任课节次
        /// </summary>
        /// <returns></returns>
        [JsonConverter(typeof(StringJsonConverter))]
        public long CourseSectionId { get; set; }
        /// <summary>
        /// 上课内容
        /// </summary>
        /// <returns></returns>
        public string ClassContent { get; set; }
        /// <summary>
        /// 状态(1：正常；2：不正常）
        /// </summary>
        /// <returns></returns>
        public int Statuz { get; set; }
        /// <summary>
        /// 识别班级学生数
        /// </summary>
        public int? DiscernStudentNum { get; set; }
        /// <summary>
        /// 当前班级学生数
        /// </summary>
        public int? StudentNum { get; set; }
        /// <summary>
        /// 登记类型 1：学科教学登记  2：其他登记
        /// </summary>
        public int RecordType { get; set; } = 1;
        /// <summary>
        /// 问题描述
        /// </summary>
        [NotMapped]
        public string RepairContent { get; set; }
        /// <summary>
        /// 登记人
        /// </summary>
        /// <returns></returns>
        [JsonConverter(typeof(StringJsonConverter))]
        public long RegisterUserId { get; set; }
        [NotMapped]
        [Description("登记人")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 15)]
        public string RealName { get; set; }
        [NotMapped]
        [Description("使用班级")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 30)]
        public string UseClassName { get; set; }
        [NotMapped]
        [Description("一级分类")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 15)]
        public string ClassNameA { get; set; }
        /// <summary>
        /// 一级分类
        /// </summary>
        [NotMapped]
        public int DictionaryId1006A { get; set; }
        [NotMapped]
        [Description("二级分类")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 20)]
        public string ClassNameB { get; set; }
        [NotMapped]
        [Description("属性")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 15)]
        public string NatureName { get; set; }
        [NotMapped]
        public int NatureType { get; set; }
        [NotMapped]
        [Description("适用学科")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 15)]
        public string SubjectName { get; set; }
        [NotMapped]
        [Description("实验室名称")]
        [ExportExcelAttribute(HorizontalAlignment.Left, 30)]
        public string Name { get; set; }
        [NotMapped]
        public List<AttachmentEntity> AttachmentList { get; set; }

        [NotMapped]
        [Description("单位名称")]
        [ExportExcelAttribute(HorizontalAlignment.Left, 30)]
        public string SchoolName { get; set; }
        [NotMapped]
        [Description("使用次数")]
        [ExportExcelAttribute(HorizontalAlignment.Right, 15)]
        public int UseNum { get; set; }
        [NotMapped]
        public int SchoolYearStart { get; set; }
        /// <summary>
        /// 学期显示名称
        /// </summary>
        [NotMapped]
        [Description("学期")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 15)]
        public string SchoolTermName { get; set; }
        [NotMapped]
        public int SchoolTerm { get; set; }
        /// <summary>
        /// 学科节次名称
        /// </summary>
        [NotMapped]
        [Description("课程节次")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 25)]
        public string SectionName { get; set; }
        /// <summary>
        /// 登录人Id
        /// </summary>
        [NotMapped]
        [JsonConverter(typeof(StringJsonConverter))]
        public long LoginUserId { get; set; }

        /// <summary>
        /// 上课节次开始时间
        /// </summary>
        [NotMapped]
        public string BeginTime { get; set; }
        /// <summary>
        /// 上课节次结束时间
        /// </summary>
        [NotMapped]
        public string EndTime { get; set; }
        /// <summary>
        /// 上课节次编号
        /// </summary>
        [NotMapped]
        public string CourseSectionNo { get; set; }
        /// <summary>
        /// 是否过期
        /// </summary>
        [NotMapped]
        public int IsExpiry { get; set; }

        /// <summary>
        /// 区县名称
        /// </summary>
        [NotMapped]
        public string CountyName { get; set; }
        /// <summary>
        /// 学科节次序号
        /// </summary>
        public int? UseCourseSectionIndex { get; set; }
        /// <summary>
        /// 学科节次开始时间
        /// </summary>
        public string UseCourseSectionBgnTime { get; set; }
        /// <summary>
        /// 学科节次结束时间
        /// </summary>
        public string UseCourseSectionEndTime { get; set; }
        /// <summary>
        /// 序号
        /// </summary>
        [NotMapped]
        [Description("序号")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 7)]
        public string ROWNUM { get; set; }

        /// <summary>
        /// 年级
        /// </summary>
        [NotMapped]
        public string GradeName { get; set; }
    }
}
