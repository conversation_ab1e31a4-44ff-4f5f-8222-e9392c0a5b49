﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <RestoreSuccess Condition=" '$(RestoreSuccess)' == '' ">True</RestoreSuccess>
    <RestoreTool Condition=" '$(RestoreTool)' == '' ">NuGet</RestoreTool>
    <ProjectAssetsFile Condition=" '$(ProjectAssetsFile)' == '' ">$(MSBuildThisFileDirectory)project.assets.json</ProjectAssetsFile>
    <NuGetPackageRoot Condition=" '$(NuGetPackageRoot)' == '' ">$(UserProfile)\.nuget\packages\</NuGetPackageRoot>
    <NuGetPackageFolders Condition=" '$(NuGetPackageFolders)' == '' ">C:\Users\<USER>\.nuget\packages\;c:\Program Files (x86)\Microsoft Visual Studio\Shared\NuGetPackages</NuGetPackageFolders>
    <NuGetProjectStyle Condition=" '$(NuGetProjectStyle)' == '' ">PackageReference</NuGetProjectStyle>
    <NuGetToolVersion Condition=" '$(NuGetToolVersion)' == '' ">6.14.0</NuGetToolVersion>
  </PropertyGroup>
  <ItemGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <SourceRoot Include="C:\Users\<USER>\.nuget\packages\" />
    <SourceRoot Include="c:\Program Files (x86)\Microsoft Visual Studio\Shared\NuGetPackages\" />
  </ItemGroup>
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)microsoft.entityframeworkcore\6.0.19\buildTransitive\net6.0\Microsoft.EntityFrameworkCore.props" Condition="Exists('$(NuGetPackageRoot)microsoft.entityframeworkcore\6.0.19\buildTransitive\net6.0\Microsoft.EntityFrameworkCore.props')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.apidescription.server\6.0.5\build\Microsoft.Extensions.ApiDescription.Server.props" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.apidescription.server\6.0.5\build\Microsoft.Extensions.ApiDescription.Server.props')" />
    <Import Project="$(NuGetPackageRoot)swashbuckle.aspnetcore\6.5.0\build\Swashbuckle.AspNetCore.props" Condition="Exists('$(NuGetPackageRoot)swashbuckle.aspnetcore\6.5.0\build\Swashbuckle.AspNetCore.props')" />
    <Import Project="$(NuGetPackageRoot)refit\8.0.0\buildTransitive\netstandard2.0\refit.props" Condition="Exists('$(NuGetPackageRoot)refit\8.0.0\buildTransitive\netstandard2.0\refit.props')" />
    <Import Project="$(NuGetPackageRoot)ikvm.msbuild.tools.runtime.win-x64\8.7.1\buildTransitive\IKVM.MSBuild.Tools.runtime.win-x64.props" Condition="Exists('$(NuGetPackageRoot)ikvm.msbuild.tools.runtime.win-x64\8.7.1\buildTransitive\IKVM.MSBuild.Tools.runtime.win-x64.props')" />
    <Import Project="$(NuGetPackageRoot)ikvm.msbuild.tools.runtime.win-arm64\8.7.1\buildTransitive\IKVM.MSBuild.Tools.runtime.win-arm64.props" Condition="Exists('$(NuGetPackageRoot)ikvm.msbuild.tools.runtime.win-arm64\8.7.1\buildTransitive\IKVM.MSBuild.Tools.runtime.win-arm64.props')" />
    <Import Project="$(NuGetPackageRoot)ikvm.msbuild.tools.runtime.osx-x64\8.7.1\buildTransitive\IKVM.MSBuild.Tools.runtime.osx-x64.props" Condition="Exists('$(NuGetPackageRoot)ikvm.msbuild.tools.runtime.osx-x64\8.7.1\buildTransitive\IKVM.MSBuild.Tools.runtime.osx-x64.props')" />
    <Import Project="$(NuGetPackageRoot)ikvm.msbuild.tools.runtime.osx-arm64\8.7.1\buildTransitive\IKVM.MSBuild.Tools.runtime.osx-arm64.props" Condition="Exists('$(NuGetPackageRoot)ikvm.msbuild.tools.runtime.osx-arm64\8.7.1\buildTransitive\IKVM.MSBuild.Tools.runtime.osx-arm64.props')" />
    <Import Project="$(NuGetPackageRoot)ikvm.msbuild.tools.runtime.linux-x64\8.7.1\buildTransitive\IKVM.MSBuild.Tools.runtime.linux-x64.props" Condition="Exists('$(NuGetPackageRoot)ikvm.msbuild.tools.runtime.linux-x64\8.7.1\buildTransitive\IKVM.MSBuild.Tools.runtime.linux-x64.props')" />
    <Import Project="$(NuGetPackageRoot)ikvm.msbuild.tools.runtime.linux-arm64\8.7.1\buildTransitive\IKVM.MSBuild.Tools.runtime.linux-arm64.props" Condition="Exists('$(NuGetPackageRoot)ikvm.msbuild.tools.runtime.linux-arm64\8.7.1\buildTransitive\IKVM.MSBuild.Tools.runtime.linux-arm64.props')" />
    <Import Project="$(NuGetPackageRoot)ikvm.msbuild.tools.runtime.linux-arm\8.7.1\buildTransitive\IKVM.MSBuild.Tools.runtime.linux-arm.props" Condition="Exists('$(NuGetPackageRoot)ikvm.msbuild.tools.runtime.linux-arm\8.7.1\buildTransitive\IKVM.MSBuild.Tools.runtime.linux-arm.props')" />
    <Import Project="$(NuGetPackageRoot)ikvm.msbuild\8.7.1\buildTransitive\IKVM.MSBuild.props" Condition="Exists('$(NuGetPackageRoot)ikvm.msbuild\8.7.1\buildTransitive\IKVM.MSBuild.props')" />
    <Import Project="$(NuGetPackageRoot)ikvm.image.runtime.win-x86\8.7.1\buildTransitive\IKVM.Image.runtime.win-x86.props" Condition="Exists('$(NuGetPackageRoot)ikvm.image.runtime.win-x86\8.7.1\buildTransitive\IKVM.Image.runtime.win-x86.props')" />
    <Import Project="$(NuGetPackageRoot)ikvm.image.runtime.win-x64\8.7.1\buildTransitive\IKVM.Image.runtime.win-x64.props" Condition="Exists('$(NuGetPackageRoot)ikvm.image.runtime.win-x64\8.7.1\buildTransitive\IKVM.Image.runtime.win-x64.props')" />
    <Import Project="$(NuGetPackageRoot)ikvm.image.runtime.win-arm64\8.7.1\buildTransitive\IKVM.Image.runtime.win-arm64.props" Condition="Exists('$(NuGetPackageRoot)ikvm.image.runtime.win-arm64\8.7.1\buildTransitive\IKVM.Image.runtime.win-arm64.props')" />
    <Import Project="$(NuGetPackageRoot)ikvm.image.runtime.osx-x64\8.7.1\buildTransitive\IKVM.Image.runtime.osx-x64.props" Condition="Exists('$(NuGetPackageRoot)ikvm.image.runtime.osx-x64\8.7.1\buildTransitive\IKVM.Image.runtime.osx-x64.props')" />
    <Import Project="$(NuGetPackageRoot)ikvm.image.runtime.osx-arm64\8.7.1\buildTransitive\IKVM.Image.runtime.osx-arm64.props" Condition="Exists('$(NuGetPackageRoot)ikvm.image.runtime.osx-arm64\8.7.1\buildTransitive\IKVM.Image.runtime.osx-arm64.props')" />
    <Import Project="$(NuGetPackageRoot)ikvm.image.runtime.linux-x64\8.7.1\buildTransitive\IKVM.Image.runtime.linux-x64.props" Condition="Exists('$(NuGetPackageRoot)ikvm.image.runtime.linux-x64\8.7.1\buildTransitive\IKVM.Image.runtime.linux-x64.props')" />
    <Import Project="$(NuGetPackageRoot)ikvm.image.runtime.linux-musl-x64\8.7.1\buildTransitive\IKVM.Image.runtime.linux-musl-x64.props" Condition="Exists('$(NuGetPackageRoot)ikvm.image.runtime.linux-musl-x64\8.7.1\buildTransitive\IKVM.Image.runtime.linux-musl-x64.props')" />
    <Import Project="$(NuGetPackageRoot)ikvm.image.runtime.linux-musl-arm64\8.7.1\buildTransitive\IKVM.Image.runtime.linux-musl-arm64.props" Condition="Exists('$(NuGetPackageRoot)ikvm.image.runtime.linux-musl-arm64\8.7.1\buildTransitive\IKVM.Image.runtime.linux-musl-arm64.props')" />
    <Import Project="$(NuGetPackageRoot)ikvm.image.runtime.linux-musl-arm\8.7.1\buildTransitive\IKVM.Image.runtime.linux-musl-arm.props" Condition="Exists('$(NuGetPackageRoot)ikvm.image.runtime.linux-musl-arm\8.7.1\buildTransitive\IKVM.Image.runtime.linux-musl-arm.props')" />
    <Import Project="$(NuGetPackageRoot)ikvm.image.runtime.linux-arm64\8.7.1\buildTransitive\IKVM.Image.runtime.linux-arm64.props" Condition="Exists('$(NuGetPackageRoot)ikvm.image.runtime.linux-arm64\8.7.1\buildTransitive\IKVM.Image.runtime.linux-arm64.props')" />
    <Import Project="$(NuGetPackageRoot)ikvm.image.runtime.linux-arm\8.7.1\buildTransitive\IKVM.Image.runtime.linux-arm.props" Condition="Exists('$(NuGetPackageRoot)ikvm.image.runtime.linux-arm\8.7.1\buildTransitive\IKVM.Image.runtime.linux-arm.props')" />
    <Import Project="$(NuGetPackageRoot)ikvm.image\8.7.1\buildTransitive\IKVM.Image.props" Condition="Exists('$(NuGetPackageRoot)ikvm.image\8.7.1\buildTransitive\IKVM.Image.props')" />
    <Import Project="$(NuGetPackageRoot)ikvm\8.7.1\buildTransitive\IKVM.props" Condition="Exists('$(NuGetPackageRoot)ikvm\8.7.1\buildTransitive\IKVM.props')" />
  </ImportGroup>
  <PropertyGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <PkgMicrosoft_Extensions_ApiDescription_Server Condition=" '$(PkgMicrosoft_Extensions_ApiDescription_Server)' == '' ">C:\Users\<USER>\.nuget\packages\microsoft.extensions.apidescription.server\6.0.5</PkgMicrosoft_Extensions_ApiDescription_Server>
  </PropertyGroup>
</Project>