﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <div id="UnitId" col="UnitId" type="text" style="display: inline-block;width:200px;"></div>
                    </li>
                    <li>
                        <div id="FunRoomId" col="FunRoomId" type="text" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <input id="Name" col="Name" type="text" placeholder="摄像机所在功能室和地点" style="display: inline-block;width:300px;">
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                        <a class="btn btn-default btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    $(function () {
        $("#UnitId").ysComboBox({
            url: '@Url.Content("~/OrganizationManage/Unit/GetListJson?PageSize=10000")',
            key: "Id",
            value: "Name",
            defaultName: '单位名称',
        });
        loadReation();
        initGrid();
        
        
    });

    function initGrid() {
        var queryUrl = '@Url.Content("~/CameraManage/SchoolCamera/GetRelationPageListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            sortName: 'Sort ASC ,FunRoomId ASC,Id asc ',
            sortOrder: 'ASC',
            columns: [
                { checkbox: true, visible: true },
                { field: 'Id', title: 'Id', visible: false },
                { field: 'CountyName', title: '区县名称', sortable: true, width: 200, halign: 'center', valign: 'middle' },
                { field: 'SchoolName', title: '学校名称', sortable: true, width: 260, halign: 'center', valign: 'middle' },
                { field: 'SrcName', title: '摄像机名称', sortable: true, width: 260, halign: 'center', valign: 'middle' },
                { field: 'Address', title: '摄像机所在功能室和地点', sortable: true, width: 260, halign: 'center', valign: 'middle' },
                { field: 'SrcIndex', title: '摄像机编码', sortable: true, width: 200, halign: 'center', valign: 'middle' },
                { field: 'FunRoomName', title: '实验（专用）室名称', sortable: true, width: 260, halign: 'center', valign: 'middle' },
                {
                    field: 'HouseName', title: '实验（专用）室地址', sortable: true, width: 260, halign: 'center', valign: 'middle',
                    formatter: function (value, row, index) {
                        var html = '--';
                        if (row.FunRoomId > 0 && row.HouseName != undefined && row.HouseName.length > 0 ) {
                            html = row.HouseName;
                            if (row.RoomName != undefined && row.RoomName.length > 0) {
                                html = (row.HouseName + "(" + row.RoomName + ")");
                            }
                        }
                        return html;
                    }
                },
                { field: 'Remark', title: '备注', sortable: true, width: 260, halign: 'center', valign: 'middle' },
                {
                    field: 'FunRoomId', title: '状态', sortable: true, width: 100, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        if (row.FunRoomId > 0) {
                            return '<span class="badge badge-primary">已关联</span>';
                        } else {
                            return '<span class="badge badge-warning">待关联</span>';
                        }
                    }
                },
                {
                    field: 'opt', title: '操作', width: 100, halign: 'center', align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        if (row.FunRoomId == 0) {
                            actions.push('&nbsp;<a class="btn btn-primary btn-xs" href="#" onclick="showRelationForm(\'' + row.Id + '\');"><i class="fa fa-check"></i>关联</a>&nbsp;');
                        } else {
                            actions.push('&nbsp;<a class="btn btn-danger btn-xs" href="#" onclick="showCancelRelationForm(\'' + row.Id + '\');"><i class="fa fa-fa-remove"></i>取消关联</a>&nbsp;');
                        }
                        return actions.join('');
                    }
                }
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }
    function loadReation() {
        var arr = [];
        arr.push({ id: 1, name: "已关联" });
        arr.push({ id: 2, name: "待关联" });
        $("#FunRoomId").ysComboBox({
            data: arr,
            key: "id",
            value: "name",
            defaultName: '关联状态',
        });
    }
    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function resetGrid() {
        $("#UnitId").ysComboBox('setValue', -1);
        $("#FunRoomId").ysComboBox('setValue', -1);
        $('#Name').val("");
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function showRelationForm(id) {
        ys.openDialog({
            title: '为摄像头编号所在功能室和地点&nbsp;&nbsp;绑定功能室',
            content: '@Url.Content("~/CameraManage/SchoolCamera/Relation")' + '?id=' + id,
            width: '768px',
            height: '360px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }
    function showCancelRelationForm(id) {
        ys.confirm("确定要取消该摄像头关联吗？", function () {
            ys.ajax({
                url: '@Url.Content("~/CameraManage/SchoolCamera/CancelRelationJson")' + '?ids=' + id,
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }
</script>
