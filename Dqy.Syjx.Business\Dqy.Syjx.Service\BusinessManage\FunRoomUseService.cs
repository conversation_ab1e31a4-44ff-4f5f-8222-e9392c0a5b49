﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.BusinessManage;
using Dqy.Syjx.Model.Param.BusinessManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Service.OrganizationManage;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Tools;
using System.Data;

namespace Dqy.Syjx.Service.BusinessManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-11-09 16:55
    /// 描 述：已登记列表服务类
    /// </summary>
    public class FunRoomUseService : RepositoryFactory
    {
        private UserSubjectSetService userSubjectSetService = new UserSubjectSetService();

        #region 获取数据
        public async Task<List<FunRoomUseEntity>> GetList(FunRoomUseListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<FunRoomUseEntity>> GetPageList(FunRoomUseListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ListFilter(param, strSql);
            IEnumerable<FunRoomUseEntity> list = null;
            if (pagination!=null)
            {
                list = await this.BaseRepository().FindList<FunRoomUseEntity>(strSql.ToString(), filter.ToArray(), pagination);
            }
            else
            {
                list = await this.BaseRepository().FindList<FunRoomUseEntity>(strSql.ToString(), filter.ToArray());
            }
            if (list != null)
            {
                list.ForEach(m =>
                {
                    m.SchoolTermName = GetSchoolTermName(m.SchoolYearStart, m.SchoolYearStart + 1, m.SchoolTerm);
                    m.IsExpiry = GetIsExpiry(param, m.UseDate);
                    m.UseClassName = m.GradeName == null ? "" : string.Format("{0}（{1}）", m.GradeName, m.UseClassName);
                 });
            }
            return list.ToList();
        }

        private int GetIsExpiry(FunRoomUseListParam param, DateTime? usedate)
        {
            int isexpiry = 1;
            if (param .CurrentSchoolTermStartYear> 0)
            {
                if (param.CurrentSchoolTermStartDate <= usedate && usedate <=param.CurrentSchoolTermEndDate)
                {
                    isexpiry = 2;
                }
            }
            return isexpiry;
        }
        public async Task<List<FunRoomUseEntity>> GetSummaryPageList(FunRoomUseListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ListSummaryFilter(param, strSql);
            var list = await this.BaseRepository().FindList<FunRoomUseEntity>(strSql.ToString(), filter.ToArray(), pagination);
            if (list!=null)
            {
                list.ForEach(m => m.SchoolTermName = GetSchoolTermName(m.SchoolYearStart, m.SchoolYearStart + 1, m.SchoolTerm));
            }
            return list.ToList();
        }
        public async Task<FunRoomUseEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<FunRoomUseEntity>(id);
        }

        public async Task<List<FunRoomUseEntity>> GetList2(FunRoomUseListParam param)
        {
            var expression = ListFilter2(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(FunRoomUseEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(FunRoomUseEntity entity, Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update bn_FunRoomUse set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
                strSql = $"update bn_FunRoomUse set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        /// <summary>
        /// 获取学期字符名称
        /// </summary>
        /// <param name="schoolYearStart">开始学年</param>
        /// <param name="schoolYearEnd">结束学年</param>
        /// <param name="schoolTerm">学期</param>
        /// <returns></returns>
        private string GetSchoolTermName(int schoolYearStart, int schoolYearEnd, int schoolTerm)
        {
            string name = "--";
            if (schoolYearStart > 1000 && schoolYearEnd > 1000)
            {
                name = string.Format("{0}~{1}", schoolYearStart % 100, schoolYearEnd % 100);
                if (schoolTerm == 1)
                {
                    name += "上学期";
                }
                else
                {
                    name += "下学期";
                }
            }
            return name;
        }

        private Expression<Func<FunRoomUseEntity, bool>> ListFilter(FunRoomUseListParam param)
        {
            var expression = LinqExtensions.True<FunRoomUseEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (param.OptType == 1)
                {
                    /**注：会存在多个班级一起上课的情况吗？（不考虑）
                     * 1：同一班级、时间、节次，不能登记多个学科。
                    * 2：同一班级、时间、节次，同一个学科，不能登记多次(多个室或同一室)。
                    * 3:一个节次，只上一个学科。所以只需验证班级、时间、节次，是否已存在就可以，如果一个节次上多个课程，需改。
                    */
                    expression = expression.And(t => t.UnitId == param.UnitId);
                    if (param.Id > 0)
                    {
                        expression = expression.And(t => t.Id != param.Id);
                    }
                    if (param.UseClass > 0)
                    {
                        expression = expression.And(t => t.UseClass == param.UseClass);
                    }
                    if (param.UseDate !=null)
                    {
                        expression = expression.And(t => t.UseDate == param.UseDate);
                    }
                    if (param.CourseSectionId > 0)
                    {
                        expression = expression.And(t => t.CourseSectionId == param.CourseSectionId);
                    }
                   // expression = expression.And(t => t.DictionaryId1005 != param.DictionaryId1005 || (t.DictionaryId1005 == param.DictionaryId1005 && t.FunRoomId != param.FunRoomId ));
                }
                else if (param.OptType == 2)
                {
                    //同一个房间，同一时间、同一节次，已登记其他信息
                    if (param.Id > 0)
                    {
                        expression = expression.And(t => t.Id != param.Id);
                    }
                    expression = expression.And(t => t.UnitId == param.UnitId && t.FunRoomId == param.FunRoomId && t.UseDate == param.UseDate && t.CourseSectionId == param.CourseSectionId);
                }
                else
                {
                    if (!string.IsNullOrEmpty(param.Ids))
                    {
                        long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                        expression = expression.And(t => idArr.Contains(t.Id.Value));
                    }
                }
            }
            return expression;
        }


        private List<DbParameter> ListFilter(FunRoomUseListParam param, StringBuilder strSql)
        {
            strSql.Append($@"  SELECT * From (
                   SELECT fru7.Id ,
                  		 fru7.BaseIsDelete ,
                  		 fru7.BaseCreateTime ,
                  		 fru7.BaseModifyTime ,
                  		 fru7.BaseCreatorId ,
                  		 fru7.BaseModifierId ,
                  		 fru7.BaseVersion ,
                  		 fru7.UnitId ,
                  		 fru7.FunRoomId ,
                  		 fru7.SchoolStage ,
                  		 fru7.DictionaryId1005 ,
                  		 fru7.UseClass ,
                  		 fru7.UseDate ,
                  		 fru7.CourseSectionId ,
                  		 fru7.ClassContent ,
                  		 fru7.Statuz ,
                  		 fru7.RegisterUserId ,
                         fru7.UseCourseSectionIndex ,
                         fru7.UseCourseSectionBgnTime ,
                         fru7.UseCourseSectionEndTime ,
                          fr1.DictionaryId1006A ,
                          fr1.DictionaryId1006B ,
                          fr1.Name ,
                          fr1.RoomAttribute ,
                          fr1.SeatNum ,
                          fr1.IsDigitalize ,
                          fr1.SysDepartmentId ,
                          fr1.SysUserId ,
                          fr1.BuildTime ,
                          fr1.ReformTime ,
                          fr1.Address ,
                          fr1.SafeguardUserId ,
                          fr1.UploadBriefInfoNum ,
                          fr1.UploadSystemNum ,
                          fr1.LaboratoryGroupNum ,
                          sd1.DicName AS ClassNameA ,
                          sd2.DicName AS ClassNameB ,
                          sd6.DicName AS NatureName ,
                          sd6.Nature AS NatureType ,
                          sd3.DicName AS SubjectName,
                          sd12.DicName AS GradeName ,
                          sgc8.ClassDesc AS UseClassName ,
                          su5.RealName ,
                          u1.Name AS SchoolName ,
                          ur.UnitId AS CountyId  ,
                          sa.AreaName AS CountyName ,
						  ur2.UnitId AS CityId ,
						  u3.Name AS CityName ,
                          st1.SchoolTerm  ,
                          st1.SchoolYearStart ,
                          {param.UserId} AS LoginUserId
                          ,fru7.UseCourseSectionBgnTime AS BeginTime
						  ,fru7.UseCourseSectionEndTime AS EndTime
						  ,fru7.UseCourseSectionIndex AS CourseSectionNo
                          FROM  bn_FunRoomUse AS fru7
                          INNER JOIN  bn_FunRoom AS fr1 ON fr1.Id = fru7.FunRoomId
                          INNER JOIN  sys_static_dictionary AS sd1 ON fr1.DictionaryId1006A = sd1.DictionaryId
		                  INNER JOIN  sys_static_dictionary AS sd2 ON fr1.DictionaryId1006B = sd2.DictionaryId
                          INNER JOIN  sys_static_dictionary AS sd6 ON sd2.Nature = sd6.DictionaryId
                          LEFT JOIN  sys_static_dictionary AS sd3 ON fru7.DictionaryId1005 = sd3.DictionaryId
                          INNER JOIN  SysUser AS su5 ON fru7.RegisterUserId = su5.Id
                          LEFT JOIN  up_SchoolGradeClass AS sgc8 ON fru7.UseClass = sgc8.Id
				          LEFT JOIN  sys_static_dictionary  AS sd12 ON sgc8.GradeId = sd12.DictionaryId
                          INNER JOIN  up_Unit AS u1 ON fru7.UnitId = u1.Id AND u1.BaseIsDelete = 0
                          LEFT JOIN  bn_SchoolTerm AS st1 ON st1.BaseIsDelete = 0 AND st1.TermStart <= fru7.UseDate AND fru7.UseDate  <= st1.TermEnd
                          INNER JOIN  up_UnitRelation AS ur ON ur.BaseIsDelete = 0 AND u1.Id = ur.ExtensionObjId AND ur.ExtensionType = 3
                          INNER JOIN  up_Unit AS u2 ON UR.UnitId = u2.Id AND u2.BaseIsDelete = 0
					      INNER JOIN  up_UnitRelation AS ur2 ON u2.Id = ur2.ExtensionObjId AND ur2.ExtensionType = 3 AND ur2.BaseIsDelete = 0
                          INNER JOIN  up_Unit AS u3 ON ur2.UnitId = u3.Id AND u3.BaseIsDelete = 0
                          LEFT JOIN  SysArea AS sa ON sa.AreaCode = u2.AreaId AND sa.BaseIsDelete = 0
                          where fru7.BaseIsDelete = 0
            ) as tb1 WHERE   1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = @BaseIsDelete ");
                parameter.Add(DbParameterExtension.CreateDbParameter("@BaseIsDelete", param.BaseIsDelete));
                if (param.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    strSql.Append(" AND UnitId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.SchoolId));
                }
                else if (param.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    strSql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.UnitId));
                    if (param.SchoolId > 0)
                    {
                        strSql.Append(" AND UnitId = @UnitId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.SchoolId));
                    }
                }
                else if (param.UnitType == UnitTypeEnum.City.ParseToInt() || param.UnitType == UnitTypeEnum.System.ParseToInt())
                {
                    strSql.Append(" AND CityId = @CityId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CityId", param.CityId));
                    if (param.CountyId > 0)
                    {
                        strSql.Append(" AND CountyId = @CountyId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                    }
                    if (param.SchoolId > 0)
                    {
                        strSql.Append(" AND UnitId = @UnitId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.SchoolId));
                    }
                }
                else
                {
                    strSql.Append(" AND 1 <> 1 ");
                }
                if (param.SchoolStage > 0)
                {
                    strSql.Append(" AND SchoolStage = @SchoolStage ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStage", param.SchoolStage));
                }
                if (param.DictionaryId1006A > 0)
                {
                    strSql.Append(" AND DictionaryId1006A = @DictionaryId1006A ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId1006A", param.DictionaryId1006A));
                }
                if (param.DictionaryId1006B > 0)
                {
                    strSql.Append(" AND DictionaryId1006B = @DictionaryId1006B ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId1006B", param.DictionaryId1006B));
                }
                if (param.DictionaryId1005 > 0)
                {
                    strSql.Append(" AND DictionaryId1005 = @DictionaryId1005 ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId1005", param.DictionaryId1005));
                }
                if (param.RoomAttribute > 0)
                {
                    strSql.Append(" AND RoomAttribute = @RoomAttribute ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@RoomAttribute", param.RoomAttribute));
                }
                if (param.UseClass > 0)
                {
                    strSql.Append(" AND UseClass = @UseClass ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UseClass", param.UseClass));
                }
                if (param.SchoolYearStart > 0)
                {
                    strSql.Append(" AND SchoolYearStart = @SchoolYearStart ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolYearStart", param.SchoolYearStart));
                }
                if (param.SchoolTerm > 0)
                {
                    strSql.Append(" AND SchoolTerm = @SchoolTerm ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolTerm", param.SchoolTerm));
                }
                if (param.Statuz > 0)
                {
                    strSql.Append(" AND Statuz = @Statuz ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Statuz", param.Statuz));
                }
                if (param.NatureType > 0)
                {
                    strSql.Append(" AND NatureType = @NatureType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@NatureType", param.NatureType));
                }
                if (!string.IsNullOrEmpty(param.Name))
                {
                    strSql.Append(" AND ( Name like @Name OR  RealName like @Name ) ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Name", $"%{param.Name}%"));
                }
                if (param.Id > 0)
                {
                    strSql.Append(" AND Id = @Id ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Id", param.Id));
                }
                if (param.OptType==3)
                {
                    strSql.Append(" AND RegisterUserId = @UserId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UserId", param.UserId));
                }
                else if (param.OptType == 4)
                {
                    strSql.Append(" AND (SafeguardUserId = @SafeguardUserId OR  RegisterUserId = @UserId)");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SafeguardUserId", param.SafeguardUserId));
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UserId", param.UserId));
                }
                else
                {
                    if (param.RegisterUserId > 0)
                    {
                        strSql.Append(" AND RegisterUserId = @RegisterUserId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@RegisterUserId", param.RegisterUserId));
                    }
                }

                if (param.SetUserId > 0)
                {
                    //判断用户是否设置了学段、学科
                    var listStage = userSubjectSetService.GetUserSetStageList(param.UserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listStage.Count > 0)
                    {
                        string where = "";
                        int stageLength = listStage.Count;
                        int index = 1;
                        foreach (var stage in listStage)
                        {
                            where += $" SchoolStage LIKE '{stage.DictionaryId}' ";
                            if (index != stageLength)
                            {
                                where += " OR ";
                            }
                            index++;
                        }
                        strSql.Append($" AND ({where})");
                    }
                    var listCourse = userSubjectSetService.GetUserSetCourseList(param.UserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listCourse.Count > 0)
                    {
                        string courseId = string.Join(",", listCourse.Select(f => f.DictionaryId));
                        strSql.Append($" AND DictionaryId1005 IN ({courseId})");
                    }
                }
            }
            return parameter;
        }

        private List<DbParameter> ListSummaryFilter(FunRoomUseListParam param, StringBuilder strSql)
        {
            strSql.Append(@"  SELECT * From (
                   SELECT
                    tb2.Id ,
                    tb2.Name,
                    tb2.UnitId ,
                    tb2.DictionaryId1005 ,
                    tb2.DictionaryId1006A ,
                    tb2.DictionaryId1006B ,
                    tb2.SchoolTerm ,
                    tb2.SchoolYearStart ,
                    tb2.UseNum ,
                    SchoolStagez ,
                    u1.Name AS SchoolName ,
                    sd1.DicName AS ClassNameA ,
                    sd2.DicName AS ClassNameB ,
                    sd6.DicName AS NatureName ,
                    sd6.Nature AS NatureType ,
                    sd2.Nature AS RoomAttribute,
                    sd3.DicName AS SubjectName ,
                    ur.UnitId AS CountyId
                    ,sa.AreaName AS CountyName
					,ur2.UnitId AS CityId
				    ,u3.Name AS CityName
                    FROM (
                    SELECT MAX(tb1.Id) AS Id,tb1.UnitId,tb1.SchoolStagez, tb1.DictionaryId1005 ,tb1.DictionaryId1006A ,tb1.DictionaryId1006B ,tb1.SchoolTerm ,tb1.SchoolYearStart ,COUNT(Id) AS UseNum , tb1.Name ,tb1.FunRoomId
                    FROM (
                    SELECT fru2.Id  AS Id ,fr1.SchoolStagez, fru2.BaseIsDelete , fru2.UnitId , fru2.DictionaryId1005 , fr1.DictionaryId1006A , fr1.DictionaryId1006B , fr1.Name  , fru2.FunRoomId ,
                    st1.SchoolTerm  ,
                    st1.SchoolYearStart
                    FROM  bn_FunRoom AS fr1
                    INNER JOIN  bn_FunRoomUse AS fru2 ON fr1.Id = fru2.FunRoomId
                    LEFT JOIN  bn_SchoolTerm AS st1 ON st1.BaseIsDelete = 0 AND st1.TermStart <= fru2.UseDate AND fru2.UseDate  <= st1.TermEnd
                    WHERE fr1.Statuz = 1 AND fru2.BaseIsDelete = 0
                    ) AS tb1
                    GROUP BY tb1.UnitId , tb1.DictionaryId1005 ,tb1.SchoolStagez,tb1.DictionaryId1006A ,tb1.DictionaryId1006B ,tb1.SchoolTerm ,tb1.SchoolYearStart ,tb1.FunRoomId, tb1.Name
                    ) tb2
                    INNER JOIN  up_Unit AS u1 ON tb2.UnitId = u1.Id AND u1.Statuz = 1 AND u1.BaseIsDelete = 0
                    INNER JOIN  sys_static_dictionary AS sd1 ON tb2.DictionaryId1006A = sd1.DictionaryId AND sd1.BaseIsDelete = 0
                    INNER JOIN  sys_static_dictionary AS sd2 ON tb2.DictionaryId1006B = sd2.DictionaryId AND sd2.BaseIsDelete = 0
                    INNER JOIN  sys_static_dictionary AS sd3 ON tb2.DictionaryId1005 = sd3.DictionaryId AND sd3.BaseIsDelete = 0
                    INNER JOIN  sys_static_dictionary AS sd6 ON sd2.Nature = sd6.DictionaryId AND sd6.BaseIsDelete = 0
                    INNER JOIN  up_UnitRelation AS ur ON ur.BaseIsDelete = 0 AND u1.Id = ur.ExtensionObjId AND ur.ExtensionType = 3
                    INNER JOIN  up_Unit AS u2 ON UR.UnitId = u2.Id AND u2.BaseIsDelete = 0
					INNER JOIN  up_UnitRelation AS ur2 ON u2.Id = ur2.ExtensionObjId AND ur2.ExtensionType = 3 AND ur2.BaseIsDelete = 0
                    INNER JOIN  up_Unit AS u3 ON ur2.UnitId = u3.Id AND u3.BaseIsDelete = 0
                    LEFT JOIN  SysArea AS sa ON sa.AreaCode = u2.AreaId AND sa.BaseIsDelete = 0
            ) as tb1 WHERE   1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    strSql.Append(" AND UnitId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.SchoolId));
                }
                else if (param.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    strSql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.UnitId));
                    if (param.SchoolId > 0)
                    {
                        strSql.Append(" AND UnitId = @UnitId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.SchoolId));
                    }
                }
                else if (param.UnitType == UnitTypeEnum.City.ParseToInt()|| param.UnitType == UnitTypeEnum.System.ParseToInt())
                {
                    strSql.Append(" AND CityId = @CityId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CityId", param.CityId));
                    if(param.CountyId > 0)
                    {
                        strSql.Append(" AND CountyId = @CountyId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                    }
                    if(param.SchoolId > 0)
                    {
                        strSql.Append(" AND UnitId = @UnitId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.SchoolId));
                    }
                }
                else
                {
                    strSql.Append(" AND 1 <> 1 ");
                }
                if (param.SchoolStage > 0)
                {
                    strSql.Append(" AND SchoolStagez like @SchoolStage ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStage", $"%{param.SchoolStage}%"));
                }
                if (param.DictionaryId1006A > 0)
                {
                    strSql.Append(" AND DictionaryId1006A = @DictionaryId1006A ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId1006A", param.DictionaryId1006A));
                }
                if (param.DictionaryId1006B > 0)
                {
                    strSql.Append(" AND DictionaryId1006B = @DictionaryId1006B ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId1006B", param.DictionaryId1006B));
                }
                if (param.DictionaryId1005 > 0)
                {
                    strSql.Append(" AND DictionaryId1005 = @DictionaryId1005 ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId1005", param.DictionaryId1005));
                }
                if (param.RoomAttribute > 0)
                {
                    strSql.Append(" AND RoomAttribute = @RoomAttribute ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@RoomAttribute", param.RoomAttribute));
                }
                if (param.UseClass > 0)
                {
                    strSql.Append(" AND UseClass = @UseClass ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UseClass", param.UseClass));
                }
                if (param.SchoolYearStart > 0)
                {
                    strSql.Append(" AND SchoolYearStart = @SchoolYearStart ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolYearStart", param.SchoolYearStart));
                }
                if (param.SchoolTerm  > 0)
                {
                    strSql.Append(" AND SchoolTerm = @SchoolTerm ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolTerm", param.SchoolTerm));
                }
                if (!string.IsNullOrEmpty(param.Name))
                {
                    strSql.Append(" AND Name like @Name ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Name", $"%{param.Name}%"));
                }

                if (param.SetUserId > 0)
                {

                    //判断用户是否设置了学段、学科
                    var listStage = userSubjectSetService.GetUserSetStageList(param.UserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listStage.Count > 0)
                    {
                        string where = "";
                        int stageLength = listStage.Count;
                        int index = 1;
                        foreach (var stage in listStage)
                        {
                            where += $" SchoolStagez LIKE '{stage.DictionaryId}' ";
                            if (index != stageLength)
                            {
                                where += " OR ";
                            }
                            index++;
                        }
                        strSql.Append($" AND ({where})");
                    }
                    var listCourse = userSubjectSetService.GetUserSetCourseList(param.UserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listCourse.Count > 0)
                    {
                        string courseId = string.Join(",", listCourse.Select(f => f.DictionaryId));
                        strSql.Append($" AND DictionaryId1005 IN ({courseId})");
                    }

                }
            }
            return parameter;
        }


        private Expression<Func<FunRoomUseEntity, bool>> ListFilter2(FunRoomUseListParam param)
        {
            var expression = LinqExtensions.True<FunRoomUseEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!param.UnitId.IsNullOrZero())
                {
                    expression = expression.And(t => t.UnitId == param.UnitId);
                }
                if (!param.FunRoomId.IsNullOrZero())
                {
                    expression = expression.And(t => t.FunRoomId == param.FunRoomId);
                }
                if (!param.DictionaryId1005.IsNullOrZero())
                {
                    expression = expression.And(t => t.DictionaryId1005 == param.DictionaryId1005);
                }
                if (!param.UseClass.IsNullOrZero())
                {
                    expression = expression.And(t => t.UseClass == param.UseClass);
                }
                if (param.UseDate.HasValue)
                {
                    expression = expression.And(t => t.UseDate == param.UseDate);
                }
                if (!param.CourseSectionId.IsNullOrZero())
                {
                    expression = expression.And(t => t.CourseSectionId == param.CourseSectionId);
                }
            }
            return expression;
        }

        /// <summary>
        /// 实验（专用）室统计数量
        /// </summary>
        /// <param name="param"></param>
        /// <param name="strSql"></param>
        /// <param name="funcSql"></param>
        /// <returns></returns>
        public async Task<IEnumerable<FunRoomUseEntity>> GetStatistics(FunRoomUseListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(@$" SELECT * From (
                    SELECT fu1.*
                    ,f2.DictionaryId1006A
                    ,sd6.Nature
                    ,se11.SchoolProp
                    ,ur2.UnitId AS CountyId
                    ,ur1.UnitId AS CityId
                    ,st1.SchoolTerm
                    ,st1.SchoolYearStart  
                    FROM bn_FunRoomUse AS fu1
                    INNER JOIN bn_FunRoom AS f2 ON f2.BaseIsDelete = 0 AND fu1.FunRoomId = f2.Id
                    INNER JOIN sys_static_dictionary as dic3 ON dic3.BaseIsDelete = 0 AND f2.DictionaryId1005 = dic3.DictionaryId
                    INNER JOIN  sys_static_dictionary AS sd2 ON f2.DictionaryId1006B = sd2.DictionaryId AND sd2.BaseIsDelete = 0
                    INNER JOIN  sys_static_dictionary AS sd6 ON sd2.Nature = sd6.DictionaryId AND sd6.BaseIsDelete = 0
                    INNER JOIN up_SchoolExtension AS se11 ON se11.BaseIsDelete = 0 AND se11.UnitId = f2.UnitId
                    LEFT JOIN  bn_SchoolTerm AS st1 ON st1.BaseIsDelete = 0 AND st1.TermStart <= fu1.UseDate AND fu1.UseDate  <= st1.TermEnd
                    LEFT JOIN  up_UnitRelation AS ur2 ON f2.UnitId = ur2.ExtensionObjId AND ur2.ExtensionType = 3 AND ur2.BaseIsDelete = 0
                    LEFT JOIN  up_UnitRelation AS ur1 ON ur1.ExtensionObjId = ur2.UnitId AND ur2.ExtensionType = 3 AND ur2.BaseIsDelete = 0
                ) as tb1 WHERE BaseIsDelete = 0 ");

            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    strSql.Append(" AND UnitId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                else if (param.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    strSql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
                else if (param.UnitType == UnitTypeEnum.City.ParseToInt())
                {
                    strSql.Append(" AND CityId = @CityId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CityId", param.CityId));
                }
                if (param.SchoolId > 0)
                {
                    strSql.Append(" AND UnitId = @SchoolId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
                }
                if (param.SchoolYearStart > 0)
                {
                    strSql.Append(" AND SchoolYearStart = @SchoolYearStart ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolYearStart", param.SchoolYearStart));
                }
                if (param.SchoolTerm > 0)
                {
                    strSql.Append(" AND SchoolTerm = @SchoolTerm ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolTerm", param.SchoolTerm));
                }
                if (param.NatureType > -1)
                {
                    strSql.Append(" AND Nature = @Nature ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Nature", param.NatureType));
                }
                if (param.SchoolStage > 0)
                {
                    strSql.Append(" AND SchoolStage = @SchoolStage ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStage", param.SchoolStage));
                }
                if (param.SchoolProple != -10000)
                {
                    strSql.Append(" AND SchoolProp <> @SchoolProple ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolProple", param.SchoolProple));
                }
            }
            return await this.BaseRepository().FindList<FunRoomUseEntity>(strSql.ToString(), parameter.ToArray(),pagination);
        }
        #endregion
    }
}
