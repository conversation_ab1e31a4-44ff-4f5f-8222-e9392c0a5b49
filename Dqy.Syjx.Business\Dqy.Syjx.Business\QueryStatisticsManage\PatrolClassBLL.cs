﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Model.Input.QueryStatisticsManage;
using Dqy.Syjx.Entity.QueryStatisticsManage;
using Dqy.Syjx.Model.Param.QueryStatisticsManage;
using Dqy.Syjx.Service.QueryStatisticsManage;
using Dqy.Syjx.Web.Code;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Entity.BusinessManage;
using Dqy.Syjx.Model.Param.BusinessManage;
using System.IO;
using System.Net;
using Dqy.Syjx.Service.BusinessManage;
using Microsoft.AspNetCore.Server.IISIntegration;
using NPOI.SS.Formula.Functions;
using Dqy.Syjx.Entity.SystemManage;
using Dqy.Syjx.Model.Param.SystemManage;
using Dqy.Syjx.Service.SystemManage;

namespace Dqy.Syjx.Business.QueryStatisticsManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2022-05-19 14:08
    /// 描 述：业务类
    /// </summary>
    public class PatrolClassBLL
    {
        /// <summary>
        /// ApiToken值（仅WebApi端传入）
        /// </summary>
        public string ApiToken = string.Empty;

        private PatrolClassService patrolClassService = new PatrolClassService();
        private AttachmentService attachmentService = new AttachmentService();
        private ConfigSetService configSetService = new ConfigSetService ();

        #region 获取数据
        public async Task<TData<List<PatrolClassEntity>>> GetList(PatrolClassListParam param)
        {
            TData<List<PatrolClassEntity>> obj = new TData<List<PatrolClassEntity>>();
            obj.Data = await patrolClassService.GetList(param);
            obj.Total = obj.Data.Count;
            obj.Tag = 1;
            return obj;
        }

        public async Task<TData<PatrolClassEntity>> GetEntity(long id)
        {
            TData<PatrolClassEntity> obj = new TData<PatrolClassEntity>();
            obj.Data = await patrolClassService.GetEntity(id);
            if (obj.Data != null)
            {
                obj.Tag = 1;
            }
            return obj;
        }

        /// <summary>
        /// 获取远程在线巡查数据
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<TData<List<FunRoomEntity>>> GetRoomVideo(FunRoomListParam param, Pagination pagination)
        {
            TData<List<FunRoomEntity>> obj = new TData<List<FunRoomEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            param.UnitType = operatorinfo.UnitType;
            param.UserId = operatorinfo.UserId.Value;
            param.SetUserId = operatorinfo.UserId.Value;
            if (param.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                param.UnitId = operatorinfo.UnitId.Value;
            }
            if (param.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                param.CountyId = operatorinfo.UnitId.Value;
            }
            if(param.UnitType == UnitTypeEnum.City.ParseToInt())
            {
                param.CityId = operatorinfo.UnitId.Value;
            }

            var list = await patrolClassService.GetRoomVideo(param, pagination);
            obj.Data = list;
            obj.Total = pagination.TotalCount;
            obj.Tag = 1;
            return obj;
        }

        /// <summary>
        /// 正在上课获取json数据
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<TData<List<FunRoomEntity>>> GetFunroomVideo(FunRoomListParam param, Pagination pagination)
        {
            TData<List<FunRoomEntity>> obj = new TData<List<FunRoomEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            param.UnitType = operatorinfo.UnitType;
            param.UserId = operatorinfo.UserId.Value;
            param.SetUserId = operatorinfo.UserId.Value;
            if (param.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                param.UnitId = operatorinfo.UnitId.Value;
            }
            if (param.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                param.CountyId = operatorinfo.UnitId.Value;
            }
            if (param.UnitType == UnitTypeEnum.City.ParseToInt())
            {
                param.CityId = operatorinfo.UnitId.Value;
            }

            var list = await patrolClassService.GetFunroomVideo(param, pagination);
            obj.Data = list;
            obj.Total = pagination.TotalCount;
            obj.Tag = 1;
            return obj;
        }


        /// <summary>
        /// 获取在线巡课查询统计数据
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<TData<List<PatrolClassEntity>>> GetStatistics(PatrolClassListParam param, Pagination pagination)
        {
            TData<List<PatrolClassEntity>> obj = new TData<List<PatrolClassEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            param.UnitType = operatorinfo.UnitType;
            param.SetUserId = operatorinfo.UserId.Value;
            if (param.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                param.UnitId = operatorinfo.UnitId.Value;
            }
            if (param.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                param.CountyId = operatorinfo.UnitId.Value;
            }
            if (param.UnitType == UnitTypeEnum.City.ParseToInt())
            {
                param.CityId = operatorinfo.UnitId.Value;
            }

            var list = await patrolClassService.GetStatistics(param, pagination);
            obj.Data = list;
            obj.Total = pagination.TotalCount;
            obj.Tag = 1;
            return obj;
        }

        /// <summary>
        /// 获取在线巡课列表的基础数据
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<TData<List<PatrolClassEntity>>> GetDetailPageList(PatrolClassListParam param, Pagination pagination)
        {
            TData<List<PatrolClassEntity>> obj = new TData<List<PatrolClassEntity>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            param.UnitType = operatorinfo.UnitType;
            param.UserId = operatorinfo.UserId.Value;
            if (param.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                param.UnitId = operatorinfo.UnitId.Value;
            }
            if (param.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                param.CountyId = operatorinfo.UnitId.Value;
            }
            if (param.UnitType == UnitTypeEnum.City.ParseToInt())
            {
                param.CityId = operatorinfo.UnitId.Value;
            }
          
            var list = await patrolClassService.GetDetailPageList(param, pagination);
            foreach (var item in list)
            {
                item.PicPath = await attachmentService.GetPageList(new AttachmentListParam() { ObjectId = item.Id, FileCategory = 5003 }, new Pagination() { PageIndex = 1, PageSize = 1 }); //查询摄像头抓拍的图片
            }
            obj.Data = list;
            obj.Total = pagination.TotalCount;
            obj.Tag = 1;
            return obj;
        }

        #endregion

        #region 提交数据
        public async Task<TData<string>> SaveForm(PatrolClassInputModel model)
        {
            var entity = new PatrolClassEntity();
            if (model.Id > 0)
            {
                entity = await patrolClassService.GetEntity(model.Id);
            }
            entity.Id = model.Id;
            entity.UnitId = model.UnitId;
            entity.FunRoomId = model.FunRoomId;
            entity.DictionaryId1006A = model.DictionaryId1006A;
            entity.DictionaryId1006B = model.DictionaryId1006B;
            entity.SchoolTermName = model.SchoolTermName;
            entity.RoomAttribute = model.RoomAttribute;
            entity.SchoolStage = model.SchoolStage;
            entity.DictionaryId1005 = model.DictionaryId1005;
            entity.CourseSectionId = model.CourseSectionId;
            entity.BeginTime = model.BeginTime;
            entity.EndTime = model.EndTime;
            entity.UseDate = model.UseDate;
            entity.IsClass = model.IsClass;
            entity.CameraId = model.CameraId;
            entity.SrcName = model.SrcName;
            entity.CameraIndexCode = model.CameraIndexCode;
            entity.Statuz = model.Statuz;
            TData<string> obj = new TData<string>();
            await patrolClassService.SaveForm(entity);
            obj.Data = entity.Id.ParseToString();
            obj.Tag = 1;
            return obj;
        }

        public async Task<TData> DeleteForm(string ids)
        {
            TData obj = new TData();
            if (ids.IsEmpty()){ return obj; }
            PatrolClassListParam param = new PatrolClassListParam { Ids = ids };
            var list = await patrolClassService.GetList(param);
            ids = "";
            foreach (var m in list)
            {
                 //此处需增加校验是否满足删除条件
            
                 ids += ", " + m.Id.Value;
             }
            obj.Tag = 1;
            if (ids.Length > 1)
                await patrolClassService.DeleteForm(ids);
            else
                 obj.Tag = 0;
            return obj;
        }

        /// <summary>
        /// 初始化当天数据
        /// </summary>
        /// <param name="usedate"></param>
        /// <returns></returns>
        public async Task<TData> InitData(string usedate)
        {
            TData obj = new TData();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            if (operatorinfo != null)
            {
                obj = await patrolClassService.InitPatrolClass(DateTime.Now.ToString("yyyy-MM-dd"), operatorinfo.UnitType, operatorinfo.UnitId.Value);
            }
            return obj;
        }

        /// <summary>
        /// 设置数据状态
        /// </summary>
        /// <param name="statuz"></param>
        /// <param name="usedate"></param>
        /// <returns></returns>
        public async Task<TData> SetStatuz(int statuz,string usedate)
        {
            TData obj = new TData();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            if (operatorinfo != null)
            {
                await patrolClassService.SetStatuz(statuz, usedate); //启用数据
                obj.Tag = 1;
            }
            else
            {
                obj.Tag = 0;
            }
            return obj;
        }

        /// <summary>
        /// 同步正常开课次数
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<TData> UpdateData(PatrolClassListParam param, Pagination pagination)
        {
            TData obj = new TData();
            int isValidPicPeople = GlobalContext.SystemConfig.IsValidPicPeople; //采集的摄像头图片人头数是否有效
            //List<ConfigSetEntity> configObj = await configSetService.GetList(new ConfigSetListParam { TypeCode = "5003_SXJCJYXRS", UnitId = 100000000000000001 }); //获取平台配置的摄像机品牌
            //if (configObj.Count > 0)
            //{
            //    if (!string.IsNullOrEmpty(configObj.LastOrDefault().ConfigValue))  //摄像机采集有效人数值
            //    {
            //        isValidPicPeople = Convert.ToInt32(configObj.LastOrDefault().ConfigValue);
            //    }
            //}

            List<PatrolClassEntity> patrolClasslist = new List<PatrolClassEntity>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            param.UnitType = operatorinfo.UnitType;
            param.UserId = operatorinfo.UserId.Value;
            if(param.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                param.UnitId = operatorinfo.UnitId.Value;
            }
            else if(param.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                param.CountyId = operatorinfo.UnitId.Value;
            }
            else if(param.UnitType == UnitTypeEnum.City.ParseToInt())
            {
                param.CityId = operatorinfo.UnitId.Value;
            }
            param.BeginTime = DateTime.Now.ToShortTimeString();
            param.EndTime = DateTime.Now.ToShortTimeString();
            param.Statuz = 1;
            param.UseDate = DateTime.Now;
            try
            {
                patrolClasslist = await patrolClassService.GetDetailPageList(param, pagination); //查询最新的“在线巡课”数据
                //更新是否正常开课状态
                if (patrolClasslist.Count > 0)
                {
                    foreach (PatrolClassEntity item in patrolClasslist)
                    {
                        var piclist = patrolClassService.GetMaxPeople(item.SrcName, item.BeginTime, item.EndTime, item.UseDate.ToString("yyyy-MM-dd")); //查询Haikang采集图片表获取每个课程节次内拍摄到最大人数图片
                        if (piclist.Result.Count() > 0)
                        {
                            foreach (var pic in piclist.Result)
                            {
                                if (pic.PeopleNum >= isValidPicPeople) //判断采集到的图片最大人头数是否有效
                                {
                                    await patrolClassService.Update(IsClassEnum.Yes.ParseToInt(), item.Id.Value);
                                }
                            }

                            //下载该图片并保存数据至附件表
                            //if (string.IsNullOrEmpty(pic.PicPath)) //拍摄到最大人数图片本地不存在，先保存图片至本地
                            //{
                            //    string picPath = await SavePic(pic.ImageUrl); //保存图片至本地
                            //    await attachmentService.SaveForm(new AttachmentEntity()
                            //    {
                            //        Title = item.SrcName,
                            //        ObjectId = item.Id.Value,
                            //        FileCategory = 0001,
                            //        Path = picPath,
                            //        Ext = ".jpg",
                            //        IsDefault = 0,
                            //        IsShow = 1,
                            //        IsDelete = 0
                            //    });
                            //}
                        }
                    }
                }

                #region 作废
                ////更新是否当前课程节次状态
                //var currentpatrolClasslist = await patrolClassService.GetCurrentSectionList(param, pagination, "DESC"); //查询当前时间所属于的课程节次列表
                //if (currentpatrolClasslist.Count == 0)  //未查询到当前时间所属课程节次时：默认取当天第一节课
                //{
                //    param.BeginTime = "";
                //    param.EndTime = "";
                //    currentpatrolClasslist = await patrolClassService.GetCurrentSectionList(param, pagination, "ASC");
                //}
                //if (currentpatrolClasslist.Count > 0)
                //{
                //    string ids = "";
                //    foreach (var item in currentpatrolClasslist)
                //    {
                //        ids += ", " + item.Id;
                //    }

                //    if (ids.Length > 1)
                //    {
                //        await patrolClassService.SetIsCurrenSection(1, ids, 1); //修改当前课程节次数据的IsCurrenSection 字段值 为 1
                //        await patrolClassService.SetIsCurrenSection(0, ids, 0); //修改非当前课程节次的IsCurrenSection 字段值 为 0
                //        obj.Tag = 1;
                //    }
                //    else
                //    {
                //        obj.Tag = 0;
                //    }
                //}
                #endregion
            }
            catch (Exception ex)
            {
                obj.Tag = 0;
                obj.Message = "操作异常" + ex.Message;
                throw new Exception("程序出现异常，异常信息为：" + ex.Message);
            }

            return obj;
        }
        #endregion

        #region 私有方法
        /// <summary>
        /// 保存海康接口图片至本地
        /// </summary>
        /// <param name="imgUrl"></param>
        /// <returns>保存成功返回图片虚拟目录</returns>
        private async Task<string> SavePic(string imgUrl)
        {
            LogHelper.Info($"下载图片的地址 {imgUrl}");
            string picVirtualPath = "";//图片虚拟路径
            if (!string.IsNullOrEmpty(imgUrl))
            {
                string dbImgUrl = imgUrl.Substring(0, imgUrl.IndexOf("/pic") + 1); //截取HaiKangEventNotify表中ImageUrl字段的http地址
                string newImgUrl = imgUrl.Replace(dbImgUrl, GlobalContext.SystemConfig.HaiKangReplaceHttpUrl); //替换后的新地址
                string strSaveName = DateTime.Now.ToString("yyyyMMddHHmmss") + Guid.NewGuid().ToString("n").Substring(0, 6) + ".jpg"; //生成图片名称

                string picSavePath = GlobalContext.SystemConfig.HaiKangSavePath + DateTime.Now.Year + "\\" + DateTime.Now.Month + "\\"; //图片保存物理路径

                if (await PageCommon.DownloadPicture(newImgUrl, picSavePath, strSaveName, 6000) != "error") //图片下载成功
                {
                    picVirtualPath = GlobalContext.SystemConfig.HaiKangVirtualDirectory + DateTime.Now.Year + "/" + DateTime.Now.Month + "/" + strSaveName;
                }
            }
            return picVirtualPath;
        }

      

        #endregion
    }
}
