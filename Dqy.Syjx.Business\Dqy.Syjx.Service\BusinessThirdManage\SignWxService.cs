﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.BusinessThirdManage;
using Dqy.Syjx.Model.Param.BusinessThirdManage;

namespace Dqy.Syjx.Service.BusinessThirdManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2025-05-22 10:26
    /// 描 述：无锡市签名验签服务类
    /// </summary>
    public class SignWxService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<SignWxEntity>> GetList(SignWxListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<SignWxEntity>> GetPageList(SignWxListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<SignWxEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<SignWxEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(SignWxEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(SignWxEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");

            // 使用跨数据库兼容的 IN 语法替代 f_split
            string strSql = $"UPDATE Third_SignWx SET BaseIsDelete = 1 WHERE Id IN ({ids})";
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<SignWxEntity, bool>> ListFilter(SignWxListParam param)
        {
            var expression = LinqExtensions.True<SignWxEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
            }
            return expression;
        }
        #endregion
    }
}
