﻿using Dqy.Syjx.Business.EvaluateManage;
using Dqy.Syjx.Business.ExperimentTeachManage;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.ExperimentTeachManage;
using Dqy.Syjx.Entity.OrganizationManage;
using Dqy.Syjx.Entity.PersonManage;
using Dqy.Syjx.Entity.SystemManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Enum.SystemManage;
using Dqy.Syjx.Model.Input;
using Dqy.Syjx.Model.Input.InstrumentManage;
using Dqy.Syjx.Model.Input.OrganizationManage;
using Dqy.Syjx.Model.Input.PersonManage;
using Dqy.Syjx.Model.Param;
using Dqy.Syjx.Model.Param.OrganizationManage;
using Dqy.Syjx.Model.Param.PersonManage;
using Dqy.Syjx.Model.Param.SystemManage;
using Dqy.Syjx.Service.ExperimentTeachManage;
using Dqy.Syjx.Service.OrganizationManage;
using Dqy.Syjx.Service.PersonManage;
using Dqy.Syjx.Service.SystemManage;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Web.Code;
using Microsoft.AspNetCore.Routing.Matching;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Dqy.Syjx.Business.OrganizationManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-09-17 10:41
    /// 描 述：业务类
    /// </summary>
    public class SchoolGradeClassBLL
    {
        /// <summary>
        /// ApiToken值（仅WebApi端传入）
        /// </summary>
        public string ApiToken = string.Empty;

        private SchoolGradeClassService schoolGradeClassService = new SchoolGradeClassService();
        private SchoolGradeClassHistoryService schoolGradeClassHistoryService = new SchoolGradeClassHistoryService();
        private StaticDictionaryService staticDictionaryService = new StaticDictionaryService();
        private SchoolExtensionService schoolExtensionService = new SchoolExtensionService();
        private UserTeachClassService userTeachClassService = new UserTeachClassService();
        private UserClassInfoService userClassInfoService = new UserClassInfoService();
        private UnitService unitService = new();
        private UserService userService = new UserService();
        private InstrumentAttendStaticBLL attendStaticBLL = new InstrumentAttendStaticBLL();
        private ExperimentAttendStaticBLL experimentAttendStaticBll= new ExperimentAttendStaticBLL();
        private PlanInfoBLL planInfoBll = new PlanInfoBLL();
        private SchoolTermBLL schoolTermBll = new SchoolTermBLL();
        #region 获取数据
        public async Task<TData<List<SchoolGradeClassEntity>>> GetList(SchoolGradeClassListParam param)
        {
            TData<List<SchoolGradeClassEntity>> obj = new TData<List<SchoolGradeClassEntity>>();
            OperatorInfo user = await Operator.Instance.Current(ApiToken);
            if (user.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                param.UnitId = user.UnitId;
            }
            param.UnitType = user.UnitType;
            obj.Data = await schoolGradeClassService.GetPageList(param, new Pagination() { PageIndex = 1, PageSize = int.MaxValue, Sort = "GradeId ASC ,ClassId ASC", SortType = "ASC" });
            obj.Total = obj.Data.Count;
            obj.Tag = 1;
            return obj;
        }

        public async Task<TData<List<SchoolGradeClassEntity>>> GetPageList(SchoolGradeClassListParam param, Pagination pagination)
        {
            TData<List<SchoolGradeClassEntity>> obj = new TData<List<SchoolGradeClassEntity>>();
            OperatorInfo operatorInfo = await Operator.Instance.Current(ApiToken);
            param.UnitId = operatorInfo.UnitId;
            param.UnitType = operatorInfo.UnitType;
            if (operatorInfo.IsSystem == 1)
            {
                param.UnitType = operatorInfo.UnitType;
            }
            else
            {
                param.UnitId = operatorInfo.UnitId;
                param.UnitType = operatorInfo.UnitType;
            }
            obj.Data = await schoolGradeClassService.GetPageList(param, pagination);
            obj.Total = pagination.TotalCount;
            if (obj.Total > 0 && param.IsTotal == 1)
            {
                var summaryList = await schoolGradeClassService.GetTotal(param, pagination);
                if (summaryList != null && summaryList.Count > 0)
                {
                    //增加总计行
                    obj.Data.Add(new SchoolGradeClassEntity
                    {
                        ClassDesc = summaryList[0].ClassDesc,
                        StudentNum = summaryList[0].StudentNum
                    });
                }

            }
            obj.Tag = 1;
            return obj;
        }

        public async Task<TData<SchoolGradeClassEntity>> GetEntity(long id)
        {
            TData<SchoolGradeClassEntity> obj = new TData<SchoolGradeClassEntity>();
            obj.Data = await schoolGradeClassService.GetEntity(id);
            if (obj.Data != null)
            {
                obj.Tag = 1;
            }
            return obj;
        }

        /// <summary>
        /// 获取当前学校某个学段的学生总数
        /// </summary>
        /// <param name="stageId"></param>
        /// <returns></returns>
        public async Task<TData<int>> GetStudentNumByStageId(int stageId)
        {
            TData<int> obj = new TData<int>();
            OperatorInfo operatorInfo = await Operator.Instance.Current(ApiToken);
            if (operatorInfo == null)
            {
                obj.Tag = -1;
                obj.Message = "登录信息失效！";
                return obj;
            }
            var list = await schoolGradeClassService.GetList(new SchoolGradeClassListParam
            {
                UnitId = operatorInfo.UnitId,
                SchoolStage = stageId.ParseToString(),
                IsGraduate = IsEnum.No.ParseToInt()
            });
            obj.Tag = 1;
            obj.Message = "查询成功";
            obj.Data = list.Sum(p => p.StudentNum).Value;
            return obj;
        }

        /// <summary>
        /// 获取当前学校某个学段的学生总数
        /// </summary>
        /// <param name="stageId"></param>
        /// <returns></returns>
        public async Task<TData<int>> GetStatisticsClassNum(int SchoolStageId)
        {
            TData<int> obj = new TData<int>();
            OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
            if (operatorinfo == null)
            {
                obj.Tag = -1;
                obj.Message = "登录信息失效！";
                return obj;
            }
            SchoolGradeClassListParam param = new SchoolGradeClassListParam();
            param.UnitType = operatorinfo.UnitType;
            param.SchoolStage = SchoolStageId.ToString();
            if (operatorinfo.UnitType == UnitTypeEnum.City.ParseToInt())
            {
                param.CityId = operatorinfo.UnitId.Value;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
            {
                param.CountyId = operatorinfo.UnitId.Value;
            }
            else if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                param.UnitId = operatorinfo.UnitId.Value;
            }
            var num = await schoolGradeClassService.GetStatisticsClassNum(param);
            if (num >= 0)
            {
                obj.Data = num;
                obj.Tag = 1;
                obj.Message = "查询成功";
            }
            return obj;
        }
        #endregion

        #region 获取班级任课信息(教师任课设置)

        /// <summary>
        /// 获取班级任课老师信息
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<TData<List<object>>> GetCourseTeachList(SchoolGradeClassListParam param, Pagination pagination)
        {
            TData<List<object>> obj = new TData<List<object>>();
            OperatorInfo operatorInfo = await Operator.Instance.Current(ApiToken);
            param.UnitId = operatorInfo.UnitId;
            param.UnitType = operatorInfo.UnitType;
            if (operatorInfo.IsSystem == 1)
            {
                param.UnitType = operatorInfo.UnitType;
            }
            else
            {
                param.UnitId = operatorInfo.UnitId;
                param.UnitType = operatorInfo.UnitType;
            }
            if (!string.IsNullOrEmpty(param.UserName))
            {
                pagination.PageSize = 10000;
            }
            param.Statuz = StatusEnum.Yes.ParseToInt();
            var gradeClassList = await schoolGradeClassService.GetPageList(param, pagination);
            //获取学科班级的任课信息。    
            var courselist = await userTeachClassService.GetCourseClassList(new UserClassInfoListParam() { UnitId = operatorInfo.UnitId.Value, Nature = 1 });
            var josdData = new List<object>();
            if (gradeClassList != null && gradeClassList.Count > 0)
            {
                //获取年级和学科的关系
                var courseGradelist = await staticDictionaryService.GetAllList(new StaticDictionaryListParam() { TypeCode = DicTypeCodeEnum.Course.ParseToInt().ToString(), PTypeCode = DicTypeCodeEnum.Grade.ParseToInt().ToString(), Nature = 1 });

                foreach (var classinfo in gradeClassList)
                {
                    //验证当前年级是否有权限
                    int isPhysics = 2;
                    int isChemistry = 2;
                    int isBiology = 2;
                    int isScience = 2;
                    if (courseGradelist!=null && courseGradelist.Count > 0)
                    {
                        var templist = courseGradelist.Where(m=>m.Pid==classinfo.GradeId && m.DictionaryId == StaticDictionaryEnum.Physics.ParseToInt());
                        if (templist!=null && templist.Count()>0)
                        {
                            isPhysics = 1;
                        }
                        templist = courseGradelist.Where(m => m.Pid == classinfo.GradeId && m.DictionaryId == StaticDictionaryEnum.Chemistry.ParseToInt());
                        if (templist != null && templist.Count() > 0)
                        {
                            isChemistry = 1;
                        }
                        templist = courseGradelist.Where(m => m.Pid == classinfo.GradeId && m.DictionaryId == StaticDictionaryEnum.Biology.ParseToInt());
                        if (templist != null && templist.Count() > 0)
                        {
                            isBiology = 1;
                        }
                        templist = courseGradelist.Where(m => m.Pid == classinfo.GradeId && m.DictionaryId == StaticDictionaryEnum.Science.ParseToInt());
                        if (templist != null && templist.Count() > 0)
                        {
                            isScience = 1;
                        }
                    }
                    var Physics = GetCourseTeacherId(courselist, StaticDictionaryEnum.Physics.ParseToInt(), classinfo.Id.Value);//物理老师
                    var Chemistry = GetCourseTeacherId(courselist, StaticDictionaryEnum.Chemistry.ParseToInt(), classinfo.Id.Value);//化学老师
                    var Biology = GetCourseTeacherId(courselist, StaticDictionaryEnum.Biology.ParseToInt(), classinfo.Id.Value);  //生物 老师
                    var Science = GetCourseTeacherId(courselist, StaticDictionaryEnum.Science.ParseToInt(), classinfo.Id.Value);  //科学老师
                    if (!string.IsNullOrEmpty(param.UserName))
                    {
                        int isValid = 0;
                        if (Physics != null)
                        {
                            if (Physics.UserName.Contains(param.UserName))
                            {
                                isValid = 1;
                            }
                        }
                        if (Chemistry != null)
                        {
                            if (Chemistry.UserName.Contains(param.UserName))
                            {
                                isValid = 1;
                            }
                        }
                        if (Biology != null)
                        {
                            if (Biology.UserName.Contains(param.UserName))
                            {
                                isValid = 1;
                            }
                        }
                        if (Science != null)
                        {
                            if (Science.UserName.Contains(param.UserName))
                            {
                                isValid = 1;
                            }
                        }
                        if (isValid == 0)
                        {
                            continue;
                        }
                    }

                    var classObj = new
                    {
                        Id = classinfo.Id.ToString(),
                        GradeName = classinfo.GradeName,
                        ClassDesc = classinfo.ClassDesc,
                        Physics = GetConvertUser(Physics),   //物理老师Id
                        isPhysics= isPhysics,
                        Chemistry = GetConvertUser(Chemistry),//化学老师,//化学老师Id
                        isChemistry = isChemistry,
                        Biology = GetConvertUser(Biology),  //生物 老师Id
                        isBiology= isBiology,
                        Science = GetConvertUser(Science),  //科学  老师Id
                        isScience= isScience
                    };
                    josdData.Add(classObj);
                }
            }
            obj.Data = josdData;
            obj.Total = pagination.TotalCount;
            obj.Tag = 1;
            return obj;
        }

        /// <summary>
        /// 根据任课信息，和班级，获取对应的任课老师
        /// </summary>
        /// <param name="list"></param>
        /// <param name="courseid"></param>
        /// <param name="gradeclassid"></param>
        /// <returns></returns>
        private UserClassInfoEntity GetCourseTeacherId(List<UserTeachClassEntity> list, int courseid, long gradeclassid)
        {
            if (list != null && list.Count > 0)
            {
                var teacherList = list.Where(m => m.ClassIdz != null && m.CourseId == courseid && m.ClassIdz.Contains(gradeclassid.ToString()));
                if (teacherList != null && teacherList.Count() > 0)
                {
                    return teacherList.Select(m => new UserClassInfoEntity { UserId = m.UserId ?? 0, UserName = m.UserName  }).FirstOrDefault();
                }
            }
            return null;
        }

        private object GetConvertUser(UserClassInfoEntity model)
        {
            if (model != null)
            {
                return new { UserId = model.UserId.ToString(), UserName = model.UserName };
            }
            return null;
        }
        public async Task<TData<object>> GetTeachList()
        {
            TData<object> obj = new TData<object>();
            OperatorInfo operatorInfo = await Operator.Instance.Current(ApiToken);
            var courselist = await staticDictionaryService.GetChildToList(new StaticDictionaryListParam() { TypeCode = DicTypeCodeEnum.Course.ParseToInt().ToString(), Pids = string.Join(",", operatorInfo.SchoolStageList), Nature = 1 });
            bool Physics = false;
            bool Chemistry = false;
            bool Biology = false;
            bool Science = false;
            if (courselist != null && courselist.Count > 0)
            {
                foreach (var item in courselist)
                {
                    if (item.DictionaryId == StaticDictionaryEnum.Physics.ParseToInt())
                    {
                        Physics = true;
                    }
                    else if (item.DictionaryId == StaticDictionaryEnum.Chemistry.ParseToInt())
                    {
                        Chemistry = true;
                    }
                    else if (item.DictionaryId == StaticDictionaryEnum.Biology.ParseToInt())
                    {
                        Biology = true;
                    }
                    else if (item.DictionaryId == StaticDictionaryEnum.Science.ParseToInt())
                    {
                        Science = true;
                    }
                }
            }
            var list = await userService.GetUserAllList(new UserListParam() { UnitId = operatorInfo.UnitId, UserStatus = 1 ,CreateUnitId = 1, RoleId = 16508640061130150 }, new Pagination() { PageSize = int.MaxValue });
            obj.Data = new
            {

                isphysics = Physics,
                ischemistry = Chemistry,
                isbiology = Biology,
                isscience = Science,
                teachList = list.Select(m => new { Id = m.Id.ToString(), RealName = m.RealName }).OrderBy(m => m.RealName),
            };
            obj.Tag = 1;
            return obj;
        }
        #endregion

        #region 提交数据
        /// <summary>
        /// 
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public async Task<TData<string>> AddForm(SchoolGradeClassInputModel entity)
        {
            TData<string> obj = new TData<string>();
            Repository db = schoolGradeClassService.BaseRepository();
            string errorMsg = "";
            if (entity.SchoolStage == null || entity.SchoolStage <= 0)
            {
                errorMsg += "请选择学段。<br/>";
            }
            if (entity.GradeId == null || entity.GradeId <= 0)
            {
                errorMsg += "请选择年级。<br/>";
            }
            if (entity.ClassNum == null || entity.ClassNum <= 0)
            {
                errorMsg += "请填写班级总数。<br/>";
            }
            if (entity.StudentNum == null || entity.StudentNum <= 0)
            {
                errorMsg += "请填写年级学生总数。<br/>";
            }
            if (errorMsg != "")
            {
                obj.Tag = 0;
                obj.Message = errorMsg;
                return obj;
            }
            try
            {
                OperatorInfo user = await Operator.Instance.Current(ApiToken);
                if (user.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    entity.UnitId = user.UnitId;
                }
                else if (user.UnitType == UnitTypeEnum.System.ParseToInt())
                {
                    //
                    var unitEntity = await unitService.GetEntity(entity.UnitId.Value);
                    if (unitEntity == null || unitEntity.UnitType != UnitTypeEnum.School.ParseToInt())
                    {
                        obj.Tag = 0;
                        obj.Message = "当前单位非学校不可创建班级";
                        return obj;
                    }
                }
                else
                {
                    obj.Tag = 0;
                    obj.Message = "执行失败，无权操作。";
                    return obj;
                }

                /*创建班级
                 * 1：验证，当前年级是否已穿件班级。已创建，获取最大的班级，
                 * 2：根据最大班级数，获取班级集合。
                 * 2：班级学生数=学生总数/班级总数，余数放在最后一个班       
                 */
                if (entity.ClassNum > 0 && entity.StudentNum > 0)
                {
                    //根据年级计算出入学年份。
                    var startYear = DateTime.Now.Year;
                    var changeMonth = DateTime.Now.Month;
                    var changeDay = DateTime.Now.Day;

                    var gradelist = await staticDictionaryService.GetList(new StaticDictionaryListParam() { TypeCode = DicTypeCodeEnum.Grade.ParseToInt().ToString(), DictionaryId = entity.GradeId.Value });
                    if (gradelist == null || gradelist.Count == 0)
                    {
                        obj.Tag = 0;
                        obj.Message = "执行失败，无法获取年级信息。";
                        return obj;
                    }
                    else
                    {
                        if (changeMonth < 8 || (changeMonth == 8 && changeDay < 25))
                        {
                            if (gradelist[0].Nature > 0)
                            {
                                startYear = startYear - gradelist[0].Nature.Value;
                            }
                        }
                        else
                        {
                            if (gradelist[0].Nature > 0)
                            {
                                startYear = startYear - gradelist[0].Nature.Value + 1;
                            }
                        }
                    }
                    StaticDictionaryListParam dicParam = new StaticDictionaryListParam();
                    dicParam.TypeCode = DicTypeCodeEnum.Class.ParseToInt().ToString();// DicTypeCodeEnum.Class;
                                                                                      //判断是否存在已创建班级
                                                                                      //根据配置获取信息

         
                    var currentClassNo = 0;//当前要创建班级，起始编号需在加1
                    var classList = await schoolGradeClassService.GetList(new SchoolGradeClassListParam() { UnitId = entity.UnitId, IsGraduate = 0, SchoolStage = entity.SchoolStage.ToString(), GradeId = entity.GradeId, StartYear = startYear });
                    if (classList != null && classList.Count > 0)
                    {
                        var dictionaryId = (int)classList.Max(m => m.ClassId == null ? 0 : m.ClassId);
                        if (dictionaryId > 0)
                        {
                            dicParam.OptType = 1;
                            dicParam.DictionaryId = dictionaryId;
                        }
                        currentClassNo = classList.Count + 1;
                    }

                    //获取班级集合（未使用的班级） 
                    var list = await staticDictionaryService.GetList(dicParam);
                  
                    if (list != null && list.Count > 0)
                    {
                        list = list.OrderBy(m => m.Sequence).ToList();
                    }
                    await db.BeginTrans();
                    var classStudentNum = entity.StudentNum / entity.ClassNum;
                    var surplusNum = entity.StudentNum % entity.ClassNum;
                    if ((classStudentNum + 1) >= 70)
                    {
                        obj.Tag = 0;
                        obj.Message = "创建失败，单个班级人数必须小于70。";
                        return obj;
                    }
                    for (int i = 0; i < entity.ClassNum; i++)
                    {
                        //最后一个班级，加上剩余数量
                        if (surplusNum > 0)
                        {
                            surplusNum -= 1;
                        }
                        else
                        {
                            surplusNum = -1;
                        }
                        int classId = i + 1;
                        string classdesc = "";
                        if (list != null && classId < list.Count)
                        {
                            //获取字典表ClassId
                            if (list[i].DictionaryId != null && int.TryParse(list[i].DicName, out classId))
                            {
                                classId = list[i].DictionaryId.Value;
                                classdesc = list[i].DicName;
                            }
                            else
                            {
                                classdesc = (currentClassNo + classId).ToString();
                                classId = 1004000 + classId;
                            }
                        }
                        else
                        {
                            classdesc = (currentClassNo + classId).ToString();
                            classId = 1004000 + classId;
                        }

                        await schoolGradeClassService.SaveTransForm(new SchoolGradeClassEntity()
                        {
                            UnitId = entity.UnitId,
                            StartYear = startYear,
                            SchoolStage = entity.SchoolStage,
                            GradeId = entity.GradeId,
                            ClassId = classId,
                            ClassDesc = string.Format("{0}{1}{2}", entity.ClassNamePrefix ?? "", classdesc, entity.ClassNamePostfix ?? ""),
                            StudentNum = surplusNum >= 0 ? (classStudentNum + 1) : classStudentNum
                        }, db);
                    }
                    obj.Tag = 1;
                    await db.CommitTrans();

                    //更新班级数和学生数量
                    await schoolExtensionService.UpdateClassNum(entity.UnitId.Value, schoolExtensionService.BaseRepository());

                    //更新学校轨数
                    UpdateSchoolTrackNum(entity.UnitId.Value);
                }
                else
                {
                    await db.RollbackTrans();
                    obj.Tag = 0;
                    obj.Message = "班级数或者学生数填写错误。";
                }
            }
            catch (Exception ex)
            {
                await db.RollbackTrans();
                obj.Tag = 0;
                obj.Message = "修改信息异常，请联系客服协助处理。";
                throw new Exception("程序出现异常，异常信息为：" + ex.Message);
            }
            return obj;
        }

        /// <summary>
        /// 班级管理 - 修改保存-修改班级人数、班级描述名称
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public async Task<TData<string>> UpdateForm(SchoolGradeClassInputModel model)
        {
            TData<string> obj = new TData<string>();
            Repository db = schoolGradeClassService.BaseRepository();
            try
            {
                if (model.StudentNum >= 70)
                {
                    obj.Message = "修改失败，单个班级学生数必须小于70。";
                    obj.Tag = 0;
                    return obj;
                }
                OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
                var entity = await schoolGradeClassService.GetEntity(model.Id);
                if (entity != null)
                {
                    /*非管理员或者本单位无权操作班级信息*/
                    if (operatorinfo.UnitType != UnitTypeEnum.System.ParseToInt() && entity.UnitId != model.UnitId)
                    {
                        obj.Tag = 0;
                        obj.Message = "非法操作，禁止操作其他单位信息。";
                        return obj;
                    }
                    if (entity.ClassDesc != model.ClassDesc)
                    {
                        var gradeclassList = await schoolGradeClassService.GetList(new SchoolGradeClassListParam() { UnitId = operatorinfo.UnitId, IsGraduate = IsEnum.No.ParseToInt() });
                        if (gradeclassList != null && gradeclassList.Count > 0)
                        {
                            var classtemp = gradeclassList.Where(m => m.GradeId == entity.GradeId && m.ClassDesc == model.ClassDesc);
                            if (classtemp != null && classtemp.Count() > 0)
                            {
                                obj.Tag = 0;
                                obj.Message = "修改失败，当前年级下已存在该班级名称。";
                                return obj;
                            }
                        }
                    }
                    var oldStudentNum = entity.StudentNum;
                    entity.ClassDesc = model.ClassDesc;
                    entity.StudentNum = model.StudentNum;
                    await db.BeginTrans();
                    await schoolGradeClassService.SaveTransForm(entity, db);
                    await db.CommitTrans();
                    obj.Tag = 1;
                    obj.Message = "修改成功。";
                    //更细学生数量
                    if (model.StudentNum != oldStudentNum)
                    {
                        //更新学校累计学生数
                        await schoolExtensionService.UpdateStudentNum(entity.UnitId == null ? 0 : (long)entity.UnitId);
                    }


                }
                else
                {
                    await db.RollbackTrans();
                    obj.Tag = 0;
                    obj.Message = "你修改的班级信息已不存在。";
                    return obj;
                }
            }
            catch (Exception ex)
            {
                await db.RollbackTrans();
                obj.Tag = 0;
                obj.Message = "修改信息异常，请联系客服协助处理。";
                throw new Exception("程序出现异常，异常信息为：" + ex.Message);
            }
            return obj;
        }


        /// <summary>
        /// 更新所选学科（高中）
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public async Task<TData<string>> UpdateSubjectJson(SchoolGradeClassInputModel model)
        {
            TData<string> obj = new TData<string>();
            Repository db = schoolGradeClassService.BaseRepository();
            try
            {
                OperatorInfo operatorinfo = await Operator.Instance.Current(ApiToken);
                var entity = await schoolGradeClassService.GetEntity(model.Id);
                if (entity != null)
                {
                    /*非管理员或者本单位无权操作班级信息*/
                    if (operatorinfo.UnitType != UnitTypeEnum.System.ParseToInt() && entity.UnitId != model.UnitId)
                    {
                        obj.Tag = 0;
                        obj.Message = "非法操作，禁止操作其他单位信息。";
                        return obj;
                    }
                    //只有物理、化学、生物
                    if (model.SubjectList != null && model.SubjectList.Count > 0 && model.SubjectList.Count <= 3)
                    {
                        entity.SelectSubject = string.Join(",", model.SubjectList);
                    }
                    else
                    {
                        entity.SelectSubject = "";
                    }
                    var listClass = await schoolGradeClassService.GetList(new SchoolGradeClassListParam() { UnitId = entity.UnitId, GradeId = entity.GradeId, IsGraduate = 0, Statuz = StatusEnum.Yes.ParseToInt() });
                    await db.BeginTrans();
                    await schoolGradeClassService.SaveTransForm(entity, db);
                    await db.CommitTrans();
                    obj.Tag = 1;
                    obj.Message = "修改成功。";
                    //更新当年当前学期，当前学科的编制计划。对应的班级
                    if (listClass != null)
                    {
                        foreach (var item in listClass)
                        {
                            if (item.Id==entity.Id)
                            {
                                item.SelectSubject = entity.SelectSubject;
                            }
                        }
                    }
                    await planInfoBll.UpdatePlanClassIdz(entity.GradeId ?? 0, model.SubjectId, listClass, entity.UnitId ?? 0);
                }
                else
                {
                    obj.Tag = 0;
                    obj.Message = "你修改的班级信息已不存在。";
                    return obj;
                }
            }
            catch (Exception ex)
            {
                await db.RollbackTrans();
                obj.Tag = 0;
                obj.Message = "修改信息异常，请联系客服协助处理。";
                throw new Exception("程序出现异常，异常信息为：" + ex.Message);
            }
            return obj;
        }
        public async Task<TData> DeleteForm(string ids, long? unitid)
        {
            TData obj = new TData();
            //验证班级是否可以删除，1：验证登记信息是否有班级，2预约信息是否有班级。
            var funroomuseNum = await schoolGradeClassService.VerifyFunRoomUseForm(ids);
            if (funroomuseNum > 0)
            {
                obj.Tag = 0;
                obj.Message = "你要删除的班级信息，在实验(专用)室管理中存在已登记使用，禁止删除。";
                return obj;
            }
            var bookingNum = await schoolGradeClassService.VerifyExperimentBookingForm(ids);
            if (bookingNum > 0)
            {
                obj.Tag = 0;
                obj.Message = "你要删除的班级信息，在实验教学管理的实验预约中已使用，禁止删除。";
                return obj;
            }
            Repository db = schoolGradeClassService.BaseRepository();
            try
            {
                await db.BeginTrans();
                await schoolGradeClassService.DeleteTransForm(ids, db);

                //更新班级数和学生数量
                await schoolExtensionService.UpdateClassNum(unitid == null ? 0 : (long)unitid, db);
                obj.Tag = 1;
                await db.CommitTrans();

                //更新学校轨数
                UpdateSchoolTrackNum(unitid ?? 0);
            }
            catch (Exception ex)
            {
                await db.RollbackTrans();
                obj.Tag = 0;
                obj.Message = "删除信息异常，请联系客服协助处理。";
                throw new Exception("程序出现异常，异常信息为：" + ex.Message);
            }
            return obj;
        }
        public async Task<TData> ImportClass(ImportParam param, List<SchoolGradeClassInputModel> list)
        {
            TData obj = new TData();
            Repository db = schoolGradeClassService.BaseRepository();
            try
            {
                OperatorInfo operatorInfo = await Operator.Instance.Current(ApiToken);
                var gradeclassList = await schoolGradeClassService.GetList(new SchoolGradeClassListParam() { UnitId = operatorInfo.UnitId, IsGraduate = IsEnum.No.ParseToInt() });
                if (list != null && list.Any() && list.Count > 0)
                {
                    /*验证
                     *1：验证单位是否存在 ，不存在不允许添加
                     */
                    List<UnitEntity> unitList = new();
                    List<StaticDictionaryEntity> schoolStageList = new();
                    if (operatorInfo.UnitType == UnitTypeEnum.School.ParseToInt())
                    {
                        var tempentity = await unitService.GetEntity(operatorInfo.UnitId.Value);
                        if (tempentity != null)
                        {
                            unitList.Add(tempentity);
                        }
                        schoolStageList = await staticDictionaryService.GetChildList(new StaticDictionaryListParam() { TypeCode = DicTypeCodeEnum.SchoolStage.ParseToInt().ToString(), Pid = operatorInfo.SchoolProp.Value });
                        if (schoolStageList == null || schoolStageList.Count == 0)
                        {
                            obj.Tag = 0;
                            obj.Message = "导入失败，请联系客服配置学段后再导入。";
                            return obj;
                        }
                    }
                    else if (operatorInfo.UnitType == UnitTypeEnum.System.ParseToInt())
                    {
                        var tempentity = await unitService.GetList(new UnitListParam() { UnitType = UnitTypeEnum.School.ParseToInt() });
                        if (tempentity != null)
                        {
                            unitList.AddRange(tempentity);
                        }
                        schoolStageList = await staticDictionaryService.GetChildList(new StaticDictionaryListParam() { TypeCode = DicTypeCodeEnum.SchoolStage.ParseToInt().ToString() });
                        if (schoolStageList == null || schoolStageList.Count == 0)
                        {
                            obj.Tag = 0;
                            obj.Message = "导入失败，请联系客服配置学段后再导入。";
                            return obj;
                        }
                    }
                    else
                    {
                        obj.Tag = 0;
                        obj.Message = "你单位无权导入班级信息。";
                        return obj;
                    }

                    //var gradeList = await staticDictionaryService.GetList(new StaticDictionaryListParam() { TypeCode = DicTypeCodeEnum.Grade.ParseToInt().ToString() });
                    var gradeList = await staticDictionaryService.GetChildList(new StaticDictionaryListParam() { TypeCode = DicTypeCodeEnum.Grade.ParseToInt().ToString() , PTypeCode = DicTypeCodeEnum.SchoolStage.ParseToInt().ToString() });
                    if (gradeList == null || gradeList.Count == 0)
                    {
                        obj.Tag = 0;
                        obj.Message = "导入失败，请联系客服配置班级后再导入。";
                        return obj;
                    }
                    var classList = await staticDictionaryService.GetList(new StaticDictionaryListParam() { TypeCode = DicTypeCodeEnum.Class.ParseToInt().ToString() });
                    var errorMsg = "";
                    int index = 4;
                    foreach (var item in list)
                    {
                        UnitEntity unitEntity = new UnitEntity();
                        string unitName = $"第【{index}】行数据,";
                        index++;

                        if (operatorInfo.UnitType == UnitTypeEnum.School.ParseToInt())
                        {
                            unitEntity = unitList[0];
                            item.UnitId = unitEntity.Id;
                            item.Code = unitEntity.Code;
                            /*验证学段是否正确*/
                            if (schoolStageList.Where(m => m.DicName == item.SchoolStageName).Count() == 0)
                            {
                                errorMsg += $"导入失败，{unitName}学段填写不对，当前单位不存在该学段。<br/>";
                            }
                            else
                            {
                                item.SchoolStage = schoolStageList.Where(m => m.DicName == item.SchoolStageName).FirstOrDefault().DictionaryId;
                            }
                        }
                        else
                        {
                            /*验证单位是否存在*/
                            if (unitList.Where(m => m.Code == item.Code && m.Name == item.UnitName).Count() == 0)
                            {
                                errorMsg += $"导入失败，{unitName}单位名称或者编码在平台中不存在。<br/>";
                            }
                            else
                            {
                                unitEntity = unitList.Where(m => m.Code == item.Code && m.Name == item.UnitName).FirstOrDefault();
                                item.UnitId = unitEntity.Id;
                                item.Code = unitEntity.Code;
                            }
                            /*验证学段是否正确*/
                            if (schoolStageList.Where(m => m.DicName == item.SchoolStageName).Count() == 0)
                            {
                                errorMsg += $"导入失败，{unitName}学段填写不对，当前单位不存在该学段。<br/>";
                            }
                            else
                            {
                                item.SchoolStage = schoolStageList.Where(m => m.DicName == item.SchoolStageName).FirstOrDefault().DictionaryId;
                                //验证，学段和单位属性是否匹配
                            }
                        }

                        /*验证入学年份*/
                        if (!(item.StartYear > 0))
                        {
                            errorMsg += $"导入失败，入学年份信息填写不对。<br/>";
                        }

                        /*验证年级是否存在*/
                        if (gradeList.Where(m => m.DicName == item.GradeName).Count() > 0)
                        {
                            var gradeEntity = gradeList.Where(m => m.DicName == item.GradeName).FirstOrDefault();
                            item.GradeId = gradeEntity.DictionaryId;
                            //验证，学段和年级是否一致
                            if (gradeEntity.Pid != item.SchoolStage)
                            {
                                errorMsg += $"导入失败，{unitName}导入的年级和学段不匹配。<br/>";
                            }
                        }
                        else
                        {
                            errorMsg += $"导入失败，{unitName}年级信息填写不对，请从下拉中选择。<br/>";
                        }
                        var startYear = DateTime.Now.Year;
                        var changeMonth = DateTime.Now.Month;
                        var changeDay = DateTime.Now.Day;
                        var gradeListTemp = gradeList.Where(m => m.DictionaryId == item.GradeId).ToList();
                        if (gradeListTemp != null && gradeListTemp.Count > 0)
                        {
                            if (changeMonth < 8 || (changeMonth == 8 && changeDay < 25))
                            {
                                if (gradeListTemp[0].Nature > 0)
                                {
                                    startYear = startYear - gradeListTemp[0].Nature.Value;
                                }
                            }
                            else
                            {
                                if (gradeListTemp[0].Nature > 0)
                                {
                                    startYear = startYear - gradeListTemp[0].Nature.Value + 1;
                                }
                            }
                        }
                        if (item.StartYear != startYear)
                        {
                            errorMsg += $"导入失败，{unitName}入学年份、和年级填写不匹配。<br/>";
                        }
                        /*验证班级是否存在*/
                        if (classList.Where(m => m.DicName == item.ClassDesc).Count() > 0)
                        {
                            item.ClassId = classList.Where(m => m.DicName == item.ClassDesc).FirstOrDefault().DictionaryId;
                        }
                        else
                        {
                            item.ClassId = 0;
                        }

                        //判断当前班级是否已经存在。
                        if (string.IsNullOrEmpty(item.ClassDesc))
                        {
                            errorMsg += $"导入失败，{unitName}班级信息填写不对，班级信息未填写。<br/>";
                        }
                        else
                        {
                            if (gradeclassList.Where(m => m.GradeId == item.GradeId && m.ClassDesc == item.ClassDesc).Count() > 0)
                            {
                                errorMsg += $"导入失败，{unitName}班级信息填写不对，该班级已存在。<br/>";
                            }

                            if (list.Where(m => m.GradeId == item.GradeId && m.ClassDesc == item.ClassDesc).Count() > 1)
                            {
                                errorMsg += $"导入失败，{unitName}班级信息填写不对，班级重复添加。<br/>";
                            }
                        }

                        /*验证班学生数大于0*/
                        if (!(item.StudentNum > 0))
                        {
                            errorMsg += $"导入失败，{unitName}学生数量信息填写不对。<br/>";
                        }
                        if (item.StudentNum >= 70)
                        {
                            errorMsg += $"导入失败，{unitName}班级学生数必须小于70。<br/>";
                        }
                    }
                    if (list.Count > list.GroupBy(m => new { m.GradeId, m.ClassId }).Count())
                    {
                        errorMsg += $"导入失败，班级信息填写不对,存在重复数据。<br/>";
                    }
                    if (errorMsg != "")
                    {
                        obj.Tag = 0;
                        obj.Message = errorMsg;
                        return obj;
                    }
                    await db.BeginTrans();
                    foreach (SchoolGradeClassInputModel model in list)
                    {
                        //添加班级信息
                        await schoolGradeClassService.SaveTransForm(new SchoolGradeClassEntity()
                        {
                            UnitId = model.UnitId,
                            StartYear = model.StartYear,
                            SchoolStage = model.SchoolStage,
                            GradeId = model.GradeId,
                            ClassId = model.ClassId,
                            ClassDesc = model.ClassDesc,
                            StudentNum = model.StudentNum

                        }, db);
                    }
                    await db.CommitTrans();
                    obj.Tag = 1;
                    obj.Message = "导入成功。";
                    var updateStudentList = list.GroupBy(m => new { m.Code, m.UnitId }).ToList();
                    foreach (var item in updateStudentList)
                    {
                        //更新学校累计学生数
                        await schoolExtensionService.UpdateStudentNum(item.FirstOrDefault().UnitId.Value);

                        //更新学校轨数
                        UpdateSchoolTrackNum(item.FirstOrDefault().UnitId.Value);
                    }
                }
                else
                {
                    obj.Message = " 未找到导入的数据";
                    await db.RollbackTrans();
                }

            }
            catch (Exception ex)
            {
                await db.RollbackTrans();
                obj.Tag = 0;
                obj.Message = "修改信息异常，请联系客服协助处理。";
                throw new Exception("程序出现异常，异常信息为：" + ex.Message);
            }
            return obj;
        }
        /// <summary>
        /// 班级升级
        /// </summary>
        /// <returns></returns>
        public async Task<TData> UpgradeForm()
        {
            TData obj = new TData();
            OperatorInfo operatorInfo = await Operator.Instance.Current(ApiToken);
            //需求：如果下学期已经结束且没到8月25日，提示：8月25日之后，才可进行年级升级；
            var paramTerm = new SchoolTermListParam();
            paramTerm.SchoolYearEnd = DateTime.Now.Year;
            paramTerm.SchoolTerm = SchoolTermEnum.NextSemester.ParseToInt();
            var listTerm = await schoolTermBll.GetList(paramTerm);
            if (listTerm.Tag == 1 && listTerm.Data != null && listTerm.Data.Count > 0)
            {
                var entitySchoolTerm = listTerm.Data.FirstOrDefault();
                //if (entitySchoolTerm.TermEnd > DateTime.Now.Date)
                //{
                //    obj.Tag = 0;
                //    obj.Message = "当前学期还未结束，学期结束后并且在8月25日之后,才可进行年级升级；";
                //    return obj;
                //}
                var consultDate = DateTime.Now.Date.AddDays(-DateTime.Now.Day + 1).AddMonths(-DateTime.Now.Month + 1);
                consultDate = consultDate.AddMonths(7).AddDays(25);
                if (entitySchoolTerm.TermEnd < DateTime.Now.Date && DateTime.Now.Date < consultDate)
                {
                    obj.Tag = 0;
                    obj.Message = "8月25日之后，才可进行年级升级；";
                    return obj;
                }
            }

            long schoolId = (long)operatorInfo.UnitId;
            var gradeClassList = await schoolGradeClassService.GetList(new SchoolGradeClassListParam() { UnitId = schoolId, IsGraduate = 0 });
            if (gradeClassList == null && gradeClassList.Count == 0)
            {
                obj.Tag = 0;
                obj.Message = "班级升级失败,你没有要升级的班级信息。";
                return obj;
            }
            //获取年级班级默认信息
            var gradeList = await staticDictionaryService.GetChildList(new StaticDictionaryListParam() { TypeCode = DicTypeCodeEnum.Grade.ParseToInt().ToString(), PTypeCode = DicTypeCodeEnum.SchoolStage.ParseToInt().ToString() });
            if (gradeList == null || gradeList.Count == 0)
            {
                obj.Tag = 0;
                obj.Message = "班级升级失败,系统未配置年级初始化信息。";
                return obj;
            }
            List<SchoolGradeClassEntity> UpdateList = new List<SchoolGradeClassEntity>();
            //入学年份
            //判断 学段，
            //1：根据入选年份，获取当前班级应该是几年级。
            //2：判断和当前年级是否一致，不一致更新。
            var inStudentYear = GetInStudentYear();//获取当前入学年份。 
            foreach (var item in gradeClassList)
            {
                if (item.StartYear == inStudentYear)
                {
                    var tempGradeId = gradeList.Where(m => m.Pid == item.SchoolStage).OrderBy(m => m.DictionaryId).FirstOrDefault().DictionaryId;
                    if (item.GradeId != tempGradeId)
                    {
                        item.GradeId = tempGradeId;
                        UpdateList.Add(item);
                    }
                }
                else if (item.StartYear < inStudentYear)
                {
                    var diffyear = inStudentYear - (int)item.StartYear;
                    if (diffyear > 0)
                    {
                        var tempgradelist = gradeList.Where(m => m.Pid == item.SchoolStage).ToList();
                        if (tempgradelist.Count > diffyear)
                        {
                            var tempGradeId = tempgradelist.OrderBy(m => m.DictionaryId).ElementAt(diffyear).DictionaryId;
                            if (tempGradeId != item.GradeId)
                            {
                                item.GradeId = tempGradeId;
                                UpdateList.Add(item);
                            }
                        }
                        else
                        {
                            item.IsGraduate = 1;//毕业
                            UpdateList.Add(item);
                        }
                    }
                }
            }
            if (UpdateList.Count > 0)
            {
                Repository db = schoolGradeClassService.BaseRepository();
                try
                {
                    await db.BeginTrans();
                    foreach (var item in UpdateList)
                    {
                        await schoolGradeClassService.UpdateTransForm(item,new List<string>() { "GradeId", "IsGraduate" }, db);
                    }
                    obj.Tag = 1;
                    await db.CommitTrans();

                    ////更新班级数和学生数量
                    await schoolExtensionService.UpdateStudentNum(schoolId);

                    //更新学校轨数
                    UpdateSchoolTrackNum(schoolId);
                    obj.Tag = 1;
                    obj.Message = "升级成功。";
                    obj.ExtendData = 1;//区分是否首次升级成功
                }
                catch (Exception ex)
                {
                    await db.RollbackTrans();
                    obj.Tag = 0;
                    obj.Message = "升级异常，请联系客服协助处理。";
                    throw new Exception("程序出现异常，异常信息为：" + ex.Message);
                }
            }
            else
            {
                obj.Tag = 1;
                obj.Message = "没有需要升级的班级信息。";
            }
            return obj;
        }

        /// <summary>
        /// 设置老师任课班级
        /// </summary>
        /// <returns></returns>
        public async Task<TData> SaveTeachCourseClass(UserTeachClassInputModel model)
        {
            TData obj = new TData();
            OperatorInfo operatorInfo = await Operator.Instance.Current(ApiToken);
            /**
             * 1：验证是否为备课组长。
             * 2: 获取任课信息。
             * 3: 更新任课信息
             * 注：当前班级如果没有任课信息，直接添加，如果存在任课信息，需要去掉。
             *      
             * **/
            var entity = await schoolGradeClassService.GetEntity(model.GradeClassId);
            if (entity == null)
            {
                obj.Tag = 0;
                obj.Message = "任课信息设置失败,班级信息不存在。";
                return obj;
            }
            var isUpgrase = await GetIsUpgrade(operatorInfo, 0);
            if (isUpgrase)
            {
                obj.Tag = 0;
                obj.Message = "学校还未进行年级升级，禁止设置；请联系系统管理员进行年级升级。";
                return obj;
            }

            //获取学科对应学段。
            int SchoolStage = 0;
            var staticDicList = await staticDictionaryService.GetChildToList(new StaticDictionaryListParam() { TypeCode = DicTypeCodeEnum.SchoolStage.ParseToInt().ToString(), Pid = entity.GradeId.Value, PTypeCode = DicTypeCodeEnum.Grade.ParseToInt().ToString() });
            if (staticDicList != null && staticDicList.Count > 0)
            {
                SchoolStage = staticDicList.FirstOrDefault().DictionaryId.Value;
            }

            //存在
            UserClassInfoEntity classInfoEntity = new UserClassInfoEntity();
            var list = await userClassInfoService.GetList(new Model.Param.PersonManage.UserClassInfoListParam() { UnitId = operatorInfo.UnitId, UserId = model.UserId, IsCurrentUnit = 1 });
            if (list != null && list.Count > 0)
            {
                classInfoEntity = list.FirstOrDefault();
                classInfoEntity.SubjectIdz = TextHelper.GetIdzAddItem(classInfoEntity.SubjectIdz, model.CourseId);
                //学段
                classInfoEntity.SchoolStageIdz = TextHelper.GetIdzAddItem(classInfoEntity.SchoolStageIdz, SchoolStage);
            }
            else
            {
                classInfoEntity.IsCurrentUnit = 1;
                classInfoEntity.UnitId = operatorInfo.UnitId.Value;
                classInfoEntity.UserId = model.UserId;
                classInfoEntity.SubjectIdz = model.CourseId.ToString();
                classInfoEntity.SchoolStageIdz = SchoolStage.ToString();
            }

            //处理班级
            UserTeachClassEntity teachClassEntity = new UserTeachClassEntity();
            if (classInfoEntity.Id > 0)
            {
                var teachClassList = await userTeachClassService.GetList(new Model.Param.PersonManage.UserTeachClassListParam() { UserClassInfoId = classInfoEntity.Id, CourseId = model.CourseId, BaseIsDelete = 0 });
                if (teachClassList != null && teachClassList.Count > 0)
                {
                    teachClassEntity = teachClassList.FirstOrDefault();
                    teachClassEntity.ClassIdz = TextHelper.GetIdzAddItem(teachClassEntity.ClassIdz, model.GradeClassId);
                }
            }
            if (teachClassEntity.Id == null)
            {
                teachClassEntity.CourseId = model.CourseId;
                teachClassEntity.ClassIdz = model.GradeClassId.ToString();
            }
            var oldTeachCuorse = await userTeachClassService.GetList(new Model.Param.PersonManage.UserTeachClassListParam() { CourseId = teachClassEntity.CourseId, ClassId = model.GradeClassId, BaseIsDelete = 0 });
            var db = userClassInfoService.BaseRepository();
            try
            {
                await db.BeginTrans();
                await userClassInfoService.SaveTransForm(classInfoEntity, db);

                teachClassEntity.UserClassInfoId = classInfoEntity.Id.Value;
                await userTeachClassService.SaveTransForm(teachClassEntity, db);

                if (oldTeachCuorse != null && oldTeachCuorse.Count > 0)
                {
                    var oldTeachClassEntity = oldTeachCuorse.FirstOrDefault();
                    if (oldTeachClassEntity.Id != teachClassEntity.Id)
                    {
                        oldTeachClassEntity.ClassIdz = TextHelper.GetIdzDeleteItem(oldTeachClassEntity.ClassIdz, model.GradeClassId);
                        await userTeachClassService.SaveTransForm(oldTeachClassEntity, db);
                    }
                }
                await db.CommitTrans();
                obj.Tag = 1;
                obj.Message = "设置成功";
            }
            catch (Exception ex)
            {
                await db.RollbackTrans();
                obj.Tag = 0;
                obj.Message = "设置失败，数据异常，请刷新重新操作，如无法解决请联系客服协助处理。" + ex.Message;
                throw new Exception("程序出现异常，异常信息为：" + ex.Message);
            }
            return obj;
        }


        /// <summary>
        /// 设置老师任课班级
        /// </summary>
        /// <returns></returns>
        public async Task<TData> DelTeachCourseClass(UserTeachClassInputModel model)
        {
            TData obj = new TData();
            OperatorInfo operatorInfo = await Operator.Instance.Current(ApiToken);
            /**
             * 1：验证是否为备课组长。
             * 2: 获取任课信息。 
             * 注：当前班级如果没有任课信息，直接添加，如果存在任课信息，需要去掉。
             *      
             * **/
            var entity = await schoolGradeClassService.GetEntity(model.GradeClassId);
            if (entity == null)
            {
                obj.Tag = 0;
                obj.Message = "任课信息设置失败,班级信息不存在。";
                return obj;
            }
            var isUpgrase = await GetIsUpgrade(operatorInfo, 0);
            if (isUpgrase)
            {
                obj.Tag = 0;
                obj.Message = "学校还未进行年级升级，禁止设置；请联系系统管理员进行年级升级。";
                return obj;
            }
            obj.Tag = 1;
            obj.Message = "删除失败！";
            //存在
            UserTeachClassEntity teachClassEntity = new UserTeachClassEntity();
            var list = await userClassInfoService.GetList(new Model.Param.PersonManage.UserClassInfoListParam() { UnitId = operatorInfo.UnitId, UserId = model.UserId, IsCurrentUnit = 1 });
            if (list != null && list.Count > 0)
            {
                var oldTeachCuorse = await userTeachClassService.GetList(new Model.Param.PersonManage.UserTeachClassListParam() { UserClassInfoId = list[0].Id, CourseId = model.CourseId, ClassId = model.GradeClassId, BaseIsDelete = 0 });
                if (oldTeachCuorse==null || oldTeachCuorse.Count==0)
                {
                    obj.Tag = 0;
                    obj.Message = "当前老师任课信息已不存在，请刷新重新操作，如无法解决请联系客服协助处理。";
                    return obj;
                }
                teachClassEntity= oldTeachCuorse.FirstOrDefault();
            }
            var db = userClassInfoService.BaseRepository();
            try
            {


                if (teachClassEntity != null && teachClassEntity.Id > 0)
                {
                    await db.BeginTrans();
                    teachClassEntity.ClassIdz = TextHelper.GetIdzDeleteItem(teachClassEntity.ClassIdz, model.GradeClassId);
                    await userTeachClassService.SaveTransForm(teachClassEntity, db);
                    await db.CommitTrans();
                    obj.Tag = 1;
                    obj.Message = "删除成功！";
                } 
            }
            catch (Exception ex)
            {
                await db.RollbackTrans();
                obj.Tag = 0;
                obj.Message = "设置失败，数据异常，请刷新重新操作，如无法解决请联系客服协助处理。" + ex.Message;
                throw new Exception("程序出现异常，异常信息为：" + ex.Message);
            }
            return obj;
        }

        public async Task<TData> SetStatuz(string ids,int statuz, long? unitid)
        {
            TData obj = new TData();
            //验证班级是否可以删除，1：验证登记信息是否有班级，2预约信息是否有班级。
            var param = new SchoolGradeClassListParam();
            param.Ids = ids;
            param.UnitId = unitid;
            var list = await schoolGradeClassService.GetList(param);
            if (list != null && list.Count > 0)
            {
                Repository db = schoolGradeClassService.BaseRepository();
                try
                {
                    await db.BeginTrans();
                    await schoolGradeClassService.SetStatuzTransForm(ids, statuz, db);

                    //更新班级数和学生数量
                    await schoolExtensionService.UpdateClassNum(unitid == null ? 0 : (long)unitid, db);
                    obj.Tag = 1;
                    await db.CommitTrans();
                }
                catch (Exception ex)
                {
                    await db.RollbackTrans();
                    obj.Tag = 0;
                    obj.Message = "设置状态信息异常，请联系客服协助处理。";
                    throw new Exception("程序出现异常，异常信息为：" + ex.Message);
                }
                //更新学校轨数
                UpdateSchoolTrackNum(unitid ?? 0);
                foreach (var item in list)
                {
                    var listClass = await schoolGradeClassService.GetList(new SchoolGradeClassListParam() { UnitId = unitid, GradeId = item.GradeId, IsGraduate = 0, Statuz = StatusEnum.Yes.ParseToInt() });
                    //更新编制计划、更新达标
                    await planInfoBll.UpdatePlanClassIdz(item.GradeId ?? 0, 0, listClass, unitid ?? 0);
                }
    
            }
            return obj;
        }

        #endregion

        #region 保存方法-备份数据至历史中。
        /// <summary>
        /// 根据学年备份单位的年级班级信息
        /// </summary>
        /// <param name="schoolyear"></param>
        /// <returns></returns>
        public async Task<TData> BackUpToHistory(int schoolyear, long unitid = 0)
        {
            TData r = new TData();
            var param = new SchoolGradeClassHistoryListParam();
            param.SchoolYearStart = schoolyear;
            if (unitid > 0)
            {
                param.UnitId = unitid;
            }
            var list = await schoolGradeClassHistoryService.GetList(param);
            if (list != null && list.Count > 0)
            {
                r.Tag = 0;
                r.Message = "当前学年下，该年级班级已备份。";
                return r;
            }
            //查询列表备份
            var paramGradeClass = new SchoolGradeClassListParam();
            paramGradeClass.SchoolYear = schoolyear;
            paramGradeClass.UnitId = unitid;

            var currentList = await schoolGradeClassService.GetStatisticsByYear(paramGradeClass);
            if (currentList != null && currentList.Count() > 0)
            {
                List<SchoolGradeClassHistoryEntity> listHistoryAdd = new List<SchoolGradeClassHistoryEntity>();
                foreach (var item in currentList)
                {

                }

                await schoolGradeClassHistoryService.SaveAddForm(listHistoryAdd);
            }
            else
            {
                //
                r.Tag = 0;
                r.Message = "当前学年，该单位没有需要备份的班级信息。";
            }
            return r;
        }
        #endregion

        #region 私有方法

        /// <summary>
        /// 更新学校轨数
        /// </summary>
        /// <param name="schoolId"></param>
        /// <returns></returns>
        private async Task UpdateSchoolTrackNum(long schoolId)
        {
            var classList = await schoolGradeClassService.GetList(
                new SchoolGradeClassListParam
                {
                    UnitId = schoolId,
                    IsGraduate = IsEnum.No.ParseToInt()
                });
            //轨数 = 班级数量 / 年级数量
            if (classList != null && classList.Count > 0)
            {
                decimal trackNum = classList.Count / classList.GroupBy(f => f.GradeId).Count();

                var schoolList = await schoolExtensionService.GetList(new SchoolExtensionListParam { UnitId = schoolId });
                if (schoolList.Count > 0)
                {
                    var school = schoolList.FirstOrDefault();
                    school.TrackNum = Convert.ToInt32(Math.Floor(trackNum));
                    await schoolExtensionService.UpdateForm(school, new List<string>() { "TrackNum" });

                    //更新学校轨数同时要更新学校已生成指标数量 
                    await attendStaticBLL.InstrumentAttendStandardNum(schoolId);

                   // experimentAttendStaticBll.SaveStaticResult(new ExperimentBookingEntity() { SchoolId= schoolId }, "", 5);

                }
            }
        }

        /// <summary>
        /// 获取当前入学年份。8月25号为一个界限，之前的入学年份为上年，之后为当前年
        /// </summary>
        /// <returns></returns>
        public int GetInStudentYear()
        {
            var startYear = DateTime.Now.Year;
            var changeMonth = DateTime.Now.Month;
            var changeDay = DateTime.Now.Day;
            if (changeMonth < 8 || (changeMonth == 8 && changeDay < 25))
            {
                startYear = (startYear - 1);
            }
            return startYear;
        }

        /// <summary>
        /// 是否需要升级，默认不需要，true:需要
        /// </summary>
        /// <param name="gradeclassid">指定班级Id</param>
        /// <returns></returns>
        public async Task<bool> GetIsUpgrade(OperatorInfo operatorInfo, long gradeclassid = 0)
        {
            var upgrade = false;
            SchoolGradeClassListParam paramClass = new SchoolGradeClassListParam();
            paramClass.UnitId = operatorInfo.UnitId;
            paramClass.IsGraduate = 0;
            if (gradeclassid > 0)
            {
                paramClass.Id = gradeclassid;
            }
            //判断是否存在年级需要升级的，如果存在则提示。
            var gradeClassList = await schoolGradeClassService.GetList(paramClass);
            if (gradeClassList == null && gradeClassList.Count == 0)
            {
                upgrade = true;
                return upgrade;
            }
            //获取年级班级默认信息
            var gradeList = await staticDictionaryService.GetChildList(new StaticDictionaryListParam() { TypeCode = DicTypeCodeEnum.Grade.ParseToInt().ToString(), PTypeCode = DicTypeCodeEnum.SchoolStage.ParseToInt().ToString() });
            if (gradeList == null || gradeList.Count == 0)
            {
                upgrade = true;
                return upgrade;
            }
            //入学年份
            //判断 学段，
            //1：根据入选年份，获取当前班级应该是几年级。
            //2：判断和当前年级是否一致，不一致更新。
            var inStudentYear = GetInStudentYear();//获取当前入学年份。  
            foreach (var item in gradeClassList)
            {
                if (item.StartYear == inStudentYear)
                {
                    var tempGradeId = gradeList.Where(m => m.Pid == item.SchoolStage).OrderBy(m => m.DictionaryId).FirstOrDefault().DictionaryId;
                    if (item.GradeId != tempGradeId)
                    {
                        upgrade = true;
                        return upgrade;
                    }
                }
                else if (item.StartYear < inStudentYear)
                {
                    var diffyear = inStudentYear - (int)item.StartYear;
                    if (diffyear > 0)
                    {
                        var tempgradelist = gradeList.Where(m => m.Pid == item.SchoolStage).ToList();
                        if (tempgradelist.Count > diffyear)
                        {
                            var tempGradeId = tempgradelist.OrderBy(m => m.DictionaryId).ElementAt(diffyear).DictionaryId;
                            if (tempGradeId != item.GradeId)
                            {
                                upgrade = true;
                                return upgrade;
                            }
                        }
                        else
                        {
                            upgrade = true;
                            return upgrade;
                        }
                    }
                }
            }
            return upgrade;
        }
        #endregion
    }
}
