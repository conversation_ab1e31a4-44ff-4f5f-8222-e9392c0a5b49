﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Model.Input.SystemManage;
using Dqy.Syjx.Entity.SystemManage;
using Dqy.Syjx.Model.Param.SystemManage;
using Dqy.Syjx.Service.SystemManage;

namespace Dqy.Syjx.Business.SystemManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2023-08-10 13:30
    /// 描 述：业务类
    /// </summary>
    public class AppManageBLL
    {
        private AppManageService appManageService = new AppManageService();

        #region 获取数据
        public async Task<TData<List<AppManageEntity>>> GetList(AppManageListParam param)
        {
            TData<List<AppManageEntity>> obj = new TData<List<AppManageEntity>>();
            obj.Data = await appManageService.GetList(param);
            obj.Total = obj.Data.Count;
            obj.Tag = 1;
            return obj;
        }

        public async Task<TData<List<AppManageEntity>>> GetPageList(AppManageListParam param, Pagination pagination)
        {
            TData<List<AppManageEntity>> obj = new TData<List<AppManageEntity>>();
            obj.Data = await appManageService.GetPageList(param, pagination);
            obj.Total = pagination.TotalCount;
            obj.Tag = 1;
            return obj;
        }

        public async Task<TData<AppManageEntity>> GetEntity(long id)
        {
            TData<AppManageEntity> obj = new TData<AppManageEntity>();
            obj.Data = await appManageService.GetEntity(id);
            if (obj.Data != null)
            {
                obj.Tag = 1;
            }
            return obj;
        }
        #endregion

        #region 提交数据
        public async Task<TData<string>> SaveForm(AppManageInputModel model)
        {
            TData<string> obj = new TData<string>();
            var entity = new AppManageEntity();
            
            if (model.Id > 0)
            {
                entity = await appManageService.GetEntity(model.Id);
            }else if(model.Id == 0)
            {
                model.AppCode = TextHelper.GenerateRandomNumber(16);
            }
            entity.Id = model.Id;
            entity.AppType = model.AppType;
            entity.AppName = model.AppName;
            entity.AppKey = model.AppKey;
            entity.Memo = model.Memo;
            entity.AppSecret = model.AppSecret;
            entity.AngentId = model.AngentId;
            entity.Secret = model.Secret;
            entity.CorpId = model.CorpId;
            entity.CorpSecret = model.CorpSecret;
            entity.UnitType = model.UnitType;
            entity.UnitId = model.UnitId;
            entity.AppCode = model.AppCode;
            entity.ClientId = model.ClientId;
            entity.ClientSecret = model.ClientSecret;
            entity.CallBackUrl = model.CallBackUrl;
            entity.AuthHost = model.AuthHost;
            entity.ApiName = model.ApiName;
            entity.ApiSecret = model.ApiSecret;
            entity.GenerateAppKey = model.GenerateAppKey;
            entity.GenerateAppSecret = model.GenerateAppSecret;
            entity.IsMain = model.IsMain;
            entity.DataHost = model.DataHost;
            entity.WebHost = model.WebHost;

            ////校验是否已经存在该应用
            //var list = await appManageService.GetList(new AppManageListParam { CorpId = model.CorpId,Secret = model.Secret,AngentId = model.AngentId });
            //if (list.Count > 0)
            //{
            //    obj.Message = "已存在该应用";
            //    obj.Tag = 0;
            //    return obj;
            //}
            //else
            //{

            //}


            await appManageService.SaveForm(entity);
            obj.Data = entity.Id.ParseToString();
            obj.Tag = 1;

            return obj;
        }

        public async Task<TData> DeleteForm(string ids)
        {
            TData obj = new TData();
            if (ids.IsEmpty()){ return obj; }
            AppManageListParam param = new AppManageListParam { Ids = ids };
            var list = await appManageService.GetList(param);
            ids = "";
            foreach (var m in list)
            {
                 //此处需增加校验是否满足删除条件
            
                 ids += ", " + m.Id.Value;
             }
            obj.Tag = 1;
            if (ids.Length > 1)
                await appManageService.DeleteForm(ids);
            else
                 obj.Tag = 0;
            return obj;
        }
        #endregion

        #region 私有方法
        #endregion
    }
}
