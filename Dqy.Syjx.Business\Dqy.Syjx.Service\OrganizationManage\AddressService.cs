﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.OrganizationManage;
using Dqy.Syjx.Model.Param.OrganizationManage;
using Dqy.Syjx.Enum;

namespace Dqy.Syjx.Service.OrganizationManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-09-29 17:19
    /// 描 述：地址管理服务类
    /// </summary>
    public class AddressService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<AddressEntity>> GetList(AddressListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<AddressEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<AddressEntity>(id);
        }

        /// <summary>
        /// 查询地址列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<List<AddressEntity>> GetAddressList(AddressListParam param)
        {
            StringBuilder strSql = new StringBuilder();
            var parameter = new List<DbParameter>();
            strSql.Append(@"
                            SELECT Id,Pid,UnitId,Name,AliasName,Code,Address,Depth,Sort,AddressType,
	                               DepartmentName,DepartmentIds FROM
                            (
	                            SELECT A.Id,A.Pid,A.UnitId,A.Name,A.AliasName,A.Code,A.Address,A.Depth,A.Sort,A.AddressType,
		                               A.Statuz,D.DepartmentName,DR.SysDepartmentId AS DepartmentIds
	                            FROM  up_Address AS A
	                            LEFT JOIN  up_DepeartmentRelation AS DR ON A.Id = DR.ExtensionObjId AND DR.ExtensionType = 2
	                            LEFT JOIN  SysDepartment AS D ON DR.SysDepartmentId = D.Id
	                            WHERE A.BaseIsDelete = 0
                            ) F ");
            if (param != null)
            {
                strSql.Append(" WHERE UnitId = @UnitId");
                parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.CurrentUnitId));

                strSql.Append(" ORDER BY Pid,Sort ASC");
            }
            var list = await this.BaseRepository().FindList<AddressEntity>(strSql.ToString(), parameter.ToArray());
            return list.ToList();
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(AddressEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }
        public async Task SaveTransForm(AddressEntity entity, Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }
        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if (ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");

            // 使用参数化查询和跨数据库兼容的 IN 语法
            var parameters = new List<DbParameter>
            {
                DbParameterExtension.CreateDbParameter("@BaseIsDelete", 1)
            };

            string strSql = $"UPDATE up_Address SET BaseIsDelete = @BaseIsDelete WHERE Id IN ({ids})";
            await this.BaseRepository().ExecuteBySql(strSql, parameters.ToArray());
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="unitId"></param>
        /// <returns></returns>
        public async Task DeleteByIds(string ids, long unitId)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if (ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");

            // 使用参数化查询和跨数据库兼容的 IN 语法
            var parameters = new List<DbParameter>
            {
                DbParameterExtension.CreateDbParameter("@UnitId", unitId),
                DbParameterExtension.CreateDbParameter("@BaseIsDelete", 1)
            };

            //删除父级下的子集数据
            string sql = $@"UPDATE up_Address SET BaseIsDelete = @BaseIsDelete
                            WHERE BaseIsDelete = 0 AND UnitId = @UnitId AND Depth <> 0 AND Pid IN ({ids})";
            await this.BaseRepository().ExecuteBySql(sql, parameters.ToArray());

            //删除选中的子集数据
            sql = $@"UPDATE up_Address SET BaseIsDelete = @BaseIsDelete
                     WHERE BaseIsDelete = 0 AND UnitId = @UnitId AND Id IN ({ids})";
            await this.BaseRepository().ExecuteBySql(sql, parameters.ToArray());
        }


        #endregion

        #region 私有方法
        private Expression<Func<AddressEntity, bool>> ListFilter(AddressListParam param)
        {
            var expression = LinqExtensions.True<AddressEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (param.CurrentUnitType == UnitTypeEnum.School.ParseToInt())
                {
                    expression = expression.And(t =>t.UnitId == param.CurrentUnitId);
                }
                if (param.Pid!=null && (long)param.Pid >=0)
                {
                    expression = expression.And(t => t.Pid == param.Pid);
                }
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (!string.IsNullOrEmpty(param.Name))
                {
                    if (param.OptType == 1)
                    {
                        expression = expression.And(t => t.Name == param.Name);
                        expression = expression.And(t => t.Pid == param.Pid);
                        if (param.Id > 0)
                        {
                            expression = expression.And(t => t.Id != param.Id);
                        }

                    }
                    else
                    {
                        expression = expression.And(t => t.Name == param.Name);
                    }
                }
            }
            return expression;
        }
        #endregion
    }
}
