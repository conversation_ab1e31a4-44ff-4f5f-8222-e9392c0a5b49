﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.OrganizationManage;
using Dqy.Syjx.Model.Param.OrganizationManage;
using Dqy.Syjx.Enum;

namespace Dqy.Syjx.Service.OrganizationManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-09-29 17:19
    /// 描 述：地址管理服务类
    /// </summary>
    public class AddressService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<AddressEntity>> GetList(AddressListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<AddressEntity>> GetPageList(AddressListParam param, Pagination pagination)
        {
            StringBuilder strSql = new();
            List<DbParameter> filter = ListFilter(param, strSql);
            var list = await this.BaseRepository().FindList<AddressEntity>(strSql.ToString(), filter.ToArray(), pagination);
            return list.ToList();
        }

        public async Task<AddressEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<AddressEntity>(id);
        }

        /// <summary>
        /// 查询地址列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<List<AddressEntity>> GetAddressList(AddressListParam param)
        {
            StringBuilder strSql = new StringBuilder();
            var parameter = new List<DbParameter>();
            strSql.Append(@"
                            SELECT Id,Pid,UnitId,Name,AliasName,Code,Address,Depth,Sort,AddressType,
	                               DepartmentName,DepartmentIds FROM
                            (
	                            SELECT A.Id,A.Pid,A.UnitId,A.Name,A.AliasName,A.Code,A.Address,A.Depth,A.Sort,A.AddressType,
		                               A.Statuz,D.DepartmentName,DR.SysDepartmentId AS DepartmentIds
	                            FROM  up_Address AS A
	                            LEFT JOIN  up_DepeartmentRelation AS DR ON A.Id = DR.ExtensionObjId AND DR.ExtensionType = 2
	                            LEFT JOIN  SysDepartment AS D ON DR.SysDepartmentId = D.Id
	                            WHERE A.BaseIsDelete = 0
                            ) F ");
            if (param != null)
            {
                strSql.Append(" WHERE UnitId = @UnitId");
                parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.CurrentUnitId));

                strSql.Append(" ORDER BY Pid,Sort ASC");
            }
            var list = await this.BaseRepository().FindList<AddressEntity>(strSql.ToString(), parameter.ToArray());
            return list.ToList();
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(AddressEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }
        public async Task SaveTransForm(AddressEntity entity, Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }
        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if (ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");

            // 使用参数化查询和跨数据库兼容的 IN 语法
            var parameters = new List<DbParameter>
            {
                DbParameterExtension.CreateDbParameter("@BaseIsDelete", 1)
            };

            string strSql = $"UPDATE up_Address SET BaseIsDelete = @BaseIsDelete WHERE Id IN ({ids})";
            await this.BaseRepository().ExecuteBySql(strSql, parameters.ToArray());
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="unitId"></param>
        /// <returns></returns>
        public async Task DeleteByIds(string ids, long unitId)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if (ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");

            // 使用参数化查询和跨数据库兼容的 IN 语法
            var parameters = new List<DbParameter>
            {
                DbParameterExtension.CreateDbParameter("@UnitId", unitId),
                DbParameterExtension.CreateDbParameter("@BaseIsDelete", 1)
            };

            //删除父级下的子集数据
            string sql = $@"UPDATE up_Address SET BaseIsDelete = @BaseIsDelete
                            WHERE BaseIsDelete = 0 AND UnitId = @UnitId AND Depth <> 0 AND Pid IN ({ids})";
            await this.BaseRepository().ExecuteBySql(sql, parameters.ToArray());

            //删除选中的子集数据
            sql = $@"UPDATE up_Address SET BaseIsDelete = @BaseIsDelete
                     WHERE BaseIsDelete = 0 AND UnitId = @UnitId AND Id IN ({ids})";
            await this.BaseRepository().ExecuteBySql(sql, parameters.ToArray());
        }


        #endregion

        #region 私有方法
        private Expression<Func<AddressEntity, bool>> ListFilter(AddressListParam param)
        {
            var expression = LinqExtensions.True<AddressEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (param.CurrentUnitType == UnitTypeEnum.School.ParseToInt())
                {
                    expression = expression.And(t =>t.UnitId == param.CurrentUnitId);
                }
                if (param.Pid!=null && (long)param.Pid >=0)
                {
                    expression = expression.And(t => t.Pid == param.Pid);
                }
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (!string.IsNullOrEmpty(param.Name))
                {
                    if (param.OptType == 1)
                    {
                        expression = expression.And(t => t.Name == param.Name);
                        expression = expression.And(t => t.Pid == param.Pid);
                        if (param.Id > 0)
                        {
                            expression = expression.And(t => t.Id != param.Id);
                        }

                    }
                    else
                    {
                        expression = expression.And(t => t.Name == param.Name);
                    }
                }
            }
            return expression;
        }

        private List<DbParameter> ListFilter(AddressListParam param, StringBuilder strSql)
        {
            strSql.Append($@"  SELECT * From (
                                  SELECT a1.Id,
                               a1.BaseIsDelete ,
                               a1.BaseCreateTime ,
                               a1.BaseModifyTime ,
                               a1.BaseCreatorId ,
                               a1.BaseModifierId ,
							   a1.BaseVersion ,
                               a1.UnitId ,
                               a1.Name ,
                               a1.AliasName ,
                               a1.Pid ,
                               a1.Code ,
                               a1.Address ,
                               a1.Memo ,
                               a1.Depth ,
                               a1.Sort ,
                               a1.Path ,
                               a1.AddressType ,
                               a1.Statuz ,
		                       b2.Name AS HouseName ,
							   b2.Sort AS HourseSort ,
                               stuff((select DISTINCT  ','+ CONVERT(NVARCHAR(128),c.SysDepartmentId) FROM  up_DepeartmentRelation AS c WHERE c.UnitId = b2.UnitId AND c.ExtensionType = 2 AND c.ExtensionObjId = a1.Id for xml PATH('')), 1, 1, '') as DepartmentIds
                        FROM  up_Address AS b2
                        INNER JOIN  up_Address AS a1 ON a1.BaseIsDelete = 0 AND a1.UnitId = b2.UnitId AND b2.Id = a1.Pid
                        WHERE b2.BaseIsDelete = 0 AND b2.Pid = 0
						UNION
						SELECT  ad1.Id ,
								ad1.BaseIsDelete ,
								ad1.BaseCreateTime ,
								ad1.BaseModifyTime ,
								ad1.BaseCreatorId ,
								ad1.BaseModifierId ,
								ad1.BaseVersion ,
								ad1.UnitId ,
								'' AS Name,
								ad1.AliasName ,
								ad1.Pid ,
								ad1.Code ,
								ad1.Address ,
								ad1.Memo ,
								ad1.Depth ,
								ad1.Sort AS Sort,
								ad1.Path ,
								ad1.AddressType ,
								ad1.Statuz  ,
								ad1.Name AS HouseName,
								ad1.Sort AS HourseSort,
								'' AS DepartmentIds
								FROM  up_Address AS ad1 WHERE ad1.Pid = 0
                          ) as tb1 WHERE   BaseIsDelete = 0 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                strSql.Append(" AND UnitId = @UnitId ");
                parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.CurrentUnitId));

                if (param.HourseId >= 0)
                {
                    strSql.Append(" AND (Pid = @HourseId OR Id  = @HourseId ) ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@HourseId", param.HourseId));
                }
                if (param.DepartmentIds != null && param.DepartmentIds.Length > 0)
                {
                    long departmentid = 0;
                    if (param.DepartmentIds.IndexOf(',')>-1)
                    {
                        var arrDepartment = param.DepartmentIds.Split(',');
                        if (arrDepartment!=null && arrDepartment.Length > 0)
                        {
                            long.TryParse(arrDepartment[arrDepartment.Length - 1], out departmentid);
                        }
                    }
                    else
                    {
                        long.TryParse(param.DepartmentIds, out departmentid);
                    }
                    if (departmentid> 0)
                    {
                        strSql.Append($" AND DepartmentIds like {departmentid} ");
                    }
                }
            }
            return parameter;
        }
        #endregion
    }
}
