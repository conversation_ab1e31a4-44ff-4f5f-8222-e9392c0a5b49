﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.ExperimentTeachManage;
using Dqy.Syjx.Model.Param.ExperimentTeachManage;
using Dqy.Syjx.Enum;

namespace Dqy.Syjx.Service.ExperimentTeachManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2024-10-24 09:50
    /// 描 述：实验考核评价服务类
    /// </summary>
    public class ExperimentCriticizeService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<ExperimentCriticizeEntity>> GetList(ExperimentCriticizeListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<ExperimentCriticizeEntity>> GetPageList(ExperimentCriticizeListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ListFilter(param, strSql);
            var list = await this.BaseRepository().FindList<ExperimentCriticizeEntity>(strSql.ToString(), filter.ToArray(), pagination);
            return list.ToList();
        }

        public async Task<ExperimentCriticizeEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<ExperimentCriticizeEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(ExperimentCriticizeEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(ExperimentCriticizeEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task UpdateForm(ExperimentCriticizeEntity entity, List<string> fields, Repository db)
        {
            if (fields != null)
            {
                await entity.Modify();
                fields.Add("BaseModifyTime");
                fields.Add("BaseModifierId");
            }
            if (db == null)
            {
                await this.BaseRepository().Update(entity, fields);
            }
            else
            {
                await db.Update(entity, fields);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update ex_ExperimentCriticize set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
                strSql = $"update ex_ExperimentCriticize set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<ExperimentCriticizeEntity, bool>> ListFilter(ExperimentCriticizeListParam param)
        {
            var expression = LinqExtensions.True<ExperimentCriticizeEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (param.ExperimentPublishId > 0)
                {
                    expression = expression.And(t => t.ExperimentPublishId == param.ExperimentPublishId);
                }
                if (param.ExperimentId > 0)
                {
                    expression = expression.And(t => t.ExperimentId == param.ExperimentId);
                }
                if (param.StudentId > 0)
                {
                    expression = expression.And(t => t.StudentId == param.StudentId);
                }
                if (param.ParentStudentId > 0)
                {
                    expression = expression.And(t => t.ParentStudentId == param.ParentStudentId);
                }
                if (param.UnitId > 0)
                {
                    expression = expression.And(t => t.SchoolId == param.UnitId);
                }
            }
            return expression;
        }


        private List<DbParameter> ListFilter(ExperimentCriticizeListParam param, StringBuilder strSql)
        {
            strSql.Append($@"  SELECT * From (
                                SELECT ec.* 
                                ,ps.StudentName
                                ,ps.IDCard6
                                ,ps.GradeId 
                                ,ps.ClassName
                                ,dic3.DicName AS GradeName
                                FROM ex_ExperimentCriticize AS ec
                                INNER JOIN up_ParentStudent AS ps ON ps.BaseIsDelete = 0 AND ec.ParentStudentId = ps.Id
                                LEFT JOIN  sys_static_dictionary AS dic3 ON dic3.BaseIsDelete = 0 AND ps.GradeId = dic3.DictionaryId AND dic3.TypeCode = '{DicTypeCodeEnum.Grade.ParseToInt().ToString()}'  
                           ) as tb1 WHERE   1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = @BaseIsDelete ");
                parameter.Add(DbParameterExtension.CreateDbParameter("@BaseIsDelete", 0));
                if (param.ExperimentPublishId > 0)
                {
                    strSql.Append(" AND ExperimentPublishId like @ExperimentPublishId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ExperimentPublishId", param.ExperimentPublishId));
                }
                if (param.ExperimentId > 0)
                {
                    strSql.Append(" AND ExperimentId like @ExperimentId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ExperimentId", param.ExperimentId));
                }
                if (param.UnitId > 0)
                {
                    strSql.Append(" AND SchoolId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                if (param.GradeId > 0)
                {
                    strSql.Append(" AND GradeId = @GradeId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@GradeId", param.GradeId));
                }
                if (!string.IsNullOrEmpty(param.Name))
                {
                    strSql.Append(" AND StudentName like @Name ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Name", $"%{param.Name}%"));
                }

            }
            return parameter;
        }

        /// <summary>
        /// 编制计划实验数量以学校为单位
        /// </summary>
        /// <param name="param"></param>
        /// <param name="strSql"></param>
        /// <returns></returns>
        public async Task<List<PublishDetailEntity>> GetRecordPageList(PublishDetailListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(@$"SELECT * From (SELECT  
                                    pd.Id ,	   
                                    pd.BaseCreateTime ,
                                    pd.BaseCreatorId ,
                                    pd.BaseIsDelete ,
                                    pd.BaseModifierId ,
                                    pd.BaseModifyTime ,
                                    pd.BaseVersion ,
                                    ep.ClassTime ,
                                    ep.CourseId ,
                                    ep.FunRoomId ,
                                    ep.FunRoomName ,
                                    ep.Name ,
                                    ep.Num ,
                                    ep.SchoolId ,
                                    ep.SchoolTerm ,
                                    ep.SchoolYearEnd ,
                                    ep.SchoolYearStart ,
                                    ep.SectionId ,
                                    ep.SectionShow ,
                                   
                                    ep.StudentNumed ,
                                    ep.StudentNumLimit ,
                                    sd4.DicName AS CourseName ,
                       
                                    u2.Name AS SchoolName ,u2.Sort
									,sdic.DictValue AS ExperimentTypeName
								    ,su5.RealName AS GuidanceTeacherName
									,pd.GuidanceTeacher 	
									,pd.ExperimentName	
									,pd.ExperimentPublishId
                                    ,pd.ExperimentId
                                    ,pd.AttendanceNum
									,eb.Statuz
									,eb.Id AS BookingId		
                                    ,ISNULL(er.Id,0) AS RecordId		
                                    FROM  ex_ExperimentPublish AS ep
									INNER JOIN ex_PublishDetail AS pd ON pd.BaseIsDelete = 0 AND ep.Id = pd.ExperimentPublishId 
									INNER JOIN ex_ExperimentBooking AS eb ON eb.BaseIsDelete = 0 AND ep.Id = eb.ExperimentPublishId	AND pd.Id = eb.TextbookVersionCurrentId	 
                                    INNER JOIN  up_Unit AS u2 ON ep.SchoolId = u2.Id AND u2.BaseIsDelete = 0 AND u2.Statuz = 1		 									  
                                    INNER JOIN  sys_static_dictionary AS sd4 ON ep.CourseId = sd4.DictionaryId 
									INNER JOIN  SysUser AS su5 ON su5.BaseIsDelete =0 AND pd.GuidanceTeacher = su5.Id
									LEFT JOIN SysDataDictDetail AS sdic ON sdic.BaseIsDelete = 0 AND sdic.TypeCode = 'OET101001' ANd pd.SchoolId = sdic.UnitId AND pd.ExperimentType = sdic.DictKey
                                    LEFT JOIN ex_ExperimentRecord AS er	ON er.BaseIsDelete = 0 AND eb.Id = er.ExperimentBookingId AND er.ActivityType = {ActivityTypeEnum.OutOfClass.ParseToInt()}
                          ) as T WHERE  1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = @BaseIsDelete ");
                parameter.Add(DbParameterExtension.CreateDbParameter("@BaseIsDelete", param.BaseIsDelete));
                if (param.UnitId> 0)
                {
                    strSql.Append(" AND SchoolId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }

                if (param.CourseId > 0)
                {
                    strSql.Append(" AND CourseId = @CourseId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }

                if (param.SchoolTerm > 0)
                {
                    strSql.Append(" AND SchoolTerm = @SchoolTerm ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolTerm", param.SchoolTerm));
                }
                if (param.SchoolYearStart > 0)
                {
                    strSql.Append(" AND SchoolYearStart = @SchoolYearStart ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolYearStart", param.SchoolYearStart));
                }
                if (!string.IsNullOrEmpty(param.Name))
                {
                    strSql.Append(" AND ExperimentName like @ExperimentName ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ExperimentName", $"%{param.Name}%"));
                } 

                if (param.Statuz != -10000 && param.Statuz != -1)
                {
                    strSql.Append(" AND Statuz = @Statuz ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Statuz", param.Statuz));
                }
                if (param.Statuzge != -10000 && param.Statuzge != -1)
                {
                    strSql.Append(" AND Statuz >= @Statuzge ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Statuzge", param.Statuzge));
                }
                if (param.ExperimentPublishId > 0)
                {
                    strSql.Append(" AND ExperimentPublishId like @ExperimentPublishId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ExperimentPublishId", param.ExperimentPublishId));
                }
                if (param.BookingId > 0)
                {
                    strSql.Append(" AND BookingId like @BookingId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@BookingId", param.BookingId));
                }
                if (param.UserId > 0)
                {
                    strSql.Append(" AND GuidanceTeacher = @GuidanceTeacher ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@GuidanceTeacher", param.UserId));
                }
            }
            var list = await this.BaseRepository().FindList<PublishDetailEntity>(strSql.ToString(), parameter.ToArray(), pagination);
            return list.ToList();
        }

        /// <summary>
        /// 编制计划实验数量以学校为单位
        /// </summary>
        /// <param name="param"></param>
        /// <param name="strSql"></param>
        /// <returns></returns>
        public async Task<List<PublishDetailEntity>> GetStudentPageList(PublishDetailListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(@$"SELECT * From (SELECT  
                                    pd.Id ,	   
                                    pd.BaseCreateTime ,
                                    pd.BaseCreatorId ,
                                    pd.BaseIsDelete ,
                                    pd.BaseModifierId ,
                                    pd.BaseModifyTime ,
                                    pd.BaseVersion ,
                                    ep.ClassTime ,
                                    ep.CourseId ,
                                    ep.FunRoomId ,
                                    ep.FunRoomName ,
                                    ep.Name ,
                                    ep.Num ,
                                    ep.SchoolId ,
                                    ep.SchoolTerm ,
                                    ep.SchoolYearEnd ,
                                    ep.SchoolYearStart ,
                                    ep.SectionId ,
                                    ep.SectionShow ,
                                   
                                    ep.StudentNumed ,
                                    ep.StudentNumLimit ,
                                    sd4.DicName AS CourseName ,
                       
                                    u2.Name AS SchoolName ,u2.Sort
									,sdic.DictValue AS ExperimentTypeName
								    ,su5.RealName AS GuidanceTeacherName
									,pd.GuidanceTeacher 	
									,pd.ExperimentName	
									,pd.ExperimentPublishId
                                    ,pd.ExperimentId
                                    ,pd.AttendanceNum
									,eb.Statuz
									,eb.Id AS BookingId		
                                    ,ISNULL(er.Id,0) AS RecordId
                                    ,ec.Score
									,ec.AttendanceStatuz
									,ec.Remark	
                                    ,ec.ParentStudentId
									,ec.StudentId
                                    ,ec.UserId
                                    FROM  ex_ExperimentPublish AS ep
									INNER JOIN ex_PublishDetail AS pd ON pd.BaseIsDelete = 0 AND ep.Id = pd.ExperimentPublishId 
									INNER JOIN ex_ExperimentBooking AS eb ON eb.BaseIsDelete = 0 AND ep.Id = eb.ExperimentPublishId	AND pd.Id = eb.TextbookVersionCurrentId	 
									INNER JOIN ex_ExperimentCriticize AS ec ON ep.Id = ec.ExperimentPublishId AND pd.Id = ec.PublishDetailId
                                    INNER JOIN  up_Unit AS u2 ON ep.SchoolId = u2.Id AND u2.BaseIsDelete = 0 AND u2.Statuz = 1		 									  
                                    INNER JOIN  sys_static_dictionary AS sd4 ON ep.CourseId = sd4.DictionaryId 
									INNER JOIN  SysUser AS su5 ON su5.BaseIsDelete =0 AND pd.GuidanceTeacher = su5.Id
									LEFT JOIN SysDataDictDetail AS sdic ON sdic.BaseIsDelete = 0 AND sdic.TypeCode = 'OET101001' ANd pd.SchoolId = sdic.UnitId AND pd.ExperimentType = sdic.DictKey
                                    LEFT JOIN ex_ExperimentRecord AS er	ON er.BaseIsDelete = 0 AND eb.Id = er.ExperimentBookingId AND er.ActivityType = {ActivityTypeEnum.OutOfClass.ParseToInt()}
                          ) as T WHERE  1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = @BaseIsDelete ");
                parameter.Add(DbParameterExtension.CreateDbParameter("@BaseIsDelete", param.BaseIsDelete));
                if (param.UnitId > 0)
                {
                    strSql.Append(" AND SchoolId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }

                if (param.CourseId > 0)
                {
                    strSql.Append(" AND CourseId = @CourseId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }

                if (param.SchoolTerm > 0)
                {
                    strSql.Append(" AND SchoolTerm = @SchoolTerm ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolTerm", param.SchoolTerm));
                }
                if (param.SchoolYearStart > 0)
                {
                    strSql.Append(" AND SchoolYearStart = @SchoolYearStart ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolYearStart", param.SchoolYearStart));
                }
                if (!string.IsNullOrEmpty(param.Name))
                {
                    strSql.Append(" AND ExperimentName like @ExperimentName ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ExperimentName", $"%{param.Name}%"));
                }

                if (param.Statuz != -10000 && param.Statuz != -1)
                {
                    strSql.Append(" AND Statuz = @Statuz ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Statuz", param.Statuz));
                }
                if (param.ExperimentPublishId > 0)
                {
                    strSql.Append(" AND ExperimentPublishId like @ExperimentPublishId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ExperimentPublishId", param.ExperimentPublishId));
                }
                if (param.BookingId > 0)
                {
                    strSql.Append(" AND BookingId like @BookingId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@BookingId", param.BookingId));
                }
                if (param.UserId > 0)
                {
                    strSql.Append(" AND UserId like @UserId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UserId", param.UserId));
                }
            }
            var list = await this.BaseRepository().FindList<PublishDetailEntity>(strSql.ToString(), parameter.ToArray(), pagination);
            return list.ToList();
        }
        #endregion
    }
}
