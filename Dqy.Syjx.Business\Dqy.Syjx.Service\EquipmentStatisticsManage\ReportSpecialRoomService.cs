﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.EquipmentStatisticsManage;
using Dqy.Syjx.Model.Param.EquipmentStatisticsManage;
using Senparc.Weixin.Annotations;
using Dqy.Syjx.Enum;

namespace Dqy.Syjx.Service.EquipmentStatisticsManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2024-01-29 10:56
    /// 描 述：学校上报幼儿园专用室信息服务类
    /// </summary>
    public class ReportSpecialRoomService : RepositoryFactory
    {
        #region 获取数据
        public async Task<List<ReportSpecialRoomEntity>> GetList(ReportSpecialRoomListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<ReportSpecialRoomEntity>> GetPageList(ReportSpecialRoomListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<ReportSpecialRoomEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<ReportSpecialRoomEntity>(id);
        }

        public async Task<List<ReportSpecialRoomEntity>> GetPageList(ReportEquipmentParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ListFilter(param, strSql);
            var list = await this.BaseRepository().FindList<ReportSpecialRoomEntity>(strSql.ToString(), filter.ToArray(), pagination);
            return list.ToList();
        }

        public async Task<List<ReportSpecialRoomEntity>> GetAllunitPageList(ReportEquipmentParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ListAllUnitFilter(param, strSql);
            var list = await this.BaseRepository().FindList<ReportSpecialRoomEntity>(strSql.ToString(), filter.ToArray(), pagination);
            return list.ToList();
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(ReportSpecialRoomEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(ReportSpecialRoomEntity entity, Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update zb_ReportSpecialRoom set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
                strSql = $"update zb_ReportSpecialRoom set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }

        public async Task DeleteTransForm(long id, Repository db)
        {
            string strSql = $"update zb_ReportSpecialRoom set BaseIsDelete = 1 where id = {id}";
            await db.ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        /// <summary>
        /// 
        /// </summary>
        /// <param name="param"></param>
        /// <param name="strSql"></param>
        /// <param name="funSql"></param>
        /// <returns></returns>
        private List<DbParameter> ListFilter(ReportEquipmentParam param, StringBuilder strSql, string funSql = "*")
        {
            strSql.Append($@"  SELECT {funSql} From (
                        SELECT RS.Id,U.Sort,U.Id AS SchoolId,U.Name AS SchoolName,RS.CategoryName,RS.Name,RS.UseArea,
	                           RS.BuildTime,RS.ReformTime,RS.Remark 
                        FROM zb_ReportSpecialRoom AS RS
                        INNER JOIN zb_Report AS R ON RS.ReportId = R.Id AND R.BaseIsDelete = 0
                        INNER JOIN up_Unit AS U ON R.SchoolId = U.Id AND U.BaseIsDelete = 0
                        WHERE R.IsReport = 1 AND R.IsCurrent = 1 AND RS.BaseIsDelete = 0
                        ) as T WHERE 1 = 1 ");

            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.SchoolId > 0)
                {
                    strSql.Append(" AND SchoolId = @SchoolId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
                }
            }
            return parameter;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="param"></param>
        /// <param name="strSql"></param>
        /// <param name="funSql"></param>
        /// <returns></returns>
        private List<DbParameter> ListAllUnitFilter(ReportEquipmentParam param, StringBuilder strSql, string funSql = "*")
        {
            strSql.Append($@"  SELECT {funSql} From (
                       SELECT RS.Id,U.Sort,U.Id AS SchoolId,U.Name AS SchoolName,RS.CategoryName,RS.Name,RS.UseArea,
                        RS.BuildTime,RS.ReformTime,RS.Remark  ,ur.UnitId AS CountyId
                         FROM up_Unit AS U
                         INNER JOIN up_UnitRelation AS ur ON ur.BaseIsDelete = 0 AND U.Id = ur.ExtensionObjId
                         LEFT JOIN zb_Report AS R ON R.BaseIsDelete = 0 AND U.Id = R.SchoolId AND R.IsReport = 1 AND R.IsCurrent = 1
                         LEFT JOIN zb_ReportSpecialRoom AS RS ON  RS.BaseIsDelete = 0 AND R.Id = RS.ReportId
                     WHERE  U.BaseIsDelete = 0 
                        ) as T WHERE 1 = 1 ");

            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.SchoolId > 0)
                {
                    strSql.Append(" AND SchoolId = @SchoolId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
                }
            }
            return parameter;
        }

        public async Task<ReportSpecialRoomEntity> GetSpecialRoomSum(ReportEquipmentParam param)
        {
            StringBuilder sql = new StringBuilder();
            var expression = ListFilter(param, sql, " '<b>总计：<b>' AS SchoolName,ISNULL(SUM(UseArea) ,0) AS UseArea");
            var list = await this.BaseRepository().FindList<ReportSpecialRoomEntity>(sql.ToString(), expression.ToArray());
            return list.FirstOrDefault();
        }

        public async Task<ReportSpecialRoomEntity> GetAllUnitSpecialRoomSum(ReportEquipmentParam param)
        {
            StringBuilder sql = new StringBuilder();
            var expression = ListAllUnitFilter(param, sql, " '<b>总计：<b>' AS SchoolName,ISNULL(SUM(UseArea) ,0) AS UseArea");
            var list = await this.BaseRepository().FindList<ReportSpecialRoomEntity>(sql.ToString(), expression.ToArray());
            return list.FirstOrDefault();
        }
        /// <summary>
        /// 获取幼儿园室统计
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<IEnumerable<ReportSpecialRoomEntity>> GetStatistics(ReportSpecialRoomListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append($@"  SELECT * From (
                        SELECT RS.Id,U.Sort,U.Id AS SchoolId,U.Name AS SchoolName,RS.CategoryId,RS.CategoryName,RS.Name,RS.UseArea,
	                           RS.BuildTime,RS.ReformTime,RS.Remark
                                 ,UR.UnitId AS CountyId
                        FROM zb_ReportSpecialRoom AS RS
                        INNER JOIN zb_Report AS R ON RS.ReportId = R.Id AND R.BaseIsDelete = 0
                        INNER JOIN up_Unit AS U ON R.SchoolId = U.Id AND U.BaseIsDelete = 0
                        INNER JOIN  up_UnitRelation AS UR ON R.SchoolId = UR.ExtensionObjId AND UR.ExtensionType = 3 AND UR.BaseIsDelete = 0
                        WHERE R.IsReport = 1 AND R.IsCurrent = 1 AND RS.BaseIsDelete = 0
                        ) as T WHERE 1 = 1 ");

            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    strSql.Append(" AND SchoolId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                else if (param.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    strSql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.UnitId));
                }
            }
            return await this.BaseRepository().FindList<ReportSpecialRoomEntity>(strSql.ToString(), parameter.ToArray(), pagination);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        private Expression<Func<ReportSpecialRoomEntity, bool>> ListFilter(ReportSpecialRoomListParam param)
        {
            var expression = LinqExtensions.True<ReportSpecialRoomEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (param.ReportId > 0)
                {
                    expression = expression.And(t => t.ReportId == param.ReportId);
                }
            }
            return expression;
        }
        #endregion
    }
}
