﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.BusinessManage;
using Dqy.Syjx.Model.Param.BusinessManage;

namespace Dqy.Syjx.Service.BusinessManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-11-05 10:29
    /// 描 述：服务类
    /// </summary>
    public class CheckRectificationService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<CheckRectificationEntity>> GetList(CheckRectificationListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<CheckRectificationEntity>> GetPageList(CheckRectificationListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<CheckRectificationEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<CheckRectificationEntity>(id);
        }

        /// <summary>
        /// 安全排查统计列表
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        public async Task<List<CheckRectificationEntity>> GetCheckRectificationStatisticsList(CheckRectificationListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            var parameter = new List<DbParameter>();
            //strSql.Append(@"SELECT DictionaryId1006A ,ClassNameA ,DictionaryId1006B ,ClassNameB ,Name ,FunRoomId ,RoomAttribute ,NatureName ,CheckTime ,RectificationTime ,ISNULL(CheckCount,0) CheckCount ,ISNULL(RectificationCount,0) RectificationCount ,SchoolId ,SchoolName ,CountyId FROM (
            //             SELECT A.DictionaryId1006A ,ClassNameA ,A.DictionaryId1006B ,ClassNameB ,A.Name ,A.FunRoomId ,A.RoomAttribute ,A.NatureName
            //             ,MAX(A.CheckTime) AS CheckTime ,MAX(A.RectificationTime) AS RectificationTime ,COUNT(1) AS CheckCount ,SchoolId ,SchoolName ,CountyId From (
            //                 SELECT CR.Id ,
            //                 FR.BaseIsDelete ,
            //                 FR.BaseCreateTime ,
            //                 FR.BaseModifyTime ,
            //                 FR.BaseCreatorId ,
            //                 FR.BaseModifierId ,
            //                 FR.BaseVersion ,
            //                 FR.UnitId ,
            //                 FR.DictionaryId1005 ,
            //                 FR.SchoolStagez ,
            //                 FR.DictionaryId1006A ,
            //                 FR.DictionaryId1006B ,
            //                 FR.Name ,
            //                 FR.UseArea ,
            //                 FR.RoomAttribute ,
            //                 FR.SeatNum ,
            //                 FR.IsDigitalize ,
            //                 FR.SysDepartmentId ,
            //                 FR.SysUserId ,
            //                 FR.BuildTime ,
            //                 FR.ReformTime ,
            //                 FR.Address ,
            //                 FR.Statuz ,
            //                 FR.SafeguardUserId ,
            //                 FR.UploadBriefInfoNum ,
            //                 FR.UploadSystemNum ,
            //                 FR.LaboratoryGroupNum ,
            //           sd1.DicName AS ClassNameA ,
            //           sd2.DicName AS ClassNameB ,
            //                 sd6.DicName AS NatureName ,
            //           sd3.DicName AS SubjectName,
            //           SU1.RealName ,
            //     ISNULL(CR.Id,0) AS CheckRectificationId,
            //     CR.CheckUserId,
            //     CR.CheckTime,CR.CheckResult,CR.ProblemDanger,CR.IsRectification,CR.RectificationTime,CR.RectificationContent,
            //     SU1.RealName AS CheckUserName
            //                 ,U.Name AS SchoolName
            //                 ,U.Id AS SchoolId
            //              ,UR.UnitId AS CountyId
            //                 ,CR.FunRoomId AS FunRoomId
            //           FROM  bn_FunRoom AS FR
            //           INNER JOIN  sys_static_dictionary AS sd1 ON FR.DictionaryId1006A = sd1.DictionaryId AND sd1.BaseIsDelete = 0
            //           INNER JOIN  sys_static_dictionary AS sd2 ON FR.DictionaryId1006B = sd2.DictionaryId AND sd2.BaseIsDelete = 0
            //           INNER JOIN  sys_static_dictionary AS sd3 ON FR.DictionaryId1005 = sd3.DictionaryId AND sd3.BaseIsDelete = 0
            //                 INNER JOIN  sys_static_dictionary AS sd6 ON sd2.Nature = sd6.DictionaryId AND sd6.BaseIsDelete = 0
            //     INNER JOIN  bn_CheckRectification AS CR ON FR.Id = CR.FunRoomId AND CR.BaseIsDelete = 0
            //     INNER JOIN  SysUser AS SU1 ON CR.CheckUserId = SU1.Id AND SU1.BaseIsDelete = 0
            //                 INNER JOIN  up_Unit AS U ON FR.UnitId = U.Id
            //              INNER JOIN  up_UnitRelation AS UR ON U.Id = UR.ExtensionObjId AND UR.ExtensionType = 3
            //            ) as A WHERE 1 = 1");
            strSql.Append(@"
                            SELECT ISNULL(CheckCount,0) CheckCount ,
		                            ISNULL(RectificationCount,0) RectificationCount ,SchoolId ,SchoolName ,CountyId,CheckTime FROM (
                            SELECT MAX(A.CheckTime) AS CheckTime ,MAX(A.RectificationTime) AS RectificationTime ,COUNT(1) AS CheckCount ,SchoolId ,SchoolName ,CountyId
                            From (
                                SELECT CR.Id ,SU1.RealName ,
	                            ISNULL(CR.Id,0) AS CheckRectificationId,
	                            CR.CheckUserId,
	                            CR.CheckTime,CR.CheckResult,CR.ProblemDanger,CR.IsRectification,CR.RectificationTime,CR.RectificationContent,
	                            SU1.RealName AS CheckUserName
                                ,U.Name AS SchoolName
                                ,U.Id AS SchoolId
	                            ,UR.UnitId AS CountyId
                                ,CR.FunRoomId AS FunRoomId
                            FROM  bn_CheckRectification AS CR
                            INNER JOIN  SysUser AS SU1 ON CR.CheckUserId = SU1.Id AND SU1.BaseIsDelete = 0
                            INNER JOIN  up_Unit AS U ON CR.UnitId = U.Id
                            INNER JOIN  up_UnitRelation AS UR ON U.Id = UR.ExtensionObjId AND UR.ExtensionType = 3
                            ) as A WHERE 1 = 1 ");
            if (param != null)
            {

                if (param.UnitId > 0)
                {
                    strSql.Append(" AND SchoolId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));

                }
                if (param.CountyId > 0)
                {
                    strSql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
                if (!string.IsNullOrEmpty(param.StartTime.ParseToString()))
                {
                    strSql.Append(" AND CheckTime >= @StartTime ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@StartTime", param.StartTime));
                }
                if (!string.IsNullOrEmpty(param.EndTime.ParseToString()))
                {
                    param.EndTime = param.EndTime.Value.Date.Add(new TimeSpan(23, 59, 59));
                    strSql.Append(" AND CheckTime <= @EndTime ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@EndTime", param.EndTime));
                }

                //if (param.DictionaryId1006A > 0)
                //{
                //    strSql.Append(" AND DictionaryId1006A = @DictionaryId1006A ");
                //    parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId1006A", param.DictionaryId1006A));
                //}
                //if (param.DictionaryId1006B > 0)
                //{
                //    strSql.Append(" AND DictionaryId1006B = @DictionaryId1006B ");
                //    parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId1006B", param.DictionaryId1006B));
                //}
                //if (param.RoomAttribute > 0)
                //{
                //    strSql.Append(" AND RoomAttribute = @RoomAttribute ");
                //    parameter.Add(DbParameterExtension.CreateDbParameter("@RoomAttribute", param.RoomAttribute));
                //}
                if (param.CheckUserId > 0)
                {
                    strSql.Append(" AND CheckUserId = @CheckUserId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CheckUserId", param.CheckUserId));
                }
                if (param.CheckResult > 0)
                {
                    strSql.Append(" AND CheckResult = @CheckResult ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CheckResult", param.CheckResult));
                }
                if (param.IsRectification > 0)
                {
                    strSql.Append(" AND IsRectification = @IsRectification ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@IsRectification", param.IsRectification));
                }
                //if (!string.IsNullOrEmpty(param.Name))
                //{
                //    strSql.Append(" AND Name like @Name");
                //    parameter.Add(DbParameterExtension.CreateDbParameter("@Name", $"%{param.Name}%"));
                //}
            }
            //strSql.Append(" GROUP BY  A.DictionaryId1006A ,A.ClassNameA ,A.DictionaryId1006B ,A.ClassNameB ,A.Name ,A.RoomAttribute ,A.NatureName ,A.FunRoomId ,A.SchoolId ,A.SchoolName ,A.CountyId ");
            //strSql.Append(" ) t1");
            //strSql.Append(" LEFT JOIN");
            //strSql.Append(" ( SELECT FunRoomId AS FunRoomId2 ,COUNT (1) AS RectificationCount FROM  bn_CheckRectification WHERE IsRectification = 1 AND BaseIsDelete = 0 Group BY FunRoomId) t2");
            //strSql.Append(" ON t1.FunRoomId = t2.FunRoomId2 ");

            strSql.Append(@"GROUP BY A.SchoolId ,A.SchoolName ,A.CountyId  ) t1
                LEFT JOIN ( SELECT UnitId,COUNT (1) AS RectificationCount FROM  bn_CheckRectification
                WHERE IsRectification = 1  GROUP BY UnitId) t2 ON t1.SchoolId = t2.UnitId");

            var list = await this.BaseRepository().FindList<CheckRectificationEntity>(strSql.ToString(), parameter.ToArray(), pagination);
            return list.ToList();
        }

        /// <summary>
        /// 填报正常30以后不允许修改
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task<List<CheckRectificationEntity>> CheckNormalNoEdit(long schoolId,string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            StringBuilder strSql = new StringBuilder();
            strSql.Append(@$"SELECT FR.Name
                            FROM  bn_FunRoom AS FR                            
                            INNER JOIN  bn_CheckRectification AS CR ON FR.Id = CR.FunRoomId AND CR.BaseIsDelete = 0
                            WHERE CR.Id IN ({ids}) AND CR.CheckResult = 1 AND CR.UnitId = {schoolId} AND DATEADD(DAY,30,CR.BaseCreateTime) < GETDATE()");

            var list = await this.BaseRepository().FindList<CheckRectificationEntity>(strSql.ToString());
            return list.ToList();
        }

        /// <summary>
        /// 填报不正常，整改后30天后不允许修改
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task<List<CheckRectificationEntity>> CheckRectificationNoEdit(long schoolId, string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            StringBuilder strSql = new StringBuilder();
            strSql.Append(@$"SELECT FR.Name
                            FROM  bn_FunRoom AS FR
                            --INNER JOIN  sys_static_dictionary AS sd1 ON FR.DictionaryId1006A = sd1.DictionaryId AND sd1.BaseIsDelete = 0
                            --INNER JOIN  sys_static_dictionary AS sd2 ON FR.DictionaryId1006B = sd2.DictionaryId AND sd2.BaseIsDelete = 0
                            --INNER JOIN  sys_static_dictionary AS sd3 ON FR.DictionaryId1005 = sd3.DictionaryId AND sd3.BaseIsDelete = 0
                            --INNER JOIN  sys_static_dictionary AS sd6 ON sd2.Nature = sd6.DictionaryId AND sd6.BaseIsDelete = 0
                            INNER JOIN  bn_CheckRectification AS CR ON FR.Id = CR.FunRoomId AND CR.BaseIsDelete = 0
                            WHERE CR.Id IN ({ids}) AND CR.CheckResult = 2 AND CR.UnitId = {schoolId} AND CR.IsRectification = 1 AND DATEADD(DAY,30,CR.RectificationTime) < GETDATE()");

            var list = await this.BaseRepository().FindList<CheckRectificationEntity>(strSql.ToString());
            return list.ToList();
        }


        /// <summary>
        /// 获取安全排查与整改列表
        /// </summary>
        /// <param name="param">查询参数</param>
        /// <param name="pagination">分页参数</param>
        /// <returns></returns>
        public async Task<List<CheckRectificationEntity>> GetCheckRectificationList(CheckRectificationListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            var parameter = new List<DbParameter>();
            //       strSql.Append(@"SELECT * From (
            //            SELECT CR.Id ,
            //            FR.BaseIsDelete ,
            //            FR.BaseCreateTime ,
            //            FR.BaseModifyTime ,
            //            FR.BaseCreatorId ,
            //            FR.BaseModifierId ,
            //            FR.BaseVersion ,
            //            FR.UnitId ,
            //            FR.DictionaryId1005 ,
            //            FR.SchoolStagez ,
            //            FR.DictionaryId1006A ,
            //            FR.DictionaryId1006B ,
            //            FR.Name ,
            //            FR.UseArea ,
            //            FR.RoomAttribute ,
            //            FR.SeatNum ,
            //            FR.IsDigitalize ,
            //            FR.SysDepartmentId ,
            //            FR.SysUserId ,
            //            FR.BuildTime ,
            //            FR.ReformTime ,
            //            FR.Address ,
            //            FR.Statuz ,
            //            FR.SafeguardUserId ,
            //            FR.UploadBriefInfoNum ,
            //            FR.UploadSystemNum ,
            //            FR.LaboratoryGroupNum ,
            //      sd1.DicName AS ClassNameA ,
            //      sd2.DicName AS ClassNameB ,
            //            sd6.DicName AS NatureName ,
            //      sd3.DicName AS SubjectName,
            //      SU1.RealName ,
            //ISNULL(CR.Id,0) AS CheckRectificationId,
            //CR.CheckUserId,
            //CR.CheckTime,CR.CheckResult,CR.ProblemDanger,CR.IsRectification,CR.RectificationTime,CR.RectificationContent,
            //SU1.RealName AS CheckUserName
            //            ,U.Name AS SchoolName
            //         ,UR.UnitId AS CountyId
            //      FROM  bn_FunRoom AS FR
            //      INNER JOIN  sys_static_dictionary AS sd1 ON FR.DictionaryId1006A = sd1.DictionaryId AND sd1.BaseIsDelete = 0
            //      INNER JOIN  sys_static_dictionary AS sd2 ON FR.DictionaryId1006B = sd2.DictionaryId AND sd2.BaseIsDelete = 0
            //      INNER JOIN  sys_static_dictionary AS sd3 ON FR.DictionaryId1005 = sd3.DictionaryId AND sd3.BaseIsDelete = 0
            //            INNER JOIN  sys_static_dictionary AS sd6 ON sd2.Nature = sd6.DictionaryId AND sd6.BaseIsDelete = 0
            //INNER JOIN  bn_CheckRectification AS CR ON FR.Id = CR.FunRoomId AND CR.BaseIsDelete = 0
            //INNER JOIN  SysUser AS SU1 ON CR.CheckUserId = SU1.Id AND SU1.BaseIsDelete = 0
            //            INNER JOIN  up_Unit AS U ON FR.UnitId = U.Id
            //         INNER JOIN  up_UnitRelation AS UR ON U.Id = UR.ExtensionObjId AND UR.ExtensionType = 3
            //       ) as A WHERE 1 = 1");
            strSql.Append(@"SELECT * From (
                 SELECT CR.Id ,CR.BaseIsDelete,
		         SU1.RealName ,
				 ISNULL(CR.Id,0) AS CheckRectificationId,
				 CR.CheckUserId,
				 CR.CheckTime,CR.CheckResult,CR.ProblemDanger,CR.IsRectification,CR.RectificationTime,CR.RectificationContent,
				 SU1.RealName AS CheckUserName
                 ,U.Name AS SchoolName
	             ,UR.UnitId AS CountyId,
				 CR.UnitId
		         FROM  bn_CheckRectification AS CR
				 INNER JOIN  SysUser AS SU1 ON CR.CheckUserId = SU1.Id AND SU1.BaseIsDelete = 0
                 INNER JOIN  up_Unit AS U ON CR.UnitId = U.Id
	             INNER JOIN  up_UnitRelation AS UR ON U.Id = UR.ExtensionObjId AND UR.ExtensionType = 3
            ) as A WHERE 1 = 1 ");
            if (param != null)
            {

                if (param.UnitId > 0)
                {
                    strSql.Append(" AND UnitId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));

                }
                if (param.CountyId > 0)
                {
                    strSql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
                if (!string.IsNullOrEmpty(param.StartTime.ParseToString()))
                {
                    strSql.Append(" AND CheckTime >= @StartTime ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@StartTime", param.StartTime));
                }
                if (!string.IsNullOrEmpty(param.EndTime.ParseToString()))
                {
                    param.EndTime = param.EndTime.Value.Date.Add(new TimeSpan(23, 59, 59));
                    strSql.Append(" AND CheckTime <= @EndTime ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@EndTime", param.EndTime));
                }

                if (param.DictionaryId1006A > 0)
                {
                    strSql.Append(" AND DictionaryId1006A = @DictionaryId1006A ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId1006A", param.DictionaryId1006A));
                }
                if (param.DictionaryId1006B > 0)
                {
                    strSql.Append(" AND DictionaryId1006B = @DictionaryId1006B ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId1006B", param.DictionaryId1006B));
                }
                if (param.RoomAttribute > 0)
                {
                    strSql.Append(" AND RoomAttribute = @RoomAttribute ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@RoomAttribute", param.RoomAttribute));
                }
                if(param.CheckUserId > 0)
                {
                    strSql.Append(" AND CheckUserId = @CheckUserId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CheckUserId", param.CheckUserId));
                }
                if (param.CheckResult > 0)
                {
                    strSql.Append(" AND CheckResult = @CheckResult ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CheckResult", param.CheckResult));
                }
                if (param.IsRectification > 0)
                {
                    strSql.Append(" AND IsRectification = @IsRectification ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@IsRectification", param.IsRectification));
                }
                //if (!string.IsNullOrEmpty(param.Name))
                //{
                //    strSql.Append(" AND Name like @Name");
                //    parameter.Add(DbParameterExtension.CreateDbParameter("@Name", $"%{param.Name}%"));
                //}
            }
            var list = await this.BaseRepository().FindList<CheckRectificationEntity>(strSql.ToString(), parameter.ToArray(), pagination);
            return list.ToList();
        }

        /// <summary>
        /// 获取安全排查信息详情
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<CheckRectificationEntity> GetCheckRectificationById(long id)
        {
            StringBuilder strSql = new StringBuilder();
            var parameter = new List<DbParameter>();
            strSql.Append(@"
                 SELECT CR.Id ,
                 FR.BaseIsDelete ,
                 FR.BaseCreateTime ,
                 FR.BaseModifyTime ,
                 FR.BaseCreatorId ,
                 FR.BaseModifierId ,
                 FR.BaseVersion ,
                 FR.UnitId ,
                 FR.DictionaryId1005 ,
                 FR.SchoolStagez ,
                 FR.DictionaryId1006A ,
                 FR.DictionaryId1006B ,
                 FR.Name ,
                 FR.UseArea ,
                 FR.RoomAttribute ,
                 FR.SeatNum ,
                 FR.IsDigitalize ,
                 FR.SysDepartmentId ,
                 FR.SysUserId ,
                 FR.BuildTime ,
                 FR.ReformTime ,
                 FR.Address ,
                 FR.Statuz ,
                 FR.SafeguardUserId ,
                 FR.UploadBriefInfoNum ,
                 FR.UploadSystemNum ,
                 FR.LaboratoryGroupNum ,
		         sd1.DicName AS ClassNameA ,
		         sd2.DicName AS ClassNameB ,
                 sd6.DicName AS NatureName ,
		         sd3.DicName AS SubjectName,
		         SU.RealName ,
				 ISNULL(CR.Id,0) AS CheckRectificationId,
				 CR.CheckUserId,
				 CR.CheckTime,CR.CheckResult,CR.ProblemDanger,CR.IsRectification,CR.RectificationTime,CR.RectificationContent,
				 SU1.RealName AS CheckUserName
                 ,U.Name AS SchoolName
	             ,UR.UnitId AS CountyId
		         FROM  bn_FunRoom AS FR
		         INNER JOIN  sys_static_dictionary AS sd1 ON FR.DictionaryId1006A = sd1.DictionaryId AND sd1.BaseIsDelete = 0
		         INNER JOIN  sys_static_dictionary AS sd2 ON FR.DictionaryId1006B = sd2.DictionaryId AND sd2.BaseIsDelete = 0
		         INNER JOIN  sys_static_dictionary AS sd3 ON FR.DictionaryId1005 = sd3.DictionaryId AND sd3.BaseIsDelete = 0
                 INNER JOIN  sys_static_dictionary AS sd6 ON sd2.Nature = sd6.DictionaryId AND sd6.BaseIsDelete = 0
		         LEFT JOIN  SysUser AS SU ON FR.SysUserId = SU.Id AND SU.BaseIsDelete = 0
				 INNER JOIN  bn_CheckRectification AS CR ON FR.Id = CR.FunRoomId AND CR.BaseIsDelete = 0
				 LEFT JOIN  SysUser AS SU1 ON CR.CheckUserId = SU1.Id AND SU1.BaseIsDelete = 0
                 INNER JOIN  up_Unit AS U ON FR.UnitId = U.Id
	             INNER JOIN  up_UnitRelation AS UR ON U.Id = UR.ExtensionObjId AND UR.ExtensionType = 3 ");
            strSql.AppendFormat(" WHERE CR.Id = {0}", id);
            var list = await this.BaseRepository().FindList<CheckRectificationEntity>(strSql.ToString());
            return list.FirstOrDefault();
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(CheckRectificationEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(CheckRectificationEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
               strSql = $" update bn_CheckRectification set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
               strSql = $"update bn_CheckRectification set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<CheckRectificationEntity, bool>> ListFilter(CheckRectificationListParam param)
        {
            var expression = LinqExtensions.True<CheckRectificationEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
            }
            return expression;
        }
        #endregion
    }
}
