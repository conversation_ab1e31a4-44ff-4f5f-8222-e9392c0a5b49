﻿using System;
using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations.Schema;
using Dqy.Syjx.Util;
using System.ComponentModel;
using Dqy.Syjx.Util.Extension;
using NPOI.SS.UserModel;

namespace Dqy.Syjx.Entity.InstrumentManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-09-27 17:37
    /// 描 述：实体类
    /// </summary>
    [Table("eq_PurchaseDeclaration")]
    public class PurchaseDeclarationEntity : BaseExtensionEntity
    {
        /// <summary>
        /// 采购年度
        /// </summary>
        /// <returns></returns>
        [Description("采购年度")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 10)]
        public int? PurchaseYear { get; set; }
        /// <summary>
        /// 仪器分类Id
        /// </summary>
        /// <returns></returns>
        [JsonConverter(typeof(StringJsonConverter))]
        public long? InstrumentStandardId { get; set; }
        /// <summary>
        /// 仪器名称
        /// </summary>
        /// <returns></returns>
        [Description("仪器名称")]
        [ExportExcelAttribute(HorizontalAlignment.Left, 20)]
        public string Name { get; set; }
        /// <summary>
        /// 规格属性Id
        /// </summary>
        [JsonConverter(typeof(StringJsonConverter))]
        public long? ModelStandardId { get; set; }
        /// <summary>
        /// 规格属性
        /// </summary>
        /// <returns></returns>
        [Description("规格属性")]
        [ExportExcelAttribute(HorizontalAlignment.Left ,25)]
        public string Model { get; set; }
        /// <summary>
        /// 计量单位
        /// </summary>
        /// <returns></returns>
        [Description("单位")]
        [ExportExcelAttribute(HorizontalAlignment.Center ,8)]
        public string UnitName { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        /// <returns></returns>
        [Description("数量")]
        [ExportExcelAttribute(HorizontalAlignment.Center ,10)]
        public decimal? Num { get; set; }
        /// <summary>
        /// 单价
        /// </summary>
        /// <returns></returns>
        [Description("单价")]
        [ExportExcelAttribute(HorizontalAlignment.Right ,10)]
        public decimal? Price { get; set; }
        /// <summary>
        /// 适用学段
        /// </summary>
        /// <returns></returns>
        [Description("适用学段")]
        [ExportExcelAttribute(HorizontalAlignment.Center ,10)]
        public string Stage { get; set; }
        /// <summary>
        /// 适用学段Id
        /// </summary>
        /// <returns></returns>
        public int? StageId { get; set; }
        /// <summary>
        /// 适用学科
        /// </summary>
        /// <returns></returns>
        [Description("适用学科")]
        [ExportExcelAttribute(HorizontalAlignment.Center ,10)]
        public string Course { get; set; }
        /// <summary>
        /// 适用学科Id
        /// </summary>
        /// <returns></returns>
        public int? CourseId { get; set; }
        /// <summary>
        /// 采购理由
        /// </summary>
        /// <returns></returns>
        public string Reason { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        /// <returns></returns>
        public int? Statuz { get; set; }
        /// <summary>
        /// 是否为退回状态
        /// </summary>
        public int? IsGoBack { get; set; }
        /// <summary>
        /// 学校Id
        /// </summary>
        /// <returns></returns>
        [JsonConverter(typeof(StringJsonConverter))]
        public long? SchoolId { get; set; }
        /// <summary>
        /// 采购方式
        /// </summary>
        public int PurchaseType { get; set; }

        /// <summary>
        /// 仪器类别id
        /// </summary>
        [NotMapped]
        public long? InstrumentClassId { get; set; }

        /// <summary>
        /// 仪器类别名称
        /// </summary>
        [NotMapped]
        [Description("仪器类别")]
        public string InstrumentClassName { get; set; }

        /// <summary>
        /// 总价
        /// </summary>
        [NotMapped]
        [Description("金额")]
        [ExportExcelAttribute(HorizontalAlignment.Right ,12)]
        public decimal? AmountSum { get; set; }

        /// <summary>
        /// 填报人
        /// </summary>
        [NotMapped]
        [Description("填报人")]
        [ExportExcelAttribute(HorizontalAlignment.Center ,10)]
        public string UserName { get; set; }

        /// <summary>
        /// 审批不通过原因
        /// </summary>
        [NotMapped]
        public string ApprovalRemark { get; set; }

        /// <summary>
        /// 状态描述
        /// </summary>
        [NotMapped]
        public string StatuzDesc { get; set; }

        /// <summary>
        /// 审批日期
        /// </summary>
        [JsonConverter(typeof(DateTimeJsonConverter))]
        [NotMapped]
        public DateTime? ApprovalDate { get; set; }


        [Description("分类代码")]
        [ExportExcelAttribute(HorizontalAlignment.Left, 25)]
        public string Code { get; set; }

        /// <summary>
        /// 学校名称
        /// </summary>
        [NotMapped]
        [Description("学校名称")]
        [ExportExcelAttribute(HorizontalAlignment.Left, 25)]
        public string SchoolName { get; set; }

        /// <summary>
        /// 是否危化品
        /// </summary>
        [NotMapped]
        public int? IsDangerChemical { get; set; }

        /// <summary>
        /// 区县对应区域名称
        /// </summary>
        [NotMapped]
        public string AreaName { get; set; }

        /// <summary>
        /// 区县Id
        /// </summary>
        [NotMapped]
        [JsonConverter(typeof(StringJsonConverter))]
        public long? CountyId { get; set; }

        /// <summary>
        /// 年度采购学生平均金额
        /// </summary>
        [NotMapped]
        public decimal? StudentAvgAmount { get; set; }

        /// <summary>
        /// 年度采购学生平均金额差额
        /// </summary>
        [NotMapped]
        public decimal? StudentDiffMount { get; set; }

        /// <summary>
        /// 品种属性
        /// </summary>
        [NotMapped]
        public string VarietyAttribute { get; set; }

        /// <summary>
        /// 规格代码
        /// </summary>
        [NotMapped]
        public string LastCode { get; set; }

        /// <summary>
        /// 录入方式
        /// </summary>
        public int EntryType { get; set; }

        /// <summary>
        /// 配备要求（1：必配 2：选配）
        /// </summary>
        [NotMapped]
        [Description("配置要求")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 20)]
        public string AllocateType { get; set; }

        /// <summary>
        /// 原始规格编码
        /// </summary>
        [Description("分类代码")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 15)]
        public string OriginalCode { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class SchoolGradeDto
    {
        /// <summary>
        /// 区县Id
        /// </summary>
        public long CountyId { get; set; }
        
        /// <summary>
        /// 市级Id
        /// </summary>
        public long CityId { get; set; }
        
        /// <summary>
        /// 学段
        /// </summary>
        public int SchoolStage { get; set; }

        /// <summary>
        /// 学生数量
        /// </summary>
        public int StudentNum { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class ConfigSetDto
    {
        /// <summary>
        /// 编码
        /// </summary>
        public string TypeCode { get; set; }

        /// <summary>
        /// 配置值
        /// </summary>
        public string ConfigValue { get; set; }
    }
}
