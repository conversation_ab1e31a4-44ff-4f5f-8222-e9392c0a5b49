﻿using Dqy.Syjx.Business.BusinessManage;
using Dqy.Syjx.Business.EvaluateManage;
using Dqy.Syjx.Business.InstrumentManage;
using Dqy.Syjx.Business.OrganizationManage;
using Dqy.Syjx.Business.SystemManage;
using Dqy.Syjx.Entity.BusinessManage;
using Dqy.Syjx.Entity.EvaluateManage;
using Dqy.Syjx.Entity.InstrumentManage;
using Dqy.Syjx.Entity.OrganizationManage;
using Dqy.Syjx.Entity.SystemManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Enum.InstrumentManage;
using Dqy.Syjx.Enum.SystemManage;
using Dqy.Syjx.Model.Input;
using Dqy.Syjx.Model.Input.InstrumentManage;
using Dqy.Syjx.Model.Param;
using Dqy.Syjx.Model.Param.BusinessManage;
using Dqy.Syjx.Model.Param.EvaluateManage;
using Dqy.Syjx.Model.Param.InstrumentManage;
using Dqy.Syjx.Model.Param.OrganizationManage;
using Dqy.Syjx.Model.Param.SystemManage;
using Dqy.Syjx.Model.Result;
using Dqy.Syjx.Model.Result.InstrumentManage;
using Dqy.Syjx.Service.BusinessManage;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Web.Code;
using Dqy.Syjx.Web.CommonLib;
using Dqy.Syjx.Web.Controllers;
using Microsoft.AspNetCore.Mvc;
using NPOI.HSSF.UserModel;
using NPOI.SS.Formula.Functions;
using NPOI.SS.UserModel;
using NPOI.SS.Util;
using NPOI.XSSF.UserModel;
using Senparc.Weixin.WxOpen.AdvancedAPIs.WxApp.WxAppJson;
using System;
using System.Collections.Generic;
using System.Diagnostics.Metrics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;


namespace Dqy.Syjx.Web.Areas.InstrumentManage.Controllers
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-11-01 15:39
    /// 描 述：控制器类
    /// </summary>

    [RequestFormLimits(ValueCountLimit = 500000)]
    [Area("InstrumentManage")]
    public class SchoolInstrumentController :  BaseController
    {
        private SchoolInstrumentBLL schoolInstrumentBLL = new SchoolInstrumentBLL();
        private UserSchoolStageSubjectBLL userSchoolStageSubjectBLL = new UserSchoolStageSubjectBLL();
        private InstrumentEvaluateProjectVersionBLL instrumentEvaluateProjectVersionBLL = new InstrumentEvaluateProjectVersionBLL();
        private ConfigSetBLL configSetBLL = new ConfigSetBLL();
        private UnitRelationBLL unitRelationBLL = new UnitRelationBLL();
        private InstrumentStandardBLL instrumentStandardBLL = new InstrumentStandardBLL();
        private UnitBLL unitBLL = new UnitBLL();
        private InstrumentAttendStaticBLL instrumentAttendStaticBLL = new InstrumentAttendStaticBLL();

        #region 视图功能
        [AuthorizeFilter("instrument:schoolinstrument:view")]
        public ActionResult SchoolInstrumentIndex()
        {
            return View();
        }

        [AuthorizeFilter("instrument:schoolinstrument:view")]
        public ActionResult SchoolInstrumentForm()
        {
            return View();
        }

        public ActionResult LookDetailForm()
        {
            return View();
        }

        /// <summary>
        /// 批量设置适用学科页面
        /// </summary>
        /// <returns></returns>
        public ActionResult BatchEditCourseForm()
        {
            return View();
        }

        /// <summary>
        /// 单条入室或修改
        /// </summary>
        /// <returns></returns>
        public ActionResult SingleInputRoomForm()
        {
            return View();
        }

        /// <summary>
        /// 批量入室
        /// </summary>
        /// <returns></returns>
        public ActionResult BatchInputRoomForm()
        {
            return View();
        }

        /// <summary>
        /// 仪器录入列表
        /// </summary>
        /// <returns></returns>
        public async Task<ActionResult> InstrumentInput()
        {

            int isAllowEditName = 1; //是否允许学校自定义仪器名称
            OperatorInfo operatorinfo = await Operator.Instance.Current();
            if (operatorinfo != null)
            {
                TData<ConfigSetEntity> configObj = await configSetBLL.GetEntityByCode(new ConfigSetListParam
                {
                    UnitId = operatorinfo.UnitId.Value,
                    UnitType = (UnitTypeEnum)operatorinfo.UnitType,
                    ConfigUnitType = UnitTypeEnum.County,
                    TypeCode = "1002_SFXXZDYYQMC"
                });

                if (configObj.Tag == 1 && configObj.Data != null)
                {
                    int.TryParse(configObj.Data.ConfigValue, out isAllowEditName);
                }
            }

            ViewBag.IsAllowEditModel = isAllowEditName;
            return View();
        }

        /// <summary>
        /// 选择标准库仪器
        /// </summary>
        /// <returns></returns>
        public ActionResult SelectStandard() 
        {
            return View();
        }
        
        /// <summary>
        /// 自定义导入
        /// </summary>
        /// <returns></returns>
        public ActionResult BatchImportStandard()
        {
            return View();
        }

        /// <summary>
        /// 按配置标准导入
        /// </summary>
        /// <returns></returns>
        public ActionResult StandardImport()
        {
            return View();
        }

        /// <summary>
        /// 制表设置
        /// </summary>
        /// <returns></returns>
        public async Task<ActionResult> TabulationSet()
        {
            OperatorInfo operatorInfo = await Operator.Instance.Current();
            if (operatorInfo != null)
            {
                ViewBag.UserInfo = operatorInfo;
            }
            return View();
        }

        /// <summary>
        /// 仪器录入编辑页面
        /// </summary>
        /// <returns></returns>
        public async Task<ActionResult> InstrumentForm()
        {

            int isAllowEditName = 1; //是否允许学校自定义仪器名称
            OperatorInfo operatorinfo = await Operator.Instance.Current();
            if (operatorinfo != null)
            {
                TData<ConfigSetEntity> configObj = await configSetBLL.GetEntityByCode(new ConfigSetListParam
                {
                    UnitId = operatorinfo.UnitId.Value,
                    UnitType = (UnitTypeEnum)operatorinfo.UnitType,
                    ConfigUnitType = UnitTypeEnum.County,
                    TypeCode = "1002_SFXXZDYYQMC"
                });

                if (configObj.Tag == 1 && configObj.Data != null)
                {
                    int.TryParse(configObj.Data.ConfigValue, out isAllowEditName);
                }
            }

            ViewBag.IsAllowEditModel = isAllowEditName;
            return View();
        }

        /// <summary>
        /// 信息修改列表页面
        /// </summary>
        /// <returns></returns>
        [AuthorizeFilter("instrument:schoolinstrument:view")]
        public async Task<ActionResult> EditInfoIndex()
        {
            OperatorInfo operatorInfo = await Operator.Instance.Current();
            int isSystem = 0;
            if (operatorInfo != null)
            {
                isSystem = operatorInfo.IsSystem ?? 0;
                string ConfigManagerId = (RoleEnum.ConfigManager.ParseToInt() + 385804331967844300).ToString(); //配置超管角色 
                //验证只有超管，配置超管才能查看
                if (operatorInfo.RoleIds.Contains(ConfigManagerId) || operatorInfo.UnitType == 0)
                {
                    isSystem = 1;
                }

                ViewBag.IsSystem = isSystem;
            }
            return View();
        }

        /// <summary>
        /// 二维码页面
        /// </summary>
        /// <returns></returns>
        public ActionResult QRCodeForm()
        {
            return View();
        }
        #endregion

        #region 获取数据
        [HttpGet]
        [AuthorizeFilter("instrument:schoolinstrument:search")]
        public async Task<ActionResult> GetListJson(SchoolInstrumentListParam param)
        {
            TData<List<SchoolInstrumentEntity>> obj = await schoolInstrumentBLL.GetList(param);
            return Json(obj);
        }

        [HttpGet]
        [AuthorizeFilter("instrument:schoolinstrument:search")]
        public async Task<ActionResult> GetPageListJson(SchoolInstrumentListParam param, Pagination pagination)
        {
            OperatorInfo operatorInfo = await Operator.Instance.Current();
            TData<List<SchoolInstrumentEntity>> obj = new TData<List<SchoolInstrumentEntity>>();
            if (operatorInfo != null)
            {
                param.UnitId = operatorInfo.UnitId ?? 0;
                if (param.ListType == 1)
                {
                    //查询当前用户所管理学段学科所对应的仪器
                    //var schoolStageData = await userSchoolStageSubjectBLL.GetList(new UserSchoolStageSubjectListParam
                    //{
                    //    UserId = operatorInfo.UserId ?? 0
                    //});
                    //if (schoolStageData.Tag == 1 && schoolStageData.Data.Count > 0)
                    //{
                    //    param.SchoolStageIdz = schoolStageData.Data[0].SchoolStageIdz;
                    //    param.SubjectIdz = schoolStageData.Data[0].SubjectIdz;
                    //}

                    //根据功能室管理人
                    param.SafeguardUserId = operatorInfo.UserId;

                    string ConfigManagerId = (RoleEnum.ConfigManager.ParseToInt() + 385804331967844300).ToString(); //配置超管  
                    if (operatorInfo.IsSystem == 1 || operatorInfo.RoleIds.Contains(ConfigManagerId) || operatorInfo.UnitType == 0) //系统管理员、配置超管查看全部仪器
                    {
                        param.UnitId = 0;
                        param.SafeguardUserId = 0;
                    }
                }
                else if (param.ListType == 2)
                {
                    //查询当前用户创建的仪器
                    param.UserId = operatorInfo.UserId;
                }
                if (param.Statuz <= 0)
                {
                    param.Statuz = -2;
                }
                
                obj = await schoolInstrumentBLL.GetPageList(param, pagination);
            }
            return Json(obj);
        }

        [HttpGet]
        public async Task<ActionResult> GetFormJson(long id)
        {
            TData<SchoolInstrumentEntity> obj = await schoolInstrumentBLL.GetEntity(id);
            return Json(obj);
        }

        /// <summary>
        /// 获取仪器达标标准库下载列表
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        [HttpGet]
        [AuthorizeFilter("instrument:schoolinstrument:view")]
        public async Task<ActionResult> GetInstrumentEvProjectDownloadList(InstrumentEvaluateProjectVersionListParam param)
        {
            TData<List<InstrumentEvaluateProjectVersionEntity>> obj = new TData<List<InstrumentEvaluateProjectVersionEntity>>();
            //获取个人管理的学段、学科
            OperatorInfo operatorinfo = await Operator.Instance.Current();
            if (operatorinfo != null)
            {
                List<UserSchoolStageSubjectEntity> powerList = (await userSchoolStageSubjectBLL.GetList(new UserSchoolStageSubjectListParam { UserId = operatorinfo.UserId ?? 0 })).Data;
                if (powerList.Count > 0)
                {
                    List<int> listSchoolStageId = powerList.ToList().Select(f => f.SchoolStageIdz).Distinct().ToList();
                    List<int> listSubjectId = powerList.ToList().Select(f => f.SubjectIdz).ToList();

                    //UserSchoolStageSubjectEntity entity = powerList.FirstOrDefault();
                    obj = await instrumentEvaluateProjectVersionBLL.GetPageList(new InstrumentEvaluateProjectVersionListParam
                    {
                        //SchoolStageList = TextHelper.SplitToArray<int>(entity.SchoolStageIdz, ',').ToList(),
                        //SubjectIdList = TextHelper.SplitToArray<int>(entity.SubjectIdz, ',').ToList() ,
                        SchoolStageList = listSchoolStageId,
                        SubjectIdList = listSubjectId,
                        BaseIsDelete = IsEnum.No.ParseToInt()
                    }, new Pagination { PageSize = int.MaxValue });
                }
            }
            return Json(obj);
        }

        /// <summary>
        /// 获取仪器录入配置信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AuthorizeFilter("instrument:schoolinstrument:search")]
        public async Task<ActionResult> GetNeedValidateJson()
        {
            InstrumentInputSetModel inputModel = new InstrumentInputSetModel();
            TData<InstrumentInputSetModel> obj = new TData<InstrumentInputSetModel>();

            ConfigSetListParam param = new ConfigSetListParam();
            param.TypeCode = "1SYYQ-2YQRK-1YQLR";
            param.ModuleType = ConfigSetModuleTypeEnum.FormRequired.ParseToInt();
            TData<List<ConfigSetEntity>> configObj = await configSetBLL.GetListByCode(param);
            if (configObj.Tag == 1 && configObj.Data.Count > 0)
            {
                InstrumentInputSetModel configSet = new InstrumentInputSetModel();
                obj.Tag = 1;
                obj.Message = "";
                //判断供应商是否必填
                var listSupplier = configObj.Data.Where(a => a.TypeCode.Equals("1SYYQ-2YQRK-1YQLR-SupplierName") && a.ConfigValue.Equals("1")).ToList();
                if (listSupplier.Count > 0)
                {
                    configSet.IsValidateSupplier = 1;
                }

                //判断品牌是否必填
                var listBrand = configObj.Data.Where(a => a.TypeCode.Equals("1SYYQ-2YQRK-1YQLR-Brand") && a.ConfigValue.Equals("1")).ToList();
                if (listBrand.Count > 0)
                {
                    configSet.IsValidateBrand = 1;
                }

                //判断单价是否必填
                var listPrice = configObj.Data.Where(a => a.TypeCode.Equals("1SYYQ-2YQRK-1YQLR-Price") && a.ConfigValue.Equals("1")).ToList();
                if (listPrice.Count > 0)
                {
                    configSet.IsValidatePrice = 1;
                }

                //判断仪器图片是否必填
                var listImg = configObj.Data.Where(a => a.TypeCode.Equals("1SYYQ-2YQRK-1YQLR-Image") && a.ConfigValue.Equals("1")).ToList();
                if (listImg.Count > 0)
                {
                    configSet.IsValidateImg = 1;
                }

                //判断保质期是否必填
                var listWarranty = configObj.Data.Where(a => a.TypeCode.Equals("1SYYQ-2YQRK-1YQLR-WarrantyMonth") && a.ConfigValue.Equals("1")).ToList();
                if (listWarranty.Count > 0)
                {
                    configSet.IsWarrantyMonth = 1;
                }


                obj.Data = configSet;
            }
            return Json(obj);
        }

        [HttpGet]
        [AuthorizeFilter("instrument:schoolinstrument:search")]
        public async Task<ActionResult> GetInstrumentSubmitJson(SchoolInstrumentListParam param)
        {
            OperatorInfo operatorInfo = await Operator.Instance.Current();
            TData<List<SchoolInstrumentEntity>> obj = new TData<List<SchoolInstrumentEntity>>();
            if (operatorInfo != null)
            {
                //param.SchoolId = operatorInfo.UnitId;
                //param.UserId = operatorInfo.UserId;
                //param.Statuz = 0;
                //obj = await schoolInstrumentBLL.GetList(param);
                obj = await schoolInstrumentBLL.GetSchoolInstrumentList(0, operatorInfo.UserId.Value, operatorInfo.UnitId.Value);

                obj.Data.FindAll(a => a.Num == 0).ForEach(a => a.Num = null);
                obj.Data.FindAll(a => a.Price == 0).ForEach(a => a.Price = null);
                obj.Data.FindAll(a => a.WarrantyMonth == 0).ForEach(a => a.WarrantyMonth = null);

                //查询区县设置
                long parentId = 0;
                var listUnitRelation = await unitRelationBLL.GetList(new UnitRelationListParam { ExtensionType = 3, ExtensionObjId = operatorInfo.UnitId.Value });
                if (listUnitRelation.Data.Count > 0)
                {
                    parentId = listUnitRelation.Data[0].UnitId.Value;
                }
                var listPriceSet = await configSetBLL.GetDefaultList(new ConfigSetListParam { TypeCode = "1002_SFXXZDYGG", UnitId = parentId });
                if (listPriceSet.Data.Count > 0 && listPriceSet.Data[0].ConfigValue.Equals("0"))
                {
                    obj.Total = -1;
                }
            }
            return Json(obj);
        }

        #endregion

        #region 提交数据
        [HttpPost]
        [AuthorizeFilter("instrument:schoolinstrument:add,instrument:schoolinstrument:edit")]
        public async Task<ActionResult> SaveFormJson(SchoolInstrumentInputModel entity)
        {
            TData<string> obj = new TData<string>();
            ConfigSetListParam param = new ConfigSetListParam();
            param.TypeCode = "1SYYQ-2YQRK-1YQLR";
            param.ModuleType = ConfigSetModuleTypeEnum.FormRequired.ParseToInt();
            TData<List<ConfigSetEntity>> configObj = await configSetBLL.GetListByCode(param);
            if (configObj.Tag == 1 && configObj.Data.Count > 0)
            {
                string errorMsg = "";
                foreach (var item in configObj.Data)
                {
                    if (item.ConfigValue == "1")
                    {
                        string typecode = item.TypeCode.Replace("1SYYQ-2YQRK-1YQLR-", "");
                        if (typecode == "Brand")
                        {
                            if (string.IsNullOrEmpty(entity.Brand))
                            {
                                errorMsg += (item.VerifyHint + "<br/>");
                            }
                        }
                        else if (typecode == "SupplierName")
                        {
                            if (string.IsNullOrEmpty(entity.SupplierName))
                            {
                                errorMsg += (item.VerifyHint + "<br/>");
                            }
                        }
                        else if (typecode == "Image")
                        {
                            if (!(entity.AttachmentId > 0))
                            {
                                errorMsg += (item.VerifyHint + "<br/>");
                            }
                        }
                        else if (typecode == "Floor")
                        {
                            if (entity.StoragePlace != null && string.IsNullOrEmpty(entity.Floor) && entity.StoragePlace.IndexOf(',') != -1)
                            {
                                errorMsg += (item.VerifyHint + "<br/>");
                            }
                        }
                        else if (typecode == "Price")
                        {
                            if (!entity.Price.HasValue)
                            {
                                errorMsg += (item.VerifyHint + "<br/>");
                            }
                        }
                    }
                }
                if (errorMsg != "")
                {
                    obj.Tag = 0;
                    obj.Message = errorMsg;
                    return Json(obj);
                }
            }

            if (entity.Code.Substring(entity.Code.Length - 2).Equals("00"))
            {
                entity.ModelCode = "";
                entity.OriginalCode = entity.Code;
            }
            else
            {
                entity.ModelCode = entity.Code;
                entity.Code = entity.Code.Substring(0, entity.Code.Length - 2) + "00";
                entity.OriginalCode = entity.ModelCode;
            }

            obj = await schoolInstrumentBLL.SaveForm(entity);
            return Json(obj);
        }

        [HttpPost]
        [AuthorizeFilter("instrument:schoolinstrument:delete")]
        public async Task<ActionResult> DeleteFormJson(long id)
        {
            TData obj = await schoolInstrumentBLL.DeleteForm(id);
            return Json(obj);
        }

        /// <summary>
        /// 导入批量仪器
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> ImportInstrumentJson(ImportParam param)
        {
            List<SchoolInstrumentEntity> list = new ExcelHelper<SchoolInstrumentEntity>().ImportFromExcel(param.FilePath);
            TData obj = await schoolInstrumentBLL.ImportInstrument(param, list);
            return Json(obj);
        }

        /// <summary>
        /// 导入存量仪器
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> ImportStockInstrumentJson(ImportParam param)
        {
            List<SchoolInstrumentEntity> list = new ExcelHelper<SchoolInstrumentEntity>().ImportFromExcel(param.FilePath);
            TData obj = await schoolInstrumentBLL.ImportInstrument(param, list, 2);
            return Json(obj);
        }

        /// <summary>
        /// 批量设置适用学科
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<ActionResult> BatchEditCourseJson(SiBatchEditCourseInputModel entity)
        {
            TData obj = await schoolInstrumentBLL.BatchEditCourse(entity);
            return Json(obj);
        }

        /// <summary>
        /// 单条入室、修改
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<ActionResult> SingleInputRoomJson(SiSingleRoomInputModel entity)
        {
            TData obj = await schoolInstrumentBLL.SingleInputRoom(entity);
            return Json(obj);
        }

        /// <summary>
        /// 批量入室
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<ActionResult> BatchInputRoomJson(SiBatchRoomInputModel entity)
        {
            TData obj = await schoolInstrumentBLL.BatchInputRoom(entity);
            return Json(obj);
        }

        /// <summary>
        /// 批量入库
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<ActionResult> BatchInputStorage(IdsInputModel entity)
        {
            TData obj = await schoolInstrumentBLL.BatchInputStorage(entity);
            return Json(obj);
        }


        /// <summary>
        /// 下载仪器导入模板
        /// </summary>
        /// <param name="id">eq_InstrumentEvaluateProjectVersion表Id</param>
        /// <returns></returns>
        public async Task<IActionResult> ExportInstrumentEvStandardTemplate(long id, string name)
        {
            TData<string> obj = new TData<string>();
            OperatorInfo operatorInfo = await Operator.Instance.Current();
            if (operatorInfo != null)
            {
                var resultObj = await schoolInstrumentBLL.GetInstrumentEvStandardTemplate(id);
                if (resultObj.Tag == 1)
                {
                    string file = new ExcelHelper<SchoolInstrumentEntity>().ExportToExcel("仪器存量导入模板.xls",
                                                                                             name + "-仪器存量导入",
                                                                                             resultObj.Data.ToList(),
                                                                                             new string[] { "Stage", "Course", "Code", "Name", "Model", "Brand", "Num", "UnitName", "StrPrice", "PurchaseDate", "WarrantyMonth", "SupplierName", "IsSelfMadeStr", "FunRoom", "Cupboard", "Floor" });
                    obj.Data = file;
                    obj.Tag = 1;
                }
            }
            return Json(obj);
        }

        /// <summary>
        /// 批量删除
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("instrument:schoolinstrument:delete")]
        public async Task<ActionResult> BatchDeleteJson(IdsInputModel entity)
        {
            TData obj = new TData();
            if (entity.Ids.IsEmpty())
            {
                obj.Tag = 0;
                obj.Message = "请至少选择一项。";
                return Json(obj);
            }
            long[] ids = TextHelper.SplitToArray<long>(entity.Ids, ',');
            int total = ids.Length;
            int success = 0;
            foreach (long id in ids)
            {
                if ((await schoolInstrumentBLL.DeleteForm(id)).Tag == 1)
                {
                    success++;
                }
            }
            obj.Tag = 1;
            if (total == success)
            {
                obj.Message = $"批量删除{total}条数据成功。";
            }
            else
            {
                obj.Message = $"共批量执行{total}条数据，其中成功{success}条，失败{total - success}条。失败可能原因：已入库的仪器不能删除。";
            }
            return Json(obj);
        }


        /// <summary>
        /// 导出柜签
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<IActionResult> ExportInstrumentCabCode(SchoolInstrumentListParam param)
        {
            TData<string> obj = new TData<string>();
            OperatorInfo operatorInfo = await Operator.Instance.Current();
            if (operatorInfo != null)
            {
                param.Statuz = InstrumentInputStatuzEnum.AlreadyInputStorage.ParseToInt();
                param.ListType = 1;
                param.IsShowTotalRow = 0;
                param.UnitId = operatorInfo.UnitId ?? 0;
                param.SafeguardUserId = operatorInfo.UserId;
                List<SchoolInstrumentEntity> list = (await schoolInstrumentBLL.GetPageList(param, new Pagination
                {
                    PageIndex = 1,
                    PageSize = int.MaxValue,
                    Sort = " FunRoom ASC , Sort ASC , Cupboard ASC , Floor ASC "
                })).Data;

                if (list.Count > 0)
                {
                    var listSum = list.GroupBy(item => new {
                        item.FunRoomId,
                        item.CupboardId,
                        item.FunRoom,
                        item.Cupboard,
                        item.Code,
                        item.Name,
                        item.Model,
                        item.UnitName,
                        item.Floor,
                    })
                       .Select(group => new SchoolInstrumentEntity
                       {
                           FunRoomId = group.Key.FunRoomId,
                           CupboardId = group.Key.CupboardId,
                           Code = group.Key.Code,
                           Name = group.Key.Name,
                           FunRoom = group.Key.FunRoom,
                           Cupboard = group.Key.Cupboard,
                           Model = group.Key.Model,
                           UnitName = group.Key.UnitName,
                           Floor = group.Key.Floor,
                           StockNum = group.Sum(f => f.StockNum)
                       }).ToList();


                    var cupList = list.GroupBy(f => new { f.FunRoomId, f.CupboardId }).ToList();

                    string sFileName = string.Empty;
                    if (cupList.Count == 1)
                    {
                        sFileName = $"{SecurityHelper.GetGuid(true)}_{list[0].FunRoom.Replace("\\", "").Replace("/", "")}";
                        if (!string.IsNullOrEmpty(list[0].Cupboard))
                            sFileName += $"{list[0].Cupboard.Replace("\\", "").Replace("/", "")}";
                        sFileName += $"柜签.xls";
                    }
                    else
                    {
                        var roomList = list.GroupBy(f => new { f.FunRoomId }).ToList();
                        if (roomList.Count == 1)
                        {
                            sFileName = $"{SecurityHelper.GetGuid(true)}_{list[0].FunRoom.Replace("\\", "").Replace("/", "")}";                          
                            sFileName += $"柜签.xls";
                        }
                        else
                        {
                            sFileName = string.Format("{0}_柜签.xls", SecurityHelper.GetGuid(true));
                        }
                    }                   

                    string sRoot = GlobalContext.HostingEnvironment.ContentRootPath;
                    string partDirectory = string.Format("Resource{0}Export{0}Excel", Path.DirectorySeparatorChar);
                    string sDirectory = Path.Combine(sRoot, partDirectory);
                    string sFilePath = Path.Combine(sDirectory, sFileName);
                    if (!Directory.Exists(sDirectory))
                    {
                        Directory.CreateDirectory(sDirectory);
                    }
                    using (MemoryStream ms = CreateExportMemoryStream(listSum, param.UName,param.UDate))
                    {
                        using (FileStream fs = new FileStream(sFilePath, FileMode.Create, FileAccess.Write))
                        {
                            byte[] data = ms.ToArray();
                            fs.Write(data, 0, data.Length);
                            fs.Flush();
                        }
                    }
                    obj.Data = partDirectory + Path.DirectorySeparatorChar + sFileName;
                    obj.Tag = 1;
                }
                else
                {
                    obj.Tag = 0;
                    obj.Message = "没有可导出的数据！";
                }
            }
            return Json(obj);
        }

        /// <summary>
        /// 导出柜签A6纸
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<IActionResult> ExportInstrumentCabCodeA6(SchoolInstrumentListParam param)
        {
            TData<string> obj = new TData<string>();
            OperatorInfo operatorInfo = await Operator.Instance.Current();
            if (operatorInfo != null)
            {
                param.Statuz = InstrumentInputStatuzEnum.AlreadyInputStorage.ParseToInt();
                param.ListType = 1;
                param.IsShowTotalRow = 0;
                param.UnitId = operatorInfo.UnitId ?? 0;
                param.SafeguardUserId = operatorInfo.UserId;
                List<SchoolInstrumentEntity> list = (await schoolInstrumentBLL.GetPageList(param, new Pagination
                {
                    PageIndex = 1,
                    PageSize = int.MaxValue,
                    Sort = " FunRoom ASC , Sort ASC , Cupboard ASC , Floor ASC "
                })).Data;

                if (list.Count > 0)
                {
                    var listSum = list.GroupBy(item => new {
                        item.FunRoomId,
                        item.CupboardId,
                        item.FunRoom,
                        item.Cupboard,
                        item.Code,
                        item.Name,
                        item.Model,
                        item.UnitName,
                        item.Floor,
                    })
                       .Select(group => new SchoolInstrumentEntity
                       {
                           FunRoomId = group.Key.FunRoomId,
                           CupboardId = group.Key.CupboardId,
                           Code = group.Key.Code,
                           Name = group.Key.Name,
                           FunRoom = group.Key.FunRoom,
                           Cupboard = group.Key.Cupboard,
                           Model = group.Key.Model,
                           UnitName = group.Key.UnitName,
                           Floor = group.Key.Floor,
                           StockNum = group.Sum(f => f.StockNum)
                       }).ToList();


                    var cupList = list.GroupBy(f => new { f.FunRoomId, f.CupboardId }).ToList();

                    string sFileName = string.Empty;
                    if (cupList.Count == 1)
                        sFileName = $"{SecurityHelper.GetGuid(true)}_{list[0].FunRoom}{list[0].Cupboard}柜签.xlsx";
                    else
                        sFileName = string.Format("{0}_柜签.xlsx", SecurityHelper.GetGuid(true));

                    string sRoot = GlobalContext.HostingEnvironment.ContentRootPath;
                    string partDirectory = string.Format("Resource{0}Export{0}Excel", Path.DirectorySeparatorChar);
                    string sDirectory = Path.Combine(sRoot, partDirectory);
                    string sFilePath = Path.Combine(sDirectory, sFileName);
                    if (!Directory.Exists(sDirectory))
                    {
                        Directory.CreateDirectory(sDirectory);
                    }

                    using (MemoryStream ms = CreateExportMemoryStreamA6(listSum, param.UName, param.UDate))
                    {
                        using (FileStream fs = new FileStream(sFilePath, FileMode.Create, FileAccess.Write))
                        {
                            byte[] data = ms.ToArray();
                            fs.Write(data, 0, data.Length);
                            fs.Flush();
                        }
                    }

                    obj.Data = partDirectory + Path.DirectorySeparatorChar + sFileName;
                    obj.Tag = 1;
                }
                else
                {
                    obj.Tag = 0;
                    obj.Message = "没有可导出的数据！";
                }
            }
            return Json(obj);
        }

        /// <summary>
        /// 批量入库撤回
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public async Task<ActionResult> RevokeJson(IdsInputModel entity)
        {
            TData obj = await schoolInstrumentBLL.BatchRevoke(entity);
            return Json(obj);
        }


        /// <summary>
        /// 导出柜签
        /// </summary>
        /// <param name="list">数据</param>
        /// <param name="uName">制表人</param>
        /// <param name="uDate">制表日期</param>
        /// <returns></returns>
        private MemoryStream CreateExportMemoryStream(List<SchoolInstrumentEntity> list,string uName,string uDate)
        {
            HSSFWorkbook workbook = new HSSFWorkbook();
            ISheet sheet = workbook.CreateSheet();
            sheet.FitToPage = false; //不缩放到一页

            IFont headFont = workbook.CreateFont();
            headFont.FontName = "宋体";
            headFont.FontHeightInPoints = 20;
            ICellStyle headStyle = workbook.CreateCellStyle();
            headStyle.Alignment = HorizontalAlignment.Center;
            headStyle.VerticalAlignment = VerticalAlignment.Center;            
            headStyle.SetFont(headFont); 

            IFont titleFont = workbook.CreateFont();
            titleFont.FontName = "宋体";
            titleFont.FontHeightInPoints = 10;
            ICellStyle titleStyle = workbook.CreateCellStyle();
            titleStyle.Alignment = HorizontalAlignment.Center; 
            ExcelHelper<int>.SetSheetStyleBorder(titleStyle);
            titleStyle.SetFont(titleFont);

            IFont contentFont = workbook.CreateFont();
            contentFont.FontName = "宋体";
            contentFont.FontHeightInPoints = 10;

            ICellStyle contentLeftStyle = workbook.CreateCellStyle();
            contentLeftStyle.Alignment = HorizontalAlignment.Left;
            ExcelHelper<int>.SetSheetStyleBorder(contentLeftStyle);
            contentLeftStyle.WrapText = true;
            contentLeftStyle.SetFont(contentFont);

            ICellStyle contentRightStyle = workbook.CreateCellStyle();
            contentRightStyle.Alignment = HorizontalAlignment.Right;
            ExcelHelper<int>.SetSheetStyleBorder(contentRightStyle);
            contentRightStyle.SetFont(contentFont);

            ICellStyle contentCenterStyle = workbook.CreateCellStyle();
            contentCenterStyle.Alignment = HorizontalAlignment.Center;
            ExcelHelper<int>.SetSheetStyleBorder(contentCenterStyle);
            contentCenterStyle.SetFont(contentFont);

            IFont biaoFont = workbook.CreateFont();
            biaoFont.FontName = "宋体";
            biaoFont.FontHeightInPoints = 10;
            ICellStyle biaoStyle = workbook.CreateCellStyle();
            biaoStyle.Alignment = HorizontalAlignment.Left;
            biaoStyle.VerticalAlignment = VerticalAlignment.Center;
            biaoStyle.SetFont(biaoFont);

            for (int i = 0; i <= 6; i++)
            {
                if (i == 0 || i == 5) sheet.SetColumnWidth(i, 5 * 256);
                else if (i == 1 || i == 6) sheet.SetColumnWidth(i, 13 * 256);
                else if (i == 2) sheet.SetColumnWidth(i, 18 * 256);
                else if (i == 3) sheet.SetColumnWidth(i, 27 * 256);
                else if (i == 4) sheet.SetColumnWidth(i, 10 * 256);
            }

            int rowIndex = 0;
            int cellIndex = 0;
            int rowNumber = 1;  //序号
                
            var cupList = list.GroupBy(f => new { f.FunRoomId, f.CupboardId }).ToList();
            foreach (var item in cupList)
            {
                var cupinstrumentList = list.Where(f => f.FunRoomId == item.Key.FunRoomId && f.CupboardId == item.Key.CupboardId).ToList();
                if (cupinstrumentList.Count > 0)
                {
                    rowIndex++;
                    IRow row = sheet.CreateRow(rowIndex);
                    row.HeightInPoints = 60;
                    sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(rowIndex, rowIndex, 0, 3));
                    rowIndex++;
                    rowNumber = 1;
                    string cupboardName = "";
                    if (!string.IsNullOrEmpty(cupinstrumentList[0].Cupboard))
                    {
                        cupboardName = ">" + cupinstrumentList[0].Cupboard.ToString();
                    }
                    row.CreateCell(0).SetCellValue(cupinstrumentList[0].FunRoom + cupboardName);
                    row.GetCell(0).CellStyle = headStyle;

                    sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(rowIndex, rowIndex, 0, 3));
                    sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(rowIndex - 1, rowIndex, 4, 6));
                    row = sheet.CreateRow(rowIndex);
                    rowIndex++;
                    row.HeightInPoints = 60;

                    string headName = "制表人：" + uName + "                \t制表日期：" + uDate;
                    row.CreateCell(0).SetCellValue(headName);
                    row.GetCell(0).CellStyle = biaoStyle;


                    var qrUrl = GlobalContext.SystemConfig.WebSite + (item.Key.CupboardId != 0 ? "/m/c/" + item.Key.CupboardId : "/m/c/0" + item.Key.FunRoomId);
                    var picBytes = QrCodeGenerater.CreateQrCode(qrUrl, 1, 4, 5);

                   
                    int pictureIdx = workbook.AddPicture(picBytes, NPOI.SS.UserModel.PictureType.PNG);

                    HSSFPatriarch patriarch = (HSSFPatriarch)sheet.CreateDrawingPatriarch();
                    //dx1：锚点离其所在单元格左上角的水平偏移量。
                    //dy1：锚点离其所在单元格左上角的垂直偏移量。
                    //dx2：锚点离其所在单元格右下角的水平偏移量。
                    //dy2：锚点离其所在单元格右下角的垂直偏移量。
                    //col1：锚点所在的起始单元格的列索引（从0开始计数）。 第n列开始
                    //row1：锚点所在的起始单元格的行索引（从0开始计数）。
                    //col2：锚点所在的结束单元格的列索引（从0开始计数）。 第n+m列结束
                    //row2：锚点所在的结束单元格的行索引（从0开始计数）。
                    HSSFClientAnchor anchor = new HSSFClientAnchor(150, 20, 0, 0, 4, rowIndex - 2, 7, rowIndex);
                   
                    HSSFPicture pict = (HSSFPicture)patriarch.CreatePicture(anchor, pictureIdx);
                    //pict.SetLineStyleColor(255, 255, 255);
                    //pict.SetFillColor(255, 255, 255);
                    

                    pict.Resize(0.76, 0.95);
                   

                    row = sheet.CreateRow(rowIndex);
                    rowIndex++;
                    row.HeightInPoints = 35;
                    row.CreateCell(cellIndex).SetCellValue("序号");
                    row.GetCell(cellIndex).CellStyle = titleStyle;
                    cellIndex++;

                    row.CreateCell(cellIndex).SetCellValue("分类代码");
                    row.GetCell(cellIndex).CellStyle = titleStyle;
                    cellIndex++;

                    row.CreateCell(cellIndex).SetCellValue("仪器名称");
                    row.GetCell(cellIndex).CellStyle = titleStyle;
                    cellIndex++;

                    row.CreateCell(cellIndex).SetCellValue("规格属性");
                    row.GetCell(cellIndex).CellStyle = titleStyle;
                    cellIndex++;

                    row.CreateCell(cellIndex).SetCellValue("数量");
                    row.GetCell(cellIndex).CellStyle = titleStyle;
                    cellIndex++;

                    row.CreateCell(cellIndex).SetCellValue("单位");
                    row.GetCell(cellIndex).CellStyle = titleStyle;
                    cellIndex++;

                    row.CreateCell(cellIndex).SetCellValue("层次");
                    row.GetCell(cellIndex).CellStyle = titleStyle;
                    cellIndex = 0;

                    int i = 0;
                    int pageOne = 15;
                    int k = 20;
                    int t = 0;

                    int rowHeight = 30;

                    foreach (var instrument in cupinstrumentList)
                    {
                        if (i == pageOne)
                        {
                            rowIndex++;
                            sheet.SetRowBreak(rowIndex);
                            rowIndex++;

                            row = sheet.CreateRow(rowIndex);
                            rowIndex++;
                            row.HeightInPoints = rowHeight;
                            row.CreateCell(cellIndex).SetCellValue("序号");
                            row.GetCell(cellIndex).CellStyle = titleStyle;
                            cellIndex++;

                            row.CreateCell(cellIndex).SetCellValue("分类代码");
                            row.GetCell(cellIndex).CellStyle = titleStyle;
                            cellIndex++;

                            row.CreateCell(cellIndex).SetCellValue("仪器名称");
                            row.GetCell(cellIndex).CellStyle = titleStyle;
                            cellIndex++;

                            row.CreateCell(cellIndex).SetCellValue("规格属性");
                            row.GetCell(cellIndex).CellStyle = titleStyle;
                            cellIndex++;

                            row.CreateCell(cellIndex).SetCellValue("数量");
                            row.GetCell(cellIndex).CellStyle = titleStyle;
                            cellIndex++;

                            row.CreateCell(cellIndex).SetCellValue("单位");
                            row.GetCell(cellIndex).CellStyle = titleStyle;
                            cellIndex++;

                            row.CreateCell(cellIndex).SetCellValue("层次");
                            row.GetCell(cellIndex).CellStyle = titleStyle;
                            cellIndex = 0;
                        }
                        else if( t != 0 && t % k == 0)
                        {
                            rowIndex++;
                            sheet.SetRowBreak(rowIndex);
                            rowIndex++;

                            row = sheet.CreateRow(rowIndex);
                            rowIndex++;
                            row.HeightInPoints = rowHeight;
                            row.CreateCell(cellIndex).SetCellValue("序号");
                            row.GetCell(cellIndex).CellStyle = titleStyle;
                            cellIndex++;

                            row.CreateCell(cellIndex).SetCellValue("分类代码");
                            row.GetCell(cellIndex).CellStyle = titleStyle;
                            cellIndex++;

                            row.CreateCell(cellIndex).SetCellValue("仪器名称");
                            row.GetCell(cellIndex).CellStyle = titleStyle;
                            cellIndex++;

                            row.CreateCell(cellIndex).SetCellValue("规格属性");
                            row.GetCell(cellIndex).CellStyle = titleStyle;
                            cellIndex++;

                            row.CreateCell(cellIndex).SetCellValue("数量");
                            row.GetCell(cellIndex).CellStyle = titleStyle;
                            cellIndex++;

                            row.CreateCell(cellIndex).SetCellValue("单位");
                            row.GetCell(cellIndex).CellStyle = titleStyle;
                            cellIndex++;

                            row.CreateCell(cellIndex).SetCellValue("层次");
                            row.GetCell(cellIndex).CellStyle = titleStyle;
                            cellIndex = 0;

                        }

                        row = sheet.CreateRow(rowIndex);
                        rowIndex++;
                        row.HeightInPoints = rowHeight;
                        row.CreateCell(cellIndex).SetCellValue(rowNumber);
                        rowNumber++;
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;

                        row.CreateCell(cellIndex).SetCellValue(instrument.Code);
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;

                        row.CreateCell(cellIndex).SetCellValue(instrument.Name);
                        row.GetCell(cellIndex).CellStyle = contentLeftStyle;
                        cellIndex++;

                        row.CreateCell(cellIndex).SetCellValue(instrument.Model);
                        row.GetCell(cellIndex).CellStyle = contentLeftStyle;
                        cellIndex++;

                        row.CreateCell(cellIndex).SetCellValue(instrument.StockNum.ToString("G0"));
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;

                        row.CreateCell(cellIndex).SetCellValue(instrument.UnitName);
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;

                        row.CreateCell(cellIndex).SetCellValue(instrument.Floor);
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex = 0;

                        i++;
                        if (i > pageOne)
                        {
                            t++;
                        }
                    }

                    #region 每页不够用空行填充
                    int totalCount = cupinstrumentList.Count;
                    if(totalCount < pageOne)
                    {
                        int fCount = pageOne - totalCount;
                        for (int f = 0; f < fCount; f++)
                        {
                            row = sheet.CreateRow(rowIndex);
                            rowIndex++;
                            row.HeightInPoints = rowHeight;
                            row.CreateCell(cellIndex).SetCellValue(rowNumber);
                            rowNumber++;
                            row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                            cellIndex++;

                            row.CreateCell(cellIndex).SetCellValue("");
                            row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                            cellIndex++;

                            row.CreateCell(cellIndex).SetCellValue("");
                            row.GetCell(cellIndex).CellStyle = contentLeftStyle;
                            cellIndex++;

                            row.CreateCell(cellIndex).SetCellValue("");
                            row.GetCell(cellIndex).CellStyle = contentLeftStyle;
                            cellIndex++;

                            row.CreateCell(cellIndex).SetCellValue("");
                            row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                            cellIndex++;

                            row.CreateCell(cellIndex).SetCellValue("");
                            row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                            cellIndex++;

                            row.CreateCell(cellIndex).SetCellValue("");
                            row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                            cellIndex = 0;
                        }
                    }
                    else
                    {
                        totalCount = totalCount - pageOne;
                        int fCount = k - (totalCount % k);
                        for (int f = 0; f < fCount; f++)
                        {
                            row = sheet.CreateRow(rowIndex);
                            rowIndex++;
                            row.HeightInPoints = rowHeight;
                            row.CreateCell(cellIndex).SetCellValue(rowNumber);
                            rowNumber++;
                            row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                            cellIndex++;

                            row.CreateCell(cellIndex).SetCellValue("");
                            row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                            cellIndex++;

                            row.CreateCell(cellIndex).SetCellValue("");
                            row.GetCell(cellIndex).CellStyle = contentLeftStyle;
                            cellIndex++;

                            row.CreateCell(cellIndex).SetCellValue("");
                            row.GetCell(cellIndex).CellStyle = contentLeftStyle;
                            cellIndex++;

                            row.CreateCell(cellIndex).SetCellValue("");
                            row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                            cellIndex++;

                            row.CreateCell(cellIndex).SetCellValue("");
                            row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                            cellIndex++;

                            row.CreateCell(cellIndex).SetCellValue("");
                            row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                            cellIndex = 0;
                        }
                    }
                    #endregion


                    sheet.SetRowBreak(rowIndex);
                    rowIndex++;
                   
                }
            }

            using (MemoryStream ms = new MemoryStream())
            {
                workbook.Write(ms);
                workbook.Close();
                ms.Flush();
                ms.Position = 0;
                return ms;
            }
        }

        /// <summary>
        /// 导出柜签A6纸
        /// </summary>
        /// <param name="list"></param>
        /// <param name="uName"></param>
        /// <param name="uDate"></param>
        /// <returns></returns>
        private MemoryStream CreateExportMemoryStreamA6(List<SchoolInstrumentEntity> list, string uName, string uDate)
        {

            XSSFWorkbook workbook = new XSSFWorkbook();
            XSSFSheet sheet = (XSSFSheet)workbook.CreateSheet();

            //XSSFWorkbook workbook = new XSSFWorkbook();
            //ISheet sheet = workbook.CreateSheet();
            //sheet.FitToPage = false; //不缩放到一页

            sheet.PrintSetup.PaperSize = (short)9; // 设置纸张大小为A4（210mm x 297mm）


            // 假设 sheet 是已经创建好的 ISheet 对象  
            sheet.SetMargin(MarginType.LeftMargin, (double)0.2); // 设置左边距为0.2英寸对应0.5cm
            sheet.SetMargin(MarginType.RightMargin, (double)0.2); // 设置右边距为0.2英寸对应0.5cm  
            sheet.SetMargin(MarginType.TopMargin, (double)0.1); // 设置上边距为0.2英寸对应0.5cm
            sheet.SetMargin(MarginType.BottomMargin, (double)0); // 设置下边距为0.2英寸对应0.5cm


            IFont headFont = workbook.CreateFont();
            headFont.FontName = "宋体";
            headFont.FontHeightInPoints = 16;
            ICellStyle headStyle = workbook.CreateCellStyle();
            headStyle.Alignment = HorizontalAlignment.Center;
            headStyle.VerticalAlignment = VerticalAlignment.Center;
            headStyle.SetFont(headFont);

            IFont titleFont = workbook.CreateFont();
            titleFont.FontName = "宋体";
            titleFont.FontHeightInPoints = 8;
            ICellStyle titleStyle = workbook.CreateCellStyle();
            titleStyle.Alignment = HorizontalAlignment.Center;
            ExcelHelper<int>.SetSheetStyleBorder(titleStyle);
            titleStyle.SetFont(titleFont);

            IFont contentFont = workbook.CreateFont();
            contentFont.FontName = "宋体";
            contentFont.FontHeightInPoints = 8;

            ICellStyle contentLeftStyle = workbook.CreateCellStyle();
            contentLeftStyle.Alignment = HorizontalAlignment.Left;
            ExcelHelper<int>.SetSheetStyleBorder(contentLeftStyle);
            contentLeftStyle.WrapText = true;
            contentLeftStyle.SetFont(contentFont);


            ICellStyle contentRightStyle = workbook.CreateCellStyle();
            contentRightStyle.Alignment = HorizontalAlignment.Right;
            ExcelHelper<int>.SetSheetStyleBorder(contentRightStyle);
            contentRightStyle.SetFont(contentFont);

            ICellStyle contentCenterStyle = workbook.CreateCellStyle();
            contentCenterStyle.Alignment = HorizontalAlignment.Center;
            ExcelHelper<int>.SetSheetStyleBorder(contentCenterStyle);
            contentCenterStyle.SetFont(contentFont);


            IFont biaoFont = workbook.CreateFont();
            biaoFont.FontName = "宋体";
            biaoFont.FontHeightInPoints = 8;
            ICellStyle biaoStyle = workbook.CreateCellStyle();
            biaoStyle.Alignment = HorizontalAlignment.Left;
            biaoStyle.VerticalAlignment = VerticalAlignment.Center;
            biaoStyle.SetFont(biaoFont);

            sheet.SetColumnWidth(0, 4 * 256);//3.37
            sheet.SetColumnWidth(1, 9 * 256);//8.91
            sheet.SetColumnWidth(2, 10 * 256);//7.91
            sheet.SetColumnWidth(3, 13 * 256); //11.91                               
            sheet.SetColumnWidth(4, 4 * 256); //3.91                                                            
            sheet.SetColumnWidth(5, 4 * 256); //3.74                                                                                            
            sheet.SetColumnWidth(6, 5 * 256); //4.91

            sheet.SetColumnWidth(7, 4 * 256);//3.37

            sheet.SetColumnWidth(8, 4 * 256);//3.37
            sheet.SetColumnWidth(9, 9 * 256);//8.91
            sheet.SetColumnWidth(10, 10 * 256);//7.91
            sheet.SetColumnWidth(11, 13 * 256); //11.91                               
            sheet.SetColumnWidth(12, 4 * 256); //3.91                                                            
            sheet.SetColumnWidth(13, 4 * 256); //3.74                                                                                            
            sheet.SetColumnWidth(14, 5 * 256); //4.91

            IRow row = null;
            int rowIndex = 0;
            int cellIndex = 0;
            int rowNumber = 1;  //序号
            int pageIndex = 0;  //总共生成多少个模块
            int picCellBeginIndex = 4;
            int picCellEndIndex = 7;


            var cupList = list.GroupBy(f => new { f.FunRoomId, f.CupboardId }).ToList();
            foreach (var item in cupList)
            {
                pageIndex++;
                var cupinstrumentList = list.Where(f => f.FunRoomId == item.Key.FunRoomId && f.CupboardId == item.Key.CupboardId).ToList();
                if (cupinstrumentList.Count > 0)
                {
                    if (pageIndex % 2 == 0)
                    {
                        rowIndex = (pageIndex / 2 - 1) * 19;
                        cellIndex = 8;

                        picCellBeginIndex = 12;
                        picCellEndIndex = 15;
                    }
                    else
                    {
                        rowIndex = (pageIndex - 1) / 2 * 19;
                        cellIndex = 0;
                        //sheet.SetRowBreak(rowIndex);

                        picCellBeginIndex = 4;
                        picCellEndIndex = 7;

                    }

                    #region 分隔
                    if ((pageIndex - 1) % 4 != 0 && (pageIndex - 2) % 4 != 0)
                    {
                        row = sheet.CreateRow(rowIndex);
                        row.HeightInPoints = 30;
                        rowIndex++;
                    }
                    else
                    {
                        row = sheet.CreateRow(rowIndex);
                        row.HeightInPoints = 5;
                        rowIndex++;
                    }
                    #endregion

                    row = sheet.GetRow(rowIndex);
                    if (row == null)
                    {
                        row = sheet.CreateRow(rowIndex);
                    }
                    row.HeightInPoints = 30;
                    sheet.AddMergedRegion(new CellRangeAddress(rowIndex, rowIndex, cellIndex, cellIndex + 3));
                    rowIndex++;
                    rowNumber = 1;

                    string cupboardName = "";
                    if (!string.IsNullOrEmpty(cupinstrumentList[0].Cupboard))
                    {
                        cupboardName = ">" + cupinstrumentList[0].Cupboard.ToString();
                    }
                    row.CreateCell(cellIndex).SetCellValue(cupinstrumentList[0].FunRoom + cupboardName);
                    row.GetCell(cellIndex).CellStyle = headStyle;

                    sheet.AddMergedRegion(new CellRangeAddress(rowIndex, rowIndex, cellIndex, cellIndex + 3));
                    sheet.AddMergedRegion(new CellRangeAddress(rowIndex - 1, rowIndex, cellIndex + 4, cellIndex + 6));
                    row = sheet.GetRow(rowIndex);
                    if (row == null)
                    {
                        row = sheet.CreateRow(rowIndex);
                    }


                    rowIndex++;
                    row.HeightInPoints = 22;

                    string headName = "制表人：" + uName + "       \t制表日期：" + uDate;
                    row.CreateCell(cellIndex).SetCellValue(headName);
                    row.GetCell(cellIndex).CellStyle = biaoStyle;


                    var qrUrl = GlobalContext.SystemConfig.WebSite + (item.Key.CupboardId != 0 ? "/m/c/" + item.Key.CupboardId : "/m/c/0" + item.Key.FunRoomId);
                    var picBytes = QrCodeGenerater.CreateQrCode(qrUrl, 1, 4, 5);
                    IDrawing drawing = sheet.CreateDrawingPatriarch() as XSSFDrawing;
                    int pictureIdx = workbook.AddPicture(picBytes, PictureType.PNG);
                    XSSFClientAnchor anchor = new XSSFClientAnchor(150, 20, 0, 0, picCellBeginIndex, rowIndex - 2, picCellEndIndex, rowIndex);
                    XSSFPicture pict = (XSSFPicture)drawing.CreatePicture(anchor, pictureIdx); 
                    pict.Resize(0.8, 1);

                    row = sheet.GetRow(rowIndex);
                    if (row == null)
                    {
                        row = sheet.CreateRow(rowIndex);
                    }
       
                    rowIndex++;
                    row.HeightInPoints = 21;
                    row.CreateCell(cellIndex).SetCellValue("序号");
                    row.GetCell(cellIndex).CellStyle = titleStyle;
                    cellIndex++;

                    row.CreateCell(cellIndex).SetCellValue("分类代码");
                    row.GetCell(cellIndex).CellStyle = titleStyle;
                    cellIndex++;

                    row.CreateCell(cellIndex).SetCellValue("仪器名称");
                    row.GetCell(cellIndex).CellStyle = titleStyle;
                    cellIndex++;

                    row.CreateCell(cellIndex).SetCellValue("规格属性");
                    row.GetCell(cellIndex).CellStyle = titleStyle;
                    cellIndex++;

                    row.CreateCell(cellIndex).SetCellValue("数量");
                    row.GetCell(cellIndex).CellStyle = titleStyle;
                    cellIndex++;

                    row.CreateCell(cellIndex).SetCellValue("单位");
                    row.GetCell(cellIndex).CellStyle = titleStyle;
                    cellIndex++;

                    row.CreateCell(cellIndex).SetCellValue("层次");
                    row.GetCell(cellIndex).CellStyle = titleStyle;
                    cellIndex = 0;

                    int i = 0;
                    int pageOne = 15;
                    int k = 15;
                    int t = 0;

                    int rowHeight = 21;

                    foreach (var instrument in cupinstrumentList)
                    {
                        if (i == pageOne)
                        {
                            pageIndex++;
                            if (pageIndex % 2 == 0)
                            {
                                rowIndex = (pageIndex / 2 - 1) * 19;
                                cellIndex = 8;

                                picCellBeginIndex = 12;
                                picCellEndIndex = 15;
                            }
                            else
                            {
                                rowIndex = (pageIndex - 1) / 2 * 19;
                                cellIndex = 0;
                                //sheet.SetRowBreak(rowIndex);

                                picCellBeginIndex = 4;
                                picCellEndIndex = 7;
                            }

                            #region 分隔
                            if ((pageIndex - 1) % 4 != 0 && (pageIndex - 2) % 4 != 0)
                            {
                                row = sheet.CreateRow(rowIndex);
                                row.HeightInPoints = 30;
                                rowIndex++;
                            }
                            else
                            {
                                row = sheet.CreateRow(rowIndex);
                                row.HeightInPoints = 5;
                                rowIndex++;
                            }
                            #endregion

                            row = sheet.GetRow(rowIndex);
                            if (row == null)
                            {
                                row = sheet.CreateRow(rowIndex);
                            }
                            row.HeightInPoints = 30;
                            sheet.AddMergedRegion(new CellRangeAddress(rowIndex, rowIndex, cellIndex, cellIndex + 3));
                            rowIndex++;

                            cupboardName = "";
                            if (!string.IsNullOrEmpty(cupinstrumentList[0].Cupboard))
                            {
                                cupboardName = ">" + cupinstrumentList[0].Cupboard.ToString();
                            }
                            row.CreateCell(cellIndex).SetCellValue(cupinstrumentList[0].FunRoom + cupboardName);
                            row.GetCell(cellIndex).CellStyle = headStyle;
                            sheet.AddMergedRegion(new CellRangeAddress(rowIndex, rowIndex, cellIndex, cellIndex + 3));
                            sheet.AddMergedRegion(new CellRangeAddress(rowIndex - 1, rowIndex, cellIndex + 4, cellIndex + 6));
                            row = sheet.GetRow(rowIndex);
                            if (row == null)
                            {
                                row = sheet.CreateRow(rowIndex);
                            }
                            rowIndex++;
                            row.HeightInPoints = 22;
                            headName = "制表人：" + uName + "       \t制表日期：" + uDate;
                            row.CreateCell(cellIndex).SetCellValue(headName);
                            row.GetCell(cellIndex).CellStyle = biaoStyle;

                            qrUrl = GlobalContext.SystemConfig.WebSite + (item.Key.CupboardId != 0 ? "/m/c/" + item.Key.CupboardId : "/m/c/0" + item.Key.FunRoomId);
                            picBytes = QrCodeGenerater.CreateQrCode(qrUrl, 1, 4, 5);
                            drawing = sheet.CreateDrawingPatriarch() as XSSFDrawing;
                            pictureIdx = workbook.AddPicture(picBytes, PictureType.PNG);
                            anchor = new XSSFClientAnchor(150, 20, 0, 0, picCellBeginIndex, rowIndex - 2, picCellEndIndex, rowIndex);
                            pict = (XSSFPicture)drawing.CreatePicture(anchor, pictureIdx);
                            pict.Resize(0.8, 1);

                            row = sheet.GetRow(rowIndex);
                            if (row == null)
                            {
                                row = sheet.CreateRow(rowIndex);
                            }
                            rowIndex++;
                            row.HeightInPoints = rowHeight;
                            row.CreateCell(cellIndex).SetCellValue("序号");
                            row.GetCell(cellIndex).CellStyle = titleStyle;
                            cellIndex++;

                            row.CreateCell(cellIndex).SetCellValue("分类代码");
                            row.GetCell(cellIndex).CellStyle = titleStyle;
                            cellIndex++;

                            row.CreateCell(cellIndex).SetCellValue("仪器名称");
                            row.GetCell(cellIndex).CellStyle = titleStyle;
                            cellIndex++;

                            row.CreateCell(cellIndex).SetCellValue("规格属性");
                            row.GetCell(cellIndex).CellStyle = titleStyle;
                            cellIndex++;

                            row.CreateCell(cellIndex).SetCellValue("数量");
                            row.GetCell(cellIndex).CellStyle = titleStyle;
                            cellIndex++;

                            row.CreateCell(cellIndex).SetCellValue("单位");
                            row.GetCell(cellIndex).CellStyle = titleStyle;
                            cellIndex++;

                            row.CreateCell(cellIndex).SetCellValue("层次");
                            row.GetCell(cellIndex).CellStyle = titleStyle;
                           
                        }
                        else if (t != 0 && t % k == 0)
                        {
                            pageIndex++;
                            if (pageIndex % 2 == 0)
                            {
                                rowIndex = (pageIndex / 2 - 1) * 19;
                                cellIndex = 8;

                                picCellBeginIndex = 12;
                                picCellEndIndex = 15;
                            }
                            else
                            {
                                rowIndex = (pageIndex - 1) / 2 * 19;
                                cellIndex = 0;
                                //sheet.SetRowBreak(rowIndex);

                                picCellBeginIndex = 4;
                                picCellEndIndex = 7;
                            }

                            #region 分隔
                            if ((pageIndex - 1) % 4 != 0 && (pageIndex - 2) % 4 != 0)
                            {
                                row = sheet.CreateRow(rowIndex);
                                row.HeightInPoints = 30;
                                rowIndex++;
                            }
                            else
                            {
                                row = sheet.CreateRow(rowIndex);
                                row.HeightInPoints = 5;
                                rowIndex++;
                            }
                            #endregion

                            row = sheet.GetRow(rowIndex);
                            if (row == null)
                            {
                                row = sheet.CreateRow(rowIndex);
                            }
                            row.HeightInPoints = 30;
                            sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(rowIndex, rowIndex, cellIndex, cellIndex + 3));
                            rowIndex++;

                            cupboardName = "";
                            if (!string.IsNullOrEmpty(cupinstrumentList[0].Cupboard))
                            {
                                cupboardName = ">" + cupinstrumentList[0].Cupboard.ToString();
                            }
                            row.CreateCell(cellIndex).SetCellValue(cupinstrumentList[0].FunRoom + cupboardName);
                            row.GetCell(cellIndex).CellStyle = headStyle;
                            sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(rowIndex, rowIndex, cellIndex, cellIndex + 3));
                            sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(rowIndex - 1, rowIndex, cellIndex + 4, cellIndex + 6));
                            row = sheet.GetRow(rowIndex);
                            if (row == null)
                            {
                                row = sheet.CreateRow(rowIndex);
                            }
                            rowIndex++;
                            row.HeightInPoints = 22;
                            headName = "制表人：" + uName + "       \t制表日期：" + uDate;
                            row.CreateCell(cellIndex).SetCellValue(headName);
                            row.GetCell(cellIndex).CellStyle = biaoStyle;
                           

                             qrUrl = GlobalContext.SystemConfig.WebSite + (item.Key.CupboardId != 0 ? "/m/c/" + item.Key.CupboardId : "/m/c/0" + item.Key.FunRoomId);
                            picBytes = QrCodeGenerater.CreateQrCode(qrUrl, 1, 4, 5);
                            drawing = sheet.CreateDrawingPatriarch() as XSSFDrawing;
                            pictureIdx = workbook.AddPicture(picBytes, PictureType.PNG);
                            anchor = new XSSFClientAnchor(150, 20, 0, 0, picCellBeginIndex, rowIndex - 2, picCellEndIndex, rowIndex);
                            pict = (XSSFPicture)drawing.CreatePicture(anchor, pictureIdx);
                            pict.Resize(0.8, 1);

                            row = sheet.GetRow(rowIndex);
                            if (row == null)
                            {
                                row = sheet.CreateRow(rowIndex);
                            }
                            rowIndex++;
                            row.HeightInPoints = rowHeight;
                            row.CreateCell(cellIndex).SetCellValue("序号");
                            row.GetCell(cellIndex).CellStyle = titleStyle;
                            cellIndex++;

                            row.CreateCell(cellIndex).SetCellValue("分类代码");
                            row.GetCell(cellIndex).CellStyle = titleStyle;
                            cellIndex++;

                            row.CreateCell(cellIndex).SetCellValue("仪器名称");
                            row.GetCell(cellIndex).CellStyle = titleStyle;
                            cellIndex++;

                            row.CreateCell(cellIndex).SetCellValue("规格属性");
                            row.GetCell(cellIndex).CellStyle = titleStyle;
                            cellIndex++;

                            row.CreateCell(cellIndex).SetCellValue("数量");
                            row.GetCell(cellIndex).CellStyle = titleStyle;
                            cellIndex++;

                            row.CreateCell(cellIndex).SetCellValue("单位");
                            row.GetCell(cellIndex).CellStyle = titleStyle;
                            cellIndex++;

                            row.CreateCell(cellIndex).SetCellValue("层次");
                            row.GetCell(cellIndex).CellStyle = titleStyle;
                        }

                        if (pageIndex % 2 == 0)
                        {
                            cellIndex = 8;
                        }
                        else
                        {
                            cellIndex = 0;
                        }

                        row = sheet.GetRow(rowIndex);
                        if (row == null)
                        {
                            row = sheet.CreateRow(rowIndex);
                        }
                        rowIndex++;
                        row.HeightInPoints = rowHeight;
                        row.CreateCell(cellIndex).SetCellValue(rowNumber);
                        rowNumber++;
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;

                        row.CreateCell(cellIndex).SetCellValue(instrument.Code);
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;

                        row.CreateCell(cellIndex).SetCellValue(instrument.Name);
                        row.GetCell(cellIndex).CellStyle = contentLeftStyle;
                        cellIndex++;

                        row.CreateCell(cellIndex).SetCellValue(instrument.Model);
                        row.GetCell(cellIndex).CellStyle = contentLeftStyle;
                        cellIndex++;

                        row.CreateCell(cellIndex).SetCellValue(instrument.StockNum.ToString("G0"));
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;

                        row.CreateCell(cellIndex).SetCellValue(instrument.UnitName);
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;

                        row.CreateCell(cellIndex).SetCellValue(instrument.Floor);
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;

                        i++;
                        if (i > pageOne)
                        {
                            t++;
                        }
                    }

                    #region 每页不够用空行填充
                    if (pageIndex % 2 == 0)
                    {
                        cellIndex = 8;
                    }
                    else
                    {
                        cellIndex = 0;
                    }
                    int totalCount = cupinstrumentList.Count;
                    if (totalCount < pageOne)
                    {
                        int fCount = pageOne - totalCount;
                        for (int f = 0; f < fCount; f++)
                        {
                            row = sheet.GetRow(rowIndex);
                            if (row == null)
                            {
                                row = sheet.CreateRow(rowIndex);
                            }
                            rowIndex++;
                            row.HeightInPoints = rowHeight;
                            row.CreateCell(cellIndex).SetCellValue(rowNumber);
                            rowNumber++;
                            row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                            cellIndex++;

                            row.CreateCell(cellIndex).SetCellValue("");
                            row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                            cellIndex++;

                            row.CreateCell(cellIndex).SetCellValue("");
                            row.GetCell(cellIndex).CellStyle = contentLeftStyle;
                            cellIndex++;

                            row.CreateCell(cellIndex).SetCellValue("");
                            row.GetCell(cellIndex).CellStyle = contentLeftStyle;
                            cellIndex++;

                            row.CreateCell(cellIndex).SetCellValue("");
                            row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                            cellIndex++;

                            row.CreateCell(cellIndex).SetCellValue("");
                            row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                            cellIndex++;

                            row.CreateCell(cellIndex).SetCellValue("");
                            row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                            if (pageIndex % 2 == 0)
                            {
                                cellIndex = 8;
                            }
                            else
                            {
                                cellIndex = 0;
                            }
                        }
                    }
                    else
                    {
                        totalCount = totalCount - pageOne;
                        int fCount = k - (totalCount % k);
                        for (int f = 0; f < fCount; f++)
                        {
                            row = sheet.GetRow(rowIndex);
                            if (row == null)
                            {
                                row = sheet.CreateRow(rowIndex);
                            }
                            rowIndex++;
                            row.HeightInPoints = rowHeight;
                            row.CreateCell(cellIndex).SetCellValue(rowNumber);
                            rowNumber++;
                            row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                            cellIndex++;

                            row.CreateCell(cellIndex).SetCellValue("");
                            row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                            cellIndex++;

                            row.CreateCell(cellIndex).SetCellValue("");
                            row.GetCell(cellIndex).CellStyle = contentLeftStyle;
                            cellIndex++;

                            row.CreateCell(cellIndex).SetCellValue("");
                            row.GetCell(cellIndex).CellStyle = contentLeftStyle;
                            cellIndex++;

                            row.CreateCell(cellIndex).SetCellValue("");
                            row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                            cellIndex++;

                            row.CreateCell(cellIndex).SetCellValue("");
                            row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                            cellIndex++;

                            row.CreateCell(cellIndex).SetCellValue("");
                            row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                            if (pageIndex % 2 == 0)
                            {
                                cellIndex = 8;
                            }
                            else
                            {
                                cellIndex = 0;
                            }
                        }
                    }
                    #endregion


                    //sheet.SetRowBreak(rowIndex);
                    rowIndex++;
                }
            }

            using (MemoryStream ms = new MemoryStream())
            {
                workbook.Write(ms);
                workbook.Close();
                ms.Flush();
                return ms;
            }


        }

        /// <summary>
        /// 批量提交
        /// </summary>
        /// <param name="list">仪器录入集合数据</param>
        /// <param name="operateType">操作类型（0：保存；1：提交）</param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("instrument:schoolinstrument:add,instrument:schoolinstrument:edit")]
        public async Task<ActionResult> SaveInstrumentBatchFormJson(List<SchoolInstrumentInputModel> list, int operateType)
        {
            TData<string> obj = new TData<string>();
            string tipMsg = "";
            OperatorInfo operatorInfo = await Operator.Instance.Current();
            ConfigSetListParam param = new ConfigSetListParam();
            param.TypeCode = "1SYYQ-2YQRK-1YQLR";
            param.ModuleType = ConfigSetModuleTypeEnum.FormRequired.ParseToInt();
            TData<List<ConfigSetEntity>> configObj = await configSetBLL.GetListByCode(param);
            if (configObj.Tag == 1 && configObj.Data.Count > 0)
            {
                string returnTips = "";

                //判断供应商是否必填
                var listSupplier = configObj.Data.Where(a => a.TypeCode.Equals("1SYYQ-2YQRK-1YQLR-SupplierName") && a.ConfigValue.Equals("1")).ToList();
                if (listSupplier.Count > 0)
                {
                    var listSaveSupplier = list.Where(a => a.SupplierName == "").ToList();
                    if (listSaveSupplier.Count > 0)
                    {
                        returnTips += "配置了“ 供应商”必填，提交数据中未填写供应商信息！<br/>";
                    }
                }

                //判断品牌是否必填
                var listBrand = configObj.Data.Where(a => a.TypeCode.Equals("1SYYQ-2YQRK-1YQLR-Brand") && a.ConfigValue.Equals("1")).ToList();
                if (listBrand.Count > 0)
                {
                    var listSaveBrand = list.Where(a => a.Brand == "").ToList();
                    if (listSaveBrand.Count > 0)
                    {
                        returnTips += $"配置了“ 品牌”必填，第【{string.Join("、", listSaveBrand.Select(a => a.RowIndex))}】行未填写！<br/>";
                    }
                }

                //判断单价是否必填
                var listPrice = configObj.Data.Where(a => a.TypeCode.Equals("1SYYQ-2YQRK-1YQLR-Price") && a.ConfigValue.Equals("1")).ToList();
                if (listPrice.Count > 0)
                {
                    var listSavePrice = list.Where(a => a.Price == null).ToList();
                    if (listSavePrice.Count > 0)
                    {
                        returnTips += $"配置了“ 单价”必填，第【{string.Join("、", listSavePrice.Select(a => a.RowIndex))}】行未填写！<br/>";
                    }
                }

                //判断仪器图片是否必填
                var listImg = configObj.Data.Where(a => a.TypeCode.Equals("1SYYQ-2YQRK-1YQLR-Image") && a.ConfigValue.Equals("1")).ToList();
                if (listImg.Count > 0)
                {
                    var listSaveImg = list.Where(a => a.AttachId == 0).ToList();
                    if (listSaveImg.Count > 0)
                    {
                        returnTips += $"配置了“  仪器图片”必填，第【{string.Join("、", listSaveImg.Select(a => a.RowIndex))}】行未上传仪器图片！<br/>";
                    }
                }
                   

                if (!string.IsNullOrEmpty(returnTips))
                {
                    obj.Tag = 0;
                    obj.Message = returnTips;
                    return Json(obj);
                }
            }

            //判断是否设置了2个以上学段
            TData<List<StaticDictionaryEntity>> staticObj = await userSchoolStageSubjectBLL.GetSchoolCourseByUser();
            if (string.IsNullOrEmpty(staticObj.Message))
            {
                //学科是否有不存在的
                var reportCourse = list.GroupBy(f => new { f.Course }).ToList();
                var listStage = await userSchoolStageSubjectBLL.GetSchoolStageByUser(0);
                var listCourse = await userSchoolStageSubjectBLL.GetSubjectByUser(0);
                if (listStage.Data.Count() == 0 || listCourse.Data.Count() == 0)
                {
                    obj.Tag = 0;
                    obj.Message = "适用学段或适用学科未获得授权，不能填报！";
                    return Json(obj);
                }
                else
                {
                    var courseList = listCourse.Data.ToList();
                    foreach (var item in reportCourse)
                    {
                        if (!courseList.Exists(f => f.DicName.Equals(item.Key.Course)))
                        {
                            tipMsg += item.Key.Course + "、";
                        }
                    }
                    if (!string.IsNullOrEmpty(tipMsg))
                    {
                        tipMsg = tipMsg.TrimEnd('、');
                        obj.Tag = 0;
                        obj.Message = $"您未被授权管理学科({tipMsg}),所以不能录入关于这些学科的仪器信息。";
                        return Json(obj);
                    }
                }
            }

            //判断输入的分类代码是否存在
            List<InstrumentModel> listCompare = await instrumentStandardBLL.GetCheckCodeList();
            foreach (SchoolInstrumentInputModel input in list)
            {
                if(input.Code.Length == 11)
                {
                    if (!listCompare.Exists(f => f.Code.Substring(0,9) == input.Code.Substring(0,9)))
                    {
                        tipMsg += $"第{input.RowIndex}行,分类代码“{input.Code}”在基础库中不存在！<br/>";
                    }
                    else
                    {
                        var o = listCompare.Where(f => f.Code == input.Code).FirstOrDefault();
                        if (o != null)
                        {
                            input.OriginalCode = input.Code;
                        }
                        else
                        {
                            input.OriginalCode = input.Code;
                            input.Code = input.OriginalCode.Substring(0, 9) + "00";
                        }
                    }
                }
                else
                {
                    tipMsg += $"第{input.RowIndex}行,分类代码“{input.Code}”必须为11位！<br/>";
                }
            }
            if (!string.IsNullOrEmpty(tipMsg))
            {
                obj.Tag = 0;
                obj.Message = tipMsg;
                return Json(obj);
            }

            //处理手动录入，只填写编码未选择，可以填写末级分类，也能填写规格属性的分类
            List<SchoolInstrumentInputModel> listSupose = list.Where(a => a.InstrumentStandardId == 0).ToList();
            if (listSupose.Count > 0)
            {
                foreach (SchoolInstrumentInputModel pd in listSupose)
                {
                    var o = listCompare.Where(a => a.Code == pd.Code).FirstOrDefault();
                    if (o.ClassType == 2 || o.IsLast == 0)
                    {
                        pd.InstrumentStandardId = o.Pid;
                        pd.ModelStandardId = o.Id;
                    }
                    else
                    {
                        pd.InstrumentStandardId = o.Id;
                    }
                }
            }
          
            obj = await schoolInstrumentBLL.SaveBatchFormJson(list, operateType);
            return Json(obj);
        }

        /// <summary>
        /// 单条修改保存
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("instrument:schoolinstrument:add,instrument:schoolinstrument:edit")]
        public async Task<ActionResult> SaveInstrumentFormJson(SchoolInstrumentInputModel entity)
        {
            TData<string> obj = new TData<string>();
            ConfigSetListParam param = new ConfigSetListParam();
            param.TypeCode = "1SYYQ-2YQRK-1YQLR";
            param.ModuleType = ConfigSetModuleTypeEnum.FormRequired.ParseToInt();
            TData<List<ConfigSetEntity>> configObj = await configSetBLL.GetListByCode(param);
            if (configObj.Tag == 1 && configObj.Data.Count > 0)
            {
                string errorMsg = "";
                foreach (var item in configObj.Data)
                {
                    if (item.ConfigValue == "1")
                    {
                        string typecode = item.TypeCode.Replace("1SYYQ-2YQRK-1YQLR-", "");
                        if (typecode == "Brand")
                        {
                            if (string.IsNullOrEmpty(entity.Brand))
                            {
                                errorMsg += (item.VerifyHint + "<br/>");
                            }
                        }
                        else if (typecode == "SupplierName")
                        {
                            if (string.IsNullOrEmpty(entity.SupplierName))
                            {
                                errorMsg += (item.VerifyHint + "<br/>");
                            }
                        }
                        else if (typecode == "Image")
                        {
                            if (!(entity.AttachmentId > 0))
                            {
                                errorMsg += (item.VerifyHint + "<br/>");
                            }
                        }                   
                        else if (typecode == "Price")
                        {
                            if (!entity.Price.HasValue)
                            {
                                errorMsg += (item.VerifyHint + "<br/>");
                            }
                        }
                    }
                }
                if (errorMsg != "")
                {
                    obj.Tag = 0;
                    obj.Message = errorMsg;
                    return Json(obj);
                }
            }

            obj = await schoolInstrumentBLL.SaveInstrumentForm(entity);
            return Json(obj);
        }

        /// <summary>
        /// 临时保存
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("instrument:schoolinstrument:add,instrument:schoolinstrument:edit")]
        public async Task<ActionResult> SaveTempFormJson(List<SchoolTempInstrumentInputModel> list)
        {
            TData<string> obj = new TData<string>();
            obj = await schoolInstrumentBLL.SaveTempFormJson(list);
            return Json(obj);
        }

        /// <summary>
        /// 生成二维码
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("instrument:schoolinstrument:edit")]
        public async Task<ActionResult> CreateQRCodeFormJson(string ids)
        {
            TData obj = await schoolInstrumentBLL.CreateQRCodeForm(ids);
            return Json(obj);
        }

        /// <summary>
        /// 二维码信息
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpGet]
        [AuthorizeFilter("instrument:schoolinstrument:edit")]
        public async Task<ActionResult> GetQRCodeJson(long id)
        {
            TData<SchoolInstrumentEntity> obj = await schoolInstrumentBLL.GetQRCodeJson(id);
            if (obj.Data != null)
            {
                OperatorInfo operatorinfo = await Operator.Instance.Current();
                obj.Data.QrCodeUrl = GlobalContext.SystemConfig.WebSite + "/m/e/" + obj.Data.QRCode;
                string base64 = Convert.ToBase64String(QrCodeGenerater.CreateQrCode(obj.Data.QrCodeUrl));
                obj.Data.QrCodeData = "data:image/png;base64," + base64;

                //根据当前单位Id获取单位Logo
                TData<UnitEntity> objUnit = await unitBLL.GetEntity(operatorinfo.UnitId.Value);
                if (objUnit != null && objUnit.Data != null && !string.IsNullOrEmpty(objUnit.Data.Logo))
                {
                    obj.Data.Logo = objUnit.Data.Logo;
                }
                else
                {
                    obj.Data.Logo = "/image/portrait.png";
                }
            }
            return Json(obj);
        }


        /// <summary>
        /// 修复编码
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<ActionResult> BatchEditCode()
        {
            TData obj = new TData();
            TData<List<SchoolInstrumentEntity>> objList = await schoolInstrumentBLL.GetList(new SchoolInstrumentListParam() { });
            if (objList.Tag == 1)
            {
                List<SchoolInstrumentEntity> list = objList.Data;
                //foreach (SchoolInstrumentModel m in list)
                //{

                //}
            }
            obj.Tag = 1;
            obj.Message = "处理成功";
            return Json(obj);
        }

        #endregion
    }
}
