﻿namespace Dqy.Syjx.Web.CommonLib.Huawei
{
    using System;
    using System.Collections.Generic;
    using System.Security.Cryptography;
    using System.Text;

    public class BaseConfig
    {
        private string appId;
        private string appSecret;

        public BaseConfig(string appId, string appSecret)
        {
            this.appId = appId;
            this.appSecret = appSecret;
        }

        // int bodyLength = JsonConvert.SerializeObject(param).GetBytes(Encoding.UTF8).Length;
        public Dictionary<string, string> GetHeaders(int bodyLength)
        {
            var headers = new Dictionary<string, string>();
            long timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            headers.Add("openTimestamp", timestamp.ToString());
            headers.Add("openAppId", this.appId);
            string content = this.appId + timestamp + bodyLength;
            headers.Add("openSign", SignUtils.Generate(content, this.appSecret));
            return headers;
        }
    }

    public static class SignUtils
    {
        public static string Generate(string content, string secret)
        {
            string sign = EncryptUtils.Sha256(content);
            sign = EncryptUtils.Md5Hex(sign + secret);
            return sign;
        }
    }

    public static class EncryptUtils
    {
        private static readonly char[] HEX_DIGITS = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f' };

        /// <summary>
        /// 32-bit MD5 encryption
        /// </summary>
        public static string Md5Hex(string s)
        {
            byte[] result = Digest("MD5", Encoding.UTF8.GetBytes(s));
            return Hex(result);
        }

        /// <summary>
        /// 32-bit SHA256 encryption
        /// </summary>
        public static string Sha256(string s)
        {
            byte[] result = Digest("SHA-256", Encoding.UTF8.GetBytes(s));
            return Hex(result);
        }

        private static byte[] Digest(string algorithm, byte[] data)
        {
            using (var hash = HashAlgorithm.Create(algorithm))
            {
                if (hash == null)
                {
                    throw new ArgumentException($"Algorithm {algorithm} not supported", nameof(algorithm));
                }
                return hash.ComputeHash(data);
            }
        }

        private static string Hex(byte[] data)
        {
            char[] result = new char[data.Length * 2];
            int c = 0;
            foreach (byte b in data)
            {
                result[c++] = HEX_DIGITS[(b >> 4) & 0xf];
                result[c++] = HEX_DIGITS[b & 0xf];
            }
            return new string(result);
        }
    }
}
