﻿using System.Net.Http.Headers;
using System.Net.Http;
using System.Threading.Tasks;
using System.Threading;
using System;
using Dqy.Syjx.Cache.Factory;
using Microsoft.AspNetCore.Http;
using Senparc.NeuChar.NeuralSystems;
using Dqy.Syjx.Util;
using Newtonsoft.Json;
using Refit;
using Dqy.Syjx.Service.HttpService.Wxzhjy.Models;

namespace Dqy.Syjx.Service.HttpService.Wxzhjy
{
    public class WxzhjyApiHandler : DelegatingHandler
    {
        private readonly IHttpContextAccessor _httpContextAccessor;

        public WxzhjyApiHandler(IHttpContextAccessor httpContextAccessor)
        {
            InnerHandler = new HttpClientHandler();
            _httpContextAccessor = httpContextAccessor;
        }

        protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
        {
            var context = _httpContextAccessor.HttpContext;

            var code = context.Request.Query["code"];

            var tokeusing System.Net.Http.Headers;
using System.Net.Http;
using System.Threading.Tasks;
using System.Threading;
using System;
using Dqy.Syjx.Cache.Factory;
using Microsoft.AspNetCore.Http;
using Senparc.NeuChar.NeuralSystems;
using Dqy.Syjx.Util;
using Newtonsoft.Json;
using Refit;
using Dqy.Syjx.Service.HttpService.Wxzhjy.Models;

namespace Dqy.Syjx.Service.HttpService.Wxzhjy
{
    public class WxzhjyApiHandler : DelegatingHandler
    {
        private readonly IHttpContextAccessor _httpContextAccessor;

        public WxzhjyApiHandler(IHttpContextAccessor httpContextAccessor)
        {
            InnerHandler = new HttpClientHandler();
            _httpContextAccessor = httpContextAccessor;
        }

        protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
        {
            var context = _httpContextAccessor.HttpContext;

            var code = context.Request.Query["code"];

            var token = string.Empty;

            if (!string.IsNullOrEmpty(code))
            {
                token = CacheFactory.Cache.GetCache<string>($"{code}_token");
            }
            else
            {
                token = CacheFactory.Cache.GetCache<string>($"client_token");
            }

            request.Headers.Add("token", token);

            LogHelper.Info($"

            var responseMessage = await base.SendAsync(request, cancellationToken);

            responseMessage.EnsureSuccessStatusCode();

            var content = await responseMessage.Content.ReadAsStringAsync(cancellationToken);

            LogHelper.Info($"

            var response = JsonConvert.DeserializeObject<WxzhjyApiResponse>(content);

            if (response.Code != "0")
            {
                LogHelper.Error($"
                throw await ApiException.Create(response.Msg, request, request.Method, responseMessage, new RefitSettings());
            }

            return responseMessage;
        }
    }
}
