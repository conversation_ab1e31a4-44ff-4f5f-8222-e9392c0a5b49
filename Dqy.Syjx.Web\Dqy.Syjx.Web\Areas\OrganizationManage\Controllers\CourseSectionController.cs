﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using System.Web;
using Microsoft.AspNetCore.Mvc;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Entity;
using Dqy.Syjx.Model;
using Dqy.Syjx.Web.Controllers;
using Dqy.Syjx.Entity.OrganizationManage;
using Dqy.Syjx.Business.OrganizationManage;
using Dqy.Syjx.Model.Param.OrganizationManage;
using Dqy.Syjx.Model.Input.OrganizationManage;
using NPOI.POIFS.FileSystem;
using Dqy.Syjx.Model.Result.OrganizationManage;
using Dqy.Syjx.Web.Code;

namespace Dqy.Syjx.Web.Areas.OrganizationManage.Controllers
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-09-24 10:32
    /// 描 述：控制器类
    /// </summary>
    [Area("OrganizationManage")]
    public class CourseSectionController :  BaseController
    {
        private CourseSectionBLL courseSectionBLL = new CourseSectionBLL();
        private UnitScheduleBLL unitSchedulBLL = new UnitScheduleBLL();

        #region 视图功能
        [AuthorizeFilter("organization:coursesection:view")]
        public ActionResult CourseSectionIndex()
        {
            return View();
        }

        public ActionResult CourseSectionForm()
        {
            return View();
        }


        [AuthorizeFilter("organization:coursesupersection:view")]
        public ActionResult CourseSuperIndex()
        {
            return View();
        }

        public ActionResult CourseSuperForm()
        {
            return View();
        }

        public ActionResult UnitScheduleForm()
        {
            return View();
        }

        public ActionResult UnitScheduleCopyForm()
        {
            return View();
        }

        public ActionResult CourseSectionCopyForm()
        {
            return View();
        }

        public ActionResult UnitScheduleSuperForm()
        {
            return View();
        }
        #endregion

        #region 获取数据
        [HttpGet]
        public async Task<ActionResult> GetListJson(CourseSectionListParam param)
        {
            TData<List<CourseSectionEntity>> obj = await courseSectionBLL.GetList(param);
            return Json(obj);
        }

        /// <summary>
        /// 学校查询课程节次信息
        /// </summary>
        /// <param name="gradeId">年级Id</param>
        /// <param name="weekId">星期Id</param>
        /// <returns></returns>
        [HttpGet]
        [AuthorizeFilter("organization:coursesection:search")]
        public async Task<ActionResult> GetCourseSectionList(int gradeId, int weekId)
        {
            TData<List<CourseSectionEntity>> obj = await courseSectionBLL.GetCourseSectionList(gradeId, weekId);
            return Json(obj);
        }

        /// <summary>
        /// 超管查询课程节次信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AuthorizeFilter("organization:coursesection:search")]
        public async Task<ActionResult> GetCourseSuperSectionList()
        {
            TData<List<CourseSectionEntity>> obj = await courseSectionBLL.GetCourseSuperSectionList();
            return Json(obj);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="gradeId"></param>
        /// <param name="weekId"></param>
        /// <returns></returns>
        [HttpGet]
        [AuthorizeFilter("organization:coursesection:search")]
        public async Task<ActionResult> GetGradeWeekList(int gradeId, int weekId)
        {
            TData<List<GradeModel>> obj = await courseSectionBLL.GetGradeWeekList(gradeId, weekId);
            return Json(obj);
        }

        /// <summary>
        /// 根据当前时间及年级获取下拉数据信息
        /// </summary>
        /// <param name="gradeId">年级Id</param>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> GetCourseSelectList(int gradeId, string dtDate = null)
        {
            TData<List<CourseSelectModel>> obj = new TData<List<CourseSelectModel>>();
            OperatorInfo operatorinfo = await Operator.Instance.Current();
            if (operatorinfo==null)
            {
                obj.Tag = -1;
                obj.Message = "登录超时。";
                return Json(obj);
            }
           obj = await courseSectionBLL.GetCourseSelectList(gradeId, operatorinfo.UnitId ?? 0, dtDate);
            return Json(obj);
        }
        #endregion

        #region 提交数据
        
        /// <summary>
        /// 批量保存
        /// </summary>
        /// <param name="list"></param>
        /// <param name="gradeId"></param>
        /// <param name="weekId"></param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("organization:coursesection:add,organization:coursesection:edit")]
        public async Task<ActionResult> SaveBatchForm(List<CourseSectionInputModel> list, int gradeId, int weekId)
        {
            TData<string> obj = await courseSectionBLL.SaveBatchForm(list, gradeId, weekId);
            return Json(obj);
        }

        /// <summary>
        /// 批量复制课程
        /// </summary>
        /// <param name="list"></param>
        /// <param name="gradeId"></param>
        /// <param name="weekId"></param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("organization:coursesection:add,organization:coursesection:edit")]
        public async Task<ActionResult> SaveBatchCopyForm(List<int> listGradeId, List<int> listWeekId, int gradeId, int weekId)
        {
            TData<string> obj = await courseSectionBLL.SaveBatchCopyForm(listGradeId, listWeekId, gradeId, weekId);
            return Json(obj);
        }

        /// <summary>
        /// 超管批量保存数据
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("organization:coursesection:add,organization:coursesection:edit")]
        public async Task<ActionResult> SaveSuperBatchForm(List<CourseSectionInputModel> list)
        {
            TData<string> obj = await courseSectionBLL.SaveSuperBatchForm(list);
            return Json(obj);
        }
        #endregion
    }
}
