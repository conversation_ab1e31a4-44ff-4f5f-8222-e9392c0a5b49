﻿using Dqy.Syjx.Enum;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Util.Tools;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Text;
using System.Threading.Tasks;

namespace Dqy.Syjx.Util
{
    public static class SendHelper
    {
        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="url"></param>
        /// <param name="data"></param>
        /// <param name="contentType">application/x-www-form-urlencoded</param>
        /// <returns></returns>
        public static async Task<T> SendPostAsync<T>(string url, string data, string contentType = "application/json")
        {
            HttpClientHandler handler = new HttpClientHandler() { AutomaticDecompression = DecompressionMethods.GZip };
            HttpClient httpClient = new HttpClient(handler);

            HttpContent httpContent = new StringContent(data, Encoding.UTF8, contentType);

            HttpResponseMessage response = await httpClient.PostAsync(url, httpContent);
            response.EnsureSuccessStatusCode();
            string resultStr = await response.Content.ReadAsStringAsync();
            return Util.Tools.JsonExtention.ToObject<T>(resultStr);
        }

        public static async Task<string> SendAsync(string url, string data, string contentType = "application/json")
        {
            HttpClientHandler handler = new HttpClientHandler() { AutomaticDecompression = DecompressionMethods.GZip };
            HttpClient httpClient = new HttpClient(handler);

            HttpContent httpContent = new StringContent(data, Encoding.UTF8, contentType);

            HttpResponseMessage response = await httpClient.PostAsync(url, httpContent);
            response.EnsureSuccessStatusCode();
            string resultStr = await response.Content.ReadAsStringAsync();
            return resultStr;
        }

        public static async Task<byte[]> SendPostAsyncToByte(string url, string data)
        {
            HttpClientHandler handler = new HttpClientHandler() { AutomaticDecompression = DecompressionMethods.GZip };
            HttpClient httpClient = new HttpClient(handler);

            HttpContent httpContent = new StringContent(data, Encoding.UTF8, "text/json");

            HttpResponseMessage response = await httpClient.PostAsync(url, httpContent);
            response.EnsureSuccessStatusCode();
            var stream = await response.Content.ReadAsStreamAsync();
            byte[] bytes = new byte[stream.Length];
            stream.Read(bytes, 0, bytes.Length);
            stream.Seek(0, SeekOrigin.Begin);
            return bytes;
        }

        public static T PostResponse<T>(string url, string postData) where T : class, new()
        {
            T result = default(T);

            HttpContent httpContent = new StringContent(postData);
            httpContent.Headers.ContentType = new MediaTypeHeaderValue("application/json");
            httpContent.Headers.ContentType.CharSet = "utf-8";
            using (HttpClient httpClient = new HttpClient())
            {
                HttpResponseMessage response = httpClient.PostAsync(url, httpContent).Result;

                if (response.IsSuccessStatusCode)
                {
                    Task<string> t = response.Content.ReadAsStringAsync();
                    //Newtonsoft.Json
                    result = JsonExtention.ToObject<T>(t.Result);
                }
            }
            return result;
        }

        /// <summary>
        /// Get请求方法
        /// </summary>
        /// <param name="url"></param>
        /// <returns></returns>
        public static async Task<TData<string>> SendGetAsync(string url, string authorization = "")
        {
            TData<string> obj = new TData<string>();
            try
            {
                using (var clientHandler = new HttpClientHandler())
                {
                    clientHandler.ServerCertificateCustomValidationCallback = (sender, cert, chain, sslPolicyErrors) => { return true; };
                    clientHandler.SslProtocols = System.Security.Authentication.SslProtocols.Tls12 | System.Security.Authentication.SslProtocols.Tls13 | System.Security.Authentication.SslProtocols.Tls11;
                    using (var httpClient = new HttpClient(clientHandler))
                    {
                        if (!string.IsNullOrEmpty(authorization))
                        {
                            httpClient.DefaultRequestHeaders.Add("Authorization", authorization);
                        }
                        using (var response = await httpClient.GetAsync(url))
                        {
                            if (response.StatusCode == HttpStatusCode.RequestTimeout)
                            {
                                obj.Tag = 0;
                                obj.Data = HttpStatusCode.RequestTimeout.ToString();
                                return obj;
                            }
                            obj.Tag = 1;
                            obj.Data = await response.Content.ReadAsStringAsync();
                            return obj;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                obj.Tag = 0;
                obj.Data = ex.Message;
                LogHelper.Info($"SendGetAsync 异常：{ex.Message}");
            }
            return obj;
        }
        public static async Task<TData<string>> SendGetAsync(string url, Dictionary<string, string> extHeaders)
        {
            TData<string> obj = new TData<string>();
            try
            {
                using (var clientHandler = new HttpClientHandler())
                {
                    clientHandler.ServerCertificateCustomValidationCallback = (sender, cert, chain, sslPolicyErrors) => { return true; };
                    clientHandler.SslProtocols = System.Security.Authentication.SslProtocols.Tls12 | System.Security.Authentication.SslProtocols.Tls13 | System.Security.Authentication.SslProtocols.Tls11;
                    using (var httpClient = new HttpClient(clientHandler))
                    {
                        if (extHeaders != null)
                        {
                            foreach (var item in extHeaders)
                            {
                                httpClient.DefaultRequestHeaders.Add(item.Key, item.Value);
                            }
                        }
                        using (var response = await httpClient.GetAsync(url))
                        {
                            if (response.StatusCode == HttpStatusCode.RequestTimeout)
                            {
                                obj.Tag = 0;
                                obj.Data = HttpStatusCode.RequestTimeout.ToString();
                                return obj;
                            }
                            obj.Tag = 1;
                            obj.Data = await response.Content.ReadAsStringAsync();
                            return obj;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                obj.Tag = 0;
                obj.Data = ex.Message;
                LogHelper.Info($"SendGetAsyncByHeader 异常：{ex.Message}");
            }
            return obj;
        }

        /// <summary>
        /// 设置header的Get请求方法
        /// </summary>
        /// <param name="url"></param>
        /// <param name="headerParam"></param>
        /// <param name="headerParamValue"></param>
        /// <returns></returns>
        public static async Task<TData<string>> SendGetAsyncByHeader(string url,string headerParam = "", string headerParamValue = "")
        {
            TData<string> obj = new TData<string>();
            try
            {
                using (var clientHandler = new HttpClientHandler())
                {
                    clientHandler.ServerCertificateCustomValidationCallback = (sender, cert, chain, sslPolicyErrors) => { return true; };
                    clientHandler.SslProtocols = System.Security.Authentication.SslProtocols.Tls12 | System.Security.Authentication.SslProtocols.Tls13 | System.Security.Authentication.SslProtocols.Tls11;
                    using (var httpClient = new HttpClient(clientHandler))
                    {
                        if (!string.IsNullOrEmpty(headerParam) && !string.IsNullOrEmpty(headerParamValue))
                        {
                            httpClient.DefaultRequestHeaders.Add(headerParam, headerParamValue);
                        }
                        using (var response = await httpClient.GetAsync(url))
                        {
                            if (response.StatusCode == HttpStatusCode.RequestTimeout)
                            {
                                obj.Tag = 0;
                                obj.Data = HttpStatusCode.RequestTimeout.ToString();
                                return obj;
                            }
                            obj.Tag = 1;
                            obj.Data = await response.Content.ReadAsStringAsync();
                            return obj;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                obj.Tag = 0;
                obj.Data = ex.Message;
                LogHelper.Info($"SendGetAsyncByHeader 异常：{ex.Message}");
            }
            return obj;
        }

        /// <summary>
        /// Post请求方法
        /// </summary>
        /// <param name="url"></param>
        /// <param name="data"></param>
        /// <param name="authorization"></param>
        /// <returns></returns>
        public static async Task<TData<string>> SendPostAsync(string url, string data, string authorization = "")
        {
            TData<string> obj = new TData<string>();
            try
            {
                using (var clientHandler = new HttpClientHandler())
                {
                    clientHandler.ServerCertificateCustomValidationCallback = (sender, cert, chain, sslPolicyErrors) => { return true; };
                    clientHandler.SslProtocols = System.Security.Authentication.SslProtocols.Tls12 | System.Security.Authentication.SslProtocols.Tls13 | System.Security.Authentication.SslProtocols.Tls11;
                    using (var httpClient = new HttpClient(clientHandler))
                    {
                        if (!string.IsNullOrEmpty(authorization))
                        {
                            httpClient.DefaultRequestHeaders.Add("Authorization", authorization);
                        }
                        HttpContent httpContent = new StringContent(data, Encoding.UTF8, "application/json");
                        using (var response = await httpClient.PostAsync(url, httpContent))
                        {
                            if (response.StatusCode == HttpStatusCode.RequestTimeout)
                            {
                                obj.Tag = 0;
                                obj.Data = HttpStatusCode.RequestTimeout.ToString();
                                return obj;
                            }
                            obj.Tag = 1;
                            obj.Data = await response.Content.ReadAsStringAsync();
                            return obj;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                obj.Tag = 0;
                obj.Data = ex.Message;
                LogHelper.Info($"SendPostAsync 异常：{ex.Message}");
            }
            return obj;
        }
        /// <summary>
        /// Post请求方法
        /// </summary>
        /// <param name="url"></param>
        /// <param name="data"></param>
        /// <param name="extHeaders"></param>
        /// <returns></returns>
        public static async Task<TData<string>> SendPostAsync(string url, string data, Dictionary<string, string> extHeaders)
        {
            TData<string> obj = new TData<string>();
            try
            {
                using (var clientHandler = new HttpClientHandler())
                {
                    clientHandler.ServerCertificateCustomValidationCallback = (sender, cert, chain, sslPolicyErrors) => { return true; };
                    clientHandler.SslProtocols = System.Security.Authentication.SslProtocols.Tls12 | System.Security.Authentication.SslProtocols.Tls13 | System.Security.Authentication.SslProtocols.Tls11;
                    using (var httpClient = new HttpClient(clientHandler))
                    {                       
                        if (extHeaders != null)
                        {
                            foreach (var item in extHeaders)
                            {
                                httpClient.DefaultRequestHeaders.Add(item.Key, item.Value);
                            }
                        }
                        HttpContent httpContent = new StringContent(data, Encoding.UTF8, "application/json");
                        using (var response = await httpClient.PostAsync(url, httpContent))
                        {
                            if (response.StatusCode == HttpStatusCode.RequestTimeout)
                            {
                                obj.Tag = 0;
                                obj.Data = HttpStatusCode.RequestTimeout.ToString();
                                return obj;
                            }
                            obj.Tag = 1;
                            obj.Data = await response.Content.ReadAsStringAsync();
                            return obj;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                obj.Tag = 0;
                obj.Data = ex.Message;
                LogHelper.Info($"SendPostAsync 异常：{ex.Message}");
            }
            return obj;
        }

        /// <summary>
        /// Post请求方法
        /// </summary>
        /// <param name="url"></param>
        /// <param name="data"></param>
        /// <param name="authorization"></param>
        /// <returns></returns>
        public static async Task<TData<string>> SendPostAsync(string url, string data, string headerParam, string headerParamValue)
        {
            TData<string> obj = new TData<string>();
            try
            {
                using (var clientHandler = new HttpClientHandler())
                {
                    clientHandler.ServerCertificateCustomValidationCallback = (sender, cert, chain, sslPolicyErrors) => { return true; };
                    clientHandler.SslProtocols = System.Security.Authentication.SslProtocols.Tls12 | System.Security.Authentication.SslProtocols.Tls13 | System.Security.Authentication.SslProtocols.Tls11;
                    using (var httpClient = new HttpClient(clientHandler))
                    {
                        httpClient.DefaultRequestHeaders.Add("Content-Type", "application/json");
                        if (!string.IsNullOrEmpty(headerParam))
                        {
                            httpClient.DefaultRequestHeaders.Add(headerParam, headerParamValue);
                        }
                        HttpContent httpContent = new StringContent(data, Encoding.UTF8, "application/json");
                        using (var response = await httpClient.PostAsync(url, httpContent))
                        {
                            if (response.StatusCode == HttpStatusCode.RequestTimeout)
                            {
                                obj.Tag = 0;
                                obj.Data = HttpStatusCode.RequestTimeout.ToString();
                                return obj;
                            }
                            obj.Tag = 1;
                            obj.Data = await response.Content.ReadAsStringAsync();
                            return obj;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                obj.Tag = 0;
                obj.Data = ex.Message;
                LogHelper.Info($"SendPostAsync 异常：{ex.Message}");
            }
            return obj;
        }

        /// <summary>
        /// 设置httpHeader的Post请求
        /// </summary>
        /// <param name="url"></param>
        /// <param name="data"></param>
        /// <param name="headerParam"></param>
        /// <param name="headerParamValue"></param>
        /// <returns></returns>
        public static async Task<TData<string>> SendPostAsyncByHeader(string url, Dictionary<string, string> data, string headerParam = "",string headerParamValue="")
        {
            TData<string> obj = new TData<string>();
            try
            {
                using (var clientHandler = new HttpClientHandler())
                {
                    clientHandler.ServerCertificateCustomValidationCallback = (sender, cert, chain, sslPolicyErrors) => { return true; };
                    clientHandler.SslProtocols = System.Security.Authentication.SslProtocols.Tls12 | System.Security.Authentication.SslProtocols.Tls13 | System.Security.Authentication.SslProtocols.Tls11;
                    using (var httpClient = new HttpClient(clientHandler))
                    {
                        if (!string.IsNullOrEmpty(headerParam) && !string.IsNullOrEmpty(headerParamValue)) 
                        {
                            httpClient.DefaultRequestHeaders.Add(headerParam, headerParamValue);
                        }

                        Dictionary<string, string> map = data;
                        var mfdc = new System.Net.Http.MultipartFormDataContent();
                        mfdc.Headers.Add("ContentType", "multipart/form-data");//声明头部
                        foreach (string key in map.Keys)
                        {
                            mfdc.Add(new StringContent(map[key]), key);//参数, 内容在前,参数名称在后
                        }

                        using (var response = await httpClient.PostAsync(url, mfdc))
                        {
                            if (response.StatusCode == HttpStatusCode.RequestTimeout)
                            {
                                obj.Tag = 0;
                                obj.Data = HttpStatusCode.RequestTimeout.ToString();
                                return obj;
                            }
                            obj.Tag = 1;
                            obj.Data = await response.Content.ReadAsStringAsync();
                            return obj;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                obj.Tag = 0;
                obj.Data = ex.Message;
                LogHelper.Info($"SendPostAsyncByHeader 异常：{ex.Message}");
            }
            return obj;
        }


     
    }
}
