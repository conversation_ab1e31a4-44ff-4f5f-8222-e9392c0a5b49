﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.PersonManage;
using Dqy.Syjx.Model.Param.PersonManage;

namespace Dqy.Syjx.Service.PersonManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-10-26 10:59
    /// 描 述：服务类
    /// </summary>
    public class UserAwardInfoService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<UserAwardInfoEntity>> GetList(UserAwardInfoListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<UserAwardInfoEntity>> GetPageList(UserAwardInfoL
        }

        public async Task<List<UserAwardInfoEntity>> GetList(UserAwardInfoListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<UserAwardInfoEntity>> GetPageList(UserAwardInfoListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ListFilter(param, strSql);
            var list= await this.BaseRepository().FindList<UserAwardInfoEntity>(strSql.ToString(), filter.ToArray(), pagination);
            return list.ToList();
        }

        public async Task<UserAwardInfoEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<UserAwardInfoEntity>(id);
        }

        /// <summary>
        /// 根据Id获取获奖信息详情
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<UserAwardInfoEntity> GetEntityById(long id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(@"  SELECT * From (
                            SELECT
                            ua1.Id ,
                            ua1.BaseIsDelete ,
                            ua1.BaseCreateTime ,
                            ua1.BaseModifyTime ,
                            ua1.BaseCreatorId ,
                            ua1.BaseModifierId ,
                            ua1.BaseVersion ,
                            ua1.UnitId ,
                            ua1.UserId ,
                            ua1.AwardCategory ,
                            ua1.AwardLevel ,
                            ua1.AwardDate ,
                            ua1.Remark ,
                            sd2.DicName AS CategoryName ,
                            sd3.DicName AS LevelName ,
                            SU.RealName
	                        ,U.Name AS SchoolName
	                        ,UR.UnitId AS CountyId
                            FROM  up_UserAwardInfo	AS ua1
                            INNER JOIN  sys_static_dictionary AS sd2 ON sd2.BaseIsDelete = 0 AND ua1.AwardCategory = sd2.DictionaryId
                            INNER JOIN  sys_static_dictionary AS sd3 ON sd3.BaseIsDelete = 0 AND ua1.AwardLevel = sd3.DictionaryId

                            INNER JOIN  SysUser AS SU ON ua1.UserId = SU.Id
	                        INNER JOIN  up_Unit AS U ON ua1.UnitId = U.Id
	                        INNER JOIN  up_UnitRelation AS UR ON U.Id = UR.ExtensionObjId AND UR.ExtensionType = 3
                          ) as tb1 WHERE   Id = "+ id + " ");
            var list = await this.BaseRepository().FindList<UserAwardInfoEntity>(strSql.ToString());
            return list.FirstOrDefault();
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(UserAwardInfoEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }
        public async Task SaveTransForm(UserAwardInfoEntity entity,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }
        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
               strSql = $" update up_UserAwardInfo set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
               strSql = $"update up_UserAwardInfo set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<UserAwardInfoEntity, bool>> ListFilter(UserAwardInfoListParam param)
        {
            var expression = LinqExtensions.True<UserAwardInfoEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
            }
            return expression;
        }

        /// <summary>
        /// 获取获奖信息，（可能存在转校的问题，所以条件没有加UnitId，只加了UserId）
        /// </summary>
        /// <param name="param"></param>
        /// <param name="strSql"></param>
        /// <returns></returns>
        private List<DbParameter> ListFilter(UserAwardInfoListParam param, StringBuilder strSql)
        {
            strSql.Append(@"  SELECT * From (
                            SELECT
                            ua1.Id ,
                            ua1.BaseIsDelete ,
                            ua1.BaseCreateTime ,
                            ua1.BaseModifyTime ,
                            ua1.BaseCreatorId ,
                            ua1.BaseModifierId ,
                            ua1.BaseVersion ,
                            ua1.UnitId ,
                            ua1.UserId ,
                            ua1.AwardCategory ,
                            ua1.AwardLevel ,
                            ua1.AwardDate ,
                            ua1.Remark ,
                            sd2.DicName AS CategoryName ,
                            sd3.DicName AS LevelName ,
                            SU.RealName
	                        ,U.Name AS SchoolName
	                        ,UR.UnitId AS CountyId ,u21.Name AS CountyName ,ur22.UnitId AS CityId
                            ,se21.SchoolProp
                            FROM  up_UserAwardInfo	AS ua1
                            INNER JOIN  sys_static_dictionary AS sd2 ON sd2.BaseIsDelete = 0 AND ua1.AwardCategory = sd2.DictionaryId
                            INNER JOIN  sys_static_dictionary AS sd3 ON sd3.BaseIsDelete = 0 AND ua1.AwardLevel = sd3.DictionaryId

                            INNER JOIN  SysUser AS SU ON ua1.UserId = SU.Id
	                        INNER JOIN  up_Unit AS U ON ua1.UnitId = U.Id AND U.Statuz = 1 AND U.BaseIsDelete = 0
                            LEFT JOIN up_SchoolExtension as se21 on U.Id = se21.UnitId AND se21.BaseIsDelete = 0
	                        INNER JOIN  up_UnitRelation AS UR ON U.Id = UR.ExtensionObjId AND UR.ExtensionType = 3
                            INNER JOIN  up_Unit AS u21 ON UR.UnitId = u21.Id AND u21.Statuz = 1 AND u21.BaseIsDelete = 0
							INNER JOIN  up_UnitRelation AS ur22 ON u21.Id = ur22.ExtensionObjId AND ur22.ExtensionType = 3 AND ur22.BaseIsDelete = 0
                          ) as tb1 WHERE   1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = @BaseIsDelete ");
                parameter.Add(DbParameterExtension.CreateDbParameter("@BaseIsDelete", param.BaseIsDelete));

                if (param.UserId > 0)
                {
                    strSql.Append(" AND UserId = @UserId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UserId", param.UserId));
                }
                if (param.UnitId > 0)
                {
                    strSql.Append(" AND UnitId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                if (param.CountyId > 0)
                {
                    strSql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
                if (param.CityId > 0)
                {
                    strSql.Append(" AND CityId = @CityId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CityId", param.CityId));
                }
                if (param.AwardLevel > 0)
                {
                    strSql.Append(" AND AwardLevel = @AwardLevel ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@AwardLevel", param.AwardLevel));
                }
                if (param.AwardCategory > 0)
                {
                    strSql.Append(" AND AwardCategory = @AwardCategory ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@AwardCategory", param.AwardCategory));
                }
                if (!string.IsNullOrEmpty(param.StartAwardDate.ParseToString()))
                {
                    strSql.Append(" AND AwardDate >= @StartAwardDate ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@StartAwardDate", param.StartAwardDate));
                }
                if (!string.IsNullOrEmpty(param.EndAwardDate.ParseToString()))
                {
                    param.EndAwardDate = param.EndAwardDate.Value.Date.Add(new TimeSpan(23, 59, 59));
                    strSql.Append(" AND AwardDate <= @EndAwardDate ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@EndAwardDate", param.EndAwardDate));
                }

                if (!string.IsNullOrEmpty(param.Remark.Trim()))
                {
                    strSql.Append(" AND (Remark like @Remark OR  RealName like @Remark )");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Remark", $"%{param.Remark.Trim()}%"));
                }
                if (param.SchoolPropList != null && param.SchoolPropList.Count > 0)
                {
                    param.SchoolPropList = param.SchoolPropList.Distinct().ToList();
                    strSql.Append($" AND ({string.Join(" OR ", param.SchoolPropList.Select(m => string.Format(" SchoolProp = {0} ", m)))}) ");
                }
            }
            return parameter;
        }
        #endregion
    }
}

