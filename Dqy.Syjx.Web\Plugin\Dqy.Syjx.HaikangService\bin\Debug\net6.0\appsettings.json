﻿{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "Quartz": {
    "WorkerCron": "0 0/3 6-20 * * ?", //每天6-20点期间执行，每3分钟执行一次
    "WorkerCron2": "0 0 12,23 * * ?" //每天12点、23点执行一次
  },
  "SystemConfig": {
    "Demo": false, // 是否是演示模式
    "LoginMultiple": true, // 是否允许一个账户在多处登录
    "LoginProvider": "Cookie", // 登录信息保存方式 Cookie Session WebApi
    "SnowFlakeWorkerId": 1, // SnowFlake 节点序号
    "ApiSite": "http://localhost:5001", // Api地址，例如可以上传文件到Api
    "VirtualDirectory": "/admin", // 虚拟目录 
    "DBProvider": "SqlServer",
    "DBConnectionString": "Server=***********;Integrated Security=False;Database=Syjx;User ID=****;Password=****",
    "DBCommandTimeout": 180, // 数据库超时时间，单位秒
    "DBBackup": "", // 数据库备份路径
    "CacheProvider": "Memory", // 缓存使用方式 Memory Redis
    "RedisConnectionString": "127.0.0.1:6379",

    "HaiKangServiceBeginTime": "20", //(服务开始时间(小时)备注：每天20点才执行)
    "DBConnectionString2": "Server=***********;Integrated Security=False;Database=Syjx;User ID=****;Password=****", //海康摄像头接口数据库
    "HaiKangVideoExcludeTime": 5, //（摄像头拍摄需要剔除时间 单位：分钟）
    "HaiKangReplaceHttpUrl": "http://*************:18002/", //替换后的http地址
    "HaiKangSavePath": "D:\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Web\\Dqy.Syjx.Web\\Resource\\UploadFile\\", //海康接口图片本地存放路径
    "HaiKangVirtualDirectory": "/Resource/UploadFile/", //海康图片虚拟目录 
    "IsValidPicPeople": 1, //采集的摄像头图片人头数是否有效

    "HaiKangAPPkey": "29904166", //海康合作方APPKey
    "HaiKangAPPsecret": "eVZMRGo8RE5jrbt0yAVG", //海康合作方APPsecret
    "HaiKangApiIp": "************", //海康接口Ip地址
    "HaiKangApiPort": "18001", //海康接口端口
    "IsHttps": false, //是否启用HTTPS协议，默认HTTPS
    "HaiKangIP": "************", //海康综合安防管理平台IP地址
    "HaiKangPort": 18001, //海康综合安防管理平台端口

    "DahuaApi": "https://***************:9999", //大华智慧校园综合管理平台Api地址
    "DahuaIsHttps": true, //是否启用HTTPS协议
    "DahuaClientId": "Junfei", //ClientId
    "DahuaClientSecret": "fb01cb7f-4e05-4eec-b197-96e49d513764", //ClientSecret
    "DahuaSysUsername": "system", //系统账号
    "DahuaSysPwd": "Admin123", //系统密码
    "DahuaVideoIP": "***************", //大华视频播放替换地址
    "VideoPlatform": 2, //采集平台(1、海康 2、大华)

    "CommonLogFilePath": "D:\\SYJX\\Common\\", //普通日志写入地址
    "ErrorLogFilePath": "D:\\SYJX\\Error\\", //错误日志写入地址
    "WarnLogFilePath": "D:\\SYJX\\Warn\\" //警告日志写入地址
  }
}
