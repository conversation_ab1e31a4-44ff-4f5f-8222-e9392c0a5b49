﻿using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.ExperimentTeachManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Enum.ExperimentTeachManage;
using Dqy.Syjx.Model.Param.ExperimentTeachManage;
using Dqy.Syjx.Service.OrganizationManage;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dynamitey.DynamicObjects;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Data.Common;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Dqy.Syjx.Service.ExperimentTeachManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-12-08 17:34
    /// 描 述：编制实验计划服务类
    /// </summary>
    public class PlanInfoService : RepositoryFactory
    {

        private UserSubjectSetService userSubjectSetService = new UserSubjectSetService();

        #region 获取数据
        public async Task<List<PlanInusing Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.ExperimentTeachManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Enum.ExperimentTeachManage;
using Dqy.Syjx.Model.Param.ExperimentTeachManage;
using Dqy.Syjx.Service.OrganizationManage;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dynamitey.DynamicObjects;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Data.Common;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Dqy.Syjx.Service.ExperimentTeachManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-12-08 17:34
    /// 描 述：编制实验计划服务类
    /// </summary>
    public class PlanInfoService : RepositoryFactory
    {

        private UserSubjectSetService userSubjectSetService = new UserSubjectSetService();

        #region 获取数据
        public async Task<List<PlanInfoEntity>> GetList(PlanInfoListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }
        public async Task<List<PlanInfoEntity>> GetVerifyList(PlanInfoListParam param)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ListVerifyFilter(param, strSql);
            var list = await this.BaseRepository().FindList<PlanInfoEntity>(strSql.ToString(), filter.ToArray());
            return list.ToList();
        }
        public async Task<List<PlanInfoEntity>> GetPageList(PlanInfoListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ListFilter(param, strSql);
            IEnumerable<PlanInfoEntity> list = null;
            if (pagination!=null)
            {
                list = await this.BaseRepository().FindList<PlanInfoEntity>(strSql.ToString(), filter.ToArray(), pagination);
            }
            else
            {
                list = await this.BaseRepository().FindList<PlanInfoEntity>(strSql.ToString(), filter.ToArray());
            }
            return list.ToList();
        }

        public async Task<PlanInfoEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<PlanInfoEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(PlanInfoEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }
        public async Task UpdateForm(PlanInfoEntity entity, List<string> fields)
        {
            if (fields != null)
            {
                await entity.Modify();
                fields.Add("BaseModifyTime");
                fields.Add("BaseModifierId");
            }
            await this.BaseRepository().Update(entity, fields);
        }
        public async Task SaveTransForm(PlanInfoEntity entity, Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update ex_PlanInfo set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
                strSql = $"update ex_PlanInfo set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }

        public async Task UpdateNum(long id,long schoolid)
        {
            string strSql = $@" UPDATE  ex_PlanInfo
                    SET Num = (SELECT COUNT(pd1.Id) FROM  ex_PlanDetail AS pd1 WHERE pd1.PlanInfoId = {id} AND ISNULL(pd1.WeekNum,0) > 0 AND pd1.BaseIsDelete = 0) ,
                NeedShowNum = (SELECT COUNT(pd1.Id) FROM  ex_PlanDetail AS pd1 WHERE pd1.PlanInfoId = {id} AND ISNULL(pd1.WeekNum,0) > 0 AND pd1.ExperimentId > 0 AND pd1.IsNeedDo = {IsStatusEnum.Yes.ParseToInt()} AND pd1.ExperimentType = {ExperimentTypeEnum.Demo.ParseToInt()}  AND pd1.BaseIsDelete = 0) ,
               NeedGroupNum = (SELECT COUNT(pd1.Id) FROM  ex_PlanDetail AS pd1 WHERE pd1.PlanInfoId = {id} AND ISNULL(pd1.WeekNum,0) > 0 AND pd1.ExperimentId > 0 AND pd1.IsNeedDo = {IsStatusEnum.Yes.ParseToInt()} AND pd1.ExperimentType = {ExperimentTypeEnum.Group.ParseToInt()} AND pd1.BaseIsDelete = 0) ,
            OptionalShowNum = (SELECT COUNT(pd1.Id) FROM  ex_PlanDetail AS pd1 WHERE pd1.PlanInfoId = {id} AND ISNULL(pd1.WeekNum,0) > 0 AND pd1.ExperimentId > 0 AND pd1.IsNeedDo = {IsStatusEnum.No.ParseToInt()}  AND pd1.ExperimentType = {ExperimentTypeEnum.Demo.ParseToInt()}  AND pd1.BaseIsDelete = 0) ,
           OptionalGroupNum = (SELECT COUNT(pd1.Id) FROM  ex_PlanDetail AS pd1 WHERE pd1.PlanInfoId = {id} AND ISNULL(pd1.WeekNum,0) > 0 AND pd1.ExperimentId > 0 AND pd1.IsNeedDo = {IsStatusEnum.No.ParseToInt()}  AND pd1.ExperimentType = {ExperimentTypeEnum.Group.ParseToInt()} AND pd1.BaseIsDelete = 0) ,

          SchoolNeedShowNum = (SELECT COUNT(Id) FROM  ex_PlanDetail WHERE PlanInfoId = {id} AND ISNULL(WeekNum,0) > 0 AND SourcePath = {SourcePathEnum.School.ParseToInt()} AND IsNeedDo = {IsStatusEnum.Yes.ParseToInt()} AND ExperimentType = {ExperimentTypeEnum.Demo.ParseToInt()} AND BaseIsDelete = 0) ,
         SchoolNeedGroupNum = (SELECT COUNT(Id) FROM  ex_PlanDetail WHERE PlanInfoId = {id} AND ISNULL(WeekNum,0) > 0 AND SourcePath = {SourcePathEnum.School.ParseToInt()} AND IsNeedDo = {IsStatusEnum.Yes.ParseToInt()} AND ExperimentType = {ExperimentTypeEnum.Group.ParseToInt()} AND BaseIsDelete = 0) ,
      SchoolOptionalShowNum = (SELECT COUNT(Id) FROM  ex_PlanDetail WHERE PlanInfoId = {id} AND ISNULL(WeekNum,0) > 0 AND SourcePath = {SourcePathEnum.School.ParseToInt()} AND IsNeedDo = {IsStatusEnum.No.ParseToInt()} AND ExperimentType = {ExperimentTypeEnum.Demo.ParseToInt()} AND BaseIsDelete = 0) ,
     SchoolOptionalGroupNum = (SELECT COUNT(Id) FROM  ex_PlanDetail WHERE PlanInfoId = {id} AND ISNULL(WeekNum,0) > 0 AND SourcePath = {SourcePathEnum.School.ParseToInt()} AND IsNeedDo = {IsStatusEnum.No.ParseToInt()} AND ExperimentType = {ExperimentTypeEnum.Group.ParseToInt()}  AND BaseIsDelete = 0)
            WHERE Id = {id} AND SchoolId = {schoolid} ";
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<PlanInfoEntity, bool>> ListFilter(PlanInfoListParam param)
        {
            var expression = LinqExtensions.True<PlanInfoEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (param.Id > 0)
                {
                    expression = expression.And(t => t.Id == param.Id);
                }
                if (param.UnitId > 0)
                {
                    expression = expression.And(t => t.SchoolId == param.UnitId);
                }
                if (param.SchoolTerm > 0)
                {
                    expression = expression.And(t => t.SchoolTerm == param.SchoolTerm);
                }
                if (param.SchoolYearStart > 0)
                {
                    expression = expression.And(t => t.SchoolYearStart == param.SchoolYearStart);
                }
                if (param.SchoolYearEnd > 0)
                {
                    expression = expression.And(t => t.SchoolYearEnd == param.SchoolYearEnd);
                }
                if (param.GradeId > 0)
                {
                    expression = expression.And(t => t.GradeId == param.GradeId);
                }
                if (param.CourseId > 0)
                {
                    expression = expression.And(t => t.CourseId == param.CourseId);
                }
                if (param.CompulsoryType > 0)
                {
                    expression = expression.And(t => t.CompulsoryType == param.CompulsoryType);
                }
                if (param.ActivityType !=-10000)
                {
                    expression = expression.And(t => t.ActivityType == param.ActivityType);
                }
            }
            return expression;
        }

        private List<DbParameter> ListFilter(PlanInfoListParam param, StringBuilder strSql)
        {
            strSql.Append($@"  SELECT * From (
                                    SELECT
                                    pi1.Id ,
                                    pi1.BaseIsDelete ,
                                    pi1.BaseCreateTime ,
                                    pi1.BaseModifyTime ,
                                    pi1.BaseCreatorId AS CreatorId,
                                    pi1.BaseModifierId ,
                                    pi1.BaseVersion ,
                                    pi1.SchoolId ,
                                    pi1.GradeId ,
                                    pi1.CourseId ,
                                    pi1.SchoolYearStart ,
                                    pi1.SchoolYearEnd ,
                                    pi1.SchoolTerm ,
                                    pi1.PlanName ,
                                    pi1.Num ,
                                    pi1.CompulsoryType ,
                                    pi1.ActivityType ,
                                     pi1.Statuz ,
                                     pi1.PublishDate ,
                                    pi1.ClassIdz ,
                                    '' AS ClassNamez ,
                                    sd3.DicName AS GradeName ,
                                    sd4.DicName AS CourseName ,
                                    su5.RealName ,
                                    u2.Name AS SchoolName ,ur.UnitId AS CountyId ,u2.Sort ,
                                    CASE WHEN  (pi1.SchoolYearStart = {param.CurrentSchoolTermStartYear} AND pi1.SchoolTerm >= {param.CurrentSchoolTerm} ) OR  pi1.SchoolYearStart= {param.CurrentSchoolTermStartYear+1} THEN 2 ELSE 1 END  AS IsExpiry ,
                                    pi1.NeedShowNum ,pi1.NeedGroupNum ,pi1.OptionalShowNum ,pi1.OptionalGroupNum ,
				                    pi1.SchoolNeedShowNum ,pi1.SchoolNeedGroupNum ,pi1.SchoolOptionalShowNum ,pi1.SchoolOptionalGroupNum ,
                                    dr.DictionaryId AS SchoolStageId ,SD.DicName AS SchoolStageName ,
                                    sa.AreaName AS CountyName ,UR2.UnitId AS CityId
                                    FROM  ex_PlanInfo AS pi1
                                    INNER JOIN  up_Unit AS u2 ON pi1.SchoolId = u2.Id AND u2.BaseIsDelete = 0 AND u2.Statuz = 1
                                    INNER JOIN  sys_static_dictionary AS sd3 ON pi1.GradeId = sd3.DictionaryId
                                    INNER JOIN  sys_dictionary_relation AS dr ON sd3.DictionaryId = dr.DictionaryToId  AND dr.RelationType = 1 AND dr.BaseIsDelete = 0
				                    INNER JOIN  sys_static_dictionary AS SD ON dr.DictionaryId = SD.DictionaryId AND SD.TypeCode = '1002' AND SD.BaseIsDelete = 0
                                    INNER JOIN  sys_static_dictionary AS sd4 ON pi1.CourseId = sd4.DictionaryId
                                    INNER JOIN  SysUser AS su5 ON pi1.BaseCreatorId = su5.Id
                                    INNER JOIN  up_UnitRelation AS ur ON pi1.SchoolId = ur.ExtensionObjId AND ur.ExtensionType = 3 AND ur.BaseIsDelete = 0
                                    INNER JOIN  up_UnitRelation AS UR2 ON UR.UnitId = UR2.ExtensionObjId AND UR2.ExtensionType = 3 AND UR2.BaseIsDelete = 0
				                    INNER JOIN  up_Unit AS u3 ON ur.UnitId = u3.Id AND u3.BaseIsDelete = 0
				                    LEFT JOIN  SysArea AS sa ON sa.AreaCode = u3.AreaId AND sa.BaseIsDelete = 0
                           ) as tb1 WHERE   1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = @BaseIsDelete ");
                parameter.Add(DbParameterExtension.CreateDbParameter("@BaseIsDelete", param.BaseIsDelete));
                if (param.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    strSql.Append(" AND SchoolId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                else if (param.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    strSql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.UnitId));
                    if (param.SchoolId > 0)
                    {
                        strSql.Append(" AND SchoolId = @UnitId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.SchoolId));
                    }
                }
                else if (param.UnitType == UnitTypeEnum.City.ParseToInt())
                {
                    strSql.Append(" AND CityId = @CityId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CityId", param.UnitId));
                    if(param.CountyId > 0)
                    {
                        strSql.Append(" AND CountyId = @CountyId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                    }
                    if(param.SchoolId > 0)
                    {
                        strSql.Append(" AND SchoolId = @SchoolId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
                    }
                }
                else
                {
                    strSql.Append(" AND 1 <> 1 ");
                }
                if (param.GradeId > 0)
                {
                    strSql.Append(" AND GradeId = @GradeId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@GradeId", param.GradeId));
                }
                if (param.CourseId > 0)
                {
                    strSql.Append(" AND CourseId = @CourseId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (param.CourseIds != null && param.CourseIds.Count > 0)
                {
                    var wheretemp = new List<string>();
                    foreach (var item in param.CourseIds)
                    {
                        wheretemp.Add(string.Format(" CourseId = {0} ", item));
                    }
                    strSql.AppendFormat(" AND ({0}) ", string.Join(" OR ", wheretemp));
                }
                if (param.GradeIds != null && param.GradeIds.Count > 0)
                {
                    var wheretemp = new List<string>();
                    foreach (var item in param.GradeIds)
                    {
                        wheretemp.Add(string.Format(" GradeId = {0} ", item));
                    }
                    strSql.AppendFormat(" AND ({0}) ", string.Join(" OR ", wheretemp));
                }

                if (param.SchoolTerm > 0)
                {
                    strSql.Append(" AND SchoolTerm = @SchoolTerm ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolTerm", param.SchoolTerm));
                }
                if (param.SchoolYearStart > 0)
                {
                    strSql.Append(" AND SchoolYearStart = @SchoolYearStart ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolYearStart", param.SchoolYearStart));
                    //查询开始时间就足够了，不需要再加结束时间。
                    //param.SchoolYearEnd = (param.SchoolYearStart + 1);
                    //strSql.Append(" AND SchoolYearEnd = @SchoolYearEnd ");
                    //parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolYearEnd", param.SchoolYearEnd));
                }
                if (!string.IsNullOrEmpty(param.PlanName))
                {
                    strSql.Append(" AND PlanName like @Name ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Name", $"%{param.PlanName}%"));
                }
                if(param.SchoolStageId > 0) //学段
                {
                    strSql.Append(" AND SchoolStageId = @SchoolStageId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStageId", param.SchoolStageId));
                }
                if (param.CreatorId > 0) //编制人
                {
                    strSql.Append(" AND CreatorId = @CreatorId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CreatorId", param.CreatorId));
                }
                if(param.ExperimentType > 0) //实验类型
                {
                    if (param.ExperimentType == 1)  //演示
                    {
                        strSql.Append(" AND (NeedShowNum>0 OR OptionalShowNum>0 OR SchoolNeedShowNum>0 OR SchoolOptionalShowNum>0) ");
                    }
                    else if (param.ExperimentType == 2) //分组
                    {
                        strSql.Append(" AND (NeedGroupNum>0 OR OptionalGroupNum>0 OR SchoolNeedGroupNum>0 OR SchoolOptionalGroupNum>0) ");
                    }
                }
                if(param.IsNeedDo > 0) //实验要求
                {
                    if (param.IsNeedDo == 1)  //是
                    {
                        strSql.Append("AND (NeedShowNum>0 OR NeedGroupNum>0 OR SchoolNeedShowNum>0 OR SchoolNeedGroupNum>0) ");
                    }
                    else if (param.IsNeedDo == 2) //否
                    {
                        strSql.Append("AND (OptionalShowNum>0 OR OptionalGroupNum>0 OR SchoolOptionalShowNum>0 OR SchoolOptionalGroupNum>0) ");
                    }
                }
                if(param.IsShowCurrentSchoolYear > 0) //只显示当前学年
                {
                    strSql.Append($" AND SchoolYearStart = {param.CurrentSchoolTermStartYear} AND SchoolYearEnd = {param.CurrentSchoolTermStartYear + 1}");
                }

                if (param.SetUserId > 0)
                {

                    //判断用户是否设置了学段、学科
                    var listStage = userSubjectSetService.GetUserSetStageList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listStage.Count > 0)
                    {
                        string stageId = string.Join(",", listStage.Select(f => f.DictionaryId));
                        strSql.Append($" AND SchoolStageId IN ({stageId})");
                    }

                    var listCourse = userSubjectSetService.GetUserSetCourseList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listCourse.Count > 0)
                    {
                        string courseId = string.Join(",", listCourse.Select(f => f.DictionaryId));
                        strSql.Append($" AND CourseId IN ({courseId})");
                    }
                }
                if (param.CompulsoryType>0)
                {
                    strSql.Append(" AND CompulsoryType = @CompulsoryType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CompulsoryType", param.CompulsoryType));

                }
                if (param.ActivityType != -10000)
                {
                    strSql.Append(" AND ActivityType = @ActivityType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ActivityType", param.ActivityType));
                }
                if (param.Statuz != -10000 && param.Statuz != -1)
                {
                    strSql.Append(" AND Statuz = @Statuz ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Statuz", param.Statuz));
                }
                if (param.Statuzge != -10000 && param.Statuzge != -1)
                {
                    strSql.Append(" AND Statuz >= @Statuzge ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Statuzge", param.Statuzge));
                }
            }
            return parameter;
        }

        private List<DbParameter> ListVerifyFilter(PlanInfoListParam param, StringBuilder strSql)
        {
            strSql.Append(@"  SELECT * From (
                SELECT
                pi1.Id ,
                pi1.BaseIsDelete ,
                pi1.BaseCreateTime ,
                pi1.BaseModifyTime ,
                pi1.BaseCreatorId ,
                pi1.BaseModifierId ,
                pi1.BaseVersion ,
                pi1.ActivityType,
                pi1.SchoolId ,
                pi1.GradeId ,
                pi1.CourseId ,
                pi1.SchoolYearStart ,
                pi1.SchoolYearEnd ,
                pi1.SchoolTerm ,
                pi1.PlanName ,
                pi1.Num ,
                pi1.ClassIdz ,
                CASE When IsNull(pi1.CompulsoryType,0) = 0 Then 2 Else pi1.CompulsoryType END AS CompulsoryType ,
                su5.RealName ,
                u2.Name AS SchoolName ,ur.UnitId AS CountyId ,u2.Sort
                FROM  ex_PlanInfo AS pi1
                INNER JOIN  up_Unit AS u2 ON pi1.SchoolId = u2.Id AND u2.BaseIsDelete = 0 AND u2.Statuz = 1
                INNER JOIN  SysUser AS su5 ON pi1.BaseCreatorId = su5.Id
                INNER JOIN  up_UnitRelation AS ur ON pi1.SchoolId = ur.ExtensionObjId AND ur.ExtensionType = 3 AND ur.BaseIsDelete = 0
            ) as tb1 WHERE   1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = @BaseIsDelete ");
                parameter.Add(DbParameterExtension.CreateDbParameter("@BaseIsDelete", param.BaseIsDelete));
                if (param.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    strSql.Append(" AND SchoolId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                else if (param.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    strSql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.UnitId));
                    if (param.SchoolId > 0)
                    {
                        strSql.Append(" AND SchoolId = @UnitId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.SchoolId));
                    }
                }
                else if (param.UnitType == UnitTypeEnum.City.ParseToInt() || param.UnitType == UnitTypeEnum.System.ParseToInt())
                {
                }
                else
                {
                    strSql.Append(" AND 1 <> 1 ");
                }
                if (param.OptType == 1)
                {
                    if (param.Id > 0)
                    {
                        strSql.Append(" AND Id <> @Id ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@Id", param.Id));
                    }
                    if (param.GradeId > 0)
                    {
                        strSql.Append(" AND GradeId = @GradeId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@GradeId", param.GradeId));
                    }
                    if (param.CourseId > 0)
                    {
                        strSql.Append(" AND CourseId = @CourseId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                    }
                    if (param.SchoolTerm > 0)
                    {
                        strSql.Append(" AND SchoolTerm = @SchoolTerm ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolTerm", param.SchoolTerm));
                    }
                    if (param.SchoolYearStart > 0)
                    {
                        strSql.Append(" AND SchoolYearStart = @SchoolYearStart ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolYearStart", param.SchoolYearStart));
                    }
                    if (param.CompulsoryType > 0)
                    {
                        strSql.Append(" AND CompulsoryType = @CompulsoryType ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@CompulsoryType", param.CompulsoryType));
                    }
                    if (!string.IsNullOrEmpty(param.ClassIdz) && param.GradeId >= @GradeEnum.GaoYi.ParseToInt())
                    {

                        long[] classIdArr = TextHelper.SplitToArray<long>(param.ClassIdz, ',');
                        if (classIdArr != null && classIdArr.Length > 0)
                        {
                            string tempWhere = "";
                            foreach (var item in classIdArr)
                            {
                                if (tempWhere != "")
                                {
                                    tempWhere += " OR ";
                                }
                                tempWhere += $" ClassIdz like '%{item}%'";
                            }
                            if (tempWhere != "")
                            {
                                strSql.Append($" AND ({tempWhere}) ");
                            }
                        }
                    }
                }
                if (param.ActivityType != -10000)
                {
                    strSql.Append(" AND ActivityType = @ActivityType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ActivityType", param.ActivityType));
                }
            }
            return parameter;
        }

        /// <summary>
        /// 获取年级学科必做实验数量
        /// </summary>
        /// <param name="param"></param>
        /// <param name="strSql"></param>
        /// <returns></returns>
        public async Task<List<PlanInfoEntity>> GetGradeCourseNumList(PlanInfoListParam param)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(@$" SELECT GradeId,CourseId,GradeName , CourseName
                            ,Sum(NeedGroupNum) AS NeedGroupNum
                            ,Sum(NeedShowNum) AS NeedShowNum
                            ,Sum(Num) AS Num
                             From (
						  SELECT p1.Id
						  ,p1.GradeId ,p1.CourseId
						  ,D2.DicName AS GradeName ,D3.DicName AS CourseName
						  ,p1.NeedGroupNum ,p1.NeedShowNum
						  ,(p1.NeedGroupNum + p1.NeedShowNum + p1.SchoolNeedGroupNum + p1.SchoolNeedShowNum) as Num
                          ,dr.DictionaryId AS SchoolStageId
						  ,ur4.UnitId AS CountyId
						  FROM  ex_PlanInfo AS p1
						  INNER JOIN  sys_static_dictionary AS D2 ON p1.GradeId = D2.DictionaryId AND D2.BaseIsDelete = 0  AND D2.TypeCode =  '{DicTypeCodeEnum.Grade.ParseToInt()}'
                          INNER JOIN  sys_static_dictionary AS D3 ON p1.CourseId = D3.DictionaryId AND D3.BaseIsDelete = 0  AND D3.TypeCode = '{DicTypeCodeEnum.Course.ParseToInt()}'
                          INNER JOIN  sys_dictionary_relation AS dr ON p1.GradeId = dr.DictionaryToId  AND dr.RelationType = 1 AND dr.BaseIsDelete = 0
						  INNER JOIN  up_UnitRelation AS ur4 ON p1.SchoolId = ur4.ExtensionObjId AND ur4.ExtensionType = 3
                          ) as T WHERE  1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.SchoolStageId > 0)
                {
                    strSql.Append(" AND SchoolStageId = @SchoolStageId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStageId", param.SchoolStageId));
                }
                if (!param.CountyId.IsNullOrZero())
                {
                    strSql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
            }
            strSql.Append(" group by GradeId,CourseId,GradeName , CourseName ");
            var list = await this.BaseRepository().FindList<PlanInfoEntity>(strSql.ToString(), parameter.ToArray());
            return list.ToList();
        }

        /// <summary>
        /// 编制计划实验数量以学校为单位
        /// </summary>
        /// <param name="param"></param>
        /// <param name="strSql"></param>
        /// <returns></returns>
        public async Task<List<PlanInfoEntity>> GetGradeCourseNumSchoolList(PlanInfoListParam param)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(@$" SELECT SchoolId,SchoolName , GradeId,CourseId,GradeName , CourseName
                            ,Sum(NeedGroupNum) AS NeedGroupNum
                            ,Sum(NeedShowNum) AS NeedShowNum
                            ,Sum(Num) AS Num
                             From (
						  SELECT p1.Id
                          ,p1.SchoolId
						  ,p1.GradeId ,p1.CourseId
						  ,D2.DicName AS GradeName ,D3.DicName AS CourseName
						  ,p1.NeedGroupNum ,p1.NeedShowNum
						  ,(p1.NeedGroupNum + p1.NeedShowNum) as Num
                          ,dr.DictionaryId AS SchoolStageId
						  ,ur4.UnitId AS CountyId
                          ,u5.Name AS SchoolName
						  FROM  ex_PlanInfo AS p1
						  INNER JOIN  sys_static_dictionary AS D2 ON p1.GradeId = D2.DictionaryId AND D2.BaseIsDelete = 0  AND D2.TypeCode =  '{DicTypeCodeEnum.Grade.ParseToInt()}'
                          INNER JOIN  sys_static_dictionary AS D3 ON p1.CourseId = D3.DictionaryId AND D3.BaseIsDelete = 0  AND D3.TypeCode = '{DicTypeCodeEnum.Course.ParseToInt()}'
                          INNER JOIN  sys_dictionary_relation AS dr ON p1.GradeId = dr.DictionaryToId  AND dr.RelationType = 1 AND dr.BaseIsDelete = 0
						  INNER JOIN  up_UnitRelation AS ur4 ON p1.SchoolId = ur4.ExtensionObjId AND ur4.ExtensionType = 3
                          INNER JOIN  up_Unit AS u5 ON p1.SchoolId = u5.Id AND u5.BaseIsDelete = 0 AND u5.Statuz = 1
                          ) as T WHERE  1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.SchoolStageId > 0)
                {
                    strSql.Append(" AND SchoolStageId = @SchoolStageId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStageId", param.SchoolStageId));
                }
                if (!string.IsNullOrEmpty(param.CourseName))
                {
                    strSql.Append(" AND CourseName = @CourseName ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseName", param.CourseName));
                }
                if (!string.IsNullOrEmpty(param.GradeName))
                {
                    strSql.Append(" AND GradeName = @GradeName ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@GradeName", param.GradeName));
                }
                if (!param.CountyId.IsNullOrZero())
                {
                    strSql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
            }
            strSql.Append(" group by SchoolId,SchoolName,GradeId,CourseId,GradeName , CourseName ");
            var list = await this.BaseRepository().FindList<PlanInfoEntity>(strSql.ToString(), parameter.ToArray());
            return list.ToList();
        }


        /// <summary>
        /// 实话实验开出列表
        /// </summary>
        /// <param name="param">查询参数</param>
        /// <param name="pagination">分页参数</param>
        /// <returns></returns>
        public async Task<List<PlanInfoEntity>> GetUnitTotalList(PlanInfoListParam param, Pagination pagination)
        {
            var parameter = new List<DbParameter>();
            StringBuilder strSql = new StringBuilder();
            strSql.Append(@$"SELECT * FROM (
                SELECT pi4.*
                ,u3.Name AS SchoolName ,u3.Sort	,ur2.UnitId AS CountyId
                FROM ex_PlanInfo AS pi4
                INNER JOIN up_UnitRelation AS ur2 ON ur2.BaseIsDelete = 0 AND pi4.SchoolId = ur2.ExtensionObjId AND ur2.ExtensionType = 3
                INNER JOIN up_Unit AS u3 ON u3.BaseIsDelete = 0 AND u3.Id = ur2.ExtensionObjId
                ) AS tab01 Where BaseIsDelete = 0
            ");
            if (param != null)
            {
                if (!param.Id.IsNullOrZero())
                {
                    strSql.Append($" AND Id = {param.Id}");
                }

                if (!param.CountyId.IsNullOrZero())
                {
                    strSql.Append($" AND CountyId = {param.CountyId}");
                    if (param.SchoolId > 0)
                    {
                        strSql.Append($" AND SchoolId = {param.SchoolId}");
                    }
                }
                else if (!param.SchoolId.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolId = {param.SchoolId}");//根据用户登录的单位ID进行查询
                }
                if (!param.SchoolYearStart.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolYearStart = {param.SchoolYearStart}"); //开始学年
                }
                if (!param.SchoolTerm.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolTerm = {param.SchoolTerm}"); //学期
                }
                if (!param.CourseId.IsNullOrZero())
                {
                    strSql.Append($" AND CourseId = {param.CourseId}"); //学科
                }
                if (param.CompulsoryType > 0)
                {
                    strSql.Append($" AND CompulsoryType = {param.CompulsoryType} "); //实验来源
                }
                if (param.ActivityType != -10000)
                {
                    strSql.Append($" AND ActivityType = {param.ActivityType}"); //课程类型
                }
            }
            var list = await this.BaseRepository().FindList<PlanInfoEntity>(strSql.ToString(), parameter.ToArray(), pagination);
            return list.ToList();
        }


        #endregion

        #region 计划实验达标结果

        /// <summary>
        /// 实话实验开出列表
        /// </summary>
        /// <param name="param">查询参数</param>
        /// <param name="pagination">分页参数</param>
        /// <returns></returns>
        public async Task<List<PlanInfoEntity>> PlanExpPageList(PlanInfoListParam param, Pagination pagination)
        {
            var parameter = new List<DbParameter>();
            StringBuilder strSql = new StringBuilder();
            strSql.Append(@$"SELECT * FROM (
                SELECT pi4.*
                ,pd3.ExperimentType , pd3.IsNeedDo ,WeekNum
                ,u3.Name AS SchoolName ,u3.Sort	,ur2.UnitId AS CountyId
                ,booking.SchoolGradeClassId
                ,se11.SchoolProp
                ,su.Id AS TeacherId
                ,su.RealName
                FROM ex_PlanInfo AS pi4
				INNER JOIN ex_PlanDetail AS pd3 ON pd3.BaseIsDelete = 0 AND pd3.PlanInfoId = pi4.Id
                INNER JOIN up_UnitRelation AS ur2 ON ur2.BaseIsDelete = 0 AND pi4.SchoolId = ur2.ExtensionObjId AND ur2.ExtensionType = 3
                INNER JOIN up_Unit AS u3 ON u3.BaseIsDelete = 0 AND u3.Id = ur2.ExtensionObjId
                INNER JOIN up_SchoolExtension AS se11 ON se11.BaseIsDelete = 0 AND se11.UnitId = u3.Id
				INNER JOIN ex_ExperimentBooking AS booking ON booking.BaseIsDelete = 0
				AND booking.Statuz = 100 AND booking.IsMain = 0
				AND booking.ExperimentId = pd3.ExperimentId
				AND booking.SchoolId =  u3.Id
				AND booking.CourseId = pi4.CourseId
				AND booking.SchoolYearStart = pi4.SchoolYearStart
				AND booking.SchoolTerm = pi4.SchoolTerm
				AND booking.GradeId = pi4.GradeId
                LEFT JOIN SysUser AS su  ON booking.BaseCreatorId = su.Id
                ) AS tab01 Where BaseIsDelete = 0
            ");
            if (param != null)
            {
                if (!param.Id.IsNullOrZero())
                {
                    strSql.Append($" AND Id = {param.Id}");
                }
                if (!param.SchoolId.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolId = {param.SchoolId}");//根据用户登录的单位ID进行查询
                }
                if (!param.CountyId.IsNullOrZero())
                {
                    strSql.Append($" AND CountyId = {param.CountyId}");
                    if (param.SchoolId > 0)
                    {
                        strSql.Append($" AND SchoolId = {param.SchoolId}");
                    }
                }
                if (param.SchoolPropList != null && param.SchoolPropList.Count > 0)
                {
                    strSql.Append($" AND ({string.Join(" OR ", param.SchoolPropList.Select(n => string.Format(" SchoolProp = {0} ", n)))})"); //学段
                }
                if (!param.SchoolYearStart.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolYearStart = {param.SchoolYearStart}"); //开始学年
                }
                if (!param.SchoolTerm.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolTerm = {param.SchoolTerm}"); //学期
                }
                //if (!param.SchoolGradeClassId.IsNullOrZero())
                //{
                //    strSql.Append($" AND SchoolGradeClassIdz like '%{param.SchoolGradeClassId}%' "); //年级班级Id
                //}
                if (!param.CourseId.IsNullOrZero())
                {
                    strSql.Append($" AND CourseId = {param.CourseId}"); //学科
                }
                if (param.CompulsoryType > 0)
                {
                    strSql.Append($" AND CompulsoryType = {param.CompulsoryType} "); //实验来源
                }
                if (!string.IsNullOrEmpty(param.TeacherName))
                {
                    strSql.Append($" AND RealName like '%{param.TeacherName}%' "); //任课老师
                }
                if (param.ActivityType != -10000)
                {
                    strSql.Append($" AND ActivityType = {param.ActivityType}"); //课程类型
                }
                if (param.WeekNumgt > 0)
                {
                    strSql.Append($" AND WeekNum > {param.WeekNumgt}"); //周次
                }
            }
            IEnumerable<PlanInfoEntity> list = null;
            if (pagination!=null)
            {
                list = await this.BaseRepository().FindList<PlanInfoEntity>(strSql.ToString(), parameter.ToArray(), pagination);
            }
            else
            {
                list = await this.BaseRepository().FindList<PlanInfoEntity>(strSql.ToString(), parameter.ToArray());
            }
            return list.ToList();
        }

        /// <summary>
        /// 实话实验开出班级明细列表，
        /// </summary>
        /// <param name="param">查询参数</param>
        /// <param name="pagination">分页参数</param>
        /// <returns></returns>
        public async Task<List<PlanInfoEntity>> PlanExpPageClassList(PlanInfoListParam param, Pagination pagination)
        {

            var parameter = new List<DbParameter>();
            StringBuilder strSql = new StringBuilder();
            string selectClassSql = "  ,gradeclass.ClassId,gradeclass.ClassDesc  ,gradeclass.Id AS SchoolGradeClassId ,gradeclass.SelectSubject  ";
            string tableClassSql = $@"  INNER JOIN up_SchoolGradeClass AS gradeclass ON gradeclass.BaseIsDelete = 0 AND u3.Id = gradeclass.UnitId AND gradeclass.GradeId = pi4.GradeId and gradeclass.IsGraduate = 0 and gradeclass.Statuz = {StatusEnum.Yes.ParseToInt()} ";

            if (param.IsCurrentSchoolYear == 2)
            {
                selectClassSql = " ,gradeclass.ClassId,gradeclass.ClassDesc  ,gradeclass.SchoolGradeClassId ,gradeclass.SelectSubject  ";
                tableClassSql = $@"  INNER JOIN up_SchoolGradeClassHistory AS gradeclass ON gradeclass.BaseIsDelete = 0 AND u3.Id = gradeclass.UnitId AND pi4.SchoolYearStart = gradeclass.SchoolYearStart AND gradeclass.GradeId = pi4.GradeId and gradeclass.IsGraduate = 0 and gradeclass.Statuz = {StatusEnum.Yes.ParseToInt()} ";
            }
            strSql.Append(@$" SELECT * FROM (
                SELECT pi4.*
				  ,schoolstage.DicName AS SchoolStageName ,course.DicName AS CourseName ,grade.DicName AS GradeName
                ,u3.Name AS SchoolName ,u3.Sort	,ur2.UnitId AS CountyId
				,se11.SchoolProp
				{selectClassSql}
                FROM ex_PlanInfo AS pi4
                INNER JOIN up_UnitRelation AS ur2 ON ur2.BaseIsDelete = 0 AND pi4.SchoolId = ur2.ExtensionObjId AND ur2.ExtensionType = 3
                INNER JOIN up_Unit AS u3 ON u3.BaseIsDelete = 0 AND u3.Id = ur2.ExtensionObjId
                INNER JOIN up_SchoolExtension AS se11 ON se11.BaseIsDelete = 0 AND se11.UnitId = u3.Id
				{tableClassSql}
                LEFT JOIN  sys_static_dictionary AS schoolstage ON schoolstage.BaseIsDelete = 0 AND gradeclass.SchoolStage = schoolstage.DictionaryId
                LEFT JOIN  sys_static_dictionary AS course ON course.BaseIsDelete = 0 AND pi4.CourseId = course.DictionaryId
                LEFT JOIN  sys_static_dictionary AS grade ON grade.BaseIsDelete = 0 AND pi4.GradeId = grade.DictionaryId
                ) AS tab01 Where BaseIsDelete = 0
            ");
            if (param != null)
            {
                if (!param.Id.IsNullOrZero())
                {
                    strSql.Append($" AND Id = {param.Id}");
                }
                if (!param.SchoolId.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolId = {param.SchoolId}");//根据用户登录的单位ID进行查询
                }
                if (!param.CountyId.IsNullOrZero())
                {
                    strSql.Append($" AND CountyId = {param.CountyId}");
                    if (param.SchoolId > 0)
                    {
                        strSql.Append($" AND SchoolId = {param.SchoolId}");
                    }
                }
                if (param.SchoolPropList != null && param.SchoolPropList.Count > 0)
                {
                    strSql.Append($" AND SchoolProp = {string.Join(" OR ", param.SchoolPropList.Select(n => string.Format(" SchoolProp = {0} ", n)))}"); //学段
                }
                if (!param.SchoolYearStart.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolYearStart = {param.SchoolYearStart}"); //开始学年
                }
                if (!param.SchoolTerm.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolTerm = {param.SchoolTerm}"); //学期
                }
                //if (!param.SchoolGradeClassId.IsNullOrZero())
                //{
                //    strSql.Append($" AND SchoolGradeClassIdz like '%{param.SchoolGradeClassId}%' "); //年级班级Id
                //}
                if (!param.CourseId.IsNullOrZero())
                {
                    strSql.Append($" AND CourseId = {param.CourseId}"); //学科
                }
                if (param.GradeId >= GradeEnum.GaoYi.ParseToInt())
                {
                    if (param.CompulsoryType==ClassCompulsoryTypeEnum.Select.ParseToInt())
                    {
                        if (param.CourseId > 0)
                        {
                            strSql.Append($" AND SelectSubject like '%{param.CourseId}%' "); //年级班级Id
                        }
                    }
                    else
                    {
                        if (param.CourseId > 0)
                        {
                            strSql.Append($" AND (ISNULL(SelectSubject,'') = '' OR  SelectSubject NOT like '%{param.CourseId}%' ) "); //年级班级Id
                        }
                    }
                }
                if (param.CompulsoryType > 0)
                {
                    strSql.Append($" AND CompulsoryType = {param.CompulsoryType} "); //实验来源
                }
                if (!param.Name.IsNullOrZero())
                {
                    strSql.Append($" AND ClassDesc like '%{param.Name}%' "); //年级班级Id
                }
            }
            var list = await this.BaseRepository().FindList<PlanInfoEntity>(strSql.ToString(), parameter.ToArray(), pagination);
            return list.ToList();
        }


        /// <summary>
        /// 实话实验开出班级老师明细列表，
        /// </summary>
        /// <param name="param">查询参数</param>
        /// <param name="pagination">分页参数</param>
        /// <returns></returns>
        public async Task<List<PlanInfoEntity>> PlanExpPageClassTeacherList(PlanInfoListParam param, Pagination pagination)
        {

            var parameter = new List<DbParameter>();
            StringBuilder strSql = new StringBuilder();
            strSql.Append(@$" SELECT * FROM (
                SELECT pi4.*
				  ,schoolstage.DicName AS SchoolStageName ,course.DicName AS CourseName ,grade.DicName AS GradeName
                ,u3.Name AS SchoolName ,u3.Sort	,ur2.UnitId AS CountyId
				,se11.SchoolProp
				,gradeclass.ClassDesc  ,gradeclass.Id AS SchoolGradeClassId
                ,gradeclass.SelectSubject ,gradeclass.ClassId
                ,su.Id AS TeacherId
				,su.RealName AS TeacherName
                FROM ex_PlanInfo AS pi4
                INNER JOIN up_UnitRelation AS ur2 ON ur2.BaseIsDelete = 0 AND pi4.SchoolId = ur2.ExtensionObjId AND ur2.ExtensionType = 3
                INNER JOIN up_Unit AS u3 ON u3.BaseIsDelete = 0 AND u3.Id = ur2.ExtensionObjId
                INNER JOIN up_SchoolExtension AS se11 ON se11.BaseIsDelete = 0 AND se11.UnitId = u3.Id
				INNER JOIN up_SchoolGradeClass AS gradeclass ON gradeclass.BaseIsDelete = 0 AND u3.Id = gradeclass.UnitId AND gradeclass.GradeId = pi4.GradeId and gradeclass.IsGraduate = 0 and gradeclass.Statuz = {StatusEnum.Yes.ParseToInt()}
			    INNER JOIN (
					SELECT pdt.PlanInfoId
					,bookt.SchoolYearStart
					,bookt.SchoolTerm
					,bookt.GradeId
					,bookt.BaseCreatorId
					,bookt.SchoolGradeClassId
					,bookt.Statuz
					,bookt.IsMain
					FROM ex_ExperimentBooking AS bookt
					INNER JOIN ex_PlanDetail AS pdt ON  bookt.BaseIsDelete = 0 AND pdt.BaseIsDelete = 0 AND pdt.WeekNum > 0 AND bookt.ExperimentId = pdt.ExperimentId
					GROUP BY pdt.PlanInfoId
					,bookt.SchoolYearStart
					,bookt.SchoolTerm
					,bookt.GradeId
					, bookt.BaseCreatorId
					, bookt.SchoolGradeClassId
					,bookt.Statuz
					,bookt.IsMain
			   ) AS booking	 ON booking.Statuz = 100 AND booking.IsMain = 0 AND booking.SchoolGradeClassId = gradeclass.Id
							   AND pi4.Id = booking.PlanInfoId
							   AND pi4.SchoolYearStart = booking.SchoolYearStart
							   AND pi4.SchoolTerm = booking.SchoolTerm
			  INNER JOIN SysUser AS su ON booking.BaseCreatorId  = su.Id
			  LEFT JOIN  sys_static_dictionary AS schoolstage ON schoolstage.BaseIsDelete = 0 AND gradeclass.SchoolStage = schoolstage.DictionaryId
              LEFT JOIN  sys_static_dictionary AS course ON course.BaseIsDelete = 0 AND pi4.CourseId = course.DictionaryId
              LEFT JOIN  sys_static_dictionary AS grade ON grade.BaseIsDelete = 0 AND pi4.GradeId = grade.DictionaryId
                ) AS tab01 Where BaseIsDelete = 0
            ");
            if (param != null)
            {
                if (!param.Id.IsNullOrZero())
                {
                    strSql.Append($" AND Id = {param.Id}");
                }
                if (!param.SchoolId.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolId = {param.SchoolId}");//根据用户登录的单位ID进行查询
                }
                if (!param.CountyId.IsNullOrZero())
                {
                    strSql.Append($" AND CountyId = {param.CountyId}");
                    if (param.SchoolId > 0)
                    {
                        strSql.Append($" AND SchoolId = {param.SchoolId}");
                    }
                }
                if (param.SchoolPropList != null && param.SchoolPropList.Count > 0)
                {
                    strSql.Append($" AND SchoolProp = {string.Join(" OR ", param.SchoolPropList.Select(n => string.Format(" SchoolProp = {0} ", n)))}"); //学段
                }
                if (!param.SchoolYearStart.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolYearStart = {param.SchoolYearStart}"); //开始学年
                }
                if (!param.SchoolTerm.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolTerm = {param.SchoolTerm}"); //学期
                }
                //if (!param.SchoolGradeClassId.IsNullOrZero())
                //{
                //    strSql.Append($" AND SchoolGradeClassIdz like '%{param.SchoolGradeClassId}%' "); //年级班级Id
                //}
                if (!param.CourseId.IsNullOrZero())
                {
                    strSql.Append($" AND CourseId = {param.CourseId}"); //学科
                }
                if (param.GradeId >= GradeEnum.GaoYi.ParseToInt())
                {
                    if (param.CompulsoryType == ClassCompulsoryTypeEnum.Select.ParseToInt())
                    {
                        if (param.CourseId > 0)
                        {
                            strSql.Append($" AND SelectSubject like '%{param.CourseId}%' "); //年级班级Id
                        }
                    }
                    else
                    {
                        if (param.CourseId > 0)
                        {
                            strSql.Append($" AND (ISNULL(SelectSubject,'') = '' OR  SelectSubject NOT like '%{param.CourseId}%' ) "); //年级班级Id
                        }
                    }
                }
                if (param.CompulsoryType > 0)
                {
                    strSql.Append($" AND CompulsoryType = {param.CompulsoryType} "); //实验来源
                }
                if (!param.Name.IsNullOrZero())
                {
                    strSql.Append($" AND ClassDesc like '%{param.Name}%' "); //年级班级Id
                }
                if (!string.IsNullOrEmpty(param.TeacherName))
                {
                    strSql.Append($" AND TeacherName like '%{param.TeacherName}%' "); //实验来源
                }
            }
            var list = await this.BaseRepository().FindList<PlanInfoEntity>(strSql.ToString(), parameter.ToArray(), pagination);
            return list.ToList();
        }
        #endregion

        #region 课外实验发布清单

        /// <summary>
        /// 课外实验发布清单列表集合
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<List<PlanInfoEntity>> GetOutPageList(PlanInfoListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append($@"  SELECT * From (
                                    SELECT
                                    pi1.Id ,
                                    pi1.BaseIsDelete ,
                                    pi1.BaseCreateTime ,
                                    pi1.BaseModifyTime ,
                                    pi1.BaseCreatorId AS CreatorId,
                                    pi1.BaseModifierId ,
                                    pi1.BaseVersion ,
                                    pi1.SchoolId ,
                                    pi1.GradeId ,
                                    pi1.CourseId ,
                                    pi1.SchoolYearStart ,
                                    pi1.SchoolYearEnd ,
                                    pi1.SchoolTerm ,
                                    pi1.PlanName ,
                                    pi1.Num ,
                                    pi1.CompulsoryType ,
                                    pi1.ActivityType ,
                                     pi1.Statuz ,
                                     pi1.PublishDate ,
                                    pi1.ClassIdz ,
                                    '' AS ClassNamez ,
                                    sd3.DicName AS GradeName ,
                                    sd4.DicName AS CourseName ,
                                    su5.RealName ,
                                    u2.Name AS SchoolName ,ur.UnitId AS CountyId ,u2.Sort ,
                                    CASE WHEN  (pi1.SchoolYearStart = {param.CurrentSchoolTermStartYear} AND pi1.SchoolTerm >= {param.CurrentSchoolTerm} ) OR  pi1.SchoolYearStart= {param.CurrentSchoolTermStartYear + 1} THEN 2 ELSE 1 END  AS IsExpiry ,
                                    pi1.NeedShowNum ,pi1.NeedGroupNum ,pi1.OptionalShowNum ,pi1.OptionalGroupNum ,
				                    pi1.SchoolNeedShowNum ,pi1.SchoolNeedGroupNum ,pi1.SchoolOptionalShowNum ,pi1.SchoolOptionalGroupNum ,
                                    dr.DictionaryId AS SchoolStageId ,SD.DicName AS SchoolStageName ,
                                    sa.AreaName AS CountyName ,UR2.UnitId AS CityId
                                    FROM  ex_PlanInfo AS pi1
                                    INNER JOIN  up_Unit AS u2 ON pi1.SchoolId = u2.Id AND u2.BaseIsDelete = 0 AND u2.Statuz = 1
                                    LEFT JOIN  sys_static_dictionary AS sd3 ON pi1.GradeId = sd3.DictionaryId
                                    LEFT JOIN  sys_dictionary_relation AS dr ON sd3.DictionaryId = dr.DictionaryToId  AND dr.RelationType = 1 AND dr.BaseIsDelete = 0
				                    LEFT JOIN  sys_static_dictionary AS SD ON dr.DictionaryId = SD.DictionaryId AND SD.TypeCode = '1002' AND SD.BaseIsDelete = 0
                                    INNER JOIN  sys_static_dictionary AS sd4 ON pi1.CourseId = sd4.DictionaryId
                                    INNER JOIN  SysUser AS su5 ON pi1.BaseCreatorId = su5.Id
                                    INNER JOIN  up_UnitRelation AS ur ON pi1.SchoolId = ur.ExtensionObjId AND ur.ExtensionType = 3 AND ur.BaseIsDelete = 0
                                    INNER JOIN  up_UnitRelation AS UR2 ON UR.UnitId = UR2.ExtensionObjId AND UR2.ExtensionType = 3 AND UR2.BaseIsDelete = 0
				                    INNER JOIN  up_Unit AS u3 ON ur.UnitId = u3.Id AND u3.BaseIsDelete = 0
				                    LEFT JOIN  SysArea AS sa ON sa.AreaCode = u3.AreaId AND sa.BaseIsDelete = 0
                           ) as tb1 WHERE   1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = @BaseIsDelete ");
                parameter.Add(DbParameterExtension.CreateDbParameter("@BaseIsDelete", param.BaseIsDelete));
                if (param.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    strSql.Append(" AND SchoolId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.UnitId));
                }
                else if (param.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    strSql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.UnitId));
                    if (param.SchoolId > 0)
                    {
                        strSql.Append(" AND SchoolId = @UnitId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.SchoolId));
                    }
                }
                else if (param.UnitType == UnitTypeEnum.City.ParseToInt())
                {
                    strSql.Append(" AND CityId = @CityId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CityId", param.UnitId));
                    if (param.CountyId > 0)
                    {
                        strSql.Append(" AND CountyId = @CountyId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                    }
                    if (param.SchoolId > 0)
                    {
                        strSql.Append(" AND SchoolId = @SchoolId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
                    }
                }
                else
                {
                    strSql.Append(" AND 1 <> 1 ");
                }
                if (param.GradeId > 0)
                {
                    strSql.Append(" AND GradeId = @GradeId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@GradeId", param.GradeId));
                }
                if (param.CourseId > 0)
                {
                    strSql.Append(" AND CourseId = @CourseId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (param.CourseIds != null && param.CourseIds.Count > 0)
                {
                    var wheretemp = new List<string>();
                    foreach (var item in param.CourseIds)
                    {
                        wheretemp.Add(string.Format(" CourseId = {0} ", item));
                    }
                    strSql.AppendFormat(" AND ({0}) ", string.Join(" OR ", wheretemp));
                }
                if (param.GradeIds != null && param.GradeIds.Count > 0)
                {
                    var wheretemp = new List<string>();
                    foreach (var item in param.GradeIds)
                    {
                        wheretemp.Add(string.Format(" GradeId = {0} ", item));
                    }
                    strSql.AppendFormat(" AND ({0}) ", string.Join(" OR ", wheretemp));
                }

                if (param.SchoolTerm > 0)
                {
                    strSql.Append(" AND SchoolTerm = @SchoolTerm ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolTerm", param.SchoolTerm));
                }
                if (param.SchoolYearStart > 0)
                {
                    strSql.Append(" AND SchoolYearStart = @SchoolYearStart ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolYearStart", param.SchoolYearStart));
                    //查询开始时间就足够了，不需要再加结束时间。
                    //param.SchoolYearEnd = (param.SchoolYearStart + 1);
                    //strSql.Append(" AND SchoolYearEnd = @SchoolYearEnd ");
                    //parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolYearEnd", param.SchoolYearEnd));
                }
                if (!string.IsNullOrEmpty(param.PlanName))
                {
                    strSql.Append(" AND PlanName like @Name ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Name", $"%{param.PlanName}%"));
                }
                if (param.SchoolStageId > 0) //学段
                {
                    strSql.Append(" AND SchoolStageId = @SchoolStageId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStageId", param.SchoolStageId));
                }
                if (param.CreatorId > 0) //编制人
                {
                    strSql.Append(" AND CreatorId = @CreatorId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CreatorId", param.CreatorId));
                }
                if (param.ExperimentType > 0) //实验类型
                {
                    if (param.ExperimentType == 1)  //演示
                    {
                        strSql.Append(" AND (NeedShowNum>0 OR OptionalShowNum>0 OR SchoolNeedShowNum>0 OR SchoolOptionalShowNum>0) ");
                    }
                    else if (param.ExperimentType == 2) //分组
                    {
                        strSql.Append(" AND (NeedGroupNum>0 OR OptionalGroupNum>0 OR SchoolNeedGroupNum>0 OR SchoolOptionalGroupNum>0) ");
                    }
                }
                if (param.IsNeedDo > 0) //实验要求
                {
                    if (param.IsNeedDo == 1)  //是
                    {
                        strSql.Append("AND (NeedShowNum>0 OR NeedGroupNum>0 OR SchoolNeedShowNum>0 OR SchoolNeedGroupNum>0) ");
                    }
                    else if (param.IsNeedDo == 2) //否
                    {
                        strSql.Append("AND (OptionalShowNum>0 OR OptionalGroupNum>0 OR SchoolOptionalShowNum>0 OR SchoolOptionalGroupNum>0) ");
                    }
                }
                if (param.IsShowCurrentSchoolYear > 0) //只显示当前学年
                {
                    strSql.Append($" AND SchoolYearStart = {param.CurrentSchoolTermStartYear} AND SchoolYearEnd = {param.CurrentSchoolTermStartYear + 1}");
                }

                if (param.SetUserId > 0)
                {

                    //判断用户是否设置了学段、学科
                    var listStage = userSubjectSetService.GetUserSetStageList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listStage.Count > 0)
                    {
                        string stageId = string.Join(",", listStage.Select(f => f.DictionaryId));
                        strSql.Append($" AND SchoolStageId IN ({stageId})");
                    }

                    var listCourse = userSubjectSetService.GetUserSetCourseList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listCourse.Count > 0)
                    {
                        string courseId = string.Join(",", listCourse.Select(f => f.DictionaryId));
                        strSql.Append($" AND CourseId IN ({courseId})");
                    }
                }
                if (param.CompulsoryType > 0)
                {
                    strSql.Append(" AND CompulsoryType = @CompulsoryType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CompulsoryType", param.CompulsoryType));

                }
                if (param.ActivityType != -10000)
                {
                    strSql.Append(" AND ActivityType = @ActivityType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ActivityType", param.ActivityType));
                }
                if (param.Statuz != -10000 && param.Statuz != -1)
                {
                    strSql.Append(" AND Statuz = @Statuz ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Statuz", param.Statuz));
                }
                if (param.Statuzge != -10000 && param.Statuzge != -1)
                {
                    strSql.Append(" AND Statuz >= @Statuzge ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Statuzge", param.Statuzge));
                }
                if (param.SubjectList!=null && param.SubjectList.Count > 0)
                {
                    strSql.Append($" AND ({string.Join(" OR ", param.SubjectList.Select(m => string.Format(" CourseId = {0} ", m)))}) ");
                }
            }
            var list = await this.BaseRepository().FindList<PlanInfoEntity>(strSql.ToString(), parameter.ToArray(),pagination);
            return list.ToList();
        }

        /// <summary>
        /// 课外实验发布清单列表集合
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<List<SchoolExperimentEntity>> GetOutPlanExperimentPageList(PlanInfoListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(@$" SELECT * FROM ( SELECT se.Id
                                , se.ActivityType
                                , se.ApplicableGrade
                                , se.BaseCreateTime
                                , se.BaseCreatorId
                                , se.BaseIsDelete
                                , se.BaseModifierId
                                , se.BaseModifyTime
                                , se.BaseVersion
                                , se.Chapter
                                , se.CourseId
                                , se.EquipmentNeed
                                , se.ExperimentCode
                                , se.ExperimentName
                                , se.ExperimentType
                                , se.GradeId
                                , se.IsNeedDo
                                , se.ItemDescription
                                , se.MaterialNeed
                                , se.Preparation
                                , se.Remark
                                , se.SchoolId
                                , se.SchoolStage

                                , se.Statuz
								,p.SchoolYearStart
								,p.SchoolYearEnd
								,p.SchoolTerm
                                ,p.Statuz AS PlanStatuz
                                ,p.Id AS PlanInfoId
                                ,u.Name AS UnitName
                                ,u.Sort
								,sdic.DictValue AS ExperimentTypeName
								FROM ex_SchoolExperiment AS se
								INNER JOIN ex_PlanInfo AS p ON se.SchoolId = p.SchoolId AND se.ActivityType = p.ActivityType AND se.CourseId = p.CourseId
                                INNER JOIN up_Unit AS u ON u.BaseIsDelete = 0 AND se.SchoolId = u.Id
								INNER JOIN  sys_static_dictionary AS dic3 ON se.CourseId = dic3.DictionaryId AND dic3.TypeCode = '{DicTypeCodeEnum.Course.ParseToInt().ToString()}'
								LEFT JOIN SysDataDictDetail AS sdic ON dic3.BaseIsDelete = 0 AND sdic.TypeCode = 'OET101001' ANd se.SchoolId = sdic.UnitId AND se.ExperimentType = sdic.DictKey
                          ) as T WHERE  1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.Id > 0)
                {
                    strSql.Append(" AND PlanInfoId = @PlanInfoId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@PlanInfoId", param.Id));
                }
                if (param.ActivityType!=-10000)
                {
                    strSql.Append(" AND ActivityType = @ActivityType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ActivityType", param.ActivityType));
                }
                if (param.CourseId > 0)
                {
                    strSql.Append(" AND CourseId = @CourseId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (param.SchoolYearStart > 0)
                {
                    strSql.Append(" AND SchoolYearStart = @SchoolYearStart ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolYearStart", param.SchoolYearStart));
                }
                if (param.SchoolTerm > 0)
                {
                    strSql.Append(" AND SchoolTerm = @SchoolTerm ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolTerm", param.SchoolTerm));
                }
                if (param.ExperimentType > 0)
                {
                    strSql.Append(" AND ExperimentType = @ExperimentType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolTerm", param.ExperimentType));
                }
                //适用年级
                if (param.GradeId> 0)
                {
                    strSql.Append(" AND ApplicableGrade like @GradeId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@GradeId", $"%{param.GradeId}%"));
                }
                if (param.Statuz != -10000 && param.Statuz != -1)
                {
                    strSql.Append(" AND PlanStatuz = @PlanStatuz ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@PlanStatuz", param.Statuz));
                }
                if (!string.IsNullOrEmpty(param.Name))
                {
                    strSql.Append(" AND ExperimentName like @Name ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Name", $"%{param.Name}%"));
                }
                if (param.SchoolIdList != null && param.SchoolIdList.Count > 0)
                {
                    strSql.Append($" AND ({string.Join(" OR ", param.SchoolIdList.Select(m => string.Format(" SchoolId = {0} ", m)))})");
                }
                if (param.SchoolId > 0)
                {
                    strSql.Append(" AND SchoolId = @SchoolId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
                }
            }
            var list = await this.BaseRepository().FindList<SchoolExperimentEntity>(strSql.ToString(), parameter.ToArray(),pagination);
            return list.ToList();
        }
        #endregion

        #region 课外实验 - 私有方法

        public async Task UpdateOutPlanNumTermCourseNum(long schoolid,int startyear,int schoolterm, int courseid, Repository db)
        {
            string strSql = @$" UPDATE ex_PlanInfo
                                SET Num = ( SELECT COUNT(Id)  FROM ex_SchoolExperiment WHERE CourseId = {courseid} AND SchoolId = {schoolid} AND Statuz = 1 AND BaseIsDelete = 0 AND ActivityType = 2)
                                WHERE BaseIsDelete = 0 AND SchoolYearStart = {startyear} AND SchoolTerm = {schoolterm} AND SchoolId = {schoolid} AND CourseId = {courseid} AND ActivityType = 2 ";

            if (db!=null)
            {
                await db.ExecuteBySql(strSql);
            }
            else
            {
                await this.BaseRepository().ExecuteBySql(strSql);
            }

        }
        #endregion
    }
}
