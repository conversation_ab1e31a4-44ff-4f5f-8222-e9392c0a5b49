2025-06-10 11:25:03.4824||DEBUG||程序启动 |ContentRootPath:D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web |WebRootPath:D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\wwwroot |IsDevelopment:True |url: |action: 
2025-06-10 11:25:59.6763||INFO||URL1:/OrganizationManage/Unit/UnitSysIndex |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-10 11:35:51.7631||INFO||URL1:/ArticleManager/Article/ArticleIndex |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-10 11:36:01.6959||INFO||URL1:/ArticleManager/ArticleCategory/ArticleCategoryIndex |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-10 11:51:55.1824||INFO||URL1:/EvaluateManage/InstrumentAttendStatic/InstrumentGroupStaticIndex |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-10 11:52:01.1052||WARN||耗时的Sql：
                SELECT S.InstrumentEvaluateProjectId,
                       UR2.UnitId AS CityId,
                       UR.UnitId AS CountyId,
                       U2.Name AS CountyName,
                       U2.Sort AS CountySort,
                       AR.AreaName,
                       S.SchoolId,
                       U.Name AS SchoolName,
                       U.Sort AS SchoolSort,
                       S.StageId,
                       SD.DicName AS StageName,
                       S.CourseId,
                       SD2.DicName AS CourseName,
                       S.AllocateType,
                       COUNT(1) AS NeedStandardCount,
                       (SELECT COUNT(1)
                        FROM eq_InstrumentAttendStatic AS IAS
                        INNER JOIN eq_InstrumentEvaluateList AS IEL ON IAS.EvaluateListId = IEL.Id
                                   AND IEL.Statuz = 1 AND IEL.BaseIsDelete = 0
                        WHERE IAS.BaseIsDelete = 0
                              AND IAS.InstrumentEvaluateProjectId = S.InstrumentEvaluateProjectId
                              AND IAS.SchoolId = S.SchoolId
                              AND IAS.StageId = S.StageId
                              AND IAS.CourseId = S.CourseId
                              AND IAS.AllocateType = S.AllocateType
                              AND IAS.StockNum >= IAS.StandardNum
                              AND IEL.IsEvaluate = 1) AS StandardCount
                FROM eq_InstrumentAttendStatic AS S
                INNER JOIN eq_InstrumentEvaluateList AS EL ON S.EvaluateListId = EL.Id
                           AND EL.BaseIsDelete = 0 AND EL.Statuz = 1
                INNER JOIN sys_static_dictionary AS SD ON S.StageId = SD.DictionaryId
                           AND SD.TypeCode = 1002 AND SD.BaseIsDelete = 0
                INNER JOIN sys_static_dictionary AS SD2 ON S.CourseId = SD2.DictionaryId
                           AND SD2.TypeCode = 1005 AND SD2.BaseIsDelete = 0
                INNER JOIN up_Unit AS U ON S.SchoolId = U.Id
                INNER JOIN up_UnitRelation AS UR ON UR.ExtensionObjId = S.SchoolId
                           AND UR.ExtensionType = 3 AND UR.BaseIsDelete = 0
                INNER JOIN up_UnitRelation AS UR2 ON UR2.ExtensionObjId = UR.UnitId
                           AND UR2.ExtensionType = 3 AND UR2.BaseIsDelete = 0
                INNER JOIN up_Unit AS U2 ON UR.UnitId = U2.Id
                INNER JOIN SysArea AS AR ON U2.AreaId = AR.AreaCode
                WHERE S.BaseIsDelete = 0 AND EL.IsEvaluate = 1 AND S.SchoolId = 395948030106275840 AND S.InstrumentEvaluateProjectId = 657935141812834304
                GROUP BY S.InstrumentEvaluateProjectId, S.SchoolId, S.StageId, S.CourseId, S.AllocateType,
                         U.Sort, U2.Sort, UR.UnitId, UR2.UnitId, U.Name, U2.Name, AR.AreaName,
                         SD.DicName, SD2.DicName |url: http://localhost/EvaluateManage/InstrumentAttendStatic/GetGroupPageListJson|action: GetGroupPageListJson
2025-06-10 11:52:04.1673||INFO||URL1:/InstrumentManage/PurchaseStatistics/PurchaseGroupStaticIndex |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-10 11:52:42.2502||INFO||URL1:/EvaluateManage/InstrumentAttendStatic/InstrumentGroupStaticIndex |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-10 11:52:42.7387||ERROR||Value cannot be null. (Parameter 'source')
   at System.Linq.ThrowHelper.ThrowArgumentNullException(ExceptionArgument argument)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Dqy.Syjx.Service.OrganizationManage.UnitService.GetChildrenPageList(UnitListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Service\OrganizationManage\UnitService.cs:line 51
   at Dqy.Syjx.Business.OrganizationManage.UnitBLL.GetChildrenPageList(UnitListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Business\OrganizationManage\UnitBLL.cs:line 65
   at Dqy.Syjx.Web.Areas.OrganizationManage.Controllers.UnitController.GetChildrenPageList(UnitListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\Areas\OrganizationManage\Controllers\UnitController.cs:line 162
   at lambda_method2666(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
 |url: http://localhost/OrganizationManage/Unit/GetChildrenPageList|action: GetChildrenPageList
2025-06-10 11:52:45.2870||ERROR||Value cannot be null. (Parameter 'source')
   at System.Linq.ThrowHelper.ThrowArgumentNullException(ExceptionArgument argument)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Dqy.Syjx.Service.EvaluateManage.InstrumentAttendStaticService.GetPageList(InstrumentAttendStaticListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Service\EvaluateManage\InstrumentAttendStaticService.cs:line 166
   at Dqy.Syjx.Business.EvaluateManage.InstrumentAttendStaticBLL.GetPageList(InstrumentAttendStaticListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Business\EvaluateManage\InstrumentAttendStaticBLL.cs:line 152
   at Dqy.Syjx.Web.Areas.EvaluateManage.Controllers.InstrumentAttendStaticController.ExportDetailStatic(InstrumentAttendStaticListParam param) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\Areas\EvaluateManage\Controllers\InstrumentAttendStaticController.cs:line 300
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
 |url: http://localhost/EvaluateManage/InstrumentAttendStatic/ExportDetailStatic|action: ExportDetailStatic
2025-06-10 11:52:55.3929||ERROR||Value cannot be null. (Parameter 'source')
   at System.Linq.ThrowHelper.ThrowArgumentNullException(ExceptionArgument argument)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Dqy.Syjx.Service.EvaluateManage.InstrumentAttendStaticService.GetPageList(InstrumentAttendStaticListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Service\EvaluateManage\InstrumentAttendStaticService.cs:line 166
   at Dqy.Syjx.Business.EvaluateManage.InstrumentAttendStaticBLL.GetPageList(InstrumentAttendStaticListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Business\EvaluateManage\InstrumentAttendStaticBLL.cs:line 152
   at Dqy.Syjx.Web.Areas.EvaluateManage.Controllers.InstrumentAttendStaticController.ExportDetailStatic(InstrumentAttendStaticListParam param) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\Areas\EvaluateManage\Controllers\InstrumentAttendStaticController.cs:line 300
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
 |url: http://localhost/EvaluateManage/InstrumentAttendStatic/ExportDetailStatic|action: ExportDetailStatic
2025-06-10 11:53:25.7194||ERROR||Value cannot be null. (Parameter 'source')
   at System.Linq.ThrowHelper.ThrowArgumentNullException(ExceptionArgument argument)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Dqy.Syjx.Service.EvaluateManage.InstrumentAttendStaticService.GetPageList(InstrumentAttendStaticListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Service\EvaluateManage\InstrumentAttendStaticService.cs:line 166
   at Dqy.Syjx.Business.EvaluateManage.InstrumentAttendStaticBLL.GetPageList(InstrumentAttendStaticListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Business\EvaluateManage\InstrumentAttendStaticBLL.cs:line 152
   at Dqy.Syjx.Web.Areas.EvaluateManage.Controllers.InstrumentAttendStaticController.ExportDetailStatic(InstrumentAttendStaticListParam param) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\Areas\EvaluateManage\Controllers\InstrumentAttendStaticController.cs:line 300
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
 |url: http://localhost/EvaluateManage/InstrumentAttendStatic/ExportDetailStatic|action: ExportDetailStatic
