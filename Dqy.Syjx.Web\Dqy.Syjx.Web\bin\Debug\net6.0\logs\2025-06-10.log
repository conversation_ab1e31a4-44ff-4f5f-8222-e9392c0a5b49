2025-06-10 11:25:03.4824||DEBUG||程序启动 |ContentRootPath:D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web |WebRootPath:D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\wwwroot |IsDevelopment:True |url: |action: 
2025-06-10 11:25:59.6763||INFO||URL1:/OrganizationManage/Unit/UnitSysIndex |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-10 11:35:51.7631||INFO||URL1:/ArticleManager/Article/ArticleIndex |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-10 11:36:01.6959||INFO||URL1:/ArticleManager/ArticleCategory/ArticleCategoryIndex |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-10 11:51:55.1824||INFO||URL1:/EvaluateManage/InstrumentAttendStatic/InstrumentGroupStaticIndex |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-10 11:52:01.1052||WARN||耗时的Sql：
                SELECT S.InstrumentEvaluateProjectId,
                       UR2.UnitId AS CityId,
                       UR.UnitId AS CountyId,
                       U2.Name AS CountyName,
                       U2.Sort AS CountySort,
                       AR.AreaName,
                       S.SchoolId,
                       U.Name AS SchoolName,
                       U.Sort AS SchoolSort,
                       S.StageId,
                       SD.DicName AS StageName,
                       S.CourseId,
                       SD2.DicName AS CourseName,
                       S.AllocateType,
                       COUNT(1) AS NeedStandardCount,
                       (SELECT COUNT(1)
                        FROM eq_InstrumentAttendStatic AS IAS
                        INNER JOIN eq_InstrumentEvaluateList AS IEL ON IAS.EvaluateListId = IEL.Id
                                   AND IEL.Statuz = 1 AND IEL.BaseIsDelete = 0
                        WHERE IAS.BaseIsDelete = 0
                              AND IAS.InstrumentEvaluateProjectId = S.InstrumentEvaluateProjectId
                              AND IAS.SchoolId = S.SchoolId
                              AND IAS.StageId = S.StageId
                              AND IAS.CourseId = S.CourseId
                              AND IAS.AllocateType = S.AllocateType
                              AND IAS.StockNum >= IAS.StandardNum
                              AND IEL.IsEvaluate = 1) AS StandardCount
                FROM eq_InstrumentAttendStatic AS S
                INNER JOIN eq_InstrumentEvaluateList AS EL ON S.EvaluateListId = EL.Id
                           AND EL.BaseIsDelete = 0 AND EL.Statuz = 1
                INNER JOIN sys_static_dictionary AS SD ON S.StageId = SD.DictionaryId
                           AND SD.TypeCode = 1002 AND SD.BaseIsDelete = 0
                INNER JOIN sys_static_dictionary AS SD2 ON S.CourseId = SD2.DictionaryId
                           AND SD2.TypeCode = 1005 AND SD2.BaseIsDelete = 0
                INNER JOIN up_Unit AS U ON S.SchoolId = U.Id
                INNER JOIN up_UnitRelation AS UR ON UR.ExtensionObjId = S.SchoolId
                           AND UR.ExtensionType = 3 AND UR.BaseIsDelete = 0
                INNER JOIN up_UnitRelation AS UR2 ON UR2.ExtensionObjId = UR.UnitId
                           AND UR2.ExtensionType = 3 AND UR2.BaseIsDelete = 0
                INNER JOIN up_Unit AS U2 ON UR.UnitId = U2.Id
                INNER JOIN SysArea AS AR ON U2.AreaId = AR.AreaCode
                WHERE S.BaseIsDelete = 0 AND EL.IsEvaluate = 1 AND S.SchoolId = 395948030106275840 AND S.InstrumentEvaluateProjectId = 657935141812834304
                GROUP BY S.InstrumentEvaluateProjectId, S.SchoolId, S.StageId, S.CourseId, S.AllocateType,
                         U.Sort, U2.Sort, UR.UnitId, UR2.UnitId, U.Name, U2.Name, AR.AreaName,
                         SD.DicName, SD2.DicName |url: http://localhost/EvaluateManage/InstrumentAttendStatic/GetGroupPageListJson|action: GetGroupPageListJson
2025-06-10 11:52:04.1673||INFO||URL1:/InstrumentManage/PurchaseStatistics/PurchaseGroupStaticIndex |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-10 11:52:42.2502||INFO||URL1:/EvaluateManage/InstrumentAttendStatic/InstrumentGroupStaticIndex |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-10 11:52:42.7387||ERROR||Value cannot be null. (Parameter 'source')
   at System.Linq.ThrowHelper.ThrowArgumentNullException(ExceptionArgument argument)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Dqy.Syjx.Service.OrganizationManage.UnitService.GetChildrenPageList(UnitListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Service\OrganizationManage\UnitService.cs:line 51
   at Dqy.Syjx.Business.OrganizationManage.UnitBLL.GetChildrenPageList(UnitListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Business\OrganizationManage\UnitBLL.cs:line 65
   at Dqy.Syjx.Web.Areas.OrganizationManage.Controllers.UnitController.GetChildrenPageList(UnitListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\Areas\OrganizationManage\Controllers\UnitController.cs:line 162
   at lambda_method2666(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
 |url: http://localhost/OrganizationManage/Unit/GetChildrenPageList|action: GetChildrenPageList
2025-06-10 11:52:45.2870||ERROR||Value cannot be null. (Parameter 'source')
   at System.Linq.ThrowHelper.ThrowArgumentNullException(ExceptionArgument argument)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Dqy.Syjx.Service.EvaluateManage.InstrumentAttendStaticService.GetPageList(InstrumentAttendStaticListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Service\EvaluateManage\InstrumentAttendStaticService.cs:line 166
   at Dqy.Syjx.Business.EvaluateManage.InstrumentAttendStaticBLL.GetPageList(InstrumentAttendStaticListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Business\EvaluateManage\InstrumentAttendStaticBLL.cs:line 152
   at Dqy.Syjx.Web.Areas.EvaluateManage.Controllers.InstrumentAttendStaticController.ExportDetailStatic(InstrumentAttendStaticListParam param) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\Areas\EvaluateManage\Controllers\InstrumentAttendStaticController.cs:line 300
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
 |url: http://localhost/EvaluateManage/InstrumentAttendStatic/ExportDetailStatic|action: ExportDetailStatic
2025-06-10 11:52:55.3929||ERROR||Value cannot be null. (Parameter 'source')
   at System.Linq.ThrowHelper.ThrowArgumentNullException(ExceptionArgument argument)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Dqy.Syjx.Service.EvaluateManage.InstrumentAttendStaticService.GetPageList(InstrumentAttendStaticListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Service\EvaluateManage\InstrumentAttendStaticService.cs:line 166
   at Dqy.Syjx.Business.EvaluateManage.InstrumentAttendStaticBLL.GetPageList(InstrumentAttendStaticListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Business\EvaluateManage\InstrumentAttendStaticBLL.cs:line 152
   at Dqy.Syjx.Web.Areas.EvaluateManage.Controllers.InstrumentAttendStaticController.ExportDetailStatic(InstrumentAttendStaticListParam param) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\Areas\EvaluateManage\Controllers\InstrumentAttendStaticController.cs:line 300
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
 |url: http://localhost/EvaluateManage/InstrumentAttendStatic/ExportDetailStatic|action: ExportDetailStatic
2025-06-10 11:53:25.7194||ERROR||Value cannot be null. (Parameter 'source')
   at System.Linq.ThrowHelper.ThrowArgumentNullException(ExceptionArgument argument)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Dqy.Syjx.Service.EvaluateManage.InstrumentAttendStaticService.GetPageList(InstrumentAttendStaticListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Service\EvaluateManage\InstrumentAttendStaticService.cs:line 166
   at Dqy.Syjx.Business.EvaluateManage.InstrumentAttendStaticBLL.GetPageList(InstrumentAttendStaticListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Business\EvaluateManage\InstrumentAttendStaticBLL.cs:line 152
   at Dqy.Syjx.Web.Areas.EvaluateManage.Controllers.InstrumentAttendStaticController.ExportDetailStatic(InstrumentAttendStaticListParam param) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\Areas\EvaluateManage\Controllers\InstrumentAttendStaticController.cs:line 300
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
 |url: http://localhost/EvaluateManage/InstrumentAttendStatic/ExportDetailStatic|action: ExportDetailStatic
2025-06-10 15:49:36.4669||DEBUG||程序启动 |ContentRootPath:D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web |WebRootPath:D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\wwwroot |IsDevelopment:True |url: |action: 
2025-06-10 15:50:01.1741||INFO||URL1:/InstrumentManage/PurchaseStatistics/PurchaseSchoolGroupStatisticsIndex |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-10 15:50:03.0908||INFO||URL1:/InstrumentManage/PurchaseStatistics/PurchaseGroupStaticIndex?purchaseYear2025 |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-10 15:50:08.7443||INFO||URL1:/InstrumentManage/PurchaseStatistics/PurchasePlanAnalyseIndex |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-10 15:50:09.6414||ERROR||Value cannot be null. (Parameter 'source')
   at System.Linq.ThrowHelper.ThrowArgumentNullException(ExceptionArgument argument)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Dqy.Syjx.Service.InstrumentManage.PurchaseDeclarationService.GetSchoolPurchasePlanAnalyseByCounty(PurchaseDeclarationListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Service\InstrumentManage\PurchaseDeclarationService.cs:line 789
   at Dqy.Syjx.Business.InstrumentManage.PurchaseDeclarationBLL.GetCountyPurchasePlanAnalyseList(PurchaseDeclarationListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Business\InstrumentManage\PurchaseDeclarationBLL.cs:line 333
   at Dqy.Syjx.Web.Areas.InstrumentManage.Controllers.PurchaseStatisticsController.GetCountyPurchasePlanAnalyseListJson(PurchaseDeclarationListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\Areas\InstrumentManage\Controllers\PurchaseStatisticsController.cs:line 272
   at lambda_method1009(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
 |url: http://localhost/InstrumentManage/PurchaseStatistics/GetCountyPurchasePlanAnalyseListJson|action: GetCountyPurchasePlanAnalyseListJson
2025-06-10 15:50:16.6320||ERROR||Value cannot be null. (Parameter 'source')
   at System.Linq.ThrowHelper.ThrowArgumentNullException(ExceptionArgument argument)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Dqy.Syjx.Service.InstrumentManage.PurchaseDeclarationService.GetSchoolPurchasePlanAnalyseByCounty(PurchaseDeclarationListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Service\InstrumentManage\PurchaseDeclarationService.cs:line 789
   at Dqy.Syjx.Business.InstrumentManage.PurchaseDeclarationBLL.GetCountyPurchasePlanAnalyseList(PurchaseDeclarationListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Business\InstrumentManage\PurchaseDeclarationBLL.cs:line 333
   at Dqy.Syjx.Web.Areas.InstrumentManage.Controllers.PurchaseStatisticsController.GetCountyPurchasePlanAnalyseListJson(PurchaseDeclarationListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\Areas\InstrumentManage\Controllers\PurchaseStatisticsController.cs:line 272
   at lambda_method1009(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
 |url: http://localhost/InstrumentManage/PurchaseStatistics/GetCountyPurchasePlanAnalyseListJson|action: GetCountyPurchasePlanAnalyseListJson
2025-06-10 15:50:18.1665||ERROR||Value cannot be null. (Parameter 'source')
   at System.Linq.ThrowHelper.ThrowArgumentNullException(ExceptionArgument argument)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Dqy.Syjx.Service.InstrumentManage.PurchaseDeclarationService.GetSchoolPurchasePlanAnalyseByCounty(PurchaseDeclarationListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Service\InstrumentManage\PurchaseDeclarationService.cs:line 789
   at Dqy.Syjx.Business.InstrumentManage.PurchaseDeclarationBLL.GetCountyPurchasePlanAnalyseList(PurchaseDeclarationListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Business\InstrumentManage\PurchaseDeclarationBLL.cs:line 333
   at Dqy.Syjx.Web.Areas.InstrumentManage.Controllers.PurchaseStatisticsController.GetCountyPurchasePlanAnalyseListJson(PurchaseDeclarationListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\Areas\InstrumentManage\Controllers\PurchaseStatisticsController.cs:line 272
   at lambda_method1009(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
 |url: http://localhost/InstrumentManage/PurchaseStatistics/GetCountyPurchasePlanAnalyseListJson|action: GetCountyPurchasePlanAnalyseListJson
2025-06-10 15:52:04.9874||ERROR||Value cannot be null. (Parameter 'source')
   at System.Linq.ThrowHelper.ThrowArgumentNullException(ExceptionArgument argument)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Dqy.Syjx.Service.InstrumentManage.PurchaseDeclarationService.GetSchoolPurchasePlanAnalyseByCounty(PurchaseDeclarationListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Service\InstrumentManage\PurchaseDeclarationService.cs:line 789
   at Dqy.Syjx.Business.InstrumentManage.PurchaseDeclarationBLL.GetCountyPurchasePlanAnalyseList(PurchaseDeclarationListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Business\InstrumentManage\PurchaseDeclarationBLL.cs:line 333
   at Dqy.Syjx.Web.Areas.InstrumentManage.Controllers.PurchaseStatisticsController.GetCountyPurchasePlanAnalyseListJson(PurchaseDeclarationListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\Areas\InstrumentManage\Controllers\PurchaseStatisticsController.cs:line 272
   at lambda_method1009(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
 |url: http://localhost/InstrumentManage/PurchaseStatistics/GetCountyPurchasePlanAnalyseListJson|action: GetCountyPurchasePlanAnalyseListJson
2025-06-10 16:02:33.3151||ERROR||Value cannot be null. (Parameter 'source')
   at System.Linq.ThrowHelper.ThrowArgumentNullException(ExceptionArgument argument)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Dqy.Syjx.Service.InstrumentManage.PurchaseDeclarationService.GetSchoolPurchasePlanAnalyseByCounty(PurchaseDeclarationListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Service\InstrumentManage\PurchaseDeclarationService.cs:line 789
   at Dqy.Syjx.Business.InstrumentManage.PurchaseDeclarationBLL.GetCountyPurchasePlanAnalyseList(PurchaseDeclarationListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Business\InstrumentManage\PurchaseDeclarationBLL.cs:line 333
   at Dqy.Syjx.Web.Areas.InstrumentManage.Controllers.PurchaseStatisticsController.GetCountyPurchasePlanAnalyseListJson(PurchaseDeclarationListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\Areas\InstrumentManage\Controllers\PurchaseStatisticsController.cs:line 272
   at lambda_method1009(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
 |url: http://localhost/InstrumentManage/PurchaseStatistics/GetCountyPurchasePlanAnalyseListJson|action: GetCountyPurchasePlanAnalyseListJson
2025-06-10 17:16:42.4021||DEBUG||程序启动 |ContentRootPath:D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web |WebRootPath:D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\wwwroot |IsDevelopment:True |url: |action: 
2025-06-10 17:20:03.4434||INFO||URL1:/InstrumentManage/PurchaseStatistics/PurchaseGroupStaticIndex |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-10 17:20:07.2575||INFO||URL1:/QueryStatisticsManage/Instrument/CountyInstrumentStatistic |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-10 17:20:10.2166||INFO||URL1:/EvaluateManage/InstrumentAttendStatic/InstrumentGroupStaticIndex |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-10 17:20:17.8968||INFO||URL1:/EvaluateManage/InstrumentAttendStatic/StandardResultAnalyseIndex |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-10 17:20:18.5968||ERROR||Value cannot be null. (Parameter 'source')
   at System.Linq.ThrowHelper.ThrowArgumentNullException(ExceptionArgument argument)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Dqy.Syjx.Service.EvaluateManage.InstrumentAttendStaticService.GetCountyStandardResultAnalyseList(InstrumentAttendStaticListParam param, String orderBy) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Service\EvaluateManage\InstrumentAttendStaticService.cs:line 551
   at Dqy.Syjx.Business.EvaluateManage.InstrumentAttendStaticBLL.GetCountyStandardResultAnalyseList(InstrumentAttendStaticListParam param) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Business\EvaluateManage\InstrumentAttendStaticBLL.cs:line 199
   at Dqy.Syjx.Web.Areas.EvaluateManage.Controllers.InstrumentAttendStaticController.GetCountyStandardResultAnalyseListJson(InstrumentAttendStaticListParam param) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\Areas\EvaluateManage\Controllers\InstrumentAttendStaticController.cs:line 124
   at lambda_method1263(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
 |url: http://localhost/EvaluateManage/InstrumentAttendStatic/GetCountyStandardResultAnalyseListJson|action: GetCountyStandardResultAnalyseListJson
2025-06-10 17:20:28.7325||INFO||URL1:/EvaluateManage/InstrumentAttendStatic/StandardResultAnalyseIndex |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-10 17:20:29.0660||ERROR||Value cannot be null. (Parameter 'source')
   at System.Linq.ThrowHelper.ThrowArgumentNullException(ExceptionArgument argument)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Dqy.Syjx.Service.EvaluateManage.InstrumentAttendStaticService.GetCountyStandardResultAnalyseList(InstrumentAttendStaticListParam param, String orderBy) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Service\EvaluateManage\InstrumentAttendStaticService.cs:line 551
   at Dqy.Syjx.Business.EvaluateManage.InstrumentAttendStaticBLL.GetCountyStandardResultAnalyseList(InstrumentAttendStaticListParam param) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Business\EvaluateManage\InstrumentAttendStaticBLL.cs:line 199
   at Dqy.Syjx.Web.Areas.EvaluateManage.Controllers.InstrumentAttendStaticController.GetCountyStandardResultAnalyseListJson(InstrumentAttendStaticListParam param) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\Areas\EvaluateManage\Controllers\InstrumentAttendStaticController.cs:line 124
   at lambda_method1263(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
 |url: http://localhost/EvaluateManage/InstrumentAttendStatic/GetCountyStandardResultAnalyseListJson|action: GetCountyStandardResultAnalyseListJson
2025-06-10 17:20:57.9233||INFO||URL1:/InstrumentManage/PurchaseStatistics/PurchasePlanAnalyseIndex |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-10 17:21:02.7485||ERROR||Value cannot be null. (Parameter 'source')
   at System.Linq.ThrowHelper.ThrowArgumentNullException(ExceptionArgument argument)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Dqy.Syjx.Service.InstrumentManage.PurchaseDeclarationService.GetSchoolPurchasePlanAnalyseByCounty(PurchaseDeclarationListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Service\InstrumentManage\PurchaseDeclarationService.cs:line 789
   at Dqy.Syjx.Business.InstrumentManage.PurchaseDeclarationBLL.GetCountyPurchasePlanAnalyseList(PurchaseDeclarationListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Business\InstrumentManage\PurchaseDeclarationBLL.cs:line 333
   at Dqy.Syjx.Web.Areas.InstrumentManage.Controllers.PurchaseStatisticsController.GetCountyPurchasePlanAnalyseListJson(PurchaseDeclarationListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\Areas\InstrumentManage\Controllers\PurchaseStatisticsController.cs:line 272
   at lambda_method1291(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
 |url: http://localhost/InstrumentManage/PurchaseStatistics/GetCountyPurchasePlanAnalyseListJson|action: GetCountyPurchasePlanAnalyseListJson
2025-06-10 17:31:32.1780||DEBUG||程序启动 |ContentRootPath:D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web |WebRootPath:D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\wwwroot |IsDevelopment:True |url: |action: 
2025-06-10 17:31:55.4746||INFO||URL1:/InstrumentManage/PurchaseStatistics/PurchasePlanAnalyseIndex |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-10 17:31:56.0476||ERROR||Value cannot be null. (Parameter 'source')
   at System.Linq.ThrowHelper.ThrowArgumentNullException(ExceptionArgument argument)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Dqy.Syjx.Service.InstrumentManage.PurchaseDeclarationService.GetSchoolPurchasePlanAnalyseByCounty(PurchaseDeclarationListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Service\InstrumentManage\PurchaseDeclarationService.cs:line 789
   at Dqy.Syjx.Business.InstrumentManage.PurchaseDeclarationBLL.GetCountyPurchasePlanAnalyseList(PurchaseDeclarationListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Business\InstrumentManage\PurchaseDeclarationBLL.cs:line 333
   at Dqy.Syjx.Web.Areas.InstrumentManage.Controllers.PurchaseStatisticsController.GetCountyPurchasePlanAnalyseListJson(PurchaseDeclarationListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\Areas\InstrumentManage\Controllers\PurchaseStatisticsController.cs:line 272
   at lambda_method814(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
 |url: http://localhost/InstrumentManage/PurchaseStatistics/GetCountyPurchasePlanAnalyseListJson|action: GetCountyPurchasePlanAnalyseListJson
2025-06-10 17:52:33.0070||DEBUG||程序启动 |ContentRootPath:D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web |WebRootPath:D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\wwwroot |IsDevelopment:True |url: |action: 
2025-06-10 17:53:03.3701||INFO||URL1:/InstrumentManage/PurchaseStatistics/PurchasePlanAnalyseIndex |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-10 17:53:16.0747||INFO||URL1:/InstrumentManage/PurchaseStatistics/PurchaseSchoolGroupStatisticsIndex |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-10 17:55:10.6905||INFO||URL1:/QueryStatisticsManage/Instrument/CountyInstrumentSum |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-10 17:55:31.4144||INFO||URL1:/QueryStatisticsManage/Instrument/CountyStockDetail?SchoolId0&Purchase2025 |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-10 17:55:34.1238||INFO||URL1:/QueryStatisticsManage/Instrument/CountyInstrumentReduce?SchoolId0&Purchase2025 |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-10 17:55:34.9394||ERROR||Value cannot be null. (Parameter 'source')
   at System.Linq.ThrowHelper.ThrowArgumentNullException(ExceptionArgument argument)
   at System.Linq.Enumerable.Count[TSource](IEnumerable`1 source)
   at Dqy.Syjx.Service.QueryStatisticsManage.InstrumentService.GetReduceList(ReduceParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Service\QueryStatisticsManage\InstrumentService.cs:line 813
   at Dqy.Syjx.Business.QueryStatisticsManage.InstrumentBLL.ReduceInstrumentList(ReduceParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Business\QueryStatisticsManage\InstrumentBLL.cs:line 273
   at Dqy.Syjx.Web.Areas.QueryStatisticsManage.Controllers.InstrumentController.GetReduceInstrumentListJson(ReduceParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\Areas\QueryStatisticsManage\Controllers\InstrumentController.cs:line 162
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
 |url: http://localhost/QueryStatisticsManage/Instrument/GetReduceInstrumentListJson|action: GetReduceInstrumentListJson
2025-06-10 17:55:45.2592||INFO||URL1:/QueryStatisticsManage/Instrument/CountyInstrumentStat?SchoolId0&Purchase2025 |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-10 17:55:45.6765||ERROR||Value cannot be null. (Parameter 'source')
   at System.Linq.ThrowHelper.ThrowArgumentNullException(ExceptionArgument argument)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Dqy.Syjx.Service.BusinessManage.FunRoomService.GetPageList(FunRoomListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Service\BusinessManage\FunRoomService.cs:line 122
   at Dqy.Syjx.Business.BusinessManage.FunRoomBLL.GetListBySafeguardUserId(FunRoomListParam param) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Business\BusinessManage\FunRoomBLL.cs:line 368
   at Dqy.Syjx.Business.BusinessManage.CupboardBLL.GetCupboardTreeList(FunRoomListParam param) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Business\BusinessManage\CupboardBLL.cs:line 84
   at Dqy.Syjx.Web.Areas.BusinessManage.Controllers.CupboardController.GetCupboardTreeListAllJson() in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\Areas\BusinessManage\Controllers\CupboardController.cs:line 92
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
 |url: http://localhost/BusinessManage/Cupboard/GetCupboardTreeListAllJson|action: GetCupboardTreeListAllJson
2025-06-10 17:55:51.8583||INFO||URL1:/QueryStatisticsManage/Instrument/CountyInstrumentStorage |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-10 17:55:55.2303||INFO||URL1:/EvaluateManage/InstrumentAttendStatic/InstrumentGroupStaticIndex |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-10 17:55:57.4645||INFO||URL1:/EvaluateManage/InstrumentAttendStatic/StandardResultAnalyseIndex |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-10 17:55:57.8000||ERROR||Value cannot be null. (Parameter 'source')
   at System.Linq.ThrowHelper.ThrowArgumentNullException(ExceptionArgument argument)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Dqy.Syjx.Service.EvaluateManage.InstrumentAttendStaticService.GetCountyStandardResultAnalyseList(InstrumentAttendStaticListParam param, String orderBy) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Service\EvaluateManage\InstrumentAttendStaticService.cs:line 551
   at Dqy.Syjx.Business.EvaluateManage.InstrumentAttendStaticBLL.GetCountyStandardResultAnalyseList(InstrumentAttendStaticListParam param) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Business\EvaluateManage\InstrumentAttendStaticBLL.cs:line 199
   at Dqy.Syjx.Web.Areas.EvaluateManage.Controllers.InstrumentAttendStaticController.GetCountyStandardResultAnalyseListJson(InstrumentAttendStaticListParam param) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\Areas\EvaluateManage\Controllers\InstrumentAttendStaticController.cs:line 124
   at lambda_method1532(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
 |url: http://localhost/EvaluateManage/InstrumentAttendStatic/GetCountyStandardResultAnalyseListJson|action: GetCountyStandardResultAnalyseListJson
2025-06-10 17:56:25.0370||ERROR||Value cannot be null. (Parameter 'source')
   at System.Linq.ThrowHelper.ThrowArgumentNullException(ExceptionArgument argument)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Dqy.Syjx.Service.EvaluateManage.InstrumentAttendStaticService.GetCountyStandardResultAnalyseList(InstrumentAttendStaticListParam param, String orderBy) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Service\EvaluateManage\InstrumentAttendStaticService.cs:line 551
   at Dqy.Syjx.Business.EvaluateManage.InstrumentAttendStaticBLL.GetCountyStandardResultAnalyseList(InstrumentAttendStaticListParam param) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Business\EvaluateManage\InstrumentAttendStaticBLL.cs:line 199
   at Dqy.Syjx.Web.Areas.EvaluateManage.Controllers.InstrumentAttendStaticController.GetCountyStandardResultAnalyseListJson(InstrumentAttendStaticListParam param) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\Areas\EvaluateManage\Controllers\InstrumentAttendStaticController.cs:line 124
   at lambda_method1532(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
 |url: http://localhost/EvaluateManage/InstrumentAttendStatic/GetCountyStandardResultAnalyseListJson|action: GetCountyStandardResultAnalyseListJson
2025-06-10 17:56:56.4088||ERROR||Value cannot be null. (Parameter 'source')
   at System.Linq.ThrowHelper.ThrowArgumentNullException(ExceptionArgument argument)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Dqy.Syjx.Service.EvaluateManage.InstrumentAttendStaticService.GetCountyStandardResultAnalyseList(InstrumentAttendStaticListParam param, String orderBy) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Service\EvaluateManage\InstrumentAttendStaticService.cs:line 551
   at Dqy.Syjx.Business.EvaluateManage.InstrumentAttendStaticBLL.GetCountyStandardResultAnalyseList(InstrumentAttendStaticListParam param) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Business\EvaluateManage\InstrumentAttendStaticBLL.cs:line 199
   at Dqy.Syjx.Web.Areas.EvaluateManage.Controllers.InstrumentAttendStaticController.GetCountyStandardResultAnalyseListJson(InstrumentAttendStaticListParam param) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\Areas\EvaluateManage\Controllers\InstrumentAttendStaticController.cs:line 124
   at lambda_method1532(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
 |url: http://localhost/EvaluateManage/InstrumentAttendStatic/GetCountyStandardResultAnalyseListJson|action: GetCountyStandardResultAnalyseListJson
2025-06-10 17:57:09.9106||DEBUG||程序启动 |ContentRootPath:D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web |WebRootPath:D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\wwwroot |IsDevelopment:True |url: |action: 
2025-06-10 17:58:18.2610||INFO||URL1:/InstrumentManage/PurchaseStatistics/PurchaseGroupStaticIndex |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-10 17:58:28.4753||INFO||URL1:/QueryStatisticsManage/Instrument/CountyInstrumentStatistic |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-10 17:58:35.0903||INFO||URL1:/EvaluateManage/InstrumentAttendStatic/InstrumentGroupStaticIndex |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-10 17:58:37.3954||INFO||URL1:/EvaluateManage/InstrumentAttendStatic/StandardResultAnalyseIndex |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-10 17:58:37.9645||ERROR||Value cannot be null. (Parameter 'source')
   at System.Linq.ThrowHelper.ThrowArgumentNullException(ExceptionArgument argument)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Dqy.Syjx.Service.EvaluateManage.InstrumentAttendStaticService.GetCountyStandardResultAnalyseList(InstrumentAttendStaticListParam param, String orderBy) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Service\EvaluateManage\InstrumentAttendStaticService.cs:line 551
   at Dqy.Syjx.Business.EvaluateManage.InstrumentAttendStaticBLL.GetCountyStandardResultAnalyseList(InstrumentAttendStaticListParam param) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Business\EvaluateManage\InstrumentAttendStaticBLL.cs:line 199
   at Dqy.Syjx.Web.Areas.EvaluateManage.Controllers.InstrumentAttendStaticController.GetCountyStandardResultAnalyseListJson(InstrumentAttendStaticListParam param) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\Areas\EvaluateManage\Controllers\InstrumentAttendStaticController.cs:line 124
   at lambda_method1220(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
 |url: http://localhost/EvaluateManage/InstrumentAttendStatic/GetCountyStandardResultAnalyseListJson|action: GetCountyStandardResultAnalyseListJson
2025-06-10 18:11:35.1839||DEBUG||程序启动 |ContentRootPath:D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web |WebRootPath:D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\wwwroot |IsDevelopment:True |url: |action: 
2025-06-10 18:12:40.6086||INFO||URL1:/InstrumentManage/PurchaseStatistics/PurchaseGroupStaticIndex |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-10 18:12:43.2927||INFO||URL1:/QueryStatisticsManage/Instrument/CountyInstrumentStatistic |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-10 18:12:45.9611||INFO||URL1:/EvaluateManage/InstrumentAttendStatic/InstrumentGroupStaticIndex |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-10 18:12:47.6548||INFO||URL1:/EvaluateManage/InstrumentAttendStatic/StandardResultAnalyseIndex |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-10 18:12:50.9955||INFO||URL1:/EvaluateManage/InstrumentAttendStatic/InstrumentAttendStaticIndex?instrumentEvaluateProjectId657935141812834304&allocateType1&schoolId452046143010902016&countyId357537171000791042&stageId1002002&courseId1005002 |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-10 18:12:54.1513||INFO||URL1:/QueryStatisticsManage/Instrument/CountyInstrumentSum |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-10 18:13:06.9677||INFO||URL1:/QueryStatisticsManage/Instrument/CountyInstrumentStorage |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-10 18:13:58.5969||INFO||URL1:/QueryStatisticsManage/FunRoom/CountyInstitution |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-10 18:13:59.4625||ERROR||Value cannot be null. (Parameter 'source')
   at System.Linq.ThrowHelper.ThrowArgumentNullException(ExceptionArgument argument)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Dqy.Syjx.Service.BusinessManage.FunRoomService.GetPageList(FunRoomListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Service\BusinessManage\FunRoomService.cs:line 122
   at Dqy.Syjx.Business.BusinessManage.FunRoomBLL.GetPageList(FunRoomListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Business\BusinessManage\FunRoomBLL.cs:line 321
   at Dqy.Syjx.Web.Areas.QueryStatisticsManage.Controllers.FunRoomController.GetFunRoomPageListJson(FunRoomListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\Areas\QueryStatisticsManage\Controllers\FunRoomController.cs:line 266
   at lambda_method1336(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
 |url: http://localhost/QueryStatisticsManage/FunRoom/GetFunRoomPageListJson|action: GetFunRoomPageListJson
2025-06-10 18:18:06.2456||ERROR||Value cannot be null. (Parameter 'source')
   at System.Linq.ThrowHelper.ThrowArgumentNullException(ExceptionArgument argument)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Dqy.Syjx.Service.BusinessManage.FunRoomService.GetPageList(FunRoomListParam param, Pagination pagination)
   at Dqy.Syjx.Business.BusinessManage.FunRoomBLL.GetPageList(FunRoomListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Business\BusinessManage\FunRoomBLL.cs:line 321
   at Dqy.Syjx.Web.Areas.QueryStatisticsManage.Controllers.FunRoomController.GetFunRoomPageListJson(FunRoomListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\Areas\QueryStatisticsManage\Controllers\FunRoomController.cs:line 266
   at lambda_method1336(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
 |url: http://localhost/QueryStatisticsManage/FunRoom/GetFunRoomPageListJson|action: GetFunRoomPageListJson
2025-06-10 18:19:26.5080||INFO||URL1:/QueryStatisticsManage/FunRoom/CountyInstitution |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-10 18:19:35.2830||INFO||URL1:/QueryStatisticsManage/FunRoom/CountySafetyCheck |url: http://localhost/ArticleManager/HelpDoc/GetHelpViewJson|action: GetHelpViewJson
2025-06-10 18:19:35.7024||ERROR||Value cannot be null. (Parameter 'source')
   at System.Linq.ThrowHelper.ThrowArgumentNullException(ExceptionArgument argument)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Dqy.Syjx.Service.BusinessManage.CheckRectificationService.GetCheckRectificationStatisticsList(CheckRectificationListParam param, Pagination pagination)
   at Dqy.Syjx.Business.BusinessManage.CheckRectificationBLL.GetCheckRectificationStatisticsList(CheckRectificationListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Business\Dqy.Syjx.Business\BusinessManage\CheckRectificationBLL.cs:line 86
   at Dqy.Syjx.Web.Areas.QueryStatisticsManage.Controllers.FunRoomController.GetCheckRectificationStatisticsList(CheckRectificationListParam param, Pagination pagination) in D:\工作管理\项目研发\实验教学管理平台\04系统开发\Dqy.Syjx\Dqy.Syjx.Web\Dqy.Syjx.Web\Areas\QueryStatisticsManage\Controllers\FunRoomController.cs:line 192
   at lambda_method1677(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfActionResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
 |url: http://localhost/QueryStatisticsManage/FunRoom/GetCheckRectificationStatisticsList|action: GetCheckRectificationStatisticsList
