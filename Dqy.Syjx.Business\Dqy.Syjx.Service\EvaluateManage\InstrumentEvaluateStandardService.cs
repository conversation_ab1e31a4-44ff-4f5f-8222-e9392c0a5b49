﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.EvaluateManage;
using Dqy.Syjx.Model.Param.EvaluateManage;

namespace Dqy.Syjx.Service.EvaluateManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-11-26 15:09
    /// 描 述：标准库设置服务类
    /// </summary>
    public class InstrumentEvaluateStandardService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<InstrumentEvaluateStandardEntity>> GetList(InstrumentEvaluateStandardListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<InstrumentEvaluateStandardEntity>> GetPageList(InstrumentEvaluateStandardListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ListFilter(param, strSql);
            var list = await this.BaseRepository().FindList<InstrumentEvaluateStandardEntity>(strSql.ToString(), filter.ToArray(), pagination);
            return list.ToList();
        }

        public async Task<InstrumentEvaluateStandardEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<InstrumentEvaluateStandardEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(InstrumentEvaluateStandardEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(InstrumentEvaluateStandardEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update eq_InstrumentEvaluateStandard set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
                strSql = $"update eq_InstrumentEvaluateStandard set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<InstrumentEvaluateStandardEntity, bool>> ListFilter(InstrumentEvaluateStandardListParam param)
        {
            var expression = LinqExtensions.True<InstrumentEvaluateStandardEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (param.SchoolStage > 0)
                {
                    expression = expression.And(t => t.SchoolStage == param.SchoolStage);
                }
                if (param.DictionaryId1005 > 0)
                {
                    expression = expression.And(t => t.DictionaryId1005 == param.DictionaryId1005);
                }
            }
            return expression;
        }
        private List<DbParameter> ListFilter(InstrumentEvaluateStandardListParam param, StringBuilder strSql)
        {
            strSql.Append(@"  SELECT * From (
                 SELECT
                    a1.Id ,
                    a1.BaseIsDelete ,
                    a1.BaseCreateTime ,
                    a1.BaseModifyTime ,
                    a1.BaseCreatorId ,
                    a1.BaseModifierId ,
                    a1.BaseVersion ,
                    a1.VersionName ,
                    a1.SchoolStage ,
                    a1.DictionaryId1005 , 
                    a1.Statuz ,
                    a1.Remark ,
                    dic2.DicName AS SchoolStageName ,
                    dic3.DicName AS SubjectName ,
                    dic2.DicName + '-' + dic3.DicName + '-' + a1.VersionName as SelectVersionName
                    FROM  eq_InstrumentEvaluateStandard AS a1
                    INNER JOIN  sys_static_dictionary AS dic2 ON a1.SchoolStage = dic2.DictionaryId AND dic2.BaseIsDelete = 0
                    INNER JOIN  sys_static_dictionary AS dic3 ON a1.DictionaryId1005  = dic3.DictionaryId AND dic3.BaseIsDelete = 0
            ) as tb1 WHERE   1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = @BaseIsDelete ");
                parameter.Add(DbParameterExtension.CreateDbParameter("@BaseIsDelete", param.BaseIsDelete));
                if (param.SchoolStage > 0)
                {
                    strSql.Append(" AND SchoolStage = @SchoolStage ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStage", param.SchoolStage));
                }
                if (param.DictionaryId1005 > 0)
                {
                    strSql.Append(" AND DictionaryId1005 = @DictionaryId1005 ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId1005", param.DictionaryId1005));
                }
                if (!string.IsNullOrEmpty(param.VersionName))
                {
                    strSql.Append(" AND VersionName like @VersionName ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@VersionName", $"%{param.VersionName}%"));
                }
            }
            return parameter;
        }
        #endregion
    }
}
