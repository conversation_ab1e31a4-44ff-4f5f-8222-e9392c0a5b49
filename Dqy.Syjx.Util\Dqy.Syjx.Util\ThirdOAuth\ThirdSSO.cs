﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Diagnostics.Contracts;
using System.Linq;
using System.Security.Permissions;
using System.Threading.Tasks;
using static Dqy.Syjx.Util.ThirdOAuth.CzsSoftAuthCore;

namespace Dqy.Syjx.Util.ThirdOAuth
{
    public class ThirdSSO
    {
        public string authHost { get; set; }
        public string dataHost { get; set; }
        public string dataHost2 { get; set; }
        public string clientID { get; set; }
        public string clientSecret { get; set; }
        public string callBackUrl { get; set; }
        public string root { get; set; }
        public string key { get; set; }
        public string privatekey { get; set; }
        public string userScope { get; set; }
        public string dataScope { get; set; }
        public string logout { get; set; }
        public string remark { get; set; }
        public string accountid { get; set; }
        public string accountpwd { get; set; }
        public string countyusercode { get; set; }
    }

    public class YanchengApiResponse<T>
    {
        /// <summary>
        /// 响应状态码
        /// </summary>
        public int Code { get; set; }

        /// <summary>
        /// 响应消息
        /// </summary>
        public string Msg { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 响应数据
        /// </summary>
        public T Data { get; set; }
    }

    public class YanChengUserData
    {
        /// <summary>
        /// 用户姓名
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 手机号码
        /// </summary>
        public string Mobile { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public long Uid { get; set; }

        /// <summary>
        /// 账号
        /// </summary>
        public string Account { get; set; }

        /// <summary>
        /// 代码(可能是用户编号)
        /// </summary>
        public string Code { get; set; }
    }

    public class DongTaiAuthResponse
    {
        [JsonProperty("access_token")]
        public string AccessToken { get; set; }

        [JsonProperty("token_type")]
        public string TokenType { get; set; }

        [JsonProperty("refresh_token")]
        public string RefreshToken { get; set; }

        [JsonProperty("expires_in")]
        public int ExpiresIn { get; set; }

        [JsonProperty("scope")]
        public string Scope { get; set; }

        [JsonProperty("login_type")]
        public string LoginType { get; set; }

        [JsonProperty("user_type")]
        public string UserType { get; set; }

        [JsonProperty("domain_id")]
        public string DomainId { get; set; }

        [JsonProperty("only_id")]
        public string OnlyId { get; set; }

        [JsonProperty("real_name")]
        public string RealName { get; set; }

        [JsonProperty("account")]
        public string Account { get; set; }

        [JsonProperty("org_id")]
        public string OrgId { get; set; }

        [JsonProperty("jti")]
        public string Jti { get; set; }
    }

}
namespace Dqy.Syjx.Util.ThirdOAuth.Self
{
    public class TokenModel
    {
        public string Token { get; set; }
    }

    public class VisitTokenResult
    {
        public int status { get; set; }
        public bool success { get; set; }
        public string msg { get; set; }
        public VisitTokenModel response { get; set; }
    }

    public class VisitTokenModel
    {
        public string Token { get; set; }

        public int AId { get; set; }
    }

    /// <summary>
    /// 用户信息
    /// </summary>
    public class User
    {
        /// <summary>
        /// 用户Id
        /// </summary> 
        public string UserId { get; set; }
        /// <summary>
        /// 登录账号
        /// </summary>
        public string AccountName { get; set; }
        /// <summary>
        /// 姓名
        /// </summary>
        public string RealName { get; set; }
        /// <summary>
        /// 手机号码
        /// </summary>
        public string Mobile { get; set; }

        /// <summary>
        /// 账号性质（1：管理员；2：一般人员）
        /// </summary>
        public int AccountType { get; set; }

    }
    /// <summary>
    /// 单位信息
    /// </summary>
    public class Unit
    {
        /// <summary>
        /// 单位Id
        /// </summary>    
        public string UnitId { get; set; }

        /// <summary>
        /// 性质类型：1市级管理机构、2区县级管理机构、3学校、4企业
        /// </summary>     
        public int UnitType { get; set; }
        /// <summary>
        /// 名称
        /// </summary>          
        public string UnitName { get; set; }

        /// <summary>
        /// 区县级管理机构Id，当UnitType = 3时有值
        /// </summary>
        public string CountyId { get; set; }

        /// <summary>
        /// 区县级管理机构名称，当UnitType = 3时有值
        /// </summary>
        public string CountyName { get; set; }

        /// <summary>
        /// 市级管理机构Id，当UnitType = 3,2时有值
        /// </summary>
        public string CityId { get; set; }
        /// <summary>
        /// 市级管理机构名称， UnitType = 3,2时有值
        /// </summary>
        public string CityName { get; set; }

    }
    /// <summary>
    /// 用户个人信息
    /// </summary>
    public class SSOUserInfo
    {
        /// <summary>
        /// 用户信息
        /// </summary>
        public User User { get; set; }
        /// <summary>
        /// 单位信息
        /// </summary>
        public Unit Unit { get; set; }
    }

    public class AccessUserResult
    {
        public int status { get; set; }
        public bool success { get; set; }
        public string msg { get; set; }
        public AccessUserModel response { get; set; }
    }

    /// <summary>
    /// 授权访问账号信息
    /// </summary>
    public class AccessUserModel
    {
        public int Id { get; set; }

        public string Name { get; set; }

        public string RealName { get; set; }

        public long UnitId { get; set; }

        public string UnitName { get; set; }

        public string Code { get; set; }

        public int RoleType { get; set; }
    }
}