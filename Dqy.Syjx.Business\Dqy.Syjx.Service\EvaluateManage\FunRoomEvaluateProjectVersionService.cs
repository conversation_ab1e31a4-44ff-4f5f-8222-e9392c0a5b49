﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.EvaluateManage;
using Dqy.Syjx.Model.Param.EvaluateManage;
using Dqy.Syjx.Entity.BusinessManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Model.Param.BusinessManage;

namespace Dqy.Syjx.Service.EvaluateManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-11-25 15:14
    /// 描 述：达标参数设置服务类
    /// </summary>
    public class FunRoomEvaluateProjectVersionService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<FunRoomEvaluateProjectVersionEntity>> GetList(FunRoomEvaluateProjectVersionListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<FunRoomEvaluateProjectVersionEntity>> GetPageList(FunRoomEvaluateProjectVersionListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ListFilter(param, strSql);
            var list = await this.BaseRepository().FindList<FunRoomEvaluateProjectVersionEntity>(strSql.ToString(), filter.ToArray(), pagination);
            return list.ToList();
        }
       
        public async Task<FunRoomEvaluateProjectVersionEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<FunRoomEvaluateProjectVersionEntity>(id);
        }
        
        public async Task<List<FunRoomEvaluateProjectEntity>> GetProjectComboList(FunRoomEvaluateProjectVersionListParam param)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ListFilterCombo(param, strSql);
            var list = await this.BaseRepository().FindList<FunRoomEvaluateProjectEntity>(strSql.ToString(), filter.ToArray());
            return list.ToList();
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(FunRoomEvaluateProjectVersionEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(FunRoomEvaluateProjectVersionEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update bn_FunRoomEvaluateProjectVersion set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
                strSql = $"update bn_FunRoomEvaluateProjectVersion set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<FunRoomEvaluateProjectVersionEntity, bool>> ListFilter(FunRoomEvaluateProjectVersionListParam param)
        {
            var expression = LinqExtensions.True<FunRoomEvaluateProjectVersionEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (param.SchoolStageList != null && param.SchoolStageList.Count > 0)
                {
                    expression = expression.And(t => param.SchoolStageList.Contains(t.SchoolStage));
                }
                if (param.DictionaryId1005 > 0)
                {
                    expression = expression.And(t => t.DictionaryId1005 == param.DictionaryId1005);
                }
            }
            return expression;
        }

        private List<DbParameter> ListFilter(FunRoomEvaluateProjectVersionListParam param, StringBuilder strSql)
        {
            strSql.Append(@"  SELECT * From (
                SELECT
                a1.Id ,
                a1.BaseIsDelete ,
                a1.BaseCreateTime ,
                a1.BaseModifyTime ,
                a1.BaseCreatorId ,
                a1.BaseModifierId ,
                a1.BaseVersion ,
                a1.EvaluateProjectId ,
                a1.DictionaryId1006A ,
                a1.DictionaryId1006B ,
                a1.SchoolStage ,
                a1.DictionaryId1005 ,
                a1.TargetName ,
                a1.EvaluateStandardId ,
                a1.Remark ,
                a1.Statuz ,
                project2.EvaluateName ,
                dic2.DicName AS SchoolStageName ,
                dic3.DicName AS SubjectName ,
                dic4.DicName AS ClassOneName,
                dic5.DicName AS ClassTwoName ,
                es6.VersionName ,
                project2.IsDefaultEv
                FROM  bn_FunRoomEvaluateProjectVersion AS a1
                INNER JOIN  bn_FunRoomEvaluateProject AS project2 ON a1.EvaluateProjectId = project2.Id
                INNER JOIN  sys_static_dictionary AS dic2 ON a1.SchoolStage = dic2.DictionaryId AND dic2.BaseIsDelete = 0
                INNER JOIN  sys_static_dictionary AS dic3 ON a1.DictionaryId1005  = dic3.DictionaryId AND dic3.BaseIsDelete = 0
                INNER JOIN  sys_static_dictionary AS dic4 ON a1.DictionaryId1006A = dic4.DictionaryId AND dic4.BaseIsDelete = 0
                INNER JOIN  sys_static_dictionary AS dic5 ON a1.DictionaryId1006B = dic5.DictionaryId AND dic5.BaseIsDelete = 0
                INNER JOIN  bn_FunRoomEvaluateStandard AS es6 ON a1.EvaluateStandardId = es6.Id
            ) as tb1 WHERE   1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = @BaseIsDelete ");
                parameter.Add(DbParameterExtension.CreateDbParameter("@BaseIsDelete", param.BaseIsDelete));
                if (param.SchoolStage > 0)
                {
                    strSql.Append(" AND SchoolStage = @SchoolStage ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStage", param.SchoolStage));
                }
                if (param.DictionaryId1005 > 0)
                {
                    strSql.Append(" AND DictionaryId1005 = @DictionaryId1005 ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId1005", param.DictionaryId1005));
                }
                if (param.DictionaryId1006A > 0)
                {
                    strSql.Append(" AND DictionaryId1006A = @DictionaryId1006A ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId1006A", param.DictionaryId1006A));

                    if (param.DictionaryId1006B > 0)
                    {
                        strSql.Append(" AND DictionaryId1006B = @DictionaryId1006B ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId1006B", param.DictionaryId1006B));
                    }
                }
                if (param.EvaluateStandardId > 0)
                {
                    strSql.Append(" AND EvaluateStandardId = @EvaluateStandardId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@EvaluateStandardId", param.EvaluateStandardId));
                }
                if (!string.IsNullOrEmpty(param.VersionName))
                {
                    strSql.Append(" AND VersionName like @VersionName ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@VersionName", $"%{param.VersionName}%"));
                }
                if (!string.IsNullOrEmpty(param.TargetName))
                {
                    strSql.Append(" AND TargetName like @TargetName ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@TargetName", $"%{param.TargetName}%"));
                }
            }
            return parameter;
        }

        /// <summary>
        /// 获取评估项目名称下拉框数据
        /// </summary>
        /// <param name="param"></param>
        /// <param name="strSql"></param>
        /// <returns></returns>
        private List<DbParameter> ListFilterCombo(FunRoomEvaluateProjectVersionListParam param, StringBuilder strSql)
        {
            strSql.Append(@"  
                SELECT DISTINCT EP.Id ,EP.EvaluateName ,ISNULL(EP.IsDefaultEv ,0) AS IsDefaultEv
                FROM  bn_FunRoomEvaluateProject AS EP
                INNER JOIN  bn_FunRoomEvaluateProjectVersion AS EPV ON EP.Id = EPV.EvaluateProjectId AND EPV.BaseIsDelete = 0
                INNER JOIN  bn_FunRoomEvaluateStandard AS ES ON EPV.EvaluateStandardId = ES.Id AND ES.BaseIsDelete = 0
                WHERE EP.BaseIsDelete = 0
             ");
            var parameter = new List<DbParameter>();
            if (param.SchoolStage > 0)
            {
                strSql.Append(" AND EPV.SchoolStage = @SchoolStage ");
                parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStage", param.SchoolStage));
            }
            if (param.SchoolStageList != null && param.SchoolStageList.Count > 0)
            {
                for (int i = 0; i < param.SchoolStageList.Count; i++)
                {
                    if (i == 0)
                    {
                        strSql.Append($" AND ( EPV.SchoolStage = {param.SchoolStageList[i]}");
                    }
                    else
                    {
                        strSql.Append($" OR EPV.SchoolStage = {param.SchoolStageList[i]} ");
                    }
                }
                strSql.Append(" )");
            }
            strSql.Append("  ORDER BY IsDefaultEv DESC ");
            return parameter;
        }


        /// <summary>
        /// 实验（专用）室二级分类统计数量
        /// </summary>
        /// <param name="param"></param>
        /// <param name="strSql"></param>
        /// <param name="funcSql"></param>
        /// <returns></returns>
        public async Task<List<FunRoomEvaluateProjectVersionEntity>> GetVersionEnormAll(FunRoomEvaluateProjectVersionListParam param)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(@"  SELECT * From (
                SELECT
                EPV.Id ,
                EPV.BaseIsDelete ,
                EPV.BaseCreateTime ,
                EPV.BaseModifyTime ,
                EPV.BaseCreatorId ,
                EPV.BaseModifierId ,
                EPV.BaseVersion ,
                EPV.EvaluateProjectId ,
                EPV.DictionaryId1006A ,
                EPV.DictionaryId1006B ,
                EPV.SchoolStage ,
                EPV.DictionaryId1005 ,
                EPV.TargetName ,
                EPV.EvaluateStandardId ,
                EPV.Statuz , 
                EE.RailStart ,  
                EE.RailEnd ,
                EE.RoomNum ,
                EE.Area  ,
                EE.StandardLevel ,
                EE.Remark 
                FROM bn_FunRoomEvaluateProjectVersion AS EPV 
                INNER JOIN bn_FunRoomEvaluateStandard AS ES ON es.BaseIsDelete = 0 AND EPV.EvaluateStandardId = ES.Id AND ES.BaseIsDelete = 0
                INNER JOIN bn_FunRoomEvaluateEnorm AS EE ON EE.BaseIsDelete = 0 AND ES.Id = EE.EvaluateStandardId AND EE.BaseIsDelete = 0  AND EE.IsEvaluate = 1 AND EE.Statuz = 1 
            ) as tb1 WHERE   1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = 0 ");
                if (param.SchoolStage > 0)
                {
                    strSql.Append(" AND SchoolStage = @SchoolStage ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStage", param.SchoolStage));
                }
                if (param.DictionaryId1005 > 0)
                {
                    strSql.Append(" AND DictionaryId1005 = @DictionaryId1005 ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId1005", param.DictionaryId1005));
                }
                if (param.DictionaryId1006A > 0)
                {
                    strSql.Append(" AND DictionaryId1006A = @DictionaryId1006A ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId1006A", param.DictionaryId1006A)); 
                }
                if (param.DictionaryId1006B > 0)
                {
                    strSql.Append(" AND DictionaryId1006B = @DictionaryId1006B ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@DictionaryId1006B", param.DictionaryId1006B));
                }
                if (param.Ids != null && param.Ids.Length > 0)
                {
                    var idArr = param.Ids.Split(",");
                    if (idArr != null)
                    {
                        List< string> whereList = new List<string> ();
                        for (int i = 0; i < idArr.Length; i++)
                        {
                            long id = 0;
                            long.TryParse(idArr[i],out id);
                            if(id > 0)
                            {
                                whereList.Add(string.Format(" Id = {0} ", id));
                            } 
                        }
                        if (whereList!=null && whereList.Count > 0)
                        {
                            strSql.Append($" AND ({string.Join(" OR ", whereList)}) ");
                        }
                    }
                    
                }
            }
            var list = await this.BaseRepository().FindList<FunRoomEvaluateProjectVersionEntity>(strSql.ToString(), parameter.ToArray());
            return list.ToList();
        }
        #endregion
    }
}
