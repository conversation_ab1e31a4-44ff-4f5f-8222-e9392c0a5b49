﻿@{
    ViewBag.Title = "实验开出超期";
    Layout = "~/Views/Shared/_Index.cshtml";
}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .select-list li input {
        width: auto !important;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <input id="Name" col="Name" placeholder="请输入单位关键字" style="width:150px;" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-default btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    $(function () {
        initGrid();
    });


    function initGrid() {
        var columnsArr=[
                    { field: 'Name', title: '<div style="width:150px;">单位名称</div>', sortable: true, halign: 'center', align: 'left', valign: 'middle', width: 90 },
                    { field: 'CourseName', title: '学科', sortable: true, halign: 'center', align: 'center', valign: 'middle',width: 60 },
                    { field: 'GradeName', title: '年级', sortable: true, halign: 'center', align: 'center', valign: 'middle',width: 60 },
                    { field: 'ExperimentNum', title: '超期数', halign: 'center', align: 'center', width: 40 }
            ];
        if("@ViewBag.UnitTypeId"=="3"){
            $("#Name").attr("placeholder","请输入学科关键字");
            columnsArr=[
                    { field: 'CourseName', title: '学科', sortable: true, halign: 'center', align: 'center', valign: 'middle',width: 60 },
                    { field: 'GradeName', title: '年级', sortable: true, halign: 'center', align: 'center', valign: 'middle',width: 60 },
                    { field: 'ClassName', title: '班级', sortable: true, halign: 'center', align: 'center', valign: 'middle',width: 60 },
                    { field: 'ExperimentNum', title: '超期数', halign: 'center', align: 'center', width: 40 }
            ];
        }
        $('#gridTable').ysTable({
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            pageSize:15,
            pageIndex:1,
            pageNumber:1,
            sortable:false,
            url: '@Url.Content("~/Home/GetOverDuePageJson")',
            sortName: '',
            columns: [columnsArr],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
    }
    function resetGrid() {
        //清空条件
        $('#Name').val('');
        $('#gridTable').ysTable('search');
    }

</script>
