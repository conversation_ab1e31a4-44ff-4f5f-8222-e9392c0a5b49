﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.OrganizationManage;
using Dqy.Syjx.Model.Param.OrganizationManage;
using Dqy.Syjx.Model.Input;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Enum.OrganizationManage;
using Dqy.Syjx.Model.Result;
using Dqy.Syjx.Model.Result.ExperimentTeachManage;
using Dqy.Syjx.Model.Result.OrganizationManage;

namespace Dqy.Syjx.Service.OrganizationManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-09-10 09:30
    /// 描 述：单位表服务类
    /// </summary>
    public class UnitService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<UnitEntity>> GetList(UnitListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<UnitEntity>> GetPageList(UnitListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<List<UnitEntity>> GetChildrenPageList(UnitListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ListFilter(param, strSql);
            var list = await this.BaseRepository().FindList<UnitEntity>(strSql.ToString(), filter.ToArray(), pagination);
            return list.ToList();
        }

        public async Task<List<UnitEntity>> GetUnitList(UnitListParam param)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ListFilter(param, strSql);
            Pagination pagination = new Pagination();
            pagination.PageIndex = 1;
            pagination.PageSize = int.MaxValue;
            var list = await this.BaseRepository().FindList<UnitEntity>(strSql.ToString(), filter.ToArray(), pagination);
            return list.ToList();
        }

        public async Task<UnitEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<UnitEntity>(id);
        }

        /// <summary>
        /// 获取单位名称
        /// </summary>
        /// <param name="unitName"></param>
        /// <returns></returns>
        public async Task<UnitEntity> GetEntity(string unitName)
        {
            return await this.BaseRepository().FindEntity<UnitEntity>(a=>a.Name == unitName && a.Statuz == StatusEnum.Yes.ParseToInt());
        }

        /// <summary>
        /// 获取市级下属区县下拉框数据
        /// Name取区县所属区域名称
        /// </summary>
        /// <param name="cityId">市级单位Id</param>
        /// <returns></returns>
        public async Task<List<ComboBoxInfo>> GetCountyBoxByCityId(long cityId)
        {
            StringBuilder sql = new StringBuilder();
            sql.AppendFormat(@"
                    SELECT U.Id ,R.AreaName AS Name
                    FROM  up_UnitRelation AS UR 
                    INNER JOIN  up_Unit AS U ON UR.ExtensionObjId = U.Id AND UnitType = {0} AND U.BaseIsDelete = 0
                    LEFT JOIN  SysArea AS R ON U.AreaId = R.AreaCode
                    WHERE UR.BaseIsDelete = 0 AND UR.ExtensionType = 3 AND UR.UnitId = {1}
            ", UnitTypeEnum.County.ParseToInt(), cityId);
            return (await this.BaseRepository().FindList<ComboBoxInfo>(sql.ToString())).ToList();
        }

        /// <summary>
        /// 获取市级下属区县和学校信息下拉框数据
        /// </summary>
        /// <param name="cityId">市级单位Id</param>
        /// <returns></returns>
        public async Task<List<SchoolUnitModel>> GetSchoolListByCityId(long cityId,List<long> schoolids)
        {
            StringBuilder sql = new StringBuilder();
            sql.AppendFormat($@" SELECT * From ( SELECT ur3.ExtensionObjId AS SchoolId ,u1.Name AS SchoolName 
                ,ur3.UnitId AS CountyId ,  sa5.AreaName AS CountyName
                FROM up_UnitRelation AS ur3 
                INNER JOIN up_Unit AS u1 ON u1.BaseIsDelete = 0 AND u1.UnitType = {UnitTypeEnum.School.ParseToInt()} AND ur3.ExtensionObjId = u1.Id 
                INNER JOIN up_Unit AS u2 ON u2.BaseIsDelete = 0 AND u2.UnitType = {UnitTypeEnum.County.ParseToInt()} AND ur3.UnitId = u2.Id
                LEFT JOIN SysArea AS sa5 ON sa5.BaseIsDelete = 0 AND u2.AreaId = sa5.AreaCode
                INNER JOIN up_UnitRelation AS ur4 ON ur3.UnitId = ur4.ExtensionObjId AND ur4.ExtensionType = 3 AND  ur4.BaseIsDelete = 0
                WHERE ur3.ExtensionType = 3 AND  ur3.BaseIsDelete = 0 AND ur4.UnitId =  {cityId} 
            ) AS T Where 1= 1 " );
            if (schoolids != null && schoolids.Count > 0)
            {
                sql.Append($" AND ({string.Join(" OR ",schoolids.Select(m=>string.Format(" SchoolId = {0} ", m)))}) ");
            }
            var list = await this.BaseRepository().FindList<SchoolUnitModel>(sql.ToString());
            return list.ToList();
        }


        /// <summary>
        /// 获取平台所有区县
        /// </summary>
        /// <param name="cityId">市级单位Id</param>
        /// <returns></returns>
        public async Task<List<ComboBoxInfo>> GetAllCountyList()
        {
            StringBuilder sql = new StringBuilder();
            sql.AppendFormat(@"
                    SELECT U.Id ,U.Name
                    FROM  up_Unit AS U
                    
                    WHERE U.BaseIsDelete = 0 AND U.UnitType = {0} AND U.Statuz = {1} ORDER BY U.Sort ASC
            ", UnitTypeEnum.County.ParseToInt(), StatusEnum.Yes.ParseToInt());
            return (await this.BaseRepository().FindList<ComboBoxInfo>(sql.ToString())).ToList();
        }

        /// <summary>
        /// 根据第三方id，获取单位信息
        /// </summary>
        /// <param name="thirdUnitId"></param>
        /// <returns></returns>
        public async Task<UnitEntity> GetThirdUnit(string thirdUnitId)
        {
            return (await GetList(new UnitListParam { ThirdUnitId = thirdUnitId})).FirstOrDefault();
            //return await this.BaseRepository().FindEntity<UnitEntity>(a => a.ThirdUnitId == thirdUnitId);
        }

        /// <summary>
        /// 根据第三方id，获取单位信息，本方法仅供内部使用，且调用前必须进行数据格式验证
        /// </summary>
        /// <param name="thirdUnitId"></param>
        /// <returns></returns>
        public async Task<List<UnitEntity>> GetThirdUnitList(string where)
        {
            var sql = $@"
                    SELECT  * FROM  up_Unit where {where}
            ";
            return (await this.BaseRepository().FindList<UnitEntity>(sql)).ToList();
        }


        #endregion

        #region 提交数据
        public async Task SaveForm(UnitEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }
        public async Task SaveTransForm(UnitEntity entity, Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }
        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update up_Unit set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
                strSql = $"update up_Unit set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        public async Task DeleteTransForm(string ids, Repository db)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update up_Unit set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
                strSql = $"update up_Unit set BaseIsDelete = 1 where id = {ids}";
            }
            await db.ExecuteBySql(strSql);
        }
        public async Task SetStatuzForm(string ids, int statuz, long unitid, int unittype)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            long[] idArr = TextHelper.SplitToArray<long>(ids, ',');
            string whereArr = "";
            for (int i = 0; i < idArr.Length; i++)
            {
                if (i > 0)
                {
                    whereArr += " OR ";
                }
                whereArr += $" a1.Id = {idArr[i]} ";
            }
            string strSql = "";
            if (unittype == UnitTypeEnum.System.ParseToInt())
            {
                strSql = $@" UPDATE  up_Unit SET Statuz = {statuz} FROM  up_Unit AS a1   WHERE ( {whereArr})";
            }
            else
            {
                strSql = $@" UPDATE  up_Unit SET Statuz = {statuz}
                FROM  up_Unit AS a1 
                INNER JOIN  up_UnitRelation AS b2 ON b2.ExtensionType = {UnitRelationTypeEnum.Unit.ParseToInt()} AND a1.Id = b2.ExtensionObjId
                WHERE b2.UnitId = {unitid} AND ( {whereArr})";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }

        public async Task SetSortForm(string ids, int sort, long unitid,int unittype)
        {
            long[] idArr = TextHelper.SplitToArray<long>(ids, ',');
            if (idArr != null && idArr.Count() > 0)
            {
                string whereArr = string.Join(" OR ", idArr.Select(m => string.Format("  a1.Id = {0} ", m)));

                string strSql = "";
                if (unittype == UnitTypeEnum.System.ParseToInt())
                {
                    strSql = $@" UPDATE  up_Unit SET Sort = {sort} FROM  up_Unit AS a1   WHERE  ({whereArr}) ";
                }
                else
                {
                    strSql = $@" UPDATE  up_Unit SET Sort = {sort}
                FROM  up_Unit AS a1 
                INNER JOIN  up_UnitRelation AS b2 ON b2.ExtensionType = {UnitRelationTypeEnum.Unit.ParseToInt()} AND a1.Id = b2.ExtensionObjId
                WHERE b2.UnitId = {unitid} AND ({whereArr}) ";
                }
                await this.BaseRepository().ExecuteBySql(strSql.ToString());
            }
        }

        public async Task<int> GetUserCountForm(string ids, long unitid)
        {
            int num = 0;
            long[] idArr = TextHelper.SplitToArray<long>(ids, ',');
            if (idArr != null && idArr.Count() > 0)
            {
                string whereArr = string.Join(" OR ", idArr.Select(m => string.Format("  a1.Id = {0} ", m)));

                string strSql = $@" SELECT COUNT(d4.Id)  FROM  up_Unit AS a1 
                INNER JOIN  up_UnitRelation AS b2 ON b2.ExtensionType = {UnitRelationTypeEnum.Unit.ParseToInt()} AND a1.Id = b2.ExtensionObjId
				INNER JOIN  up_Unit AS c3 ON b2.UnitId = c3.Id 
				INNER JOIN  up_UnitRelation AS d4 ON d4.ExtensionType = {UnitRelationTypeEnum.User.ParseToInt()} AND a1.Id = d4.UnitId
				INNER JOIN  SysUser AS e5 ON d4.ExtensionObjId = e5.Id AND e5.BaseIsDelete = 0
                WHERE b2.UnitId = {unitid} AND ( {whereArr} ) ";
                var userNum = await this.BaseRepository().FindObject(strSql); 
                if (userNum != null)
                {
                    int.TryParse(userNum.ToString(), out num);
                }
            }
            return num;
        }

        public async Task<int> GetUserCountForm(string ids)
        {
            int num = 0;
            long[] idArr = TextHelper.SplitToArray<long>(ids, ',');
            if (idArr != null && idArr.Count() > 0)
            {
                string whereArr = string.Join(" OR ", idArr.Select(m => string.Format("  a1.Id = {0} ", m)));
                string strSql = $@" SELECT COUNT(e5.Id)  FROM  up_Unit AS a1
				INNER JOIN  up_UnitRelation AS d4 ON d4.ExtensionType = {UnitRelationTypeEnum.User.ParseToInt()} AND a1.Id = d4.UnitId
				INNER JOIN  SysUser AS e5 ON d4.ExtensionObjId = e5.Id AND e5.UserStatus > -1
                WHERE ( {whereArr} ) ";
                var userNum = await this.BaseRepository().FindObject(strSql);
          
                if (userNum != null)
                {
                    int.TryParse(userNum.ToString(), out num);
                }
            }
            return num;
        }
        public async Task<int> GetChildrenUnitCountForm(string ids)
        {
            int num = 0;
            long[] idArr = TextHelper.SplitToArray<long>(ids, ',');
            if (idArr != null && idArr.Count() > 0)
            {
                string whereArr = string.Join(" OR ", idArr.Select(m => string.Format("  a1.Id = {0} ", m)));
                string strSql = $@" SELECT COUNT(c3.Id)  FROM  up_Unit AS a1
				 INNER JOIN  up_UnitRelation AS b2 ON b2.ExtensionType = {UnitRelationTypeEnum.Unit.ParseToInt()} AND a1.Id = b2.UnitId
				 INNER JOIN  up_Unit AS c3 ON b2.ExtensionObjId = c3.Id  AND c3.BaseIsDelete = 0
                WHERE ( {whereArr} ) ";
                var unitNum = await this.BaseRepository().FindObject(strSql);

                if (unitNum != null)
                {
                    int.TryParse(unitNum.ToString(), out num);
                }
            }
            return num;
        }

        /// <summary>
        /// 获取当前单位父级ID
        /// </summary>
        /// <param name="currentUnitid"></param>
        /// <returns></returns>
        public async Task<long> GetParentUnitId(long currentUnitid)
        {
            string strSql = $@"SELECT u2.Id FROM  up_Unit u
                               INNER JOIN  up_UnitRelation AS ur ON u.Id = ur.ExtensionObjId AND ur.ExtensionType = 3 AND ur.BaseIsDelete = 0
                               INNER JOIN  up_Unit AS u2 ON ur.UnitId = u2.Id AND u2.BaseIsDelete = 0 
                               WHERE u.Id = {currentUnitid}";
            var resultId = await this.BaseRepository().FindObject(strSql);
            long parentId = 0;
            if (resultId != null)
            {
                long.TryParse(resultId.ToString(), out parentId);
            }
            return parentId;
        }

        #endregion

        #region 私有方法
        private Expression<Func<UnitEntity, bool>> ListFilter(UnitListParam param)
        {
            var expression = LinqExtensions.True<UnitEntity>();
            if (param != null)
            { 
                expression = expression.And(t => t.BaseIsDelete == 0);
                if (param.OptType == 1)
                {
                    if (param.Id > 0)
                    {
                        expression = expression.And(t => t.Id != param.Id);
                    }
                    if (string.IsNullOrEmpty(param.OrganizationCode))
                    {
                        expression = expression.And(t => t.Name == param.Name || t.Code == param.Code);
                    }
                    else
                    {
                        expression = expression.And(t => t.Name == param.Name || t.Code == param.Code || t.OrganizationCode == param.OrganizationCode);
                    }
                }
                else
                {
                    if (!string.IsNullOrEmpty(param.ids))
                    {
                        long[] uintIdList = TextHelper.SplitToArray<long>(param.ids, ',');
                        expression = expression.And(t => uintIdList.Contains(t.Id.Value));
                    }
                    if (param.UnitType > -1)
                    {
                        expression = expression.And(t => t.UnitType == param.UnitType);
                    }
                    if (!string.IsNullOrEmpty(param.Code))
                    {
                        expression = expression.And(t => t.Code.Contains(param.Code));
                    }
                    if (!string.IsNullOrEmpty(param.Name))
                    {
                        expression = expression.And(t => t.Name.Contains(param.Name) || t.Code.Contains(param.Name));
                    }
                    if (param.Statuz > 0)
                    {
                        expression = expression.And(t => t.Statuz == param.Statuz);
                    }

                    if (param.ThirdUnitId.Length > 0)
                    {
                        expression = expression.And(t => t.ThirdUnitId == param.ThirdUnitId);
                    }
                }
            }
            return expression;
        }

        private List<DbParameter> ListFilter(UnitListParam param, StringBuilder strSql)
        {
            string sqlColumn = "";
            string sqlTable = "";
            if (param.SchoolStageId > 0)
            {
                sqlColumn = " ,dr6.DictionaryToId AS SchoolStageId ";
                sqlTable = " LEFT JOIN  sys_dictionary_relation AS dr6 ON d4.SchoolProp = dr6.DictionaryId AND dr6.RelationType = 1 ";
            }
            strSql.Append($@" SELECT * From (
                               SELECT c3.Id ,
                               c3.BaseIsDelete ,
                               c3.BaseCreateTime ,
                               c3.BaseModifyTime ,
                               c3.BaseCreatorId ,
                               c3.BaseModifierId ,
                               c3.BaseVersion ,
                               c3.UnitType ,
                               c3.IndustryId ,
                               c3.Code ,
                               c3.OrganizationCode ,
                               c3.Name ,
                               c3.Brief ,
                               c3.PinYin ,
                               c3.PinYinBrief ,
                               c3.AreaId ,
                               c3.Address ,
                               c3.Mobile ,
                               c3.Position ,
                               c3.Memo ,
                               c3.Statuz ,
                               c3.Sort ,
                               d4.SchoolNature ,
                               d4.ClassNum ,
                               d4.TeacherNum ,
                               d4.StudentNum ,
                               d4.FloorArea ,
                               d4.BuildArea ,
                               ISNULL(d4.SchoolProp, 0) AS  SchoolProp ,
                               d4.IsLock,
                               a1.Id as PId,  
                               e5.DicName as SchoolPropName
                                {sqlColumn}
                             FROM  up_Unit AS a1 
                             INNER JOIN  up_UnitRelation AS b2 ON b2.BaseIsDelete = 0 AND b2.ExtensionType = 3 AND a1.Id = b2.UnitId
                             INNER JOIN  up_Unit AS c3 ON b2.ExtensionObjId = c3.Id 
                             LEFT JOIN  up_SchoolExtension AS d4 ON d4.BaseIsDelete = 0 AND c3.Id = d4.UnitId 
                             {sqlTable}
                             LEFT JOIN  sys_static_dictionary AS e5 ON e5.TypeCode = '1001' AND d4.SchoolProp = e5.DictionaryId
                             WHERE c3.BaseIsDelete = 0 
                          ) as tb1 WHERE   1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.Pid > 0)
                {
                    strSql.Append(" AND PId = @PId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@PId", param.Pid));
                }
                if (param.Id > 0)
                {
                    strSql.Append(" AND Id = @Id ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Id", param.Id));
                }
                if (param.SchoolProp > 0)
                {
                    strSql.Append(" AND SchoolProp = @SchoolProp ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolProp", param.SchoolProp));
                }
                if (param.SchoolStageId > 0)
                {
                    strSql.Append(" AND SchoolStageId = @SchoolStageId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStageId", param.SchoolStageId));
                }
                if (param.SchoolNature > 0)
                {
                    strSql.Append(" AND SchoolNature = @SchoolNature ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolNature", param.SchoolNature));
                }
                if (param.Statuz > -1)
                {
                    strSql.Append(" AND Statuz = @Statuz");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Statuz", param.Statuz));
                }
                if (!string.IsNullOrEmpty(param.Name))
                {
                    strSql.Append(" AND Name like @Name ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Name", $"%{param.Name}%"));
                }
                if (!param.IsHasKindergarten)
                {
                    strSql.Append(" AND SchoolProp <> 1001000 ");
                }
            }
            return parameter;
        }

        public async Task<List<UnitEntity>> GetSchoolList(UnitListParam param)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append($@" SELECT * From (
                                SELECT u1.* 
                                ,se2.SchoolNature
                                ,se2.SchoolProp
                                ,se2.TrackNum
                                ,ur3.UnitId AS CountyId
                                ,ur4.UnitId AS CityId
                                FROM up_Unit AS u1 
                                INNER JOIN up_SchoolExtension AS se2 ON se2.BaseIsDelete = 0 AND u1.Id = se2.UnitId 
                                LEFT JOIN up_UnitRelation AS ur3 ON ur3.BaseIsDelete = 0 AND u1.Id = ur3.ExtensionObjId AND ur3.ExtensionType = 3
	                            LEFT JOIN up_UnitRelation AS ur4 ON ur4.BaseIsDelete = 0 AND ur3.UnitId = ur4.ExtensionObjId AND ur3.ExtensionType = 3
                          ) as tb1 WHERE   1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.Pid > 0)
                {
                    if (param.CurrentUnitType == UnitTypeEnum.County.ParseToInt())
                    {
                        strSql.Append(" AND CountyId = @PId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@PId", param.Pid));
                    }
                    else if (param.CurrentUnitType == UnitTypeEnum.City.ParseToInt())
                    {
                        strSql.Append(" AND CityId = @PId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@PId", param.Pid));
                    }
                }
                else
                {

                }
                if (param.Id > 0)
                {
                    strSql.Append(" AND Id = @Id ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Id", param.Id));
                }
                if (param.UnitType > 0)
                {
                    strSql.Append(" AND UnitType = @UnitType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitType", param.UnitType));
                }
                if (param.SchoolProp > 0)
                {
                    strSql.Append(" AND SchoolProp = @SchoolProp ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolProp", param.SchoolProp));
                }
                if (param.SchoolProple !=-10000)
                {
                    strSql.Append(" AND SchoolProp <> @SchoolProple ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolProple", param.SchoolProple));
                }
                if (param.SchoolStageId > 0)
                {
                    strSql.Append(" AND SchoolStageId = @SchoolStageId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStageId", param.SchoolStageId));
                }
                if (param.SchoolNature > 0)
                {
                    strSql.Append(" AND SchoolNature = @SchoolNature ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolNature", param.SchoolNature));
                }
                if (param.Statuz > -1)
                {
                    strSql.Append(" AND Statuz = @Statuz");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Statuz", param.Statuz));
                }
                if (!string.IsNullOrEmpty(param.Name))
                {
                    strSql.Append(" AND Name like @Name ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Name", $"%{param.Name}%"));
                }
            }
            var list = await this.BaseRepository().FindList<UnitEntity>(strSql.ToString(), parameter.ToArray());
            return list.ToList();
        }
        #endregion

        #region 查询统计

        /// <summary>
        /// 单位学段统计集合(区县查询PId=Unitid)
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<List<UnitEntity>> GetSchoolSchoolStageList(UnitListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append($@" SELECT * From (
                               SELECT school.Id ,
                               school.BaseIsDelete ,
                               school.BaseCreateTime ,
                               school.BaseModifyTime ,
                               school.BaseCreatorId ,
                               school.BaseModifierId ,
                               school.BaseVersion ,
                               school.UnitType ,
                               school.IndustryId ,
                               school.Code ,
                               school.OrganizationCode ,
                               school.Name ,
                               school.Brief ,
                               school.PinYin ,
                               school.PinYinBrief ,
                               school.AreaId ,
                               school.Address ,
                               school.Mobile ,
                               school.Position ,
                               school.Memo ,
                               school.Statuz ,
                               school.Sort ,
                               d4.SchoolNature ,
                               d4.ClassNum ,
                               d4.TeacherNum ,
                               d4.StudentNum ,
                               d4.FloorArea ,
                               d4.BuildArea ,
                               ISNULL(d4.SchoolProp, 0) AS  SchoolProp ,
                               d4.IsLock,
                               county.Id as CountyId,
                               SchoolStage.DicName as SchoolStageName
                              ,dr6.DictionaryToId AS SchoolStageId
							 FROM  up_Unit AS school
                             INNER JOIN  up_UnitRelation AS b2 ON b2.BaseIsDelete = 0 AND b2.ExtensionType = 3 AND school.Id = b2.ExtensionObjId
                             INNER JOIN  up_Unit AS county ON county.BaseIsDelete = 0 AND b2.UnitId = county.Id
                             LEFT JOIN  up_SchoolExtension AS d4 ON d4.BaseIsDelete = 0 AND school.Id = d4.UnitId
                             LEFT JOIN  sys_dictionary_relation AS dr6 ON dr6.BaseIsDelete = 0 AND  d4.SchoolProp = dr6.DictionaryId AND dr6.RelationType = 1
                             LEFT JOIN  sys_static_dictionary AS SchoolStage ON SchoolStage.BaseIsDelete = 0 AND   SchoolStage.TypeCode = '1002' AND dr6.DictionaryToId = SchoolStage.DictionaryId
                             WHERE school.BaseIsDelete = 0 
                          ) as tb1 WHERE   1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.Pid > 0)
                {
                    strSql.Append(" AND CountyId = @PId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@PId", param.Pid));
                }
                if (param.Id > 0)
                {
                    strSql.Append(" AND Id = @Id ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Id", param.Id));
                }
                if (param.SchoolProp > 0)
                {
                    strSql.Append(" AND SchoolProp = @SchoolProp ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolProp", param.SchoolProp));
                }
                if (param.SchoolStageId > 0)
                {
                    strSql.Append(" AND SchoolStageId = @SchoolStageId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStageId", param.SchoolStageId));
                }
                if (param.SchoolNature > 0)
                {
                    strSql.Append(" AND SchoolNature = @SchoolNature ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolNature", param.SchoolNature));
                }
                if (param.Statuz > -1)
                {
                    strSql.Append(" AND Statuz = @Statuz ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Statuz", param.Statuz));
                }
                if (!string.IsNullOrEmpty(param.Name))
                {
                    strSql.Append(" AND Name like @Name ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Name", $"%{param.Name}%"));
                }
                if (!param.IsHasKindergarten)
                {
                    strSql.Append(" AND SchoolProp <> 1001000 ");
                }
            }
            IEnumerable<UnitEntity> list = null;
            if (pagination != null)
            {
                list = await this.BaseRepository().FindList<UnitEntity>(strSql.ToString(), parameter.ToArray(), pagination);
            }
            else
            {
                list = await this.BaseRepository().FindList<UnitEntity>(strSql.ToString(), parameter.ToArray());
            }
            return list.ToList();
        }

        public async Task<List<UnitStatisticModel>> GetSchoolGradeCourseList(UnitListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append($@" SELECT * From ( 
                SELECT school.* 
                ,urcounty.UnitId AS CountyId 
                ,dicSchoolStage.DictionaryId AS SchoolStageId ,dicSchoolStage.DicName AS SchoolStageName 
                ,dicgrade.DictionaryId AS GradeId ,dicgrade.DicName AS GradeName  
                ,diccourse.DictionaryId AS CourseId , diccourse.DicName AS CourseName 
                FROM up_Unit AS school
                INNER JOIN  up_SchoolExtension AS se7 ON school.Id  = se7.UnitId AND se7.BaseIsDelete = 0
                INNER JOIN  up_UnitRelation AS urcounty ON urcounty.BaseIsDelete = 0 AND school.Id = urcounty.ExtensionObjId AND urcounty.ExtensionType = 3 AND urcounty.BaseIsDelete = 0
                INNER JOIN sys_dictionary_relation AS rlschoolsatge ON rlschoolsatge.BaseIsDelete = 0 AND se7.SchoolProp = rlschoolsatge.DictionaryId AND rlschoolsatge.RelationType = 1 
                INNER JOIN sys_static_dictionary AS dicSchoolStage ON dicSchoolStage.BaseIsDelete = 0 AND rlschoolsatge.DictionaryToId = dicSchoolStage.DictionaryId AND dicSchoolStage.TypeCode = '1002'
                INNER JOIN sys_dictionary_relation AS rlgrade ON rlgrade.BaseIsDelete = 0 AND rlschoolsatge.DictionaryToId = rlgrade.DictionaryId AND rlgrade.RelationType = 1 
                INNER JOIN sys_static_dictionary AS dicgrade ON dicgrade.BaseIsDelete = 0 AND rlgrade.DictionaryToId = dicgrade.DictionaryId  AND dicgrade.TypeCode = '1003'
                INNER JOIN sys_dictionary_relation AS rlcourse ON rlcourse.BaseIsDelete = 0 AND rlgrade.DictionaryToId = rlcourse.DictionaryToId AND rlcourse.RelationType = 1
                INNER JOIN sys_static_dictionary AS diccourse ON diccourse.BaseIsDelete = 0 AND rlcourse.DictionaryId = diccourse.DictionaryId AND diccourse.TypeCode = '1005'
                WHERE school.BaseIsDelete = 0 AND diccourse.Nature = 1
                ) as tb1 WHERE   1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.Pid > 0)
                {
                    strSql.Append(" AND CountyId = @PId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@PId", param.Pid)); 
                }
              
                if (param.Id > 0)
                {
                    strSql.Append(" AND Id = @Id ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Id", param.Id));
                }
                
                if (param.SchoolProp > 0)
                {
                    strSql.Append(" AND SchoolProp = @SchoolProp ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolProp", param.SchoolProp));
                }
             
                if (param.SchoolStageId > 0)
                {
                    strSql.Append(" AND SchoolStageId = @SchoolStageId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolStageId", param.SchoolStageId));
                }
                if (param.SchoolNature > 0)
                {
                    strSql.Append(" AND SchoolNature = @SchoolNature ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolNature", param.SchoolNature));
                }
                if (param.Statuz > -1)
                {
                    strSql.Append(" AND Statuz = @Statuz");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Statuz", param.Statuz));
                }
                if (!string.IsNullOrEmpty(param.Name))
                {
                    strSql.Append(" AND Name like @Name ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Name", $"%{param.Name}%"));
                }
            }
            IEnumerable<UnitStatisticModel> list=null;
            if (pagination!=null)
            {
                list = await this.BaseRepository().FindList<UnitStatisticModel>(strSql.ToString(), parameter.ToArray(), pagination);
            }
            else
            {
                list = await this.BaseRepository().FindList<UnitStatisticModel>(strSql.ToString(), parameter.ToArray());
            }
            
            return list.ToList();
        }

        #endregion
    }
}
