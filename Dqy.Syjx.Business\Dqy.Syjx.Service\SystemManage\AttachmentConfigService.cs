﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.SystemManage;
using Dqy.Syjx.Model.Param.SystemManage;

namespace Dqy.Syjx.Service.SystemManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-10-25 11:02
    /// 描 述：附件配置服务类
    /// </summary>
    public class AttachmentConfigService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<AttachmentConfigEntity>> GetList(AttachmentConfigListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<AttachmentConfigEntity>> Ge
        }

        public async Task<List<AttachmentConfigEntity>> GetList(AttachmentConfigListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<AttachmentConfigEntity>> GetPageList(AttachmentConfigListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<AttachmentConfigEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<AttachmentConfigEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(AttachmentConfigEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
               strSql = $" update bn_AttachmentConfig set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
               strSql = $"update bn_AttachmentConfig set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<AttachmentConfigEntity, bool>> ListFilter(AttachmentConfigListParam param)
        {
            var expression = LinqExtensions.True<AttachmentConfigEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }

                if (param.FileCategory > 0)
                {
                    expression = expression.And(t => t.FileCategory == param.FileCategory);
                }
                if (!string.IsNullOrEmpty(param.Name))
                {
                    expression = expression.And(t => t.Name.Contains(param.Name) || t.Remark.Contains(param.Name) || t.GroupCode.Contains(param.Name) || t.OwnTableName.Contains(param.Name));
                }
            }
            return expression;
        }
        #endregion
    }
}

