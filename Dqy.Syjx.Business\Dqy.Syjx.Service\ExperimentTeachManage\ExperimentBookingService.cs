﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.ExperimentTeachManage;
using Dqy.Syjx.Model.Param.ExperimentTeachManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Enum.ExperimentTeachManage;
using Dqy.Syjx.Entity.OrganizationManage;
using Dqy.Syjx.Model.Param.OrganizationManage;
using Castle.DynamicProxy.Generators.Emitters.SimpleAST;
using Namotion.Reflection;
using Dqy.Syjx.Service.OrganizationManage;
using Dqy.Syjx.Model.Result.ExperimentTeachManage;
using Microsoft.EntityFrameworkCore.Query.SqlExpressions;

namespace Dqy.Syjx.Service.ExperimentTeachManage
{



    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-12-14 11:28
    /// 描 述：实验预约服务类
    /// </summary>
    public class ExperimentBookingService :  RepositoryFactory
    {
        private UserSubjectSetService userSubjectSetService = new UserSubjectSetService();

        #region 获取数据
        public async Task<List<ExperimentBookingEntity>> GetList(ExperimentBookingListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<ExperimentBookingEntity>> GetPageList(ExperimentBookingListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<ExperimentBookingEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<ExperimentBookingEntity>(id);
        }

        /// <summary>
        /// 实验预约查询
        /// </summary>
        /// <param name="param">查询参数</param>
        /// <param name="pagination">分页参数</param>
        /// <returns></returns>
        public async Task<List<ExperimentBookingEntity>> GetExperimentBookingList(ExperimentBookingListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            var parameter = new List<DbParameter>();

            strSql.Append(@$" SELECT * ,
                                CASE WHEN IsNeedDo = 1 THEN '{IsNeedEnum.MustDo.GetDescription()}' ELSE '{IsNeedEnum.SelectToDo.GetDescription()}' END AS IsNeedDoName,
                                CASE WHEN ExperimentType = 1 THEN '演示' WHEN ExperimentType = 2 THEN '分组' ELSE '演示/分组' END AS ExperimentTypeName,
                                CASE WHEN SourceType = 1 THEN '实验计划' WHEN SourceType = 2 THEN '实验目录' ELSE '实验计划/实验目录' END AS SourceTypeName,
                                CASE WHEN SchoolTerm = 1 THEN stuff((convert(varchar,RIGHT(SchoolYearStart,2))+'~'+(convert(varchar,RIGHT(SchoolYearEnd,2))))+ '上学期',1,0,'')
                                ELSE stuff((convert(varchar,RIGHT(SchoolYearStart,2))+'~'+(convert(varchar,RIGHT(SchoolYearEnd,2))))+ '下学期',1,0,'') END AS SchoolTermName
                                From (
                                       SELECT  UR2.UnitId AS CityId ,sa.AreaName AS CountyName ,EB.Id ,EB.BaseCreateTime ,EB.BaseModifyTime ,
                                               EB.SchoolYearStart ,EB.SchoolYearEnd ,EB.SchoolTerm ,EB.SchoolId ,
                                               EB.SchoolGradeClassId ,EB.CourseId ,EB.SourceType ,EB.PlanInfoId ,EB.PlanDetailId ,
                                               EB.TextbookVersionDetailId ,EB.ExperimentName ,EB.ExperimentType ,EB.Groupz ,
                                               EB.ClassTime ,EB.SectionId ,EB.SectionShow ,EB.FunRoomId ,EB.EquipmentNeed ,
                                               EB.MaterialNeed ,EB.Remark ,EB.Statuz ,EB.ArrangerId ,EB.ArrangerTime ,EB.RecordMode ,EB.ArrangerRemark ,
                                               EB.ArrangerEquipmentNeed ,EB.ArrangerMaterialNeed ,
                                               D2.DicName AS CourseName ,ISNULL(FR.Name,'普通教室') AS FunRoomName ,
                                               EB.SchoolStage ,D13.DicName AS SchoolStageName ,
                                               D1.DicName AS GradeName ,
                                               GC.ClassDesc ,
                                               EB.BaseCreatorId AS BookUserId ,U.RealName AS BookUserName ,
                                               ER.BaseCreatorId AS RecordUserId ,U2.RealName AS RecordUserName ,FR.SafeguardUserId ,
                                               ER.ExperimentSummary ,ER.RunStatuz ,ER.ProblemDesc ,ER.Id AS ExperimentRecordId ,
                                               UR.UnitId AS CountyId ,UT.Name AS SchoolName ,UT.Sort ,
                                               CASE WHEN ISNULL(sc3.Id ,0) = 0 THEN 0 ELSE 1 END IsRelationCamera ,
                                               CASE WHEN SourceType = 1 THEN PD.IsNeedDo WHEN SourceType = 2 THEN TBV.IsNeedDo ELSE 0 END AS IsNeedDo ,
                                               EB.IsMain ,EB.Pid ,EB.SchoolGradeClassIdz ,EB.ClassName ,ISNULL(EB.SchoolExperimentId ,0) AS SchoolExperimentId
                                        FROM  ex_ExperimentBooking AS EB
                                        LEFT JOIN  up_SchoolGradeClass AS GC ON EB.SchoolGradeClassId = GC.Id
                                        INNER JOIN  sys_static_dictionary AS D13 ON EB.SchoolStage = D13.DictionaryId AND D13.TypeCode = '{DicTypeCodeEnum.SchoolStage.ParseToInt()}'
                                        LEFT JOIN  sys_static_dictionary AS D1 ON GC.GradeId = D1.DictionaryId AND D1.TypeCode = '{DicTypeCodeEnum.Grade.ParseToInt()}'
                                        INNER JOIN  sys_static_dictionary AS D2 ON EB.CourseId = D2.DictionaryId AND D2.TypeCode = '{DicTypeCodeEnum.Course.ParseToInt()}'
                                        INNER JOIN  SysUser AS U ON EB.BaseCreatorId = U.Id
                                        INNER JOIN  up_UnitRelation AS UR ON UR.ExtensionObjId = EB.SchoolId AND UR.ExtensionType = 3 AND UR.BaseIsDelete = 0
                                        INNER JOIN  up_Unit AS UT ON EB.SchoolId = UT.Id
                                        INNER JOIN  up_UnitRelation AS UR2 ON UR.UnitId = UR2.ExtensionObjId AND UR2.ExtensionType = 3 AND UR2.BaseIsDelete = 0
					                    INNER JOIN  up_Unit AS un2 ON ur.UnitId = un2.Id AND un2.BaseIsDelete = 0
					                    LEFT JOIN  SysArea AS sa ON sa.AreaCode = un2.AreaId AND sa.BaseIsDelete = 0
                                        LEFT JOIN  bn_FunRoom AS FR ON EB.FunRoomId = FR.Id
                                        LEFT JOIN  ex_ExperimentRecord AS ER ON EB.Id = ER.ExperimentBookingId AND ER.BaseIsDelete = 0
                                        LEFT JOIN  SysUser AS U2 ON ER.BaseCreatorId = U2.Id
                                        LEFT JOIN  bn_SchoolCamera AS sc3 ON EB.SchoolId = sc3.UnitId AND EB.FunRoomId = sc3.FunRoomId AND sc3.BaseIsDelete = 0 AND sc3.Statuz = 1
                                        LEFT JOIN  ex_PlanDetail AS PD ON PD.Id = EB.PlanDetailId AND PD.BaseIsDelete = 0
								        LEFT JOIN ex_TextbookVersionDetail AS TBV ON TBV.Id = EB.TextbookVersionDetailId AND TBV.BaseIsDelete = 0
                                        WHERE EB.BaseIsDelete = 0
                          ) as T WHERE  1 = 1 ");
            if (param != null)
            {
                if (!param.Id.IsNullOrZero())
                {
                    strSql.Append($" AND Id = {param.Id}");
                }
                if (!param.SchoolId.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolId = {param.SchoolId}");//根据用户登录的单位ID进行查询
                }
                if (!param.CountyId.IsNullOrZero())
                {
                    strSql.Append($" AND CountyId = {param.CountyId}");
                    if (param.SchoolId > 0)
                    {
                        strSql.Append($" AND SchoolId = {param.SchoolId}");
                    }
                }
                if (!param.CityId.IsNullOrZero())
                {
                    strSql.Append($" AND CityId = {param.CityId}");
                    if (param.CountyId > 0)
                    {
                        strSql.Append($" AND CountyId = {param.CountyId}");
                    }
                    if (param.SchoolId > 0)
                    {
                        strSql.Append($" AND SchoolId = {param.SchoolId}");
                    }
                }
                if (param.SchoolStageId > 0)
                {
                    strSql.Append($" AND SchoolStage = {param.SchoolStageId}"); //学段
                }
                if (!param.SchoolYearStart.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolYearStart = {param.SchoolYearStart}"); //开始学年
                }
                if (!param.SchoolTerm.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolTerm = {param.SchoolTerm}"); //学期
                }
                if (!param.SchoolGradeClassId.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolGradeClassIdz like '%{param.SchoolGradeClassId}%' "); //年级班级Id
                }
                if (!param.CourseId.IsNullOrZero())
                {
                    strSql.Append($" AND CourseId = {param.CourseId}"); //学科
                }
                if (!param.SourceType.IsNullOrZero())
                {
                    strSql.Append($" AND (SourceType = {param.SourceType} or SourceType = 3) "); //实验来源
                }
                if (!param.ExperimentType.IsNullOrZero())
                {
                    strSql.Append($" AND (ExperimentType = {param.ExperimentType} or ExperimentType = 3)"); //实验类型
                }
                if (!param.BookUserId.IsNullOrZero())
                {
                    strSql.Append($" AND BookUserId = {param.BookUserId}"); //预约人
                }
                if (!param.RecordUserId.IsNullOrZero())
                {
                    strSql.Append($" AND RecordUserId = {param.RecordUserId}"); //登记人
                }
                if(param.Statuz.HasValue && param.Statuz > -1)
                {
                    strSql.Append($" AND Statuz = {param.Statuz}"); //状态
                }
                if(param.ThanStatuz.HasValue && param.ThanStatuz > -1)
                {
                    strSql.Append($" AND Statuz > {param.ThanStatuz}"); //大于状态
                }
                if (!param.RecordMode.IsNullOrZero())
                {
                    strSql.Append($" AND RecordMode = {param.RecordMode}"); //记录模式
                }
                if (param.ListType == 1)
                {
                    if (!param.SafeguardUserId.IsNullOrZero())
                    {
                        strSql.Append($" AND (SafeguardUserId = {param.SafeguardUserId} OR ArrangerId = {param.SafeguardUserId})"); //功能室维护人或预约指定的安排人
                    }
                }
                else
                {
                    if (!param.SafeguardUserId.IsNullOrZero())
                    {
                        strSql.Append($" AND SafeguardUserId = {param.SafeguardUserId}"); //功能室维护人
                    }
                }
                if (!param.ExperimentName.IsEmpty())
                {
                    strSql.Append($" AND ExperimentName like '%{param.ExperimentName}%'"); //实验名称
                }

                //if (param.IsShowCurrentSchoolYearStart == 1)
                //{
                //    strSql.Append($" AND SchoolYearStart={DateTime.Now.Year}");
                //}
                if(param.IsNeedDo > 0)
                {
                    strSql.Append($" AND IsNeedDo = {param.IsNeedDo} "); //实验要求实验
                }
                if (param.IsMain.HasValue)
                {
                    strSql.Append($" AND IsMain = {param.IsMain}"); //父级Id
                }
                if (!param.Pid.IsNullOrZero())
                {
                    strSql.Append($" AND Pid = {param.Pid}"); //父级Id
                }

                if (param.SetUserId > 0)
                {

                    //判断用户是否设置了学段、学科
                    var listStage = userSubjectSetService.GetUserSetStageList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listStage.Count > 0)
                    {
                        string stageId = string.Join(",", listStage.Select(f => f.DictionaryId));
                        strSql.Append($" AND SchoolStage IN ({stageId})");
                    }

                    var listCourse = userSubjectSetService.GetUserSetCourseList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listCourse.Count > 0)
                    {
                        string courseId = string.Join(",", listCourse.Select(f => f.DictionaryId));
                        strSql.Append($" AND CourseId IN ({courseId})");
                    }

                }
            }
            var list = await this.BaseRepository().FindList<ExperimentBookingEntity>(strSql.ToString(), parameter.ToArray(), pagination);
            return list.ToList();
        }

        /// <summary>
        /// 查询实验开出记录详细
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ExperimentBookingEntity> GetExperimentRecordById(long id)
        {
            StringBuilder strSql = new StringBuilder();
            var parameter = new List<DbParameter>();
            strSql.Append(@$" SELECT * From (
                               SELECT  EB.Id ,EB.BaseCreateTime ,EB.BaseModifyTime ,
                                       EB.SchoolYearStart ,EB.SchoolYearEnd ,EB.SchoolTerm ,EB.SchoolId ,
                                       EB.SchoolGradeClassId ,EB.CourseId ,EB.SourceType ,EB.PlanInfoId ,EB.PlanDetailId ,
                                       EB.TextbookVersionDetailId ,EB.ExperimentName ,EB.ExperimentType ,EB.Groupz ,
                                       EB.ClassTime ,EB.SectionId ,EB.SectionShow ,EB.FunRoomId ,EB.EquipmentNeed ,
                                       EB.MaterialNeed ,EB.Remark ,EB.Statuz ,EB.ArrangerId ,EB.ArrangerTime ,EB.RecordMode ,
                                       D1.DicName AS GradeName ,GC.ClassDesc ,D2.DicName AS CourseName ,ISNULL(FR.Name ,'普通教室') AS FunRoomName ,
                                       EB.BaseCreatorId AS BookUserId ,U.RealName AS BookUserName ,
                                       ER.BaseCreatorId AS RecordUserId ,U2.RealName AS RecordUserName ,FR.SafeguardUserId
                                FROM  ex_ExperimentBooking AS EB
                                INNER JOIN  up_SchoolGradeClass AS GC ON EB.SchoolGradeClassId = GC.Id
                                INNER JOIN  sys_static_dictionary AS D1 ON GC.GradeId = D1.DictionaryId AND D1.TypeCode = '{DicTypeCodeEnum.Grade.ParseToInt()}'
                                INNER JOIN  sys_static_dictionary AS D2 ON EB.CourseId = D2.DictionaryId AND D2.TypeCode = '{DicTypeCodeEnum.Course.ParseToInt()}'
                                INNER JOIN  SysUser AS U ON EB.BaseCreatorId = U.Id
                                LEFT JOIN  bn_FunRoom AS FR ON EB.FunRoomId = FR.Id
                                LEFT JOIN  ex_ExperimentRecord AS ER ON EB.Id = ER.ExperimentBookingId AND ER.BaseIsDelete = 0
                                LEFT JOIN  SysUser AS U2 ON ER.BaseCreatorId = U2.Id
                                WHERE EB.BaseIsDelete = 0
                          ) as T");
            strSql.AppendFormat($" WHERE Id = {id}");
            var list = await this.BaseRepository().FindList<ExperimentBookingEntity>(strSql.ToString());
            return list.FirstOrDefault();
        }

        /// <summary>
        /// 获取最后一个安排的实验信息
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<ExperimentBookingEntity> GetLastArrangeNeed(ExperimentBookingListParam param)
        {
            ExperimentBookingEntity entity = new ExperimentBookingEntity();
            var expression = LinqExtensions.True<ExperimentBookingEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                expression = expression.And(t => t.SchoolId == param.SchoolId);
                expression = expression.And(t => t.Statuz > 11);
                if (param.TextbookVersionDetailId > 0)
                {
                    expression = expression.And(t => t.TextbookVersionDetailId == param.TextbookVersionDetailId);
                }
                if (param.SchoolExperimentId > 0)
                {
                    expression = expression.And(t => t.SchoolExperimentId == param.SchoolExperimentId);
                }
            }
            Pagination page = new Pagination();
            page.Sort = "BaseModifyTime";
            page.SortType = " dasc ";
            page.PageSize = 1;
            var list = await this.BaseRepository().FindList(expression, page);
            if (list != null && list.Count() > 0)
            {
                return list.FirstOrDefault();
            }
            return null;
        }

        /// <summary>
        /// 实验任务列表
        /// </summary>
        /// <param name="param">查询参数</param>
        /// <param name="pagination">分页参数</param>
        /// <returns></returns>
        public async Task<List<ExperimentBookingEntity>> GetTaskList(ExperimentBookingListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            var parameter = new List<DbParameter>();
            strSql.Append($@" SELECT * FROM (
                            SELECT tb201.Id ,
				                    tb201.BaseIsDelete ,
				                    tb201.BaseCreateTime ,
				                    tb201.BaseModifyTime ,
				                    tb201.BaseCreatorId ,
				                    tb201.BaseModifierId ,
				                    tb201.BaseVersion ,
				                    tb201.SchoolYearStart ,
				                    tb201.SchoolYearEnd ,
				                    tb201.SchoolTerm ,
				                    tb201.GradeId ,
				                    tb201.CourseId ,
				                    tb201.ExperimentName ,
				                    tb201.ExperimentType ,
				                    tb201.IsNeedDo
				                    ,tb201.SourceType
				                    ,tb201.SourcePath
				                    ,tb201.SchoolStage
                                    ,tb201.VersionName
                                    ,tb201.ExperimentVersionId
				                    ,tb201.SchoolGradeClassId
				                    ,tb201.StartYear
				                    ,tb201.StudentNum
				                    ,tb201.Memo
				                    ,tb201.ClassDesc
	                                ,sd23.DicName AS Gradename
									,sd24.DicName AS CourseName
                                    ,ISNULL(eb51.Statuz,-1) Statuz
									,ISNULL(eb51.ArrangerTime,'') AS ArrangerTime
                    FROM (
                     SELECT tb101.Id ,
				                    tb101.BaseIsDelete ,
				                    tb101.BaseCreateTime ,
				                    tb101.BaseModifyTime ,
				                    tb101.BaseCreatorId ,
				                    tb101.BaseModifierId ,
				                    tb101.BaseVersion ,
				                    tb101.SchoolYearStart ,
				                    tb101.SchoolYearEnd ,
				                    tb101.SchoolTerm ,
				                    tb101.GradeId ,
				                    tb101.CourseId ,
				                    tb101.ExperimentName ,
				                    tb101.ExperimentType ,
				                    tb101.IsNeedDo
				                    ,tb101.SourceType
				                    ,tb101.SourcePath
				                    ,tb101.SchoolStage
                                    ,tb101.VersionName
                                    ,tb101.ExperimentVersionId
				                    ,gc41.Id AS SchoolGradeClassId
				                    ,gc41.StartYear
				                    ,gc41.StudentNum
				                    ,gc41.Memo
				                    ,gc41.ClassDesc
	                     FROM (
				            SELECT
                            pd1.Id ,
                            pd1.BaseIsDelete ,
                            pd1.BaseCreateTime ,
                            pd1.BaseModifyTime ,
                            pd1.BaseCreatorId ,
                            pd1.BaseModifierId ,
                            pd1.BaseVersion ,
				            pi12.SchoolYearStart ,
				            pi12.SchoolYearEnd ,
				            pi12.SchoolTerm ,
				            pi12.GradeId ,
				            pi12.CourseId ,
				            pd1.ExperimentName ,
                            pd1.ExperimentType ,
                            pd1.IsNeedDo
			                ,CASE WHEN pd1.SourcePath = 2 THEN '校本实验' ELSE vb223.VersionName END AS VersionName
                            ,ISNULL(vb223.Id,0) AS ExperimentVersionId
				            ,1 AS SourceType
                            ,pd1.SourcePath
                            ,sd22.DictionaryId AS SchoolStage
                            FROM  ex_PlanDetail AS pd1
                            INNER JOIN  ex_PlanInfo AS pi12 ON pd1.PlanInfoId = pi12.Id AND pi12.BaseIsDelete = 0
                            INNER JOIN  sys_dictionary_relation AS dr21 ON pi12.GradeId = dr21.DictionaryToId
                            INNER JOIN  sys_static_dictionary AS sd22 ON dr21.DictionaryId = sd22.DictionaryId AND sd22.TypeCode= '{DicTypeCodeEnum.SchoolStage.ParseToInt()}'
                            LEFT JOIN  ex_TextbookVersionBase AS vb223 ON pd1.TextbookVersionBaseId = vb223.Id AND vb223.BaseIsDelete = 0
				            WHERE pi12.SchoolId = {param.SchoolId} AND  pi12.SchoolYearStart = {param.SchoolYearStart}
                            UNION
				            SELECT
				            vd2.Id ,
				            vd2.BaseIsDelete ,
				            vd2.BaseCreateTime ,
				            vd2.BaseModifyTime ,
				            vd2.BaseCreatorId ,
				            vd2.BaseModifierId ,
				            vd2.BaseVersion ,
				            0 as SchoolYearStart ,
				            0 as SchoolYearEnd ,
				            vc3.SchoolTerm ,
				            vc3.GradeId ,
				            vc3.CourseId ,
				            vd2.ExperimentName ,
				            vd2.ExperimentType ,
				            vd2.IsNeedDo
                            ,vb1.VersionName
                            ,vb1.Id AS ExperimentVersionId
				            ,2 AS SourceType
				            ,1 AS SourcePath
	 			            ,vc3.SchoolStage
		                    FROM  ex_TextbookVersionDetail AS vd2
		                    INNER JOIN  ex_TextbookVersionBase AS vb1 ON vd2.TextbookVersionBaseId = vb1.Id AND vb1.BaseIsDelete = 0 AND vb1.Statuz =  {StatusEnum.Yes.ParseToInt()}
				            INNER JOIN   ex_TextbookVersionCurrent AS vc3 ON vb1.Id = vc3.TextbookVersionBaseId AND vc3.BaseIsDelete = 0 AND vb1.Statuz =  {StatusEnum.Yes.ParseToInt()}
				            LEFT JOIN ex_PlanDetail AS pd31 ON pd31.BaseIsDelete = 0 AND vd2.Id = pd31.TextbookVersionDetailId
				            WHERE pd31.Id IS NULL AND vc3.CountyIdz like '%{param.CountyId}%'
                            UNION
				            SELECT
				            se21.Id ,
				            se21.BaseIsDelete ,
				            se21.BaseCreateTime ,
				            se21.BaseModifyTime ,
				            se21.BaseCreatorId ,
				            se21.BaseModifierId ,
				            se21.BaseVersion ,
				            se21.SchoolYearStart ,
				            se21.SchoolYearEnd ,
				            se21.SchoolTerm ,
				            se21.GradeId ,
				            se21.CourseId ,
				            se21.ExperimentName ,
				            se21.ExperimentType ,
				            se21.IsNeedDo
                            ,'校本实验' AS VersionName
                            , 0 AS ExperimentVersionId
				            ,2 AS SourceType
				            ,2 AS SourcePath
				            ,se21.SchoolStage
				            FROM  ex_SchoolExperiment AS se21
				            LEFT JOIN ex_PlanDetail AS pd31 ON pd31.BaseIsDelete = 0 AND se21.Id = pd31.SchoolExperimentId
				            WHERE pd31.Id IS NULL  AND se21.SchoolId = {param.SchoolId}

				            ) AS tb101
				            CROSS JOIN (
                                SELECT  gc11.Id  ,gc11.StartYear  ,gc11.GradeId   ,gc11.StudentNum  ,gc11.Memo   ,gc11.ClassDesc ,tc13.CourseId
                                FROM  up_SchoolGradeClass AS gc11
                                INNER JOIN  up_UserClassInfo AS ci12 ON gc11.UnitId = ci12.UnitId AND  ci12.SchoolStageIdz like CONCAT('%',gc11.SchoolStage,'%') AND ci12.UserId =  {param.BookUserId}
                                INNER JOIN  up_UserTeachClass AS tc13 ON ci12.Id = tc13.UserClassInfoId  AND  tc13.ClassIdz like CONCAT('%',gc11.Id,'%')
                                WHERE ci12.BaseIsDelete = 0 AND ci12.BaseIsDelete = 0 AND tc13.BaseIsDelete = 0 AND gc11.IsGraduate = 0 AND ci12.IsCurrentUnit = 1 AND gc11.UnitId = {param.SchoolId}
                                    ) AS gc41
                                where  tb101.CourseId = gc41.CourseId AND  tb101.GradeId = gc41.GradeId
				            ) AS tb201
                        INNER JOIN  sys_static_dictionary AS sd23 ON sd23.TypeCode = '{DicTypeCodeEnum.Grade.ParseToInt()}' AND sd23.DictionaryId = tb201.GradeId
	                    INNER JOIN  sys_static_dictionary AS sd24 ON sd24.TypeCode = '{DicTypeCodeEnum.Course.ParseToInt()}' AND sd24.DictionaryId = tb201.CourseId
				        LEFT JOIN  ex_ExperimentBooking AS eb51 ON eb51.IsMain = 0 AND eb51.BaseIsDelete = 0 AND eb51.SchoolId = {param.SchoolId} AND tb201.SchoolGradeClassId = eb51.SchoolGradeClassId AND eb51.SchoolId = {param.SchoolId}
                                                                        AND ((tb201.SourceType = {SourceTypeEnum.ExperimentPlan.ParseToInt()} AND tb201.SourcePath = {SourcePathEnum.Version.ParseToInt()} AND eb51.PlanDetailId = tb201.Id)
															            OR (tb201.SourceType = {SourceTypeEnum.ExperimentList.ParseToInt()} AND tb201.SourcePath =  {SourcePathEnum.Version.ParseToInt()} AND eb51.TextbookVersionDetailId = tb201.Id)
															            OR (tb201.SourceType = {SourceTypeEnum.ExperimentList.ParseToInt()} AND tb201.SourcePath = {SourcePathEnum.School.ParseToInt()} AND eb51.SchoolExperimentId = tb201.Id))
                    ) AS tab601 Where 1 = 1
                ");
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = 0 ");
                if (param.Id > 0)
                {
                    strSql.Append(" AND Id = @Id ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Id", param.Id));
                }
                if (param.SourceType > 0)
                {
                    if (param.SourceType == SourceTypeEnum.ExperimentPlan.ParseToInt())
                    {
                        strSql.Append(" AND SourceType = @SourceType ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@SourceType", param.SourceType));
                    }
                    else
                    {
                        strSql.Append(" AND SourceType = @SourceType ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@SourceType", SourceTypeEnum.ExperimentList.ParseToInt()));
                    }
                }
                if (param.SchoolTerm > 0)
                {
                    strSql.Append(" AND SchoolTerm = @SchoolTerm ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolTerm", param.SchoolTerm));
                }
                if (param.CourseId > 0)
                {
                    strSql.Append(" AND CourseId = @CourseId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (param.GradeId > 0)
                {
                    strSql.Append(" AND GradeId = @GradeId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@GradeId", param.GradeId));
                }
                if (param.SchoolGradeClassId > 0)
                {
                    strSql.Append(" AND SchoolGradeClassId = @SchoolGradeClassId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolGradeClassId", param.SchoolGradeClassId));
                }
                if (param.ExperimentVersionId > 0)
                {
                    if (param.ExperimentVersionId == 99)
                    {
                        strSql.Append(" AND SourcePath = @SourcePath ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@SourcePath", SourcePathEnum.School.ParseToInt()));
                    }
                    else
                    {
                        strSql.Append(" AND ExperimentVersionId = @ExperimentVersionId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@ExperimentVersionId", param.ExperimentVersionId));
                    }
                }
                if (param.ExperimentType > 0)
                {
                    strSql.Append(" AND ExperimentType = @ExperimentType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ExperimentType", param.ExperimentType));
                }
                if (param.IsNeedDo > 0)
                {
                    strSql.Append(" AND IsNeedDo = @IsNeedDo ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@IsNeedDo", param.IsNeedDo));
                }
                if (param.IsFinish == 0)
                {
                    strSql.AppendFormat(@" AND Statuz <= {0} ", ExperimentBookStatuzEnum.ArrangeNoPass.ParseToInt());
                }
                else if (param.IsFinish == 1)
                {
                    strSql.AppendFormat(@" AND Statuz > {0} ", ExperimentBookStatuzEnum.ArrangeNoPass.ParseToInt());
                }
                if (!string.IsNullOrEmpty(param.ExperimentName))
                {
                    strSql.Append(" AND (VersionName like @Name OR ExperimentName like @Name ) ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Name", $"%{param.ExperimentName}%"));
                }
            }
            var list = await this.BaseRepository().FindList<ExperimentBookingEntity>(strSql.ToString(), parameter.ToArray(), pagination);
            return list.ToList();
        }


        /// <summary>
        /// 获取实验目录预约列表
        /// </summary>
        /// <param name="param">查询参数</param>
        /// <param name="pagination">分页参数</param>
        /// <returns></returns>
        public async Task<List<ExperimentBookingEntity>> GetCatalogList(ExperimentBookingListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            var parameter = new List<DbParameter>();
            string whereActivityType = "";
            if (param.ActivityType!= - 10000)
            {
                whereActivityType = string.Format(" AND se21.ActivityType = {0} ", param.ActivityType);
            }
            strSql.Append($@" SELECT * FROM (
                            SELECT tab1.*
							            ,sd23.DicName AS Gradename
							            ,sd24.DicName AS CourseName
                                          ,( SELECT case when count(cls.Id) > 0 then 1 else 0 end
                                            FROM  up_SchoolGradeClass AS cls
						                    LEFT JOIN  ex_ExperimentBooking AS eb ON eb.BaseIsDelete = 0 AND eb.Statuz > 0 ANd eb.Statuz <> 11 AND cls.UnitId = eb.SchoolId AND eb.ExperimentId = tab1.ExperimentId AND eb.SchoolGradeClassId =cls.Id  AND eb.IsMain = 0
                                            where cls.BaseIsDelete = 0  AND ISNULL(eb.Id, 0) = 0 AND  cls.UnitId =  {param.SchoolId}  AND cls.GradeId = tab1.GradeId
                                              AND (',' + userinfo.ClassIdz + ',') LIKE ('%,' + CAST(cls.Id AS VARCHAR) + ',%')
                                         ) AS AllNum
                                         ,userinfo.ClassIdz AS SchoolGradeClassIdz
							            FROM (
							            SELECT
				                        vd2.Id ,
				                        vd2.BaseIsDelete ,
				                        vd2.BaseCreateTime ,
				                        vd2.BaseModifyTime ,
				                        vd2.BaseCreatorId ,
				                        vd2.BaseModifierId ,
				                        vd2.BaseVersion ,
				                        vc3.SchoolTerm ,
				                        vc3.GradeId ,
				                        vc3.CourseId ,
				                        vd2.ExperimentName ,
				                        vd2.ExperimentType ,
                                        vd2.Chapter ,
				                        vd2.IsNeedDo
                                        ,vb1.VersionName
                                        ,vb1.Id AS ExperimentVersionId
                                        ,vb1.CompulsoryType
                                        ,vc3.Id AS TextbookVersionCurrentId
				                        ,2 AS SourceType
				                        ,1 AS SourcePath
	 			                        ,vc3.SchoolStage
                                        ,vd2.Id AS ExperimentId
                                        ,CASE WHEN ISNULL(tab01.Id,0) > 0 THEN 1 ELSE 2 END AS IsExam
		                                FROM  ex_TextbookVersionDetail AS vd2
		                                INNER JOIN  ex_TextbookVersionBase AS vb1 ON vd2.TextbookVersionBaseId = vb1.Id AND vb1.BaseIsDelete = 0 AND vb1.Statuz = 1
				                        INNER JOIN   ex_TextbookVersionCurrent AS vc3 ON vb1.Id = vc3.TextbookVersionBaseId AND vc3.BaseIsDelete = 0 AND vb1.Statuz =  1
                                        LEFT JOIN (
                                                SELECT pp1.* ,u3.Id AS SchoolId , u3.Name AS SchoolName ,u3.Sort
                                                ,pe2.ExperimentId
                                                FROM ex_PlanExamParameter AS pp1
                                                INNER JOIN up_UnitRelation AS ur2 ON ur2.BaseIsDelete = 0 AND pp1.UnitId = ur2.UnitId AND ur2.ExtensionType = 3
                                                INNER JOIN up_Unit AS u3 ON u3.BaseIsDelete = 0 AND u3.Id = ur2.ExtensionObjId
                                                INNER JOIN up_SchoolExtension AS se11 ON se11.BaseIsDelete = 0 AND se11.UnitId = u3.Id
				                                INNER JOIN sys_dictionary_relation AS dicr12 ON dicr12.BaseIsDelete = 0
                                                                                        AND dicr12.DictionaryId = se11.SchoolProp
                                                                                        AND dicr12.DictionaryToId = pp1.SchoolStage
                                                                                        AND dicr12.RelationType = 1
                                                INNER JOIN ex_PlanExamExperiment AS pe2 ON pe2.BaseIsDelete = 0 AND pp1.Id = pe2.PlanParameterSetId
                                             ) AS tab01 ON tab01.BaseIsDelete = 0  AND  tab01.UnitId = {param.CountyId ?? 0}
                                                AND tab01.SchoolYearStart = {param.SchoolYearStart}
			                                    AND tab01.SchoolTerm = {param.SchoolTerm}
                                                AND  tab01.ExperimentId = vd2.Id
                                                AND tab01.CourseId = vc3.CourseId
                                                AND tab01.GradeId = vc3.GradeId
                                                AND ISNULL(tab01.CompulsoryType,0) = ISNULL(vb1.CompulsoryType,0)

                                        WHERE vc3.CountyIdz LIKE '%{param.CountyId??0}%'
							            UNION
						                SELECT
				                        se21.Id ,
				                        se21.BaseIsDelete ,
				                        se21.BaseCreateTime ,
				                        se21.BaseModifyTime ,
				                        se21.BaseCreatorId ,
				                        se21.BaseModifierId ,
				                        se21.BaseVersion ,
				                        se21.SchoolTerm ,
				                        se21.GradeId ,
				                        se21.CourseId ,
				                        se21.ExperimentName ,
				                        se21.ExperimentType ,
                                        se21.Chapter ,
				                        se21.IsNeedDo
                                        ,'校本实验' AS VersionName
                                        , 0 AS ExperimentVersionId
                                        , 0 AS CompulsoryType
                                        ,0 AS TextbookVersionCurrentId
				                        ,2 AS SourceType
				                        ,2 AS SourcePath
				                        ,se21.SchoolStage
                                        ,se21.Id AS ExperimentId
                                        , 2 AS IsExam
				                        FROM  ex_SchoolExperiment AS se21
 				                        WHERE se21.SchoolId = {param.SchoolId} {whereActivityType}
							            ) AS tab1
							            LEFT JOIN  sys_static_dictionary AS sd23 ON sd23.TypeCode = '1003' AND sd23.DictionaryId = tab1.GradeId
							            LEFT JOIN  sys_static_dictionary AS sd24 ON sd24.TypeCode = '1005' AND sd24.DictionaryId = tab1.CourseId
                                        LEFT JOIN
					                             (SELECT userTeach.CourseId ,userTeach.ClassIdz
                                                     FROM 	up_UserTeachClass AS userTeach
					                                 INNER JOIN  up_UserClassInfo AS userClassInfo ON  userTeach.UserClassInfoId = userClassInfo.Id
                                                     where userClassInfo.BaseIsDelete = 0 AND userTeach.BaseIsDelete = 0 AND userClassInfo.UserId = {param.BookUserId}
                                                 ) AS userinfo  ON tab1.CourseId = userinfo.CourseId
                                    ) AS tab601 Where 1 = 1 AND GradeId < {GradeEnum.GaoYi.ParseToInt()}  AND (IsNULL(SchoolGradeClassIdz,'')) <> ''   ");
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = 0 ");
                if (param.SchoolTerm > 0)
                {
                    strSql.Append(" AND SchoolTerm = @SchoolTerm ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolTerm", param.SchoolTerm));
                }
                if (param.CourseIds != null && param.CourseIds.Count > 0)
                {
                    var wheretemp = new List<string>();
                    foreach (var item in param.CourseIds)
                    {
                        wheretemp.Add(string.Format(" CourseId = {0} ", item));
                    }
                    strSql.AppendFormat(" AND ({0}) ", string.Join(" OR ", wheretemp));
                }
                if (param.CourseId > 0)
                {
                    strSql.Append(" AND CourseId = @CourseId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (param.GradeIds != null && param.GradeIds.Count > 0)
                {
                    var wheretemp = new List<string>();
                    foreach (var item in param.GradeIds)
                    {
                        wheretemp.Add(string.Format(" GradeId = {0} ", item));
                    }
                    strSql.AppendFormat(" AND ({0}) ", string.Join(" OR ", wheretemp));
                }
                if (param.GradeId > 0)
                {
                    strSql.Append(" AND GradeId = @GradeId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@GradeId", param.GradeId));
                }
                if (param.SchoolStageList != null && param.SchoolStageList.Count > 0)
                {
                    strSql.AppendFormat(" AND ({0}) ", string.Join(" OR ", param.SchoolStageList.Select(m=> string.Format(" SchoolStage = {0} ", m))));
                }
                if (param.SchoolGradeClassId > 0)
                {
                    strSql.Append(" AND SchoolGradeClassId = @SchoolGradeClassId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolGradeClassId", param.SchoolGradeClassId));
                }
                if (param.ExperimentType > 0)
                {
                    strSql.Append(" AND ExperimentType = @ExperimentType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ExperimentType", param.ExperimentType));
                }
                if (param.IsNeedDo > 0)
                {
                    strSql.Append(" AND IsNeedDo = @IsNeedDo ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@IsNeedDo", param.IsNeedDo));
                }
                if (param.ExperimentVersionId > 0)
                {
                    if (param.ExperimentVersionId == 99)
                    {
                        strSql.Append(" AND SourcePath = @SourcePath ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@SourcePath", SourcePathEnum.School.ParseToInt()));
                    }
                    else
                    {
                        strSql.Append(" AND TextbookVersionCurrentId = @ExperimentVersionId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@ExperimentVersionId", param.ExperimentVersionId));
                    }
                }
                if (!string.IsNullOrEmpty(param.ExperimentName))
                {
                    strSql.Append(" AND ExperimentName like @Name ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Name", $"%{param.ExperimentName}%"));
                }
                if (param.IsExam == 1 || param.IsExam == 2)
                {
                    strSql.Append(" AND IsExam = @IsExam ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@IsExam", param.IsExam));
                }
            }
            var list = await this.BaseRepository().FindList<ExperimentBookingEntity>(strSql.ToString(), parameter.ToArray(), pagination);
            return list.ToList();
        }

        /// <summary>
        /// 查询预约页面，该预约班级可添加的实验。
        /// </summary>
        /// <param name="param">查询参数</param>
        /// <param name="pagination">分页参数</param>
        /// <returns></returns>
        public async Task<List<ExperimentBookingEntity>> GetAddBookingCatalogList(ExperimentBookingListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            var parameter = new List<DbParameter>();
            strSql.Append($@" SELECT * FROM (
                            SELECT tab1.*
							            ,sd23.DicName AS Gradename
							            ,sd24.DicName AS CourseName
							            ,ISNULL(booking.Id,0) AS BookingId
                                        ,ISNULL(userinfo.ClassIdz ,'') AS SchoolGradeClassIdz
							            FROM (
							            SELECT
				                        vd2.Id ,
				                        vd2.BaseIsDelete ,
				                        vd2.BaseCreateTime ,
				                        vd2.BaseModifyTime ,
				                        vd2.BaseCreatorId ,
				                        vd2.BaseModifierId ,
				                        vd2.BaseVersion ,
				                        vc3.SchoolTerm ,
				                        vc3.GradeId ,
				                        vc3.CourseId ,
				                        vd2.ExperimentName ,
				                        vd2.ExperimentType ,
				                        vd2.IsNeedDo
                                        ,vb1.VersionName
                                        ,vc3.Id AS ExperimentVersionId
				                        ,2 AS SourceType
				                        ,1 AS SourcePath
	 			                        ,vc3.SchoolStage
                                        ,vc3.Id AS TextbookVersionCurrentId
                                        ,vd2.Id AS ExperimentId
                                        ,vb1.CompulsoryType
		                                FROM  ex_TextbookVersionDetail AS vd2
		                                INNER JOIN  ex_TextbookVersionBase AS vb1 ON vd2.TextbookVersionBaseId = vb1.Id AND vb1.BaseIsDelete = 0 AND vb1.Statuz = 1
				                        INNER JOIN   ex_TextbookVersionCurrent AS vc3 ON vb1.Id = vc3.TextbookVersionBaseId AND vc3.BaseIsDelete = 0 AND vb1.Statuz =  1
                                        LEFT JOIN (
                                                SELECT pp1.* ,u3.Id AS SchoolId , u3.Name AS SchoolName ,u3.Sort
                                                ,pe2.ExperimentId
                                                FROM ex_PlanExamParameter AS pp1
                                                INNER JOIN up_UnitRelation AS ur2 ON ur2.BaseIsDelete = 0 AND pp1.UnitId = ur2.UnitId AND ur2.ExtensionType = 3
                                                INNER JOIN up_Unit AS u3 ON u3.BaseIsDelete = 0 AND u3.Id = ur2.ExtensionObjId
                                                INNER JOIN up_SchoolExtension AS se11 ON se11.BaseIsDelete = 0 AND se11.UnitId = u3.Id
				                                INNER JOIN sys_dictionary_relation AS dicr12 ON dicr12.BaseIsDelete = 0
                                                                                        AND dicr12.DictionaryId = se11.SchoolProp
                                                                                        AND dicr12.DictionaryToId = pp1.SchoolStage
                                                                                        AND dicr12.RelationType = 1
                                                INNER JOIN ex_PlanExamExperiment AS pe2 ON pe2.BaseIsDelete = 0 AND pp1.Id = pe2.PlanParameterSetId
                                             ) AS tab01 ON tab01.BaseIsDelete = 0  AND  tab01.UnitId = {param.CountyId ?? 0}
                                                AND tab01.SchoolYearStart = {param.SchoolYearStart}
			                                    AND tab01.SchoolTerm = {param.SchoolTerm}
                                                AND  tab01.ExperimentId = vd2.Id
                                                AND tab01.CourseId = vc3.CourseId
                                                AND tab01.GradeId = vc3.GradeId
                                                AND ISNULL(tab01.CompulsoryType,0) = ISNULL(vb1.CompulsoryType,0)
                                        WHERE vc3.CountyIdz LIKE '%{param.CountyId ?? 0}%'
							            UNION
						                SELECT
				                        se21.Id ,
				                        se21.BaseIsDelete ,
				                        se21.BaseCreateTime ,
				                        se21.BaseModifyTime ,
				                        se21.BaseCreatorId ,
				                        se21.BaseModifierId ,
				                        se21.BaseVersion ,
				                        se21.SchoolTerm ,
				                        se21.GradeId ,
				                        se21.CourseId ,
				                        se21.ExperimentName ,
				                        se21.ExperimentType ,
				                        se21.IsNeedDo
                                        ,'校本实验' AS VersionName
                                        , 0 AS ExperimentVersionId
				                        ,2 AS SourceType
				                        ,2 AS SourcePath
				                        ,se21.SchoolStage
                                        , 0 AS TextbookVersionCurrentId
                                        ,se21.Id AS ExperimentId
                                        ,0 AS CompulsoryType
				                        FROM  ex_SchoolExperiment AS se21
 				                        WHERE se21.SchoolId = {param.SchoolId}
							            ) AS tab1
							            LEFT JOIN  sys_static_dictionary AS sd23 ON sd23.TypeCode = '1003' AND sd23.DictionaryId = tab1.GradeId
							            LEFT JOIN  sys_static_dictionary AS sd24 ON sd24.TypeCode = '1005' AND sd24.DictionaryId = tab1.CourseId
                                        LEFT JOIN (
						                    SELECT bk2.Id, bk2.ExperimentId FROM  ex_ExperimentBooking AS bk1
						                    INNER JOIN  ex_ExperimentBooking AS bk2 ON bk2.BaseIsDelete = 0 AND bk2.SchoolGradeClassId = bk1.SchoolGradeClassId
						                    WHERE bk1.BaseIsDelete = 0 AND ( bk2.Statuz > 0 OR bk2.Pid = {param.Pid})  AND bk1.Pid = {param.Pid}
						                    AND bk1.SchoolId = {param.SchoolId}
					                    ) AS booking ON tab1.Id = booking.ExperimentId
                                        LEFT JOIN
					                    (SELECT userTeach.CourseId ,userTeach.ClassIdz
                                            FROM 	up_UserTeachClass AS userTeach
					                        INNER JOIN  up_UserClassInfo AS userClassInfo ON  userTeach.UserClassInfoId = userClassInfo.Id
                                            where userClassInfo.BaseIsDelete = 0 AND userTeach.BaseIsDelete = 0 AND userClassInfo.UserId = {param.BookUserId}
                                        ) AS userinfo  ON tab1.CourseId = userinfo.CourseId

                            ) AS tab601 Where BookingId = 0
                ");
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = 0 ");
                if (param.SchoolTerm > 0)
                {
                    strSql.Append(" AND SchoolTerm = @SchoolTerm ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolTerm", param.SchoolTerm));
                }
                if (param.CourseId > 0)
                {
                    strSql.Append(" AND CourseId = @CourseId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (param.CourseIds != null && param.CourseIds.Count > 0)
                {
                    var wheretemp = new List<string>();
                    foreach (var item in param.CourseIds)
                    {
                        wheretemp.Add(string.Format(" CourseId = {0} ", item));
                    }
                    strSql.AppendFormat(" AND ({0}) ", string.Join(" OR ", wheretemp));
                }
                if (param.GradeId > 0)
                {
                    strSql.Append(" AND GradeId = @GradeId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@GradeId", param.GradeId));
                }
                if (param.GradeIds.Count > 0)
                {
                    var wheretemp = new List<string>();
                    foreach (var item in param.GradeIds)
                    {
                        wheretemp.Add(string.Format(" GradeId = {0} ", item));
                    }
                    strSql.AppendFormat(" AND ({0}) ", string.Join(" OR ", wheretemp));
                }
                if (param.SchoolGradeClassId > 0)
                {
                    strSql.Append(" AND SchoolGradeClassId = @SchoolGradeClassId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolGradeClassId", param.SchoolGradeClassId));
                }
                if (param.SchoolGradeClassIds != null && param.SchoolGradeClassIds.Count > 0)
                {
                    var wheretemp = new List<string>();
                    foreach (var item in param.SchoolGradeClassIds)
                    {
                        wheretemp.Add(string.Format(" SchoolGradeClassIdz like '%{0}%' ", item));
                    }
                    strSql.AppendFormat(" AND ({0}) ", string.Join(" OR ", wheretemp));
                }
                if (param.ExperimentVersionId > 0)
                {
                    if (param.ExperimentVersionId == 99)
                    {
                        strSql.Append(" AND SourcePath = @SourcePath ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@SourcePath", SourcePathEnum.School.ParseToInt()));
                    }
                    else
                    {
                        strSql.Append(" AND ExperimentVersionId = @ExperimentVersionId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@ExperimentVersionId", param.ExperimentVersionId));
                    }
                }
                if (param.ExperimentType > 0)
                {
                    strSql.Append(" AND ExperimentType = @ExperimentType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ExperimentType", param.ExperimentType));
                }
                if (param.IsNeedDo > 0)
                {
                    strSql.Append(" AND IsNeedDo = @IsNeedDo ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@IsNeedDo", param.IsNeedDo));
                }

                if (!string.IsNullOrEmpty(param.ExperimentName))
                {
                    strSql.Append(" AND (VersionName like @Name OR ExperimentName like @Name ) ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Name", $"%{param.ExperimentName}%"));
                }
                if (param.IsExam == 1 || param.IsExam == 2)
                {
                    strSql.Append(" AND IsExam = @IsExam ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@IsExam", param.IsExam));
                }
            }
            var list = await this.BaseRepository().FindList<ExperimentBookingEntity>(strSql.ToString(), parameter.ToArray(), pagination);
            return list.ToList();
        }


        /// <summary>
        /// 实验预约计划列表
        /// </summary>
        /// <param name="param">查询参数</param>
        /// <param name="pagination">分页参数</param>
        /// <returns></returns>
        public async Task<List<ExperimentBookingEntity>> GetPlanList(ExperimentBookingListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            var parameter = new List<DbParameter>();
            strSql.Append($@" SELECT * FROM (
                SELECT
                     pd1.Id ,
                     pd1.BaseIsDelete ,
                     pd1.BaseCreateTime ,
                     pd1.BaseModifyTime ,
                     pd1.BaseCreatorId ,
                     pd1.BaseModifierId ,
                     pd1.BaseVersion ,
                     pd1.ActivityType ,
                     pi12.SchoolId ,
				     pi12.SchoolYearStart ,
				     pi12.SchoolYearEnd ,
				     pi12.SchoolTerm ,
				     pi12.GradeId ,
				     pi12.CourseId ,
                     pi12.ClassIdz AS PlanClassIdz ,
                     pi12.CompulsoryType ,
				     pd1.ExperimentName ,
                     pd1.ExperimentType ,
                     pd1.WeekNum
                     ,pd1.IsNeedDo
                     , pd1.ExperimentId
				     ,1 AS SourceType
                     ,pd1.SourcePath
                     ,( SELECT case when count(cls.Id) > 0 then 1 else 0 end
                        FROM  up_SchoolGradeClass AS cls
						LEFT JOIN  ex_ExperimentBooking AS eb ON eb.BaseIsDelete = 0 AND eb.Statuz > 0 ANd eb.Statuz <> 11 AND cls.UnitId = eb.SchoolId AND eb.ExperimentId = pd1.ExperimentId AND eb.SchoolGradeClassId =cls.Id  AND eb.IsMain = 0
                      where cls.BaseIsDelete = 0  AND ISNULL(eb.Id, 0) = 0 AND  cls.UnitId =  {param.SchoolId}  AND cls.GradeId = pi12.GradeId
                        AND (',' + userinfo.ClassIdz + ',') LIKE ('%,' + CAST(cls.Id AS VARCHAR) + ',%')
                     ) AS AllNum
                     ,userinfo.ClassIdz AS SchoolGradeClassIdz
                     ,dic3.DictionaryId AS SchoolStage
                     ,CASE WHEN ISNULL(tab01.Id,0) > 0 THEN 1 ELSE 2 END AS IsExam
                     FROM  ex_PlanDetail AS pd1
                     INNER JOIN  ex_PlanInfo AS pi12 ON pd1.PlanInfoId = pi12.Id AND pi12.BaseIsDelete = 0
                     INNER JOIN sys_dictionary_relation AS r2 ON r2.BaseIsDelete = 0 AND pi12.GradeId = r2.DictionaryId AND r2.RelationType = 1
	                 INNER JOIN sys_static_dictionary AS dic3 ON dic3.BaseIsDelete = 0 AND r2.DictionaryToId = dic3.DictionaryId AND dic3.TypeCode = '{DicTypeCodeEnum.SchoolStage.ParseToInt()}'
                     LEFT JOIN
					(SELECT userTeach.CourseId ,userTeach.ClassIdz
                        FROM 	up_UserTeachClass AS userTeach
					    INNER JOIN  up_UserClassInfo AS userClassInfo ON  userTeach.UserClassInfoId = userClassInfo.Id
                        where userClassInfo.BaseIsDelete = 0 AND userTeach.BaseIsDelete = 0 AND userClassInfo.UserId = {param.BookUserId}
                    ) AS userinfo  ON pi12.CourseId = userinfo.CourseId
                     LEFT JOIN (
                        SELECT pp1.* ,u3.Id AS SchoolId , u3.Name AS SchoolName ,u3.Sort
                        ,pe2.ExperimentId
                        FROM ex_PlanExamParameter AS pp1
                        INNER JOIN up_UnitRelation AS ur2 ON ur2.BaseIsDelete = 0 AND pp1.UnitId = ur2.UnitId AND ur2.ExtensionType = 3
                        INNER JOIN up_Unit AS u3 ON u3.BaseIsDelete = 0 AND u3.Id = ur2.ExtensionObjId
                        INNER JOIN up_SchoolExtension AS se11 ON se11.BaseIsDelete = 0 AND se11.UnitId = u3.Id
				        INNER JOIN sys_dictionary_relation AS dicr12 ON dicr12.BaseIsDelete = 0
                                                                AND dicr12.DictionaryId = se11.SchoolProp
                                                                AND dicr12.DictionaryToId = pp1.SchoolStage
                                                                AND dicr12.RelationType = 1
                        INNER JOIN ex_PlanExamExperiment AS pe2 ON pe2.BaseIsDelete = 0 AND pp1.Id = pe2.PlanParameterSetId
                     ) AS tab01 ON tab01.BaseIsDelete = 0  AND  tab01.SchoolId = pi12.SchoolId
                            AND tab01.SchoolYearStart = pi12.SchoolYearStart
			                AND tab01.SchoolTerm = pi12.SchoolTerm
                            AND  tab01.ExperimentId = pd1.ExperimentId
                            AND tab01.CourseId = pi12.CourseId
                            AND tab01.GradeId = pi12.GradeId
                            AND ISNULL(tab01.CompulsoryType,0) = ISNULL(pi12.CompulsoryType,0)
                ) AS tab601 Where WeekNum > 0 AND IsNULL(SchoolGradeClassIdz,'') <> '' AND ((GradeId >= {GradeEnum.GaoYi.ParseToInt()} AND ISNULL(PlanClassIdz,'') <> '' ) OR GradeId < {GradeEnum.GaoYi.ParseToInt()}) ");
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = 0 ");
                if (param.SchoolId > 0)
                {
                    strSql.Append(" AND SchoolId = @SchoolId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
                }
                if (param.SchoolYearStart > 0)
                {
                    strSql.Append(" AND SchoolYearStart = @SchoolYearStart ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolYearStart", param.SchoolYearStart));
                }
                if (param.SchoolTerm > 0)
                {
                    strSql.Append(" AND SchoolTerm = @SchoolTerm ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolTerm", param.SchoolTerm));
                }
                if (param.CourseId > 0)
                {
                    strSql.Append(" AND CourseId = @CourseId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (param.CourseIds != null && param.CourseIds.Count > 0)
                {
                    var wheretemp = new List<string>();
                    foreach (var item in param.CourseIds)
                    {
                        wheretemp.Add(string.Format(" CourseId = {0} ", item));
                    }
                    strSql.AppendFormat(" AND ({0}) ", string.Join(" OR ", wheretemp));
                }
                if (param.GradeId > 0)
                {
                    strSql.Append(" AND GradeId = @GradeId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@GradeId", param.GradeId));
                }
                if (param.GradeIds != null && param.GradeIds.Count > 0)
                {
                    var wheretemp = new List<string>();
                    foreach (var item in param.GradeIds)
                    {
                        wheretemp.Add(string.Format(" GradeId = {0} ", item));
                    }
                    strSql.AppendFormat(" AND ({0}) ", string.Join(" OR ", wheretemp));
                }
                if (param.SchoolStageList != null && param.SchoolStageList.Count > 0)
                {
                    strSql.AppendFormat(" AND ({0}) ", string.Join(" OR ", param.SchoolStageList.Select(m => string.Format(" SchoolStage = {0} ", m))));
                }
                if (param.SchoolGradeClassId > 0)
                {
                    strSql.Append(" AND SchoolGradeClassId = @SchoolGradeClassId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolGradeClassId", param.SchoolGradeClassId));
                }

                if (param.ExperimentType > 0)
                {
                    strSql.Append(" AND ExperimentType = @ExperimentType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ExperimentType", param.ExperimentType));
                }
                if (param.IsNeedDo > 0)
                {
                    strSql.Append(" AND IsNeedDo = @IsNeedDo ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@IsNeedDo", param.IsNeedDo));
                }

                if (!string.IsNullOrEmpty(param.ExperimentName))
                {
                    strSql.Append(" AND ExperimentName like @Name ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Name", $"%{param.ExperimentName}%"));
                }
                if (param.IsExam == 1 || param.IsExam == 2)
                {
                    strSql.Append(" AND IsExam = @IsExam ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@IsExam", param.IsExam));
                }
                if (param.ActivityType != -10000)
                {
                    strSql.Append(" AND ActivityType = @ActivityType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ActivityType", param.ActivityType));
                }
            }
            var list = await this.BaseRepository().FindList<ExperimentBookingEntity>(strSql.ToString(), parameter.ToArray(), pagination);
            return list.ToList();
        }

        /// <summary>
        /// 查询预约页面，该预约班级可添加的实验。
        /// </summary>
        /// <param name="param">查询参数</param>
        /// <param name="pagination">分页参数</param>
        /// <remarks>
        /// 1：当前预约已添加实验过滤不显示，
        /// 2：当前预约班级。可预约班级的交集（多个班级的情况），也就是排查不可预约的。
        /// 3：当前老师任课的学科，年级
        /// </remarks>
        /// <returns></returns>
        public async Task<List<ExperimentBookingEntity>> GetAddBookingPlanList(ExperimentBookingListParam param, Pagination pagination)
        {
            string whereGrade = "";
            if (param!=null)
            {
                whereGrade = $"AND ((GradeId >= {GradeEnum.GaoYi.ParseToInt()} AND LEN(ISNULL(PlanClassIdz,'')) >3 ) OR GradeId < {GradeEnum.GaoYi.ParseToInt()})";
                if (param.SchoolGradeClassIds != null && param.SchoolGradeClassIds.Count > 0)
                {
                    var wheretemp = new List<string>();
                    foreach (var item in param.SchoolGradeClassIds)
                    {
                        wheretemp.Add(string.Format(" PlanClassIdz like '%{0}%' ", item));
                    }

                    whereGrade = $"AND ((GradeId >= {GradeEnum.GaoYi.ParseToInt()} AND LEN(ISNULL(PlanClassIdz,'')) > 3 AND ({string.Join(" OR ", wheretemp)})) OR GradeId < {GradeEnum.GaoYi.ParseToInt()})";
                }
            }
            StringBuilder strSql = new StringBuilder();
            var parameter = new List<DbParameter>();
            strSql.Append($@" SELECT * FROM (
                SELECT
                     pd1.Id ,
                     pd1.BaseIsDelete ,
                     pd1.BaseCreateTime ,
                     pd1.BaseModifyTime ,
                     pd1.BaseCreatorId ,
                     pd1.BaseModifierId ,
                     pd1.BaseVersion ,
                     pd1.ActivityType ,
                     pi12.SchoolId ,
				     pi12.SchoolYearStart ,
				     pi12.SchoolYearEnd ,
				     pi12.SchoolTerm ,
				     pi12.GradeId ,
                     pi12.ClassIdz ,
				     pi12.CourseId ,
                     pi12.ClassIdz AS PlanClassIdz ,
				     pd1.ExperimentName ,
                     pd1.ExperimentType ,
                     pd1.IsNeedDo ,
                     pd1.WeekNum  ,
			         CASE WHEN pd1.SourcePath = 2 THEN '校本实验' ELSE vb223.VersionName END AS VersionName ,
                     ISNULL(vb223.Id,0) AS ExperimentVersionId ,
				     1 AS SourceType ,
                     pd1.SourcePath ,
                     pd1.TextbookVersionCurrentId  ,
                     sd22.DictionaryId AS SchoolStage ,
					 sd23.DicName AS Gradename ,
					 sd24.DicName AS CourseName ,
					 ISNULL(eb31.Id,0) AS BookingId
                     ,userinfo.ClassIdz AS SchoolGradeClassIdz
                     ,pd1.ExperimentId
                     ,pi12.CompulsoryType
                     ,CASE WHEN ISNULL(tab01.Id,0) > 0 THEN 1 ELSE 2 END AS IsExam
                     FROM  ex_PlanDetail AS pd1
                     INNER JOIN  ex_PlanInfo AS pi12 ON pd1.PlanInfoId = pi12.Id AND pi12.BaseIsDelete = 0
                     INNER JOIN  sys_dictionary_relation AS dr21 ON pi12.GradeId = dr21.DictionaryToId
                     INNER JOIN  sys_static_dictionary AS sd22 ON dr21.DictionaryId = sd22.DictionaryId AND sd22.TypeCode= '1002'
                     LEFT JOIN  ex_TextbookVersionBase AS vb223 ON pd1.TextbookVersionBaseId = vb223.Id AND vb223.BaseIsDelete = 0
					 INNER JOIN  sys_static_dictionary AS sd23 ON sd23.TypeCode = '1003' AND sd23.DictionaryId = pi12.GradeId
	                 INNER JOIN  sys_static_dictionary AS sd24 ON sd24.TypeCode = '1005' AND sd24.DictionaryId = pi12.CourseId
	                 LEFT JOIN (
						SELECT bk2.Id, bk2.ExperimentId FROM  ex_ExperimentBooking AS bk1
						INNER JOIN  ex_ExperimentBooking AS bk2 ON bk2.BaseIsDelete = 0 AND bk2.SchoolGradeClassId = bk1.SchoolGradeClassId
						WHERE bk1.BaseIsDelete = 0 AND ( bk2.Statuz > 0 OR bk2.Pid = {param.Pid})  AND bk1.Pid = {param.Pid}
						AND bk1.SchoolId = {param.SchoolId}
					) AS eb31 ON pd1.ExperimentId = eb31.ExperimentId
                    LEFT JOIN (
                        SELECT pp1.* ,u3.Id AS SchoolId , u3.Name AS SchoolName ,u3.Sort
                        ,pe2.ExperimentId
                        FROM ex_PlanExamParameter AS pp1
                        INNER JOIN up_UnitRelation AS ur2 ON ur2.BaseIsDelete = 0 AND pp1.UnitId = ur2.UnitId AND ur2.ExtensionType = 3
                        INNER JOIN up_Unit AS u3 ON u3.BaseIsDelete = 0 AND u3.Id = ur2.ExtensionObjId
                        INNER JOIN up_SchoolExtension AS se11 ON se11.BaseIsDelete = 0 AND se11.UnitId = u3.Id
				        INNER JOIN sys_dictionary_relation AS dicr12 ON dicr12.BaseIsDelete = 0
                                                                AND dicr12.DictionaryId = se11.SchoolProp
                                                                AND dicr12.DictionaryToId = pp1.SchoolStage
                                                                AND dicr12.RelationType = 1
                        INNER JOIN ex_PlanExamExperiment AS pe2 ON pe2.BaseIsDelete = 0 AND pp1.Id = pe2.PlanParameterSetId
                     ) AS tab01 ON tab01.BaseIsDelete = 0  AND  tab01.SchoolId = pi12.SchoolId
                            AND tab01.SchoolYearStart = pi12.SchoolYearStart
			                AND tab01.SchoolTerm = pi12.SchoolTerm
                            AND  tab01.ExperimentId = pd1.ExperimentId
                            AND tab01.CourseId = pi12.CourseId
                            AND tab01.GradeId = pi12.GradeId
                            AND ISNULL(tab01.CompulsoryType,0) = ISNULL(pi12.CompulsoryType,0)
                    LEFT JOIN
					(SELECT userTeach.CourseId ,userTeach.ClassIdz
                        FROM 	up_UserTeachClass AS userTeach
					    INNER JOIN  up_UserClassInfo AS userClassInfo ON  userTeach.UserClassInfoId = userClassInfo.Id
                        where userClassInfo.BaseIsDelete = 0 AND userTeach.BaseIsDelete = 0 AND userClassInfo.UserId = {param.BookUserId}
                    ) AS userinfo  ON pi12.CourseId = userinfo.CourseId
                ) AS tab601 Where BookingId = 0 AND WeekNum > 0
                        AND len(IsNULL(SchoolGradeClassIdz,'')) > 3
                        {whereGrade}
            ");
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = 0 ");
                if (param.Id > 0)
                {
                    strSql.Append(" AND Id = @Id ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Id", param.Id));
                }
                if (param.SchoolId > 0)
                {
                    strSql.Append(" AND SchoolId = @SchoolId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
                }
                if (param.SchoolYearStart > 0)
                {
                    strSql.Append(" AND SchoolYearStart = @SchoolYearStart ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolYearStart", param.SchoolYearStart));
                }
                if (param.SchoolTerm > 0)
                {
                    strSql.Append(" AND SchoolTerm = @SchoolTerm ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolTerm", param.SchoolTerm));
                }
                if (param.CourseIds != null && param.CourseIds.Count > 0)
                {
                    var wheretemp = new List<string>();
                    foreach (var item in param.CourseIds)
                    {
                        wheretemp.Add(string.Format(" CourseId = {0} ", item));
                    }
                    strSql.AppendFormat(" AND ({0}) ", string.Join(" OR ", wheretemp));
                }
                if (param.CourseId > 0)
                {
                    strSql.Append(" AND CourseId = @CourseId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (param.GradeId > 0)
                {
                    strSql.Append(" AND GradeId = @GradeId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@GradeId", param.GradeId));
                }
                if (param.GradeIds != null && param.GradeIds.Count > 0)
                {
                    var wheretemp = new List<string>();
                    foreach (var item in param.GradeIds)
                    {
                        wheretemp.Add(string.Format(" GradeId = {0} ", item));
                    }
                    strSql.AppendFormat(" AND ({0}) ", string.Join(" OR ", wheretemp));
                }
                if (param.SchoolGradeClassId > 0)
                {
                    strSql.Append(" AND SchoolGradeClassId = @SchoolGradeClassId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolGradeClassId", param.SchoolGradeClassId));
                }
                if (param.ExperimentVersionId > 0)
                {
                    if (param.ExperimentVersionId == 99)
                    {
                        strSql.Append(" AND SourcePath = @SourcePath ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@SourcePath", SourcePathEnum.School.ParseToInt()));
                    }
                    else
                    {
                        strSql.Append(" AND ExperimentVersionId = @ExperimentVersionId ");
                        parameter.Add(DbParameterExtension.CreateDbParameter("@ExperimentVersionId", param.ExperimentVersionId));
                    }
                }
                if (param.ExperimentType > 0)
                {
                    strSql.Append(" AND ExperimentType = @ExperimentType ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@ExperimentType", param.ExperimentType));
                }
                if (param.IsNeedDo > 0)
                {
                    strSql.Append(" AND IsNeedDo = @IsNeedDo ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@IsNeedDo", param.IsNeedDo));
                }
                if (param.IsFinish == 0)
                {
                    strSql.AppendFormat(@" AND Statuz <= {0} ", ExperimentBookStatuzEnum.ArrangeNoPass.ParseToInt());
                }
                else if (param.IsFinish == 1)
                {
                    strSql.AppendFormat(@" AND Statuz > {0} ", ExperimentBookStatuzEnum.ArrangeNoPass.ParseToInt());
                }
                if (!string.IsNullOrEmpty(param.ExperimentName))
                {
                    strSql.Append(" AND (VersionName like @Name OR ExperimentName like @Name ) ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Name", $"%{param.ExperimentName}%"));
                }
                if (param.IsExam == 1 || param.IsExam == 2)
                {
                    strSql.Append(" AND IsExam = @IsExam ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@IsExam", param.IsExam));
                }
                if (param.ActivityType != -10000)
                {
                    strSql.Append($" AND ActivityType = {param.ActivityType}"); //课程类型
                }
            }
            var list = await this.BaseRepository().FindList<ExperimentBookingEntity>(strSql.ToString(), parameter.ToArray(), pagination);
            return list.ToList();
        }


        /// <summary>
        /// 查询实验开出记录详细
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<List<SchoolGradeClassEntity>> GetClassList(ExperimentBookingListParam param)
        {
            StringBuilder strSql = new StringBuilder();
            var parameter = new List<DbParameter>();
            strSql.Append(@$" SELECT * From (
                SELECT
                  cls.Id ,
                  cls.BaseIsDelete ,
                  cls.BaseCreateTime ,
                  cls.BaseModifyTime ,
                  cls.BaseCreatorId ,
                  cls.BaseModifierId ,
                  cls.BaseVersion ,
                  cls.UnitId ,
                  cls.StartYear ,
                  cls.SchoolStage ,
                  cls.GradeId ,
                  cls.ClassId ,
                  cls.StudentNum ,
                  cls.Memo ,
                  sd.DicName+'（'+cls.ClassDesc+'）' AS ClassDesc,
                  cls.IsGraduate ,
                  ISNULL(eb.Id, 0) AS BookingId ,
                  ISNULL(eb.Statuz,-1) AS Statuz,
                  ISNULL(eb.RecordMode,-1) AS RecordMode
                  FROM  up_SchoolGradeClass AS cls
                  INNER JOIN  sys_static_dictionary AS sd ON cls.GradeId = sd.DictionaryId AND sd.BaseIsDelete = 0
                  LEFT JOIN  ex_ExperimentBooking AS eb ON eb.BaseIsDelete = 0 AND  eb.Statuz > 0 AND eb.Statuz <> 11 AND eb.SchoolId = {param.SchoolId} AND eb.ExperimentId = {param.Id} AND eb.SchoolGradeClassId =cls.Id  AND eb.IsMain = 0
                  where cls.Statuz = {StatusEnum.Yes.ParseToInt()}
            ) as T");
            strSql.AppendFormat($" Where  BaseIsDelete = 0  AND IsGraduate = 0 AND UnitId = {param.SchoolId}");
            if (!string.IsNullOrEmpty(param.Ids))
            {
                var wherList = new List<string>();
                var idArr = param.Ids.Split(",");
                foreach (var item in idArr)
                {
                    wherList.Add(string.Format(" Id = {0} ",item));
                }
                strSql.AppendFormat(" AND ({0}) ", string.Join(" OR ", wherList));
            }
            else
            {
                strSql.Append(" AND 1 <> 1 ");
            }
            if (param.GradeId > 0)
            {
                strSql.Append($" AND GradeId = {param.GradeId}"); //年级
            }
            var list = await this.BaseRepository().FindList<SchoolGradeClassEntity>(strSql.ToString());
            return list.OrderBy(m=>m.ClassId).ToList();
        }
        #endregion
        #region 预约【已预约】
        /// <summary>
        /// 实验预约查询
        /// </summary>
        /// <param name="param">查询参数</param>
        /// <param name="pagination">分页参数</param>
        /// <returns></returns>
        public async Task<List<ExperimentBookingEntity>> GetBookingList(ExperimentBookingListParam param, Pagination pagination)
        {
            var parameter = new List<DbParameter>();
            StringBuilder strSql = new StringBuilder();
            strSql.Append(@$"SELECT * FROM (
                SELECT mainEb.Id ,mainEb.BaseIsDelete ,  mainEb.BaseCreateTime , mainEb.BaseModifyTime , mainEb.BaseCreatorId AS BookUserId , mainEb.BaseModifierId
                , mainEb.CourseId ,mainEb.GradeId ,mainEb.SchoolGradeClassIdz ,mainEb.SourceType ,mainEb.RecordMode ,mainEb.ExperimentName
                , mainEb.SchoolYearStart ,mainEb.SchoolYearEnd ,mainEb.SchoolTerm  ,mainEb.SchoolId ,mainEb.ClassTime ,mainEb.SectionIndex
                ,mainEb.Statuz  ,mainEb.FunRoomId ,mainEb.ActivityType
                ,D2.DicName AS CourseName ,D1.DicName AS GradeName
                ,su.RealName AS BookUserName
                ,CASE WHEN ISNULL(fr.Id,0) > 0 THEN fr.SafeguardUserId ELSE mainEb.ArrangerId END AS ArrangerId
                FROM  ex_ExperimentBooking AS mainEb
                INNER JOIN  sys_static_dictionary AS D2 ON mainEb.CourseId = D2.DictionaryId AND D2.TypeCode = '{DicTypeCodeEnum.Course.ParseToInt()}'
                INNER JOIN  sys_static_dictionary AS D1 ON mainEb.GradeId = D1.DictionaryId AND D1.TypeCode = '{DicTypeCodeEnum.Grade.ParseToInt()}'
                JOIN  SysUser AS su ON mainEb.BaseCreatorId = su.Id
                LEFT JOIN bn_FunRoom AS fr ON fr.BaseIsDelete = 0 AND mainEb.FunRoomId = fr.Id
                WHERE mainEb.BaseIsDelete = 0  AND mainEb.IsMain = 1
                ) as T WHERE  BaseIsDelete = 0 ");
            if (param != null)
            {
                if (!param.Id.IsNullOrZero())
                {
                    strSql.Append($" AND Id = {param.Id}");
                }
                if (!param.SchoolId.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolId = {param.SchoolId}");//根据用户登录的单位ID进行查询
                }
                if (!param.CountyId.IsNullOrZero())
                {
                    strSql.Append($" AND CountyId = {param.CountyId}");
                    if (param.SchoolId > 0)
                    {
                        strSql.Append($" AND SchoolId = {param.SchoolId}");
                    }
                }
                if (!param.CityId.IsNullOrZero())
                {
                    strSql.Append($" AND CityId = {param.CityId}");
                    if (param.CountyId > 0)
                    {
                        strSql.Append($" AND CountyId = {param.CountyId}");
                    }
                    if (param.SchoolId > 0)
                    {
                        strSql.Append($" AND SchoolId = {param.SchoolId}");
                    }
                }
                if (param.SchoolStageId > 0)
                {
                    strSql.Append($" AND SchoolStage = {param.SchoolStageId}"); //学段
                }
                if (!param.SchoolYearStart.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolYearStart = {param.SchoolYearStart}"); //开始学年
                }
                if (!param.SchoolTerm.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolTerm = {param.SchoolTerm}"); //学期
                }
                if (!param.SchoolGradeClassId.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolGradeClassIdz like '%{param.SchoolGradeClassId}%' "); //年级班级Id
                }
                if (!param.CourseId.IsNullOrZero())
                {
                    strSql.Append($" AND CourseId = {param.CourseId}"); //学科
                }
                if (!param.SourceType.IsNullOrZero())
                {
                    strSql.Append($" AND (SourceType = {param.SourceType} or SourceType = 3) "); //实验来源
                }
                if (param.ThanStatuz.HasValue && param.ThanStatuz > -1)
                {
                    strSql.Append($" AND Statuz >= {param.ThanStatuz}"); //大于状态
                }
                if (param.Statuz > -1)
                {
                    strSql.Append($" AND Statuz = {param.Statuz}"); //查询状态
                }
                if (!param.BookUserId.IsNullOrZero())
                {
                    strSql.Append($" AND BookUserId = {param.BookUserId}"); //预约人
                }
                if (!param.RecordUserId.IsNullOrZero())
                {
                    strSql.Append($" AND RecordUserId = {param.RecordUserId}"); //登记人
                }
                if (!param.ExperimentName.IsEmpty())
                {
                    strSql.Append($" AND ExperimentName like '%{param.ExperimentName}%'"); //实验名称
                }
                if (param.ActivityType != -10000)
                {
                    strSql.Append($" AND ActivityType like '%{param.ActivityType}%'"); //实验可成类型
                }
            }
            var list = await this.BaseRepository().FindList<ExperimentBookingEntity>(strSql.ToString(), parameter.ToArray(), pagination);
            return list.ToList();
        }
        #endregion

        #region 安排【待安排列表 ，已安排列表】
        /// <summary>
        /// 实验预约查询
        /// </summary>
        /// <param name="param">查询参数</param>
        /// <param name="pagination">分页参数</param>
        /// <returns></returns>
        public async Task<List<ExperimentBookingEntity>> GetArrangeList(ExperimentBookingListParam param, Pagination pagination)
        {

            var parameter = new List<DbParameter>();
            StringBuilder strChildrenSql = new StringBuilder();
            strChildrenSql.Append($@"
                SELECT eb.Pid ,Max(eb.Statuz) AS Statuz,eb.RecordMode  ,eb.ArrangerId ,fr.SafeguardUserId
                ,Max(eb.ClassName) AS ClassName ,Max(eb.ClassTime) AS ClassTime,Max(eb.FunRoomId) as FunRoomId,Max(eb.SectionIndex) AS SectionIndex
                ,Max(eb.SourceType) AS SourceType ,Max(eb.Id) AS Id
                ,Max(D2.DicName) AS CourseName ,Max(D1.DicName) AS GradeName
                ,Max(su.RealName) AS BookUserName
                FROM   ex_ExperimentBooking AS eb
                INNER JOIN  sys_static_dictionary AS D2 ON eb.CourseId = D2.DictionaryId AND D2.TypeCode = '{DicTypeCodeEnum.Course.ParseToInt()}'
                INNER JOIN  sys_static_dictionary AS D1 ON eb.GradeId = D1.DictionaryId AND D1.TypeCode = '{DicTypeCodeEnum.Grade.ParseToInt()}'
                LEFT JOIN  bn_FunRoom AS fr ON fr.BaseIsDelete = 0 AND eb.FunRoomId = fr.Id
                JOIN  SysUser AS su ON eb.BaseCreatorId = su.Id
                WHERE eb.BaseIsDelete = 0 AND eb.Pid >0 AND eb.IsMain = 0
            ");
            if (param != null)
            {
                if (!param.SchoolId.IsNullOrZero())
                {
                    strChildrenSql.Append($" AND eb.SchoolId = {param.SchoolId}");//根据用户登录的单位ID进行查询
                }
                if (!param.SchoolYearStart.IsNullOrZero())
                {
                    strChildrenSql.Append($" AND eb.SchoolYearStart = {param.SchoolYearStart}"); //开始学年
                }
                if (!param.SchoolTerm.IsNullOrZero())
                {
                    strChildrenSql.Append($" AND eb.SchoolTerm = {param.SchoolTerm}"); //学期
                }
                if (!param.ExperimentName.IsEmpty())
                {
                    strChildrenSql.Append($" AND eb.ExperimentName like '%{param.ExperimentName}%'"); //实验名称
                }
                if (!param.ExperimentType.IsNullOrZero())
                {
                    strChildrenSql.AppendFormat($" AND eb.ExperimentType = {0} ", param.ExperimentType); //实验类型
                }
                if (param.IsNeedDo > 0)
                {
                    strChildrenSql.Append($" AND eb.IsNeedDo = {param.IsNeedDo} "); //实验要求实验
                }
                if (param.Statuz.HasValue && param.Statuz > -1)
                {
                    strChildrenSql.Append($" AND eb.Statuz = {param.Statuz}"); //状态
                }
                if (param.ThanStatuz.HasValue && param.ThanStatuz > -1)
                {
                    strChildrenSql.Append($" AND eb.Statuz > {param.ThanStatuz}"); //大于状态
                }
                if (!param.RecordMode.IsNullOrZero())
                {
                    strChildrenSql.Append($" AND eb.RecordMode = {param.RecordMode}"); //记录模式
                }
                if (param.ActivityType != -10000)
                {
                    strChildrenSql.Append($" AND eb.ActivityType = {param.ActivityType}"); //课程类型
                }
                if (!param.SafeguardUserId.IsNullOrZero())
                {
                    strChildrenSql.Append($" AND (fr.SafeguardUserId = {param.SafeguardUserId} OR eb.ArrangerId = {param.SafeguardUserId})"); //功能室维护人或预约指定的安排人
                }
                if (param.SchoolGradeClassId > 0)
                {
                    strChildrenSql.Append($" AND eb.SchoolGradeClassIdz LIKE '%{param.SchoolGradeClassId}%' "); //查询班级
                }
            }
            strChildrenSql.Append(" GROUP BY eb.Pid ,eb.RecordMode  ,eb.ArrangerId ,fr.SafeguardUserId");
            var listchildren = await this.BaseRepository().FindList<ExperimentBookingEntity>(strChildrenSql.ToString(), parameter.ToArray(), pagination);

            StringBuilder strSql = new StringBuilder();
            strSql.Append(@$"SELECT * FROM (
                SELECT mainEb.Id ,mainEb.CourseId ,mainEb.GradeId ,mainEb.SchoolGradeClassIdz ,mainEb.SourceType ,mainEb.ActivityType
                , mainEb.SchoolYearStart ,mainEb.SchoolYearEnd ,mainEb.SchoolTerm  ,mainEb.SchoolId ,mainEb.ClassTime ,mainEb.SectionIndex ,mainEb.FunRoomId
                ,D2.DicName AS CourseName ,D1.DicName AS GradeName
                , mainEb.BaseCreatorId AS BookUserId ,su.RealName AS BookUserName
                FROM  ex_ExperimentBooking AS mainEb
                INNER JOIN  sys_static_dictionary AS D2 ON mainEb.CourseId = D2.DictionaryId AND D2.TypeCode = '{DicTypeCodeEnum.Course.ParseToInt()}'
                INNER JOIN  sys_static_dictionary AS D1 ON mainEb.GradeId = D1.DictionaryId AND D1.TypeCode = '{DicTypeCodeEnum.Grade.ParseToInt()}'
                JOIN  SysUser AS su ON mainEb.BaseCreatorId = su.Id
                WHERE mainEb.IsMain = 1
                ) as T WHERE  1 = 1 ");
            //拆分查询
            if (listchildren != null && listchildren.Count() > 0)
            {
                strSql.Append($" AND ({string.Join(" OR ", listchildren.Select(m => string.Format(" Id = {0} ", m.Pid)))}) ");
            }
            else
            {
                strSql.Append(" AND 1 <> 1 ");
            }

            if (param != null)
            {
                //if (!param.SafeguardUserId.IsNullOrZero())
                //{
                //    strSql.Append($" AND (SafeguardUserId = {param.SafeguardUserId} OR ArrangerId = {param.SafeguardUserId})"); //功能室维护人或预约指定的安排人
                //}
                if (!param.Id.IsNullOrZero())
                {
                    strSql.Append($" AND Id = {param.Id}");
                }
                if (!param.SchoolId.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolId = {param.SchoolId}");//根据用户登录的单位ID进行查询
                }
                if (!param.CountyId.IsNullOrZero())
                {
                    strSql.Append($" AND CountyId = {param.CountyId}");
                    if (param.SchoolId > 0)
                    {
                        strSql.Append($" AND SchoolId = {param.SchoolId}");
                    }
                }
                if (!param.CityId.IsNullOrZero())
                {
                    strSql.Append($" AND CityId = {param.CityId}");
                    if (param.CountyId > 0)
                    {
                        strSql.Append($" AND CountyId = {param.CountyId}");
                    }
                    if (param.SchoolId > 0)
                    {
                        strSql.Append($" AND SchoolId = {param.SchoolId}");
                    }
                }
                if (param.SchoolStageId > 0)
                {
                    strSql.Append($" AND SchoolStage = {param.SchoolStageId}"); //学段
                }
                if (!param.SchoolYearStart.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolYearStart = {param.SchoolYearStart}"); //开始学年
                }
                if (!param.SchoolTerm.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolTerm = {param.SchoolTerm}"); //学期
                }
                if (!param.SchoolGradeClassId.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolGradeClassIdz like '%{param.SchoolGradeClassId}%' "); //年级班级Id
                }
                if (!param.CourseId.IsNullOrZero())
                {
                    strSql.Append($" AND CourseId = {param.CourseId}"); //学科
                }
                if (!param.SourceType.IsNullOrZero())
                {
                    strSql.Append($" AND (SourceType = {param.SourceType} or SourceType = 3) "); //实验来源
                }

                if (!param.BookUserId.IsNullOrZero())
                {
                    strSql.Append($" AND BookUserId = {param.BookUserId}"); //预约人
                }
                if (!param.RecordUserId.IsNullOrZero())
                {
                    strSql.Append($" AND RecordUserId = {param.RecordUserId}"); //登记人
                }
                if (param.ActivityType != -10000)
                {
                    strSql.Append($" AND ActivityType = {param.ActivityType}"); //课程类型
                }
            }
            var list = await this.BaseRepository().FindList<ExperimentBookingEntity>(strSql.ToString(), parameter.ToArray());
            return list.ToList();
        }
        #endregion

        #region 登记【待登记列表 ，已登记列表】
        /// <summary>
        /// 实验预约查询
        /// </summary>
        /// <param name="param">查询参数</param>
        /// <param name="pagination">分页参数</param>
        /// <returns></returns>
        public async Task<List<ExperimentBookingEntity>> GetRecordList(ExperimentBookingListParam param, Pagination pagination)
        {

            var parameter = new List<DbParameter>();
            StringBuilder strChildrenSql = new StringBuilder();
            strChildrenSql.Append($@"
                SELECT eb.Pid ,eb.Statuz,eb.RecordMode  ,eb.SysUserId  FROM   ex_ExperimentBooking AS eb
                WHERE eb.BaseIsDelete = 0 AND eb.Pid >0 AND eb.IsMain = 0
            ");
            if (param != null)
            {
                if (!param.ExperimentName.IsEmpty())
                {
                    strChildrenSql.Append($" AND eb.ExperimentName like '%{param.ExperimentName}%'"); //实验名称
                }
                if (!param.ExperimentType.IsNullOrZero())
                {
                    strChildrenSql.AppendFormat($" AND eb.ExperimentType = {0} ", param.ExperimentType); //实验类型
                }
                if (param.IsNeedDo > 0)
                {
                    strChildrenSql.Append($" AND eb.IsNeedDo = {param.IsNeedDo} "); //实验要求实验
                }
                if (param.Statuz.HasValue && param.Statuz > -1)
                {
                    strChildrenSql.Append($" AND eb.Statuz = {param.Statuz}"); //状态
                }
                if (param.ThanStatuz.HasValue && param.ThanStatuz > -1)
                {
                    strChildrenSql.Append($" AND eb.Statuz > {param.ThanStatuz}"); //大于状态
                }
                if (!param.RecordMode.IsNullOrZero())
                {
                    strChildrenSql.Append($" AND eb.RecordMode = {param.RecordMode}"); //记录模式
                }
            }
            strChildrenSql.Append(" GROUP BY eb.Pid ,eb.Statuz,eb.RecordMode  ,eb.SysUserId ");
            StringBuilder strSql = new StringBuilder();
            strSql.Append(@$"SELECT * FROM (
                SELECT mainEb.Id ,mainEb.CourseId ,mainEb.GradeId ,mainEb.SchoolGradeClassIdz ,mainEb.SourceType
                , mainEb.SchoolYearStart ,mainEb.SchoolYearEnd ,mainEb.SchoolTerm  ,mainEb.SchoolId
                ,D2.DicName AS CourseName ,D1.DicName AS GradeName
                , mainEb.BaseCreatorId AS BookUserId ,su.RealName AS BookUserName
                , childreneb.Statuz,childreneb.RecordMode ,childreneb.SysUserId FROM  ex_ExperimentBooking AS mainEb
                INNER JOIN  sys_static_dictionary AS D2 ON mainEb.CourseId = D2.DictionaryId AND D2.TypeCode = '{DicTypeCodeEnum.Course.ParseToInt()}'
                INNER JOIN  sys_static_dictionary AS D1 ON mainEb.GradeId = D1.DictionaryId AND D1.TypeCode = '{DicTypeCodeEnum.Grade.ParseToInt()}'
                JOIN  SysUser AS su ON mainEb.BaseCreatorId = su.Id
                INNER JOIN ( {strChildrenSql.ToString()} ) AS childreneb ON mainEb.Id = childreneb.Pid
                WHERE mainEb.IsMain = 1
                ) as T WHERE  1 = 1 ");
            if (param != null)
            {
                if (!param.Id.IsNullOrZero())
                {
                    strSql.Append($" AND Id = {param.Id}");
                }
                if (!param.SchoolId.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolId = {param.SchoolId}");//根据用户登录的单位ID进行查询
                }
                if (!param.CountyId.IsNullOrZero())
                {
                    strSql.Append($" AND CountyId = {param.CountyId}");
                    if (param.SchoolId > 0)
                    {
                        strSql.Append($" AND SchoolId = {param.SchoolId}");
                    }
                }
                if (!param.CityId.IsNullOrZero())
                {
                    strSql.Append($" AND CityId = {param.CityId}");
                    if (param.CountyId > 0)
                    {
                        strSql.Append($" AND CountyId = {param.CountyId}");
                    }
                    if (param.SchoolId > 0)
                    {
                        strSql.Append($" AND SchoolId = {param.SchoolId}");
                    }
                }
                if (param.SchoolStageId > 0)
                {
                    strSql.Append($" AND SchoolStage = {param.SchoolStageId}"); //学段
                }
                if (!param.SchoolYearStart.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolYearStart = {param.SchoolYearStart}"); //开始学年
                }
                if (!param.SchoolTerm.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolTerm = {param.SchoolTerm}"); //学期
                }
                if (!param.SchoolGradeClassId.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolGradeClassIdz like '%{param.SchoolGradeClassId}%' "); //年级班级Id
                }
                if (!param.CourseId.IsNullOrZero())
                {
                    strSql.Append($" AND CourseId = {param.CourseId}"); //学科
                }
                if (!param.SourceType.IsNullOrZero())
                {
                    strSql.Append($" AND (SourceType = {param.SourceType} or SourceType = 3) "); //实验来源
                }

                if (!param.BookUserId.IsNullOrZero())
                {
                    strSql.Append($" AND BookUserId = {param.BookUserId}"); //预约人
                }
                if (!param.RecordUserId.IsNullOrZero())
                {
                    strSql.Append($" AND RecordUserId = {param.RecordUserId}"); //登记人
                }
            }
            var list = await this.BaseRepository().FindList<ExperimentBookingEntity>(strSql.ToString(), parameter.ToArray(), pagination);
            return list.ToList();
        }

        #endregion

        #region 提交数据
        public async Task SaveForm(ExperimentBookingEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(ExperimentBookingEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task UpdateForm(ExperimentBookingEntity entity, List<string> fields, Repository db)
        {
            if (fields != null)
            {
                await entity.Modify();
                //fields.Add("BaseModifyTime");
                //fields.Add("BaseModifierId");
            }
            if (db!=null)
            {
                await db.Update(entity, fields);
            }
            else
            {
                await this.BaseRepository().Update(entity, fields);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
               strSql = $" update ex_ExperimentBooking set BaseIsDelete = 1 where Id in ({ids}) OR Pid in ({ids}) ";
            }
            else
            {
               strSql = $"update ex_ExperimentBooking set BaseIsDelete = 1 where id = {ids} OR Pid = {ids} ";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<ExperimentBookingEntity, bool>> ListFilter(ExperimentBookingListParam param)
        {
            var expression = LinqExtensions.True<ExperimentBookingEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (param.OptType == 1)
                {
                    //同一个实验，同一个班级只能预约一次
                    expression = expression.And(t => t.SchoolId == param.SchoolId);
                    expression = expression.And(t => t.SchoolGradeClassId == param.SchoolGradeClassId);
                    expression = expression.And(t => t.ExperimentId == param.ExperimentId);
                    expression = expression.And(t => t.IsMain == 0); //子实验数据
                    expression = expression.And(t => t.Statuz > ExperimentBookStatuzEnum.BookIng.ParseToInt()); //提交预约的才算
                    expression = expression.And(t => t.Statuz != ExperimentBookStatuzEnum.ArrangeNoPass.ParseToInt()); //安排退回的不算
                    if (param.Id > 0)
                    {
                        expression = expression.And(t => t.Id != param.Id);
                    }
                }
                else if (param.OptType == 2)
                {
                    //同一个实验，同一个时间，同一节次只能存在一个预约。
                    expression = expression.And(t => t.SchoolId == param.SchoolId);
                    if (param.Id > 0)
                    {
                        expression = expression.And(t => t.Id != param.Id);
                    }
                    expression = expression.And(t => t.ClassTime == param.ClassTime);
                    expression = expression.And(t => t.SectionId == param.SectionId);
                    if (param.SourceType == SourceTypeEnum.ExperimentPlan.ParseToInt())
                    {
                        // expression = expression.And(t => t.ClassTime == param.ClassTime && t.SectionId == param.SectionId && t.PlanDetailId == param.PlanDetailId);
                        expression = expression.And(t => t.PlanDetailId == param.PlanDetailId);
                    }
                    else
                    {
                        expression = expression.And(t => t.TextbookVersionDetailId == param.TextbookVersionDetailId);
                    }
                    expression = expression.And(t => t.IsMain == 0); //子实验数据
                }
                else if (param.OptType == 5)
                {
                    //相同时间，相同节次，一个班级只能预约一次实验，只需判断主节点数据
                    expression = expression.And(t => t.SchoolId == param.SchoolId);
                    if (param.Id > 0)
                    {
                        expression = expression.And(t => t.Id != param.Id);
                    }
                    expression = expression.And(t => t.ClassTime == param.ClassTime);
                    expression = expression.And(t => t.SectionId == param.SectionId);
                    //expression = expression.And(t => t.SchoolGradeClassId == param.SchoolGradeClassId);
                    expression = expression.And(t => t.SchoolGradeClassIdz.Contains(param.SchoolGradeClassId.ToString()));
                    expression = expression.And(t => t.IsMain == 1); //主节点数据
                }
                else if (param.OptType == 6)
                {
                    //同一地点，同一时间只能有一个预约登记
                    expression = expression.And(t => t.SchoolId == param.SchoolId);
                    if (param.Id > 0)
                    {
                        expression = expression.And(t => t.Id != param.Id);
                    }
                    expression = expression.And(t => t.ClassTime == param.ClassTime);
                    expression = expression.And(t => t.SectionId == param.SectionId);
                    expression = expression.And(t => t.FunRoomId == param.FunRoomId);
                    expression = expression.And(t => t.IsMain == 1); //主节点数据
                }
                else if (param.OptType == 3)
                {
                    if (!string.IsNullOrEmpty(param.Ids))
                    {
                        long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                        expression = expression.And(t => idArr.Contains(t.PlanInfoId));

                        expression = expression.And(t => t.Statuz > 0);
                    }
                }
                else if (param.OptType == 4)
                {
                    if (!string.IsNullOrEmpty(param.Ids))
                    {
                        long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                        expression = expression.And(t => idArr.Contains(t.PlanDetailId));
                    }
                    if (!param.SchoolId.IsNullOrZero())
                    {
                        expression = expression.And(t => t.SchoolId == param.SchoolId);
                    }
                    if (!param.PlanInfoId.IsNullOrZero())
                    {
                        expression = expression.And(t => t.PlanInfoId == param.PlanInfoId);
                    }
                }
                else if (param.OptType == 11)
                {
                    //相同时间，相同节次，一个班级只能有一个预约或登记
                    //验证当前时间是否已存在其它预约，
                    expression = expression.And(t => t.SchoolId == param.SchoolId);
                    expression = expression.And(t => t.ClassTime == param.ClassTime);
                    expression = expression.And(t => t.SectionIndex == param.SectionIndex);
                    expression = expression.And(t => t.SchoolGradeClassId == param.SchoolGradeClassId);
                    expression = expression.And(t => t.IsMain == 0); //子节点数据
                    expression = expression.And(t => t.Pid != param.Pid); //非同一主节点的数据。
                    expression = expression.And(t => t.Statuz > ExperimentBookStatuzEnum.BookIng.ParseToInt()); //提交预约的才算
                    expression = expression.And(t => t.Statuz != ExperimentBookStatuzEnum.ArrangeNoPass.ParseToInt()); //安排退回的不算
                }
                else if (param.OptType == 12)
                {
                    //当前实验室，该时间节次，是否已存在预约
                    //验证当当前实验室是否已存在其它预约，
                    expression = expression.And(t => t.SchoolId == param.SchoolId);
                    expression = expression.And(t => t.FunRoomId == param.FunRoomId);
                    expression = expression.And(t => t.ClassTime == param.ClassTime);
                    expression = expression.And(t => t.SectionIndex == param.SectionIndex);
                    expression = expression.And(t => t.IsMain == 0); //子节点数据
                    expression = expression.And(t => t.Pid != param.Pid); //非同一主节点的数据。
                    expression = expression.And(t => t.Statuz > ExperimentBookStatuzEnum.BookIng.ParseToInt()); //提交预约的才算
                    expression = expression.And(t => t.Statuz != ExperimentBookStatuzEnum.ArrangeNoPass.ParseToInt()); //安排退回的不算
                }
                else if (param.OptType == 13)
                {
                    //预约，验证当前班级是否已预约或者登记过该实验
                    expression = expression.And(t => t.SchoolId == param.SchoolId);
                    expression = expression.And(t => t.ExperimentId == param.ExperimentId);
                    expression = expression.And(t => t.SchoolGradeClassId == param.SchoolGradeClassId);
                    expression = expression.And(t => t.IsMain == 0); //子节点数据
                    expression = expression.And(t => t.Pid != param.Pid); //非同一主节点的数据。
                    expression = expression.And(t => t.Statuz > ExperimentBookStatuzEnum.BookIng.ParseToInt()); //提交预约的才算
                    expression = expression.And(t => t.Statuz != ExperimentBookStatuzEnum.ArrangeNoPass.ParseToInt()); //安排退回的不算
                }
                else if (param.OptType == 14)
                {
                    //预约，验证当前老师该时间是否已存在上课
                    expression = expression.And(t => t.SchoolId == param.SchoolId);
                    expression = expression.And(t => t.BaseCreatorId == param.BookUserId);
                    expression = expression.And(t => t.ClassTime == param.ClassTime);
                    expression = expression.And(t => t.SectionIndex == param.SectionIndex);
                    expression = expression.And(t => t.IsMain == 0); //子节点数据
                    expression = expression.And(t => t.Pid != param.Pid); //非同一主节点的数据。
                    expression = expression.And(t => t.Statuz > ExperimentBookStatuzEnum.BookIng.ParseToInt()); //提交预约的才算
                    expression = expression.And(t => t.Statuz != ExperimentBookStatuzEnum.ArrangeNoPass.ParseToInt()); //安排退回的不算
                }
                else
                {
                    if (!string.IsNullOrEmpty(param.Ids))
                    {
                        long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                        expression = expression.And(t => idArr.Contains(t.Id.Value));
                    }
                    if (!param.SchoolId.IsNullOrZero())
                    {
                        expression = expression.And(t => t.SchoolId == param.SchoolId);
                    }
                    if ( param.SchoolGradeClassIds!=null && param.SchoolGradeClassIds.Count >0)
                    {
                        expression = expression.And(t => param.SchoolGradeClassIds.Contains(t.SchoolGradeClassId.ToString()));
                    }
                    if (!param.ArrangerUserId.IsNullOrZero())
                    {
                        expression = expression.And(t => t.ArrangerId == param.ArrangerUserId);
                    }
                    if (!param.SourceType.IsNullOrZero())
                    {
                        expression = expression.And(t => t.SourceType == param.SourceType);
                    }
                    if (param.ExperimentId > 0)
                    {
                        expression = expression.And(t => t.ExperimentId == param.ExperimentId);
                    }
                    if (param.ExperimentIdList!=null && param.ExperimentIdList.Count > 0)
                    {
                        expression = expression.And(t => param.ExperimentIdList.Contains(t.ExperimentId.Value));
                    }
                    if (!param.PlanDetailId.IsNullOrZero())
                    {
                        expression = expression.And(t => t.PlanDetailId == param.PlanDetailId);
                    }
                    if (!param.PlanInfoId.IsNullOrZero())
                    {
                        expression = expression.And(t => t.PlanInfoId == param.PlanInfoId);
                    }
                    if (!param.TextbookVersionDetailId.IsNullOrZero())
                    {
                        expression = expression.And(t => t.TextbookVersionDetailId == param.TextbookVersionDetailId);
                    }
                    if (param.IsMain.HasValue)
                    {
                        expression = expression.And(t => t.IsMain == param.IsMain);
                    }
                    if (!param.Pid.IsNullOrZero())
                    {
                        expression = expression.And(t => t.Pid == param.Pid);
                    }
                    if (param.Pids!=null&& param.Pids.Count > 0)
                    {
                        expression = expression.And(t => param.Pids.Contains(t.Pid ?? 0));
                    }
                    if (param.SchoolGradeClassId > 0)
                    {
                        expression = expression.And(t => t.SchoolGradeClassId == param.SchoolGradeClassId);
                    }
                    if (!string.IsNullOrEmpty(param.SchoolExperimentIds))
                    {
                        long[] idArr = TextHelper.SplitToArray<long>(param.SchoolExperimentIds, ',');
                        expression = expression.And(t => idArr.Contains(t.SchoolExperimentId.Value));
                    }
                    if (param.FunRoomId > 0)
                    {
                        expression = expression.And(t => t.FunRoomId == param.FunRoomId);
                    }
                    if (param.StartDate != null)
                    {
                        expression = expression.And(t => t.ClassTime >= param.StartDate);
                    }
                    if (param.EndDate != null)
                    {
                        expression = expression.And(t => t.ClassTime < param.EndDate);
                    }
                    if (param.ClassTime!=null && param.ClassTime> GlobalConstant.DefaultTime)
                    {
                        expression = expression.And(t => t.ClassTime == ((DateTime)param.ClassTime).Date);
                    }
                    if (param.OptType == 21)
                    {
                        expression = expression.And(t => t.Statuz == ExperimentBookStatuzEnum.BookIng.ParseToInt() || t.Statuz == ExperimentBookStatuzEnum.ArrangeNoPass.ParseToInt());
                    }
                    else
                    {
                        if (param.ThanStatuz > -1)
                        {
                            expression = expression.And(t => t.Statuz > param.ThanStatuz);
                        }
                        if (param.Statuz.HasValue)
                        {
                            expression = expression.And(t => t.Statuz == param.Statuz);
                        }
                    }
                    if (param.BookUserId > 0)
                    {
                        expression = expression.And(t => t.BaseCreatorId == param.BookUserId);
                    }
                    if (param.RecordMode > 0)
                    {
                        expression = expression.And(t => t.RecordMode == param.RecordMode);
                    }
                    if (param.StaticSchoolYearStart > 0)
                    {
                        expression = expression.And(t => t.StaticSchoolYearStart == param.StaticSchoolYearStart);
                    }
                    if (param.StaticSchoolTerm > 0)
                    {
                        expression = expression.And(t => t.StaticSchoolTerm == param.StaticSchoolTerm);
                    }
                    if (param.StaticGradeId > 0)
                    {
                        expression = expression.And(t => t.StaticGradeId == param.StaticGradeId);
                    }
                    if (param.ExperimentPublishId > 0)
                    {
                        expression = expression.And(t => t.ExperimentPublishId == param.ExperimentPublishId);
                    }
                }

                if (param.ActivityType != -10000)
                {
                    expression = expression.And(t => t.ActivityType == param.ActivityType);
                }
            }
            return expression;
        }
        #endregion

        #region 获取预约实验slq语句方法


        public async Task<List<ExperimentBookingEntity>> GetAllPageList(ExperimentBookingListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append($@"  SELECT * From (
                SELECT b1.*
				,dicCourse.DicName AS CourseName
                ,b1.SectionIndex as RowIndex
                ,b1.ClassName as ClassDesc
				,dicGrade.DicName AS GradeName
                ,fr4.Name AS FunRoomName
                ,fr4.SafeguardUserId
                ,b1.BaseCreatorId as BookUserId
                ,su.RealName
                ,su.RealName AS BookUserName
				,CASE WHEN b1.ArrangerId > 0 THEN userArranger.RealName ELSE userSafegured.RealName END AS ArrangerName
                ,er.ExperimentSummary ,er.RunStatuz ,er.ProblemDesc ,er.Id AS ExperimentRecordId
                FROM  ex_ExperimentBooking	 AS b1
                LEFT JOIN  SysUser AS su ON b1.BaseCreatorId = su.Id
                LEFT JOIN  SysUser AS userArranger ON b1.ArrangerId = userArranger.Id
				LEFT JOIN  sys_static_dictionary AS dicCourse ON dicCourse.BaseIsDelete = 0 AND b1.CourseId = dicCourse.DictionaryId
				LEFT JOIN  sys_static_dictionary AS dicGrade ON dicGrade.BaseIsDelete = 0 AND b1.GradeId = dicGrade.DictionaryId
                LEFT JOIN  bn_FunRoom AS fr4 ON b1.FunRoomId = fr4.Id
	            LEFT JOIN  SysUser AS userSafegured ON fr4.SafeguardUserId = userSafegured.Id
		        LEFT JOIN  ex_ExperimentRecord AS er ON er.BaseIsDelete= 0 AND b1.Id = er.ExperimentBookingId
            ) as tb1 WHERE   1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = 0 ");
                if (param.SchoolId > 0)
                {
                    strSql.Append(" AND SchoolId = @SchoolId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
                }
                if (param.SafeguardUserId > 0)
                {
                    strSql.Append(" AND (SafeguardUserId = @SafeguardUserId OR ArrangerId = @SafeguardUserId) ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SafeguardUserId", param.SafeguardUserId));
                }
                if (!param.BookUserId.IsNullOrZero())
                {
                    strSql.Append($" AND BookUserId = {param.BookUserId} "); //功能室维护人或预约指定的安排人
                }
                if (param.Id > 0)
                {
                    strSql.Append(" AND Id = @Id ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Id", param.Id));
                }
                if (param.Pid > 0)
                {
                    strSql.Append(" AND Pid = @Pid ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Pid", param.Pid));
                }
                if (param.IsMain>-1)
                {
                    strSql.Append(" AND IsMain = @IsMain ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@IsMain", param.IsMain));
                }
                if (param.Pids != null && param.Pids.Count > 0)
                {
                    strSql.Append($" AND Pid in ({string.Join(",", param.Pids)}) "); //年级班级Id
                }
                if (!param.ExperimentName.IsEmpty())
                {
                    strSql.Append($" AND ExperimentName like '%{param.ExperimentName}%'"); //实验名称
                }
                if (!param.ExperimentType.IsNullOrZero())
                {
                    strSql.Append($" AND ExperimentType = {param.ExperimentType} "); //实验类型
                }
                if (param.IsNeedDo > 0)
                {
                    strSql.Append($" AND IsNeedDo = {param.IsNeedDo} "); //实验要求实验
                }
                if (param.Statuz.HasValue && param.Statuz > -1)
                {
                    strSql.Append($" AND Statuz = {param.Statuz}"); //状态
                }
                if (param.ThanStatuz.HasValue && param.ThanStatuz > -1)
                {
                    strSql.Append($" AND Statuz > {param.ThanStatuz}"); //大于状态
                }
                if (!param.RecordMode.IsNullOrZero())
                {
                    strSql.Append($" AND RecordMode = {param.RecordMode}"); //记录模式
                }
                if (param.SchoolStageId > 0)
                {
                    strSql.Append($" AND SchoolStage = {param.SchoolStageId}"); //学段
                }
                if (!param.SchoolYearStart.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolYearStart = {param.SchoolYearStart}"); //开始学年
                }
                if (!param.SchoolTerm.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolTerm = {param.SchoolTerm}"); //学期
                }
                if (!param.CourseId.IsNullOrZero())
                {
                    strSql.Append($" AND CourseId = {param.CourseId}"); //学科
                }
                if (param.SourceType > 0)
                {
                    strSql.Append($" AND SourceType = {param.SourceType}"); //来源
                }
                if (param.SchoolGradeClassId > 0)
                {
                    strSql.Append($" AND SchoolGradeClassId = {param.SchoolGradeClassId}"); //班级
                }
                if (param.ActivityType != -10000)
                {
                    strSql.Append($" AND ActivityType = {param.ActivityType}"); //课程类型
                }
            }
            IEnumerable<ExperimentBookingEntity> list = null;
            if (pagination == null)
            {
                list = await this.BaseRepository().FindList<ExperimentBookingEntity>(strSql.ToString(), parameter.ToArray());
            }
            else
            {
                list = await this.BaseRepository().FindList<ExperimentBookingEntity>(strSql.ToString(), parameter.ToArray(), pagination);
            }
            return list.ToList();
        }

        public async Task<List<ExperimentBookingEntity>> GetStaticPageList(ExperimentBookingListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append($@"  SELECT * From (
                SELECT b1.*
				,dicCourse.DicName AS CourseName
                ,b1.SectionIndex as RowIndex
                ,b1.ClassName as ClassDesc
				,dicGrade.DicName AS GradeName
                ,fr4.Name AS FunRoomName
                ,fr4.SafeguardUserId
                ,b1.BaseCreatorId as BookUserId
                ,su.RealName
                ,su.RealName AS BookUserName
				,CASE WHEN b1.ArrangerId > 0 THEN userArranger.RealName ELSE userSafegured.RealName END AS ArrangerName
                ,er.ExperimentSummary ,er.RunStatuz ,er.ProblemDesc ,er.Id AS ExperimentRecordId
                ,ur1.UnitId AS CountyId                
                FROM  ex_ExperimentBooking	 AS b1
                INNER JOIN up_UnitRelation AS ur1 ON ur1.BaseIsDelete = 0 AND b1.SchoolId = ur1.ExtensionObjId AND ur1.ExtensionType = 3 
                LEFT JOIN  SysUser AS su ON b1.BaseCreatorId = su.Id
                LEFT JOIN  SysUser AS userArranger ON b1.ArrangerId = userArranger.Id
				LEFT JOIN  sys_static_dictionary AS dicCourse ON dicCourse.BaseIsDelete = 0 AND b1.CourseId = dicCourse.DictionaryId
				LEFT JOIN  sys_static_dictionary AS dicGrade ON dicGrade.BaseIsDelete = 0 AND b1.GradeId = dicGrade.DictionaryId
                LEFT JOIN  bn_FunRoom AS fr4 ON b1.FunRoomId = fr4.Id
	            LEFT JOIN  SysUser AS userSafegured ON fr4.SafeguardUserId = userSafegured.Id
		        LEFT JOIN  ex_ExperimentRecord AS er ON er.BaseIsDelete= 0 AND b1.Id = er.ExperimentBookingId
            ) as tb1 WHERE   1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = 0 ");
                if (param.SchoolId > 0)
                {
                    strSql.Append(" AND SchoolId = @SchoolId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
                }
                if (param.CountyId > 0)
                {
                    strSql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
                if (param.SafeguardUserId > 0)
                {
                    strSql.Append(" AND (SafeguardUserId = @SafeguardUserId OR ArrangerId = @SafeguardUserId) ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SafeguardUserId", param.SafeguardUserId));
                }
                if (!param.BookUserId.IsNullOrZero())
                {
                    strSql.Append($" AND BookUserId = {param.BookUserId} "); //功能室维护人或预约指定的安排人
                }
                if (param.Id > 0)
                {
                    strSql.Append(" AND Id = @Id ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Id", param.Id));
                }
                if (param.Pid > 0)
                {
                    strSql.Append(" AND Pid = @Pid ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@Pid", param.Pid));
                }
                if (param.IsMain > -1)
                {
                    strSql.Append(" AND IsMain = @IsMain ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@IsMain", param.IsMain));
                }
                if (param.Pids != null && param.Pids.Count > 0)
                {
                    strSql.Append($" AND Pid in ({string.Join(",", param.Pids)}) "); //年级班级Id
                }
                if (!param.ExperimentName.IsEmpty())
                {
                    strSql.Append($" AND ExperimentName like '%{param.ExperimentName}%'"); //实验名称
                }
                if (!param.ExperimentType.IsNullOrZero())
                {
                    strSql.Append($" AND ExperimentType = {param.ExperimentType} "); //实验类型
                }
                if (param.IsNeedDo > 0)
                {
                    strSql.Append($" AND IsNeedDo = {param.IsNeedDo} "); //实验要求实验
                }
                if (param.Statuz.HasValue && param.Statuz > -1)
                {
                    strSql.Append($" AND Statuz = {param.Statuz}"); //状态
                }
                if (param.ThanStatuz.HasValue && param.ThanStatuz > -1)
                {
                    strSql.Append($" AND Statuz > {param.ThanStatuz}"); //大于状态
                }
                if (!param.RecordMode.IsNullOrZero())
                {
                    strSql.Append($" AND RecordMode = {param.RecordMode}"); //记录模式
                }
                if (param.SchoolStageId > 0)
                {
                    strSql.Append($" AND SchoolStage = {param.SchoolStageId}"); //学段
                }
                if (!param.SchoolYearStart.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolYearStart = {param.SchoolYearStart}"); //开始学年
                }
                if (!param.SchoolTerm.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolTerm = {param.SchoolTerm}"); //学期
                }
                if (!param.CourseId.IsNullOrZero())
                {
                    strSql.Append($" AND CourseId = {param.CourseId}"); //学科
                }
                if (param.SourceType > 0)
                {
                    strSql.Append($" AND SourceType = {param.SourceType}"); //来源
                }
                if (param.GradeId > 0)
                {
                    strSql.Append($" AND GradeId = {param.GradeId}"); //班级
                }
                if (param.ActivityType != -10000)
                {
                    strSql.Append($" AND ActivityType = {param.ActivityType}"); //课程类型
                }
                if (param.IsEvaluate > -1)
                {
                    strSql.Append($" AND IsExamine = {param.IsEvaluate}"); //状态
                }
                if (param.SchoolGradeClassId > 0)
                {
                    strSql.Append($" AND SchoolGradeClassId = {param.SchoolGradeClassId} "); //状态
                }
                if (param.ClassTimele!=null)
                {
                    strSql.Append($" AND CONVERT(DATE, ClassTime)  <= '{((DateTime)param.ClassTimele).ToString("yyyy-MM-dd")}' "); //上课时间
                }
            }
            IEnumerable<ExperimentBookingEntity> list = null;
            if (pagination != null)
            {
                list = await this.BaseRepository().FindList<ExperimentBookingEntity>(strSql.ToString(), parameter.ToArray(), pagination);
            }
            else
            {
                list = await this.BaseRepository().FindList<ExperimentBookingEntity>(strSql.ToString(), parameter.ToArray());

            }
            return list.ToList();
        }
        #endregion

        #region 开出统计，优化语句


        /// <summary>
        /// 实验预约查询
        /// </summary>
        /// <param name="param">查询参数</param>
        /// <param name="pagination">分页参数</param>
        /// <returns></returns>
        public async Task<List<ExperimentBookingEntity>> GetStatisticsPageList(ExperimentBookingListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            var parameter = new List<DbParameter>();

            strSql.Append(@$" SELECT * ,
                        CASE WHEN IsNeedDo = 1 THEN '{IsNeedEnum.MustDo.GetDescription()}' ELSE '{IsNeedEnum.SelectToDo.GetDescription()}' END AS IsNeedDoName,
                        CASE WHEN ExperimentType = 1 THEN '演示' WHEN ExperimentType = 2 THEN '分组' ELSE '演示/分组' END AS ExperimentTypeName,
                        CASE WHEN StaticSchoolTerm = 1 THEN stuff((convert(varchar,RIGHT(StaticSchoolYearStart,2))+'~'+(convert(varchar,RIGHT(StaticSchoolYearEnd,2))))+ '上学期',1,0,'')
                        ELSE stuff((convert(varchar,RIGHT(StaticSchoolYearStart,2))+'~'+(convert(varchar,RIGHT(StaticSchoolYearEnd,2))))+ '下学期',1,0,'') END AS SchoolTermName
                        From (
                               SELECT  UR2.UnitId AS CityId ,sa.AreaName AS CountyName ,EB.Id ,EB.BaseCreateTime ,EB.BaseModifyTime ,
                                       EB.StaticSchoolYearStart ,EB.StaticSchoolYearEnd ,EB.StaticSchoolTerm ,EB.SchoolId ,
                                       EB.SchoolGradeClassId ,EB.CourseId ,EB.SourceType ,EB.PlanInfoId ,EB.PlanDetailId ,
                                       EB.TextbookVersionDetailId ,EB.ExperimentName ,EB.Groupz ,
                                       EB.ClassTime ,EB.SectionId ,EB.SectionShow ,EB.SectionIndex,EB.FunRoomId ,EB.EquipmentNeed ,
                                       EB.MaterialNeed ,EB.Remark ,EB.Statuz ,EB.ArrangerId ,EB.ArrangerTime ,EB.RecordMode ,EB.ArrangerRemark ,
                                       EB.ArrangerEquipmentNeed ,EB.ArrangerMaterialNeed ,
                                       D2.DicName AS CourseName ,ISNULL(FR.Name,'普通教室') AS FunRoomName ,
                                       EB.SchoolStage ,D13.DicName AS SchoolStageName ,
                                       D1.DicName AS GradeName ,
                                       EB.BaseCreatorId AS BookUserId ,U.RealName AS BookUserName ,
                                       EB.BaseCreatorId AS RecordUserId ,U.RealName AS RecordUserName ,FR.SafeguardUserId ,
                                       UR.UnitId AS CountyId ,UT.Name AS SchoolName ,UT.Sort ,
                                       TD.IsNeedDo ,TD.ExperimentType ,TD.IsEvaluate ,TD.IsBase ,
                                       EB.IsMain ,EB.Pid ,EB.SchoolGradeClassIdz ,EB.ClassName ,ISNULL(EB.SchoolExperimentId ,0) AS SchoolExperimentId
                                FROM  ex_ExperimentBooking AS EB
                                INNER JOIN ex_TextbookVersionDetail AS TD ON TD.BaseIsDelete = 0 AND EB.ExperimentId = TD.Id
                                INNER JOIN ex_TextbookVersionBase AS vb2 ON vb2.BaseIsDelete = 0 AND TD.TextbookVersionBaseId = vb2.Id
                                INNER JOIN ex_TextbookVersionCurrent AS VC ON VC.BaseIsDelete = 0 AND vb2.Id = VC.TextbookVersionBaseId
                                INNER JOIN  sys_static_dictionary AS D13 ON EB.SchoolStage = D13.DictionaryId AND D13.TypeCode = '{DicTypeCodeEnum.SchoolStage.ParseToInt()}'
                                LEFT JOIN  sys_static_dictionary AS D1 ON EB.StaticGradeId = D1.DictionaryId AND D1.TypeCode = '{DicTypeCodeEnum.Grade.ParseToInt()}'
                                INNER JOIN  sys_static_dictionary AS D2 ON EB.CourseId = D2.DictionaryId AND D2.TypeCode = '{DicTypeCodeEnum.Course.ParseToInt()}'
                                INNER JOIN  SysUser AS U ON EB.BaseCreatorId = U.Id
                                INNER JOIN  up_UnitRelation AS UR ON UR.ExtensionObjId = EB.SchoolId AND UR.ExtensionType = 3 AND UR.BaseIsDelete = 0
                                INNER JOIN  up_Unit AS UT ON EB.SchoolId = UT.Id
                                INNER JOIN  up_UnitRelation AS UR2 ON UR.UnitId = UR2.ExtensionObjId AND UR2.ExtensionType = 3 AND UR2.BaseIsDelete = 0
                                INNER JOIN  up_Unit AS un2 ON ur.UnitId = un2.Id AND un2.BaseIsDelete = 0
                                LEFT JOIN  SysArea AS sa ON sa.AreaCode = un2.AreaId AND sa.BaseIsDelete = 0
                                LEFT JOIN  bn_FunRoom AS FR ON EB.FunRoomId = FR.Id
                                WHERE EB.BaseIsDelete = 0
                  ) as T WHERE  1 = 1 ");
            if (param != null)
            {
                if (!param.Id.IsNullOrZero())
                {
                    strSql.Append($" AND Id = {param.Id}");
                }
                if (!param.SchoolId.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolId = {param.SchoolId}");//根据用户登录的单位ID进行查询
                }
                if (!param.CountyId.IsNullOrZero())
                {
                    strSql.Append($" AND CountyId = {param.CountyId}");
                    if (param.SchoolId > 0)
                    {
                        strSql.Append($" AND SchoolId = {param.SchoolId}");
                    }
                }
                if (!param.CityId.IsNullOrZero())
                {
                    strSql.Append($" AND CityId = {param.CityId}");
                    if (param.CountyId > 0)
                    {
                        strSql.Append($" AND CountyId = {param.CountyId}");
                    }
                    if (param.SchoolId > 0)
                    {
                        strSql.Append($" AND SchoolId = {param.SchoolId}");
                    }
                }
                if (param.SchoolStageId > 0)
                {
                    strSql.Append($" AND SchoolStage = {param.SchoolStageId}"); //学段
                }
                if (!param.StaticSchoolYearStart.IsNullOrZero())
                {
                    strSql.Append($" AND StaticSchoolYearStart = {param.StaticSchoolYearStart}"); //开始学年
                }
                if (!param.StaticSchoolTerm.IsNullOrZero())
                {
                    strSql.Append($" AND StaticSchoolTerm = {param.StaticSchoolTerm}"); //学期
                }
                if (!param.SchoolGradeClassId.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolGradeClassIdz like '%{param.SchoolGradeClassId}%' "); //年级班级Id
                }
                if (!param.CourseId.IsNullOrZero())
                {
                    strSql.Append($" AND CourseId = {param.CourseId}"); //学科
                }
                if (!param.SourceType.IsNullOrZero())
                {
                    strSql.Append($" AND (SourceType = {param.SourceType} or SourceType = 3) "); //实验来源
                }
                if (!param.ExperimentType.IsNullOrZero())
                {
                    strSql.Append($" AND (ExperimentType = {param.ExperimentType} or ExperimentType = 3)"); //实验类型
                }
                if (!param.BookUserId.IsNullOrZero())
                {
                    strSql.Append($" AND BookUserId = {param.BookUserId}"); //预约人
                }
                if (!param.RecordUserId.IsNullOrZero())
                {
                    strSql.Append($" AND RecordUserId = {param.RecordUserId}"); //登记人
                }
                if (param.Statuz.HasValue && param.Statuz > -1)
                {
                    strSql.Append($" AND Statuz = {param.Statuz}"); //状态
                }
                if (param.ThanStatuz.HasValue && param.ThanStatuz > -1)
                {
                    strSql.Append($" AND Statuz > {param.ThanStatuz}"); //大于状态
                }
                if (!param.RecordMode.IsNullOrZero())
                {
                    strSql.Append($" AND RecordMode = {param.RecordMode}"); //记录模式
                }
                if (param.ListType == 1)
                {
                    if (!param.SafeguardUserId.IsNullOrZero())
                    {
                        strSql.Append($" AND (SafeguardUserId = {param.SafeguardUserId} OR ArrangerId = {param.SafeguardUserId})"); //功能室维护人或预约指定的安排人
                    }
                }
                else
                {
                    if (!param.SafeguardUserId.IsNullOrZero())
                    {
                        strSql.Append($" AND SafeguardUserId = {param.SafeguardUserId}"); //功能室维护人
                    }
                }
                if (!param.ExperimentName.IsEmpty())
                {
                    strSql.Append($" AND ExperimentName like '%{param.ExperimentName}%'"); //实验名称
                }

                if (param.IsNeedDo > 0)
                {
                    strSql.Append($" AND IsNeedDo = {param.IsNeedDo} "); //实验要求实验
                }
                if (param.IsMain.HasValue)
                {
                    strSql.Append($" AND IsMain = {param.IsMain}"); //父级Id
                }
                if (!param.Pid.IsNullOrZero())
                {
                    strSql.Append($" AND Pid = {param.Pid}"); //父级Id
                }

                if (param.SetUserId > 0)
                {

                    //判断用户是否设置了学段、学科
                    var listStage = userSubjectSetService.GetUserSetStageList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listStage.Count > 0)
                    {
                        strSql.Append($" AND ({string.Join(" OR ", listStage.Select(f => string.Format(" SchoolStage = {0} ", f.DictionaryId)))})");
                    }

                    var listCourse = userSubjectSetService.GetUserSetCourseList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listCourse.Count > 0)
                    {
                        strSql.Append($" AND ({string.Join(" OR ", listCourse.Select(f => string.Format(" CourseId = {0} ", f.DictionaryId)))})");
                    }

                }
            }
            var list = await this.BaseRepository().FindList<ExperimentBookingEntity>(strSql.ToString(), parameter.ToArray(), pagination);
            return list.ToList();
        }


        #endregion

        #region 获取实验记录的学年列表


        /// <summary>
        /// 获取实验记录的学年列表
        /// </summary>
        /// <param name="param">查询参数</param>
        /// <returns></returns>
        public async Task<List<ExperimentBookingEntity>> GetSchoolTermList(ExperimentBookingListParam param)
        {
            StringBuilder strSql = new StringBuilder();
            var parameter = new List<DbParameter>();

            strSql.Append(@$" SELECT StaticSchoolYearStart ,StaticSchoolYearEnd, StaticSchoolTerm ,StaticGradeId  From (
                               SELECT
                                       EB.Id ,EB.BaseCreateTime ,EB.BaseModifyTime ,EB.CourseId ,  EB.SchoolStage ,
                                       EB.StaticSchoolYearStart ,EB.StaticSchoolYearEnd ,EB.StaticSchoolTerm , EB.StaticGradeId,
                                       EB.SchoolId ,  UR.UnitId AS CountyId ,UT.Name AS SchoolName ,UT.Sort ,
                                       UR2.UnitId AS CityId ,sa.AreaName AS CountyName
                                FROM  ex_ExperimentBooking AS EB
                                INNER JOIN  up_UnitRelation AS UR ON UR.ExtensionObjId = EB.SchoolId AND UR.ExtensionType = 3 AND UR.BaseIsDelete = 0
                                INNER JOIN  up_Unit AS UT ON EB.SchoolId = UT.Id
                                INNER JOIN  up_UnitRelation AS UR2 ON UR.UnitId = UR2.ExtensionObjId AND UR2.ExtensionType = 3 AND UR2.BaseIsDelete = 0
                                INNER JOIN  up_Unit AS un2 ON ur.UnitId = un2.Id AND un2.BaseIsDelete = 0
                                LEFT JOIN  SysArea AS sa ON sa.AreaCode = un2.AreaId AND sa.BaseIsDelete = 0
                                WHERE EB.BaseIsDelete = 0 AND EB.IsMain = 0 AND EB.Statuz > 0
                  ) as T WHERE  1 = 1 ");
            if (param != null)
            {
                if (!param.SchoolId.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolId = {param.SchoolId}");//根据用户登录的单位ID进行查询
                }
                if (!param.CountyId.IsNullOrZero())
                {
                    strSql.Append($" AND CountyId = {param.CountyId}");
                    if (param.SchoolId > 0)
                    {
                        strSql.Append($" AND SchoolId = {param.SchoolId}");
                    }
                }
                if (!param.CityId.IsNullOrZero())
                {
                    strSql.Append($" AND CityId = {param.CityId}");
                    if (param.CountyId > 0)
                    {
                        strSql.Append($" AND CountyId = {param.CountyId}");
                    }
                    if (param.SchoolId > 0)
                    {
                        strSql.Append($" AND SchoolId = {param.SchoolId}");
                    }
                }
                if (param.SchoolStageId > 0)
                {
                    strSql.Append($" AND SchoolStage = {param.SchoolStageId}"); //学段
                }
                if (!param.StaticSchoolYearStart.IsNullOrZero())
                {
                    strSql.Append($" AND StaticSchoolYearStart = {param.StaticSchoolYearStart}"); //开始学年
                }
                if (!param.StaticSchoolTerm.IsNullOrZero())
                {
                    strSql.Append($" AND StaticSchoolTerm = {param.StaticSchoolTerm}"); //学期
                }

                if (!param.CourseId.IsNullOrZero())
                {
                    strSql.Append($" AND CourseId = {param.CourseId}"); //学科
                }
            }
            strSql.Append(" GROUP BY StaticSchoolYearStart ,StaticSchoolYearEnd, StaticSchoolTerm ,StaticGradeId ");
            var list = await this.BaseRepository().FindList<ExperimentBookingEntity>(strSql.ToString(), parameter.ToArray());
            return list.ToList();
        }
        #endregion

        #region 统计查询数据

        /// <summary>
        /// 统计查询预约表相关数据
        /// </summary>
        /// <param name="param">查询参数</param>
        /// <returns></returns>
        public async Task<List<ExperimentBookingStaticModel>> GetPlanAll(ExperimentBookingListParam param)
        {
            StringBuilder strSql = new StringBuilder();

            string tableTextbookVersionSql = @" LEFT JOIN ex_TextbookVersionDetail AS TD ON TD.BaseIsDelete = 0 AND pd3.ExperimentId = TD.Id
                LEFT JOIN ex_TextbookVersionBase AS vb2 ON vb2.BaseIsDelete = 0 AND TD.TextbookVersionBaseId = vb2.Id
                LEFT JOIN ex_TextbookVersionCurrent AS VC ON VC.BaseIsDelete = 0 AND vb2.Id = VC.TextbookVersionBaseId
";
            if (param.IsCurrentSchoolYear == 2)
            {
                tableTextbookVersionSql = @" LEFT JOIN ex_TextbookVersionDetailHistory AS TD ON TD.BaseIsDelete = 0 AND pd3.ExperimentId = TD.TextbookVersionDetailId
                LEFT JOIN ex_TextbookVersionBaseHistory AS vb2 ON vb2.BaseIsDelete = 0 AND TD.TextbookVersionBaseId = vb2.TextbookVersionBaseId
                LEFT JOIN ex_TextbookVersionCurrentHistory AS VC ON VC.BaseIsDelete = 0 AND vb2.TextbookVersionBaseId = VC.TextbookVersionBaseId ";
            }
            strSql.Append(@$" SELECT *  From (
                SELECT   EB.SchoolId ,
                EB.SchoolGradeClassId ,
                EB.CourseId ,
                EB.SchoolStage ,
                EB.SchoolYearStart ,
                EB.SchoolYearEnd ,
                EB.SchoolTerm ,
                EB.GradeId ,
                CASE WHEN ISNULL(TD.Id , 0) > 0 THEN TD.ExperimentType ELSE EB.ExperimentType END AS ExperimentType  ,
	            CASE WHEN ISNULL(TD.Id , 0) > 0 THEN TD.IsNeedDo ELSE EB.IsNeedDo END AS IsNeedDo  ,
                CASE WHEN ISNULL(TD.Id , 0) > 0 THEN TD.IsEvaluate ELSE 0 END AS IsEvaluate  ,
                CASE WHEN ISNULL(TD.Id , 0) > 0 THEN TD.IsBase ELSE 0 END AS IsBase  ,
                EB.BaseModifierId ,
                pi2.CompulsoryType
                FROM ex_ExperimentBooking AS EB
                INNER JOIN ex_PlanInfo AS pi2 ON EB.PlanInfoId = pi2.Id	AND pi2.BaseIsDelete = 0
                INNER JOIN ex_PlanDetail AS pd3 ON pd3.BaseIsDelete = 0  AND pi2.Id = pd3.PlanInfoId AND pd3.BaseIsDelete = 0 AND EB.ExperimentId = pd3.ExperimentId
                {tableTextbookVersionSql}
                WHERE EB.BaseIsDelete = 0 AND EB.Statuz = 100 AND EB.IsMain = 0
            ) as T WHERE  1 = 1 ");
            if (param != null)
            {
                if (!param.SchoolId.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolId = {param.SchoolId}");//根据用户登录的单位ID进行查询
                }

                if (param.SchoolStageId > 0)
                {
                    strSql.Append($" AND SchoolStage = {param.SchoolStageId}"); //学段
                }
                if (!param.SchoolYearStart.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolYearStart = {param.SchoolYearStart}"); //开始学年
                }
                if (!param.SchoolTerm.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolTerm = {param.SchoolTerm}"); //学期
                }
                if (!param.SchoolTerm.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolTerm = {param.SchoolTerm}"); //学期
                }
                if (param.GradeId > 0)
                {
                    strSql.Append($" AND GradeId = {param.GradeId}"); //高中
                }
                if (!param.CourseId.IsNullOrZero())
                {
                    strSql.Append($" AND CourseId = {param.CourseId}"); //学科
                }
                if (param.IsEvaluate > -1)
                {
                    strSql.Append($" AND IsEvaluate = {param.IsEvaluate}"); //是否评估
                }
                if (param.CompulsoryType>0)
                {
                    strSql.Append($" AND CompulsoryType = {param.CompulsoryType}"); //教材版本
                }
            }
            var list = await this.BaseRepository().FindList<ExperimentBookingStaticModel>(strSql.ToString());
            return list.ToList();
        }

        /// <summary>
        /// 统计查询预约表相关数据
        /// </summary>
        /// <param name="param">查询参数</param>
        /// <returns></returns>
        public async Task<List<ExperimentBookingStaticModel>> GetAll(ExperimentBookingListParam param)
        {
            StringBuilder strSql = new StringBuilder();
            string tableTextbookVersionSql = @" LEFT JOIN ex_TextbookVersionDetail AS TD ON TD.BaseIsDelete = 0 AND EB.ExperimentId = TD.Id
                LEFT JOIN ex_TextbookVersionBase AS vb2 ON vb2.BaseIsDelete = 0 AND TD.TextbookVersionBaseId = vb2.Id
                LEFT JOIN ex_TextbookVersionCurrent AS VC ON VC.BaseIsDelete = 0 AND vb2.Id = VC.TextbookVersionBaseId ";
            if (param.IsCurrentSchoolYear == 2)
            {
                tableTextbookVersionSql = @" LEFT JOIN ex_TextbookVersionDetailHistory AS TD ON TD.BaseIsDelete = 0 AND EB.ExperimentId = TD.TextbookVersionDetailId
                LEFT JOIN ex_TextbookVersionBaseHistory AS vb2 ON vb2.BaseIsDelete = 0 AND TD.TextbookVersionBaseId = vb2.TextbookVersionBaseId
                LEFT JOIN ex_TextbookVersionCurrentHistory AS VC ON VC.BaseIsDelete = 0 AND vb2.TextbookVersionBaseId = VC.TextbookVersionBaseId ";
            }
            strSql.Append(@$" SELECT *  From (
                SELECT   EB.SchoolId ,
                  EB.SchoolGradeClassId ,
                  EB.CourseId ,
                  EB.SchoolStage ,
                  EB.SchoolYearStart ,
                  EB.SchoolYearEnd ,
                  EB.SchoolTerm ,
                  EB.GradeId ,
                  CASE WHEN ISNULL(TD.Id , 0) > 0 THEN TD.ExperimentType ELSE EB.ExperimentType END AS ExperimentType  ,
	              CASE WHEN ISNULL(TD.Id , 0) > 0 THEN TD.IsNeedDo ELSE EB.IsNeedDo END AS IsNeedDo  ,
                  CASE WHEN ISNULL(TD.Id , 0) > 0 THEN TD.IsEvaluate ELSE 0 END AS IsEvaluate  ,
                  CASE WHEN ISNULL(TD.Id , 0) > 0 THEN TD.IsBase ELSE 0 END AS IsBase  ,
                  EB.BaseModifierId ,
                  EB.ActivityType
                FROM ex_ExperimentBooking AS EB
                {tableTextbookVersionSql}
                WHERE EB.BaseIsDelete = 0 AND EB.Statuz = 100 AND EB.IsMain = 0
            ) as T WHERE  1 = 1 ");
            if (param != null)
            {
                if (!param.SchoolId.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolId = {param.SchoolId}");//根据用户登录的单位ID进行查询
                }
                else if (param.SchoolIds != null && param.SchoolIds.Count > 0)
                {
                    strSql.Append($" AND  ({string.Join(" OR ", param.SchoolIds.Select(m => string.Format(" SchoolId = {0} ", m)))}) ");//根据用户登录的单位ID进行查询
                }
                if (param.SchoolStageId > 0)
                {
                    strSql.Append($" AND SchoolStage = {param.SchoolStageId}"); //学段
                }
                if (!param.SchoolYearStart.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolYearStart = {param.SchoolYearStart}"); //开始学年
                }
                if (!param.SchoolTerm.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolTerm = {param.SchoolTerm}"); //学期
                }
                if (param.GradeId > 0)
                {
                    strSql.Append($" AND GradeId = {param.GradeId}"); //高中
                }
                if (!param.CourseId.IsNullOrZero())
                {
                    strSql.Append($" AND CourseId = {param.CourseId}"); //学科
                }
                if (param.IsEvaluate > -1)
                {
                    strSql.Append($" AND IsEvaluate = {param.IsEvaluate}"); //是否评估
                }
                if (param.ActivityType != -10000)
                {
                    strSql.Append($" AND ActivityType = {param.ActivityType}"); //课程类型
                }
            }
            var list = await this.BaseRepository().FindList<ExperimentBookingStaticModel>(strSql.ToString());
            return list.ToList();
        }

        /// <summary>
        /// 统计查询预约表相关数据
        /// </summary>
        /// <param name="param">查询参数</param>
        /// <returns></returns>
        public async Task<List<ExperimentBookingEntity>> GetDetailPageList(ExperimentBookingListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(@$" SELECT *
                        ,CASE WHEN IsNeedDo = 1 THEN '{IsNeedEnum.MustDo.GetDescription()}' ELSE '{IsNeedEnum.SelectToDo.GetDescription()}' END AS IsNeedDoName
                        ,CASE WHEN ExperimentType = 1 THEN '演示' WHEN ExperimentType = 2 THEN '分组' ELSE '演示/分组' END AS ExperimentTypeName
                        ,CASE WHEN SchoolTerm = 1 THEN stuff((convert(varchar,RIGHT(SchoolYearStart,2))+'~'+(convert(varchar,RIGHT(SchoolYearEnd,2))))+ '上学期',1,0,'')
                        ELSE stuff((convert(varchar,RIGHT(SchoolYearStart,2))+'~'+(convert(varchar,RIGHT(SchoolYearEnd,2))))+ '下学期',1,0,'') END AS SchoolTermName
                From (
                SELECT EB.Id ,  EB.SchoolId ,
                  EB.SchoolGradeClassId ,
				  EB.ClassName ,
                  EB.CourseId ,
                  EB.SchoolStage ,
                  EB.SchoolYearStart ,
                  EB.SchoolYearEnd ,
                  EB.SchoolTerm ,
                  EB.GradeId ,
				  EB.SourceType ,
				  EB.ExperimentName ,
				  EB.Groupz ,
				  EB.ClassTime ,EB.SectionId ,EB.SectionShow ,EB.SectionIndex,EB.FunRoomId ,EB.EquipmentNeed ,
				  EB.ArrangerEquipmentNeed ,EB.ArrangerMaterialNeed ,
				  ISNULL(FR.Name,'普通教室') AS FunRoomName ,
                  TD.ExperimentType ,
                  TD.IsNeedDo ,
                  TD.IsEvaluate ,
                  TD.IsBase ,
                  u5.RealName AS RecordUserName,
                  EB.RecordMode , EB.ActivityType
                FROM ex_ExperimentBooking AS EB
                LEFT JOIN ex_TextbookVersionDetail AS TD ON TD.BaseIsDelete = 0 AND EB.ExperimentId = TD.Id
                LEFT JOIN ex_TextbookVersionBase AS vb2 ON vb2.BaseIsDelete = 0 AND TD.TextbookVersionBaseId = vb2.Id
                LEFT JOIN ex_TextbookVersionCurrent AS VC ON VC.BaseIsDelete = 0 AND vb2.Id = VC.TextbookVersionBaseId  ANd EB.StaticSchoolTerm = VC.SchoolTerm
                LEFT JOIN  bn_FunRoom AS FR ON EB.FunRoomId = FR.Id
                LEFT JOIN SysUser AS u5 ON EB.BaseModifierId = u5.Id
                WHERE EB.BaseIsDelete = 0 AND EB.Statuz = 100 AND EB.IsMain = 0
            ) as T WHERE  1 = 1 ");
            if (param != null)
            {
                if (!param.SchoolId.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolId = {param.SchoolId}");//根据用户登录的单位ID进行查询
                }
                if (param.CountyId > 0)
                {
                    strSql.Append($" AND SchoolId IN (SELECT ExtensionObjId FROM up_UnitRelation WHERE ExtensionType = 3 AND UnitId = {param.CountyId} )");//根据用户登录的单位ID进行查询
                }
                if (param.CityId > 0)
                {
                    strSql.Append(@$" AND SchoolId IN (SELECT ur3.ExtensionObjId FROM up_UnitRelation AS ur3
                                    INNER JOIN up_UnitRelation AS ur4 ON ur3.UnitId = ur4.ExtensionObjId AND ur4.ExtensionType = 3 AND  ur4.BaseIsDelete = 0
                                    WHERE ur3.ExtensionType = 3 AND  ur3.BaseIsDelete = 0 AND ur4.UnitId = {param.CityId})");//根据用户登录的单位ID进行查询
                }
                if (param.SchoolStageId > 0)
                {
                    strSql.Append($" AND SchoolStage = {param.SchoolStageId}"); //学段
                }
                if (param.SchoolGradeClassId>0)
                {
                    strSql.Append($" AND SchoolGradeClassId = {param.SchoolGradeClassId}"); //班级
                }
                if (!param.SchoolYearStart.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolYearStart = {param.SchoolYearStart}"); //开始学年
                }
                if (!param.SchoolTerm.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolTerm = {param.SchoolTerm}"); //学期
                }
                if (param.GradeId > 0)
                {
                    strSql.Append($" AND GradeId = {param.GradeId}"); //高中
                }
                if (!param.CourseId.IsNullOrZero())
                {
                    strSql.Append($" AND CourseId = {param.CourseId}"); //学科
                }
                if (param.IsEvaluate > -1)
                {
                    strSql.Append($" AND IsEvaluate = {param.IsEvaluate}"); //是否评估
                }
                if (param.ExperimentType > 0)
                {
                    strSql.Append($" AND ExperimentType = {param.ExperimentType}"); //学科
                }
                if (param.IsNeedDo > 0)
                {
                    strSql.Append($" AND IsNeedDo = {param.IsNeedDo}"); //学科
                }
                if (param.ExperimentName!=null && param.ExperimentName.Length > 0)
                {
                    strSql.Append($" AND (ExperimentName like '%{param.ExperimentName}%' OR ClassName like '%{param.ExperimentName}%') "); //学科
                }
                if (param.RecordUserName != null && param.RecordUserName.Length > 0)
                {
                    strSql.Append($" AND (RecordUserName like '%{param.RecordUserName}%') "); //学科
                }
                if (param.SetUserId > 0)
                {
                    //判断用户是否设置了学段、学科
                    var listStage = userSubjectSetService.GetUserSetStageList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listStage.Count > 0)
                    {
                        strSql.Append($" AND ({string.Join(" OR ", listStage.Select(f => string.Format(" SchoolStage = {0} ", f.DictionaryId)))})");
                    }

                    var listCourse = userSubjectSetService.GetUserSetCourseList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listCourse.Count > 0)
                    {
                        strSql.Append($" AND ({string.Join(" OR ", listCourse.Select(f => string.Format(" CourseId = {0} ", f.DictionaryId)))})");
                    }

                }
                if (param.ActivityType != -10000)
                {
                    strSql.Append($" AND ActivityType = {param.ActivityType}"); //课程类型
                }
            }
            var parameter = new List<DbParameter>();
            var list = await this.BaseRepository().FindList<ExperimentBookingEntity>(strSql.ToString(), parameter.ToArray(), pagination);
            return list.ToList();
        }

        /// <summary>
        /// 统计查询预约表相关数据
        /// </summary>
        /// <param name="param">查询参数</param>
        /// <returns></returns>
        public async Task<List<ExperimentBookingStaticModel>> GetXiaoBenAll(ExperimentBookingListParam param)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(@$" SELECT *  From (
                SELECT   EB.SchoolId ,
                  EB.CourseId ,
                  EB.SchoolStage ,
                  EB.StaticSchoolYearStart ,
                  EB.StaticSchoolYearEnd ,
                  EB.StaticSchoolTerm ,
                  EB.StaticGradeId ,
                  SE.ExperimentType ,
                  SE.IsNeedDo ,
                  1 as IsEvaluate  ,
                  1 as IsBase
              FROM ex_ExperimentBooking AS EB
              INNER JOIN ex_SchoolExperiment AS SE ON EB.SourcePath = 2 AND EB.ExperimentId = SE.Id AND SE.BaseIsDelete = 0 AND SE.Statuz = 1
		      WHERE EB.BaseIsDelete = 0 AND EB.Statuz = 100 AND EB.IsMain = 0
             ) as T WHERE  1 = 1 ");
            if (param != null)
            {
                if (!param.SchoolId.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolId = {param.SchoolId}");//根据用户登录的单位ID进行查询
                }

                if (param.SchoolStageId > 0)
                {
                    strSql.Append($" AND SchoolStage = {param.SchoolStageId}"); //学段
                }
                if (!param.StaticSchoolYearStart.IsNullOrZero())
                {
                    strSql.Append($" AND StaticSchoolYearStart = {param.StaticSchoolYearStart}"); //开始学年
                }
                if (!param.StaticSchoolTerm.IsNullOrZero())
                {
                    strSql.Append($" AND StaticSchoolTerm = {param.StaticSchoolTerm}"); //学期
                }
                if (param.GradeId > 0)
                {
                    strSql.Append($" AND StaticGradeId = {param.GradeId}"); //高中
                }
                if (!param.CourseId.IsNullOrZero())
                {
                    strSql.Append($" AND CourseId = {param.CourseId}"); //学科
                }
            }
            var list = await this.BaseRepository().FindList<ExperimentBookingStaticModel>(strSql.ToString());
            return list.ToList();
        }
        #endregion

        #region 获取统计详情方法。
        /// <summary>
        /// 获取实验记录的学年列表(学校,支持按登记人)
        /// </summary>
        /// <param name="param">查询参数</param>
        /// <returns></returns>
        public async Task<List<ExperimentBookingEntity>> GetStaticClassPage(ExperimentBookingListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            var parameter = new List<DbParameter>();
            string tablTextbookVersioneSql = @"LEFT JOIN ex_TextbookVersionDetail AS TD ON TD.BaseIsDelete = 0 AND EB.ExperimentId = TD.Id
                    LEFT JOIN ex_TextbookVersionBase AS vb2 ON vb2.BaseIsDelete = 0 AND TD.TextbookVersionBaseId = vb2.Id
                    LEFT JOIN ex_TextbookVersionCurrent AS VC ON VC.BaseIsDelete = 0 AND vb2.Id = VC.TextbookVersionBaseId";
            if (param.IsCurrentSchoolYear == 2)
            {
                tablTextbookVersioneSql = @"LEFT JOIN ex_TextbookVersionDetailHistory AS TD ON TD.BaseIsDelete = 0 AND EB.ExperimentId = TD.TextbookVersionDetailId
                    LEFT JOIN ex_TextbookVersionBaseHistory AS vb2 ON vb2.BaseIsDelete = 0 AND TD.TextbookVersionBaseId = vb2.TextbookVersionBaseId
                    LEFT JOIN ex_TextbookVersionCurrentHistory AS VC ON VC.BaseIsDelete = 0 AND vb2.TextbookVersionBaseId = VC.TextbookVersionBaseId";
            }

            if (param.IsShowClassTeacher == 1)
            {
                strSql.Append(@$" SELECT SchoolId,SchoolStage,CourseId ,SchoolYearStart,SchoolYearEnd ,SchoolTerm , GradeId , SchoolGradeClassId ,min(ClassName) AS ClassName ,BaseModifierId,RecordUserName From (
                    SELECT EB.SchoolId ,
                    EB.CourseId ,
                    EB.SchoolStage ,
                    EB.SchoolYearStart ,
                    EB.SchoolYearEnd ,
                    EB.SchoolTerm ,
                    EB.GradeId ,
                    EB.SchoolGradeClassId ,
                    EB.ClassName ,
                    EB.ActivityType,
                    EB.BaseModifierId ,
                    u5.RealName AS RecordUserName ,
                    CASE WHEN ISNULL(TD.Id , 0) > 0 THEN TD.ExperimentType ELSE EB.ExperimentType END AS ExperimentType  ,
	                CASE WHEN ISNULL(TD.Id , 0) > 0 THEN TD.IsNeedDo ELSE EB.IsNeedDo END AS IsNeedDo  ,
                    CASE WHEN ISNULL(TD.Id , 0) > 0 THEN TD.IsEvaluate ELSE 0 END AS IsEvaluate  ,
                    CASE WHEN ISNULL(TD.Id , 0) > 0 THEN TD.IsBase ELSE 0 END AS IsBase
                    FROM ex_ExperimentBooking AS EB
                    LEFT JOIN SysUser AS u5 ON EB.BaseModifierId = u5.Id
                    {tablTextbookVersioneSql}
                    WHERE EB.BaseIsDelete = 0 AND EB.Statuz = 100 AND EB.IsMain = 0
                ) as T WHERE  1 = 1 ");
            }
            else
            {
                strSql.Append(@$" SELECT SchoolId,SchoolStage ,CourseId ,SchoolYearStart,SchoolYearEnd ,SchoolTerm , GradeId , SchoolGradeClassId ,min(ClassName) AS ClassName From (
                    SELECT EB.SchoolId ,
                    EB.CourseId ,
                    EB.SchoolStage ,
                    EB.SchoolYearStart ,
                    EB.SchoolYearEnd ,
                    EB.SchoolTerm ,
                    EB.GradeId ,
                    EB.SchoolGradeClassId ,
                    EB.ClassName ,
                    EB.ActivityType,
                    CASE WHEN ISNULL(TD.Id , 0) > 0 THEN TD.ExperimentType ELSE EB.ExperimentType END AS ExperimentType  ,
	                CASE WHEN ISNULL(TD.Id , 0) > 0 THEN TD.IsNeedDo ELSE EB.IsNeedDo END AS IsNeedDo  ,
                    CASE WHEN ISNULL(TD.Id , 0) > 0 THEN TD.IsEvaluate ELSE 0 END AS IsEvaluate  ,
                    CASE WHEN ISNULL(TD.Id , 0) > 0 THEN TD.IsBase ELSE 0 END AS IsBase
                    FROM ex_ExperimentBooking AS EB
                    {tablTextbookVersioneSql}
                    WHERE EB.BaseIsDelete = 0 AND EB.Statuz = 100 AND EB.IsMain = 0
                ) as T WHERE  1 = 1 ");
            }
            if (param != null)
            {
                if (!param.SchoolId.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolId = {param.SchoolId}");//根据用户登录的单位ID进行查询
                }
                if (param.CountyId > 0)
                {
                    strSql.Append($" AND SchoolId IN (SELECT ExtensionObjId FROM up_UnitRelation WHERE ExtensionType = 3 AND UnitId = {param.CountyId} )");//根据用户登录的单位ID进行查询
                }
                if (param.CityId > 0)
                {
                    strSql.Append(@$" AND SchoolId IN (SELECT ur3.ExtensionObjId FROM up_UnitRelation AS ur3
                                    INNER JOIN up_UnitRelation AS ur4 ON ur3.UnitId = ur4.ExtensionObjId AND ur4.ExtensionType = 3 AND  ur4.BaseIsDelete = 0
                                    WHERE ur3.ExtensionType = 3 AND  ur3.BaseIsDelete = 0 AND ur4.UnitId = {param.CityId})");//根据用户登录的单位ID进行查询
                }
                if (param.SchoolStageId > 0)
                {
                    strSql.Append($" AND SchoolStage = {param.SchoolStageId}"); //学段
                }
                if (!param.SchoolYearStart.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolYearStart = {param.SchoolYearStart}"); //开始学年
                }
                if (!param.SchoolTerm.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolTerm = {param.SchoolTerm}"); //学期
                }
                if (!param.GradeId.IsNullOrZero())
                {
                    strSql.Append($" AND GradeId = {param.GradeId}"); //学期
                }
                if (!param.CourseId.IsNullOrZero())
                {
                    strSql.Append($" AND CourseId = {param.CourseId}"); //学科
                }
                if (param.ExperimentType>0)
                {
                    strSql.Append($" AND ExperimentType = {param.ExperimentType}"); //学科
                }
                if (param.IsNeedDo > 0)
                {
                    strSql.Append($" AND IsNeedDo = {param.IsNeedDo}"); //学科
                }
                if (param.SetUserId > 0)
                {
                    //判断用户是否设置了学段、学科
                    var listStage = userSubjectSetService.GetUserSetStageList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listStage.Count > 0)
                    {
                        strSql.Append($" AND ({string.Join(" OR ", listStage.Select(f => string.Format(" SchoolStage = {0} ", f.DictionaryId)))})");
                    }

                    var listCourse = userSubjectSetService.GetUserSetCourseList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listCourse.Count > 0)
                    {
                        strSql.Append($" AND ({string.Join(" OR ", listCourse.Select(f => string.Format(" CourseId = {0} ", f.DictionaryId)))})");
                    }

                }
                if (param.ActivityType != -10000)
                {
                    strSql.Append($" AND ActivityType = {param.ActivityType}"); //课程类型
                }
            }
            if (param.IsShowClassTeacher == 1)
            {
                strSql.Append(" GROUP BY SchoolId,SchoolStage ,CourseId ,SchoolYearStart,SchoolYearEnd ,SchoolTerm ,GradeId, SchoolGradeClassId ,BaseModifierId,RecordUserName ");
            }
            else
            {
                strSql.Append(" GROUP BY SchoolId ,SchoolStage,CourseId , SchoolYearStart,SchoolYearEnd ,SchoolTerm ,GradeId,SchoolGradeClassId ");
            }
            var list = await this.BaseRepository().FindList<ExperimentBookingEntity>(strSql.ToString(), parameter.ToArray(), pagination);
            return list.ToList();
        }

        /// <summary>
        /// 获取登记的班级（高中）列表(学校,支持按登记人)
        /// </summary>
        /// <param name="param">查询参数</param>
        /// <returns></returns>
        public async Task<List<ExperimentBookingEntity>> GetStaticClassGaoZhongPage(ExperimentBookingListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            var parameter = new List<DbParameter>();
            string tablTextbookVersioneSql = @"LEFT JOIN ex_TextbookVersionDetail AS TD ON TD.BaseIsDelete = 0 AND EB.ExperimentId = TD.Id
                    LEFT JOIN ex_TextbookVersionBase AS vb2 ON vb2.BaseIsDelete = 0 AND TD.TextbookVersionBaseId = vb2.Id
                    LEFT JOIN ex_TextbookVersionCurrent AS VC ON VC.BaseIsDelete = 0 AND vb2.Id = VC.TextbookVersionBaseId";
            if (param.IsCurrentSchoolYear == 2)
            {
                tablTextbookVersioneSql = @"LEFT JOIN ex_TextbookVersionDetailHistory AS TD ON TD.BaseIsDelete = 0 AND EB.ExperimentId = TD.TextbookVersionDetailId
                    LEFT JOIN ex_TextbookVersionBaseHistory AS vb2 ON vb2.BaseIsDelete = 0 AND TD.TextbookVersionBaseId = vb2.TextbookVersionBaseId
                    LEFT JOIN ex_TextbookVersionCurrentHistory AS VC ON VC.BaseIsDelete = 0 AND vb2.TextbookVersionBaseId = VC.TextbookVersionBaseId";
            }

            if (param.IsShowClassTeacher == 1)
            {
                strSql.Append(@$" SELECT SchoolId,SchoolStage,CourseId ,SchoolYearStart,SchoolYearEnd ,SchoolTerm , GradeId , SchoolGradeClassId ,min(ClassName) AS ClassName ,BaseModifierId,RecordUserName  From (
                    SELECT EB.SchoolId ,
                    EB.CourseId ,
                    EB.SchoolStage ,
                    EB.SchoolYearStart ,
                    EB.SchoolYearEnd ,
                    EB.SchoolTerm ,
                    EB.GradeId ,
                    EB.SchoolGradeClassId ,
                    EB.ClassName ,
                    EB.BaseModifierId ,
                    u5.RealName AS RecordUserName ,
                    pi2.CompulsoryType ,
                    CASE WHEN ISNULL(TD.Id , 0) > 0 THEN TD.ExperimentType ELSE EB.ExperimentType END AS ExperimentType  ,
	                CASE WHEN ISNULL(TD.Id , 0) > 0 THEN TD.IsNeedDo ELSE EB.IsNeedDo END AS IsNeedDo  ,
                    CASE WHEN ISNULL(TD.Id , 0) > 0 THEN TD.IsEvaluate ELSE 0 END AS IsEvaluate  ,
                    CASE WHEN ISNULL(TD.Id , 0) > 0 THEN TD.IsBase ELSE 0 END AS IsBase  ,
	                CASE WHEN ISNULL(VC.Id , 0) > 0 THEN VC.SchoolTerm ELSE EB.SchoolTerm END AS SchoolTerm
                    FROM ex_ExperimentBooking AS EB
                    INNER JOIN ex_PlanInfo AS pi2 ON EB.PlanInfoId = pi2.Id	AND pi2.BaseIsDelete = 0
                    INNER JOIN ex_PlanDetail AS pd3 ON pd3.BaseIsDelete = 0  AND pi2.Id = pd3.PlanInfoId AND pd3.BaseIsDelete = 0 AND EB.ExperimentId = pd3.ExperimentId
                    LEFT JOIN SysUser AS u5 ON EB.BaseModifierId = u5.Id
                    {tablTextbookVersioneSql}
                    WHERE EB.BaseIsDelete = 0 AND EB.Statuz = 100 AND EB.IsMain = 0
                ) as T WHERE  1 = 1 ");
            }
            else
            {
                strSql.Append(@$" SELECT SchoolId,SchoolStage ,CourseId ,SchoolYearStart,SchoolYearEnd ,SchoolTerm , GradeId , SchoolGradeClassId ,min(ClassName) AS ClassName  From (
                    SELECT EB.SchoolId ,
                    EB.CourseId ,
                    EB.SchoolStage ,
                    EB.SchoolYearStart ,
                    EB.SchoolYearEnd ,
                    EB.SchoolTerm ,
                    EB.GradeId ,
                    EB.SchoolGradeClassId ,
                    EB.ClassName ,
                    pi2.CompulsoryType ,
                    CASE WHEN ISNULL(TD.Id , 0) > 0 THEN TD.ExperimentType ELSE EB.ExperimentType END AS ExperimentType  ,
	                CASE WHEN ISNULL(TD.Id , 0) > 0 THEN TD.IsNeedDo ELSE EB.IsNeedDo END AS IsNeedDo  ,
                    CASE WHEN ISNULL(TD.Id , 0) > 0 THEN TD.IsEvaluate ELSE 0 END AS IsEvaluate  ,
                    CASE WHEN ISNULL(TD.Id , 0) > 0 THEN TD.IsBase ELSE 0 END AS IsBase  ,
	                CASE WHEN ISNULL(VC.Id , 0) > 0 THEN VC.SchoolTerm ELSE EB.SchoolTerm END AS SchoolTerm
                    FROM ex_ExperimentBooking AS EB
                    INNER JOIN ex_PlanInfo AS pi2 ON EB.PlanInfoId = pi2.Id	AND pi2.BaseIsDelete = 0
                    INNER JOIN ex_PlanDetail AS pd3 ON pd3.BaseIsDelete = 0  AND pi2.Id = pd3.PlanInfoId AND pd3.BaseIsDelete = 0 AND EB.ExperimentId = pd3.ExperimentId
                    {tablTextbookVersioneSql}
                    WHERE EB.BaseIsDelete = 0 AND EB.Statuz = 100 AND EB.IsMain = 0
                ) as T WHERE  1 = 1 ");
            }
            if (param != null)
            {
                if (!param.SchoolId.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolId = {param.SchoolId}");//根据用户登录的单位ID进行查询
                }
                if (param.CountyId > 0)
                {
                    strSql.Append($" AND SchoolId IN (SELECT ExtensionObjId FROM up_UnitRelation WHERE ExtensionType = 3 AND UnitId = {param.CountyId} )");//根据用户登录的单位ID进行查询
                }
                if (param.CityId > 0)
                {
                    strSql.Append(@$" AND SchoolId IN (SELECT ur3.ExtensionObjId FROM up_UnitRelation AS ur3
                                    INNER JOIN up_UnitRelation AS ur4 ON ur3.UnitId = ur4.ExtensionObjId AND ur4.ExtensionType = 3 AND  ur4.BaseIsDelete = 0
                                    WHERE ur3.ExtensionType = 3 AND  ur3.BaseIsDelete = 0 AND ur4.UnitId = {param.CityId})");//根据用户登录的单位ID进行查询
                }
                if (param.SchoolStageId > 0)
                {
                    strSql.Append($" AND SchoolStage = {param.SchoolStageId}"); //学段
                }
                if (!param.SchoolYearStart.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolYearStart = {param.SchoolYearStart}"); //开始学年
                }
                if (!param.SchoolTerm.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolTerm = {param.SchoolTerm}"); //学期
                }
                if (!param.GradeId.IsNullOrZero())
                {
                    strSql.Append($" AND GradeId = {param.GradeId}"); //学期
                }
                if (!param.CourseId.IsNullOrZero())
                {
                    strSql.Append($" AND CourseId = {param.CourseId}"); //学科
                }
                if (param.ExperimentType > 0)
                {
                    strSql.Append($" AND ExperimentType = {param.ExperimentType}"); //类型
                }
                if (param.IsNeedDo > 0)
                {
                    strSql.Append($" AND IsNeedDo = {param.IsNeedDo}"); //必须
                }
                if (param.CompulsoryType > 0)
                {
                    strSql.Append($" AND CompulsoryType = {param.CompulsoryType}"); //教材版本
                }
                if (param.SetUserId > 0)
                {
                    //判断用户是否设置了学段、学科
                    var listStage = userSubjectSetService.GetUserSetStageList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listStage.Count > 0)
                    {
                        strSql.Append($" AND ({string.Join(" OR ", listStage.Select(f => string.Format(" SchoolStage = {0} ", f.DictionaryId)))})");
                    }

                    var listCourse = userSubjectSetService.GetUserSetCourseList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listCourse.Count > 0)
                    {
                        strSql.Append($" AND ({string.Join(" OR ", listCourse.Select(f => string.Format(" CourseId = {0} ", f.DictionaryId)))})");
                    }

                }
            }
            if (param.IsShowClassTeacher == 1)
            {
                strSql.Append(" GROUP BY SchoolId,SchoolStage ,CourseId , SchoolYearStart,SchoolYearEnd ,SchoolTerm ,GradeId, SchoolGradeClassId ,BaseModifierId,RecordUserName ");
            }
            else
            {
                strSql.Append(" GROUP BY SchoolId ,SchoolStage,CourseId , SchoolYearStart,SchoolYearEnd ,SchoolTerm ,GradeId,SchoolGradeClassId ");
            }
            var list = await this.BaseRepository().FindList<ExperimentBookingEntity>(strSql.ToString(), parameter.ToArray(), pagination);
            return list.ToList();
        }
        #endregion

        #region 获取登记表记录


        /// <summary>
        /// 实验预约查询
        /// </summary>
        /// <param name="param">查询参数</param>
        /// <param name="pagination">分页参数</param>
        /// <returns></returns>
        public async Task<List<ExperimentBookingEntity>> GetExperimentBookingLog(ExperimentBookingListParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            var parameter = new List<DbParameter>();

            strSql.Append(@$" SELECT * ,
                                CASE WHEN IsNeedDo = 1 THEN '{IsNeedEnum.MustDo.GetDescription()}' ELSE '{IsNeedEnum.SelectToDo.GetDescription()}' END AS IsNeedDoName,
                                CASE WHEN ExperimentType = 1 THEN '演示' WHEN ExperimentType = 2 THEN '分组' ELSE '演示/分组' END AS ExperimentTypeName,
                                CASE WHEN SourceType = 1 THEN '实验计划' WHEN SourceType = 2 THEN '实验目录' ELSE '实验计划/实验目录' END AS SourceTypeName,
                                CASE WHEN SchoolTerm = 1 THEN stuff((convert(varchar,RIGHT(SchoolYearStart,2))+'~'+(convert(varchar,RIGHT(SchoolYearEnd,2))))+ '上学期',1,0,'')
                                ELSE stuff((convert(varchar,RIGHT(SchoolYearStart,2))+'~'+(convert(varchar,RIGHT(SchoolYearEnd,2))))+ '下学期',1,0,'') END AS SchoolTermName
                                From (
                                       SELECT  EB.*
	                                           ,UT.Name AS SchoolName ,UT.Sort ,UR.UnitId AS CountyId, UR2.UnitId AS CityId
                                               ,D1.DicName AS GradeName ,D2.DicName AS CourseName , D13.DicName AS SchoolStageName
                                               ,EB.BaseCreatorId AS BookUserId ,U.RealName AS BookUserName
                                        FROM  ex_ExperimentBooking AS EB
                                        INNER JOIN  up_Unit AS UT ON UT.BaseIsDelete =0 AND EB.SchoolId = UT.Id
                                        LEFT JOIN  up_UnitRelation AS UR ON UR.ExtensionObjId = EB.SchoolId AND UR.ExtensionType = 3 AND UR.BaseIsDelete = 0
                                        INNER JOIN  up_UnitRelation AS UR2 ON UR.UnitId = UR2.ExtensionObjId AND UR2.ExtensionType = 3 AND UR2.BaseIsDelete = 0
                                        LEFT JOIN  sys_static_dictionary AS D13 ON EB.SchoolStage = D13.DictionaryId
                                        LEFT JOIN  sys_static_dictionary AS D1 ON EB.StaticGradeId = D1.DictionaryId
                                        LEFT JOIN  sys_static_dictionary AS D2 ON EB.CourseId = D2.DictionaryId
                                        LEFT JOIN  SysUser AS U ON EB.BaseCreatorId = U.Id
                                        WHERE EB.BaseIsDelete = 0
                          ) as T WHERE  1 = 1 ");
            if (param != null)
            {
                if (!param.Id.IsNullOrZero())
                {
                    strSql.Append($" AND Id = {param.Id}");
                }
                if (!param.SchoolId.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolId = {param.SchoolId}");//根据用户登录的单位ID进行查询
                }
                if (!param.CountyId.IsNullOrZero())
                {
                    strSql.Append($" AND CountyId = {param.CountyId}");
                    if (param.SchoolId > 0)
                    {
                        strSql.Append($" AND SchoolId = {param.SchoolId}");
                    }
                }
                if (!param.CityId.IsNullOrZero())
                {
                    strSql.Append($" AND CityId = {param.CityId}");
                    if (param.CountyId > 0)
                    {
                        strSql.Append($" AND CountyId = {param.CountyId}");
                    }
                    if (param.SchoolId > 0)
                    {
                        strSql.Append($" AND SchoolId = {param.SchoolId}");
                    }
                }
                if (param.SchoolStageId > 0)
                {
                    strSql.Append($" AND SchoolStage = {param.SchoolStageId}"); //学段
                }
                if (!param.SchoolYearStart.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolYearStart = {param.SchoolYearStart}"); //开始学年
                }
                if (!param.SchoolTerm.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolTerm = {param.SchoolTerm}"); //学期
                }
                if (!param.StaticSchoolYearStart.IsNullOrZero())
                {
                    strSql.Append($" AND StaticSchoolYearStart = {param.StaticSchoolYearStart}"); //开始学年
                }
                if (!param.StaticSchoolTerm.IsNullOrZero())
                {
                    strSql.Append($" AND StaticSchoolTerm = {param.StaticSchoolTerm}"); //学期
                }
                if (!param.SchoolGradeClassId.IsNullOrZero())
                {
                    strSql.Append($" AND SchoolGradeClassIdz like '%{param.SchoolGradeClassId}%' "); //年级班级Id
                }
                if (!param.CourseId.IsNullOrZero())
                {
                    strSql.Append($" AND CourseId = {param.CourseId}"); //学科
                }
                if (!param.SourceType.IsNullOrZero())
                {
                    strSql.Append($" AND (SourceType = {param.SourceType} or SourceType = 3) "); //实验来源
                }
                if (!param.ExperimentType.IsNullOrZero())
                {
                    strSql.Append($" AND (ExperimentType = {param.ExperimentType} or ExperimentType = 3)"); //实验类型
                }
                if (!param.BookUserId.IsNullOrZero())
                {
                    strSql.Append($" AND BookUserId = {param.BookUserId}"); //预约人
                }
                if (param.Statuz.HasValue && param.Statuz > -1)
                {
                    strSql.Append($" AND Statuz = {param.Statuz}"); //状态
                }
                if (param.ThanStatuz.HasValue && param.ThanStatuz > -1)
                {
                    strSql.Append($" AND Statuz > {param.ThanStatuz}"); //大于状态
                }
                if (!param.RecordMode.IsNullOrZero())
                {
                    strSql.Append($" AND RecordMode = {param.RecordMode}"); //记录模式
                }
                if (!param.ExperimentName.IsEmpty())
                {
                    strSql.Append($" AND ExperimentName like '%{param.ExperimentName}%'"); //实验名称
                }
                if (param.IsNeedDo > 0)
                {
                    strSql.Append($" AND IsNeedDo = {param.IsNeedDo} "); //实验要求实验
                }
                if (param.IsMain.HasValue)
                {
                    strSql.Append($" AND IsMain = {param.IsMain}"); //父级Id
                }
                if (!param.Pid.IsNullOrZero())
                {
                    strSql.Append($" AND Pid = {param.Pid}"); //父级Id
                }
                if (param.ActivityType != -1 && param.ActivityType != -10000)
                {
                    strSql.Append($" AND ActivityType = {param.ActivityType} "); //实验要求实验
                }
            }
            var list = await this.BaseRepository().FindList<ExperimentBookingEntity>(strSql.ToString(), parameter.ToArray(), pagination);
            return list.ToList();
        }

        #endregion
    }
}
