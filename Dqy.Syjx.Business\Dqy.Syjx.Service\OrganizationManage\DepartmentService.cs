using System;
using System.Collections.Generic;
using System.Data.Common;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.OrganizationManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Model;
using Dqy.Syjx.Model.Param.OrganizationManage;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;

namespace Dqy.Syjx.Service.OrganizationManage
{
    public class DepartmentService : RepositoryFactory
    {
        #region 获取数据
        public async Task<List<DepartmentEntity>> GetList(DepartmentListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.OrderBy(p => p.DepartmentSort).ToList();
        }
        /// <summary>
        /// 获取单位下的部门集合
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<List<DepartmentEntity>> GetUnitList(DepartmentListParam param)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ListFilter(param, strSql);
            var list = await this.BaseRepository().FindList<DepartmentEntity>(strSql.ToString(), filter.ToArray());
            return list.ToList();
        }

        public async Task<DepartmentEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<DepartmentEntity>(id);
        }

        public async Task<int> GetMaxSort()
        {
            object result = await this.BaseRepository().FindObject("SELECT MAX(DepartmentSort) FROM SysDepartment");
            int sort = result.ParseToInt();
            sort++;
            return sort;
        }

        /// <summary>
        /// 部门名称是否存在
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public bool ExistDepartmentName(DepartmentEntity entity)
        {
            var expression = LinqExtensions.True<DepartmentEntity>();
            expression = expression.And(t => t.BaseIsDelete == 0).And(t => t.UnitId == entity.UnitId);
            if (entity.Id.IsNullOrZero())
            {
                expression = expression.And(t => t.DepartmentName == entity.DepartmentName);
            }
            else
            {
                expression = expression.And(t => t.DepartmentName == entity.DepartmentName && t.Id != entity.Id);
            }
            return this.BaseRepository().IQueryable(expression).Count() > 0 ? true : false;
        }

        /// <summary>
        /// 是否存在子部门
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public bool ExistChildrenDepartment(long id)
        {
            var expression = LinqExtensions.True<DepartmentEntity>();
            expression = expression.And(t => t.BaseIsDelete == 0).And(t => t.ParentId == id);
            return this.BaseRepository().IQueryable(expression).Count() > 0 ? true : false;
        }

        /// <summary>
        /// 根据用户Id获取部门名称集合
        /// </summary>
        /// <param name="userId">用户Id</param>
        /// <returns></returns>
        public async Task<List<DepartmentEntity>> GetDepartmentListByUserId(long userId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(@"SELECT D.DepartmentName
                            FROM  SysUser AS SU
                            INNER JOIN  up_DepeartmentRelation AS DR ON SU.Id = DR.ExtensionObjId AND DR.ExtensionType = 1 AND DR.BaseIsDelete = 0
                            INNER JOIN  SysDepartment AS D ON DR.SysDepartmentId = D.Id AND D.BaseIsDelete = 0");
            strSql.Append($" WHERE SU.Id = {userId}");
            var list = await this.BaseRepository().FindList<DepartmentEntity>(strSql.ToString());
            return list.ToList();
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(DepartmentEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update SysDepartment set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
                strSql = $" update SysDepartment set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<DepartmentEntity, bool>> ListFilter(DepartmentListParam param)
        {
            var expression = LinqExtensions.True<DepartmentEntity>();
            expression = expression.And(t => t.BaseIsDelete == 0);
            if (param != null)
            {
                if (!param.UnitId.IsNullOrZero())
                {
                    expression = expression.And(t => t.UnitId == param.UnitId);
                }
                if (!param.DepartmentName.IsEmpty())
                {
                    expression = expression.And(t => t.DepartmentName.Contains(param.DepartmentName));
                }
                if (param.CurrentUnitId > 0)
                {
                    expression = expression.And(t => t.UnitId == param.CurrentUnitId);
                }
                if (param.Statuz > 0)
                {
                    expression = expression.And(t=>t.Statuz == param.Statuz);
                }
            }
            return expression;
        }


        private List<DbParameter> ListFilter(DepartmentListParam param, StringBuilder strSql)
        {
            strSql.Append(@"  SELECT * From (
                             SELECT a1.Id ,
                              a1.BaseIsDelete ,
                              a1.BaseCreateTime ,
                              a1.BaseModifyTime ,
                              a1.BaseCreatorId ,
                              a1.BaseModifierId ,
                              a1.BaseVersion ,
                              a1.ParentId ,
                              a1.DepartmentName ,
                              a1.Telephone ,
                              a1.Fax ,
                              a1.Email ,
                              a1.PrincipalId ,
                              a1.DepartmentSort ,
                              a1.Remark ,
                              a1.Statuz ,
		                      a1.UnitId
		                      FROM  SysDepartment AS a1
		                      INNER JOIN  up_DepeartmentRelation AS b2 ON b2.ExtensionType = 2 AND a1.Id = b2.ExtensionObjId
                          ) as tb1 WHERE   1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                strSql.Append(" AND BaseIsDelete = @BaseIsDelete ");
                parameter.Add(DbParameterExtension.CreateDbParameter("@BaseIsDelete", param.BaseIsDelete));
                if (param.CurrentUnitType == UnitTypeEnum.School.ParseToInt())
                {
                    strSql.Append(" AND UnitId = @UnitId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@UnitId", param.CurrentUnitId));
                }
            }
            return parameter;
        }
        #endregion
    }
}
