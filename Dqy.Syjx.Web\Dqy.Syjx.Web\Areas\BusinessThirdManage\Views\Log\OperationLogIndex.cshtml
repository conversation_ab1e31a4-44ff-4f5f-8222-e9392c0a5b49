﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        登录名称：<input id="userName" col="UserName" type="text" />
                    </li>
                    <li>
                        操作方法：<input id="executeUrl" col="ExecuteUrl" type="text" />
                    </li>
                    <li>
                        操作状态：<span id="logStatus" col="LogStatus"></span>
                    </li>
                    <li class="select-time">
                        <label>操作时间： </label>
                        <input id="startTime" col="StartTime" type="text" class="time-input" placeholder="开始时间" />
                        <span>-</span>
                        <input id="endTime" col="EndTime" type="text" class="time-input" placeholder="结束时间" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                </ul>
            </div>
        </div>
        
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>

<script type="text/javascript">
    $(function () {
        initGrid();

        $("#logStatus").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(OperateStatusEnum).EnumToDictionaryString())) });

        laydate.render({ elem: '#startTime', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed'});
        laydate.render({ elem: '#endTime', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed' });
    });

    function initGrid() {
        var queryUrl = '@Url.Content("~/SystemManage/LogOperate/GetPageListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            columns: [
                { checkbox: true, visible: true },
                { field: 'Id', title: 'Id', visible: false },
                { field: 'UserName', title: '登录名称' },
                { field: 'DepartmentName', title: '部门名称' },
                { field: 'IpAddress', title: 'Ip地址' },
                { field: 'ExecuteUrl', title: '操作方法' },
                { field: 'ExecuteTime', title: '耗时（ms）' },
                {
                    field: 'LogStatus', title: '操作状态', align: 'center',
                    formatter: function (value, row, index) {
                        if (value == "@OperateStatusEnum.Success.ParseToInt()") {
                            return '<span class="badge badge-primary">' + "@OperateStatusEnum.Success.GetDescription()" + '</span>';
                        } else {
                            return '<span class="badge badge-warning">' + "@OperateStatusEnum.Fail.GetDescription()" + '</span>';
                        }
                    }
                },
                {
                    field: 'BaseCreateTime', title: '操作时间', formatter: function (value, row, index) {
                        return ys.formatDate(value, "yyyy-MM-dd HH:mm:ss");
                    }
                },
                {
                    field: '', title: '操作', halign: 'center', align: 'center', width: 60 ,
                     formatter: function (value, row, index) {
                        var html = $.Format('<a class="btn btn-info btn-xs" href="#" onclick="verify(this)" value="{0}"><i class="fa fa-eye"></i>验签</a> ', row.Id);
                        return html;
                    }
                }
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $("#searchDiv").getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function verify(obj) {
        var id = $(obj).attr('value');
        var url = '@Url.Content("~/BusinessThirdManage/SignWx/Verify?bizType=1002")' + '&id=' + id;

        ys.ajax({
                    url: url,
                    type: 'post',
                    success: function (obj) {
                        if (obj.Tag == 1) {
                            ys.msgSuccess(obj.Message);
                        }
                        else {
                            ys.msgError(obj.Message);
                        }
                    }
                });

    }
</script>
