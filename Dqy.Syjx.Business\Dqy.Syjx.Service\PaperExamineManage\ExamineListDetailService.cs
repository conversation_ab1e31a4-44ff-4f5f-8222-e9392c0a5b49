﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.PaperExamineManage;
using Dqy.Syjx.Model.Param.PaperExamineManage;

namespace Dqy.Syjx.Service.PaperExamineManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2022-08-22 11:11
    /// 描 述：服务类
    /// </summary>
    public class ExamineListDetailService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<ExamineListDetailEntity>> GetList(ExamineListDetailListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<ExamineListDetailEntity>> GetPageList(ExamineListDetailListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<ExamineListDetailEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<ExamineListDetailEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(ExamineListDetailEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(ExamineListDetailEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
               strSql = $" update cp_ExamineListDetail set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
               strSql = $"update cp_ExamineListDetail set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<ExamineListDetailEntity, bool>> ListFilter(ExamineListDetailListParam param)
        {
            var expression = LinqExtensions.True<ExamineListDetailEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }

                if (param.ExamineId>0)
                {
                    expression = expression.And(t => t.ExamineId == param.ExamineId);
                }
                if (param.PaperQuestionBankId > 0)
                {
                    expression = expression.And(t => t.PaperQuestionBankId == param.PaperQuestionBankId);
                }
                if (param.ExamineListId > 0)
                {
                    expression = expression.And(t => t.ExamineListId == param.ExamineListId);
                }
                if (param.IsTrue > 0)
                {
                    expression = expression.And(t => t.IsTrue == param.IsTrue);
                }

            }
            return expression;
        }
        #endregion
    }
}
