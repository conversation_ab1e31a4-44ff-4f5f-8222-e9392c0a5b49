﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using System.Web;
using Microsoft.AspNetCore.Mvc;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Entity;
using Dqy.Syjx.Model;
using Dqy.Syjx.Web.Controllers;
using Dqy.Syjx.Entity.OrganizationManage;
using Dqy.Syjx.Business.OrganizationManage;
using Dqy.Syjx.Model.Param.OrganizationManage;
using Dqy.Syjx.Model.Input;
using Dqy.Syjx.Web.Code;
using Dqy.Syjx.Model.Param;
using Dqy.Syjx.Model.Input.PersonManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.Util.Extension;

namespace Dqy.Syjx.Web.Areas.OrganizationManage.Controllers
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-09-17 10:41
    /// 描 述：控制器类
    /// </summary>
    [Area("OrganizationManage")]
    public class SchoolGradeClassController :  BaseController
    {
        private SchoolGradeClassBLL schoolGradeClassBLL = new SchoolGradeClassBLL();

        #region 视图功能
        [AuthorizeFilter("organization:schoolgradeclass:view")]
        public ActionResult SchoolGradeClassIndex()
        {
            return View();
        }

        public ActionResult SchoolGradeClassForm()
        {
            return View();
        }

        public ActionResult EditClassForm()
        {
            return View();
        }

        public IActionResult ClassImport()
        {
            return View();
        }

        [AuthorizeFilter("organization:schoolgradeclass:view")]
        public ActionResult ClassSysIndex()
        {
            return View();
        }
        public ActionResult CreateClassSysForm()
        {
            return View();
        }

        public ActionResult CourseTeachList()
        {
            return View();
        }
        #endregion

        #region 高中选择学科
        public ActionResult SelectSubjectIndex()
        {
            return View();
        }
        #endregion

        #region 获取数据
        [HttpGet]
        public async Task<ActionResult> GetListJson(SchoolGradeClassListParam param)
        {
            param.Statuz = StatusEnum.Yes.ParseToInt();
            TData<List<SchoolGradeClassEntity>> obj = await schoolGradeClassBLL.GetList(param);
            return Json(obj);
        }
        
        [HttpGet]
        public async Task<ActionResult> GetPageListJson(SchoolGradeClassListParam param, Pagination pagination)
        {
            TData<List<SchoolGradeClassEntity>> obj = await schoolGradeClassBLL.GetPageList(param, pagination);
            return Json(obj);
        }

        [HttpGet]
        public async Task<ActionResult> GetFormJson(long id)
        {
            TData<SchoolGradeClassEntity> obj = await schoolGradeClassBLL.GetEntity(id);
            return Json(obj);
        }
        #endregion

        #region  班级任课信息    
        /// <summary>
        ///  获取班级任课信息列表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> GetCourseTeachListJson(SchoolGradeClassListParam param, Pagination pagination)
        {
            var obj = await schoolGradeClassBLL.GetCourseTeachList(param, pagination);
            return Json(obj);
        }


        /// <summary>
        /// 获取老师集合
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> GetTeachListJson()
        {
            var obj = await schoolGradeClassBLL.GetTeachList();
            return Json(obj);
        }
        #endregion


        #region 提交数据
        [HttpPost]
        [AuthorizeFilter("organization:schoolgradeclass:add")]
        public async Task<ActionResult> SaveFormJson(SchoolGradeClassInputModel entity)
        {
            TData<string> obj = await schoolGradeClassBLL.AddForm(entity);
            return Json(obj);
        }
        [HttpPost]
        [AuthorizeFilter("organization:schoolgradeclass:edit")]
        public async Task<ActionResult> SaveEditFormJson(SchoolGradeClassInputModel entity)
        {
            OperatorInfo user = await Operator.Instance.Current();
            entity.UnitId = user.UnitId;
            TData<string> obj = await schoolGradeClassBLL.UpdateForm(entity);
            return Json(obj);
        }
        [HttpPost]
        [AuthorizeFilter("organization:schoolgradeclass:delete")]
        public async Task<ActionResult> DeleteFormJson(string ids)
        {
            OperatorInfo user = await Operator.Instance.Current();
            TData obj = await schoolGradeClassBLL.DeleteForm(ids, user.UnitId);
            return Json(obj);
        }
        [HttpPost]
        public async Task<IActionResult> ImportClassJson(ImportParam param)
        {
            List<SchoolGradeClassInputModel> list = new ExcelHelper<SchoolGradeClassInputModel>().ImportFromExcel(param.FilePath, 2);
            TData obj = await schoolGradeClassBLL.ImportClass(param, list);
            return Json(obj);
        }

        /// <summary>
        /// 班级升级信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("organization:schoolgradeclass:add")]
        public async Task<ActionResult> UpgradeFormJson()
        {
            TData obj = await schoolGradeClassBLL.UpgradeForm();
            return Json(obj);
        }
        /// <summary>
        /// 设置状态
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("organization:schoolgradeclass:edit")]
        public async Task<ActionResult> SetStatuzJson(string ids,int statuz)
        {
            OperatorInfo user = await Operator.Instance.Current();
            if (statuz != StatusEnum.Yes.ParseToInt())
            {
                statuz = StatusEnum.No.ParseToInt();
            }
            TData obj = await schoolGradeClassBLL.SetStatuz(ids, statuz, user.UnitId);
            return Json(obj);
        }
        #endregion
        #region 设置班级所选学科
        [HttpPost]
        [AuthorizeFilter("organization:schoolgradeclass:edit")]
        public async Task<ActionResult> SaveSubjectJson(SchoolGradeClassInputModel model)
        {
            OperatorInfo user = await Operator.Instance.Current();
            model.UnitId = user.UnitId;
            TData<string> obj = await schoolGradeClassBLL.UpdateSubjectJson(model);
            return Json(obj);
        }
        #endregion
        #region 任课管理
        /// <summary>
        /// 保存任课班级设置，验证备课组长角色
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("organization:schoolgradeclass:add")]
        public async Task<ActionResult> SaveTeachCourseClassJson(UserTeachClassInputModel model)
        {
            TData obj = await schoolGradeClassBLL.SaveTeachCourseClass(model);
            return Json(obj);
        }

        /// <summary>
        /// 保存任课班级设置，验证备课组长角色
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [AuthorizeFilter("organization:schoolgradeclass:add")]
        public async Task<ActionResult> DelTeachCourseClassJson(UserTeachClassInputModel model)
        {
            TData obj = await schoolGradeClassBLL.DelTeachCourseClass(model);
            return Json(obj);
        }
        #endregion
    }
}
