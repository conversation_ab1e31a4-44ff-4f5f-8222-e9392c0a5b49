﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.BusinessManage;
using Dqy.Syjx.Model.Param.BusinessManage;

namespace Dqy.Syjx.Service.BusinessManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-11-08 09:54
    /// 描 述：创建分组服务类
    /// </summary>
    public class LaboratoryGroupService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<LaboratoryGroupEntity>> GetList(LaboratoryGroupListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<LaboratoryGroupEntity>> GetPageList(LaboratoryGroupListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<LaboratoryGroupEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<LaboratoryGroupEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(LaboratoryGroupEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(LaboratoryGroupEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            //ids = ids?.Trim(',');
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids  == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
               strSql = $" update bn_LaboratoryGroup set BaseIsDelete = 1 where Id in ({ids})";
            }
            else
            {
               strSql = $"update bn_LaboratoryGroup set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<LaboratoryGroupEntity, bool>> ListFilter(LaboratoryGroupListParam param)
        {
            var expression = LinqExtensions.True<LaboratoryGroupEntity>();
            if (param.BaseIsDelete >= 0)
            {
                expression = expression.And(t => t.BaseIsDelete.Value == param.BaseIsDelete);
            }
       
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
            }
            return expression;
        }
        #endregion
    }
}
