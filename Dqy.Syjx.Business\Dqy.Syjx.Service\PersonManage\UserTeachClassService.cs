﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.PersonManage;
using Dqy.Syjx.Model.Param.PersonManage;

namespace Dqy.Syjx.Service.PersonManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2022-11-21 10:30
    /// 描 述：服务类
    /// </summary>
    public class UserTeachClassService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<UserTeachClassEntity>> GetList(UserTeachClassListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<UserTeachClassEntity>> GetPageList(UserTeachC
        }

        public async Task<List<UserTeachClassEntity>> GetList(UserTeachClassListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<UserTeachClassEntity>> GetPageList(UserTeachClassListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<UserTeachClassEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<UserTeachClassEntity>(id);
        }

        /// <summary>
        /// 获取学科及任课班级信息
        /// </summary>
        /// <param name="courseIdz"></param>
        /// <param name="unitId"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task<List<UserTeachClassEntity>> GetUserTeachClassList(string courseIdz ,long unitId,long userId)
        {
            StringBuilder sql = new StringBuilder();
            sql.Append($@"
                SELECT D.DictionaryId AS CourseId,D.DicName AS CourseName ,ISNULL(T.ClassIdz ,'') AS ClassIdz ,IsNull(D.Nature,0) AS Nature
                FROM  sys_static_dictionary AS D
                LEFT JOIN ( SELECT UC.CourseId ,UC.ClassIdz FROM  up_UserTeachClass AS UC
			                INNER JOIN  up_UserClassInfo AS UCI ON UC.UserClassInfoId = UCI.Id AND UCI.BaseIsDelete = 0
                                                                      AND UCI.UnitId = {unitId} AND UCI.UserId = {userId} AND UCI.IsCurrentUnit = 1
			                WHERE UC.BaseIsDelete = 0 ) AS T ON D.DictionaryId = T.CourseId
                WHERE D.BaseIsDelete = 0 AND D.TypeCode = '1005' AND D.DictionaryId IN ({courseIdz})
            ");
            var list = await this.BaseRepository().FindList<UserTeachClassEntity>(sql.ToString());
            return list.ToList();
        }

        /// <summary>
        /// 获取学科及任课班级信息
        /// </summary>
        /// <param name="courseIdz"></param>
        /// <param name="unitId"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task<List<UserTeachClassEntity>> GetCourseClassList(UserClassInfoListParam param)
        {
            StringBuilder sql = new StringBuilder();
                  sql.Append($@"  SELECT * FROM (");
            sql.Append($@" SELECT
			    tc.Id ,
			    tc.BaseIsDelete ,
			    tc.BaseCreateTime ,
			    tc.BaseModifyTime ,
			    tc.BaseCreatorId ,
			    tc.BaseModifierId ,
			    tc.BaseVersion ,
			    tc.UserClassInfoId ,
			    tc.CourseId ,
                tc.GradeIdz ,
			    tc.ClassIdz ,
			    dic.DicName AS CourseName ,
			    uc.UnitId ,
			    uc.UserId ,
			    su.RealName AS UserName ,
				IsNULL(dic.Nature,0) AS Nature
			 FROM  up_UserTeachClass AS tc
			 INNER JOIN  sys_static_dictionary AS dic ON dic.BaseIsDelete = 0 AND dic.TypeCode =  '1005' AND tc.CourseId = dic.DictionaryId
			 INNER JOIN  up_UserClassInfo AS uc ON uc.BaseIsDelete =0 AND tc.UserClassInfoId = uc.Id
			 INNER JOIN  SysUser AS su ON uc.UserId = su.Id
			 WHERE tc.BaseIsDelete = 0
            ");
            sql.Append($"  ) as tb1 Where UnitId = {param.UnitId} ");
            if (param.UserId > 0)
            {
                sql.Append($" AND UserId = {param.UserId} ");
            }
            if (param.Nature != -1)
            {
                sql.Append($" AND Nature = {param.Nature} ");
            }
            if (param.GradeId > 0)
            {
                sql.Append($" AND GradeIdz like '%{param.GradeId}%'"); //实验名称
            }
            if (param.CourseId > 0)
            {
                sql.Append($" AND CourseId = {param.CourseId} ");
            }
            var list = await this.BaseRepository().FindList<UserTeachClassEntity>(sql.ToString());
            return list.ToList();
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(UserTeachClassEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(UserTeachClassEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
               strSql = $" update up_UserTeachClass set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
               strSql = $"update up_UserTeachClass set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }

        /// <summary>
        /// 根据任课信息主表Id删除任课班级数据
        /// </summary>
        /// <param name="userClassInfoId"></param>
        /// <returns></returns>
        public async Task DeleteByUserClassInfoIdForm(long userClassInfoId)
        {
            string sql = $"update up_UserTeachClass set BaseIsDelete = 1 where UserClassInfoId = {userClassInfoId} and BaseIsDelete = 0";
            await this.BaseRepository().ExecuteBySql(sql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<UserTeachClassEntity, bool>> ListFilter(UserTeachClassListParam param)
        {
            var expression = LinqExtensions.True<UserTeachClassEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!param.UserClassInfoId.IsNullOrZero())
                {
                    expression = expression.And(t => t.UserClassInfoId == param.UserClassInfoId);
                }
                if (param.GradeId > 0)
                {
                    expression = expression.And(t => t.ClassIdz.Contains(param.GradeId.ToString()));
                }
                if (param.CourseId > 0)
                {
                    expression = expression.And(t => t.CourseId == param.CourseId);
                }
                if (param.ClassId > 0)
                {
                    expression = expression.And(t => t.ClassIdz.Contains(param.ClassId.ToString()));
                }
            }
            return expression;
        }
        #endregion
    }
}

