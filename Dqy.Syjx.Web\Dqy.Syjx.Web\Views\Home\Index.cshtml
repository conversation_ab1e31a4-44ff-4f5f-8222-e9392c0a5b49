﻿@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@{
    var loginCfg = await Dqy.Syjx.Web.CommonLib.Configs.GetWebTitle();  
    ViewBag.Title = loginCfg;
    Layout = "~/Views/Shared/_Layout.cshtml";

    OperatorInfo operatorInfo = ViewBag.OperatorInfo;
    String portrait = operatorInfo.Portrait;
    if (portrait == null || string.IsNullOrEmpty(portrait))
    {
        portrait = await Dqy.Syjx.Web.CommonLib.Configs.GetLogo(); 
    }
    else
    {
        portrait = GlobalContext.SystemConfig.WebSite + portrait;
    }
    string dashboardUrl = "~/Home/Welcome";
    if (operatorInfo.UnitType == UnitTypeEnum.School.ParseToInt() || operatorInfo.UnitType == UnitTypeEnum.County.ParseToInt())
    {
        dashboardUrl = "~/Home/Dashboard";
    }
}
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/yisha/js/yisha-data.min.js"))





<div id="wrapper">
    <!--左侧导航开始-->
    <nav class="navbar-default navbar-static-side" role="navigation">
        <div class="nav-close">
            <i class="fa fa-times-circle"></i>
        </div>
        <div class="sidebar-collapse">
            <ul class="nav" id="side-menu">
                <li class="logo" style="text-align:left;letter-spacing:0.1rem;padding:0 10px;">  <img src="@portrait" class="user-image" style="width:30px;border-radius:50%;margin-right:4px;"> <span style="vertical-align: middle;">导航栏</span> </li>
                @*<li class="nav-header">
                <div class="dropdown profile-element clear">
                <div class="image-left">
                <img alt="image" src='@portrait' class="img-circle" height="50" width="50" />
                </div>
                <div class="image-right">
                <span class="block m-t-xs">@operatorInfo.UserName</span>
                <span class="text-xs block">@operatorInfo.DepartmentName</span>
                </div>
                </div>
                </li>*@
                @{
                    List<MenuEntity> menuList = ViewBag.MenuList;
                    foreach (MenuEntity menu in menuList.Where(p => p.ParentId == 0).OrderBy(p => p.MenuSort))
                    {
                            <li data-type="menu">
                                @{
                                if (HttpHelper.IsUrl(menu.MenuUrl))
                                {
                                            <a class="menuItem" href="@menu.MenuUrl">
                                                <i class="@menu.MenuIcon"></i>
                                                <span class="nav-label">@menu.MenuName</span>
                                            </a>
                                }
                                else
                                {
                                            <a href="#">
                                                <i class="@menu.MenuIcon"></i>
                                                <span class="nav-label">@menu.MenuName</span>
                                                <span class="fa arrow"></span>
                                            </a>
                                }
                                }
                                <ul class="nav nav-second-level collapse" data-type="menu">
                                    @foreach (MenuEntity secondMenu in menuList.Where(p => p.ParentId == menu.Id).OrderBy(p => p.MenuSort))
                                {
                                        <li data-type="menu">
                                            @{
                                            if (menuList.Where(p => p.ParentId == secondMenu.Id && p.MenuType != (int)MenuTypeEnum.Button).Count() == 0)
                                            {
                                                if (HttpHelper.IsUrl(secondMenu.MenuUrl))
                                                {
                                                            <a class="menuItem" href='@secondMenu.MenuUrl'>@secondMenu.MenuName</a>
                                                }
                                                else if (secondMenu.MenuUrl.Length > 0)
                                                {
                                                            <a class="menuItem" href="#" data-url='@Url.Content("~/" + secondMenu.MenuUrl)'>@secondMenu.MenuName</a>
                                                }
                                            }
                                            else
                                            {
                                                        <a href="#">@secondMenu.MenuName<span class="fa arrow"></span></a>
                                                        <ul class="nav nav-third-level" data-type="menu">
                                                            @foreach (MenuEntity thirdMenu in menuList.Where(p => p.ParentId == secondMenu.Id).OrderBy(p => p.MenuSort))
                                                    {
                                                                <li data-type="menu">
                                                                    <a class="menuItem" href="#" data-url='@Url.Content("~/" + thirdMenu.MenuUrl)'>@thirdMenu.MenuName</a>
                                                                </li>
                                                    }
                                                        </ul>
                                            }
                                            }
                                        </li>
                                }
                                </ul>
                            </li>
                    }
                }
            </ul>
        </div>
    </nav>
    <!--左侧导航结束-->
    <!--右侧部分开始-->
    <div id="page-wrapper" class="gray-bg dashbard-1">
        <div class="row">
            <nav class="navbar navbar-static-top" role="navigation" style="margin-bottom: 0">
                <a class="navbar-minimalize minimalize-styl-2" href="#" title="收起菜单">
                    <i class="fa fa-bars"></i>
                </a>
                <span style="color: #fff;font-size: 20px;vertical-align: middle;">@loginCfg</span>
                <ul class="nav navbar-top-links navbar-right welcome-message">
                    @if (operatorInfo.UnitType == UnitTypeEnum.County.ParseToInt())
                    {
                        <li><a href='@Url.Content("~/DataCharts/Index")' target="_blank"><i class="fa fa-line-chart"></i>数据看板</a></li>
                    }
                    <li><a id="fullScreen"><i class="fa fa-arrows-alt"></i>全屏</a></li>
                    <li class="dropdown user-menu">
                        <a href="javascript:void(0)" class="dropdown-toggle" data-hover="dropdown">
                          @*   <img src="@portrait" class="user-image"> *@
                            <span class="hidden-xs">@operatorInfo.RealName</span>
                        </a>
                        <ul class="dropdown-menu">
                            @if (operatorInfo.IsThirdLogin == 0)
                            {
                                <li class="mt5">
                                    <a class="menuItem" href="#" data-url='@Url.Content("~/OrganizationManage/User/UserDetail")?id=@operatorInfo.UserId'>
                                        <i class="fa fa-user"></i> 个人中心
                                    </a>
                                </li>
                                <li>
                                    <a onclick="showChangePasswordForm()">
                                        <i class="fa fa-key"></i> 修改密码
                                    </a>
                                </li>
                            }
                            else if (operatorInfo.ThirdUserUnitList != null && operatorInfo.ThirdUserUnitList.Count() > 1)
                            {
                                foreach (var u in operatorInfo.ThirdUserUnitList) 
                                {
                                    if(u.Id != operatorInfo.UserId){
                                    <li>
                                            <a onclick="turnUser('@u.Id')" title="切换至 @u.Name">
                                                <i class="fa fa-exchange"></i> 切换至 @u.Name
                                        </a>
                                    </li>
                                    }
                                }
                            }
                            <li>
                                <a onclick="showSwitchSkinForm()">
                                    <i class="fa fa-dashboard"></i> 切换主题
                                </a>
                            </li>
                            <li class="divider"></li>
                            <li>
                                <a href="@Url.Content("~/Home/LoginOff")">
                                    <i class="fa fa-sign-out"></i> 退出登录
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </nav>
        </div>
        <div class="row content-tabs">
            <button class="roll-nav roll-left tabLeft">
                <i class="fa fa-backward"></i>
            </button>
            <nav class="page-tabs menuTabs">
                <div class="page-tabs-content">
                    <a href="javascript:;" class="active menuTab" data-id="/system/main">首页</a>
                </div>
            </nav>
            <button class="roll-nav roll-right tabRight">
                <i class="fa fa-forward"></i>
            </button>
            <div class="btn-group roll-nav roll-right">
                <button class="dropdown J_tabClose" data-toggle="dropdown">
                    页签操作<span class="caret"></span>
                </button>
                <ul role="menu" class="dropdown-menu dropdown-menu-right">
                    <li><a class="tabCloseCurrent" href="#">关闭当前</a></li>
                    <li><a class="tabCloseOther" href="#">关闭其他</a></li>
                    <li><a class="tabCloseAll" href="#">全部关闭</a></li>
                </ul>
            </div>
            <a href="#" class="roll-nav roll-right tabReload"><i class="fa fa-refresh"></i> 刷新</a>
        </div>
        <div class="row mainContent" id="content-main">
            <iframe class="YiSha_iframe" name="iframe0" width="100%" height="100%" data-id="/system/main" src="@Url.Content(dashboardUrl)" frameborder="0" seamless></iframe>
        </div>


@*        <div class="divResize" data-key="drag">
            <div class="lineAll line-n" data-key="n"></div>
            <div class="lineAll line-e" data-key="e"></div>
            <div class="lineAll line-s" data-key="s"></div>
            <div class="lineAll line-w" data-key="w"></div>
            <div class="point point-n" data-key="n"></div>
            <div class="point point-e" data-key="e"></div>
            <div class="point point-s" data-key="s"></div>
            <div class="point point-w" data-key="w"></div>
            <div class="point point-ne" data-key="ne"></div>
            <div class="point point-se" data-key="se"></div>
            <div class="point point-sw" data-key="sw"></div>
            <div class="point point-nw" data-key="nw"></div>
            <div>
                <div class="dialogTitle">
                    <div>弹窗标题</div>
                    <div class="dialogTitle-right">
                        <i class="fa fa-close" onclick="delDialog()"></i>
                    </div>
                </div>
                <div class="dialogContent">内容区域</div>
            </div>
        </div>*@
    </div>
    <!--右侧部分结束-->
</div>

<script type="text/javascript">
    if (!ys.isNullOrEmpty($.cookie('Skin'))) {
        var skin = decodeURIComponent($.cookie('Skin'));
        $("body").addClass(skin.split('|')[0]);
        $("body").addClass(skin.split('|')[1]);
    } else {
        var sideTheme = "theme-dark";
        var skinName = "skin-blue";
        $("body").addClass(sideTheme);
        $("body").addClass(skinName);
    }


    function showChangePasswordForm() {
        ys.openDialog({
            title: "修改密码",
            content: '@Url.Content("~/OrganizationManage/User/ChangePassword")' + '?id=@operatorInfo.UserId',
            height: "350px",
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function turnUser(id) {
            var url = '@Url.Content("~/Home/TurnUser")';
            ys.ajax({
                url: url + "?id=" + id,
                type: "post",
                data: null,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        location.href = '@Url.Content("~/Home/Index")';
                    }
                    else {                        
                        ys.msgError(obj.Message);                        
                    }
                }
            });
    }
    function showSwitchSkinForm() {
        ys.openDialog({
            title: "切换主题",
            content: '@Url.Content("~/Home/Skin")',
            width: '530px',
            height: '390px',
            btn: null,
            maxmin: false
        });
    }

 
    //function resizeCallback(event) {
    //    if (event) {
    //        $(".divResize").show()
    //        console.log("获取到子元素值",  event)
    //    }
    //}

    //// 获取dom
    //const box = document.querySelector(".divResize");
    //$(".divResize").hide()
    //// 声明全局变量
    //let width = 600, height = 400, minWidth = 300, minHeight = 200, isPointerdown = false,
    //    x = (window.innerWidth - width) , y = (window.innerHeight - height),
    //    diff = { x: 0, y: 0 }, lastPointermove = { x: 0, y: 0 }, key = "", rect = null;
    //box.style.width = width + "px";
    //box.style.height = height + "px";
    //box.style.transform = "translate3d(" + x + "px, " + y + "px, 0px)";
    //const action = {
    //    drag: function () {
    //        x += diff.x;
    //        y += diff.y;
    //    },
    //    n: function (e) {
    //        if (rect.bottom - e.clientY > minHeight) {
    //            height = rect.bottom - e.clientY;
    //            y = e.clientY;
    //        }
    //    },
    //    e: function (e) {
    //        console.log(e.clientX, rect.left, minWidth)
    //        if (e.clientX - rect.left > minWidth) {
    //            width = e.clientX - rect.left;
    //        }
    //    },
    //    s: function (e) {
    //        if (e.clientY - rect.top > minHeight) {
    //            height = e.clientY - rect.top;
    //        }
    //    },
    //    w: function (e) {
    //        if (rect.right - e.clientX > minWidth) {
    //            width = rect.right - e.clientX;
    //            x = e.clientX;
    //        }
    //    },
    //    ne: function (e) {
    //        this.n(e);
    //        this.e(e);
    //    },
    //    se: function (e) {
    //        this.s(e);
    //        this.e(e);
    //    },
    //    sw: function (e) {
    //        this.s(e);
    //        this.w(e);
    //    },
    //    nw: function (e) {
    //        this.n(e);
    //        this.w(e);
    //    }
    //}

    //// 绑定事件
    //box.addEventListener("pointerdown", function (e) {
    //  console.log(e,"e")
    //    isPointerdown = true;
    //    e.target.setPointerCapture(e.pointerId);
    //    lastPointermove = { x: e.clientX, y: e.clientY };
    //    key = e.target.dataset.key;
    //    rect = box.getBoundingClientRect();
    //    console.log(rect,"rect")
    //});

    //box.addEventListener("pointermove", function (e) {
    //    if (isPointerdown) {
    //        const current = { x: e.clientX, y: e.clientY };
    //        diff.x = current.x - lastPointermove.x;
    //        diff.y = current.y - lastPointermove.y;
    //        lastPointermove = { x: current.x, y: current.y };
    //        if (action[key]) {
    //            action[key](e);
    //        } else {
    //            action["drag"](e);
    //        }
    //        //action[key](e);
    //        console.log(width, "-----", height)
    //        box.style.width = width + "px";
    //        box.style.height = height + "px";
    //        box.style.transform = "translate3d(" + x + "px, " + y + "px, 0)";
    //        e.preventDefault();
    //    }
    //});
    //box.addEventListener("pointerup", function (e) {
    //    if (isPointerdown) {
    //        isPointerdown = false;
    //    }
    //});
    //box.addEventListener("pointercancel", function (e) {
    //    if (isPointerdown) {
    //        isPointerdown = false;
    //    }
    //});



    //function delDialog() {
    //    $(".divResize").hide()
    //    box.style.width = "600px";
    //    box.style.height = "400px";
    //    width = 600;
    //    height = 400;
    //}


</script>
<script src="~/junfei/js/GetAccessId.js"></script>