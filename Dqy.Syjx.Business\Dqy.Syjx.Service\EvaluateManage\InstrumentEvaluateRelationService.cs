﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.EvaluateManage;
using Dqy.Syjx.Model.Param.EvaluateManage;

namespace Dqy.Syjx.Service.EvaluateManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2023-12-06 18:10
    /// 描 述：服务类
    /// </summary>
    public class InstrumentEvaluateRelationService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<InstrumentEvaluateRelationEntity>> GetList(InstrumentEvaluateRelationListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<InstrumentEvaluateRelationEntity>> GetPageList(InstrumentEvaluateRelationListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<InstrumentEvaluateRelationEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<InstrumentEvaluateRelationEntity>(id);
        }

        #endregion

        #region 提交数据
        public async Task SaveForm(InstrumentEvaluateRelationEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

       

        public async Task SaveTransForm(InstrumentEvaluateRelationEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update eq_InstrumentEvaluateRelation set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
                strSql = $"update eq_InstrumentEvaluateRelation set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }

        /// <summary>
        /// 根据仪器配置标准明细表Id删除信息
        /// </summary>
        /// <param name="evaluateListId">仪器配置标准明细表Id</param>
        /// <returns></returns>
        public async Task DeleteByEvaluateListId(long evaluateListId)
        {
            string strSql=$"DELETE FROM eq_InstrumentEvaluateRelation WHERE EvaluateListId = {evaluateListId}";
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<InstrumentEvaluateRelationEntity, bool>> ListFilter(InstrumentEvaluateRelationListParam param)
        {
            var expression = LinqExtensions.True<InstrumentEvaluateRelationEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (param.EvaluateListId > 0)
                {
                    expression = expression.And(f => f.EvaluateListId == param.EvaluateListId);

                }

            }
            return expression;
        }
        #endregion
    }
}
