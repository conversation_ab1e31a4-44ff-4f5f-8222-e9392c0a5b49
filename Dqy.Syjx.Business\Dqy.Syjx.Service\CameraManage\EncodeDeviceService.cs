﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.CameraManage;
using Dqy.Syjx.Model.Param.CameraManage;

namespace Dqy.Syjx.Service.CameraManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2022-05-05 11:03
    /// 描 述：服务类
    /// </summary>
    public class EncodeDeviceService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<EncodeDeviceEntity>> GetList(EncodeDeviceListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<EncodeDeviceEntity>> GetPageList(EncodeDeviceListParam param, Pagination pagination)
        {
            //var expression = ListFilter(param);
            //var list= await this.BaseRepository().FindList(expression, pagination);
            //return list.ToList();
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ListFilter(param, strSql);
            var list = await this.BaseRepository().FindList<EncodeDeviceEntity>(strSql.ToString(), filter.ToArray(), pagination);
            return list.ToList();
        }

        public async Task<EncodeDeviceEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<EncodeDeviceEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(EncodeDeviceEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(EncodeDeviceEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                entity.Create();
                await db.Insert(entity);
            }
            else
            {
                
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
               strSql = $" update cm_EncodeDevice set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
               strSql = $"update cm_EncodeDevice set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<EncodeDeviceEntity, bool>> ListFilter(EncodeDeviceListParam param)
        {
            var expression = LinqExtensions.True<EncodeDeviceEntity>();
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (!string.IsNullOrEmpty(param.EncodeName))
                {
                    expression = expression.And(t => t.EncodeName.Contains(param.EncodeName));
                }
            }
            return expression;
        }
        private List<DbParameter> ListFilter(EncodeDeviceListParam param, StringBuilder strSql)
        {
            strSql.Append($@" SELECT * From (
                                SELECT c.Id AS Id,GrantId,ClientName,ClientId,IssuerURI,Audience,AllowRememberLogin,EnabledLocalLogin,EnableExternalLogin,IsAutoAuth,HomeUrl,ApplicationBrief,
                                ApplicationRemark,ApplicationIcon,ApplicationIconRemark,ApplicableSystem,ApplicableTerminals,c.IsShow,c.IsOnline,Memo,a.Title AS ApplicationIconTitle,a.Path AS ApplicationIconPath
                                FROM Clients c LEFT JOIN Attachment a ON c.ApplicationIcon = a.Id
                            ) as tb1 WHERE 1 = 1 ");
            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.EncodeName))
                {
                    strSql.Append(" AND EncodeName like @EncodeName");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@EncodeName", $"%{param.EncodeName}%"));
                }
                if (!string.IsNullOrEmpty(param.CameraName))
                {
                    strSql.Append(" AND CameraName like @CameraName");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CameraName", $"%{param.CameraName}%"));
                }
                if (!string.IsNullOrEmpty(param.DeviceCode))
                {
                    strSql.Append(" AND DeviceCode like @DeviceCode");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@DeviceCode", $"%{param.DeviceCode}%"));
                }
            }
            return parameter;
        }
        #endregion
    }
}
