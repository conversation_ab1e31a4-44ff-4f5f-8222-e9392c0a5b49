using Dqy.Syjx.Business.ArticleManager;
using Dqy.Syjx.Business.EvaluateManage;
using Dqy.Syjx.Business.ExperimentTeachManage;
using Dqy.Syjx.Business.OrganizationManage;
using Dqy.Syjx.Business.QueryStatisticsManage;
using Dqy.Syjx.Business.SystemManage;
using Dqy.Syjx.Business.WxManage;
using Dqy.Syjx.Entity.BusinessManage;
using Dqy.Syjx.Entity.ExperimentTeachManage;
using Dqy.Syjx.Entity.OrganizationManage;
using Dqy.Syjx.Entity.SystemManage;
using Dqy.Syjx.Entity.WxManage;
using Dqy.Syjx.Enum;
using Dqy.Syjx.IdGenerator;
using Dqy.Syjx.Model.Param.ArticleManager;
using Dqy.Syjx.Model.Param.EvaluateManage;
using Dqy.Syjx.Model.Param.ExperimentTeachManage;
using Dqy.Syjx.Model.Param.OrganizationManage;
using Dqy.Syjx.Model.Param.SystemManage;
using Dqy.Syjx.Model.Result;
using Dqy.Syjx.Model.Result.OrganizationManage;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Util.ThirdOAuth;
using Dqy.Syjx.Util.Tools;
using Dqy.Syjx.Util.Tools.Helper;
using Dqy.Syjx.Web.Code;
using Dqy.Syjx.Web.CommonLib;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.VisualBasic;
using NetTaste;
using NetTopologySuite.Index.HPRtree;
using Newtonsoft.Json;
using NPOI.POIFS.FileSystem;
using NPOI.SS.Formula.Functions;
using Polly;
using RTools_NTS.Util;
//using ThoughtWorks.QRCode.Codec;
using SkiaSharp;
using SkiaSharp.QrCode;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Diagnostics;
using System.Diagnostics.Metrics;
using System.IO;
using System.Linq;
using System.Reflection.Metadata;
using System.Threading.Tasks;

namespace Dqy.Syjx.Web.Controllers
{
    public class HomeController : BaseController
    {
        private MenuBLL menuBLL = new MenuBLL();
        private UserBLL userBLL = new UserBLL();
        private LogLoginBLL logLoginBLL = new LogLoginBLL();
        private MenuAuthorizeBLL menuAuthorizeBLL = new MenuAuthorizeBLL();
        private ArticleCategoryBLL articleCategoryBLL = new ArticleCategoryBLL();
        private ConfigSetBLL configSetBLL = new ConfigSetBLL();
        private UserBindOpenidBLL userbindBLL = new UserBindOpenidBLL();
        private AppManageBLL appManagerBLL = new AppManageBLL();
        private WorkBLL workBLL = new WorkBLL();
        private StaticDictionaryBLL staticDictionaryBll = new StaticDictionaryBLL();
        private SchoolTermBLL schoolTermBLL = new SchoolTermBLL();
        private PlanExamParameterBLL planExamParameterBll = new PlanExamParameterBLL();
        public IConfiguration Configuration { get; }
        private readonly NetHelper _netHelper;
        public HomeController(IConfiguration configuration, NetHelper netHelper)
        {
            Configuration = configuration;
            _netHelper = netHelper;
        }

        #region 视图功能
        [HttpGet]
        [AuthorizeFilter]
        public async Task<IActionResult> Index()
        {
            //var pu = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCjwgZr6tPCBfhZBdSOzNRSrYNrji4I9yUknp/cWcVflokbUrf1rOajDs2Q9Ysc71/rU9Sq/o0WVT8xHSaxkoTCJ5GDvd6Pps1YQt6aDjc9IlkDkUFPJEgIuNLiHVu74xDPCeZfexsENLr+To3hEHt75UYMpZMJmR9hsOddNBHcPQIDAQAB";
            //var pr = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAKPCBmvq08IF+FkF1I7M1FKtg2uOLgj3JSSen9xZxV+WiRtSt/Ws5qMOzZD1ixzvX+tT1Kr+jRZVPzEdJrGShMInkYO93o+mzVhC3poONz0iWQORQU8kSAi40uIdW7vjEM8J5l97GwQ0uv5OjeEQe3vlRgylkwmZH2Gw5100Edw9AgMBAAECgYA8J2fIeKh021CREw0zr5ZHEKn39nB+ppqkSwiaHvfb1yZiPQ6KVosaVrO4jhod9OidP1wdvAgDrijaV8UA8buGJ+KnZTQumJCc34EArHlUenp2EuPEK+XnfmCvpFr+yRkuPPmE5C9OIjFzoHvrpfCEIUWOAozlWh/i2NnkxEDIAQJBANman7xf1/QeQ0iFyY8mtCXP3+76UDuA9jSqvesXagDWLp8MmToCMzOOFXIgm1W6K692p9GzJvkS3rH5P+1xywECQQDApx941Tc2JfmFHtC8YSaq69u2lvj1N+zu4GI+nd3CwMprZ/5MSBzO5yHLpsH6LICIZWycStIRagQE+8so1X09AkEAv6Oaa/bopFiGnvccVNMcMnTJXZjbUhw4OJfqXIGpXIVEV9RrmZfYad4G6xTmWHB2CrhwAZFKkG7a61h8wWvHAQJAAuqEr2+KxmcMv9cXkcJms2+eg8UFP7D5BSSgWBJLqQcCCV4pmz38MaWN/WcoyICj8WyXmEYJp/nS1cB58sRIgQJAQWxfYWWtiByntjpTWw0f503yq7D9aIpYuRz9YyvhMXAxPJ1HA9sWc+IMis5cEe811Fq1E+BF/yzsv+fMA/VG/g==";
            //var aa = RSAUtils.EncryptByPublicKey("在公开密钥密码体制中，加密密钥（即公开密钥）PK是公开信息，而解密密钥（即秘密密钥）SK是需要保密的。加密算法E和解密算法D也都是公开的。虽然解密密钥SK是由公开密钥PK决定的，但却不能根据PK计算出SK",
            //    pu);
            //var bb = RSAUtils.DecryptByPrivateKey(aa,
            //    pr);

            OperatorInfo operatorInfo = await Operator.Instance.Current();

            TData<List<MenuEntity>> objMenu = await menuBLL.GetList(null);
            List<MenuEntity> menuList = new List<MenuEntity>();
            if (objMenu.Data != null && objMenu.Data.Count > 0)
            {
                menuList = objMenu.Data.DeepClone();
            }
            MenuEntity entityMenuGao = null;//保存高中选课菜单；
            if (operatorInfo.IsSystem != 1)
            {
                if (operatorInfo.UnitType == UnitTypeEnum.School.ParseToInt() && menuList != null && menuList.Count > 0)
                {
                    /**
                     *简易模式是否区县开启。
                     */
                    var roleMenu = menuList.Where(m => m.MenuName == "简易模式").ToList();
                    if (roleMenu != null && roleMenu.Count() > 0)
                    {
                        /**
                         *设置是否显示简易模式登记功能
                         *1：获取配置，区县设置的，是否开启简易登记。
                         *2：默认是不开启的，没有查到，或者没有设置开始，默认移除。
                         */
                        var configset = await configSetBLL.GetEntityByCode(new Model.Param.SystemManage.ConfigSetListParam()
                        {
                            UnitId = operatorInfo.UnitId.Value,
                            UnitType = (UnitTypeEnum)operatorInfo.UnitType,
                            ConfigUnitType = UnitTypeEnum.County,
                            TypeCode = "1003_SFKQJYDJ"
                        });
                        if (!(configset != null && configset.Data != null && configset.Data.ConfigValue == "1"))
                        {
                            menuList.Where(m => m.Id == 391881588633243648 || m.ParentId == 391881588633243648).ForEach(m => m.MenuStatus = 2);
                        }
                        else
                        {
                            //验证对应的学段是否开启。
                            var configsets = await configSetBLL.GetEntityListByCode(new Model.Param.SystemManage.ConfigSetListParam()
                            {
                                TypeCode = "1003_SFKQJYDJ_",
                                ConfigUnitType = UnitTypeEnum.County,
                                UnitId = operatorInfo.UnitId ?? 0,
                                UnitType = (UnitTypeEnum)operatorInfo.UnitType,
                                ModuleCode = "1003"
                            });
                            if (configsets != null && configsets.Data != null && configsets.Data.Count > 0)
                            {
                                bool isOPen = false;
                                if (operatorInfo.SchoolStageList != null && operatorInfo.SchoolStageList.Count > 0)
                                {
                                    foreach (var item in configsets.Data)
                                    {
                                        if (item.ConfigValue == "1")
                                        {
                                            if (item.TypeCode == "1003_SFKQJYDJ_1GZ")
                                            {
                                                if (operatorInfo.SchoolStageList.Contains(SchoolStageEnum.GaoZhong.ParseToInt()))
                                                {
                                                    isOPen = true;
                                                }
                                            }
                                            else if (item.TypeCode == "1003_SFKQJYDJ_2CZ")
                                            {
                                                if (operatorInfo.SchoolStageList.Contains(SchoolStageEnum.ChuZhong.ParseToInt()))
                                                {
                                                    isOPen = true;
                                                }
                                            }
                                            else if (item.TypeCode == "1003_SFKQJYDJ_3XX")
                                            {
                                                if (operatorInfo.SchoolStageList.Contains(SchoolStageEnum.XiaoXue.ParseToInt()))
                                                {
                                                    isOPen = true;
                                                }
                                            }
                                        }
                                    }
                                }
                                if (!isOPen)
                                {
                                    menuList.Where(m => m.Id == 391881588633243648 || m.ParentId == 391881588633243648).ForEach(m => m.MenuStatus = 2);
                                }
                            }
                        }
                    }
                    /** 
                     *实验目录是否区县开启。 
                     */
                    var currentMenu = menuList.Where(m => m.MenuName == "按目录登记" || m.MenuName == "按目录预约").ToList();
                    if (currentMenu != null && currentMenu.Count() > 0)
                    {
                        /**
                         *设置是否显示简易模式登记功能
                         *1：获取配置，区县设置的，是否开启简易登记。
                         *2：默认是不开启的，没有查到，或者没有设置开始，默认移除。
                         */
                        var configset = await configSetBLL.GetEntityByCode(new Model.Param.SystemManage.ConfigSetListParam()
                        {
                            UnitId = operatorInfo.UnitId.Value,
                            UnitType = (UnitTypeEnum)operatorInfo.UnitType,
                            ConfigUnitType = UnitTypeEnum.County,
                            TypeCode = "1003_SFKQSYMLXZ"
                        });
                        if (!(configset != null && configset.Data != null && configset.Data.ConfigValue == "1") || operatorInfo.SchoolProp == SchoolPropEnum.GaoZhong.ParseToInt())
                        {
                            foreach (var item in currentMenu)
                            {
                                menuList.Where(m => m.MenuName == "按目录登记" || m.MenuName == "按目录预约").ForEach(m => m.MenuStatus = 2);
                            }
                        }
                    }
                }
                //第三方统一身份认证，关闭修改密码功能
                if (operatorInfo.IsThirdLogin == 1 && menuList != null && menuList.Count > 0)
                {
                    menuList.Where(m => m.MenuName == "密码修改").ForEach(m => m.MenuStatus = 2);
                    //判断是否存在一个账号跨多个单位，如果存在，增加切换功能

                }
                //只是一般老师，才关闭实验管理模块
                if (operatorInfo.RoleValues == "31" && operatorInfo.IsNeedExperiment == 0 && menuList != null && menuList.Count > 0)
                {
                    menuList.Where(m => m.MenuName == "实验管理").ForEach(m => m.MenuStatus = 2);
                }
                //不存在高中学段的，则关闭高中选科
                if (operatorInfo.SchoolStageList == null || !operatorInfo.SchoolStageList.Contains(SchoolStageEnum.GaoZhong.ParseToInt()))
                {
                    menuList.Where(m => m.MenuName == "高中选科").ForEach(m => m.MenuStatus = 2);
                    entityMenuGao = menuList.Where(m => m.MenuName == "高中选科").FirstOrDefault();
                }
                //这个菜单，学校、区县、市级都有权限。
                if (operatorInfo.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    if (operatorInfo.SchoolStageList == null || !operatorInfo.SchoolStageList.Contains(SchoolStageEnum.GaoZhong.ParseToInt()))
                    {
                        menuList.Where(m => m.MenuName == "高中教育开出率").ForEach(m => m.MenuStatus = 2);
                    }
                    if (operatorInfo.SchoolStageList == null || (operatorInfo.SchoolStageList.Contains(SchoolStageEnum.GaoZhong.ParseToInt()) && operatorInfo.SchoolStageList.Count == 1))
                    {
                        //只有高中，隐藏九年义务
                        menuList.Where(m => m.MenuName == "义务教育开出率").ForEach(m => m.MenuStatus = 2);
                    }
                }
            }
            menuList = menuList.Where(p => p.MenuStatus == StatusEnum.Yes.ParseToInt()).ToList();
            if (operatorInfo.IsSystem != 1)
            {
                TData<List<MenuAuthorizeInfo>> objMenuAuthorize = await menuAuthorizeBLL.GetAuthorizeList(operatorInfo);
                List<long?> authorizeMenuIdList = objMenuAuthorize.Data.Select(p => p.MenuId).ToList();
                menuList = menuList.Where(p => authorizeMenuIdList.Contains(p.Id)).ToList();

                if (entityMenuGao != null)
                {
                    //不存在高中学段的，则关闭高中选科
                    if (operatorInfo.SchoolStageList == null || !operatorInfo.SchoolStageList.Contains(SchoolStageEnum.GaoZhong.ParseToInt()))
                    {
                        var listChilder = menuList.Where(m => m.ParentId == entityMenuGao.ParentId && m.MenuStatus != 2);
                        var listP = menuList.Where(m => m.Id == entityMenuGao.ParentId);
                        //处理父级问题。
                        if (!(listChilder != null && listChilder.Count() > 0))
                        {
                            //父级，二级菜单
                            if (listP != null && listP.Count() > 0)
                            {
                                entityMenuGao = listP.FirstOrDefault();
                                menuList.Remove(entityMenuGao);
                                listP = menuList.Where(m => m.Id == entityMenuGao.ParentId);
                                listChilder = menuList.Where(m => m.ParentId == entityMenuGao.ParentId && m.MenuStatus != 2);
                            }

                            //处理一级菜单
                            if (!(listChilder != null && listChilder.Count() > 0))
                            {
                                if (listP != null && listP.Count() > 0)
                                {
                                    entityMenuGao = listP.FirstOrDefault();
                                    menuList.Remove(entityMenuGao);
                                }
                            }
                        }
                    }
                }
            }
            if (operatorInfo.IsSystem == 1 && GlobalContext.SystemConfig.Debug)
            {
                menuList.Add(new MenuEntity()
                {
                    Id = 16000000061130079,
                    MenuStatus = 1,
                    MenuType = 2,
                    MenuSort = 9999,
                    MenuUrl = "ToolManage/CodeGenerator/CodeGeneratorIndex",
                    Authorize = "tool:codegenerator:view",
                    MenuName = "代码生成",
                    ParentId = 16508640061130070,
                    DisplayLocation = 0,
                    BaseIsDelete = 0,
                    BaseCreateTime = DateTime.Now,
                    BaseCreatorId = 0,
                    BaseModifyTime = DateTime.Now,
                    BaseModifierId = 0,
                    BaseVersion = 0

                });

            }
            ViewBag.MenuList = menuList;
            ViewBag.OperatorInfo = operatorInfo;
            return View();
        }

        [HttpGet]
        public async Task<IActionResult> Welcome()
        {
            OperatorInfo operatorInfo = await Operator.Instance.Current();
            ViewBag.OperatorInfo = operatorInfo;
            ViewBag.Article = await articleCategoryBLL.GetIndexArticle();


            TData<List<MenuEntity>> objMenu = await menuBLL.GetList(null);
            List<MenuEntity> menuList = new List<MenuEntity>();
            if (objMenu.Data != null && objMenu.Data.Count > 0)
            {
                menuList = objMenu.Data.DeepClone();
            }

            if (operatorInfo != null)
            {
                if (operatorInfo.IsSystem != 1)
                {
                    if (operatorInfo.UnitType == UnitTypeEnum.School.ParseToInt() && menuList != null && menuList.Count > 0)
                    {
                        /**
                         *简易模式是否区县开启。
                         */
                        var roleMenu = menuList.Where(m => m.MenuName == "简易模式").ToList();
                        if (roleMenu != null && roleMenu.Count() > 0)
                        {
                            /**
                             *设置是否显示简易模式登记功能
                             *1：获取配置，区县设置的，是否开启简易登记。
                             *2：默认是不开启的，没有查到，或者没有设置开始，默认移除。
                             */
                            var configset = await configSetBLL.GetEntityByCode(new Model.Param.SystemManage.ConfigSetListParam()
                            {
                                UnitId = operatorInfo.UnitId.Value,
                                UnitType = (UnitTypeEnum)operatorInfo.UnitType,
                                ConfigUnitType = UnitTypeEnum.County,
                                TypeCode = "1003_SFKQJYDJ"
                            });
                            if (!(configset != null && configset.Data != null && configset.Data.ConfigValue == "1"))
                            {
                                menuList.Where(m => m.Id == 391881588633243648 || m.ParentId == 391881588633243648).ForEach(m => m.MenuStatus = 2);
                            }
                        }
                        /** 
                         *实验目录是否区县开启。 
                         */
                        var currentMenu = menuList.Where(m => m.MenuName == "按目录登记" || m.MenuName == "按目录预约").ToList();
                        if (currentMenu != null && currentMenu.Count() > 0)
                        {
                            /**
                             *设置是否显示简易模式登记功能
                             *1：获取配置，区县设置的，是否开启简易登记。
                             *2：默认是不开启的，没有查到，或者没有设置开始，默认移除。
                             */
                            var configset = await configSetBLL.GetEntityByCode(new Model.Param.SystemManage.ConfigSetListParam()
                            {
                                UnitId = operatorInfo.UnitId.Value,
                                UnitType = (UnitTypeEnum)operatorInfo.UnitType,
                                ConfigUnitType = UnitTypeEnum.County,
                                TypeCode = "1003_SFKQSYMLXZ"
                            });
                            if (!(configset != null && configset.Data != null && configset.Data.ConfigValue == "1") || operatorInfo.SchoolProp == SchoolPropEnum.GaoZhong.ParseToInt())
                            {
                                foreach (var item in currentMenu)
                                {
                                    menuList.Where(m => m.MenuName == "按目录登记" || m.MenuName == "按目录预约").ForEach(m => m.MenuStatus = 2);
                                }
                            }
                        }
                    }
                    //第三方统一身份认证，关闭修改密码功能
                    if (operatorInfo.IsThirdLogin == 1 && menuList != null && menuList.Count > 0)
                    {
                        menuList.Where(m => m.MenuName == "密码修改").ForEach(m => m.MenuStatus = 2);
                    }
                    //只是一般老师，才关闭实验管理模块
                    if (operatorInfo.RoleValues == "31" && operatorInfo.IsNeedExperiment == 0 && menuList != null && menuList.Count > 0)
                    {
                        menuList.Where(m => m.MenuName == "实验管理").ForEach(m => m.MenuStatus = 2);
                    }

                }
                menuList = menuList.Where(p => p.MenuStatus == StatusEnum.Yes.ParseToInt()).ToList();
                if (operatorInfo.IsSystem != 1)
                {
                    TData<List<MenuAuthorizeInfo>> objMenuAuthorize = await menuAuthorizeBLL.GetAuthorizeList(operatorInfo);
                    List<long?> authorizeMenuIdList = objMenuAuthorize.Data.Select(p => p.MenuId).ToList();
                    menuList = menuList.Where(p => authorizeMenuIdList.Contains(p.Id)).ToList();
                }
            }
            ViewBag.MenuList = menuList;
            ViewBag.OperatorInfo = operatorInfo;

            return View();
        }

        [HttpGet]
        public IActionResult Login()
        {
            if (GlobalContext.SystemConfig.Demo)
            {
                ViewBag.UserName = "admin";
                ViewBag.Password = "123456";
            }
            return View();
        }

        [HttpGet]
        public async Task<IActionResult> LoginOff()
        {
            #region 退出系统
            OperatorInfo user = await Operator.Instance.Current();
            if (user != null)
            {
                // 如果不允许同一个用户多次登录，当用户登出的时候，就不在线了
                if (!GlobalContext.SystemConfig.LoginMultiple)
                {
                    await userBLL.UpdateUser(new UserEntity { Id = user.UserId, IsOnline = 0 });
                }
                string userAgent = _netHelper.GetUserAgent();
                // 登出日志
                await logLoginBLL.SaveForm(new LogLoginEntity
                {
                    LogStatus = OperateStatusEnum.Success.ParseToInt(),
                    Remark = "退出系统",
                    IpAddress = _netHelper.GetIp(),
                    IpLocation = string.Empty,
                    Browser = _netHelper.GetBrowser(userAgent),
                    OS = _netHelper.GetOSVersion(userAgent),
                    ExtraRemark = userAgent,
                    BaseCreatorId = user.UserId
                });

                Operator.Instance.RemoveCurrent();
                new CookieHelper().RemoveCookie("RememberMe");
            }
            #endregion
            if (user != null && user.IsThirdLogin == 1)
            {
                var thirdToken = HttpContext.Session.GetString("third_token");
                if (!string.IsNullOrEmpty(thirdToken))
                {
                    TData<List<AppManageEntity>> obj = await appManagerBLL.GetList(new AppManageListParam() { AppKey = thirdToken });
                    if (obj.Total > 0)
                    {
                        AppManageEntity objEntity = obj.Data.FirstOrDefault();
                        if (!string.IsNullOrEmpty(objEntity.CallBackUrl))
                        {
                            return Redirect(objEntity.CallBackUrl);
                        }
                    }
                }
                var ThirdSSO = Configuration.GetSection("sso").Get<ThirdSSO>();
                if (string.IsNullOrEmpty(ThirdSSO.logout))
                    return View(nameof(Login));
                else
                    return Redirect(ThirdSSO.logout);

                //return Redirect("/AccountThird/LoginOff");
            }
            else
                return View(nameof(Login));
        }

        [HttpGet]
        public IActionResult NoPermission()
        {
            return View();
        }

        public IActionResult NewList()
        {
            return View();
        }

        public IActionResult NewListDetail()
        {
            return View();
        }

        [HttpGet]
        public IActionResult Error(string message)
        {
            ViewBag.Message = message;
            return View();
        }

        [HttpGet]
        public IActionResult Skin()
        {
            return View();
        }
        /// <summary>
        /// 咨询列表
        /// </summary>
        /// <returns></returns>
        public IActionResult ArticelList()
        {
            return View();
        }
        /// <summary>
        /// 超期列表
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> OverDueList()
        {
            OperatorInfo operatorInfo = await Operator.Instance.Current();
            ViewBag.OperatorInfo = operatorInfo;
            ViewBag.UnitTypeId = operatorInfo.UnitType;
            return View();
        }
        /// <summary>
        /// 平台使用情况列表
        /// </summary>
        /// <returns></returns>

        [AuthorizeFilter]
        public IActionResult UseInfoList()
        {
            return View();
        }
        #endregion

        #region 视图页面 新设计202506
        [HttpGet]
        [AuthorizeFilter]
        public async Task<IActionResult> Dashboard()
        {
            OperatorInfo operatorInfo = await Operator.Instance.Current();
            TData<List<MenuEntity>> objMenu = await menuBLL.GetList(null);
            List<MenuEntity> menuList = new List<MenuEntity>();
            if (objMenu.Data != null && objMenu.Data.Count > 0)
            {
                menuList = objMenu.Data.DeepClone();
            }
            MenuEntity entityMenuGao = null;//保存高中选课菜单；
            if (operatorInfo.IsSystem != 1)
            {
                if (operatorInfo.UnitType == UnitTypeEnum.School.ParseToInt() && menuList != null && menuList.Count > 0)
                {
                    /**
                     *简易模式是否区县开启。
                     */
                    var roleMenu = menuList.Where(m => m.MenuName == "简易模式").ToList();
                    if (roleMenu != null && roleMenu.Count() > 0)
                    {
                        /**
                         *设置是否显示简易模式登记功能
                         *1：获取配置，区县设置的，是否开启简易登记。
                         *2：默认是不开启的，没有查到，或者没有设置开始，默认移除。
                         */
                        var configset = await configSetBLL.GetEntityByCode(new Model.Param.SystemManage.ConfigSetListParam()
                        {
                            UnitId = operatorInfo.UnitId.Value,
                            UnitType = (UnitTypeEnum)operatorInfo.UnitType,
                            ConfigUnitType = UnitTypeEnum.County,
                            TypeCode = "1003_SFKQJYDJ"
                        });
                        if (!(configset != null && configset.Data != null && configset.Data.ConfigValue == "1"))
                        {
                            menuList.Where(m => m.Id == 391881588633243648 || m.ParentId == 391881588633243648).ForEach(m => m.MenuStatus = 2);
                        }
                        else
                        {
                            //验证对应的学段是否开启。
                            var configsets = await configSetBLL.GetEntityListByCode(new Model.Param.SystemManage.ConfigSetListParam()
                            {
                                TypeCode = "1003_SFKQJYDJ_",
                                ConfigUnitType = UnitTypeEnum.County,
                                UnitId = operatorInfo.UnitId ?? 0,
                                UnitType = (UnitTypeEnum)operatorInfo.UnitType,
                                ModuleCode = "1003"
                            });
                            if (configsets != null && configsets.Data != null && configsets.Data.Count > 0)
                            {
                                bool isOPen = false;
                                if (operatorInfo.SchoolStageList != null && operatorInfo.SchoolStageList.Count > 0)
                                {
                                    foreach (var item in configsets.Data)
                                    {
                                        if (item.ConfigValue == "1")
                                        {
                                            if (item.TypeCode == "1003_SFKQJYDJ_1GZ")
                                            {
                                                if (operatorInfo.SchoolStageList.Contains(SchoolStageEnum.GaoZhong.ParseToInt()))
                                                {
                                                    isOPen = true;
                                                }
                                            }
                                            else if (item.TypeCode == "1003_SFKQJYDJ_2CZ")
                                            {
                                                if (operatorInfo.SchoolStageList.Contains(SchoolStageEnum.ChuZhong.ParseToInt()))
                                                {
                                                    isOPen = true;
                                                }
                                            }
                                            else if (item.TypeCode == "1003_SFKQJYDJ_3XX")
                                            {
                                                if (operatorInfo.SchoolStageList.Contains(SchoolStageEnum.XiaoXue.ParseToInt()))
                                                {
                                                    isOPen = true;
                                                }
                                            }
                                        }
                                    }
                                }
                                if (!isOPen)
                                {
                                    menuList.Where(m => m.Id == 391881588633243648 || m.ParentId == 391881588633243648).ForEach(m => m.MenuStatus = 2);
                                }
                            }
                        }
                    }
                    /** 
                     *实验目录是否区县开启。 
                     */
                    var currentMenu = menuList.Where(m => m.MenuName == "按目录登记" || m.MenuName == "按目录预约").ToList();
                    if (currentMenu != null && currentMenu.Count() > 0)
                    {
                        /**
                         *设置是否显示简易模式登记功能
                         *1：获取配置，区县设置的，是否开启简易登记。
                         *2：默认是不开启的，没有查到，或者没有设置开始，默认移除。
                         */
                        var configset = await configSetBLL.GetEntityByCode(new Model.Param.SystemManage.ConfigSetListParam()
                        {
                            UnitId = operatorInfo.UnitId.Value,
                            UnitType = (UnitTypeEnum)operatorInfo.UnitType,
                            ConfigUnitType = UnitTypeEnum.County,
                            TypeCode = "1003_SFKQSYMLXZ"
                        });
                        if (!(configset != null && configset.Data != null && configset.Data.ConfigValue == "1") || operatorInfo.SchoolProp == SchoolPropEnum.GaoZhong.ParseToInt())
                        {
                            foreach (var item in currentMenu)
                            {
                                menuList.Where(m => m.MenuName == "按目录登记" || m.MenuName == "按目录预约").ForEach(m => m.MenuStatus = 2);
                            }
                        }
                    }
                }
                //第三方统一身份认证，关闭修改密码功能
                if (operatorInfo.IsThirdLogin == 1 && menuList != null && menuList.Count > 0)
                {
                    menuList.Where(m => m.MenuName == "密码修改").ForEach(m => m.MenuStatus = 2);
                    //判断是否存在一个账号跨多个单位，如果存在，增加切换功能

                }
                //只是一般老师，才关闭实验管理模块
                if (operatorInfo.RoleValues == "31" && operatorInfo.IsNeedExperiment == 0 && menuList != null && menuList.Count > 0)
                {
                    menuList.Where(m => m.MenuName == "实验管理").ForEach(m => m.MenuStatus = 2);
                }
                //不存在高中学段的，则关闭高中选科
                if (operatorInfo.SchoolStageList == null || !operatorInfo.SchoolStageList.Contains(SchoolStageEnum.GaoZhong.ParseToInt()))
                {
                    menuList.Where(m => m.MenuName == "高中选科").ForEach(m => m.MenuStatus = 2);
                    entityMenuGao = menuList.Where(m => m.MenuName == "高中选科").FirstOrDefault();
                }
                //这个菜单，学校、区县、市级都有权限。
                if (operatorInfo.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    if (operatorInfo.SchoolStageList == null || !operatorInfo.SchoolStageList.Contains(SchoolStageEnum.GaoZhong.ParseToInt()))
                    {
                        menuList.Where(m => m.MenuName == "高中教育开出率").ForEach(m => m.MenuStatus = 2);
                    }
                    if (operatorInfo.SchoolStageList == null || (operatorInfo.SchoolStageList.Contains(SchoolStageEnum.GaoZhong.ParseToInt()) && operatorInfo.SchoolStageList.Count == 1))
                    {
                        //只有高中，隐藏九年义务
                        menuList.Where(m => m.MenuName == "义务教育开出率").ForEach(m => m.MenuStatus = 2);
                    }
                }
            }
            menuList = menuList.Where(p => p.MenuStatus == StatusEnum.Yes.ParseToInt()).ToList();
            if (operatorInfo.IsSystem != 1)
            {
                TData<List<MenuAuthorizeInfo>> objMenuAuthorize = await menuAuthorizeBLL.GetAuthorizeList(operatorInfo);
                List<long?> authorizeMenuIdList = objMenuAuthorize.Data.Select(p => p.MenuId).ToList();
                menuList = menuList.Where(p => authorizeMenuIdList.Contains(p.Id)).ToList();

                if (entityMenuGao != null)
                {
                    //不存在高中学段的，则关闭高中选科
                    if (operatorInfo.SchoolStageList == null || !operatorInfo.SchoolStageList.Contains(SchoolStageEnum.GaoZhong.ParseToInt()))
                    {
                        var listChilder = menuList.Where(m => m.ParentId == entityMenuGao.ParentId && m.MenuStatus != 2);
                        var listP = menuList.Where(m => m.Id == entityMenuGao.ParentId);
                        //处理父级问题。
                        if (!(listChilder != null && listChilder.Count() > 0))
                        {
                            //父级，二级菜单
                            if (listP != null && listP.Count() > 0)
                            {
                                entityMenuGao = listP.FirstOrDefault();
                                menuList.Remove(entityMenuGao);
                                listP = menuList.Where(m => m.Id == entityMenuGao.ParentId);
                                listChilder = menuList.Where(m => m.ParentId == entityMenuGao.ParentId && m.MenuStatus != 2);
                            }

                            //处理一级菜单
                            if (!(listChilder != null && listChilder.Count() > 0))
                            {
                                if (listP != null && listP.Count() > 0)
                                {
                                    entityMenuGao = listP.FirstOrDefault();
                                    menuList.Remove(entityMenuGao);
                                }
                            }
                        }
                    }
                }
            }
            if (operatorInfo.IsSystem == 1 && GlobalContext.SystemConfig.Debug)
            {
                menuList.Add(new MenuEntity()
                {
                    Id = 16000000061130079,
                    MenuStatus = 1,
                    MenuType = 2,
                    MenuSort = 9999,
                    MenuUrl = "ToolManage/CodeGenerator/CodeGeneratorIndex",
                    Authorize = "tool:codegenerator:view",
                    MenuName = "代码生成",
                    ParentId = 16508640061130070,
                    DisplayLocation = 0,
                    BaseIsDelete = 0,
                    BaseCreateTime = DateTime.Now,
                    BaseCreatorId = 0,
                    BaseModifyTime = DateTime.Now,
                    BaseModifierId = 0,
                    BaseVersion = 0

                });

            }
            ViewBag.MenuList = menuList;
            ViewBag.OperatorInfo = operatorInfo;
            return View();
        }

        #endregion

        #region 获取数据
        public IActionResult GetCaptchaImage()
        {
            string sessionId = GlobalContext.ServiceProvider?.GetService<IHttpContextAccessor>().HttpContext.Session.Id;
            ValidateCodeHelper codeHelper = new ValidateCodeHelper(1, true);
            byte[] bytes = codeHelper.GetVerifyCodeImage();
            new SessionHelper().WriteSession("CaptchaCode", codeHelper.VerifyCodeResult);
            return File(bytes, @"image/jpeg");
        }


        /// <summary>
        /// 判断是否显示验证码(当前IP访问，如果当天错误>=3次则显示验证码,备注：必须连续出现)
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> IsShowCode()
        {
            //NetHelper.SetConfigHttpContext(HttpContext);
            string ip = _netHelper.GetIp();
            TData obj = await logLoginBLL.VerifiedIsShowCode(ip);
            return Json(obj);
        }


        /// <summary>
        /// 获取客服配置代码信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> GetAccessId()
        {
            string accessId = "";
            var obj = await configSetBLL.GetEntityByCode(new Model.Param.SystemManage.ConfigSetListParam()
            {
                ConfigType = 0,
                TypeCode = "5000_PTKFDM"
            });
            if (obj != null && obj.Data != null && !string.IsNullOrEmpty(obj.Data.ConfigValue) && obj.Data.ConfigValue.Length > 10)
            {
                accessId = obj.Data.ConfigValue;
            }
            return Json(accessId);
        }

        /// <summary>
        /// 获取扫码登录二维码
        /// </summary>
        /// <param name="mobileClientType">移动客户端类型 0: H5；1：微信小程序；2：钉钉小程序；3：企业微信 </param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetQrCode(int mobileClientType)
        {
            TData obj = new TData();
            string token = string.Empty;
            if (new SessionHelper().GetSession("Qr_LoginCode") != null)
            {
                token = JsonConvert.DeserializeObject<string>(new SessionHelper().GetSession("Qr_LoginCode"));
                if (!string.IsNullOrEmpty(token) && token.Length > 0)
                {
                    await userbindBLL.DisableQrcodeLogin(token); //将token改为失效
                }
            }
            token = SecurityHelper.GetGuid(true); //生成新token
            await userbindBLL.SaveQrcodeLogin(token);
            new SessionHelper().WriteSession("Qr_LoginCode", token); //写入Session

            if (mobileClientType == 0)  //H5
            {
                string base64Img = string.Empty;
                base64Img = "data:image/png;base64," + Convert.ToBase64String(QrCodeGenerater.CreateQrCode(token));
                obj.Tag = 1;
                obj.Message = base64Img;
            }
            else if (mobileClientType == 1) //微信
            {
                var result = await WeiXinApi.GetWxacodeUnlimitBase64("pages/scancodelogin/scancodelogin", token);
                if (result.Tag == 1)
                {
                    string base64Img = result.Data;
                    if (!string.IsNullOrEmpty(base64Img))
                    {
                        obj.Tag = 1;
                        obj.Message = base64Img;
                    }
                    else
                    {
                        obj.Tag = 0;
                    }
                }
            }
            return Json(obj);
        }
        #endregion

        #region 提交数据
        [HttpPost]
        public async Task<IActionResult> LoginJson(string userName, string password, string captchaCode = "")
        {
            TData obj = new TData();

            if (string.IsNullOrEmpty(userName) || string.IsNullOrEmpty(password))
            {
                obj.Tag = 0;
                obj.Message = "用户名或密码不能为空";
                return Json(obj);
            }
            //NetHelper.SetConfigHttpContext(HttpContext);
            string ip = _netHelper.GetIp();
            var objLogLogin = await logLoginBLL.VerifiedIsShowCode(ip);
            if (objLogLogin.Tag == 1)
            {
                if (string.IsNullOrEmpty(captchaCode))
                {
                    obj.Message = "请输入验证码";
                    return Json(obj);
                }
                string sessionCode = JsonConvert.DeserializeObject<string>(new SessionHelper().GetSession("CaptchaCode"));
                if (captchaCode != sessionCode)
                {
                    obj.Tag = 5;
                    obj.Message = "验证码错误，请重新输入";
                    return Json(obj);
                }
            }

            TData<UserEntity> userObj = await userBLL.CheckLogin(userName, password, (int)PlatformEnum.Web, 1, ip: ip);
            if (userObj.Tag == 1)
            {
                await new UserBLL().UpdateUser(userObj.Data);
                await Operator.Instance.AddCurrent(userObj.Data.WebToken);
            }

            string userAgent = _netHelper.GetUserAgent();
            string browser = _netHelper.GetBrowser(userAgent);
            string os = _netHelper.GetOSVersion(userAgent);

            Action taskAction = async () =>
            {
                LogLoginEntity logLoginEntity = new LogLoginEntity
                {
                    LogStatus = userObj.Tag == 1 ? OperateStatusEnum.Success.ParseToInt() : OperateStatusEnum.Fail.ParseToInt(),
                    Remark = userObj.Message,
                    IpAddress = ip,
                    IpLocation = IpLocationHelper.GetIpLocation(ip),
                    Browser = browser,
                    OS = os,
                    ExtraRemark = userAgent,
                    BaseCreatorId = userObj.Data?.Id
                };

                // 让底层不用获取HttpContext
                logLoginEntity.BaseCreatorId = logLoginEntity.BaseCreatorId ?? 0;

                await logLoginBLL.SaveForm(logLoginEntity);
            };
            AsyncTaskHelper.StartTask(taskAction);

            obj.Tag = userObj.Tag;
            obj.Message = userObj.Message;
            if (userObj.Tag != 1)
            {
                obj.ExtendData = Url.Content("~/Home/GetCaptchaImage") + "?t=" + DateTimeHelper.GetUnixTimeStamp(DateTime.Now);
            }
            return Json(obj);
        }

        [HttpPost]
        public async Task<IActionResult> TurnUser(long id)
        {
            TData obj = new TData();
            //NetHelper.SetConfigHttpContext(HttpContext);
            string ip = _netHelper.GetIp();
            OperatorInfo operatorInfo = await Operator.Instance.Current();
            bool bCanTurn = false;
            if (operatorInfo != null && operatorInfo.ThirdUserUnitList.Count() > 1)
            {
                if (operatorInfo.ThirdUserUnitList.Any(m => m.Id == id))
                {
                    bCanTurn = true;
                }
            }
            if (!bCanTurn)
            {
                obj.Tag = 0;
                obj.Message = "非法操作，系统阻止。";
                return Json(obj);
            }
            TData<UserEntity> userObj = await userBLL.TurnUserLogin(id, (int)PlatformEnum.Web);
            if (userObj.Tag == 1)
            {
                await new UserBLL().UpdateUser(userObj.Data);
                await Operator.Instance.AddCurrent(userObj.Data.WebToken);
            }

            string userAgent = _netHelper.GetUserAgent();
            string browser = _netHelper.GetBrowser(userAgent);
            string os = _netHelper.GetOSVersion(userAgent);

            Action taskAction = async () =>
            {
                LogLoginEntity logLoginEntity = new LogLoginEntity
                {
                    LogStatus = userObj.Tag == 1 ? OperateStatusEnum.Success.ParseToInt() : OperateStatusEnum.Fail.ParseToInt(),
                    Remark = userObj.Message,
                    IpAddress = ip,
                    IpLocation = IpLocationHelper.GetIpLocation(ip),
                    Browser = browser,
                    OS = os,
                    ExtraRemark = userAgent,
                    BaseCreatorId = userObj.Data?.Id
                };

                // 让底层不用获取HttpContext
                logLoginEntity.BaseCreatorId = logLoginEntity.BaseCreatorId ?? 0;

                await logLoginBLL.SaveForm(logLoginEntity);
            };
            AsyncTaskHelper.StartTask(taskAction);

            obj.Tag = userObj.Tag;
            obj.Message = userObj.Message;

            return Json(obj);
        }


        /// <summary>
        /// 获取微信小程序二维码
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> GetWxacode()
        {
            TData obj = new TData();
            string token = string.Empty;
            if (new SessionHelper().GetSession("Qr_LoginCode") != null)
            {
                token = JsonConvert.DeserializeObject<string>(new SessionHelper().GetSession("Qr_LoginCode"));
                if (!string.IsNullOrEmpty(token) && token.Length > 0)
                {
                    await userbindBLL.DisableQrcodeLogin(token); //将token改为失效
                }
            }
            token = SecurityHelper.GetGuid(true); //生成新token
            await userbindBLL.SaveQrcodeLogin(token);
            new SessionHelper().WriteSession("Qr_LoginCode", token); //写入Session

            var result = await WeiXinApi.GetWxacodeUnlimitBase64("pages/scancodelogin/scancodelogin", token);
            if (result.Tag == 1)
            {
                string base64Img = result.Data;
                if (!string.IsNullOrEmpty(base64Img))
                {
                    obj.Tag = 1;
                    obj.Message = base64Img;
                }
                else
                {
                    obj.Tag = 0;
                }
            }
            return Json(obj);
        }

        /// <summary>
        /// 扫码登录
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> Account_Login_QrCode()
        {
            TData obj = new TData();
            string token = string.Empty;
            if (new SessionHelper().GetSession("Qr_LoginCode") != null)
            {
                token = JsonConvert.DeserializeObject<string>(new SessionHelper().GetSession("Qr_LoginCode"));
                if (token != null && token.Length > 0)
                {
                    var qrcodeLogin = await userbindBLL.QrcodeLogin(token);
                    if (qrcodeLogin.Data == null)
                    {
                        obj.Tag = 0;
                    }
                    else
                    {
                        obj.Tag = 0;
                        if (qrcodeLogin.Data.Statuz == 1)
                        {
                            TData<UserEntity> userObj = await userBLL.GetEntity(qrcodeLogin.Data.UserId.Value);
                            if (userObj.Tag == 1)
                            {
                                if (userObj.Data.UserValidate != null && userObj.Data.UserValidate < DateTime.Now.AddDays(-1))
                                {
                                    obj.Tag = 0;
                                    obj.Message = "您的账号已过有效期！";
                                    return Json(obj);
                                }

                                obj.Tag = 1;
                                await new UserBLL().UpdateUser(userObj.Data);
                                await Operator.Instance.AddCurrent(userObj.Data.WebToken);
                                LogHelper.Info($"用户：{userObj.Data.Id}-{userObj.Data.UserName}扫码登录成功。", null);
                            }
                            else
                            {
                                obj.Tag = 0;
                                obj.Message = "扫码登录失败";
                                LogHelper.Info($"扫码登录失败。", null);
                            }
                        }
                        else
                        {
                            obj.Tag = 0;
                            obj.Message = "二维码已失效";
                        }
                    }
                }
            }

            return Json(obj);
        }
        #endregion

        #region Dashboard 获取接口数据

        /// <summary>
        /// 基础信息数量。
        /// </summary>
        /// <remarks>
        /// Planed:已编实验计划（个）
        /// Recorded:已开出实验（个）
        /// InstrumentNum:仪器存量（件）
        /// FunroomNum:实验室数量（个）
        /// ExperimenterUserNum:实验管理员（人）
        /// </remarks>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> BaseInfoNum()
        {
            TData<object> result = new TData<object>();
            int planedNum = 0;
            var planed = await workBLL.GetPlanedList(new Model.Param.ExperimentTeachManage.PlanInfoListParam() { });
            if (planed != null)
            {
                planedNum = planed.Sum(m => m.Num);
            }
            int recorded = 0;
            var record = await workBLL.GetRecordList(new ExperimentBookingListParam() { });
            if (record != null)
            {
                recorded = record.Count;
            }
            decimal instrumentNum = 0;
            var instrument = await workBLL.GetInstrumentList(new Model.Param.QueryStatisticsManage.InstrumentParam() { });
            if (instrument > 0)
            {
                instrumentNum = instrument;
            }
            int funroomNum = 0;
            var funroom = await workBLL.GetFunroomNum(new Model.Param.BusinessManage.FunRoomListParam() { });
            if (funroom != null)
            {
                funroomNum = funroom.FunRoomCount;
            }
            int usernum = 0;
            var experimenterUser = await workBLL.GetExperimenterUserList(new Model.Param.BusinessManage.UserExperimenterSetListParam() { });
            if (experimenterUser != null)
            {
                usernum = experimenterUser.Count;
            }
            int exprimentBookingNum = 0;
            var  experimentBooking = await workBLL.GetExperimentBookingBookingList(new ExperimentBookingListParam() { });
            if (experimentBooking!=null)
            {
                exprimentBookingNum = experimentBooking.Count;
            }
            result.Data = new
            {
                Planed = planedNum,
                Recorded = recorded,
                InstrumentNum = instrumentNum,
                ExprimentBookingNum = exprimentBookingNum,
                FunroomNum = funroomNum,
                ExperimenterUserNum = usernum,
            };
            return Json(result);
        }

        /// <summary>
        /// 待办事项数量
        /// </summary>
        /// <remarks>
        /// WaitArrangerNum:待安排实验数量
        /// WaitRecordNum:需登记数量
        /// WaitBackNum:待归还仪器数量
        /// WaitPurchaseAuditNum:采购待审批数量
        /// WaitBookingNum:有几节实验待预约
        /// </remarks>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> WaitDoNum()
        {
            TData<object> result = new TData<object>();
            int waitArrangerNum = 0;
            var waitArrange = await workBLL.GetArrangeList(new ExperimentBookingListParam() { });
            if (waitArrange != null)
            {
                waitArrangerNum = waitArrange.Count;
            }
            int waitRecordNum = 0;
            var waitRecord = await workBLL.GetWaitRecordList(new ExperimentBookingListParam() { });
            if (waitRecord != null)
            {
                waitRecordNum = waitRecord.Count;
            }
            int waitBackNum = 0;
            var waitBack = await workBLL.GetLendInstrumentList(new Model.Param.InstrumentManage.InstrumentLendListParam() { Statuz = 20 });//待归还
            if (waitBack != null)
            {
                waitBackNum = waitBack.Count;
            }
            int waitPurchaseAuditNum = 0;
            var waitPurchase = await workBLL.GetWaitApproveInstrumentList(new Model.Param.InstrumentManage.PurchaseDeclarationListParam() { });
            if (waitPurchase > 0)
            {
                waitPurchaseAuditNum = waitPurchase;
            }
            int waitBookingNum = 0;
            var waitBooking = await workBLL.GetBookingWeekList(new PlanDetailListParam() { });
            if (waitBooking > 0)
            {
                waitBookingNum = waitBooking;
            }
            int waitPlanNum = 0;
            var waitPlan = await workBLL.GetWaitPlanExperimentNum();
            if (waitPlan > 0)
            {
                waitPlanNum = waitPlan;
            }
            result.Data = new
            {
                WaitArrangerNum = waitArrangerNum,
                WaitRecordNum = waitRecordNum,
                WaitBackNum = waitBackNum,
                WaitPurchaseAuditNum = waitPurchaseAuditNum,
                WaitBookingNum = waitBookingNum,
                WaitPlanNum = waitPlanNum
            };
            return Json(result);
        }

        /// <summary>
        /// 开出超期
        /// </summary>
        /// <param name="schoolstageid"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> GetOverDue()
        {
            TData<List<UnitStatisticModel>> result = new TData<List<UnitStatisticModel>>();
            var param = new Model.Param.OrganizationManage.UnitListParam();
            OperatorInfo user = await Operator.Instance.Current();
            if (user.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                var list = await workBLL.GetOverDueList(param);
                if (list != null)
                {
                    result.Data= list.OrderByDescending(m => m.ExperimentNum).ThenBy(m=>m.ClassId).Take(10).ToList();
                }
            }
            else
            {
                var list = await workBLL.GetUnitSchoolOverDueList(param);
                if (list != null)
                {
                    result.Data = list.OrderByDescending(m => m.ExperimentNum).ThenBy(m => m.GradeId).Take(10).ToList();
                }
            }
            result.Tag = 1;
            result.Message = "查询成功";
            return Json(result);
        }

        /// <summary>
        /// 实验开出率
        /// </summary>
        /// <remarks>
        /// </remarks>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> ExpertmentRecordRatio(int schoolstageid)
        {
            TData<object> result = new TData<object>();
            OperatorInfo user = await Operator.Instance.Current();
            var param = new PlanExamListParameterListParam();
            param.SchoolStage = schoolstageid;
            param.SchoolStageList = new List<int>() { schoolstageid };
            param.SchoolYearStart = user.SchoolTermStartYear;
            param.SchoolTerm = user.SchoolTerm;
            result = await workBLL.GetExperimentRatioList(param);

            result.Tag = 1;
            result.Message = "查询成功。";
            return Json(result);
        }

        /// <summary>
        /// 平台使用情况表
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<ActionResult> UseInfo(UnitListParam param)
        {
            TData<List<UnitStatisticModel>> result = new TData<List<UnitStatisticModel>>();
            var list = await workBLL.GetUnitSchoolStageList(param);
            result.Data = list;
            result.Tag = 1;
            result.Message = "查询成功";
            return Json(result);
        }


        /// <summary>
        /// 平台使用情况详情表
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination">这个参数未使用，因框架列表查询带该参数。</param>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> UseInfoDetail(UnitListParam param, Pagination pagination)
        {
            TData<List<UnitStatisticModel>> result = new TData<List<UnitStatisticModel>>();
            var list = await workBLL.GetUnitSchoolStageDetailList(param);
            if (list != null)
            {
                int statrt = (pagination.PageIndex - 1) * pagination.PageSize;
                result.Data = list.OrderBy(m=>m.Sort).ThenBy(m=>m.SchoolStageId).Skip(statrt).Take(pagination.PageSize).ToList();
                result.Total = list.Count;
            }
            result.Tag = 1;
            result.Message = "查询成功";
            return Json(result);
        }
        #endregion

        #region 首页基础信息

        /// <summary>
        /// 获取学段
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> GetSchoolStageJson()
        {
            TData<object> obj = new TData<object>();
            OperatorInfo operatorinfo = await Operator.Instance.Current();
            var param = new Model.Param.SystemManage.StaticDictionaryListParam();
            param.TypeCode = DicTypeCodeEnum.SchoolStage.ParseToInt().ToString();
            var result = await staticDictionaryBll.GetList(param);
            if (result != null && result.Tag == 1 && result.Data != null)
            {
                obj.Tag = 1;
                obj.Message = "查询成功！";
                var list = (from item in result.Data
                            select new
                            {
                                DictionaryId = item.DictionaryId,
                                DicName = item.DicName,
                                Sort = item.Sequence
                            });
                obj.Data = list;
            }
            return Json(obj);
        }

        [HttpGet]
        public async Task<ActionResult> GetArticleJson()
        {
            int cateogrynum = 3;//显示3条
            int articlenum = 13;//显示最新13条
            var list = await articleCategoryBLL.GetArticle(cateogrynum, articlenum);
            return Json(list);
        }

        /// <summary>
        /// 根据分类获取资讯Id
        /// </summary>
        /// <param name="param">传递分类：Cid：</param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<ActionResult> GetArticlePageJson(ArticleListParam param, Pagination pagination)
        {
            TData<object> obj = new TData<object>();
            param.UnitId = 100000000000000001;
            param.Statuz = 2;//已发布审核
            pagination.Sort = "BaseModifyTime";
            pagination.SortType = "Desc";
            var list = await articleCategoryBLL.GetPageArticleByCidList(param, pagination);
            if (list != null && list.Count > 0)
            {
                obj.Total = pagination.TotalPage;
                obj.Data = list;
                obj.Tag = 1;
                obj.Message = "查询成功";
            }
            else
            {
                obj.Tag = 1;
                obj.Message = "未查询到数据";
            }
            return Json(obj);
        }

        /// <summary>
        /// 获取当前学期、第几周、
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> GetTermInfoJson()
        {
            var obj = new TData<object>();
            int schoolYearStart = 0;
            int schoolYearEnd = 0;
            int schoolTerm = 1;
            DateTime termStart = DateTime.MinValue;
            DateTime termEnd = DateTime.MinValue;
            int weekNum = 0;
            TData<SchoolTermEntity> termObj = await schoolTermBLL.GetDefaultSchoolTerm();
            if (termObj.Tag == 1 && termObj.Data != null)
            {
                schoolYearStart = termObj.Data.SchoolYearStart;
                schoolYearEnd = termObj.Data.SchoolYearEnd;
                schoolTerm = termObj.Data.SchoolTerm;
                termStart = termObj.Data.TermStart;
                termEnd = termObj.Data.TermEnd;
                weekNum = DateTimeHelper.GetDayWeekNnum(termObj.Data.FirstWeekDate, DateTime.Now);
            }
            //计算周次
            obj.Data = new
            {
                SchoolYearStart = schoolYearStart,//开始学期
                SchoolYearEnd = schoolYearEnd,//结束学期
                SchoolTerm = schoolTerm,//学期 1：上学期  2：下学期
                TermStart = termStart.ToString("yyyy-MM-dd"),//学期结束日期
                TermEnd = termEnd.ToString("yyyy-MM-dd"),//学期结束日期
                WeekNum = weekNum,//第几周，
                CurrentDateTime = DateTime.Now.ToString("yyyy-MM-dd"),//当前日期
                DayWeekName = DateTime.Now.Date.DayOfWeek//当前星期几
            };
            return Json(obj);
        }

        /// <summary>
        /// 获取超期数据集合
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> GetOverDuePageJson(UnitListParam param, Pagination pagination)
        {
            TData<List<UnitStatisticModel>> result = new TData<List<UnitStatisticModel>>();
            OperatorInfo user = await Operator.Instance.Current();
            var start = (pagination.PageIndex - 1) * pagination.PageSize;
            if (user.UnitType == UnitTypeEnum.School.ParseToInt())
            {
                var list = await workBLL.GetOverDueList(param);
                result.Data = list.Where(m => (!string.IsNullOrEmpty(param.Name) && m.CourseName != null) ? m.CourseName.Contains(param.Name) : true)
                    .OrderByDescending(item => item.ExperimentNum)
                    .ThenBy(item => item.CourseId)
                    .ThenBy(item => item.GradeId)
                    .ThenBy(item => item.ClassId)
                    .Skip(start).Take(pagination.PageSize).ToList();
                result.Total = list.Where(m => (!string.IsNullOrEmpty(param.Name) && m.CourseName != null) ? m.CourseName.Contains(param.Name) : true).Count();
            }
            else
            {
                var list = await workBLL.GetUnitSchoolOverDueList(param);
                result.Data = list.Where(m => (!string.IsNullOrEmpty(param.Name) && m.Name != null) ? m.Name.Contains(param.Name) : true)
                    .OrderByDescending(item => item.ExperimentNum)
                    .ThenBy(item => item.Id)
                    .ThenBy(item => item.CourseId)
                    .ThenBy(item => item.GradeId)
                    .Skip(start).Take(pagination.PageSize).ToList();
                result.Total = list.Where(m => (!string.IsNullOrEmpty(param.Name) && m.Name != null) ? m.Name.Contains(param.Name) : true).Count();
            }
            result.Tag = 1;
            result.Message = "查询成功";
            return Json(result);
        }

        #endregion
    }
}
