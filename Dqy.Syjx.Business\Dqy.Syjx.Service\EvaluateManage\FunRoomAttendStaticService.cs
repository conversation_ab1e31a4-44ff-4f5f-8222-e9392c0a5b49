﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.EvaluateManage;
using Dqy.Syjx.Model.Param.EvaluateManage;
using Dqy.Syjx.Enum;
using System.Data;
using Dqy.Syjx.Service.OrganizationManage;
using Microsoft.Extensions.Primitives;

namespace Dqy.Syjx.Service.EvaluateManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2022-01-05 16:46
    /// 描 述：服务类
    /// </summary>
    public class FunRoomAttendStaticService :  RepositoryFactory
    {
        private UserSubjectSetService userSubjectSetService = new UserSubjectSetService();

        #region 获取数据

        public async Task<List<FunRoomAttendStaticEntity>> GetList(FunRoomAttendStaticParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        /// <summary>
        /// 获取达标结果分析数据
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<List<FunRoomAnalysisEntity>> GetStandardAnalysisList(FunRoomAttendStaticParam param)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = AnalysisListFilter(param, strSql," CourseId  ,CourseName ,COUNT(CourseId) AS StatisticsCount", "CourseId ,CourseName");
            var list = await this.BaseRepository().FindList<FunRoomAnalysisEntity>(strSql.ToString(), filter.ToArray());
            return list.ToList();
        }


        /// <summary>
        /// 获取区县实验专用室达标结果分析数据
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<List<FunRoomAnalysisEntity>> GetCountyStandardResultAnalyseList(FunRoomAttendStaticParam param)
        {
            StringBuilder sql = new StringBuilder();
            sql.Append(@$" SELECT *,
                                  CASE WHEN NeedStandardCount > 0
                                       THEN CAST((CAST(RoomStandardCount AS DECIMAL(18,2)) / CAST(NeedStandardCount AS DECIMAL(18,2)) * 100) AS DECIMAL(18,2))
                                       ELSE 0 END AS RoomStandardRate,
                                  CASE WHEN NeedStandardCount > 0
                                       THEN CAST((CAST(AreaStandardCount AS DECIMAL(18,2)) / CAST(NeedStandardCount AS DECIMAL(18,2)) * 100) AS DECIMAL(18,2))
                                       ELSE 0 END AS AreaStandardRate
			                    FROM (
                                        SELECT  S.FunRoomEvaluateProjectId ,S.OneClassId ,S.StandardLevel ,S.StageId ,S.CourseId,SD.DicName AS StageName ,SD2.DicName AS CourseName ,UR.UnitId AS CountyId ,
		                                COUNT(1) AS NeedStandardCount ,
							            ( SELECT  COUNT(1)
			                              FROM    bn_FunRoomAttendStatic AS FRS
			                              INNER	 JOIN up_UnitRelation as UUR ON FRS.SchoolId = UUR.ExtensionObjId AND UUR.ExtensionType = 3 AND UUR.BaseIsDelete = 0
			                              WHERE  FRS.BaseIsDelete = 0 AND UUR.UnitId = UR.UnitId AND FRS.FunRoomEvaluateProjectId = S.FunRoomEvaluateProjectId
				                          AND FRS.StageId = S.StageId AND FRS.CourseId = S.CourseId AND FRS.FunRoomNum >= FRS.StandardNum ) AS RoomStandardCount,
							            ( SELECT  COUNT(1)
			                              FROM    bn_FunRoomAttendStatic AS FRS2
			                              INNER	 JOIN up_UnitRelation as UUR2 ON FRS2.SchoolId = UUR2.ExtensionObjId AND UUR2.ExtensionType = 3 AND UUR2.BaseIsDelete = 0
			                              WHERE  FRS2.BaseIsDelete = 0 AND UUR2.UnitId = UR.UnitId AND FRS2.FunRoomEvaluateProjectId = S.FunRoomEvaluateProjectId
				                          AND FRS2.StageId = S.StageId AND FRS2.CourseId = S.CourseId AND FRS2.FunRoomArea >= FRS2.StandardArea ) AS AreaStandardCount
                            FROM  bn_FunRoomAttendStatic AS S
                            INNER JOIN  up_Unit AS U ON S.SchoolId = U.Id
                            INNER JOIN  sys_static_dictionary AS SD ON S.StageId = SD.DictionaryId AND SD.TypeCode = '1002' AND SD.BaseIsDelete = 0
                            INNER JOIN  sys_static_dictionary AS SD2 ON S.CourseId = SD2.DictionaryId AND SD2.TypeCode = '1005' AND SD2.BaseIsDelete = 0
                            INNER JOIN  sys_static_dictionary AS SD3 ON S.OneClassId = SD3.DictionaryId AND SD3.TypeCode = '1006' AND SD3.BaseIsDelete = 0
                            INNER JOIN  up_UnitRelation AS UR ON UR.ExtensionObjId = S.SchoolId AND UR.ExtensionType = 3 AND UR.BaseIsDelete = 0
                            WHERE UR.UnitId = {param.CountyId} AND 1=1");
            var parameter = new List<DbParameter>();
            if (parameter != null)
            {
                if (!param.FunRoomEvaluateProjectId.IsNullOrZero())
                {
                    sql.Append($" AND FunRoomEvaluateProjectId = {param.FunRoomEvaluateProjectId}");
                }
                if (!param.SchoolStageId.IsNullOrZero())
                {
                    sql.Append($" AND StageId = {param.SchoolStageId}");
                }
                if (!param.Level.IsNullOrZero())
                {
                    sql.Append($" AND StandardLevel = {param.Level}");
                }
                if (!param.OneClassId.IsNullOrZero())
                {
                    sql.Append($" AND OneClassId = {param.OneClassId}");
                }

                if (param.SetUserId > 0)
                {
                    //判断用户是否设置了学段、学科
                    var listStage = userSubjectSetService.GetUserSetStageList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listStage.Count > 0)
                    {
                        string stageId = string.Join(",", listStage.Select(f => f.DictionaryId));
                        sql.Append($" AND StageId IN ({stageId})");
                    }

                    var listCourse = userSubjectSetService.GetUserSetCourseList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listCourse.Count > 0)
                    {
                        string courseId = string.Join(",", listCourse.Select(f => f.DictionaryId));
                        sql.Append($" AND CourseId IN ({courseId})");
                    }
                }
            }

            sql.Append(" GROUP BY S.FunRoomEvaluateProjectId ,UR.UnitId ,S.StageId ,SD.DicName ,S.CourseId ,SD2.DicName ,S.OneClassId ,S.StandardLevel");
            sql.Append(" ) T");
            sql.Append(" ORDER BY CountyId ASC,StageId ASC, CourseId ASC");
            var list = await this.BaseRepository().FindList<FunRoomAnalysisEntity>(sql.ToString());
            return list.ToList();
        }

        /// <summary>
        /// 获取市级实验专用室达标结果分析数据
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<List<FunRoomAnalysisEntity>> GetCityStandardResultAnalyseList(FunRoomAttendStaticParam param)
        {
            StringBuilder sql = new StringBuilder();
            sql.AppendFormat(@" SELECT *,
                                  CASE WHEN NeedStandardCount > 0
                                       THEN CAST((CAST(RoomStandardCount AS DECIMAL(18,2)) / CAST(NeedStandardCount AS DECIMAL(18,2)) * 100) AS DECIMAL(18,2))
                                       ELSE 0 END AS RoomStandardRate,
                                  CASE WHEN NeedStandardCount > 0
                                       THEN CAST((CAST(AreaStandardCount AS DECIMAL(18,2)) / CAST(NeedStandardCount AS DECIMAL(18,2)) * 100) AS DECIMAL(18,2))
                                       ELSE 0 END AS AreaStandardRate
			                    FROM (
                                        SELECT  UR2.UnitId AS CityId ,S.FunRoomEvaluateProjectId ,S.OneClassId ,S.StandardLevel ,S.StageId ,S.CourseId,SD.DicName AS StageName ,SD2.DicName AS CourseName ,
		                                COUNT(1) AS NeedStandardCount ,
							            ( SELECT  COUNT(1)
			                               FROM    bn_FunRoomAttendStatic AS FRS
			                              INNER	 JOIN up_UnitRelation as UUR ON FRS.SchoolId = UUR.ExtensionObjId AND UUR.ExtensionType = 3 AND UUR.BaseIsDelete = 0
										  INNER  JOIN  up_UnitRelation AS UUR2 ON UUR.UnitId = UUR2.ExtensionObjId AND UUR2.ExtensionType = 3 AND UUR2.BaseIsDelete = 0
			                              WHERE  FRS.BaseIsDelete = 0 AND UUR2.UnitId = UR2.UnitId AND FRS.FunRoomEvaluateProjectId = S.FunRoomEvaluateProjectId
				                          AND FRS.StageId = S.StageId AND FRS.CourseId = S.CourseId AND FRS.FunRoomNum >= FRS.StandardNum {0}) AS RoomStandardCount,
							            ( SELECT  COUNT(1)
			                              FROM    bn_FunRoomAttendStatic AS FRS2
			                              INNER	 JOIN up_UnitRelation as UUR ON FRS2.SchoolId = UUR.ExtensionObjId AND UUR.ExtensionType = 3 AND UUR.BaseIsDelete = 0
										  INNER  JOIN  up_UnitRelation AS UUR2 ON UUR.UnitId = UUR2.ExtensionObjId AND UUR2.ExtensionType = 3 AND UUR2.BaseIsDelete = 0
			                              WHERE  FRS2.BaseIsDelete = 0 AND UUR2.UnitId = UR2.UnitId AND FRS2.FunRoomEvaluateProjectId = S.FunRoomEvaluateProjectId
				                          AND FRS2.StageId = S.StageId AND FRS2.CourseId = S.CourseId AND FRS2.FunRoomArea >= FRS2.StandardArea {1}) AS AreaStandardCount
                            FROM  bn_FunRoomAttendStatic AS S
                            INNER JOIN  up_Unit AS U ON S.SchoolId = U.Id
                            INNER JOIN  sys_static_dictionary AS SD ON S.StageId = SD.DictionaryId AND SD.TypeCode = '1002' AND SD.BaseIsDelete = 0
                            INNER JOIN  sys_static_dictionary AS SD2 ON S.CourseId = SD2.DictionaryId AND SD2.TypeCode = '1005' AND SD2.BaseIsDelete = 0
                            INNER JOIN  sys_static_dictionary AS SD3 ON S.OneClassId = SD3.DictionaryId AND SD3.TypeCode = '1006' AND SD3.BaseIsDelete = 0
                            INNER JOIN  up_UnitRelation AS UR ON UR.ExtensionObjId = S.SchoolId AND UR.ExtensionType = 3 AND UR.BaseIsDelete = 0
							INNER JOIN  up_UnitRelation AS UR2 ON UR.UnitId = UR2.ExtensionObjId AND UR2.ExtensionType = 3 AND UR2.BaseIsDelete = 0
                            WHERE UR2.UnitId = {2}", !param.CountyId.IsNullOrZero() ? $" AND UUR.UnitId = {param.CountyId}" : "", !param.CountyId.IsNullOrZero() ? $" AND UUR.UnitId = {param.CountyId}" : "", param.CityId);
            var parameter = new List<DbParameter>();
            if (parameter != null)
            {
                if (!param.CountyId.IsNullOrZero() && param.CountyId > 0)
                {
                    sql.Append($" AND UR.UnitId = {param.CountyId}");
                }
                if (!param.FunRoomEvaluateProjectId.IsNullOrZero() && param.FunRoomEvaluateProjectId > 0 )
                {
                    sql.Append($" AND FunRoomEvaluateProjectId = {param.FunRoomEvaluateProjectId}");
                }
                if (!param.SchoolStageId.IsNullOrZero() && param.SchoolStageId > 0)
                {
                    sql.Append($" AND StageId = {param.SchoolStageId}");
                }
                if (!param.Level.IsNullOrZero() && param.Level > 0)
                {
                    sql.Append($" AND StandardLevel = {param.Level}");
                }
                if (!param.OneClassId.IsNullOrZero() && param.OneClassId > 0)
                {
                    sql.Append($" AND OneClassId = {param.OneClassId}");
                }
            }

            sql.Append(" GROUP BY S.FunRoomEvaluateProjectId ,UR2.UnitId ,S.StageId ,SD.DicName ,S.CourseId ,SD2.DicName ,S.OneClassId ,S.StandardLevel");
            sql.Append(" ) T");
            sql.Append(" ORDER BY StageId ASC, CourseId ASC");
            var list = await this.BaseRepository().FindList<FunRoomAnalysisEntity>(sql.ToString());
            return list.ToList();
        }

        public async Task<List<FunRoomAttendStaticEntity>> GetPageList(FunRoomAttendStaticParam param, Pagination pagination)
        {
            StringBuilder sql = new StringBuilder();
            sql.Append(@$" SELECT * From (
                            SELECT  S.Id ,
                            S.BaseIsDelete ,S.BaseCreateTime ,S.BaseModifyTime ,S.BaseCreatorId ,S.BaseModifierId ,S.BaseVersion ,
                            S.FunRoomEvaluateProjectId ,S.SchoolId ,S.OneClassId ,S.TwoClassId ,
                            S.TargetName ,S.StageId ,S.CourseId ,S.StandardNum ,S.FunRoomNum ,S.StandardArea ,S.FunRoomArea ,S.StandardLevel ,S.Remark ,
                            (S.FunRoomNum - S.StandardNum) AS RoomNumDifference ,(S.FunRoomArea - S.StandardArea) AS AreaDifference ,
		                    SD.DicName AS StageName ,SD2.DicName AS CourseName ,
		                    SD3.DicName AS OneClassName ,SD4.DicName AS TwoClassName ,
		                    U.Name AS SchoolName ,UR.UnitId AS CountyId ,U.Sort ,
                            sa.AreaName AS CountyName ,
							UR2.UnitId AS CityId
                            FROM  bn_FunRoomAttendStatic AS S
                            INNER JOIN  up_Unit AS U ON S.SchoolId = U.Id
                            INNER JOIN  sys_static_dictionary AS SD ON S.StageId = SD.DictionaryId
                                                                    AND SD.TypeCode = '{DicTypeCodeEnum.SchoolStage.ParseToInt()}' AND SD.BaseIsDelete = 0
                            INNER JOIN  sys_static_dictionary AS SD2 ON S.CourseId = SD2.DictionaryId
                                                                    AND SD2.TypeCode = '{DicTypeCodeEnum.Course.ParseToInt()}' AND SD2.BaseIsDelete = 0
                            INNER JOIN  sys_static_dictionary AS SD3 ON S.OneClassId = SD3.DictionaryId
                                                                    AND SD3.TypeCode = '{DicTypeCodeEnum.FunRoomClass.ParseToInt()}' AND SD3.BaseIsDelete = 0
                            INNER JOIN  sys_static_dictionary AS SD4 ON S.TwoClassId = SD4.DictionaryId
                                                                    AND SD4.TypeCode = '{DicTypeCodeEnum.FunRoomClass.ParseToInt()}' AND SD4.BaseIsDelete = 0
                            INNER JOIN  up_UnitRelation AS UR ON UR.ExtensionObjId = S.SchoolId AND UR.ExtensionType = 3 AND UR.BaseIsDelete = 0
                            INNER JOIN  up_UnitRelation AS UR2 ON UR.UnitId = UR2.ExtensionObjId AND UR2.ExtensionType = 3 AND UR2.BaseIsDelete = 0
							INNER JOIN  up_Unit AS u2 ON ur.UnitId = u2.Id AND u2.BaseIsDelete = 0
							LEFT JOIN  SysArea AS sa ON sa.AreaCode = u2.AreaId AND sa.BaseIsDelete = 0
                          ) as T WHERE  1 = 1 ");
            var parameter = new List<DbParameter>();
            if (parameter != null)
            {
                if (!param.SchoolId.IsNullOrZero())
                {
                    sql.Append(" AND SchoolId = @SchoolId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
                }
                if (!param.CountyId.IsNullOrZero())
                {
                    sql.Append(" AND CountyId = @CountyId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
                if (!param.CityId.IsNullOrZero())
                {
                    sql.Append(" AND CityId = @CityId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CityId", param.CityId));
                }
                if (!param.FunRoomEvaluateProjectId.IsNullOrZero())
                {
                    sql.Append(" AND FunRoomEvaluateProjectId = @FunRoomEvaluateProjectId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@FunRoomEvaluateProjectId", param.FunRoomEvaluateProjectId));
                }
                if (!param.SchoolStageId.IsNullOrZero())
                {
                    sql.Append(" AND StageId = @StageId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@StageId", param.SchoolStageId));
                }
                if (!param.CourseId.IsNullOrZero())
                {
                    sql.Append(" AND CourseId = @CourseId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (!param.Level.IsNullOrZero())
                {
                    sql.Append(" AND StandardLevel = @StandardLevel");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@StandardLevel", param.Level));
                }
                if (!param.OneClassId.IsNullOrZero())
                {
                    sql.Append(" AND OneClassId = @OneClassId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@OneClassId", param.OneClassId));
                }
                if (!param.KeyWord.IsEmpty())
                {
                    sql.Append($" AND (TargetName LIKE '%{param.KeyWord}%') ");
                }
                if (param.IsOnlyShowNoStandard == 1)
                {
                    sql.Append(" AND (RoomNumDifference < 0 OR AreaDifference < 0) ");
                }
                if (!param.CourseName.IsEmpty())
                {
                    sql.Append(" AND CourseName = @CourseName");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseName", param.CourseName));
                }
                if (!param.TwoClassName.IsEmpty())
                {
                    sql.Append(" AND TwoClassName = @TwoClassName");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@TwoClassName", param.TwoClassName));
                }

                if (param.SetUserId > 0)
                {
                    //判断用户是否设置了学段、学科
                    var listStage = userSubjectSetService.GetUserSetStageList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listStage.Count > 0)
                    {
                        sql.Append($" AND ({string.Join(" OR ", listStage.Select(f => string.Format(" StageId = {0} ", f.DictionaryId)))})");
                    }

                    var listCourse = userSubjectSetService.GetUserSetCourseList(param.SetUserId).Result.Where(f => f.UserSubjectSetId > 0).ToList();
                    if (listCourse.Count > 0)
                    {
                        sql.Append($" AND ({string.Join(" OR ", listCourse.Select(f => string.Format(" CourseId = {0} ", f.DictionaryId)))})");
                    }
                }
            }
            var list = await this.BaseRepository().FindList<FunRoomAttendStaticEntity>(sql.ToString(), parameter.ToArray(), pagination);
            return list.ToList();
        }


        public async Task<List<FunRoomAnalysisEntity>> GetCityPageList(FunRoomAttendStaticParam param, Pagination pagination)
        {
            StringBuilder sql = new StringBuilder();
            sql.AppendFormat(@" SELECT *,
                                  CASE WHEN NeedStandardCount > 0
                                       THEN CAST((CAST(RoomStandardCount AS DECIMAL(18,2)) / CAST(NeedStandardCount AS DECIMAL(18,2)) * 100) AS DECIMAL(18,2))
                                       ELSE 0 END AS RoomStandardRate,
                                  CASE WHEN NeedStandardCount > 0
                                       THEN CAST((CAST(AreaStandardCount AS DECIMAL(18,2)) / CAST(NeedStandardCount AS DECIMAL(18,2)) * 100) AS DECIMAL(18,2))
                                       ELSE 0 END AS AreaStandardRate
			                    FROM (
                                        SELECT  S.TargetName ,UR2.UnitId AS CityId ,UR.UnitId AS CountyId ,sa.AreaName AS CountyName ,S.FunRoomEvaluateProjectId ,S.OneClassId ,SD3.DicName AS OneClassName ,S.TwoClassId ,S.StandardLevel ,S.StageId ,S.CourseId,SD.DicName AS StageName ,SD2.DicName AS CourseName ,
		                                COUNT(1) AS NeedStandardCount ,
							            ( SELECT  COUNT(1)
			                               FROM    bn_FunRoomAttendStatic AS FRS
			                              INNER	 JOIN up_UnitRelation as UUR ON FRS.SchoolId = UUR.ExtensionObjId AND UUR.ExtensionType = 3 AND UUR.BaseIsDelete = 0
										  INNER  JOIN  up_UnitRelation AS UUR2 ON UUR.UnitId = UUR2.ExtensionObjId AND UUR2.ExtensionType = 3 AND UUR2.BaseIsDelete = 0
			                              WHERE  FRS.BaseIsDelete = 0 AND UUR2.UnitId = UR2.UnitId AND FRS.FunRoomEvaluateProjectId = S.FunRoomEvaluateProjectId
										  AND UUR.UnitId = ur.UnitId
				                          AND FRS.StageId = S.StageId AND FRS.CourseId = S.CourseId AND FRS.FunRoomNum >= FRS.StandardNum ) AS RoomStandardCount,
							            ( SELECT  COUNT(1)
			                              FROM    bn_FunRoomAttendStatic AS FRS2
			                              INNER	 JOIN up_UnitRelation as UUR ON FRS2.SchoolId = UUR.ExtensionObjId AND UUR.ExtensionType = 3 AND UUR.BaseIsDelete = 0
										  INNER  JOIN  up_UnitRelation AS UUR2 ON UUR.UnitId = UUR2.ExtensionObjId AND UUR2.ExtensionType = 3 AND UUR2.BaseIsDelete = 0
			                              WHERE  FRS2.BaseIsDelete = 0 AND UUR2.UnitId = UR2.UnitId AND FRS2.FunRoomEvaluateProjectId = S.FunRoomEvaluateProjectId
										  AND UUR.UnitId = ur.UnitId
				                          AND FRS2.StageId = S.StageId AND FRS2.CourseId = S.CourseId AND FRS2.FunRoomArea >= FRS2.StandardArea ) AS AreaStandardCount
                            FROM  bn_FunRoomAttendStatic AS S
                            INNER JOIN  up_Unit AS U ON S.SchoolId = U.Id
                            INNER JOIN  sys_static_dictionary AS SD ON S.StageId = SD.DictionaryId AND SD.TypeCode = '1002' AND SD.BaseIsDelete = 0
                            INNER JOIN  sys_static_dictionary AS SD2 ON S.CourseId = SD2.DictionaryId AND SD2.TypeCode = '1005' AND SD2.BaseIsDelete = 0
                            INNER JOIN  sys_static_dictionary AS SD3 ON S.OneClassId = SD3.DictionaryId AND SD3.TypeCode = '1006' AND SD3.BaseIsDelete = 0
                            INNER JOIN  sys_static_dictionary AS SD4 ON S.TwoClassId = SD4.DictionaryId AND SD4.TypeCode = '1006' AND SD4.BaseIsDelete = 0
                            INNER JOIN  up_UnitRelation AS UR ON UR.ExtensionObjId = S.SchoolId AND UR.ExtensionType = 3 AND UR.BaseIsDelete = 0
							INNER JOIN  up_UnitRelation AS UR2 ON UR.UnitId = UR2.ExtensionObjId AND UR2.ExtensionType = 3 AND UR2.BaseIsDelete = 0
							INNER JOIN  up_Unit AS u2 ON ur.UnitId = u2.Id AND u2.BaseIsDelete = 0
							LEFT JOIN  SysArea AS sa ON sa.AreaCode = u2.AreaId AND sa.BaseIsDelete = 0
                            WHERE UR2.UnitId = {2}", !param.CountyId.IsNullOrZero() ? $" AND UUR.UnitId = {param.CountyId}" : "", !param.CountyId.IsNullOrZero() ? $" AND UUR.UnitId = {param.CountyId}" : "", param.CityId);
            var parameter = new List<DbParameter>();
            if (parameter != null)
            {
                if (!param.CountyId.IsNullOrZero() && param.CountyId > 0)
                {
                    sql.Append(" AND UR.UnitId = @CountyId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
                if (!param.FunRoomEvaluateProjectId.IsNullOrZero())
                {
                    sql.Append(" AND FunRoomEvaluateProjectId = @FunRoomEvaluateProjectId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@FunRoomEvaluateProjectId", param.FunRoomEvaluateProjectId));
                }
                if (!param.SchoolStageId.IsNullOrZero())
                {
                    sql.Append(" AND StageId = @StageId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@StageId", param.SchoolStageId));
                }
                if (!param.CourseId.IsNullOrZero())
                {
                    sql.Append(" AND CourseId = @CourseId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (!param.Level.IsNullOrZero())
                {
                    sql.Append(" AND StandardLevel = @StandardLevel");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@StandardLevel", param.Level));
                }
                if (!param.OneClassId.IsNullOrZero())
                {
                    sql.Append(" AND OneClassId = @OneClassId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@OneClassId", param.OneClassId));
                }
                if (!param.KeyWord.IsEmpty())
                {
                    sql.Append($" AND (TargetName LIKE '%{param.KeyWord}%') ");
                }
                //if (param.IsOnlyShowNoStandard == 1)
                //{
                //    sql.Append(" AND (RoomNumDifference < 0 OR AreaDifference < 0) ");
                //}
            }
            sql.Append(" GROUP BY S.FunRoomEvaluateProjectId ,UR2.UnitId ,UR.UnitId ,S.StageId ,SD.DicName ,S.CourseId ,SD2.DicName ,S.OneClassId ,SD3.DicName ,S.TwoClassId ,S.StandardLevel ,S.TargetName ,sa.AreaName ");
            sql.Append(" ) T");
            var list = await this.BaseRepository().FindList<FunRoomAnalysisEntity>(sql.ToString(), parameter.ToArray(), pagination);
            return list.ToList();
        }

        /// <summary>
        /// 获取区县实验室达标看板数据
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<List<FunRoomAnalysisEntity>> GetCountyFunRoomQualifyList(FunRoomAttendStaticParam param)
        {
            StringBuilder sql = new StringBuilder();
            sql.Append($@"
                SELECT  *,
                        CASE WHEN NeedStandardCount > 0
                             THEN CAST((CAST(RoomStandardCount AS DECIMAL(18,2)) / CAST(NeedStandardCount AS DECIMAL(18,2)) * 100) AS DECIMAL(18,2))
                             ELSE 0 END AS RoomStandardRate,
                        CASE WHEN NeedStandardCount > 0
                             THEN CAST((CAST(AreaStandardCount AS DECIMAL(18,2)) / CAST(NeedStandardCount AS DECIMAL(18,2)) * 100) AS DECIMAL(18,2))
                             ELSE 0 END AS AreaStandardRate
                FROM    ( SELECT    S.FunRoomEvaluateProjectId ,
                                    S.OneClassId ,
                                    S.TwoClassId ,
					                SD4.DicName AS TwoClassName ,
                                    UR.UnitId AS CountyId ,
                                    COUNT(1) AS NeedStandardCount ,
                                    ( SELECT    COUNT(1)
                                      FROM       bn_FunRoomAttendStatic AS FRS
                                                INNER	 JOIN up_UnitRelation AS UUR ON FRS.SchoolId = UUR.ExtensionObjId AND UUR.ExtensionType = 3 AND UUR.BaseIsDelete = 0
                                      WHERE     FRS.BaseIsDelete = 0 AND UUR.UnitId = UR.UnitId AND FRS.FunRoomEvaluateProjectId = S.FunRoomEvaluateProjectId
								                AND FRS.TwoClassId = S.TwoClassId
								                AND FRS.FunRoomNum >= FRS.StandardNum
                                    ) AS RoomStandardCount ,
                                    ( SELECT    COUNT(1)
                                      FROM       bn_FunRoomAttendStatic AS FRS2
                                                INNER	 JOIN up_UnitRelation AS UUR2 ON FRS2.SchoolId = UUR2.ExtensionObjId AND UUR2.ExtensionType = 3 AND UUR2.BaseIsDelete = 0
                                      WHERE     FRS2.BaseIsDelete = 0 AND UUR2.UnitId = UR.UnitId AND FRS2.FunRoomEvaluateProjectId = S.FunRoomEvaluateProjectId
								                AND FRS2.TwoClassId = S.TwoClassId
								                AND FRS2.FunRoomArea >= FRS2.StandardArea
                                    ) AS AreaStandardCount
                          FROM       bn_FunRoomAttendStatic AS S
                                    INNER JOIN  up_Unit AS U ON S.SchoolId = U.Id
                                    INNER JOIN  sys_static_dictionary AS SD ON S.StageId = SD.DictionaryId AND SD.TypeCode = '1002' AND SD.BaseIsDelete = 0 --学段字典表
                                    INNER JOIN  sys_static_dictionary AS SD2 ON S.CourseId = SD2.DictionaryId AND SD2.TypeCode = '1005' AND SD2.BaseIsDelete = 0	--学科字典表
                                    INNER JOIN  sys_static_dictionary AS SD3 ON S.OneClassId = SD3.DictionaryId AND SD3.TypeCode = '1006' AND SD3.BaseIsDelete = 0   --一级分类
                                    INNER JOIN  sys_static_dictionary AS SD4 ON S.TwoClassId = SD4.DictionaryId AND SD4.TypeCode = '1006' AND SD4.BaseIsDelete = 0   --二级分类
                                    INNER JOIN  up_UnitRelation AS UR ON UR.ExtensionObjId = S.SchoolId AND UR.ExtensionType = 3 AND UR.BaseIsDelete = 0
                                    WHERE UR.UnitId = { param.CountyId}
            ");
            var parameter = new List<DbParameter>();
            if (parameter != null)
            {
                if (!param.FunRoomEvaluateProjectId.IsNullOrZero())
                {
                    sql.Append($" AND S.FunRoomEvaluateProjectId = {param.FunRoomEvaluateProjectId}");
                }
                if (!param.SchoolStageId.IsNullOrZero())
                {
                    sql.Append($" AND S.StageId = {param.SchoolStageId}");
                }
                if (!param.Level.IsNullOrZero())
                {
                    sql.Append($" AND S.StandardLevel = {param.Level}");
                }
                if (!param.OneClassId.IsNullOrZero())
                {
                    sql.Append($" AND S.OneClassId = {param.OneClassId}");
                }
            }
            sql.Append(@"
                          GROUP BY  S.FunRoomEvaluateProjectId ,
                                    UR.UnitId ,
                                    S.OneClassId ,
                                    S.TwoClassId ,
					                SD4.DicName
                        ) T
            ");
            var list = await this.BaseRepository().FindList<FunRoomAnalysisEntity>(sql.ToString());
            return list.ToList();
        }
        #endregion

        #region 提交方法
        public async Task SaveForm(FunRoomAttendStaticEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(FunRoomAttendStaticEntity entity, Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }
        #endregion

        #region 私有方法

        private Expression<Func<FunRoomAttendStaticEntity, bool>> ListFilter(FunRoomAttendStaticParam param)
        {
            var expression = LinqExtensions.True<FunRoomAttendStaticEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (param.SchoolId > 0)
                {
                    expression = expression.And(t => t.SchoolId == param.SchoolId);
                }
                if (param.EvaluateProjectVersionId > 0)
                {
                    expression = expression.And(t => t.FunRoomEvaluateProjectVersionId == param.EvaluateProjectVersionId);
                }
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (param.SchoolStageId > 0)
                {
                    expression = expression.And(t => t.StageId == param.SchoolStageId);
                }
                if (param.CourseId > 0)
                {
                    expression = expression.And(t => t.CourseId == param.CourseId);
                }
                if (param.TwoClassId > 0)
                {
                    expression = expression.And(t => t.TwoClassId == param.TwoClassId);
                }
                if (param.VersionIdList!=null && param.VersionIdList.Count > 0)
                {
                    expression = expression.And(t => param.VersionIdList.Contains(t.FunRoomEvaluateProjectVersionId.Value));
                }
                if (param.SchoolIdList != null && param.SchoolIdList.Count > 0)
                {
                    expression = expression.And(t => param.SchoolIdList.Contains(t.SchoolId.Value));
                }
            }
            return expression;
        }

        /// <summary>
        /// 获取达标结果分析数据过滤查询
        /// </summary>
        /// <param name="param"></param>
        /// <param name="selectSql"></param>
        /// <param name="groupSql"></param>
        /// <returns></returns>
        private List<DbParameter> AnalysisListFilter(FunRoomAttendStaticParam param, StringBuilder sql, string selectSql = "", string groupSql = "")
        {
            if (!string.IsNullOrEmpty(selectSql))
            {
                sql.Append(@$"SELECT {selectSql} From (");
            }
            sql.Append(@$" SELECT * From (
                            SELECT  S.Id ,
                            S.BaseIsDelete ,S.BaseCreateTime ,S.BaseModifyTime ,S.BaseCreatorId ,S.BaseModifierId ,S.BaseVersion ,
                            S.FunRoomEvaluateProjectId ,S.SchoolId ,S.OneClassId ,S.TwoClassId ,
                            S.TargetName ,S.StageId ,S.CourseId ,S.StandardNum ,S.FunRoomNum ,S.StandardArea ,S.FunRoomArea ,S.StandardLevel ,
                            (S.FunRoomNum - S.StandardNum) AS RoomNumDifference ,(S.FunRoomArea - S.StandardArea) AS AreaDifference ,
		                    SD.DicName AS StageName ,SD2.DicName AS CourseName ,
		                    SD3.DicName AS OneClassName ,SD4.DicName AS TwoClassName ,
		                    U.Name AS SchoolName ,UR.UnitId AS CountyId ,U.Sort
                            FROM  bn_FunRoomAttendStatic AS S
                            INNER JOIN  up_Unit AS U ON S.SchoolId = U.Id
                            INNER JOIN  sys_static_dictionary AS SD ON S.StageId = SD.DictionaryId
                                                                    AND SD.TypeCode = '{DicTypeCodeEnum.SchoolStage.ParseToInt()}' AND SD.BaseIsDelete = 0
                            INNER JOIN  sys_static_dictionary AS SD2 ON S.CourseId = SD2.DictionaryId
                                                                    AND SD2.TypeCode = '{DicTypeCodeEnum.Course.ParseToInt()}' AND SD2.BaseIsDelete = 0
                            INNER JOIN  sys_static_dictionary AS SD3 ON S.OneClassId = SD3.DictionaryId
                                                                    AND SD3.TypeCode = '{DicTypeCodeEnum.FunRoomClass.ParseToInt()}' AND SD3.BaseIsDelete = 0
                            INNER JOIN  sys_static_dictionary AS SD4 ON S.TwoClassId = SD4.DictionaryId
                                                                    AND SD4.TypeCode = '{DicTypeCodeEnum.FunRoomClass.ParseToInt()}' AND SD4.BaseIsDelete = 0
                            INNER JOIN  up_UnitRelation AS UR ON UR.ExtensionObjId = S.SchoolId AND UR.ExtensionType = 3 AND UR.BaseIsDelete = 0
                          ) as T WHERE  1 = 1 ");
            var parameter = new List<DbParameter>();
            if (parameter != null)
            {
                if (!param.SchoolId.IsNullOrZero())
                {
                    sql.Append(" AND SchoolId = @SchoolId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
                }
                if (!param.CountyId.IsNullOrZero())
                {
                    sql.Append(" AND CountyId = @CountyId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
                if (!param.FunRoomEvaluateProjectId.IsNullOrZero())
                {
                    sql.Append(" AND FunRoomEvaluateProjectId = @FunRoomEvaluateProjectId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@FunRoomEvaluateProjectId", param.FunRoomEvaluateProjectId));
                }
                if (!param.SchoolStageId.IsNullOrZero())
                {
                    sql.Append(" AND StageId = @StageId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@StageId", param.SchoolStageId));
                }
                if (!param.CourseId.IsNullOrZero())
                {
                    sql.Append(" AND CourseId = @CourseId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CourseId", param.CourseId));
                }
                if (!param.Level.IsNullOrZero())
                {
                    sql.Append(" AND StandardLevel = @StandardLevel");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@StandardLevel", param.Level));
                }
                if (!param.OneClassId.IsNullOrZero())
                {
                    sql.Append(" AND OneClassId = @OneClassId");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@OneClassId", param.OneClassId));
                }
                if (!param.KeyWord.IsEmpty())
                {
                    sql.Append($" AND (TargetName LIKE '%{param.KeyWord}%') ");
                }
                if (param.IsOnlyShowNoStandard == 1)
                {
                    sql.Append(" AND (RoomNumDifference < 0 OR AreaDifference < 0) ");
                }
                if (param.RoomIsStandard > 0)
                {
                    sql.Append(" AND RoomNumDifference >= 0");
                }

                if (param.RoomIsStandard > 0)
                {
                    sql.Append(" AND RoomNumDifference >= 0");
                }
            }
            if (!string.IsNullOrEmpty(groupSql))
            {
                sql.Append($" ) T2 GROUP BY {groupSql}");
            }
            return parameter;
        }
        #endregion

        #region 执行汇总计算数据

        public async Task Deletes(long schoolid)
        {
            string strSql = $" Delete bn_FunRoomAttendStatic where SchoolId = {schoolid} AND BaseIsDelete = 0 ";
            await this.BaseRepository().ExecuteBySql(strSql);
        }

        public async Task Delete(long id)
        {
            string strSql = $" Delete bn_FunRoomAttendStatic where Id = {id} ";
            await this.BaseRepository().ExecuteBySql(strSql);
        }

        public async Task AddFunRoomAttendStatic(long id, long schoolid, int tracknum)
        {
            // 采用分步处理的方案，避免复杂的 f_split 逻辑
            // 第一步：获取评估标准数据
            var parameters = new List<DbParameter>
            {
                DbParameterExtension.CreateDbParameter("@SchoolId", schoolid),
                DbParameterExtension.CreateDbParameter("@ProjectVersionId", id),
                DbParameterExtension.CreateDbParameter("@TrackNum", tracknum)
            };

            // 第二步：获取功能室数据并在C#中处理学段匹配
            var funRoomDataSql = @"
                SELECT fr1.DictionaryId1005, fr1.DictionaryId1006A, fr1.DictionaryId1006B, fr1.SchoolStagez,
                       COUNT(1) AS FunRoomNum, ISNULL(SUM(fr1.UseArea), 0) AS UseArea
                FROM bn_FunRoom AS fr1
                WHERE fr1.Statuz = 1 AND fr1.UnitId = @SchoolId AND fr1.BaseIsDelete = 0
                GROUP BY fr1.DictionaryId1005, fr1.DictionaryId1006A, fr1.DictionaryId1006B, fr1.SchoolStagez";

            var funRoomData = await this.BaseRepository().FindList<dynamic>(funRoomDataSql, parameters.ToArray());

            // 第三步：构建主查询，使用临时表或CTE来处理匹配
            StringBuilder sb = new StringBuilder();
            sb.Append(@"
                INSERT INTO bn_FunRoomAttendStatic
	        ( BaseIsDelete, BaseCreateTime, BaseModifyTime, BaseCreatorId, BaseModifierId, BaseVersion,
	          FunRoomEvaluateProjectId, SchoolId, OneClassId, TwoClassId,
	          TargetName, StageId, CourseId,
	          StandardNum, FunRoomNum,
	          StandardArea, FunRoomArea, StandardLevel, Remark
	        )
		        SELECT  0, GETDATE(), GETDATE(), 0, 0, 0,
				        EPV.EvaluateProjectId, @SchoolId, ES.DictionaryId1006A, ES.DictionaryId1006B,
				        ES.TargetName, ES.SchoolStage, ES.DictionaryId1005,
				        EE.RoomNum, 0 AS FunRoomNum,
				        (EE.RoomArea * EE.RoomNum), 0 AS FunRoomArea, EE.StandardLevel, EE.Remark
		           FROM    bn_FunRoomEvaluateProjectVersion AS EPV
		           INNER JOIN bn_FunRoomEvaluateStandard AS ES ON EPV.EvaluateStandardId = ES.Id AND ES.BaseIsDelete = 0
		           INNER JOIN bn_FunRoomEvaluateEnorm AS EE ON ES.Id = EE.EvaluateStandardId AND EE.BaseIsDelete = 0
								           AND EE.IsEvaluate = 1 AND EE.Statuz = 1 AND RailStart <= @TrackNum AND RailEnd >= @TrackNum
	           WHERE  EPV.BaseIsDelete = 0 AND EPV.Id = @ProjectVersionId
            ");

            // 执行插入
            await this.BaseRepository().ExecuteBySql(sb.ToString(), parameters.ToArray());

            // 第四步：在C#中处理学段匹配并更新功能室数据
            await UpdateFunRoomDataWithStageMatching(schoolid, funRoomData);
        }

        /// <summary>
        /// 在C#中处理学段匹配并更新功能室数据
        /// </summary>
        private async Task UpdateFunRoomDataWithStageMatching(long schoolId, IEnumerable<dynamic> funRoomData)
        {
            foreach (var room in funRoomData)
            {
                var schoolStagez = room.SchoolStagez?.ToString() ?? "";
                if (string.IsNullOrEmpty(schoolStagez)) continue;

                // 分割学段字符串
                var stages = schoolStagez.Split(',', StringSplitOptions.RemoveEmptyEntries);

                foreach (var stage in stages)
                {
                    if (long.TryParse(stage.Trim(), out long stageId))
                    {
                        // 更新对应的达标统计数据
                        var updateSql = @"
                            UPDATE bn_FunRoomAttendStatic
                            SET FunRoomNum = @FunRoomNum, FunRoomArea = @FunRoomArea
                            WHERE SchoolId = @SchoolId
                              AND StageId = @StageId
                              AND CourseId = @CourseId
                              AND OneClassId = @OneClassId
                              AND TwoClassId = @TwoClassId";

                        var updateParams = new List<DbParameter>
                        {
                            DbParameterExtension.CreateDbParameter("@FunRoomNum", room.FunRoomNum),
                            DbParameterExtension.CreateDbParameter("@FunRoomArea", room.UseArea),
                            DbParameterExtension.CreateDbParameter("@SchoolId", schoolId),
                            DbParameterExtension.CreateDbParameter("@StageId", stageId),
                            DbParameterExtension.CreateDbParameter("@CourseId", room.DictionaryId1005),
                            DbParameterExtension.CreateDbParameter("@OneClassId", room.DictionaryId1006A),
                            DbParameterExtension.CreateDbParameter("@TwoClassId", room.DictionaryId1006B)
                        };

                        await this.BaseRepository().ExecuteBySql(updateSql, updateParams.ToArray());
                    }
                }
            }
        }

        /// <summary>
        /// 删除达标数据标准的时候，更新达标统计
        /// </summary>
        /// <returns></returns>
        public async Task ClearFunRoomAttendStatic()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append($@" DELETE bn_FunRoomAttendStatic WHERE FunRoomEvaluateProjectVersionId NOT IN (SELECT Id FROM bn_FunRoomEvaluateProjectVersion WHERE BaseIsDelete = 0) ");
            await this.BaseRepository().ExecuteBySql(sb.ToString());
        }

        #endregion
    }
}
