﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.OrganizationManage;
using Dqy.Syjx.Model.Param.OrganizationManage;

namespace Dqy.Syjx.Service.OrganizationManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2021-11-16 19:01
    /// 描 述：学年学期列表服务类
    /// </summary>
    public class SchoolTermService : RepositoryFactory
    {
        #region 获取数据
        public async Task<List<SchoolTermEntity>> GetList(SchoolTermListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<SchoolTermEntity>> GetPageList(SchoolTermListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<SchoolTermEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<SchoolTermEntity>(id);
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(SchoolTermEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(SchoolTermEntity entity, Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
                strSql = $" update bn_SchoolTerm set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
                strSql = $"update bn_SchoolTerm set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        private Expression<Func<SchoolTermEntity, bool>> ListFilter(SchoolTermListParam param)
        {
            var expression = LinqExtensions.True<SchoolTermEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (param.OptType == 1)
                {
                    if (param.Id > 0)
                    {
                        expression = expression.And(t => t.Id != param.Id);
                    }
                    if (param.TermEnd != null && param.TermStart != null)
                    {
                        expression = expression.And(t => (t.TermStart < param.TermStart && param.TermStart < t.TermEnd) || (t.TermStart < param.TermEnd && param.TermEnd < t.TermEnd));
                    }
                }
                else if (param.OptType == 2)
                {
                    if (param.ClassTime != null)
                    {
                        expression = expression.And(t => (t.TermStart <= param.ClassTime && param.ClassTime <= t.TermEnd));
                    }
                }
                else if (param.OptType == 3)
                {
                    if (param.Id > 0)
                    {
                        expression = expression.And(t => t.Id != param.Id);
                    }
                    expression = expression.And(t => (t.SchoolYearStart == param.SchoolYearStart && t.SchoolTerm == param.SchoolTerm));
                }
                else if (param.OptType == 4)
                {
                    if (param.Id > 0)
                    {
                        expression = expression.And(t => t.Id != param.Id);
                    }
                    expression = expression.And(t => (t.TermEnd < param.TermStart && t.TermEnd >= param.FirstWeekDate));
                }
                else
                {
                    if (!string.IsNullOrEmpty(param.Ids))
                    {
                        long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                        expression = expression.And(t => idArr.Contains(t.Id.Value));
                    }
                    if (param.SchoolTerm > 0)
                    {
                        expression = expression.And(t => t.SchoolTerm == param.SchoolTerm);
                    }
                    if (param.SchoolYearStart > 0)
                    {
                        expression = expression.And(t => t.SchoolYearStart == param.SchoolYearStart);
                    }
                    if (param.SchoolYearEnd > 0)
                    {
                        expression = expression.And(t => t.SchoolYearEnd == param.SchoolYearEnd);
                    }
                    //判断当前时间，是那个学期的。
                    if (param.ClassTime != null)
                    {
                        expression = expression.And(t => t.TermStart <= param.ClassTime && param.ClassTime <=t.TermEnd);
                    }
                }
            }
            return expression;
        }
        #endregion
    }
}
